// 直接测试分析函数
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAnalyzeFunction() {
  try {
    console.log('🧪 测试分析函数...');
    
    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    });
    
    if (!aiConfig) {
      console.log('❌ 未找到AI配置');
      return;
    }
    
    console.log('✅ AI配置:', aiConfig.provider, aiConfig.model);
    
    // 模拟调用DeepSeekClient
    const { DeepSeekClient } = require('./src/lib/ai.ts');
    const client = new DeepSeekClient(aiConfig);
    
    console.log('🔄 测试AI客户端...');
    const testResponse = await client.callAPI('测试提示', 100);
    console.log('✅ AI响应:', testResponse.substring(0, 200));
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAnalyzeFunction();
