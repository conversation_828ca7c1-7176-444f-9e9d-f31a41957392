import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET - 调试：查看所有PlotInfo记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    if (episodeId) {
      // 查看特定剧集的PlotInfo
      const plotInfo = await prisma.plotInfo.findUnique({
        where: { episodeId },
        include: {
          episode: {
            select: {
              id: true,
              title: true
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          episodeId,
          plotInfo,
          hasDetailedCharacters: !!plotInfo?.detailedCharacters,
          detailedCharactersLength: plotInfo?.detailedCharacters?.length || 0
        }
      })
    } else {
      // 查看所有PlotInfo记录
      const allPlotInfo = await prisma.plotInfo.findMany({
        include: {
          episode: {
            select: {
              id: true,
              title: true
            }
          }
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          total: allPlotInfo.length,
          records: allPlotInfo.map(info => ({
            id: info.id,
            episodeId: info.episodeId,
            episodeTitle: info.episode.title,
            hasDetailedCharacters: !!info.detailedCharacters,
            detailedCharactersLength: info.detailedCharacters?.length || 0,
            hasDetailedScenes: !!info.detailedScenes,
            hasPlotSequences: !!info.plotSequences,
            hasEmotionalArc: !!info.emotionalArc,
            hasGeneratedPrompt: !!info.generatedPrompt,
            createdAt: info.createdAt,
            updatedAt: info.updatedAt
          }))
        }
      })
    }
  } catch (error) {
    console.error('调试查询失败:', error)
    return NextResponse.json(
      { success: false, error: '查询失败' },
      { status: 500 }
    )
  }
}
