import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

interface MinimaxVideoRequest {
  prompt: string
  model?: string
  video_setting?: {
    video_duration?: number
    video_aspect_ratio?: string
    video_quality?: string
  }
  customPrompt?: string
}

interface MinimaxVideoResponse {
  task_id: string
  status: string
  video_url?: string
  error?: string
}

// POST - 生成视频
export async function POST(request: NextRequest) {
  try {
    const body: MinimaxVideoRequest = await request.json()
    const { prompt, model = 'hailuo-02', video_setting, customPrompt } = body

    // 获取MiniMax配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'minimax',
        model: model,
        enabled: true
      }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { error: 'MiniMax模型未配置或未启用' },
        { status: 400 }
      )
    }

    // 构建最终提示词
    let finalPrompt = prompt
    if (customPrompt && customPrompt.trim()) {
      finalPrompt = `${prompt}\n\n增强要求：${customPrompt}`
    }

    // 调用MiniMax API
    const videoResult = await generateVideoWithMinimax(aiConfig.apiKey, {
      prompt: finalPrompt,
      model,
      video_setting: {
        video_duration: 6,
        video_aspect_ratio: '16:9',
        video_quality: 'high',
        ...video_setting
      }
    })

    return NextResponse.json(videoResult)
  } catch (error) {
    console.error('MiniMax视频生成失败:', error)
    return NextResponse.json(
      { error: '视频生成失败' },
      { status: 500 }
    )
  }
}

// GET - 查询视频生成状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('task_id')

    if (!taskId) {
      return NextResponse.json(
        { error: '缺少task_id参数' },
        { status: 400 }
      )
    }

    // 获取MiniMax配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'minimax',
        enabled: true
      }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { error: 'MiniMax模型未配置' },
        { status: 400 }
      )
    }

    // 查询任务状态
    const statusResult = await queryVideoStatus(aiConfig.apiKey, taskId)

    return NextResponse.json(statusResult)
  } catch (error) {
    console.error('查询视频状态失败:', error)
    return NextResponse.json(
      { error: '查询状态失败' },
      { status: 500 }
    )
  }
}

// 调用MiniMax视频生成API
async function generateVideoWithMinimax(
  apiKey: string,
  params: {
    prompt: string
    model: string
    video_setting: {
      video_duration: number
      video_aspect_ratio: string
      video_quality: string
    }
  }
): Promise<MinimaxVideoResponse> {
  try {
    const response = await fetch('https://api.minimax.chat/v1/video_generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: params.model,
        prompt: params.prompt,
        video_setting: params.video_setting
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`MiniMax API错误: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('MiniMax API调用失败:', error)
    throw error
  }
}

// 查询视频生成状态
async function queryVideoStatus(
  apiKey: string,
  taskId: string
): Promise<MinimaxVideoResponse> {
  try {
    const response = await fetch(`https://api.minimax.chat/v1/video_generation/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`MiniMax API错误: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('查询MiniMax状态失败:', error)
    throw error
  }
}

// 轮询等待视频生成完成
export async function waitForVideoCompletion(
  apiKey: string,
  taskId: string,
  maxWaitTime: number = 300000, // 5分钟
  pollInterval: number = 5000    // 5秒
): Promise<MinimaxVideoResponse> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      const status = await queryVideoStatus(apiKey, taskId)
      
      if (status.status === 'completed' && status.video_url) {
        return status
      } else if (status.status === 'failed') {
        throw new Error(`视频生成失败: ${status.error || '未知错误'}`)
      }
      
      // 等待下次轮询
      await new Promise(resolve => setTimeout(resolve, pollInterval))
    } catch (error) {
      console.error('轮询视频状态失败:', error)
      throw error
    }
  }
  
  throw new Error('视频生成超时')
}

// 生成视频的完整流程（包含轮询）
export async function generateVideoComplete(
  apiKey: string,
  params: {
    prompt: string
    model: string
    video_setting: {
      video_duration: number
      video_aspect_ratio: string
      video_quality: string
    }
  }
): Promise<MinimaxVideoResponse> {
  // 1. 启动视频生成任务
  const taskResult = await generateVideoWithMinimax(apiKey, params)
  
  if (!taskResult.task_id) {
    throw new Error('未获取到任务ID')
  }
  
  // 2. 轮询等待完成
  const finalResult = await waitForVideoCompletion(apiKey, taskResult.task_id)
  
  return finalResult
}

// 批量生成视频（用于多个剧集）
export async function generateMultipleVideos(
  apiKey: string,
  videoRequests: Array<{
    prompt: string
    model: string
    video_setting: any
  }>
): Promise<MinimaxVideoResponse[]> {
  const results: MinimaxVideoResponse[] = []
  
  // 并发启动所有任务
  const taskPromises = videoRequests.map(request => 
    generateVideoWithMinimax(apiKey, request)
  )
  
  const taskResults = await Promise.all(taskPromises)
  
  // 并发等待所有任务完成
  const completionPromises = taskResults.map(task => 
    task.task_id ? waitForVideoCompletion(apiKey, task.task_id) : Promise.reject('无效任务ID')
  )
  
  const finalResults = await Promise.allSettled(completionPromises)
  
  finalResults.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      results.push(result.value)
    } else {
      results.push({
        task_id: taskResults[index].task_id || '',
        status: 'failed',
        error: result.reason
      })
    }
  })
  
  return results
}
