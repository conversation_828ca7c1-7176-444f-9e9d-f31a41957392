import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取单个项目详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        characters: {
          orderBy: { createdAt: 'asc' }
        },
        episodes: {
          orderBy: { createdAt: 'asc' },
          include: {
            plotInfo: true
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 解析角色的生成图像数据
    const processedProject = {
      ...project,
      characters: project.characters.map(character => ({
        ...character,
        generatedImages: character.generatedImages
          ? JSON.parse(character.generatedImages)
          : null
      }))
    }

    return NextResponse.json({
      success: true,
      data: processedProject,
    })
  } catch (error) {
    console.error('获取项目详情失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目详情失败' },
      { status: 500 }
    )
  }
}

// 更新项目
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { name, description, status } = body

    // 检查项目是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 如果更新名称，检查是否与其他项目重名
    if (name && name.trim() !== existingProject.name) {
      const duplicateProject = await prisma.project.findFirst({
        where: {
          name: name.trim(),
          id: { not: id }
        }
      })

      if (duplicateProject) {
        return NextResponse.json(
          { success: false, error: '项目名称已存在，请使用其他名称' },
          { status: 400 }
        )
      }
    }

    // 更新项目
    const updatedProject = await prisma.project.update({
      where: { id },
      data: {
        ...(name && { name: name.trim() }),
        ...(description !== undefined && { description: description?.trim() || null }),
        ...(status && { status }),
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedProject,
      message: '项目更新成功'
    })
  } catch (error) {
    console.error('更新项目失败:', error)
    return NextResponse.json(
      { success: false, error: '更新项目失败' },
      { status: 500 }
    )
  }
}

// 删除项目
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查项目是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })

    if (!existingProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 删除项目（级联删除相关数据）
    await prisma.project.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      message: '项目删除成功'
    })
  } catch (error) {
    console.error('删除项目失败:', error)
    return NextResponse.json(
      { success: false, error: '删除项目失败' },
      { status: 500 }
    )
  }
}
