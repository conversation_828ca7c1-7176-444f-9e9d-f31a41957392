@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 确保输入框文字可见 */
input, textarea, select {
  color: #374151 !important; /* 强制使用深灰色文字 */
}

input::placeholder, textarea::placeholder {
  color: #9CA3AF !important; /* 占位符颜色 */
}

/* 浏览器兼容性修复 */
* {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-user-select: inherit;
  -ms-user-select: inherit;
  user-select: inherit;
}

/* 背景裁剪兼容性 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* 遮罩图像兼容性 */
.mask-image {
  -webkit-mask-image: inherit;
  mask-image: inherit;
}

/* 外观兼容性 */
input[type="range"], input[type="checkbox"], input[type="radio"] {
  -webkit-appearance: none;
  appearance: none;
}

/* 背景滤镜兼容性 */
.backdrop-filter {
  -webkit-backdrop-filter: inherit;
  backdrop-filter: inherit;
}
