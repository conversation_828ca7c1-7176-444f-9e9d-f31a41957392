'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Save, X } from 'lucide-react'
import Layout from '@/components/Layout'

export default function NewProjectPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      setError('项目名称不能为空')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        // 创建成功，跳转到项目详情页
        router.push(`/projects/${data.data.id}`)
      } else {
        throw new Error(data.error || '创建项目失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto">
        {/* 页面头部 */}
        <div className="mb-8">
          <Link
            href="/projects"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="mr-2" size={16} />
            返回项目列表
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">新建项目</h1>
          <p className="mt-1 text-sm text-gray-600">
            创建一个新的小说转视频项目
          </p>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-6 rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">
              {error}
              <button
                onClick={() => setError(null)}
                className="ml-2 p-1 text-red-800 hover:text-red-900 rounded-full hover:bg-red-100 transition-colors"
                title="关闭"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        )}

        {/* 创建表单 */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6 p-6">
            {/* 项目名称 */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                项目名称 <span className="text-red-500">*</span>
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-500"
                  placeholder="请输入项目名称"
                  required
                />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                为您的小说转视频项目起一个有意义的名称
              </p>
            </div>

            {/* 项目描述 */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                项目描述
              </label>
              <div className="mt-1">
                <textarea
                  name="description"
                  id="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-500"
                  placeholder="请输入项目描述（可选）"
                />
              </div>
              <p className="mt-2 text-sm text-gray-500">
                简要描述这个项目的内容和目标
              </p>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/projects"
                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                取消
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    创建中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2" size={16} />
                    创建项目
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 创建后的步骤提示 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">
            创建项目后您可以：
          </h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 上传小说文件（支持.txt、.docx格式）</li>
            <li>• AI自动分析并提取角色信息</li>
            <li>• 按章节拆分剧集</li>
            <li>• 提取每集的剧情信息</li>
            <li>• 生成专业的视频脚本</li>
          </ul>
        </div>
      </div>
    </Layout>
  )
}
