/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/[id]/upload/route";
exports.ids = ["app/api/projects/[id]/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/[id]/upload/route.ts */ \"(rsc)/./src/app/api/projects/[id]/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/[id]/upload/route\",\n        pathname: \"/api/projects/[id]/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/[id]/upload/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\projects\\\\[id]\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/projects/[id]/upload/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/projects/[id]/upload/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// 上传文件到项目\nasync function POST(request, { params }) {\n    try {\n        const { id: projectId } = await params;\n        const body = await request.json();\n        const { fileName, content } = body;\n        if (!fileName || !content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '文件名和内容不能为空'\n            }, {\n                status: 400\n            });\n        }\n        // 验证文件格式\n        const supportedExtensions = [\n            '.txt',\n            '.docx',\n            '.doc'\n        ];\n        const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n        if (!supportedExtensions.includes(fileExtension)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `不支持的文件格式。支持的格式: ${supportedExtensions.join(', ')}`\n            }, {\n                status: 400\n            });\n        }\n        // 验证内容长度\n        if (content.length > 2000000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '文件内容太长，请控制在200万字符以内'\n            }, {\n                status: 400\n            });\n        }\n        // 检查项目是否存在\n        const project = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findUnique({\n            where: {\n                id: projectId\n            }\n        });\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 更新项目，添加文件内容\n        const updatedProject = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.update({\n            where: {\n                id: projectId\n            },\n            data: {\n                fileName,\n                content,\n                status: 'uploaded',\n                updatedAt: new Date()\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedProject,\n            message: '文件上传成功'\n        });\n    } catch (error) {\n        console.error('文件上传失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '文件上传失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/[id]/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fupload%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();