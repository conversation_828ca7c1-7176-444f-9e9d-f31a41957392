const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugFailedVideos() {
  try {
    console.log('🔍 查询失败的视频片段...');
    
    // 查询失败的视频片段
    const failedSegments = await prisma.videoSegment.findMany({
      where: { status: 'failed' },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        storyVideo: true
      }
    });

    // 获取相关的剧集信息
    const episodeIds = [...new Set(failedSegments.map(s => s.episodeId))];
    const episodes = await prisma.episode.findMany({
      where: { id: { in: episodeIds } },
      select: { id: true, title: true }
    });

    const episodeMap = Object.fromEntries(episodes.map(e => [e.id, e]));
    
    if (failedSegments.length === 0) {
      console.log('✅ 没有失败的视频片段');
      return;
    }
    
    console.log(`❌ 找到 ${failedSegments.length} 个失败的视频片段:`);
    
    failedSegments.forEach((segment, index) => {
      console.log(`\n${index + 1}. 片段: ${segment.title}`);
      console.log(`   剧集: ${episodeMap[segment.episodeId]?.title || '未知'}`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   创建时间: ${segment.createdAt}`);
      
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   错误信息: ${metadata.error || '无错误信息'}`);
          console.log(`   提供商: ${metadata.provider || '未知'}`);
          console.log(`   任务ID: ${metadata.taskId || '无'}`);
          console.log(`   失败时间: ${metadata.failedAt || '未知'}`);
          console.log(`   重试次数: ${metadata.retryCount || 0}`);
        } catch (e) {
          console.log(`   元数据解析失败: ${segment.metadata}`);
        }
      } else {
        console.log(`   无元数据`);
      }
    });
    
  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugFailedVideos();
