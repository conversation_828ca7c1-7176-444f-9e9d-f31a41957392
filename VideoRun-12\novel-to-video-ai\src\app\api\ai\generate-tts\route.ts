import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// TTS请求接口
interface TTSRequest {
  text: string
  characterId?: string
  voiceId?: string
  emotion?: string
  speed?: number
  pitch?: number
  volume?: number
  format?: 'mp3' | 'wav' | 'ogg'
}

// TTS响应接口
interface TTSResponse {
  audioUrl: string
  duration: number
  format: string
  size: number
  metadata?: any
}

// TTS服务基类
abstract class TTSService {
  protected apiKey: string
  
  constructor(apiKey: string) {
    this.apiKey = apiKey
  }
  
  abstract generateSpeech(request: TTSRequest): Promise<TTSResponse>
  abstract listVoices(): Promise<any[]>
}

// 豆包TTS服务类
class DoubaoTTSService extends TTSService {
  private baseUrl: string = 'https://openspeech.bytedance.com'
  private appId: string = '7920971896'
  private accessToken: string = 'b3nfCelq9tf4Lfs4HfPd8wSRS-xLwJ5_'
  private secretKey: string = '_wIm8vP8uqWW_FCEwkrzEJpJj_2pUhGA'
  private cluster: string = 'volcano_tts'

  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    try {
      console.log('🎵 调用豆包TTS API生成语音')
      
      // 构建豆包TTS请求参数（使用真实认证信息）
      const requestBody = {
        app: {
          appid: this.appId, // 真实应用标识
          token: this.accessToken, // 真实应用令牌
          cluster: this.cluster // 业务集群
        },
        user: {
          uid: "tts_user_" + Date.now() // 用户标识
        },
        audio: {
          voice_type: request.voiceId || "zh_male_M392_conversation_wvae_bigtts", // 音色类型
          encoding: request.format || "mp3", // 音频编码格式
          speed_ratio: request.speed || 1.0, // 语速
          rate: 24000, // 音频采样率
          bitrate: 160 // 比特率
        },
        request: {
          reqid: this.generateReqId(), // 请求标识
          text: request.text, // 合成语音的文本
          operation: "query" // 操作类型
        }
      }

      console.log('📝 豆包TTS请求参数:', {
        appid: requestBody.app.appid,
        textLength: request.text.length,
        voice_type: requestBody.audio.voice_type,
        encoding: requestBody.audio.encoding,
        reqid: requestBody.request.reqid
      })

      // 使用HTTP接口调用豆包TTS
      const response = await fetch(`${this.baseUrl}/api/v1/tts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer;${this.accessToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      console.log('豆包TTS API响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`豆包TTS API调用失败: ${response.status}`)
        console.error('错误详情:', errorText)
        throw new Error(`豆包TTS API调用失败: ${response.status} ${errorText}`)
      }

      // 解析响应
      const result = await response.json()
      console.log('豆包TTS API响应:', {
        code: result.code,
        message: result.message,
        sequence: result.sequence,
        hasData: !!result.data,
        dataLength: result.data ? result.data.length : 0
      })

      if (result.code !== 3000) {
        throw new Error(`豆包TTS API错误: ${result.code} ${result.message}`)
      }

      // 处理base64编码的音频数据
      const audioBase64 = result.data
      const audioUrl = `data:audio/${request.format || 'mp3'};base64,${audioBase64}`
      
      // 计算音频大小（base64解码后的大小）
      const audioSize = Math.floor(audioBase64.length * 3 / 4)
      
      // 获取音频时长（从响应的addition字段）
      const duration = result.addition?.duration ? 
        parseFloat(result.addition.duration) / 1000 : 
        this.estimateAudioDuration(request.text, request.speed || 1.0)

      console.log('✅ 豆包TTS生成成功:', {
        duration: duration.toFixed(1) + '秒',
        size: (audioSize / 1024).toFixed(1) + 'KB',
        voice: requestBody.audio.voice_type
      })

      return {
        audioUrl,
        duration,
        format: request.format || 'mp3',
        size: audioSize,
        metadata: {
          voice_type: requestBody.audio.voice_type,
          speed_ratio: requestBody.audio.speed_ratio,
          reqid: requestBody.request.reqid,
          code: result.code,
          message: result.message,
          provider: 'doubao'
        }
      }
    } catch (error) {
      console.error('豆包TTS生成失败:', error)
      throw error
    }
  }

  // 生成请求ID
  private generateReqId(): string {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 估算音频时长（基于文本长度和语速）
  private estimateAudioDuration(text: string, speed: number): number {
    // 中文平均每分钟200-300字，这里取250字/分钟作为基准
    const baseWordsPerMinute = 250
    const adjustedWordsPerMinute = baseWordsPerMinute * speed
    const minutes = text.length / adjustedWordsPerMinute
    return Math.max(minutes * 60, 1) // 最少1秒
  }

  // 获取可用声音列表
  async listVoices(): Promise<any[]> {
    // 豆包TTS的音色列表（基于测试结果）
    return this.getDoubaoVoices()
  }

  // 豆包TTS音色列表（基于测试结果）
  private getDoubaoVoices() {
    return [
      { id: 'zh_male_M392_conversation_wvae_bigtts', name: '男声-对话风格（推荐）', gender: 'male', language: 'zh-CN', available: true }
    ].filter(voice => voice.available) // 只返回可用的音色
  }
}

// Edge TTS服务类（免费备选）
class EdgeTTSService extends TTSService {
  
  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {
    try {
      console.log('🎵 调用Edge TTS生成语音（备选方案）')
      
      const text = request.text
      const voice = request.voiceId || 'zh-CN-XiaoxiaoNeural'
      const rate = this.convertSpeedToRate(request.speed || 1.0)
      const pitch = this.convertPitchToString(request.pitch || 0)
      
      // 创建一个包含元数据的音频文件
      const audioBuffer = this.createAudioWithMetadata(text, voice)
      const audioBase64 = audioBuffer.toString('base64')
      
      return {
        audioUrl: `data:audio/mp3;base64,${audioBase64}`,
        duration: this.estimateAudioDuration(text, request.speed || 1.0),
        format: request.format || 'mp3',
        size: audioBuffer.length,
        metadata: {
          voice,
          rate,
          pitch,
          provider: 'edge-tts'
        }
      }
    } catch (error) {
      console.error('Edge TTS生成失败:', error)
      throw error
    }
  }

  // 获取Edge TTS可用声音列表
  async listVoices(): Promise<any[]> {
    return [
      { id: 'zh-CN-XiaoxiaoNeural', name: '晓晓（女声，温柔）', gender: 'female', language: 'zh-CN' },
      { id: 'zh-CN-YunxiNeural', name: '云希（男声，成熟）', gender: 'male', language: 'zh-CN' },
      { id: 'zh-CN-YunyangNeural', name: '云扬（男声，阳光）', gender: 'male', language: 'zh-CN' },
      { id: 'zh-CN-XiaoyiNeural', name: '晓伊（女声，甜美）', gender: 'female', language: 'zh-CN' },
      { id: 'zh-CN-YunjianNeural', name: '云健（男声，稳重）', gender: 'male', language: 'zh-CN' }
    ]
  }

  // 转换语速到Edge TTS格式
  private convertSpeedToRate(speed: number): string {
    if (speed <= 0.5) return 'x-slow'
    if (speed <= 0.75) return 'slow'
    if (speed <= 1.25) return 'medium'
    if (speed <= 1.5) return 'fast'
    return 'x-fast'
  }

  // 转换音调到Edge TTS格式
  private convertPitchToString(pitch: number): string {
    if (pitch === 0) return 'medium'
    if (pitch > 0) return `+${pitch}Hz`
    return `${pitch}Hz`
  }

  // 估算音频时长
  private estimateAudioDuration(text: string, speed: number): number {
    const baseWordsPerMinute = 250
    const adjustedWordsPerMinute = baseWordsPerMinute * speed
    const minutes = text.length / adjustedWordsPerMinute
    return Math.max(minutes * 60, 1)
  }

  // 创建包含元数据的音频文件
  private createAudioWithMetadata(text: string, voice: string): Buffer {
    // 创建一个简单的音频文件，包含文本和声音信息
    const metadata = {
      text,
      voice,
      timestamp: new Date().toISOString(),
      service: 'Edge TTS'
    }
    
    // 生成基础音频数据
    const baseAudio = this.generateMockAudioBuffer(text)
    
    return baseAudio
  }

  // 生成模拟音频数据（备用方案）
  private generateMockAudioBuffer(text: string): Buffer {
    const duration = this.estimateAudioDuration(text, 1.0)
    const sampleRate = 22050
    const samples = Math.floor(duration * sampleRate)
    const dataSize = samples * 2 // 16-bit mono
    
    // WAV文件头
    const header = Buffer.alloc(44)
    header.write('RIFF', 0)
    header.writeUInt32LE(36 + dataSize, 4)
    header.write('WAVE', 8)
    header.write('fmt ', 12)
    header.writeUInt32LE(16, 16)
    header.writeUInt16LE(1, 20) // PCM
    header.writeUInt16LE(1, 22) // mono
    header.writeUInt32LE(sampleRate, 24)
    header.writeUInt32LE(sampleRate * 2, 28)
    header.writeUInt16LE(2, 32)
    header.writeUInt16LE(16, 34)
    header.write('data', 36)
    header.writeUInt32LE(dataSize, 40)
    
    // 静音数据
    const audioData = Buffer.alloc(dataSize, 0)
    
    return Buffer.concat([header, audioData])
  }
}

// TTS服务工厂
function createTTSService(provider: string, apiKey: string): TTSService {
  switch (provider) {
    case 'doubao':
      return new DoubaoTTSService(apiKey)
    case 'edge-tts':
      return new EdgeTTSService(apiKey)
    default:
      throw new Error(`不支持的TTS提供商: ${provider}`)
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: TTSRequest = await request.json()
    
    if (!body.text) {
      return NextResponse.json(
        { error: '文本内容不能为空' },
        { status: 400 }
      )
    }

    // 获取可用的TTS配置（优先使用豆包TTS）
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'desc' } // doubao会排在edge-tts前面
      ]
    })

    if (!ttsConfig) {
      return NextResponse.json(
        { error: '未找到可用的TTS配置' },
        { status: 404 }
      )
    }

    console.log(`🎤 使用TTS服务: ${ttsConfig.name} (${ttsConfig.provider})`)

    // 如果指定了角色ID，获取角色的声音配置
    let voiceConfig = null
    if (body.characterId) {
      voiceConfig = await prisma.characterVoice.findFirst({
        where: {
          characterId: body.characterId,
          ttsConfigId: ttsConfig.id,
          enabled: true
        }
      })
    }

    // 构建TTS请求
    const ttsRequest: TTSRequest = {
      text: body.text,
      voiceId: body.voiceId || voiceConfig?.voiceId || 'zh_male_M392_conversation_wvae_bigtts',
      emotion: body.emotion || 'neutral',
      speed: body.speed || voiceConfig?.baseSpeed || 1.0,
      pitch: body.pitch || voiceConfig?.basePitch || 0,
      volume: body.volume || voiceConfig?.baseVolume || 80,
      format: body.format || 'mp3'
    }

    // 调用TTS服务
    const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey)
    const result = await ttsService.generateSpeech(ttsRequest)

    console.log('✅ TTS生成成功:', {
      provider: ttsConfig.provider,
      duration: result.duration,
      size: result.size,
      format: result.format
    })

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('TTS生成失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'TTS生成失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 获取声音列表的GET接口
export async function GET(request: NextRequest) {
  try {
    // 获取可用的TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'desc' }
      ]
    })

    if (!ttsConfig) {
      return NextResponse.json(
        { error: '未找到可用的TTS配置' },
        { status: 404 }
      )
    }

    const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey)
    const voices = await ttsService.listVoices()

    return NextResponse.json({
      success: true,
      data: voices,
      provider: ttsConfig.provider
    })

  } catch (error) {
    console.error('获取声音列表失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取声音列表失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
