import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { generateStoryVideoPrompt, createCharacterDNA, createSceneDNA } from '@/utils/storyVideoPromptGenerator'
import { createAIClient } from '@/lib/ai'

const prisma = new PrismaClient()

// POST - 生成带一致性约束的视频
export async function POST(request: NextRequest) {
  try {
    console.log('🎬 收到一致性视频生成请求')
    const body = await request.json()
    const { 
      episodeId, 
      consistencyMode = 'balanced',
      includeReferenceImages = true,
      customEnhancement,
      style = 'cinematic',
      quality = 'high'
    } = body

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少剧集ID' },
        { status: 400 }
      )
    }

    console.log('📋 参数:', { episodeId, consistencyMode, includeReferenceImages })

    // 1. 获取剧集和项目信息
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      include: {
        project: {
          include: {
            characters: true
          }
        },
        plotInfo: true
      }
    })

    if (!episode) {
      return NextResponse.json(
        { success: false, error: '剧集不存在' },
        { status: 404 }
      )
    }

    console.log('✅ 剧集信息获取成功')

    // 2. 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '未找到可用的AI配置' },
        { status: 500 }
      )
    }

    // 3. 解析剧情信息
    const plotInfo = episode.plotInfo
    let characters: any[] = []
    let scenes: any[] = []
    let plotSequences: any[] = []
    let emotionalArc = ''

    if (plotInfo) {
      try {
        characters = JSON.parse(plotInfo.detailedCharacters || plotInfo.characters || '[]')
        scenes = JSON.parse(plotInfo.detailedScenes || plotInfo.scenes || '[]')
        plotSequences = JSON.parse(plotInfo.plotSequences || '[]')
        emotionalArc = plotInfo.emotionalArc || ''
      } catch (error) {
        console.error('解析剧情信息失败:', error)
      }
    }

    console.log('📊 剧情数据:', { 
      charactersCount: characters.length, 
      scenesCount: scenes.length,
      plotSequencesCount: plotSequences.length 
    })

    // 4. 创建角色DNA档案（增强版）
    const characterDNAs = characters.map(char => {
      const dna = createCharacterDNA(char)
      
      // 查找项目角色库中的匹配角色
      const projectCharacter = episode.project.characters.find(pc => 
        pc.name.toLowerCase() === char.name.toLowerCase()
      )
      
      if (projectCharacter) {
        // 使用项目角色库的一致性信息
        dna.consistencyScore = projectCharacter.consistencyScore || 0.8
        dna.referenceImages = projectCharacter.referenceImages
        dna.detailedDNA = projectCharacter.detailedDNA
        console.log(`🔗 角色 ${char.name} 已关联项目角色库`)
      }
      
      return dna
    })

    // 5. 创建场景DNA档案
    const sceneDNA = scenes.length > 0 ? createSceneDNA(scenes[0]) : createSceneDNA({
      location: '室内场景',
      description: '温馨的室内环境',
      atmosphere: '舒适自然'
    })

    // 6. 准备参考图像数据
    const referenceImages: { [characterId: string]: string[] } = {}
    if (includeReferenceImages) {
      characterDNAs.forEach(char => {
        if (char.referenceImages) {
          try {
            const images = typeof char.referenceImages === 'string' 
              ? JSON.parse(char.referenceImages) 
              : char.referenceImages
            if (images && (images.front || images.side || images.full)) {
              referenceImages[char.id] = Object.values(images).filter(Boolean) as string[]
            }
          } catch (error) {
            console.error(`解析角色 ${char.name} 的参考图像失败:`, error)
          }
        }
      })
    }

    console.log('🖼️ 参考图像数据:', Object.keys(referenceImages).length, '个角色有参考图像')

    // 7. 生成增强的视频提示词
    const videoPrompt = generateStoryVideoPrompt({
      characters: characterDNAs,
      scene: sceneDNA,
      timeOfDay: 'midday',
      plotSequences: plotSequences.length > 0 ? plotSequences : [{
        sequenceId: 'default',
        action: episode.content.substring(0, 200),
        emotion: '自然真实',
        duration: plotSequences.length > 0 ? plotSequences[0].duration : '15秒', // 使用剧情分析的时长或默认15秒
        keyMoments: ['开始', '发展', '结束'],
        visualElements: '生动的人物表现'
      }],
      emotionalArc: emotionalArc || '平静自然的情感发展',
      customEnhancement,
      style,
      quality,
      consistencyMode,
      referenceImages,
      consistencyWeights: {
        textWeight: consistencyMode === 'strict' ? 0.8 : 0.7,
        imageWeight: consistencyMode === 'strict' ? 0.2 : 0.3
      }
    })

    console.log('✅ 视频提示词生成完成，长度:', videoPrompt.length)

    // 8. 保存生成记录
    const videoGeneration = await prisma.videoGeneration.create({
      data: {
        episodeId,
        prompt: videoPrompt,
        status: 'pending',
        consistencyMode,
        includeReferenceImages,
        style,
        quality,
        metadata: JSON.stringify({
          characterCount: characterDNAs.length,
          hasReferenceImages: Object.keys(referenceImages).length > 0,
          consistencyFeatures: {
            characterDNA: true,
            sceneDNA: true,
            referenceImages: includeReferenceImages,
            consistencyMode
          }
        })
      }
    })

    console.log('💾 生成记录已保存:', videoGeneration.id)

    // 9. 返回结果
    return NextResponse.json({
      success: true,
      data: {
        generationId: videoGeneration.id,
        prompt: videoPrompt,
        consistencyInfo: {
          mode: consistencyMode,
          characterCount: characterDNAs.length,
          referenceImageCount: Object.keys(referenceImages).length,
          consistencyFeatures: {
            characterDNA: true,
            sceneDNA: true,
            dualConstraints: true,
            validationProtocol: true
          }
        },
        characters: characterDNAs.map(char => ({
          id: char.id,
          name: char.name,
          hasReferenceImages: !!referenceImages[char.id],
          consistencyScore: char.consistencyScore || 0
        })),
        estimatedProcessingTime: '2-5分钟'
      }
    })

  } catch (error) {
    console.error('一致性视频生成失败:', error)
    const errorMessage = error instanceof Error ? error.message : '生成失败，请重试'
    return NextResponse.json(
      { success: false, error: errorMessage, details: error },
      { status: 500 }
    )
  }
}

// GET - 获取生成状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const generationId = searchParams.get('generationId')

    if (!generationId) {
      return NextResponse.json(
        { success: false, error: '缺少生成ID' },
        { status: 400 }
      )
    }

    const generation = await prisma.videoGeneration.findUnique({
      where: { id: generationId },
      include: {
        episode: {
          select: {
            title: true,
            project: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    if (!generation) {
      return NextResponse.json(
        { success: false, error: '生成记录不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: generation.id,
        status: generation.status,
        prompt: generation.prompt,
        videoUrl: generation.videoUrl,
        consistencyMode: generation.consistencyMode,
        style: generation.style,
        quality: generation.quality,
        createdAt: generation.createdAt,
        updatedAt: generation.updatedAt,
        episode: generation.episode,
        metadata: generation.metadata ? JSON.parse(generation.metadata) : null
      }
    })

  } catch (error) {
    console.error('获取生成状态失败:', error)
    return NextResponse.json(
      { success: false, error: '获取状态失败' },
      { status: 500 }
    )
  }
}
