import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { AIServiceError } from '@/lib/ai'

// 保存AI配置（全局配置）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      provider = 'deepseek',
      apiKey,
      model = 'deepseek-chat',
      temperature = 0.7,
      maxTokens = 4000,
      topP = 0.9,
    } = body

    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API密钥不能为空' },
        { status: 400 }
      )
    }

    // 创建或更新全局AI配置
    const aiConfig = await prisma.aIConfig.upsert({
      where: { id: 'global' },
      update: {
        provider,
        apiKey,
        model,
        temperature,
        maxTokens,
        topP,
        status: 'disconnected', // 保存后需要重新测试
        updatedAt: new Date(),
      },
      create: {
        id: 'global',
        provider,
        apiKey,
        model,
        temperature,
        maxTokens,
        topP,
        status: 'disconnected',
      },
    })

    return NextResponse.json({
      success: true,
      data: aiConfig,
      message: 'AI配置保存成功',
    })
  } catch (error) {
    console.error('保存AI配置失败:', error)
    return NextResponse.json(
      { success: false, error: '保存AI配置失败' },
      { status: 500 }
    )
  }
}

// 获取全局AI配置
export async function GET(request: NextRequest) {
  try {
    const aiConfig = await prisma.aIConfig.findUnique({
      where: { id: 'global' },
    })

    if (!aiConfig) {
      return NextResponse.json({
        success: true,
        data: null,
        message: '未找到AI配置',
      })
    }

    return NextResponse.json({
      success: true,
      data: aiConfig,
    })
  } catch (error) {
    console.error('获取AI配置失败:', error)
    return NextResponse.json(
      { success: false, error: '获取AI配置失败' },
      { status: 500 }
    )
  }
}
