// 为已完成但缺少音频的片段生成音频
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function generateMissingAudio() {
  try {
    console.log('🎵 开始为缺少音频的片段生成音频...\n');

    // 获取所有已完成但缺少音频的片段
    const segments = await prisma.videoSegment.findMany({
      where: {
        status: 'completed',
        videoUrl: {
          not: null
        }
      },
      orderBy: { segmentIndex: 'asc' }
    });

    console.log(`📋 找到 ${segments.length} 个已完成的片段`);

    for (const segment of segments) {
      try {
        // 检查是否已有音频
        let hasAudio = false;
        if (segment.metadata) {
          const metadata = JSON.parse(segment.metadata);
          hasAudio = !!metadata.audio;
        }

        if (hasAudio) {
          console.log(`✅ 片段 ${segment.segmentIndex} 已有音频，跳过`);
          continue;
        }

        console.log(`🎵 为片段 ${segment.segmentIndex} 生成音频...`);

        // 调用TTS API生成音频
        const response = await fetch('http://localhost:3002/api/ai/generate-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: segment.description || segment.title,
            voiceId: 'zh_male_M392_conversation_wvae_bigtts', // 默认男声
            emotion: 'neutral',
            speed: 1.0,
            pitch: 0,
            volume: 80,
            format: 'mp3'
          })
        });

        if (!response.ok) {
          throw new Error(`TTS API调用失败: ${response.status}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'TTS生成失败');
        }

        console.log(`✅ 片段 ${segment.segmentIndex} 音频生成成功，时长: ${result.data.duration}秒`);

        // 更新片段元数据
        const existingMetadata = JSON.parse(segment.metadata || '{}');
        await prisma.videoSegment.update({
          where: { id: segment.id },
          data: {
            metadata: JSON.stringify({
              ...existingMetadata,
              audio: {
                audioUrl: result.data.audioUrl,
                duration: result.data.duration,
                provider: 'doubao',
                generatedAt: new Date().toISOString()
              }
            })
          }
        });

        console.log(`✅ 片段 ${segment.segmentIndex} 音频信息已保存\n`);

      } catch (error) {
        console.error(`❌ 片段 ${segment.segmentIndex} 音频生成失败:`, error.message);
      }
    }

    console.log('🎉 音频生成任务完成！');

  } catch (error) {
    console.error('❌ 任务失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

generateMissingAudio();
