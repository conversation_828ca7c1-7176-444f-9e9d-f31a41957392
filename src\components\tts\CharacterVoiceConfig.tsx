'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Play, Save, Trash2, Plus, Settings, User } from 'lucide-react'

interface Character {
  id: string
  name: string
  description?: string
}

interface Voice {
  id: string
  name: string
  gender: string
  language: string
}

interface VoiceConfig {
  id: string
  characterId: string
  ttsConfigId: string
  voiceId: string
  voiceName?: string
  basePitch: number
  baseSpeed: number
  baseVolume: number
  emotionMappings?: string
  enabled: boolean
  ttsConfig: {
    id: string
    name: string
    provider: string
  }
}

interface CharacterVoiceData {
  character: Character
  voiceConfigs: VoiceConfig[]
}

export function CharacterVoiceConfig({ characterId }: { characterId: string }) {
  const [characterData, setCharacterData] = useState<CharacterVoiceData | null>(null)
  const [voices, setVoices] = useState<Voice[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  
  // 新配置表单状态
  const [newConfig, setNewConfig] = useState({
    voiceId: '',
    voiceName: '',
    basePitch: 0,
    baseSpeed: 1.0,
    baseVolume: 80
  })

  // 情感映射
  const emotions = [
    { key: 'neutral', label: '中性' },
    { key: 'happy', label: '高兴' },
    { key: 'sad', label: '悲伤' },
    { key: 'angry', label: '愤怒' },
    { key: 'excited', label: '兴奋' },
    { key: 'worried', label: '担心' },
    { key: 'surprised', label: '惊讶' },
    { key: 'gentle', label: '温柔' }
  ]

  // 加载角色声音配置
  useEffect(() => {
    if (characterId) {
      loadCharacterVoiceConfig()
      loadVoices()
    }
  }, [characterId])

  const loadCharacterVoiceConfig = async () => {
    try {
      const response = await fetch(`/api/characters/${characterId}/voice`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setCharacterData(result.data)
        }
      }
    } catch (error) {
      console.error('加载角色声音配置失败:', error)
    }
  }

  const loadVoices = async () => {
    try {
      const response = await fetch('/api/ai/generate-tts')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setVoices(result.data)
        }
      }
    } catch (error) {
      console.error('加载声音列表失败:', error)
    }
  }

  // 保存声音配置
  const saveVoiceConfig = async () => {
    if (!newConfig.voiceId) {
      alert('请选择声音')
      return
    }

    setIsSaving(true)
    try {
      const selectedVoice = voices.find(v => v.id === newConfig.voiceId)
      
      const configData = {
        ttsConfigId: 'edge-tts-config', // 这里应该从实际的TTS配置中获取
        voiceId: newConfig.voiceId,
        voiceName: newConfig.voiceName || selectedVoice?.name,
        basePitch: newConfig.basePitch,
        baseSpeed: newConfig.baseSpeed,
        baseVolume: newConfig.baseVolume,
        emotionMappings: {
          neutral: { pitchAdjust: 0, speedAdjust: 0, volumeAdjust: 0 },
          happy: { pitchAdjust: 2, speedAdjust: 0.1, volumeAdjust: 5 },
          sad: { pitchAdjust: -2, speedAdjust: -0.1, volumeAdjust: -5 },
          angry: { pitchAdjust: 3, speedAdjust: 0.2, volumeAdjust: 10 },
          excited: { pitchAdjust: 4, speedAdjust: 0.15, volumeAdjust: 8 },
          worried: { pitchAdjust: -1, speedAdjust: -0.05, volumeAdjust: -3 },
          surprised: { pitchAdjust: 5, speedAdjust: 0.1, volumeAdjust: 5 },
          gentle: { pitchAdjust: -1, speedAdjust: -0.1, volumeAdjust: -2 }
        }
      }

      const response = await fetch(`/api/characters/${characterId}/voice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          alert('声音配置保存成功')
          loadCharacterVoiceConfig()
          // 重置表单
          setNewConfig({
            voiceId: '',
            voiceName: '',
            basePitch: 0,
            baseSpeed: 1.0,
            baseVolume: 80
          })
        } else {
          alert(`保存失败: ${result.error}`)
        }
      } else {
        alert('保存失败，请重试')
      }
    } catch (error) {
      console.error('保存声音配置失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSaving(false)
    }
  }

  // 删除声音配置
  const deleteVoiceConfig = async (voiceConfigId: string) => {
    if (!confirm('确定要删除这个声音配置吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/characters/${characterId}/voice?voiceConfigId=${voiceConfigId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('声音配置已删除')
        loadCharacterVoiceConfig()
      } else {
        alert('删除失败，请重试')
      }
    } catch (error) {
      console.error('删除声音配置失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 测试声音
  const testVoice = async (voiceId: string, config?: VoiceConfig) => {
    setIsLoading(true)
    try {
      const testText = `我是${characterData?.character.name}，这是我的声音测试。`
      
      const request = {
        text: testText,
        voiceId,
        speed: config?.baseSpeed || newConfig.baseSpeed,
        pitch: config?.basePitch || newConfig.basePitch,
        volume: config?.baseVolume || newConfig.baseVolume,
        emotion: 'neutral'
      }

      const response = await fetch('/api/ai/generate-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // 播放音频
          const audio = new Audio(result.data.audioUrl)
          audio.play()
        } else {
          alert(`测试失败: ${result.error}`)
        }
      } else {
        alert('测试失败，请重试')
      }
    } catch (error) {
      console.error('测试声音失败:', error)
      alert('测试失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (!characterData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">加载中...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {characterData.character.name} - 声音配置
          </CardTitle>
          <CardDescription>
            为角色配置专属的声音特征和参数
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="existing" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="existing">现有配置</TabsTrigger>
              <TabsTrigger value="new">添加新配置</TabsTrigger>
            </TabsList>
            
            <TabsContent value="existing" className="space-y-4">
              {characterData.voiceConfigs.length > 0 ? (
                <div className="space-y-4">
                  {characterData.voiceConfigs.map((config) => (
                    <Card key={config.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium">{config.voiceName}</h4>
                            <p className="text-sm text-muted-foreground">
                              {config.ttsConfig.name} ({config.voiceId})
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => testVoice(config.voiceId, config)}
                              disabled={isLoading}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteVoiceConfig(config.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <Label>语速: {config.baseSpeed}</Label>
                          </div>
                          <div>
                            <Label>音调: {config.basePitch}</Label>
                          </div>
                          <div>
                            <Label>音量: {config.baseVolume}</Label>
                          </div>
                        </div>
                        
                        <div className="mt-3">
                          <Badge variant={config.enabled ? "default" : "secondary"}>
                            {config.enabled ? "已启用" : "已禁用"}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无声音配置，请添加新配置
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="new" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>选择声音</Label>
                  <Select value={newConfig.voiceId} onValueChange={(value) => setNewConfig({...newConfig, voiceId: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择声音" />
                    </SelectTrigger>
                    <SelectContent>
                      {voices.map((voice) => (
                        <SelectItem key={voice.id} value={voice.id}>
                          {voice.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>自定义名称（可选）</Label>
                  <Input
                    value={newConfig.voiceName}
                    onChange={(e) => setNewConfig({...newConfig, voiceName: e.target.value})}
                    placeholder="为这个声音配置起个名字"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>语速: {newConfig.baseSpeed}</Label>
                    <Slider
                      value={[newConfig.baseSpeed]}
                      onValueChange={([value]) => setNewConfig({...newConfig, baseSpeed: value})}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>音调: {newConfig.basePitch}</Label>
                    <Slider
                      value={[newConfig.basePitch]}
                      onValueChange={([value]) => setNewConfig({...newConfig, basePitch: value})}
                      min={-20}
                      max={20}
                      step={1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>音量: {newConfig.baseVolume}</Label>
                    <Slider
                      value={[newConfig.baseVolume]}
                      onValueChange={([value]) => setNewConfig({...newConfig, baseVolume: value})}
                      min={0}
                      max={100}
                      step={5}
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() => testVoice(newConfig.voiceId)}
                    disabled={!newConfig.voiceId || isLoading}
                    variant="outline"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    测试声音
                  </Button>
                  
                  <Button
                    onClick={saveVoiceConfig}
                    disabled={!newConfig.voiceId || isSaving}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? '保存中...' : '保存配置'}
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
