// 角色一致性功能测试脚本
// 使用Node.js 18+的内置fetch

const BASE_URL = 'http://localhost:3000';

// 测试数据
const testCharacter = {
  name: '张小雅',
  identity: '高中生',
  personality: '活泼开朗，善良勇敢',
  physique: '身材娇小，体态轻盈',
  facial: '圆脸，大眼睛，樱桃小嘴',
  hairstyle: '黑色长发，马尾辫',
  clothing: '校服，白色衬衫配蓝色裙子'
};

async function testConsistencyFeatures() {
  console.log('🚀 开始测试角色一致性功能...\n');

  try {
    // 1. 测试详细DNA生成
    console.log('1. 测试详细DNA生成功能');
    const dnaResponse = await fetch(`${BASE_URL}/api/ai/generate-appearance`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        modelId: 'test-model',
        character: testCharacter,
        generateDetailedDNA: true,
        useConsistencyConstraints: false,
        consistencyMode: 'hybrid'
      })
    });

    if (dnaResponse.ok) {
      const dnaResult = await dnaResponse.json();
      console.log('✅ 详细DNA生成成功');
      console.log('   生成的DNA:', JSON.stringify(dnaResult.data?.detailedDNA, null, 2));
    } else {
      console.log('❌ 详细DNA生成失败:', dnaResponse.status);
    }

    // 2. 测试一致性约束生成
    console.log('\n2. 测试一致性约束生成功能');
    const consistencyResponse = await fetch(`${BASE_URL}/api/ai/generate-appearance`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        modelId: 'test-model',
        character: {
          ...testCharacter,
          detailedDNA: {
            facial: {
              faceShape: '圆脸',
              eyeShape: '杏眼',
              eyeColor: '深邃的黑色',
              noseShape: '小巧的鼻子',
              mouthShape: '樱桃小嘴',
              skinTone: '白皙透亮',
              facialFeatures: '清秀的五官'
            },
            uniqueIdentifiers: ['圆脸', '大眼睛', '马尾辫']
          }
        },
        generateDetailedDNA: false,
        useConsistencyConstraints: true,
        consistencyMode: 'consistency'
      })
    });

    if (consistencyResponse.ok) {
      const consistencyResult = await consistencyResponse.json();
      console.log('✅ 一致性约束生成成功');
      console.log('   一致性评分:', consistencyResult.data?.consistencyScore);
      console.log('   一致性模式:', consistencyResult.data?.consistencyMode);
    } else {
      console.log('❌ 一致性约束生成失败:', consistencyResponse.status);
    }

    // 3. 测试混合模式
    console.log('\n3. 测试混合模式功能');
    const hybridResponse = await fetch(`${BASE_URL}/api/ai/generate-appearance`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        modelId: 'test-model',
        character: testCharacter,
        generateDetailedDNA: true,
        useConsistencyConstraints: true,
        consistencyMode: 'hybrid',
        customPrompt: '高质量动漫风格，精美细节'
      })
    });

    if (hybridResponse.ok) {
      const hybridResult = await hybridResponse.json();
      console.log('✅ 混合模式生成成功');
      console.log('   详细DNA:', hybridResult.data?.detailedDNA ? '已生成' : '未生成');
      console.log('   一致性评分:', hybridResult.data?.consistencyScore);
      console.log('   生成的图像:', {
        front: hybridResult.data?.front ? '已生成' : '未生成',
        side: hybridResult.data?.side ? '已生成' : '未生成',
        back: hybridResult.data?.back ? '已生成' : '未生成'
      });
    } else {
      console.log('❌ 混合模式生成失败:', hybridResponse.status);
    }

    console.log('\n🎉 角色一致性功能测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testConsistencyFeatures();
