// 修复视频URL的脚本
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixVideoUrls() {
  try {
    console.log('开始修复视频URL...')

    // 修复片段2 - 使用服务器日志中的完整URL
    const segment2Url = 'https://dashscope-result-wlcb-acdr-1.oss-cn-wulanchabu-acdr-1.aliyuncs.com/1d/6f/20250619/71f5dff0/fada46dc-2038-4b5e-a28b-f8fc80b6ed3a.mp4?Expires=**********&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=iDZU4Z%2F8SzdroXMrcFHgCUBFzJU%3D'
    
    const segment2 = await prisma.videoSegment.update({
      where: { id: 'cmc2tubyo00068z32uhl4wayz' },
      data: {
        videoUrl: segment2Url,
        metadata: JSON.stringify({
          taskId: 'fada46dc-2038-4b5e-a28b-f8fc80b6ed3a',
          provider: 'tongyi',
          completedAt: '2025-06-19T11:36:41.867Z',
          videoSize: 'unknown',
          responseTime: 218,
          totalAttempts: 16,
          generationTime: 61467,
          fixedUrl: true
        })
      }
    })
    console.log('✅ 片段2 URL已修复:', segment2.title)

    // 修复片段4 - 使用服务器日志中的完整URL
    const segment4Url = 'https://dashscope-result-wlcb-acdr-1.oss-cn-wulanchabu-acdr-1.aliyuncs.com/1d/3d/20250619/71f5dff0/8998f31d-4df9-4914-a012-cb5566bbb010.mp4?Expires=**********&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=UJxH8s8VbimRUlvFqClzZBiywQU%3D'
    
    const segment4 = await prisma.videoSegment.update({
      where: { id: 'cmc2tubyo00088z328rynv4gv' },
      data: {
        videoUrl: segment4Url,
        metadata: JSON.stringify({
          taskId: '8998f31d-4df9-4914-a012-cb5566bbb010',
          provider: 'tongyi',
          completedAt: '2025-06-19T11:38:07.910Z',
          videoSize: 'unknown',
          responseTime: 188,
          totalAttempts: 16,
          generationTime: 57406,
          fixedUrl: true
        })
      }
    })
    console.log('✅ 片段4 URL已修复:', segment4.title)

    // 检查修复结果
    const allSegments = await prisma.videoSegment.findMany({
      where: { storyVideoId: 'cmc2tubyn00018z32ggqhqhqh' },
      orderBy: { segmentIndex: 'asc' }
    })

    console.log('\n📊 修复后的片段状态:')
    allSegments.forEach(segment => {
      const hasSignature = segment.videoUrl && segment.videoUrl.includes('Signature=')
      console.log(`片段${segment.segmentIndex}: ${segment.status} ${hasSignature ? '✅ 有签名' : '❌ 无签名'}`)
      if (segment.videoUrl) {
        console.log(`  URL: ${segment.videoUrl.substring(0, 100)}...`)
      }
    })

    console.log('\n🎉 URL修复完成！')
  } catch (error) {
    console.error('❌ URL修复失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixVideoUrls()
