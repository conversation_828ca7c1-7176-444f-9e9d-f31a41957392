import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 完整影视作品生成接口
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const episodeId = params.id
    const body = await request.json()
    const { 
      generateMode = 'auto', // auto: 自动生成, manual: 手动控制
      includeBackgroundMusic = true,
      includeSoundEffects = true,
      videoQuality = 'high',
      audioQuality = 'high'
    } = body

    console.log(`🎬 开始生成完整影视作品 - 剧集: ${episodeId}`)

    // 1. 验证剧集存在
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      include: {
        project: true,
        plotInfo: true
      }
    })

    if (!episode) {
      return NextResponse.json(
        { error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 2. 创建影视作品生成任务
    const movieTask = await prisma.movieGenerationTask.create({
      data: {
        episodeId,
        projectId: episode.projectId,
        status: 'initializing',
        generateMode,
        settings: JSON.stringify({
          includeBackgroundMusic,
          includeSoundEffects,
          videoQuality,
          audioQuality
        }),
        timeline: JSON.stringify({
          totalDuration: 0,
          segments: [],
          audioTracks: [],
          musicTracks: [],
          effectTracks: []
        })
      }
    })

    console.log(`📋 创建影视生成任务: ${movieTask.id}`)

    // 3. 异步启动完整生成流程
    generateCompleteMovieAsync(movieTask.id, episode, {
      generateMode,
      includeBackgroundMusic,
      includeSoundEffects,
      videoQuality,
      audioQuality
    })

    return NextResponse.json({
      success: true,
      data: {
        taskId: movieTask.id,
        status: 'initializing',
        message: '影视作品生成任务已启动'
      }
    })

  } catch (error) {
    console.error('创建影视生成任务失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '生成任务创建失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 获取生成任务状态
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const episodeId = params.id
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('taskId')

    if (taskId) {
      // 获取特定任务状态
      const task = await prisma.movieGenerationTask.findUnique({
        where: { id: taskId },
        include: {
          episode: {
            select: { title: true }
          }
        }
      })

      if (!task) {
        return NextResponse.json(
          { error: '任务不存在' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        data: task
      })
    } else {
      // 获取剧集的所有生成任务
      const tasks = await prisma.movieGenerationTask.findMany({
        where: { episodeId },
        orderBy: { createdAt: 'desc' },
        take: 10
      })

      return NextResponse.json({
        success: true,
        data: tasks
      })
    }

  } catch (error) {
    console.error('获取生成任务状态失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取任务状态失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 异步生成完整影视作品
async function generateCompleteMovieAsync(
  taskId: string, 
  episode: any, 
  settings: any
) {
  try {
    console.log(`🎬 开始异步生成影视作品 - 任务: ${taskId}`)

    // 更新任务状态
    await updateTaskStatus(taskId, 'analyzing', '分析剧情结构...')

    // 1. 剧情分析和时间轴规划
    const timeline = await createMovieTimeline(episode)
    
    // 更新任务状态
    await updateTaskStatus(taskId, 'generating_video', '生成视频片段...', {
      timeline
    })

    // 2. 生成视频片段（带时间同步）
    const videoSegments = await generateSynchronizedVideoSegments(
      episode, 
      timeline, 
      settings
    )

    // 更新任务状态
    await updateTaskStatus(taskId, 'generating_audio', '生成音频轨道...', {
      timeline,
      videoSegments
    })

    // 3. 生成多轨道音频
    const audioTracks = await generateMultiTrackAudio(
      episode,
      timeline,
      videoSegments,
      settings
    )

    // 更新任务状态
    await updateTaskStatus(taskId, 'synchronizing', '同步音视频...', {
      timeline,
      videoSegments,
      audioTracks
    })

    // 4. 音视频同步和合并
    const finalMovie = await synchronizeAndMerge(
      taskId,
      videoSegments,
      audioTracks,
      timeline,
      settings
    )

    // 更新任务状态
    await updateTaskStatus(taskId, 'completed', '影视作品生成完成', {
      timeline,
      videoSegments,
      audioTracks,
      finalMovie
    })

    console.log(`✅ 影视作品生成完成 - 任务: ${taskId}`)

  } catch (error) {
    console.error(`❌ 影视作品生成失败 - 任务: ${taskId}`, error)
    await updateTaskStatus(taskId, 'failed', `生成失败: ${error.message}`)
  }
}

// 更新任务状态
async function updateTaskStatus(
  taskId: string, 
  status: string, 
  message: string, 
  data?: any
) {
  await prisma.movieGenerationTask.update({
    where: { id: taskId },
    data: {
      status,
      statusMessage: message,
      progress: getProgressByStatus(status),
      result: data ? JSON.stringify(data) : undefined,
      updatedAt: new Date()
    }
  })
}

// 根据状态获取进度百分比
function getProgressByStatus(status: string): number {
  const progressMap = {
    'initializing': 5,
    'analyzing': 15,
    'generating_video': 40,
    'generating_audio': 70,
    'synchronizing': 85,
    'completed': 100,
    'failed': 0
  }
  return progressMap[status] || 0
}

// 创建影视时间轴
async function createMovieTimeline(episode: any) {
  console.log('📅 创建影视时间轴...')
  
  // 分析剧情结构
  const plotInfo = episode.plotInfo
  if (!plotInfo) {
    throw new Error('剧情信息不存在，请先进行剧情分析')
  }

  // 解析详细剧情序列
  const plotSequences = plotInfo.plotSequences ? 
    JSON.parse(plotInfo.plotSequences) : []

  // 创建时间轴
  const timeline = {
    totalDuration: 0,
    segments: [],
    audioTracks: {
      dialogue: [],
      narration: [],
      backgroundMusic: [],
      soundEffects: []
    }
  }

  let currentTime = 0
  
  // 为每个情节序列创建时间段
  for (let i = 0; i < plotSequences.length; i++) {
    const sequence = plotSequences[i]
    const segmentDuration = calculateSegmentDuration(sequence)
    
    const timelineSegment = {
      id: `segment_${i + 1}`,
      sequenceId: sequence.sequenceId,
      startTime: currentTime,
      duration: segmentDuration,
      endTime: currentTime + segmentDuration,
      title: sequence.action,
      description: sequence.description,
      characters: sequence.characters || [],
      location: sequence.location,
      mood: sequence.emotion,
      dialogues: extractDialogues(sequence),
      visualElements: sequence.visualElements
    }
    
    timeline.segments.push(timelineSegment)
    currentTime += segmentDuration
  }
  
  timeline.totalDuration = currentTime
  
  console.log(`✅ 时间轴创建完成，总时长: ${timeline.totalDuration}秒`)
  return timeline
}

// 计算片段时长
function calculateSegmentDuration(sequence: any): number {
  // 基础时长：5秒
  let duration = 5
  
  // 根据对话数量调整
  const dialogueCount = (sequence.dialogue || []).length
  if (dialogueCount > 0) {
    duration += dialogueCount * 2 // 每句对话增加2秒
  }
  
  // 根据动作复杂度调整
  if (sequence.action && sequence.action.length > 50) {
    duration += 3 // 复杂动作增加3秒
  }
  
  // 限制在5-15秒之间
  return Math.max(5, Math.min(15, duration))
}

// 提取对话
function extractDialogues(sequence: any) {
  const dialogues = []
  
  if (sequence.dialogue && Array.isArray(sequence.dialogue)) {
    sequence.dialogue.forEach((line, index) => {
      dialogues.push({
        id: `dialogue_${index}`,
        speaker: line.speaker || '旁白',
        text: line.text,
        emotion: line.emotion || 'neutral',
        startTime: index * 2, // 每句对话间隔2秒
        duration: estimateDialogueDuration(line.text)
      })
    })
  }
  
  return dialogues
}

// 估算对话时长
function estimateDialogueDuration(text: string): number {
  // 中文平均每分钟250字
  const wordsPerMinute = 250
  const minutes = text.length / wordsPerMinute
  return Math.max(minutes * 60, 2) // 最少2秒
}

// 生成同步视频片段
async function generateSynchronizedVideoSegments(
  episode: any,
  timeline: any,
  settings: any
) {
  console.log('🎥 生成同步视频片段...')

  // 模拟视频片段生成（实际应调用真实的视频生成API）
  const videoSegments = []

  for (let i = 0; i < timeline.segments.length; i++) {
    const segment = timeline.segments[i]

    // 模拟视频生成
    const videoSegment = {
      id: `video_segment_${i + 1}`,
      segmentIndex: i + 1,
      title: segment.title,
      description: segment.description,
      startTime: segment.startTime,
      duration: segment.duration,
      endTime: segment.endTime,
      videoUrl: `mock_video_${i + 1}.mp4`, // 模拟视频URL
      status: 'completed',
      metadata: {
        characters: segment.characters,
        location: segment.location,
        mood: segment.mood
      }
    }

    videoSegments.push(videoSegment)
  }

  console.log(`✅ 生成了 ${videoSegments.length} 个视频片段`)
  return videoSegments
}

// 生成多轨道音频
async function generateMultiTrackAudio(
  episode: any,
  timeline: any,
  videoSegments: any,
  settings: any
) {
  console.log('🎵 生成多轨道音频...')
  
  const audioTracks = {
    dialogue: [],
    narration: [],
    backgroundMusic: [],
    soundEffects: []
  }
  
  // 1. 生成对话音频
  for (const segment of timeline.segments) {
    if (segment.dialogues && segment.dialogues.length > 0) {
      for (const dialogue of segment.dialogues) {
        const audioResponse = await fetch('/api/ai/generate-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: dialogue.text,
            emotion: dialogue.emotion,
            characterName: dialogue.speaker,
            format: 'mp3'
          })
        })
        
        if (audioResponse.ok) {
          const audioResult = await audioResponse.json()
          if (audioResult.success) {
            audioTracks.dialogue.push({
              id: dialogue.id,
              segmentId: segment.id,
              startTime: segment.startTime + dialogue.startTime,
              duration: audioResult.data.duration,
              audioUrl: audioResult.data.audioUrl,
              speaker: dialogue.speaker,
              text: dialogue.text
            })
          }
        }
      }
    }
  }
  
  // 2. 生成背景音乐（如果启用）
  if (settings.includeBackgroundMusic) {
    audioTracks.backgroundMusic = await generateBackgroundMusic(timeline)
  }
  
  // 3. 生成音效（如果启用）
  if (settings.includeSoundEffects) {
    audioTracks.soundEffects = await generateSoundEffects(timeline)
  }
  
  return audioTracks
}

// 生成背景音乐
async function generateBackgroundMusic(timeline: any) {
  console.log('🎼 生成背景音乐...')
  
  // 这里可以集成音乐生成AI或使用预设音乐库
  const musicTracks = []
  
  // 根据情绪生成不同的背景音乐
  const moodMusicMap = {
    'happy': 'upbeat_background.mp3',
    'sad': 'melancholy_background.mp3',
    'tense': 'suspense_background.mp3',
    'romantic': 'romantic_background.mp3',
    'action': 'action_background.mp3'
  }
  
  for (const segment of timeline.segments) {
    const musicFile = moodMusicMap[segment.mood] || 'neutral_background.mp3'
    
    musicTracks.push({
      id: `music_${segment.id}`,
      segmentId: segment.id,
      startTime: segment.startTime,
      duration: segment.duration,
      musicFile: musicFile,
      volume: 0.3, // 背景音乐音量较低
      mood: segment.mood
    })
  }
  
  return musicTracks
}

// 生成音效
async function generateSoundEffects(timeline: any) {
  console.log('🔊 生成音效...')
  
  const soundEffects = []
  
  // 根据场景和动作添加音效
  for (const segment of timeline.segments) {
    const effects = detectSoundEffects(segment)
    
    effects.forEach(effect => {
      soundEffects.push({
        id: `effect_${segment.id}_${effect.type}`,
        segmentId: segment.id,
        startTime: segment.startTime + effect.timing,
        duration: effect.duration,
        effectType: effect.type,
        effectFile: effect.file,
        volume: effect.volume
      })
    })
  }
  
  return soundEffects
}

// 检测需要的音效
function detectSoundEffects(segment: any) {
  const effects = []
  const description = segment.description.toLowerCase()
  
  // 简单的关键词匹配
  if (description.includes('门') || description.includes('开门') || description.includes('关门')) {
    effects.push({
      type: 'door',
      file: 'door_sound.mp3',
      timing: 1,
      duration: 2,
      volume: 0.5
    })
  }
  
  if (description.includes('脚步') || description.includes('走路')) {
    effects.push({
      type: 'footsteps',
      file: 'footsteps.mp3',
      timing: 0,
      duration: segment.duration,
      volume: 0.3
    })
  }
  
  if (description.includes('风') || description.includes('微风')) {
    effects.push({
      type: 'wind',
      file: 'wind_sound.mp3',
      timing: 0,
      duration: segment.duration,
      volume: 0.2
    })
  }
  
  return effects
}

// 同步和合并
async function synchronizeAndMerge(
  taskId: string,
  videoSegments: any,
  audioTracks: any,
  timeline: any,
  settings: any
) {
  console.log('🔄 同步和合并音视频...')
  
  // 调用视频合并API，传入音频轨道信息
  const mergeResponse = await fetch('/api/video/merge-segments-with-audio', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      taskId,
      videoSegments,
      audioTracks,
      timeline,
      outputFormat: 'mp4',
      quality: settings.videoQuality
    })
  })
  
  if (!mergeResponse.ok) {
    throw new Error('音视频合并失败')
  }
  
  const mergeResult = await mergeResponse.json()
  return mergeResult.data
}
