// 测试正确的豆包视频生成模型名称
async function testCorrectDoubaoModel() {
  try {
    console.log('🎬 测试正确的豆包视频生成模型名称...');
    
    // 正确的豆包视频生成模型名称
    const correctModelName = 'doubao-seedance-1-0-pro-250528';
    
    console.log('\n📝 正确的模型信息:');
    console.log('   模型名称:', correctModelName);
    console.log('   模型类型: 视频生成模型');
    console.log('   支持功能: 文生视频、图生视频');
    console.log('   基于首帧: 是');
    console.log('   发布日期: 2025年5月28日');
    
    // 1. 保存正确的模型配置
    console.log('\n💾 1. 保存正确的模型配置...');
    
    const saveResponse = await fetch('http://localhost:3000/api/models', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'doubao',
        model: correctModelName,
        name: '豆包 Seedance 1.0 Pro (正确版本)',
        description: '豆包视频生成模型，支持文生视频和图生视频，基于首帧生成高质量视频',
        apiKey: 'test-placeholder-key',
        enabled: false
      })
    });
    
    if (saveResponse.ok) {
      const saveResult = await saveResponse.json();
      console.log('✅ 正确模型配置保存成功:', saveResult.data?.id);
    } else {
      const errorText = await saveResponse.text();
      console.log('❌ 保存失败:', errorText);
    }
    
    // 2. 测试模型连接（使用测试密钥）
    console.log('\n🧪 2. 测试模型连接格式...');
    
    try {
      const testResponse = await fetch('http://localhost:3000/api/models/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'doubao',
          model: correctModelName,
          apiKey: 'test-invalid-key'
        })
      });
      
      const testResult = await testResponse.json();
      
      if (testResult.error && testResult.error.includes('API key')) {
        console.log('✅ 模型名称格式正确（API密钥无效是预期的）');
      } else if (testResult.error && testResult.error.includes('does not exist')) {
        console.log('❌ 模型名称仍然不正确:', testResult.error);
      } else {
        console.log('🔍 测试结果:', testResult);
      }
    } catch (error) {
      console.log('❌ 测试过程中发生错误:', error.message);
    }
    
    // 3. 显示其他可能的豆包视频模型名称
    console.log('\n📋 3. 其他可能的豆包视频模型名称...');
    
    const possibleVideoModels = [
      'doubao-seedance-1-0-pro-250528',
      'doubao-seedance-1.0-pro',
      'doubao-seedance-pro',
      'doubao-video-generation',
      'doubao-seedance-lite',
      'doubao-seedance-1-0-lite'
    ];
    
    possibleVideoModels.forEach((model, index) => {
      console.log(`   ${index + 1}. ${model}`);
    });
    
    // 4. 显示模型权限说明
    console.log('\n🔑 4. 模型权限和开通说明...');
    
    console.log('\n📋 开通步骤:');
    console.log('   1. 访问火山引擎控制台: https://console.volcengine.com/');
    console.log('   2. 进入火山方舟大模型服务平台');
    console.log('   3. 在模型广场中搜索"豆包视频生成"或"Seedance"');
    console.log('   4. 点击模型卡片查看详情');
    console.log('   5. 点击"立即使用"或"申请试用"');
    console.log('   6. 等待审核通过（通常几分钟到几小时）');
    console.log('   7. 审核通过后即可使用该模型');
    
    console.log('\n⚠️ 注意事项:');
    console.log('   • 视频生成模型可能需要单独申请权限');
    console.log('   • 某些模型可能需要企业认证账户');
    console.log('   • 视频生成通常比文本生成消耗更多配额');
    console.log('   • 建议先在控制台测试模型可用性');
    
    // 5. 显示替代方案
    console.log('\n🔄 5. 替代方案...');
    
    console.log('\n如果豆包视频生成模型暂时无法使用，可以考虑:');
    console.log('   1. 使用豆包文本模型生成详细的视频描述');
    console.log('   2. 结合其他视频生成服务（如Runway、Pika等）');
    console.log('   3. 等待模型权限审核通过');
    console.log('   4. 联系火山引擎技术支持获取帮助');
    
    // 6. 显示API调用示例
    console.log('\n📝 6. 正确的API调用示例...');
    
    const apiExample = {
      url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
      },
      body: {
        model: correctModelName,
        messages: [
          {
            role: 'user',
            content: '生成一个5秒的视频：阳光明媚的海滩，海浪轻拍沙滩'
          }
        ],
        max_tokens: 1000
      }
    };
    
    console.log('✅ API调用示例:');
    console.log('   端点:', apiExample.url);
    console.log('   方法:', apiExample.method);
    console.log('   模型:', apiExample.body.model);
    console.log('   格式: OpenAI兼容');
    
    console.log('\n🎉 豆包视频生成模型测试完成！');
    console.log('\n📝 总结:');
    console.log('   ✅ 已更新为正确的模型名称');
    console.log('   ✅ API调用格式正确');
    console.log('   ⚠️ 需要在火山方舟控制台开通模型权限');
    console.log('   📞 如有问题可联系火山引擎技术支持');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testCorrectDoubaoModel();
