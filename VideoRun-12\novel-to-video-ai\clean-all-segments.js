const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanAllSegments() {
  try {
    console.log('🧹 清理所有视频片段...');
    
    // 查询所有视频片段
    const allSegments = await prisma.videoSegment.findMany({
      include: {
        storyVideo: true
      }
    });
    
    console.log(`找到 ${allSegments.length} 个视频片段`);
    
    if (allSegments.length === 0) {
      console.log('✅ 没有片段需要清理');
      return;
    }
    
    // 删除所有片段
    const deleteResult = await prisma.videoSegment.deleteMany({});
    console.log(`🗑️ 已删除 ${deleteResult.count} 个视频片段`);
    
    // 获取相关的故事视频ID
    const storyVideoIds = [...new Set(allSegments.map(s => s.storyVideoId))];
    
    // 删除所有故事视频记录
    for (const storyVideoId of storyVideoIds) {
      try {
        await prisma.storyVideo.delete({
          where: { id: storyVideoId }
        });
        console.log(`🗑️ 已删除故事视频记录: ${storyVideoId}`);
      } catch (error) {
        console.log(`⚠️ 删除故事视频记录失败: ${storyVideoId}`, error.message);
      }
    }
    
    console.log('✅ 清理完成');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanAllSegments();
