const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoTTS() {
  try {
    console.log('🎵 测试豆包TTS API...');
    
    // 1. 检查豆包TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true,
        enabled: true
      }
    });
    
    if (!ttsConfig) {
      console.error('❌ 未找到豆包TTS配置');
      console.log('请先运行: node setup-doubao-tts.js');
      return;
    }
    
    console.log('✅ 找到豆包TTS配置:', ttsConfig.name);
    
    // 2. 测试API调用
    const testText = '你好，这是豆包语音合成的测试。今天天气很好，适合出去走走。';
    
    console.log('📡 调用豆包TTS API...');
    console.log('测试文本:', testText);
    
    // 构建请求参数
    const requestBody = {
      model: 'doubao-tts-v1',
      input: {
        text: testText
      },
      voice: 'zh-CN-XiaoxiaoNeural',
      response_format: 'mp3',
      speed: 1.0,
      pitch: 0
    };
    
    console.log('请求参数:', {
      model: requestBody.model,
      textLength: testText.length,
      voice: requestBody.voice,
      format: requestBody.response_format
    });
    
    // 调用API
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ttsConfig.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('API响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status}`);
      console.error('错误详情:', errorText);
      
      // 尝试解析错误信息
      try {
        const errorJson = JSON.parse(errorText);
        console.error('错误JSON:', JSON.stringify(errorJson, null, 2));
      } catch (e) {
        console.error('错误文本:', errorText);
      }
      return;
    }
    
    // 处理响应
    const contentType = response.headers.get('content-type');
    console.log('响应内容类型:', contentType);
    
    if (contentType && contentType.includes('audio')) {
      // 音频响应
      const audioBuffer = await response.arrayBuffer();
      console.log('✅ 成功获取音频数据');
      console.log('音频大小:', audioBuffer.byteLength, '字节');
      
      // 估算时长
      const estimatedDuration = Math.max(testText.length / 250 * 60, 1);
      console.log('估算时长:', estimatedDuration.toFixed(1), '秒');
      
      // 可以选择保存音频文件进行测试
      const fs = require('fs');
      const audioPath = `test_tts_${Date.now()}.mp3`;
      fs.writeFileSync(audioPath, Buffer.from(audioBuffer));
      console.log('✅ 音频已保存到:', audioPath);
      
    } else {
      // JSON响应
      const result = await response.json();
      console.log('API响应:', JSON.stringify(result, null, 2));
      
      if (result.data && result.data[0] && result.data[0].url) {
        console.log('✅ 获取到音频URL:', result.data[0].url);
      }
    }
    
    // 3. 测试获取声音列表
    console.log('\n🎤 测试获取声音列表...');
    
    const voicesResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/audio/voices', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ttsConfig.apiKey}`
      }
    });
    
    console.log('声音列表API状态:', voicesResponse.status);
    
    if (voicesResponse.ok) {
      const voicesResult = await voicesResponse.json();
      console.log('✅ 成功获取声音列表');
      console.log('可用声音数量:', voicesResult.voices ? voicesResult.voices.length : '未知');
      
      if (voicesResult.voices && voicesResult.voices.length > 0) {
        console.log('前5个声音:');
        voicesResult.voices.slice(0, 5).forEach((voice, index) => {
          console.log(`  ${index + 1}. ${voice.name} (${voice.id})`);
        });
      }
    } else {
      const voicesError = await voicesResponse.text();
      console.warn('⚠️ 获取声音列表失败:', voicesResponse.status);
      console.warn('使用默认声音列表');
    }
    
    console.log('\n🎉 豆包TTS API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.error('网络连接失败，请检查网络设置');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('连接被拒绝，请检查API端点');
    } else {
      console.error('详细错误:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoTTS();
