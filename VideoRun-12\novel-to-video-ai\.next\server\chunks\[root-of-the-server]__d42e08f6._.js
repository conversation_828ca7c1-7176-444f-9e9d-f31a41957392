module.exports = {

"[project]/.next-internal/server/app/api/ai/generate-single-segment/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/app/api/ai/generate-single-segment/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const { segmentId, modelId } = await request.json();
        console.log('🎬 开始生成单个视频片段');
        console.log('📋 参数:', {
            segmentId,
            modelId
        });
        if (!segmentId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少片段ID'
            }, {
                status: 400
            });
        }
        // 获取片段信息
        const segment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.findUnique({
            where: {
                id: segmentId
            },
            include: {
                storyVideo: true
            }
        });
        if (!segment) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '片段不存在'
            }, {
                status: 404
            });
        }
        // 获取项目ID - 需要通过storyVideo查找episode，再查找project
        const storyVideo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].storyVideo.findUnique({
            where: {
                id: segment.storyVideo.id
            },
            include: {
                episode: {
                    include: {
                        project: true
                    }
                }
            }
        });
        if (!storyVideo?.episode?.project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '无法找到关联的项目信息'
            }, {
                status: 404
            });
        }
        const projectId = storyVideo.episode.project.id;
        // 🔒 先决条件检查：主要角色必须有参考图像
        console.log('🔍 检查角色参考图像先决条件...');
        const characterCheck = await checkCharacterImagePrerequisites(projectId);
        if (!characterCheck.success) {
            console.log('❌ 角色图像先决条件检查失败:', characterCheck.error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: characterCheck.error,
                missingCharacters: characterCheck.missingCharacters,
                requirementType: 'character_images'
            }, {
                status: 400
            });
        }
        console.log('✅ 角色图像先决条件检查通过');
        // 检查片段状态
        if (segment.status === 'generating') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '该片段正在生成中，请稍后重试'
            }, {
                status: 400
            });
        }
        if (segment.status === 'completed' && segment.videoUrl) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '该片段已经生成完成'
            }, {
                status: 400
            });
        }
        // 更新片段状态为生成中
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segmentId
            },
            data: {
                status: 'generating',
                updatedAt: new Date()
            }
        });
        console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`);
        // 异步调用内部生成API（不等待完成）
        fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/ai/generate-story-video/segment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                segmentId: segment.id,
                modelId
            })
        }).catch((error)=>{
            console.error(`片段 ${segment.segmentIndex} 生成失败:`, error);
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                message: `片段 ${segment.segmentIndex} 开始生成`,
                segmentId: segment.id,
                segmentIndex: segment.segmentIndex,
                title: segment.title
            }
        });
    } catch (error) {
        console.error('生成单个视频片段失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '生成视频片段失败'
        }, {
            status: 500
        });
    }
}
// 检查角色图像先决条件（复用主API的逻辑）
async function checkCharacterImagePrerequisites(projectId) {
    try {
        console.log(`🔍 检查项目 ${projectId} 的角色图像先决条件`);
        // 获取项目的所有角色
        const characters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
            where: {
                projectId
            },
            select: {
                id: true,
                name: true,
                generatedImages: true
            }
        });
        if (characters.length === 0) {
            return {
                success: false,
                error: '项目中没有角色信息，请先创建角色',
                missingCharacters: []
            };
        }
        console.log(`📋 找到 ${characters.length} 个角色`);
        // 检查每个角色是否有参考图像
        const missingCharacters = [];
        for (const character of characters){
            console.log(`🎭 检查角色: ${character.name}`);
            if (!character.generatedImages) {
                console.log(`❌ 角色 ${character.name} 没有生成图像`);
                missingCharacters.push(character.name);
                continue;
            }
            try {
                const images = JSON.parse(character.generatedImages);
                const hasAllImages = images.front && images.side && images.back;
                if (!hasAllImages) {
                    console.log(`❌ 角色 ${character.name} 的图像不完整`);
                    missingCharacters.push(character.name);
                } else {
                    console.log(`✅ 角色 ${character.name} 有完整的参考图像`);
                }
            } catch (e) {
                console.log(`❌ 角色 ${character.name} 的图像数据解析失败`);
                missingCharacters.push(character.name);
            }
        }
        if (missingCharacters.length > 0) {
            const errorMessage = `以下角色缺少参考图像，请先生成角色形象：${missingCharacters.join('、')}`;
            console.log(`❌ 先决条件检查失败: ${errorMessage}`);
            return {
                success: false,
                error: errorMessage,
                missingCharacters
            };
        }
        console.log('✅ 所有角色都有完整的参考图像');
        return {
            success: true
        };
    } catch (error) {
        console.error('检查角色图像先决条件失败:', error);
        return {
            success: false,
            error: '检查角色图像时发生错误，请稍后重试'
        };
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d42e08f6._.js.map