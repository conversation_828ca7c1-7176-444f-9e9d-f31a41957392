'use client'

import React, { useState, useEffect } from 'react'
import { Play, Pause, Ski<PERSON><PERSON><PERSON><PERSON>, Skip<PERSON><PERSON>, Download, Merge } from 'lucide-react'

interface VideoSegment {
  id: string
  segmentIndex: number
  title: string
  description: string
  videoUrl: string | null
  duration: number
  status: string
  segmentType: string
}

interface StoryVideo {
  id: string
  status: string
  mergedVideoUrl: string | null
  totalDuration: number | null
  segments: VideoSegment[]
}

interface MicroEpisodePlayerProps {
  storyVideo: StoryVideo
  onMergeRequest?: () => void
}

export function MicroEpisodePlayer({ storyVideo, onMergeRequest }: MicroEpisodePlayerProps) {
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMerging, setIsMerging] = useState(false)

  const completedSegments = storyVideo.segments.filter(s => s.status === 'completed' && s.videoUrl)
  const currentSegment = completedSegments[currentSegmentIndex]

  // 自动播放下一个片段
  const handleSegmentEnd = () => {
    if (currentSegmentIndex < completedSegments.length - 1) {
      setCurrentSegmentIndex(currentSegmentIndex + 1)
    } else {
      setIsPlaying(false)
    }
  }

  // 播放控制
  const togglePlay = () => {
    setIsPlaying(!isPlaying)
  }

  const playNext = () => {
    if (currentSegmentIndex < completedSegments.length - 1) {
      setCurrentSegmentIndex(currentSegmentIndex + 1)
    }
  }

  const playPrevious = () => {
    if (currentSegmentIndex > 0) {
      setCurrentSegmentIndex(currentSegmentIndex - 1)
    }
  }

  // 合并视频
  const handleMerge = async () => {
    if (completedSegments.length < 2) {
      alert('需要至少2个完成的片段才能合并')
      return
    }

    setIsMerging(true)
    try {
      const response = await fetch('/api/video/merge-segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          storyVideoId: storyVideo.id,
          outputFormat: 'mp4',
          quality: 'high'
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`视频合并成功！总时长: ${result.data.totalDuration}秒`)
        if (onMergeRequest) {
          onMergeRequest()
        }
      } else {
        const error = await response.text()
        alert(`合并失败: ${error}`)
      }
    } catch (error) {
      alert(`合并失败: ${error}`)
    } finally {
      setIsMerging(false)
    }
  }

  // 获取片段类型的显示样式
  const getSegmentTypeStyle = (type: string) => {
    const styles = {
      environment: 'bg-green-100 text-green-800',
      character: 'bg-blue-100 text-blue-800',
      action: 'bg-red-100 text-red-800',
      emotion: 'bg-purple-100 text-purple-800',
      dialogue: 'bg-yellow-100 text-yellow-800',
      suspense: 'bg-gray-100 text-gray-800'
    }
    return styles[type as keyof typeof styles] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-2">🎬 微剧集播放器</h3>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>总片段: {storyVideo.segments.length}</span>
          <span>已完成: {completedSegments.length}</span>
          {storyVideo.totalDuration && (
            <span>总时长: {storyVideo.totalDuration}秒</span>
          )}
        </div>
      </div>

      {/* 主视频播放区域 */}
      {currentSegment ? (
        <div className="mb-6">
          <div className="bg-black rounded-lg overflow-hidden mb-4">
            <video
              key={currentSegment.id}
              className="w-full h-64 object-contain"
              controls
              autoPlay={isPlaying}
              onEnded={handleSegmentEnd}
              src={currentSegment.videoUrl || undefined}
            >
              您的浏览器不支持视频播放
            </video>
          </div>

          {/* 当前片段信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">
                片段 {currentSegment.segmentIndex}: {currentSegment.title}
              </h4>
              <span className={`px-2 py-1 rounded-full text-xs ${getSegmentTypeStyle(currentSegment.segmentType)}`}>
                {currentSegment.segmentType}
              </span>
            </div>
            <p className="text-gray-600 text-sm mb-2">{currentSegment.description}</p>
            <div className="text-xs text-gray-500">
              时长: {currentSegment.duration}秒 | 
              状态: {currentSegment.status}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center mb-6">
          <p className="text-gray-500">暂无可播放的片段</p>
        </div>
      )}

      {/* 播放控制 */}
      <div className="flex items-center justify-center gap-4 mb-6">
        <button
          onClick={playPrevious}
          disabled={currentSegmentIndex === 0}
          className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
        >
          <SkipBack className="w-5 h-5" />
        </button>

        <button
          onClick={togglePlay}
          disabled={!currentSegment}
          className="p-3 rounded-full bg-blue-500 hover:bg-blue-600 text-white disabled:opacity-50"
        >
          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </button>

        <button
          onClick={playNext}
          disabled={currentSegmentIndex === completedSegments.length - 1}
          className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
        >
          <SkipForward className="w-5 h-5" />
        </button>
      </div>

      {/* 片段列表 */}
      <div className="mb-6">
        <h4 className="font-semibold mb-3">片段列表</h4>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {storyVideo.segments.map((segment, index) => (
            <div
              key={segment.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                index === currentSegmentIndex
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
              onClick={() => {
                if (segment.status === 'completed' && segment.videoUrl) {
                  const completedIndex = completedSegments.findIndex(s => s.id === segment.id)
                  if (completedIndex !== -1) {
                    setCurrentSegmentIndex(completedIndex)
                  }
                }
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">片段 {segment.segmentIndex}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${getSegmentTypeStyle(segment.segmentType)}`}>
                      {segment.segmentType}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{segment.title}</p>
                </div>
                <div className="text-right">
                  <div className={`text-xs px-2 py-1 rounded ${
                    segment.status === 'completed' ? 'bg-green-100 text-green-800' :
                    segment.status === 'generating' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {segment.status}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {segment.duration}秒
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-3">
        <button
          onClick={handleMerge}
          disabled={completedSegments.length < 2 || isMerging}
          className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg disabled:opacity-50"
        >
          <Merge className="w-4 h-4" />
          {isMerging ? '合并中...' : '合并视频'}
        </button>

        {storyVideo.mergedVideoUrl && (
          <a
            href={storyVideo.mergedVideoUrl}
            download
            className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
          >
            <Download className="w-4 h-4" />
            下载完整视频
          </a>
        )}
      </div>
    </div>
  )
}
