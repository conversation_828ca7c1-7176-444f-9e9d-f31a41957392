'use client'

import { useState, useEffect } from 'react'

export default function TestAPIPage() {
  const [characters, setCharacters] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadCharacters()
  }, [])

  const loadCharacters = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/characters?projectId=cmc5yxtyl0000vmcsfqkr7qoi')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        setCharacters(data.data)
      } else {
        throw new Error(data.error || '加载失败')
      }
    } catch (error) {
      console.error('加载失败:', error)
      setError(error instanceof Error ? error.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">❌ 加载失败</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={loadCharacters}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API测试页面</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">角色数据 ({characters.length} 个)</h2>
          
          {characters.length === 0 ? (
            <p className="text-gray-500">暂无角色数据</p>
          ) : (
            <div className="space-y-4">
              {characters.map((character: any, index) => (
                <div key={character.id || index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">{character.name}</h3>
                    {character.consistencyScore && (
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        character.consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :
                        character.consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        一致性: {(character.consistencyScore * 100).toFixed(1)}%
                      </span>
                    )}
                  </div>
                  
                  <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>身份:</strong> {character.identity || '未设置'}
                    </div>
                    <div>
                      <strong>性格:</strong> {character.personality || '未设置'}
                    </div>
                    <div>
                      <strong>外貌:</strong> {character.facial || '未设置'}
                    </div>
                    <div>
                      <strong>详细DNA:</strong> {character.detailedDNA ? '已设置' : '未设置'}
                    </div>
                    <div>
                      <strong>参考图像:</strong> {character.referenceImages ? '已设置' : '未设置'}
                    </div>
                    <div>
                      <strong>全局角色:</strong> {character.isGlobalCharacter ? '是' : '否'}
                    </div>
                  </div>
                  
                  {character.detailedDNA && (
                    <div className="mt-3 p-3 bg-gray-50 rounded">
                      <h4 className="font-medium text-gray-700 mb-2">详细DNA信息</h4>
                      <pre className="text-xs text-gray-600 overflow-auto">
                        {JSON.stringify(JSON.parse(character.detailedDNA), null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
