{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/CharacterDetail.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, User, <PERSON>lette, Save, Edit, <PERSON>rk<PERSON>, RefreshCw, ChevronDown, Eye } from 'lucide-react'\nimport { Character } from '@/types'\nimport ModelSelector from './ModelSelector'\nimport AppearancePreview from './AppearancePreview'\nimport PromptPreview from './PromptPreview'\n\ninterface CharacterDetailProps {\n  character: Character\n  isOpen: boolean\n  onClose: () => void\n  onUpdate?: (character: Character) => void\n}\n\nexport default function CharacterDetail({ \n  character, \n  isOpen, \n  onClose, \n  onUpdate \n}: CharacterDetailProps) {\n  const [activeTab, setActiveTab] = useState<'info' | 'appearance' | 'consistency'>('info')\n  const [isEditing, setIsEditing] = useState(false)\n  const [editedCharacter, setEditedCharacter] = useState(character)\n\n  // AI形象生成相关状态\n  const [selectedModel, setSelectedModel] = useState<string>('')\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [showPreview, setShowPreview] = useState(false)\n  const [generatedAppearance, setGeneratedAppearance] = useState<any>(null)\n\n  // 增强提示词相关状态\n  const [showPromptDropdown, setShowPromptDropdown] = useState(false)\n  const [enhancePrompt, setEnhancePrompt] = useState('')\n  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')\n\n  // 提示词预览相关状态\n  const [showPromptPreview, setShowPromptPreview] = useState(false)\n\n  // 新增：一致性相关状态\n  const [consistencyMode, setConsistencyMode] = useState<'plot' | 'consistency' | 'hybrid'>('hybrid')\n  const [useConsistencyConstraints, setUseConsistencyConstraints] = useState(true)\n  const [generateDetailedDNA, setGenerateDetailedDNA] = useState(true)\n  const [consistencyScore, setConsistencyScore] = useState(character.consistencyScore || 0)\n\n  // 默认增强提示词\n  const defaultEnhancePrompt = '三视图是该角色三个角度的特写，第一张是该角色的正面，第二张是该角色的侧面，第三张是该角色的背面。要求：1，生成的三视图 人物五官和身形要保持一致。2，三视图一定要符合角色描述。3，角色三视图的背景色一样。4，高质量，精美细节。'\n\n\n\n  // 如果弹窗未打开，不渲染\n  if (!isOpen) return null\n\n  // 保存编辑\n  const handleSave = async () => {\n    try {\n      // 调用API保存到数据库\n      const response = await fetch(`/api/characters/${character.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: editedCharacter.name,\n          identity: editedCharacter.identity,\n          personality: editedCharacter.personality,\n          physique: editedCharacter.physique,\n          facial: editedCharacter.facial,\n          hairstyle: editedCharacter.hairstyle,\n          clothing: editedCharacter.clothing\n        })\n      })\n\n      const result = await response.json()\n\n      if (result.success) {\n        // 更新本地状态\n        onUpdate?.(editedCharacter)\n        setIsEditing(false)\n        alert('角色信息保存成功！')\n      } else {\n        throw new Error(result.error || '保存失败')\n      }\n    } catch (error) {\n      console.error('保存角色信息失败:', error)\n      alert('保存失败，请重试')\n    }\n  }\n\n  // 取消编辑\n  const handleCancel = () => {\n    setEditedCharacter(character)\n    setIsEditing(false)\n  }\n\n  // 更新角色信息\n  const updateCharacterField = (field: keyof Character, value: string) => {\n    setEditedCharacter(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n\n\n  // AI生成角色形象\n  const handleGenerateAppearance = async (customPrompt?: string) => {\n    if (!selectedModel) {\n      alert('请先选择AI模型')\n      return\n    }\n\n    setIsGenerating(true)\n    setShowPreview(true)\n\n    // 合并增强提示词\n    let finalPrompt = customPrompt || ''\n    if (savedEnhancePrompt) {\n      finalPrompt = savedEnhancePrompt + (finalPrompt ? ', ' + finalPrompt : '')\n    }\n\n\n\n    try {\n      const response = await fetch('/api/ai/generate-appearance', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          modelId: selectedModel,\n          character: {\n            id: character.id,\n            name: character.name,\n            facial: character.facial,\n            identity: character.identity,\n            personality: character.personality,\n            physique: character.physique,\n            hairstyle: character.hairstyle,\n            clothing: character.clothing,\n            detailedDNA: character.detailedDNA\n          },\n          customPrompt: finalPrompt, // 使用合并后的提示词\n          // 新增：一致性相关参数\n          generateDetailedDNA,\n          useConsistencyConstraints,\n          consistencyMode\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        setGeneratedAppearance(data.data)\n        // 更新一致性评分\n        if (data.data.consistencyScore) {\n          setConsistencyScore(data.data.consistencyScore)\n        }\n      } else {\n        throw new Error(data.error || 'AI生成失败')\n      }\n    } catch (error) {\n      console.error('AI生成形象失败:', error)\n      setGeneratedAppearance(null)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  // 保存生成的形象\n  const handleSaveGeneratedAppearance = async (data: any) => {\n    try {\n      // 准备保存的图像数据\n      const generatedImages = {\n        front: data.front?.url,\n        side: data.side?.url,\n        back: data.back?.url\n      }\n\n      // 调用API保存到数据库\n      const response = await fetch(`/api/characters/${character.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          generatedImages\n        })\n      })\n\n      const result = await response.json()\n\n      if (result.success) {\n        // 更新本地状态\n        const updatedCharacter = {\n          ...character,\n          generatedImages\n        }\n        onUpdate?.(updatedCharacter)\n        setShowPreview(false)\n        alert('角色形象保存成功！')\n      } else {\n        throw new Error(result.error || '保存失败')\n      }\n    } catch (error) {\n      console.error('保存角色形象失败:', error)\n      alert('保存失败，请重试')\n    }\n  }\n\n  // 重新生成（支持自定义提示词）\n  const handleRegenerate = (customPrompt?: string) => {\n    handleGenerateAppearance(customPrompt)\n  }\n\n  // 保存增强提示词\n  const handleSaveEnhancePrompt = () => {\n    setSavedEnhancePrompt(enhancePrompt)\n    setShowPromptDropdown(false)\n    // 保存到localStorage\n    const storageKey = `enhance_prompt_${character.id}`\n    localStorage.setItem(storageKey, enhancePrompt)\n  }\n\n  // 从localStorage加载增强提示词\n  useEffect(() => {\n    if (character.id) {\n      const storageKey = `enhance_prompt_${character.id}`\n      const savedPrompt = localStorage.getItem(storageKey)\n      if (savedPrompt) {\n        setSavedEnhancePrompt(savedPrompt)\n        setEnhancePrompt(savedPrompt)\n      } else {\n        // 如果没有保存的提示词，使用默认提示词\n        setSavedEnhancePrompt(defaultEnhancePrompt)\n        setEnhancePrompt(defaultEnhancePrompt)\n        // 自动保存默认提示词\n        localStorage.setItem(storageKey, defaultEnhancePrompt)\n      }\n    }\n  }, [character.id])\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* 背景遮罩 */}\n      <div \n        className=\"fixed inset-0 bg-black bg-opacity-50\"\n        onClick={onClose}\n      />\n      \n      {/* 弹窗内容 */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div className=\"relative bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[95vh] overflow-hidden\">\n          {/* 头部 */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4\">\n                <User className=\"text-purple-600\" size={24} />\n              </div>\n              <div>\n                <div className=\"flex items-center space-x-3\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">{character.name}</h2>\n                  {consistencyScore > 0 && (\n                    <span className={`px-3 py-1 text-sm rounded-full font-medium ${\n                      consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :\n                      consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      一致性: {(consistencyScore * 100).toFixed(1)}%\n                    </span>\n                  )}\n                </div>\n                <p className=\"text-sm text-gray-500\">人物信息详情</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              {!isEditing ? (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <Edit className=\"mr-1\" size={14} />\n                  编辑\n                </button>\n              ) : (\n                <>\n                  <button\n                    onClick={handleSave}\n                    className=\"inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700\"\n                  >\n                    <Save className=\"mr-1\" size={14} />\n                    保存\n                  </button>\n                  <button\n                    onClick={handleCancel}\n                    className=\"inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                  >\n                    取消\n                  </button>\n                </>\n              )}\n              <button\n                onClick={onClose}\n                className=\"p-2 text-gray-400 hover:text-gray-600\"\n              >\n                <X size={20} />\n              </button>\n            </div>\n          </div>\n\n          {/* 标签页导航 */}\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"flex space-x-8 px-6\">\n              <button\n                onClick={() => setActiveTab('info')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'info'\n                    ? 'border-purple-500 text-purple-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <User className=\"inline mr-2\" size={16} />\n                角色信息\n              </button>\n              <button\n                onClick={() => setActiveTab('appearance')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'appearance'\n                    ? 'border-purple-500 text-purple-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Palette className=\"inline mr-2\" size={16} />\n                形象设置\n              </button>\n              <button\n                onClick={() => setActiveTab('consistency')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'consistency'\n                    ? 'border-purple-500 text-purple-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Sparkles className=\"inline mr-2\" size={16} />\n                一致性管理\n                {consistencyScore > 0 && (\n                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${\n                    consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :\n                    consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-red-100 text-red-800'\n                  }`}>\n                    {(consistencyScore * 100).toFixed(0)}%\n                  </span>\n                )}\n              </button>\n            </nav>\n          </div>\n\n          {/* 标签页内容 */}\n          <div className=\"p-6 overflow-y-auto max-h-[75vh]\">\n            {activeTab === 'info' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                  AI提取的角色信息\n                </h3>\n                \n                {/* 身份 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    身份\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.identity || ''}\n                      onChange={(e) => updateCharacterField('identity', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={3}\n                      placeholder=\"职业、社会地位、背景等身份信息\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.identity || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n\n                {/* 性格 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    性格\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.personality || ''}\n                      onChange={(e) => updateCharacterField('personality', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={4}\n                      placeholder=\"性格特征、行为习惯、心理特点等内在特质\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.personality || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n\n                {/* 身材特征 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    身材特征\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.physique || ''}\n                      onChange={(e) => updateCharacterField('physique', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={3}\n                      placeholder=\"身高、体型、体态、肌肉线条等身体特征\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.physique || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n\n                {/* 五官特征 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    五官特征\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.facial || ''}\n                      onChange={(e) => updateCharacterField('facial', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={3}\n                      placeholder=\"脸型、眼睛、鼻子、嘴唇、眉毛等面部特征\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.facial || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n\n                {/* 发型样式 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    发型样式\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.hairstyle || ''}\n                      onChange={(e) => updateCharacterField('hairstyle', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={3}\n                      placeholder=\"发型样式、发色、发长、发质等头发特征\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.hairstyle || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n\n                {/* 服饰风格 */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    服饰风格\n                  </label>\n                  {isEditing ? (\n                    <textarea\n                      value={editedCharacter.clothing || ''}\n                      onChange={(e) => updateCharacterField('clothing', e.target.value)}\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                      rows={3}\n                      placeholder=\"服装风格、颜色搭配、材质、配饰等穿着特征\"\n                    />\n                  ) : (\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded-md\">\n                      {character.clothing || '暂无信息'}\n                    </p>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'appearance' && (\n              <div className=\"space-y-6\">\n                {/* 已保存的三视图展示 */}\n                {character.generatedImages && (character.generatedImages.front || character.generatedImages.side || character.generatedImages.back) && (\n                  <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                      <User className=\"text-green-600 mr-2\" size={20} />\n                      已保存的角色形象\n                    </h3>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                      {/* 正面视图 */}\n                      {character.generatedImages.front && (\n                        <div className=\"space-y-3\">\n                          <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                            <div className=\"w-3 h-3 bg-purple-500 rounded-full mr-2\"></div>\n                            正面视图\n                          </h5>\n                          <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                            <img\n                              src={character.generatedImages.front}\n                              alt=\"正面视图\"\n                              className=\"w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity\"\n                              onClick={() => {\n                                window.open(character.generatedImages.front, '_blank')\n                              }}\n                            />\n                          </div>\n                        </div>\n                      )}\n\n                      {/* 侧面视图 */}\n                      {character.generatedImages.side && (\n                        <div className=\"space-y-3\">\n                          <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                            <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n                            侧面视图\n                          </h5>\n                          <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                            <img\n                              src={character.generatedImages.side}\n                              alt=\"侧面视图\"\n                              className=\"w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity\"\n                              onClick={() => {\n                                window.open(character.generatedImages.side, '_blank')\n                              }}\n                            />\n                          </div>\n                        </div>\n                      )}\n\n                      {/* 背面视图 */}\n                      {character.generatedImages.back && (\n                        <div className=\"space-y-3\">\n                          <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                            <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                            背面视图\n                          </h5>\n                          <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                            <img\n                              src={character.generatedImages.back}\n                              alt=\"背面视图\"\n                              className=\"w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity\"\n                              onClick={() => {\n                                window.open(character.generatedImages.back, '_blank')\n                              }}\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"mt-4 flex justify-center\">\n                      <button\n                        onClick={() => handleGenerateAppearance()}\n                        disabled={isGenerating || !selectedModel}\n                        className=\"inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <RefreshCw className=\"mr-2\" size={16} />\n                        重新生成形象\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {/* AI生成功能区域 */}\n                <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n                        <Sparkles className=\"text-purple-600 mr-2\" size={20} />\n                        AI一键生成角色形象\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mt-1\">\n                        基于角色信息自动生成正、侧、背三视图，为连续剧人物一致性准备\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"relative flex\">\n                      <button\n                        onClick={() => handleGenerateAppearance()}\n                        disabled={!selectedModel || isGenerating}\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-l-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                      >\n                        <Sparkles className=\"mr-2\" size={16} />\n                        {isGenerating ? '生成中...' : 'AI一键生成角色形象'}\n                      </button>\n\n                      <button\n                        onClick={() => setShowPromptDropdown(!showPromptDropdown)}\n                        disabled={!selectedModel || isGenerating}\n                        className=\"inline-flex items-center px-2 py-2 border border-l-0 border-transparent rounded-r-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                      >\n                        <ChevronDown size={16} />\n                      </button>\n\n                      {/* 增强提示词下拉框 */}\n                      {showPromptDropdown && (\n                        <div className=\"absolute top-full left-0 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10\">\n                          <div className=\"p-4\">\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                              增强提示词设置\n                            </label>\n                            <textarea\n                              value={enhancePrompt}\n                              onChange={(e) => setEnhancePrompt(e.target.value)}\n                              placeholder=\"输入增强提示词...\"\n                              className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none\"\n                              rows={6}\n                            />\n\n                            {/* 保存按钮 */}\n                            <div className=\"mt-3\">\n                              <button\n                                onClick={handleSaveEnhancePrompt}\n                                className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2\"\n                              >\n                                保存增强提示词\n                              </button>\n                            </div>\n\n                            {/* 操作按钮 */}\n                            <div className=\"flex justify-between items-center mt-3\">\n                              <button\n                                onClick={() => {\n                                  setEnhancePrompt(defaultEnhancePrompt)\n                                }}\n                                className=\"text-sm text-purple-600 hover:text-purple-800\"\n                              >\n                                恢复默认\n                              </button>\n                              <button\n                                onClick={() => setShowPromptDropdown(false)}\n                                className=\"text-sm text-gray-600 hover:text-gray-800\"\n                              >\n                                关闭\n                              </button>\n                            </div>\n\n                            {/* 当前保存的提示词预览 */}\n                            {savedEnhancePrompt && (\n                              <div className=\"mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600\">\n                                <div className=\"font-medium mb-1\">当前已保存的增强提示词：</div>\n                                <div className=\"max-h-16 overflow-y-auto\">\n                                  {savedEnhancePrompt}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <ModelSelector\n                      selectedModel={selectedModel}\n                      onModelSelect={setSelectedModel}\n                    />\n\n                    {/* 提示词预览按钮 */}\n                    <button\n                      onClick={() => setShowPromptPreview(true)}\n                      disabled={!selectedModel}\n                      className=\"inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <Eye className=\"mr-2\" size={16} />\n                      预览提示词\n                    </button>\n                  </div>\n                </div>\n\n\n\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n                    AI形象生成说明\n                  </h4>\n                  <p className=\"text-sm text-blue-700\">\n                    使用AI生成功能可以基于角色信息自动生成正、侧、背三视图，确保该角色在不同剧集中保持视觉一致性。\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'consistency' && (\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                  <Sparkles className=\"text-purple-600 mr-2\" size={20} />\n                  角色一致性管理\n                </h3>\n\n                {/* 一致性评分显示 */}\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">当前一致性评分</h4>\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n                        <span>一致性评分</span>\n                        <span>{(consistencyScore * 100).toFixed(1)}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                        <div\n                          className={`h-3 rounded-full transition-all duration-300 ${\n                            consistencyScore >= 0.9 ? 'bg-green-500' :\n                            consistencyScore >= 0.7 ? 'bg-yellow-500' :\n                            'bg-red-500'\n                          }`}\n                          style={{ width: `${consistencyScore * 100}%` }}\n                        />\n                      </div>\n                    </div>\n                    <div className={`px-4 py-2 rounded-lg text-sm font-medium ${\n                      consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :\n                      consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {consistencyScore >= 0.9 ? '优秀' :\n                       consistencyScore >= 0.7 ? '良好' : '需要改进'}\n                    </div>\n                  </div>\n                </div>\n\n                {/* 一致性模式设置 */}\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">一致性模式设置</h4>\n                  <div className=\"space-y-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <label className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50\">\n                        <input\n                          type=\"radio\"\n                          value=\"plot\"\n                          checked={consistencyMode === 'plot'}\n                          onChange={(e) => setConsistencyMode(e.target.value as any)}\n                          className=\"mr-3\"\n                        />\n                        <div>\n                          <div className=\"font-medium text-gray-900\">剧情优先</div>\n                          <div className=\"text-sm text-gray-500\">重视故事情节的自然发展</div>\n                        </div>\n                      </label>\n\n                      <label className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50\">\n                        <input\n                          type=\"radio\"\n                          value=\"consistency\"\n                          checked={consistencyMode === 'consistency'}\n                          onChange={(e) => setConsistencyMode(e.target.value as any)}\n                          className=\"mr-3\"\n                        />\n                        <div>\n                          <div className=\"font-medium text-gray-900\">一致性优先</div>\n                          <div className=\"text-sm text-gray-500\">重视角色外貌的一致性</div>\n                        </div>\n                      </label>\n\n                      <label className=\"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 border-purple-200 bg-purple-50\">\n                        <input\n                          type=\"radio\"\n                          value=\"hybrid\"\n                          checked={consistencyMode === 'hybrid'}\n                          onChange={(e) => setConsistencyMode(e.target.value as any)}\n                          className=\"mr-3\"\n                        />\n                        <div>\n                          <div className=\"font-medium text-purple-900\">混合模式</div>\n                          <div className=\"text-sm text-purple-600\">平衡剧情和一致性（推荐）</div>\n                        </div>\n                      </label>\n                    </div>\n                  </div>\n                </div>\n\n                {/* 一致性约束设置 */}\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h4 className=\"text-md font-medium text-gray-900 mb-4\">约束设置</h4>\n                  <div className=\"space-y-4\">\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={useConsistencyConstraints}\n                        onChange={(e) => setUseConsistencyConstraints(e.target.checked)}\n                        className=\"mr-3\"\n                      />\n                      <div>\n                        <div className=\"font-medium text-gray-900\">启用一致性约束</div>\n                        <div className=\"text-sm text-gray-500\">在生成图像时应用一致性约束条件</div>\n                      </div>\n                    </label>\n\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={generateDetailedDNA}\n                        onChange={(e) => setGenerateDetailedDNA(e.target.checked)}\n                        className=\"mr-3\"\n                      />\n                      <div>\n                        <div className=\"font-medium text-gray-900\">生成详细DNA</div>\n                        <div className=\"text-sm text-gray-500\">自动生成详细的角色外貌特征描述</div>\n                      </div>\n                    </label>\n                  </div>\n                </div>\n\n                {/* 详细DNA信息显示 */}\n                {character.detailedDNA && (\n                  <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                    <h4 className=\"text-md font-medium text-gray-900 mb-4\">详细角色DNA</h4>\n                    <div className=\"space-y-4\">\n                      <div>\n                        <h5 className=\"font-medium text-gray-700 mb-2\">面部特征</h5>\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                          <div>脸型: {character.detailedDNA.facial?.faceShape || '未设置'}</div>\n                          <div>眼型: {character.detailedDNA.facial?.eyeShape || '未设置'}</div>\n                          <div>眼色: {character.detailedDNA.facial?.eyeColor || '未设置'}</div>\n                          <div>鼻型: {character.detailedDNA.facial?.noseShape || '未设置'}</div>\n                          <div>嘴型: {character.detailedDNA.facial?.mouthShape || '未设置'}</div>\n                          <div>肤色: {character.detailedDNA.facial?.skinTone || '未设置'}</div>\n                        </div>\n                        {character.detailedDNA.facial?.facialFeatures && (\n                          <div className=\"mt-2 text-sm\">\n                            <strong>特殊特征:</strong> {character.detailedDNA.facial.facialFeatures}\n                          </div>\n                        )}\n                      </div>\n\n                      {character.detailedDNA.uniqueIdentifiers && character.detailedDNA.uniqueIdentifiers.length > 0 && (\n                        <div>\n                          <h5 className=\"font-medium text-gray-700 mb-2\">独特标识</h5>\n                          <div className=\"flex flex-wrap gap-2\">\n                            {character.detailedDNA.uniqueIdentifiers.map((identifier, index) => (\n                              <span key={index} className=\"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm\">\n                                {identifier}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* 一致性说明 */}\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n                    角色一致性说明\n                  </h4>\n                  <p className=\"text-sm text-blue-700\">\n                    角色一致性功能通过详细的DNA特征描述和参考图像约束，确保同一角色在不同剧集和场景中保持外貌的高度一致性。\n                    一致性评分越高，表示角色在视频生成中的外貌越稳定。\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* AI形象生成预览弹窗 */}\n      <AppearancePreview\n        isOpen={showPreview}\n        onClose={() => setShowPreview(false)}\n        onSave={handleSaveGeneratedAppearance}\n        onRegenerate={handleRegenerate}\n        data={generatedAppearance}\n        isGenerating={isGenerating}\n      />\n\n      {/* 提示词预览弹窗 */}\n      <PromptPreview\n        character={character}\n        customPrompt={savedEnhancePrompt}\n        isOpen={showPromptPreview}\n        onClose={() => setShowPromptPreview(false)}\n        onPromptChange={(prompt) => {\n          // 可以在这里处理提示词变更\n          console.log('新提示词:', prompt)\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;;AAgBe,SAAS,gBAAgB,EACtC,SAAS,EACT,MAAM,EACN,OAAO,EACP,QAAQ,EACa;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,aAAa;IACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEpE,YAAY;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,YAAY;IACZ,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,aAAa;IACb,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAC1F,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,gBAAgB,IAAI;IAEvF,UAAU;IACV,MAAM,uBAAuB;IAI7B,cAAc;IACd,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO;IACP,MAAM,aAAa;QACjB,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,UAAU,EAAE,EAAE,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,gBAAgB,IAAI;oBAC1B,UAAU,gBAAgB,QAAQ;oBAClC,aAAa,gBAAgB,WAAW;oBACxC,UAAU,gBAAgB,QAAQ;oBAClC,QAAQ,gBAAgB,MAAM;oBAC9B,WAAW,gBAAgB,SAAS;oBACpC,UAAU,gBAAgB,QAAQ;gBACpC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS;gBACT,WAAW;gBACX,aAAa;gBACb,MAAM;YACR,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,mBAAmB;QACnB,aAAa;IACf;IAEA,SAAS;IACT,MAAM,uBAAuB,CAAC,OAAwB;QACpD,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAIA,WAAW;IACX,MAAM,2BAA2B,OAAO;QACtC,IAAI,CAAC,eAAe;YAClB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,eAAe;QAEf,UAAU;QACV,IAAI,cAAc,gBAAgB;QAClC,IAAI,oBAAoB;YACtB,cAAc,qBAAqB,CAAC,cAAc,OAAO,cAAc,EAAE;QAC3E;QAIA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,WAAW;wBACT,IAAI,UAAU,EAAE;wBAChB,MAAM,UAAU,IAAI;wBACpB,QAAQ,UAAU,MAAM;wBACxB,UAAU,UAAU,QAAQ;wBAC5B,aAAa,UAAU,WAAW;wBAClC,UAAU,UAAU,QAAQ;wBAC5B,WAAW,UAAU,SAAS;wBAC9B,UAAU,UAAU,QAAQ;wBAC5B,aAAa,UAAU,WAAW;oBACpC;oBACA,cAAc;oBACd,aAAa;oBACb;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,uBAAuB,KAAK,IAAI;gBAChC,UAAU;gBACV,IAAI,KAAK,IAAI,CAAC,gBAAgB,EAAE;oBAC9B,oBAAoB,KAAK,IAAI,CAAC,gBAAgB;gBAChD;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uBAAuB;QACzB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,UAAU;IACV,MAAM,gCAAgC,OAAO;QAC3C,IAAI;YACF,YAAY;YACZ,MAAM,kBAAkB;gBACtB,OAAO,KAAK,KAAK,EAAE;gBACnB,MAAM,KAAK,IAAI,EAAE;gBACjB,MAAM,KAAK,IAAI,EAAE;YACnB;YAEA,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,UAAU,EAAE,EAAE,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS;gBACT,MAAM,mBAAmB;oBACvB,GAAG,SAAS;oBACZ;gBACF;gBACA,WAAW;gBACX,eAAe;gBACf,MAAM;YACR,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,mBAAmB,CAAC;QACxB,yBAAyB;IAC3B;IAEA,UAAU;IACV,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,sBAAsB;QACtB,kBAAkB;QAClB,MAAM,aAAa,CAAC,eAAe,EAAE,UAAU,EAAE,EAAE;QACnD,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,EAAE,EAAE;YAChB,MAAM,aAAa,CAAC,eAAe,EAAE,UAAU,EAAE,EAAE;YACnD,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,sBAAsB;gBACtB,iBAAiB;YACnB,OAAO;gBACL,qBAAqB;gBACrB,sBAAsB;gBACtB,iBAAiB;gBACjB,YAAY;gBACZ,aAAa,OAAO,CAAC,YAAY;YACnC;QACF;IACF,GAAG;QAAC,UAAU,EAAE;KAAC;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAkB,MAAM;;;;;;;;;;;sDAE1C,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAuC,UAAU,IAAI;;;;;;wDAClE,mBAAmB,mBAClB,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,oBAAoB,MAAM,gCAC1B,oBAAoB,MAAM,kCAC1B,2BACA;;gEAAE;gEACI,CAAC,mBAAmB,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAIhD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,0BACA,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;iEAIrC;;8DACE,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAO,MAAM;;;;;;wDAAM;;;;;;;8DAGrC,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;sDAKL,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,sCACA,8EACJ;;0DAEF,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAc,MAAM;;;;;;4CAAM;;;;;;;kDAG5C,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,eACV,sCACA,8EACJ;;0DAEF,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;gDAAc,MAAM;;;;;;4CAAM;;;;;;;kDAG/C,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,gBACV,sCACA,8EACJ;;0DAEF,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAc,MAAM;;;;;;4CAAM;4CAE7C,mBAAmB,mBAClB,8OAAC;gDAAK,WAAW,CAAC,oCAAoC,EACpD,oBAAoB,MAAM,gCAC1B,oBAAoB,MAAM,kCAC1B,2BACA;;oDACC,CAAC,mBAAmB,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/C,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,wBACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAKvD,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,QAAQ,IAAI;oDACnC,UAAU,CAAC,IAAM,qBAAqB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,QAAQ,IAAI;;;;;;;;;;;;sDAM7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,WAAW,IAAI;oDACtC,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;oDACnE,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,WAAW,IAAI;;;;;;;;;;;;sDAMhC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,QAAQ,IAAI;oDACnC,UAAU,CAAC,IAAM,qBAAqB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,QAAQ,IAAI;;;;;;;;;;;;sDAM7B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,MAAM,IAAI;oDACjC,UAAU,CAAC,IAAM,qBAAqB,UAAU,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,MAAM,IAAI;;;;;;;;;;;;sDAM3B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,SAAS,IAAI;oDACpC,UAAU,CAAC,IAAM,qBAAqB,aAAa,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,SAAS,IAAI;;;;;;;;;;;;sDAM9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;gDAG/D,0BACC,8OAAC;oDACC,OAAO,gBAAgB,QAAQ,IAAI;oDACnC,UAAU,CAAC,IAAM,qBAAqB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAChE,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;yEAGd,8OAAC;oDAAE,WAAU;8DACV,UAAU,QAAQ,IAAI;;;;;;;;;;;;;;;;;;gCAOhC,cAAc,8BACb,8OAAC;oCAAI,WAAU;;wCAEZ,UAAU,eAAe,IAAI,CAAC,UAAU,eAAe,CAAC,KAAK,IAAI,UAAU,eAAe,CAAC,IAAI,IAAI,UAAU,eAAe,CAAC,IAAI,mBAChI,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAsB,MAAM;;;;;;wDAAM;;;;;;;8DAIpD,8OAAC;oDAAI,WAAU;;wDAEZ,UAAU,eAAe,CAAC,KAAK,kBAC9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAAgD;;;;;;;8EAGjE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,UAAU,eAAe,CAAC,KAAK;wEACpC,KAAI;wEACJ,WAAU;wEACV,SAAS;4EACP,OAAO,IAAI,CAAC,UAAU,eAAe,CAAC,KAAK,EAAE;wEAC/C;;;;;;;;;;;;;;;;;wDAOP,UAAU,eAAe,CAAC,IAAI,kBAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA8C;;;;;;;8EAG/D,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,UAAU,eAAe,CAAC,IAAI;wEACnC,KAAI;wEACJ,WAAU;wEACV,SAAS;4EACP,OAAO,IAAI,CAAC,UAAU,eAAe,CAAC,IAAI,EAAE;wEAC9C;;;;;;;;;;;;;;;;;wDAOP,UAAU,eAAe,CAAC,IAAI,kBAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAI,WAAU;;;;;;wEAA+C;;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,UAAU,eAAe,CAAC,IAAI;wEACnC,KAAI;wEACJ,WAAU;wEACV,SAAS;4EACP,OAAO,IAAI,CAAC,UAAU,eAAe,CAAC,IAAI,EAAE;wEAC9C;;;;;;;;;;;;;;;;;;;;;;;8DAOV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM;wDACf,UAAU,gBAAgB,CAAC;wDAC3B,WAAU;;0EAEV,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;sDAQhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;wEAAuB,MAAM;;;;;;oEAAM;;;;;;;0EAGzD,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;;;;;;8DAM9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAS,IAAM;oEACf,UAAU,CAAC,iBAAiB;oEAC5B,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;4EAAO,MAAM;;;;;;wEAChC,eAAe,WAAW;;;;;;;8EAG7B,8OAAC;oEACC,SAAS,IAAM,sBAAsB,CAAC;oEACtC,UAAU,CAAC,iBAAiB;oEAC5B,WAAU;8EAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;wEAAC,MAAM;;;;;;;;;;;gEAIpB,oCACC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,8OAAC;gFACC,OAAO;gFACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gFAChD,aAAY;gFACZ,WAAU;gFACV,MAAM;;;;;;0FAIR,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFACC,SAAS;oFACT,WAAU;8FACX;;;;;;;;;;;0FAMH,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,SAAS;4FACP,iBAAiB;wFACnB;wFACA,WAAU;kGACX;;;;;;kGAGD,8OAAC;wFACC,SAAS,IAAM,sBAAsB;wFACrC,WAAU;kGACX;;;;;;;;;;;;4EAMF,oCACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGAAmB;;;;;;kGAClC,8OAAC;wFAAI,WAAU;kGACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEASf,8OAAC,mIAAA,CAAA,UAAa;4DACZ,eAAe;4DACf,eAAe;;;;;;sEAIjB,8OAAC;4DACC,SAAS,IAAM,qBAAqB;4DACpC,UAAU,CAAC;4DACX,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;oEAAO,MAAM;;;;;;gEAAM;;;;;;;;;;;;;;;;;;;sDAQxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;gCAO1C,cAAc,+BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAuB,MAAM;;;;;;gDAAM;;;;;;;sDAKzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;;gFAAM,CAAC,mBAAmB,GAAG,EAAE,OAAO,CAAC;gFAAG;;;;;;;;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAW,CAAC,6CAA6C,EACvD,oBAAoB,MAAM,iBAC1B,oBAAoB,MAAM,kBAC1B,cACA;wEACF,OAAO;4EAAE,OAAO,GAAG,mBAAmB,IAAI,CAAC,CAAC;wEAAC;;;;;;;;;;;;;;;;;sEAInD,8OAAC;4DAAI,WAAW,CAAC,yCAAyC,EACxD,oBAAoB,MAAM,gCAC1B,oBAAoB,MAAM,kCAC1B,2BACA;sEACC,oBAAoB,MAAM,OAC1B,oBAAoB,MAAM,OAAO;;;;;;;;;;;;;;;;;;sDAMxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,oBAAoB;wEAC7B,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wEAClD,WAAU;;;;;;kFAEZ,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAA4B;;;;;;0FAC3C,8OAAC;gFAAI,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAI3C,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,oBAAoB;wEAC7B,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wEAClD,WAAU;;;;;;kFAEZ,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAA4B;;;;;;0FAC3C,8OAAC;gFAAI,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAI3C,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,oBAAoB;wEAC7B,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wEAClD,WAAU;;;;;;kFAEZ,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAA8B;;;;;;0FAC7C,8OAAC;gFAAI,WAAU;0FAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS;oEACT,UAAU,CAAC,IAAM,6BAA6B,EAAE,MAAM,CAAC,OAAO;oEAC9D,WAAU;;;;;;8EAEZ,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAA4B;;;;;;sFAC3C,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAI3C,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS;oEACT,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,OAAO;oEACxD,WAAU;;;;;;8EAEZ,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAA4B;;;;;;sFAC3C,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAO9C,UAAU,WAAW,kBACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAiC;;;;;;8EAC/C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,aAAa;;;;;;;sFACrD,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,YAAY;;;;;;;sFACpD,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,YAAY;;;;;;;sFACpD,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,aAAa;;;;;;;sFACrD,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,cAAc;;;;;;;sFACtD,8OAAC;;gFAAI;gFAAK,UAAU,WAAW,CAAC,MAAM,EAAE,YAAY;;;;;;;;;;;;;gEAErD,UAAU,WAAW,CAAC,MAAM,EAAE,gCAC7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAO;;;;;;wEAAc;wEAAE,UAAU,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;;wDAKxE,UAAU,WAAW,CAAC,iBAAiB,IAAI,UAAU,WAAW,CAAC,iBAAiB,CAAC,MAAM,GAAG,mBAC3F,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAiC;;;;;;8EAC/C,8OAAC;oEAAI,WAAU;8EACZ,UAAU,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,YAAY,sBACxD,8OAAC;4EAAiB,WAAU;sFACzB;2EADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAYzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjD,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,eAAe;gBAC9B,QAAQ;gBACR,cAAc;gBACd,MAAM;gBACN,cAAc;;;;;;0BAIhB,8OAAC,mIAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,cAAc;gBACd,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,gBAAgB,CAAC;oBACf,eAAe;oBACf,QAAQ,GAAG,CAAC,SAAS;gBACvB;;;;;;;;;;;;AAIR", "debugId": null}}]}