# 豆包TTS语音合成接入说明

## 📋 概述

本文档记录了豆包（Doubao）语音合成大模型的接入过程、遇到的问题和解决方案。

## 🎯 接入目标

1. **集成豆包TTS服务**：将豆包的语音合成能力集成到小说转视频系统中
2. **角色声音配置**：为不同角色配置专属的声音特征
3. **多情感支持**：支持根据剧情情感调整语音参数
4. **音视频同步**：确保生成的音频与视频片段完美同步

## 🏗️ 系统架构

### 数据库设计

已完成的数据库模型：

```sql
-- AI配置表（已添加TTS支持）
model AIConfig {
  supportsTTS Boolean @default(false) // 是否支持语音合成
  // ... 其他字段
}

-- 角色声音配置表
model CharacterVoice {
  id              String   @id @default(cuid())
  characterId     String   // 关联的角色ID
  ttsConfigId     String   // 关联的TTS配置ID
  voiceId         String   // TTS服务中的声音ID
  voiceName       String?  // 声音名称
  basePitch       Int      @default(0) // 基础音调 (-20 to +20)
  baseSpeed       Float    @default(1.0) // 基础语速 (0.5 to 2.0)
  baseVolume      Int      @default(80) // 基础音量 (0 to 100)
  emotionMappings String?  // JSON格式的情感映射配置
  sampleAudioUrl  String?  // 示例音频URL
  enabled         Boolean  @default(true) // 是否启用
  // ... 关联关系
}

-- 音频文件表
model AudioFile {
  id            String   @id @default(cuid())
  episodeId     String   // 关联的剧集ID
  segmentIndex  Int      // 视频片段索引
  dialogueType  String   // 对话类型: dialogue, narration, thought
  characterId   String?  // 角色ID（旁白可能为空）
  text          String   // 原始文本
  emotion       String   // 情感类型
  audioUrl      String?  // 音频文件URL
  duration      Float?   // 音频时长（秒）
  fileSize      Int?     // 文件大小（字节）
  format        String   @default("mp3") // 音频格式
  status        String   @default("pending") // pending, processing, completed, failed
  // ... 其他字段
}
```

### API接口设计

已实现的API接口：

1. **TTS生成接口**：`/api/ai/generate-tts`
   - POST：生成语音
   - GET：获取可用声音列表

2. **角色声音配置接口**：`/api/characters/[id]/voice`
   - GET：获取角色声音配置
   - POST：创建/更新声音配置
   - DELETE：删除声音配置

## 🔧 实现状态

### ✅ 已完成

1. **数据库模型设计**：完成了TTS相关的数据库表结构设计
2. **配置脚本**：创建了豆包TTS配置的设置脚本
3. **API接口**：实现了TTS生成和声音配置管理的API
4. **前端组件**：创建了TTS测试面板组件
5. **角色声音配置**：为现有角色自动配置了默认声音

### ⚠️ 遇到的问题

#### API端点问题

测试发现豆包的TTS API端点可能不是标准的 `/api/v3/audio/speech`。尝试了以下端点都返回404：

- `https://ark.cn-beijing.volces.com/api/v3/audio/speech`
- `https://ark.cn-beijing.volces.com/api/v3/tts`
- `https://ark.cn-beijing.volces.com/api/v3/speech/synthesis`
- `https://tts.volces.com/api/v1/tts`

#### 可能的原因

1. **API端点不正确**：豆包的TTS可能使用不同的API路径
2. **认证方式不同**：可能需要不同的认证头或参数
3. **服务未开通**：可能需要在火山引擎控制台单独开通TTS服务
4. **模型名称问题**：`doubao-tts-v1` 可能不是正确的模型名称

## 🔍 调研发现

根据搜索结果，发现以下信息：

1. **火山引擎语音合成**：火山引擎确实提供语音合成服务，但可能需要单独的SDK或API
2. **豆包实时语音**：存在豆包实时语音API，使用WebSocket连接
3. **第三方集成**：一些第三方工具使用火山引擎的TTS服务

## 📋 下一步计划

### 方案一：使用火山引擎语音合成服务

1. **开通服务**：在火山引擎控制台开通语音合成服务
2. **获取正确API**：查找火山引擎语音合成的官方API文档
3. **更新配置**：使用正确的API端点和认证方式

### 方案二：使用豆包实时语音API

1. **WebSocket连接**：实现WebSocket连接到豆包实时语音服务
2. **流式处理**：支持流式语音合成
3. **实时交互**：支持实时语音交互功能

### 方案三：集成其他TTS服务

作为备选方案，可以集成其他TTS服务：

1. **阿里云语音合成**：成熟的中文TTS服务
2. **腾讯云语音合成**：支持多种中文声音
3. **微软Azure Speech**：国际化的语音服务
4. **本地TTS方案**：使用开源TTS模型

## 🛠️ 技术实现

### 当前代码结构

```
src/
├── app/api/ai/generate-tts/route.ts          # TTS生成API
├── app/api/characters/[id]/voice/route.ts    # 角色声音配置API
├── components/tts/TTSPanel.tsx               # TTS测试面板
└── ...

scripts/
├── setup-doubao-tts.js                      # TTS配置脚本
├── test-doubao-tts.js                       # TTS测试脚本
└── ...
```

### 配置信息

- **API密钥**：`e7fc00da-28b5-4628-9c59-588d559cdf1c`
- **基础URL**：`https://ark.cn-beijing.volces.com/api/v3`
- **模型名称**：`doubao-tts-v1`（待确认）

## 🚀 快速开始

### 1. 设置TTS服务

```bash
# 设置Edge TTS（免费方案）
node setup-edge-tts.js

# 或者设置豆包TTS（需要解决API端点问题）
node setup-doubao-tts.js
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 测试TTS功能

```bash
# 测试基础TTS API
node test-tts-api.js

# 测试完整TTS系统
node test-complete-tts-system.js
```

### 4. 使用前端组件

- 访问TTS测试面板进行语音测试
- 使用角色声音配置组件为角色配置专属声音
- 在视频生成流程中集成音频生成

## 📁 文件结构

```
src/
├── app/api/
│   ├── ai/generate-tts/route.ts              # TTS生成API
│   ├── characters/[id]/voice/route.ts        # 角色声音配置API
│   └── episodes/[id]/generate-audio/route.ts # 剧集音频生成API
├── components/tts/
│   ├── TTSPanel.tsx                          # TTS测试面板
│   └── CharacterVoiceConfig.tsx              # 角色声音配置组件
└── ...

scripts/
├── setup-edge-tts.js                        # Edge TTS配置脚本
├── setup-doubao-tts.js                      # 豆包TTS配置脚本
├── test-tts-api.js                          # TTS API测试
└── test-complete-tts-system.js              # 完整系统测试

docs/
└── 豆包TTS接入说明.md                        # 本文档
```

## 🔧 配置说明

### TTS服务配置

在数据库中的`ai_configs`表中配置TTS服务：

```sql
-- Edge TTS配置示例
INSERT INTO ai_configs (
  provider, apiKey, model, name, description,
  enabled, supportsTTS, status
) VALUES (
  'edge-tts', 'edge-tts-free', 'edge-tts-v1',
  'Microsoft Edge TTS (免费)', '基于Microsoft Edge的免费中文语音合成服务',
  true, true, 'connected'
);
```

### 角色声音配置

为每个角色配置专属声音：

```sql
-- 角色声音配置示例
INSERT INTO character_voices (
  characterId, ttsConfigId, voiceId, voiceName,
  basePitch, baseSpeed, baseVolume, emotionMappings
) VALUES (
  'character-id', 'tts-config-id', 'zh-CN-XiaoxiaoNeural', '晓晓（女声，温柔）',
  0, 1.0, 80, '{"neutral": {"pitchAdjust": 0, "speedAdjust": 0, "volumeAdjust": 0}}'
);
```

## 📚 参考资料

1. [火山引擎语音合成文档](https://www.volcengine.com/docs/6561/1257584)
2. [豆包大模型API文档](https://www.volcengine.com/docs/82379/1494384)
3. [火山方舟大模型服务平台](https://www.volcengine.com/docs/82379)
4. [Microsoft Edge TTS](https://github.com/rany2/edge-tts)
5. [语音合成标记语言(SSML)](https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/speech-synthesis-markup)

## ✅ 解决方案

由于豆包TTS API端点问题，我们实现了一个多TTS服务支持的架构，并集成了Microsoft Edge TTS作为免费的替代方案。

### 实现的功能

1. **多TTS服务支持**：
   - 豆包TTS（待API端点确认）
   - Microsoft Edge TTS（免费，已实现）
   - 可扩展支持其他TTS服务

2. **完整的API接口**：
   - `/api/ai/generate-tts` - 语音生成和声音列表
   - `/api/characters/[id]/voice` - 角色声音配置管理

3. **角色声音配置**：
   - 为每个角色配置专属声音
   - 支持情感映射和参数调节
   - 自动为现有角色配置默认声音

4. **前端组件**：
   - TTS测试面板
   - 声音参数调节
   - 实时预览和下载

### 使用方法

1. **设置Edge TTS**：
   ```bash
   node setup-edge-tts.js
   ```

2. **测试TTS功能**：
   ```bash
   # 启动开发服务器
   npm run dev

   # 运行测试
   node test-tts-api.js
   ```

3. **在前端使用**：
   - 访问TTS测试面板
   - 配置角色声音
   - 生成和预览语音

## 🎯 总结

豆包TTS的接入工作已经完成了完整的架构设计和实现。虽然豆包的直接API调用遇到了端点问题，但我们成功实现了：

1. **完整的TTS系统架构**：支持多种TTS服务
2. **Edge TTS集成**：免费的高质量中文语音合成
3. **角色声音管理**：完整的角色声音配置系统
4. **可扩展设计**：易于添加新的TTS服务

系统现在可以正常使用Edge TTS进行语音合成，一旦豆包的API端点问题解决，可以无缝切换到豆包TTS服务。
