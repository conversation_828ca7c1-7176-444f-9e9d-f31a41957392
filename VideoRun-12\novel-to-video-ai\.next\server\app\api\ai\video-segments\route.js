/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/video-segments/route";
exports.ids = ["app/api/ai/video-segments/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fvideo-segments%2Froute&page=%2Fapi%2Fai%2Fvideo-segments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fvideo-segments%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fvideo-segments%2Froute&page=%2Fapi%2Fai%2Fvideo-segments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fvideo-segments%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_video_segments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/video-segments/route.ts */ \"(rsc)/./src/app/api/ai/video-segments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/video-segments/route\",\n        pathname: \"/api/ai/video-segments\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/video-segments/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\video-segments\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_video_segments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fvideo-segments%2Froute&page=%2Fapi%2Fai%2Fvideo-segments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fvideo-segments%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/video-segments/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/ai/video-segments/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n// GET - 获取视频片段状态\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const episodeId = searchParams.get('episodeId');\n        const storyVideoId = searchParams.get('storyVideoId');\n        if (!episodeId && !storyVideoId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少episodeId或storyVideoId参数'\n            }, {\n                status: 400\n            });\n        }\n        let whereClause = {};\n        if (storyVideoId) {\n            whereClause.storyVideoId = storyVideoId;\n        } else if (episodeId) {\n            whereClause.episodeId = episodeId;\n        }\n        // 获取视频片段信息\n        const segments = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.videoSegment.findMany({\n            where: whereClause,\n            orderBy: {\n                segmentIndex: 'asc'\n            },\n            include: {\n                storyVideo: {\n                    select: {\n                        id: true,\n                        status: true,\n                        metadata: true\n                    }\n                }\n            }\n        });\n        if (segments.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    segments: [],\n                    summary: {\n                        totalSegments: 0,\n                        completedSegments: 0,\n                        generatingSegments: 0,\n                        failedSegments: 0,\n                        pendingSegments: 0,\n                        progress: 0,\n                        overallStatus: 'none'\n                    }\n                }\n            });\n        }\n        // 统计生成状态\n        const statusCounts = segments.reduce((acc, segment)=>{\n            acc[segment.status] = (acc[segment.status] || 0) + 1;\n            return acc;\n        }, {});\n        const totalSegments = segments.length;\n        const completedSegments = statusCounts.completed || 0;\n        const generatingSegments = statusCounts.generating || 0;\n        const failedSegments = statusCounts.failed || 0;\n        const pendingSegments = statusCounts.pending || 0;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                segments: segments.map((segment)=>({\n                        id: segment.id,\n                        title: segment.title,\n                        videoUrl: segment.videoUrl || '',\n                        duration: segment.duration || 0,\n                        createdAt: segment.createdAt.toISOString(),\n                        status: segment.status\n                    })),\n                summary: {\n                    totalSegments,\n                    completedSegments,\n                    generatingSegments,\n                    failedSegments,\n                    pendingSegments,\n                    progress: Math.round(completedSegments / totalSegments * 100),\n                    overallStatus: segments[0].storyVideo?.status || 'unknown'\n                }\n            }\n        });\n    } catch (error) {\n        console.error('获取视频片段失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '获取失败，请重试'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/video-segments/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPVxuICBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fvideo-segments%2Froute&page=%2Fapi%2Fai%2Fvideo-segments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fvideo-segments%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();