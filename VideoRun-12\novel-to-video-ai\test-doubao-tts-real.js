const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 火山引擎认证信息
const ACCESS_KEY_ID = 'AKLTOTgwMzIxY2VlNDIxNDNiMWFlZjAzOWY1OTU3ZDIwOWE';
const SECRET_ACCESS_KEY = 'WXpBMU9ETmtNamxoTmpZMk5EQTNZV0psWVdZelpqRXlOREkxT1dJM01ETQ==';

// 生成请求ID
function generateReqId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 生成访问令牌（简化版）
function generateAccessToken() {
  // 这里应该使用火山引擎的签名算法
  // 目前使用简化版本进行测试
  return Buffer.from(`${ACCESS_KEY_ID}:${SECRET_ACCESS_KEY}`).toString('base64');
}

async function testDoubaoTTSReal() {
  try {
    console.log('🎵 测试真实豆包TTS API...');
    
    // 1. 检查豆包TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true,
        enabled: true
      }
    });
    
    if (!ttsConfig) {
      console.error('❌ 未找到豆包TTS配置');
      console.log('请先运行: node setup-doubao-tts.js');
      return;
    }
    
    console.log('✅ 找到豆包TTS配置:', ttsConfig.name);
    
    // 2. 测试豆包TTS API调用
    const testText = '你好，这是豆包语音合成的真实测试。今天我们要验证API是否正常工作。';
    
    console.log('📡 调用豆包TTS API...');
    console.log('测试文本:', testText);
    
    // 构建豆包TTS请求参数（按照官方文档格式）
    const requestBody = {
      app: {
        appid: "doubao_tts_app", // 应用标识
        token: "access_token", // 应用令牌  
        cluster: "volcano_tts" // 业务集群
      },
      user: {
        uid: "test_user_001" // 用户标识
      },
      audio: {
        voice_type: "zh_male_M392_conversation_wvae_bigtts", // 音色类型
        encoding: "mp3", // 音频编码格式
        speed_ratio: 1.0, // 语速
        rate: 24000, // 音频采样率
        bitrate: 160 // 比特率
      },
      request: {
        reqid: generateReqId(), // 请求标识
        text: testText, // 合成语音的文本
        operation: "query" // 操作类型（HTTP只能用query）
      }
    };
    
    console.log('请求参数:', {
      textLength: testText.length,
      voice_type: requestBody.audio.voice_type,
      encoding: requestBody.audio.encoding,
      reqid: requestBody.request.reqid
    });
    
    // 调用豆包TTS API
    console.log('🔗 API端点: https://openspeech.bytedance.com/api/v1/tts');
    
    const response = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer;${generateAccessToken()}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('API响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status}`);
      console.error('错误详情:', errorText);
      
      // 尝试解析错误信息
      try {
        const errorJson = JSON.parse(errorText);
        console.error('错误JSON:', JSON.stringify(errorJson, null, 2));
        
        // 分析常见错误
        if (errorJson.message && errorJson.message.includes('authenticate')) {
          console.error('🔑 认证失败：请检查AccessKeyID和SecretAccessKey是否正确');
        } else if (errorJson.message && errorJson.message.includes('quota')) {
          console.error('📊 配额不足：请检查账户余额或配额设置');
        }
      } catch (e) {
        console.error('错误文本:', errorText);
      }
      return;
    }
    
    // 处理响应
    const result = await response.json();
    console.log('✅ API调用成功');
    console.log('响应结果:', {
      code: result.code,
      message: result.message,
      sequence: result.sequence,
      hasData: !!result.data,
      dataLength: result.data ? result.data.length : 0
    });
    
    if (result.code === 3000) {
      console.log('🎉 语音合成成功！');
      
      if (result.data) {
        // 保存音频文件进行测试
        const fs = require('fs');
        const audioBuffer = Buffer.from(result.data, 'base64');
        const audioPath = `doubao_tts_${Date.now()}.mp3`;
        fs.writeFileSync(audioPath, audioBuffer);
        console.log('✅ 音频已保存到:', audioPath);
        console.log('音频大小:', audioBuffer.length, '字节');
        
        // 获取音频时长
        if (result.addition && result.addition.duration) {
          const duration = parseFloat(result.addition.duration) / 1000;
          console.log('音频时长:', duration.toFixed(1), '秒');
        }
      }
    } else {
      console.error('❌ 语音合成失败');
      console.error('错误码:', result.code);
      console.error('错误信息:', result.message);
      
      // 分析错误码
      switch (result.code) {
        case 3001:
          console.error('💡 建议：检查请求参数是否正确');
          break;
        case 3003:
          console.error('💡 建议：降低并发请求数量');
          break;
        case 3005:
          console.error('💡 建议：稍后重试');
          break;
        case 3010:
          console.error('💡 建议：减少文本长度');
          break;
        case 3011:
          console.error('💡 建议：检查文本内容是否有效');
          break;
        case 3050:
          console.error('💡 建议：检查voice_type参数');
          break;
      }
    }
    
    // 3. 测试不同的音色
    console.log('\n🎤 测试不同音色...');
    
    const voiceTypes = [
      'zh_female_F001_conversation_wvae_bigtts',
      'zh_male_M001_news_wvae_bigtts',
      'zh_female_F002_news_wvae_bigtts'
    ];
    
    for (const voiceType of voiceTypes) {
      console.log(`测试音色: ${voiceType}`);
      
      const voiceTestBody = {
        ...requestBody,
        audio: {
          ...requestBody.audio,
          voice_type: voiceType
        },
        request: {
          ...requestBody.request,
          reqid: generateReqId(),
          text: `这是${voiceType}音色的测试。`
        }
      };
      
      try {
        const voiceResponse = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer;${generateAccessToken()}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(voiceTestBody)
        });
        
        if (voiceResponse.ok) {
          const voiceResult = await voiceResponse.json();
          if (voiceResult.code === 3000) {
            console.log(`  ✅ ${voiceType} 测试成功`);
          } else {
            console.log(`  ❌ ${voiceType} 测试失败: ${voiceResult.message}`);
          }
        } else {
          console.log(`  ❌ ${voiceType} API调用失败: ${voiceResponse.status}`);
        }
      } catch (error) {
        console.log(`  ❌ ${voiceType} 测试异常:`, error.message);
      }
      
      // 避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎉 豆包TTS真实API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.error('🌐 网络连接失败，请检查网络设置');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔗 连接被拒绝，请检查API端点');
    } else {
      console.error('详细错误:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 显示使用说明
function showUsage() {
  console.log('🔧 豆包TTS真实API测试');
  console.log('');
  console.log('使用的认证信息:');
  console.log(`AccessKeyID: ${ACCESS_KEY_ID}`);
  console.log(`SecretAccessKey: ${SECRET_ACCESS_KEY.substring(0, 20)}...`);
  console.log('');
  console.log('API端点: https://openspeech.bytedance.com/api/v1/tts');
  console.log('');
  console.log('注意事项:');
  console.log('1. 确保已在火山引擎控制台开通语音合成服务');
  console.log('2. 确保账户有足够的配额');
  console.log('3. 当前使用简化的认证方式，生产环境需要完整的签名算法');
  console.log('');
}

async function main() {
  showUsage();
  await testDoubaoTTSReal();
}

main();
