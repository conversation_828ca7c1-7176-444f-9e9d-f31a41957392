{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/skyreels.ts"], "sourcesContent": ["import { AIConfig } from '@/types'\n\n// SkyReels-V2 API响应类型\nexport interface SkyReelsTaskResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsStatusResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  progress: number\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsHealthResponse {\n  status: 'healthy' | 'unhealthy'\n  model_loaded: boolean\n  timestamp: string\n}\n\nexport interface SkyReelsGenerationParams {\n  prompt: string\n  num_frames?: number\n  guidance_scale?: number\n  seed?: number\n  fps?: number\n  resolution?: string\n}\n\n// SkyReels-V2 API客户端\nexport class SkyReelsClient {\n  private baseUrl: string\n  private apiKey: string\n  private model: string\n\n  constructor(config: AIConfig) {\n    // SkyReels是本地API，apiKey用作baseUrl\n    this.baseUrl = config.apiKey || 'http://localhost:8000'\n    this.apiKey = config.apiKey\n    this.model = config.model || 'SkyReels-V2-DF-1.3B-540P'\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(`${this.baseUrl}/health`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data: SkyReelsHealthResponse = await response.json()\n      return data.status === 'healthy' && data.model_loaded\n    } catch (error) {\n      console.error('SkyReels连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 生成视频\n  async generateVideo(params: SkyReelsGenerationParams): Promise<SkyReelsTaskResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: params.prompt,\n          num_frames: params.num_frames || 257, // 默认10秒视频\n          guidance_scale: params.guidance_scale || 6.0,\n          seed: params.seed,\n          fps: params.fps || 24,\n          resolution: params.resolution || '540P',\n        }),\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsTaskResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels视频生成失败:', error)\n      throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 查询任务状态\n  async getTaskStatus(taskId: string): Promise<SkyReelsStatusResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/status/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsStatusResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels状态查询失败:', error)\n      throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 下载视频\n  async downloadVideo(taskId: string): Promise<Blob> {\n    try {\n      const response = await fetch(`${this.baseUrl}/download/${taskId}`, {\n        method: 'GET',\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      return await response.blob()\n    } catch (error) {\n      console.error('SkyReels视频下载失败:', error)\n      throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 等待任务完成并返回视频URL\n  async generateAndWait(\n    params: SkyReelsGenerationParams,\n    maxWaitTime: number = 1800000, // 30分钟\n    pollInterval: number = 5000 // 5秒\n  ): Promise<string> {\n    console.log('🎬 开始SkyReels视频生成...')\n    \n    // 开始生成\n    const task = await this.generateVideo(params)\n    console.log(`📝 任务已创建: ${task.task_id}`)\n\n    const startTime = Date.now()\n    \n    // 轮询状态直到完成\n    while (Date.now() - startTime < maxWaitTime) {\n      const status = await this.getTaskStatus(task.task_id)\n      console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`)\n\n      if (status.status === 'completed') {\n        console.log('✅ 视频生成完成!')\n        return status.video_path || ''\n      } else if (status.status === 'failed') {\n        throw new Error(`视频生成失败: ${status.error || '未知错误'}`)\n      }\n\n      // 等待下次轮询\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n\n    throw new Error('视频生成超时')\n  }\n\n  // 获取所有任务列表\n  async getTasks(): Promise<any[]> {\n    try {\n      const response = await fetch(`${this.baseUrl}/tasks`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data = await response.json()\n      return data.tasks || []\n    } catch (error) {\n      console.error('SkyReels任务列表获取失败:', error)\n      throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n}\n\n// AI服务错误类\nexport class SkyReelsServiceError extends Error {\n  constructor(message: string, public statusCode?: number) {\n    super(message)\n    this.name = 'SkyReelsServiceError'\n  }\n}\n"], "names": [], "mappings": ";;;;AAoCO,MAAM;IACH,QAAe;IACf,OAAc;IACd,MAAa;IAErB,YAAY,MAAgB,CAAE;QAC5B,iCAAiC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,IAAI;QAChC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;IAC/B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO,KAAK,MAAM,KAAK,aAAa,KAAK,YAAY;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAgC,EAAiC;QACnF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,OAAO,MAAM;oBACrB,YAAY,OAAO,UAAU,IAAI;oBACjC,gBAAgB,OAAO,cAAc,IAAI;oBACzC,MAAM,OAAO,IAAI;oBACjB,KAAK,OAAO,GAAG,IAAI;oBACnB,YAAY,OAAO,UAAU,IAAI;gBACnC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAAc,EAAmC;QACnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAc,EAAiB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,iBAAiB;IACjB,MAAM,gBACJ,MAAgC,EAChC,cAAsB,OAAO,EAC7B,eAAuB,KAAK,KAAK;IAAN,EACV;QACjB,QAAQ,GAAG,CAAC;QAEZ,OAAO;QACP,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;QACtC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAEvC,MAAM,YAAY,KAAK,GAAG;QAE1B,WAAW;QACX,MAAO,KAAK,GAAG,KAAK,YAAY,YAAa;YAC3C,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,OAAO;YACpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,OAAO,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,UAAU,IAAI;YAC9B,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU;gBACrC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,QAAQ;YACrD;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,WAAW;IACX,MAAM,WAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,KAAK,IAAI,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;AACF;AAGO,MAAM,6BAA6B;;IACxC,YAAY,OAAe,EAAE,AAAO,UAAmB,CAAE;QACvD,KAAK,CAAC,eAD4B,aAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/ai.ts"], "sourcesContent": ["import { AIConfig, Character, Episode, PlotInfo, AnalysisResponse } from '@/types'\nimport { SkyReelsClient } from './skyreels'\n\n// AI服务错误类\nexport class AIServiceError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode: number = 500\n  ) {\n    super(message)\n    this.name = 'AIServiceError'\n  }\n}\n\n// DeepSeek API客户端\nexport class DeepSeekClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string = 'https://api.deepseek.com/v1/chat/completions'\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: '测试连接' }],\n          max_tokens: 10,\n        }),\n      })\n\n      return response.ok\n    } catch (error) {\n      console.error('DeepSeek连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 调用AI API的通用方法（公开方法）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: prompt }],\n          max_tokens: maxTokens,\n          temperature: 0.7,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new AIServiceError(\n          errorData.error?.message || 'API调用失败',\n          'API_ERROR',\n          response.status\n        )\n      }\n\n      const data = await response.json()\n      return data.choices[0]?.message?.content || ''\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        'AI服务调用失败，请检查网络连接',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 生成测试响应\n  private generateTestResponse(prompt: string): string {\n    if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {\n      // 角色一致性分析的测试响应\n      return `{\n        \"characters\": [\n          {\n            \"name\": \"张小雅\",\n            \"identity\": \"高中生\",\n            \"appearance\": \"马尾辫，甜美笑容，青春活泼\",\n            \"personality\": \"开朗活泼，善良纯真\",\n            \"clothing\": \"校服或休闲装\",\n            \"role\": \"主要角色\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"zhang_xiaoya\",\n              \"consistencyMatch\": 0.95,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持马尾辫和甜美笑容的标志性特征\"\n            }\n          },\n          {\n            \"name\": \"王美丽\",\n            \"identity\": \"咖啡店老板\",\n            \"appearance\": \"瓜子脸，波浪卷发，温柔气质\",\n            \"personality\": \"温柔善良，热情好客\",\n            \"clothing\": \"简约优雅的服装\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"wang_meili\",\n              \"consistencyMatch\": 0.88,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持瓜子脸和波浪卷发的特征\"\n            }\n          },\n          {\n            \"name\": \"李明轩\",\n            \"identity\": \"大学教授\",\n            \"appearance\": \"方脸，花白短发，学者气质\",\n            \"personality\": \"温文尔雅，博学睿智\",\n            \"clothing\": \"正式的学者装扮\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"li_mingxuan\",\n              \"consistencyMatch\": 0.92,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持方脸和花白短发的学者形象\"\n            }\n          },\n          {\n            \"name\": \"林志强\",\n            \"identity\": \"程序员\",\n            \"appearance\": \"高瘦身材，黑框眼镜，简约穿着\",\n            \"personality\": \"内向专注，技术宅\",\n            \"clothing\": \"白色T恤，牛仔裤\",\n            \"role\": \"新角色\",\n            \"isKnownCharacter\": false,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": null,\n              \"consistencyMatch\": 0.0,\n              \"differences\": [\"新出现的角色\"],\n              \"consistencyConstraints\": \"建立新的角色DNA档案\"\n            }\n          }\n        ]\n      }`\n    } else if (prompt.includes('场景信息')) {\n      return `{\n        \"scenes\": [\n          {\n            \"location\": \"温馨咖啡店\",\n            \"description\": \"装修温馨的小型咖啡店，木质桌椅，暖色调灯光\",\n            \"atmosphere\": \"温馨舒适，充满生活气息\",\n            \"timeOfDay\": \"下午\",\n            \"lighting\": \"暖色调室内灯光\",\n            \"keyElements\": \"咖啡香味，轻柔音乐，温馨装饰\"\n          }\n        ]\n      }`\n    } else if (prompt.includes('情节序列')) {\n      return `{\n        \"plotSequences\": [\n          {\n            \"sequenceId\": \"reunion_1\",\n            \"action\": \"张小雅进入咖啡店与王美丽重逢\",\n            \"emotion\": \"惊喜和温暖\",\n            \"duration\": \"2分钟\",\n            \"keyMoments\": [\"进门\", \"相认\", \"拥抱\"],\n            \"visualElements\": \"特写表情变化，温馨的重逢场面\"\n          },\n          {\n            \"sequenceId\": \"professor_arrival\",\n            \"action\": \"李明轩教授进入咖啡店\",\n            \"emotion\": \"温和友善\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"打招呼\"],\n            \"visualElements\": \"学者风度，温和笑容\"\n          },\n          {\n            \"sequenceId\": \"new_character\",\n            \"action\": \"林志强询问WiFi\",\n            \"emotion\": \"略显紧张的初次见面\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"询问\", \"介绍\"],\n            \"visualElements\": \"新角色登场，技术宅形象\"\n          }\n        ]\n      }`\n    } else {\n      return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽'\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n    \n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析AI响应失败:', error)\n      throw new AIServiceError(\n        'AI响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n    \n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// 豆包 (Doubao) API客户端\nexport class DoubaoClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string\n  private isVideoModel: boolean\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model || 'doubao-seedance-1.0-pro'\n    // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID\n    this.isVideoModel = this.model.includes('seedance') ||\n                       this.model.includes('video') ||\n                       this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式\n\n    // 根据模型类型选择正确的API端点\n    if (this.isVideoModel) {\n      // 豆包视频生成使用专门的视频生成API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'\n    } else {\n      // 文本模型使用chat completions API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'\n    }\n\n    if (config.baseUrl) {\n      this.baseUrl = config.baseUrl\n    }\n  }\n\n  // 测试API连接（带重试机制）\n  async testConnection(): Promise<boolean> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: \"测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p\"\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: '测试连接' }],\n            max_tokens: 10\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          return true\n        }\n\n        // 检查是否是可重试的错误\n        const errorText = await response.text()\n        if (errorText.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        return false\n      } catch (error) {\n        console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error)\n        if (attempt < maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n        return false\n      }\n    }\n\n    return false\n  }\n\n  // 调用AI API的通用方法（带重试机制）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: prompt }],\n            max_tokens: maxTokens,\n            temperature: 0.7\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          const data = await response.json()\n\n          if (this.isVideoModel) {\n            // 视频生成返回任务信息\n            return JSON.stringify({\n              task_id: data.task_id,\n              status: data.status || 'submitted',\n              message: '视频生成任务已提交，请稍后查询结果'\n            })\n          } else {\n            // 文本生成返回内容\n            return data.choices[0]?.message?.content || ''\n          }\n        }\n\n        const errorData = await response.json()\n        const errorMessage = errorData.error?.message || '豆包API调用失败'\n\n        // 检查是否是可重试的内部错误\n        if (errorMessage.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        // 不可重试的错误，直接抛出\n        throw new AIServiceError(\n          errorMessage,\n          'API_ERROR',\n          response.status\n        )\n      } catch (error) {\n        if (error instanceof AIServiceError) {\n          // 如果是已知的API错误且不可重试，直接抛出\n          throw error\n        }\n\n        // 网络错误等，可以重试\n        if (attempt < maxRetries) {\n          console.log(`豆包API调用失败，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        throw new AIServiceError(\n          '豆包服务调用失败，请检查网络连接和API密钥',\n          'NETWORK_ERROR',\n          500\n        )\n      }\n    }\n\n    throw new AIServiceError(\n      '豆包服务调用失败，已达到最大重试次数',\n      'MAX_RETRIES_EXCEEDED',\n      500\n    )\n  }\n\n  // 专门的视频生成方法\n  async generateVideo(prompt: string, duration: number = 5): Promise<string> {\n    if (!this.isVideoModel) {\n      throw new Error('此模型不支持视频生成')\n    }\n\n    try {\n      const requestBody = {\n        model: this.model,\n        prompt: prompt,\n        video_setting: {\n          video_duration: duration,\n          video_aspect_ratio: '16:9',\n          video_resolution: '720p'\n        }\n      }\n\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        return JSON.stringify({\n          task_id: data.task_id,\n          status: data.status || 'submitted',\n          message: '视频生成任务已提交，请稍后查询结果'\n        })\n      }\n\n      const errorData = await response.json()\n      throw new AIServiceError(\n        errorData.error?.message || '视频生成失败',\n        'VIDEO_GENERATION_ERROR',\n        response.status\n      )\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        '视频生成服务调用失败',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n\n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析豆包响应失败:', error)\n      throw new AIServiceError(\n        '豆包响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n\n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析豆包剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '豆包剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// AI客户端工厂函数\nexport function createAIClient(config: AIConfig): DeepSeekClient | DoubaoClient | SkyReelsClient {\n  switch (config.provider) {\n    case 'deepseek':\n      return new DeepSeekClient(config)\n    case 'doubao':\n      return new DoubaoClient(config)\n    case 'skyreels':\n      return new SkyReelsClient(config)\n    default:\n      // 默认使用DeepSeek客户端，但可以扩展支持其他提供商\n      return new DeepSeekClient(config)\n  }\n}\n\n// 错误处理包装器\nexport async function handleAIRequest<T>(\n  request: () => Promise<T>\n): Promise<T> {\n  try {\n    return await request()\n  } catch (error) {\n    if (error instanceof AIServiceError) {\n      throw error\n    }\n    \n    // 网络错误\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new AIServiceError(\n        'AI服务连接失败，请检查网络连接',\n        'CONNECTION_ERROR',\n        503\n      )\n    }\n    \n    // 通用错误\n    throw new AIServiceError(\n      'AI服务处理失败，请重试',\n      'UNKNOWN_ERROR',\n      500\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;AAGO,MAAM,uBAAuB;;;IAClC,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,aAAqB,GAAG,CAC/B;QACA,KAAK,CAAC,eAHC,OAAA,WACA,aAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,UAAkB,+CAA8C;IAExE,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;IAC3B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QAEvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;oBACZ,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,WAC5B,aACA,SAAS,MAAM;YAEnB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAC9C,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,oBACA,iBACA;QAEJ;IACF;IAEA,SAAS;IACD,qBAAqB,MAAc,EAAU;QACnD,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,UAAU;YACvD,eAAe;YACf,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+DP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;OAWP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BP,CAAC;QACJ,OAAO;YACL,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,eACR,cACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,QAAe;IACf,aAAqB;IAE7B,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;QAC7B,sDAAsD;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YACpB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,uBAAuB;;QAEvE,mBAAmB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,qBAAqB;YACrB,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,6BAA6B;YAC7B,IAAI,CAAC,OAAO,GAAG;QACjB;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC/B;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAmC;QACvC,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM;4BACR;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;oBACd;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,OAAO;gBACT;gBAEA,cAAc;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBAChE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;gBACzD,IAAI,UAAU,YAAY;oBACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBACA,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QACvE,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,GAAG,OAAO,gDAAgD,CAAC;4BACnE;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;wBACZ,aAAa;oBACf;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,IAAI,CAAC,YAAY,EAAE;wBACrB,aAAa;wBACb,OAAO,KAAK,SAAS,CAAC;4BACpB,SAAS,KAAK,OAAO;4BACrB,QAAQ,KAAK,MAAM,IAAI;4BACvB,SAAS;wBACX;oBACF,OAAO;wBACL,WAAW;wBACX,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;oBAC9C;gBACF;gBAEA,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,eAAe,UAAU,KAAK,EAAE,WAAW;gBAEjD,gBAAgB;gBAChB,IAAI,aAAa,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBACnE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,eAAe;gBACf,MAAM,IAAI,eACR,cACA,aACA,SAAS,MAAM;YAEnB,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,gBAAgB;oBACnC,wBAAwB;oBACxB,MAAM;gBACR;gBAEA,aAAa;gBACb,IAAI,UAAU,YAAY;oBACxB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,MAAM,IAAI,eACR,0BACA,iBACA;YAEJ;QACF;QAEA,MAAM,IAAI,eACR,sBACA,wBACA;IAEJ;IAEA,YAAY;IACZ,MAAM,cAAc,MAAc,EAAE,WAAmB,CAAC,EAAmB;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,IAAI,CAAC,KAAK;gBACjB,QAAQ;gBACR,eAAe;oBACb,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,SAAS,CAAC;oBACpB,SAAS,KAAK,OAAO;oBACrB,QAAQ,KAAK,MAAM,IAAI;oBACvB,SAAS;gBACX;YACF;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,UAC5B,0BACA,SAAS,MAAM;QAEnB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,cACA,iBACA;QAEJ;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,eACR,gBACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,SAAS,eAAe,MAAgB;IAC7C,OAAQ,OAAO,QAAQ;QACrB,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B,KAAK;YACH,OAAO,IAAI,aAAa;QAC1B,KAAK;YACH,OAAO,IAAI,wHAAA,CAAA,iBAAc,CAAC;QAC5B;YACE,+BAA+B;YAC/B,OAAO,IAAI,eAAe;IAC9B;AACF;AAGO,eAAe,gBACpB,OAAyB;IAEzB,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,gBAAgB;YACnC,MAAM;QACR;QAEA,OAAO;QACP,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,MAAM,IAAI,eACR,oBACA,oBACA;QAEJ;QAEA,OAAO;QACP,MAAM,IAAI,eACR,gBACA,iBACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/utils/smartModelSelector.ts"], "sourcesContent": ["// 智能模型选择器 - 根据片段类型和内容自动选择最适合的AI模型\n\ninterface ModelConfig {\n  id: string\n  provider: string\n  model: string\n  name: string\n  supportsVideo: boolean\n  supportsImage: boolean\n  supportsImageToVideo: boolean\n  enabled: boolean\n  apiKey: string\n}\n\ninterface SegmentInfo {\n  type: string\n  title: string\n  description?: string\n  prompt?: string\n  hasCharacterImages: boolean\n  characterCount: number\n  sceneComplexity: 'low' | 'medium' | 'high'\n  emotionalIntensity: 'low' | 'medium' | 'high'\n}\n\ninterface ModelSelectionResult {\n  selectedModel: ModelConfig\n  reason: string\n  confidence: number\n  alternatives: ModelConfig[]\n}\n\nexport class SmartModelSelector {\n  private models: ModelConfig[]\n\n  constructor(models: ModelConfig[]) {\n    this.models = models.filter(m => m.enabled && m.apiKey)\n  }\n\n  /**\n   * 智能选择最适合的模型\n   */\n  selectBestModel(segmentInfo: SegmentInfo): ModelSelectionResult {\n    const availableModels = this.getAvailableModels()\n    \n    if (availableModels.length === 0) {\n      throw new Error('没有可用的视频生成模型')\n    }\n\n    // 计算每个模型的适配分数\n    const modelScores = availableModels.map(model => ({\n      model,\n      score: this.calculateModelScore(model, segmentInfo),\n      reason: this.getSelectionReason(model, segmentInfo)\n    }))\n\n    // 按分数排序\n    modelScores.sort((a, b) => b.score - a.score)\n\n    const best = modelScores[0]\n    const alternatives = modelScores.slice(1, 3).map(item => item.model)\n\n    return {\n      selectedModel: best.model,\n      reason: best.reason,\n      confidence: best.score,\n      alternatives\n    }\n  }\n\n  /**\n   * 获取可用的视频生成模型\n   */\n  private getAvailableModels(): ModelConfig[] {\n    return this.models.filter(model => \n      model.supportsVideo || model.supportsImageToVideo\n    )\n  }\n\n  /**\n   * 计算模型适配分数 (0-100)\n   */\n  private calculateModelScore(model: ModelConfig, segmentInfo: SegmentInfo): number {\n    let score = 50 // 基础分数\n\n    // 1. 模型类型适配性\n    if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {\n      score += 30 // 图生视频模型 + 有角色图像 = 高分\n    } else if (model.supportsVideo && !segmentInfo.hasCharacterImages) {\n      score += 20 // 文生视频模型 + 无角色图像 = 中等分\n    } else if (model.supportsVideo) {\n      score += 10 // 文生视频模型作为备选\n    }\n\n    // 2. 片段类型适配性\n    score += this.getSegmentTypeScore(model, segmentInfo.type)\n\n    // 3. 角色数量适配性\n    if (segmentInfo.characterCount > 0 && model.supportsImageToVideo) {\n      score += Math.min(segmentInfo.characterCount * 5, 15)\n    }\n\n    // 4. 场景复杂度适配性\n    score += this.getComplexityScore(model, segmentInfo.sceneComplexity)\n\n    // 5. 情感强度适配性\n    score += this.getEmotionalScore(model, segmentInfo.emotionalIntensity)\n\n    // 6. 特定模型优势\n    score += this.getModelSpecificBonus(model, segmentInfo)\n\n    return Math.min(Math.max(score, 0), 100)\n  }\n\n  /**\n   * 根据片段类型计算分数\n   */\n  private getSegmentTypeScore(model: ModelConfig, segmentType: string): number {\n    const typeScores: Record<string, Record<string, number>> = {\n      'environment': {\n        'text-to-video': 15,    // 环境建立适合文生视频\n        'image-to-video': 5\n      },\n      'character': {\n        'text-to-video': 5,\n        'image-to-video': 20    // 角色引入适合图生视频\n      },\n      'action': {\n        'text-to-video': 10,\n        'image-to-video': 15    // 动作场景图生视频略好\n      },\n      'emotion': {\n        'text-to-video': 5,\n        'image-to-video': 25    // 情感表达强烈推荐图生视频\n      },\n      'dialogue': {\n        'text-to-video': 8,\n        'image-to-video': 18    // 对话场景推荐图生视频\n      },\n      'suspense': {\n        'text-to-video': 12,\n        'image-to-video': 8     // 悬念场景文生视频更有创意\n      }\n    }\n\n    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'\n    return typeScores[segmentType]?.[modelType] || 0\n  }\n\n  /**\n   * 根据场景复杂度计算分数\n   */\n  private getComplexityScore(model: ModelConfig, complexity: string): number {\n    const complexityScores: Record<string, Record<string, number>> = {\n      'low': {\n        'image-to-video': 10,   // 简单场景图生视频更稳定\n        'text-to-video': 5\n      },\n      'medium': {\n        'image-to-video': 8,\n        'text-to-video': 8      // 中等复杂度两者相当\n      },\n      'high': {\n        'image-to-video': 5,\n        'text-to-video': 12     // 复杂场景文生视频更灵活\n      }\n    }\n\n    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'\n    return complexityScores[complexity]?.[modelType] || 0\n  }\n\n  /**\n   * 根据情感强度计算分数\n   */\n  private getEmotionalScore(model: ModelConfig, intensity: string): number {\n    const emotionalScores: Record<string, Record<string, number>> = {\n      'low': {\n        'image-to-video': 5,\n        'text-to-video': 8\n      },\n      'medium': {\n        'image-to-video': 10,\n        'text-to-video': 6\n      },\n      'high': {\n        'image-to-video': 15,   // 高情感强度推荐图生视频\n        'text-to-video': 3\n      }\n    }\n\n    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'\n    return emotionalScores[intensity]?.[modelType] || 0\n  }\n\n  /**\n   * 特定模型的额外加分\n   */\n  private getModelSpecificBonus(model: ModelConfig, segmentInfo: SegmentInfo): number {\n    let bonus = 0\n\n    // 豆包模型特定优势\n    if (model.provider === 'doubao') {\n      if (model.model === 'ep-20250624013749-dbrbn') {\n        // Seedance 1.0 Lite I2V - 图生视频模型在角色一致性方面的优势\n        if (segmentInfo.hasCharacterImages) {\n          bonus += 15\n        }\n      } else if (model.model === 'ep-20250624013223-bwdtj') {\n        // Seedance 1.0 Lite T2V - 文生视频模型在创意性方面的优势\n        if (segmentInfo.sceneComplexity === 'high') {\n          bonus += 12\n        }\n      } else if (model.model === 'ep-20250622184757-q77k7') {\n        // Seedance 1.0 Pro - 原有文生视频模型\n        if (segmentInfo.sceneComplexity === 'high') {\n          bonus += 8\n        }\n      }\n    }\n\n    return bonus\n  }\n\n  /**\n   * 生成选择理由\n   */\n  private getSelectionReason(model: ModelConfig, segmentInfo: SegmentInfo): string {\n    const reasons: string[] = []\n\n    if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {\n      reasons.push('基于角色参考图像生成，确保角色一致性')\n    }\n\n    if (segmentInfo.type === 'emotion' && model.supportsImageToVideo) {\n      reasons.push('情感表达场景，图生视频效果更佳')\n    }\n\n    if (segmentInfo.type === 'environment' && !model.supportsImageToVideo) {\n      reasons.push('环境建立场景，文生视频创意性更强')\n    }\n\n    if (segmentInfo.characterCount > 1 && model.supportsImageToVideo) {\n      reasons.push('多角色场景，图生视频角色识别更准确')\n    }\n\n    if (segmentInfo.sceneComplexity === 'high' && !model.supportsImageToVideo) {\n      reasons.push('复杂场景，文生视频适应性更好')\n    }\n\n    if (model.provider === 'doubao') {\n      reasons.push('豆包模型在中文内容理解方面表现优秀')\n    }\n\n    return reasons.length > 0 ? reasons.join('；') : '综合评估最适合的模型'\n  }\n\n  /**\n   * 分析片段信息\n   */\n  static analyzeSegment(\n    segment: any, \n    characterImages: string[] = []\n  ): SegmentInfo {\n    const title = segment.title || ''\n    const description = segment.description || ''\n    const prompt = segment.prompt || ''\n    const text = `${title} ${description} ${prompt}`.toLowerCase()\n\n    // 分析片段类型\n    const type = this.detectSegmentType(text)\n\n    // 分析角色数量\n    const characterCount = this.countCharacters(text)\n\n    // 分析场景复杂度\n    const sceneComplexity = this.analyzeSceneComplexity(text)\n\n    // 分析情感强度\n    const emotionalIntensity = this.analyzeEmotionalIntensity(text)\n\n    return {\n      type,\n      title,\n      description,\n      prompt,\n      hasCharacterImages: characterImages.length > 0,\n      characterCount,\n      sceneComplexity,\n      emotionalIntensity\n    }\n  }\n\n  /**\n   * 检测片段类型\n   */\n  private static detectSegmentType(text: string): string {\n    const typeKeywords = {\n      environment: ['环境', '场景', '建立', '雪夜', '城楼', '关城', '山峰'],\n      character: ['登场', '出现', '角色', '人物', '李四', '张三'],\n      action: ['动作', '跑', '扛', '冲', '抽刀', '战斗', '移动'],\n      emotion: ['表情', '情感', '内心', '思考', '回忆', '愤怒', '悲伤'],\n      dialogue: ['对话', '说', '讲述', '报告', '讨论', '交谈'],\n      suspense: ['悬念', '远方', '狼烟', '威胁', '危机', '神秘']\n    }\n\n    for (const [type, keywords] of Object.entries(typeKeywords)) {\n      if (keywords.some(keyword => text.includes(keyword))) {\n        return type\n      }\n    }\n\n    return 'action' // 默认类型\n  }\n\n  /**\n   * 统计角色数量\n   */\n  private static countCharacters(text: string): number {\n    const characterNames = ['张三', '李四', '王五', '赵六']\n    return characterNames.filter(name => text.includes(name.toLowerCase())).length\n  }\n\n  /**\n   * 分析场景复杂度\n   */\n  private static analyzeSceneComplexity(text: string): 'low' | 'medium' | 'high' {\n    const complexityIndicators = {\n      high: ['战斗', '追逐', '多人', '复杂', '混乱', '激烈'],\n      medium: ['对话', '互动', '移动', '检查', '讨论'],\n      low: ['站立', '观察', '思考', '静止', '单人']\n    }\n\n    for (const [level, indicators] of Object.entries(complexityIndicators)) {\n      if (indicators.some(indicator => text.includes(indicator))) {\n        return level as 'low' | 'medium' | 'high'\n      }\n    }\n\n    return 'medium'\n  }\n\n  /**\n   * 分析情感强度\n   */\n  private static analyzeEmotionalIntensity(text: string): 'low' | 'medium' | 'high' {\n    const intensityIndicators = {\n      high: ['愤怒', '恐惧', '激动', '震惊', '绝望', '狂怒'],\n      medium: ['担心', '疑惑', '严肃', '警惕', '紧张'],\n      low: ['平静', '思考', '观察', '正常', '淡定']\n    }\n\n    for (const [level, indicators] of Object.entries(intensityIndicators)) {\n      if (indicators.some(indicator => text.includes(indicator))) {\n        return level as 'low' | 'medium' | 'high'\n      }\n    }\n\n    return 'medium'\n  }\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;AAgC3B,MAAM;IACH,OAAqB;IAE7B,YAAY,MAAqB,CAAE;QACjC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,MAAM;IACxD;IAEA;;GAEC,GACD,gBAAgB,WAAwB,EAAwB;QAC9D,MAAM,kBAAkB,IAAI,CAAC,kBAAkB;QAE/C,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,cAAc;QACd,MAAM,cAAc,gBAAgB,GAAG,CAAC,CAAA,QAAS,CAAC;gBAChD;gBACA,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO;gBACvC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO;YACzC,CAAC;QAED,QAAQ;QACR,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;QAE5C,MAAM,OAAO,WAAW,CAAC,EAAE;QAC3B,MAAM,eAAe,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QAEnE,OAAO;YACL,eAAe,KAAK,KAAK;YACzB,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,KAAK;YACtB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,qBAAoC;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QACxB,MAAM,aAAa,IAAI,MAAM,oBAAoB;IAErD;IAEA;;GAEC,GACD,AAAQ,oBAAoB,KAAkB,EAAE,WAAwB,EAAU;QAChF,IAAI,QAAQ,GAAG,OAAO;;QAEtB,aAAa;QACb,IAAI,MAAM,oBAAoB,IAAI,YAAY,kBAAkB,EAAE;YAChE,SAAS,GAAG,sBAAsB;;QACpC,OAAO,IAAI,MAAM,aAAa,IAAI,CAAC,YAAY,kBAAkB,EAAE;YACjE,SAAS,GAAG,uBAAuB;;QACrC,OAAO,IAAI,MAAM,aAAa,EAAE;YAC9B,SAAS,GAAG,aAAa;;QAC3B;QAEA,aAAa;QACb,SAAS,IAAI,CAAC,mBAAmB,CAAC,OAAO,YAAY,IAAI;QAEzD,aAAa;QACb,IAAI,YAAY,cAAc,GAAG,KAAK,MAAM,oBAAoB,EAAE;YAChE,SAAS,KAAK,GAAG,CAAC,YAAY,cAAc,GAAG,GAAG;QACpD;QAEA,cAAc;QACd,SAAS,IAAI,CAAC,kBAAkB,CAAC,OAAO,YAAY,eAAe;QAEnE,aAAa;QACb,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,YAAY,kBAAkB;QAErE,YAAY;QACZ,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;QAE3C,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI;IACtC;IAEA;;GAEC,GACD,AAAQ,oBAAoB,KAAkB,EAAE,WAAmB,EAAU;QAC3E,MAAM,aAAqD;YACzD,eAAe;gBACb,iBAAiB;gBACjB,kBAAkB;YACpB;YACA,aAAa;gBACX,iBAAiB;gBACjB,kBAAkB,GAAM,aAAa;YACvC;YACA,UAAU;gBACR,iBAAiB;gBACjB,kBAAkB,GAAM,aAAa;YACvC;YACA,WAAW;gBACT,iBAAiB;gBACjB,kBAAkB,GAAM,eAAe;YACzC;YACA,YAAY;gBACV,iBAAiB;gBACjB,kBAAkB,GAAM,aAAa;YACvC;YACA,YAAY;gBACV,iBAAiB;gBACjB,kBAAkB,EAAM,eAAe;YACzC;QACF;QAEA,MAAM,YAAY,MAAM,oBAAoB,GAAG,mBAAmB;QAClE,OAAO,UAAU,CAAC,YAAY,EAAE,CAAC,UAAU,IAAI;IACjD;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAkB,EAAE,UAAkB,EAAU;QACzE,MAAM,mBAA2D;YAC/D,OAAO;gBACL,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,UAAU;gBACR,kBAAkB;gBAClB,iBAAiB,EAAO,YAAY;YACtC;YACA,QAAQ;gBACN,kBAAkB;gBAClB,iBAAiB,GAAO,cAAc;YACxC;QACF;QAEA,MAAM,YAAY,MAAM,oBAAoB,GAAG,mBAAmB;QAClE,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC,UAAU,IAAI;IACtD;IAEA;;GAEC,GACD,AAAQ,kBAAkB,KAAkB,EAAE,SAAiB,EAAU;QACvE,MAAM,kBAA0D;YAC9D,OAAO;gBACL,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,UAAU;gBACR,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,QAAQ;gBACN,kBAAkB;gBAClB,iBAAiB;YACnB;QACF;QAEA,MAAM,YAAY,MAAM,oBAAoB,GAAG,mBAAmB;QAClE,OAAO,eAAe,CAAC,UAAU,EAAE,CAAC,UAAU,IAAI;IACpD;IAEA;;GAEC,GACD,AAAQ,sBAAsB,KAAkB,EAAE,WAAwB,EAAU;QAClF,IAAI,QAAQ;QAEZ,WAAW;QACX,IAAI,MAAM,QAAQ,KAAK,UAAU;YAC/B,IAAI,MAAM,KAAK,KAAK,2BAA2B;gBAC7C,4CAA4C;gBAC5C,IAAI,YAAY,kBAAkB,EAAE;oBAClC,SAAS;gBACX;YACF,OAAO,IAAI,MAAM,KAAK,KAAK,2BAA2B;gBACpD,0CAA0C;gBAC1C,IAAI,YAAY,eAAe,KAAK,QAAQ;oBAC1C,SAAS;gBACX;YACF,OAAO,IAAI,MAAM,KAAK,KAAK,2BAA2B;gBACpD,8BAA8B;gBAC9B,IAAI,YAAY,eAAe,KAAK,QAAQ;oBAC1C,SAAS;gBACX;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAkB,EAAE,WAAwB,EAAU;QAC/E,MAAM,UAAoB,EAAE;QAE5B,IAAI,MAAM,oBAAoB,IAAI,YAAY,kBAAkB,EAAE;YAChE,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,YAAY,IAAI,KAAK,aAAa,MAAM,oBAAoB,EAAE;YAChE,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,YAAY,IAAI,KAAK,iBAAiB,CAAC,MAAM,oBAAoB,EAAE;YACrE,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,YAAY,cAAc,GAAG,KAAK,MAAM,oBAAoB,EAAE;YAChE,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,YAAY,eAAe,KAAK,UAAU,CAAC,MAAM,oBAAoB,EAAE;YACzE,QAAQ,IAAI,CAAC;QACf;QAEA,IAAI,MAAM,QAAQ,KAAK,UAAU;YAC/B,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO,QAAQ,MAAM,GAAG,IAAI,QAAQ,IAAI,CAAC,OAAO;IAClD;IAEA;;GAEC,GACD,OAAO,eACL,OAAY,EACZ,kBAA4B,EAAE,EACjB;QACb,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,cAAc,QAAQ,WAAW,IAAI;QAC3C,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,OAAO,GAAG,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE,QAAQ,CAAC,WAAW;QAE5D,SAAS;QACT,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAEpC,SAAS;QACT,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC;QAE5C,UAAU;QACV,MAAM,kBAAkB,IAAI,CAAC,sBAAsB,CAAC;QAEpD,SAAS;QACT,MAAM,qBAAqB,IAAI,CAAC,yBAAyB,CAAC;QAE1D,OAAO;YACL;YACA;YACA;YACA;YACA,oBAAoB,gBAAgB,MAAM,GAAG;YAC7C;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,OAAe,kBAAkB,IAAY,EAAU;QACrD,MAAM,eAAe;YACnB,aAAa;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YACvD,WAAW;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC/C,QAAQ;gBAAC;gBAAM;gBAAK;gBAAK;gBAAK;gBAAM;gBAAM;aAAK;YAC/C,SAAS;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YACnD,UAAU;gBAAC;gBAAM;gBAAK;gBAAM;gBAAM;gBAAM;aAAK;YAC7C,UAAU;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QAChD;QAEA,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,cAAe;YAC3D,IAAI,SAAS,IAAI,CAAC,CAAA,UAAW,KAAK,QAAQ,CAAC,WAAW;gBACpD,OAAO;YACT;QACF;QAEA,OAAO,SAAS,OAAO;;IACzB;IAEA;;GAEC,GACD,OAAe,gBAAgB,IAAY,EAAU;QACnD,MAAM,iBAAiB;YAAC;YAAM;YAAM;YAAM;SAAK;QAC/C,OAAO,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,KAAK,WAAW,KAAK,MAAM;IAChF;IAEA;;GAEC,GACD,OAAe,uBAAuB,IAAY,EAA6B;QAC7E,MAAM,uBAAuB;YAC3B,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC1C,QAAQ;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YACtC,KAAK;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QACrC;QAEA,KAAK,MAAM,CAAC,OAAO,WAAW,IAAI,OAAO,OAAO,CAAC,sBAAuB;YACtE,IAAI,WAAW,IAAI,CAAC,CAAA,YAAa,KAAK,QAAQ,CAAC,aAAa;gBAC1D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAe,0BAA0B,IAAY,EAA6B;QAChF,MAAM,sBAAsB;YAC1B,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC1C,QAAQ;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YACtC,KAAK;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QACrC;QAEA,KAAK,MAAM,CAAC,OAAO,WAAW,IAAI,OAAO,OAAO,CAAC,qBAAsB;YACrE,IAAI,WAAW,IAAI,CAAC,CAAA,YAAa,KAAK,QAAQ,CAAC,aAAa;gBAC1D,OAAO;YACT;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-story-video/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { DeepSeekClient, AIServiceError, handleAIRequest } from '@/lib/ai'\nimport { SmartModelSelector } from '@/utils/smartModelSelector'\nimport {\n  DetailedCharacterDNA,\n  EnhancedCharacter,\n  ConsistencySettings,\n  ReferenceImageSet,\n  ConsistencyResult\n} from '@/types/character-consistency'\nimport fs from 'fs'\nimport path from 'path'\n\n// 内容安全检查\nfunction validatePromptContent(prompt: string): { isValid: boolean; reason?: string } {\n  // 检查敏感词汇\n  const sensitiveWords = ['暴力', '血腥', '政治', '色情', '赌博', '毒品']\n  const foundSensitive = sensitiveWords.find(word => prompt.includes(word))\n  if (foundSensitive) {\n    return { isValid: false, reason: `包含敏感词汇: ${foundSensitive}` }\n  }\n\n  // 检查长度\n  if (prompt.length > 800) {\n    return { isValid: false, reason: '提示词过长，超过800字符限制' }\n  }\n\n  // 检查是否为空\n  if (!prompt.trim()) {\n    return { isValid: false, reason: '提示词不能为空' }\n  }\n\n  return { isValid: true }\n}\n\n// 优化提示词\nfunction optimizePrompt(prompt: string): string {\n  return prompt\n    .replace(/[^\\u4e00-\\u9fa5a-zA-Z0-9\\s，。！？、：；\"\"''（）【】]/g, '') // 保留中文、英文、数字和基本标点\n    .replace(/\\s+/g, ' ') // 合并多个空格\n    .substring(0, 500) // 限制长度为500字符\n    .trim()\n}\n\n// 构建结构化角色DNA描述\nfunction buildStructuredCharacterDNA(character: any): string {\n  const name = character.name || '未知角色'\n\n  // 解析或生成详细DNA\n  const dna = parseCharacterDNA(character)\n  if (!dna) {\n    return `【角色 - ${name}】基础信息不足，无法生成详细DNA`\n  }\n\n  // 构建结构化DNA描述\n  return `【角色DNA - ${name}】\n📋 基础信息：\n- 姓名：${name}\n- 身份：${character.identity || '未知'}\n- 性格：${character.personality || '未知'}\n\n🎭 面部特征：\n- 脸型：${dna.facial.faceShape}\n- 眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}\n- 鼻型：${dna.facial.noseShape}\n- 嘴型：${dna.facial.mouthShape}\n- 肤色：${dna.facial.skinTone}\n- 面部特征：${dna.facial.facialFeatures}\n\n👤 体型特征：\n- 身高：${dna.physique.height}\n- 体型：${dna.physique.build}\n- 体态：${dna.physique.posture}\n\n👔 服装特征：\n- 风格：${dna.clothing.style}\n- 颜色：${dna.clothing.colors.join('、')}\n- 配饰：${dna.clothing.accessories.join('、') || '无'}\n\n🔍 独特标识：\n${dna.uniqueIdentifiers.map(id => `- ${id}`).join('\\n')}\n\n📝 标准提示词：\n${dna.standardPrompt}\n\n【生成约束】\n- 严格按照上述DNA特征生成\n- 确保面部特征高度一致\n- 保持角色独特标识清晰可见\n- 适应场景但不改变基本外貌`\n}\n\n// 提取特征的辅助函数\nfunction extractFeature(text: string, pattern: string, defaultValue: string): string {\n  if (!text) return defaultValue\n\n  const regex = new RegExp(`(${pattern})[^，。]*`, 'i')\n  const match = text.match(regex)\n\n  if (match) {\n    return match[0].replace(/^(脸型|眼睛?|鼻子?|嘴唇?|肤色|身材|体型|身高|体态|姿态|发型|头发|发色|颜色|风格|款式|材质|面料|气质|特质)[:：]?/, '').trim()\n  }\n\n  return defaultValue\n}\n\n// 提取独特特征\nfunction extractUniqueFeatures(facial: string, physique: string): string {\n  const text = `${facial} ${physique}`.toLowerCase()\n  const uniqueFeatures = []\n\n  // 检查常见独特标识\n  if (text.includes('疤') || text.includes('伤')) uniqueFeatures.push('疤痕')\n  if (text.includes('痣') || text.includes('黑点')) uniqueFeatures.push('痣')\n  if (text.includes('酒窝')) uniqueFeatures.push('酒窝')\n  if (text.includes('雀斑')) uniqueFeatures.push('雀斑')\n  if (text.includes('胡子') || text.includes('胡须')) uniqueFeatures.push('胡须')\n  if (text.includes('眼镜')) uniqueFeatures.push('眼镜')\n\n  return uniqueFeatures.length > 0 ? uniqueFeatures.join('、') : '无明显特殊标记'\n}\n\n// 构建结构化提示词\nfunction buildStructuredPrompt(\n  plotContent: string,\n  characterDNA: string,\n  sceneDescription: string,\n  cameraAngle: string,\n  duration?: number\n): string {\n  const durationText = duration ? `${duration}秒` : '15-30秒'\n\n  return `【双重约束视频生成提示词】\n\n📖 剧情内容：\n${plotContent}\n\n${characterDNA}\n\n🎬 场景要求：\n- 场景描述：${sceneDescription}\n- 镜头角度：${cameraAngle}\n- 视频时长：${durationText}\n- 画面质量：高清晰度，电影级质量\n- 光影效果：专业级光影，细节丰富\n\n⏱️ 时长要求：\n- 生成${durationText}的视频内容\n- 确保动作和剧情完整展现\n- 避免过快或过慢的节奏\n- 保持自然的时间流逝感\n\n🔒 生成约束：\n1. 【最高优先级】严格按照角色DNA特征生成，确保面部特征高度一致\n2. 【视觉约束】参考提供的角色图像，保持外貌一致性\n3. 【文本约束】遵循详细的角色DNA描述，不允许偏差\n4. 【时长约束】生成${durationText}的完整视频内容\n5. 【场景适应】在保持角色一致性前提下适应场景氛围\n6. 【独特标识】确保角色独特标识清晰可见且位置准确\n\n⚠️ 严格禁止：\n- 改变角色基本面部特征\n- 忽略独特标识\n- 模糊或歧义的特征表现\n- 与DNA描述不符的外貌\n- 生成过短或过长的视频内容`\n}\n\n// 解析角色DNA数据\nfunction parseCharacterDNA(character: any): DetailedCharacterDNA | null {\n  try {\n    if (character.detailedDNA) {\n      return JSON.parse(character.detailedDNA)\n    }\n\n    // 如果没有详细DNA，从基础字段构建\n    return generateBasicDNA(character)\n  } catch (error) {\n    console.warn(`解析角色 ${character.name} 的DNA失败:`, error)\n    return generateBasicDNA(character)\n  }\n}\n\n// 从基础字段生成DNA\nfunction generateBasicDNA(character: any): DetailedCharacterDNA {\n  return {\n    facial: {\n      faceShape: extractFeature(character.facial, '脸型', '方正轮廓'),\n      eyeShape: extractFeature(character.facial, '眼', '中等大小'),\n      eyeColor: extractFeature(character.facial, '眼色|眼珠', '深褐色'),\n      noseShape: extractFeature(character.facial, '鼻', '高挺'),\n      mouthShape: extractFeature(character.facial, '嘴|唇', '适中'),\n      skinTone: extractFeature(character.facial, '肤色|皮肤', '健康'),\n      facialFeatures: extractUniqueFeatures(character.facial, character.physique)\n    },\n    physique: {\n      height: extractFeature(character.physique, '身高', '中等偏高'),\n      build: extractFeature(character.physique, '身材|体型', '魁梧'),\n      posture: extractFeature(character.physique, '体态|姿态', '挺拔')\n    },\n    clothing: {\n      style: extractFeature(character.clothing, '风格|款式', '实用'),\n      colors: extractColors(character.clothing),\n      accessories: extractAccessories(character.clothing)\n    },\n    uniqueIdentifiers: extractUniqueIdentifiers(character),\n    standardPrompt: generateStandardPrompt(character)\n  }\n}\n\n// 提取颜色信息\nfunction extractColors(clothing: string): string[] {\n  if (!clothing) return ['深色系']\n\n  const colors = []\n  const colorMap = {\n    '黑': '黑色', '白': '白色', '红': '红色', '蓝': '蓝色',\n    '绿': '绿色', '黄': '黄色', '灰': '灰色', '棕': '棕色'\n  }\n\n  for (const [key, value] of Object.entries(colorMap)) {\n    if (clothing.includes(key)) {\n      colors.push(value)\n    }\n  }\n\n  return colors.length > 0 ? colors : ['深色系']\n}\n\n// 提取配饰信息\nfunction extractAccessories(clothing: string): string[] {\n  if (!clothing) return []\n\n  const accessories = []\n  const accessoryMap = {\n    '帽': '帽子', '眼镜': '眼镜', '手表': '手表', '项链': '项链',\n    '戒指': '戒指', '耳环': '耳环', '腰带': '腰带', '徽章': '徽章'\n  }\n\n  for (const [key, value] of Object.entries(accessoryMap)) {\n    if (clothing.includes(key)) {\n      accessories.push(value)\n    }\n  }\n\n  return accessories\n}\n\n// 提取独特标识符\nfunction extractUniqueIdentifiers(character: any): string[] {\n  const identifiers = []\n  const text = `${character.facial || ''} ${character.physique || ''} ${character.personality || ''}`.toLowerCase()\n\n  // 检查物理特征\n  if (text.includes('疤') || text.includes('伤痕')) identifiers.push('疤痕')\n  if (text.includes('痣') || text.includes('黑痣')) identifiers.push('痣')\n  if (text.includes('酒窝')) identifiers.push('酒窝')\n  if (text.includes('雀斑')) identifiers.push('雀斑')\n  if (text.includes('胡子') || text.includes('胡须')) identifiers.push('胡须')\n  if (text.includes('眼镜')) identifiers.push('眼镜')\n\n  // 检查性格特征转化为外貌特征\n  if (text.includes('严肃') || text.includes('冷峻')) identifiers.push('严肃表情')\n  if (text.includes('温和') || text.includes('和善')) identifiers.push('温和气质')\n  if (text.includes('威严') || text.includes('威武')) identifiers.push('威严气场')\n\n  return identifiers.length > 0 ? identifiers : ['无明显特殊标记']\n}\n\n// 生成标准提示词\nfunction generateStandardPrompt(character: any): string {\n  const name = character.name || '角色'\n  const facial = character.facial || ''\n  const physique = character.physique || ''\n  const clothing = character.clothing || ''\n\n  return `${name}，${facial}，${physique}，${clothing}，高质量人物肖像，细节丰富，专业摄影`\n}\n\n// 下载视频到本地存储\nasync function downloadVideoToLocal(videoUrl: string, segmentId: string, segmentTitle: string): Promise<string | null> {\n  try {\n    console.log(`📥 开始下载视频: ${videoUrl}`)\n\n    // 创建下载目录\n    const downloadDir = path.join(process.cwd(), 'public', 'downloads', 'videos')\n    if (!fs.existsSync(downloadDir)) {\n      fs.mkdirSync(downloadDir, { recursive: true })\n    }\n\n    // 生成文件名\n    const fileName = `${segmentId}_${segmentTitle.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_')}.mp4`\n    const filePath = path.join(downloadDir, fileName)\n    const publicPath = `/downloads/videos/${fileName}`\n\n    // 下载视频\n    const response = await fetch(videoUrl)\n    if (!response.ok) {\n      console.error(`下载视频失败: ${response.status} ${response.statusText}`)\n      return null\n    }\n\n    // 保存到本地\n    const buffer = await response.arrayBuffer()\n    fs.writeFileSync(filePath, Buffer.from(buffer))\n\n    console.log(`✅ 视频下载成功: ${publicPath}`)\n    return publicPath\n\n  } catch (error) {\n    console.error('下载视频到本地失败:', error)\n    return null\n  }\n}\n\n// POST - 生成剧情视频\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { episodeId, prompt, projectId, retrySegmentId, modelId, generationMode = 'manual' } = body\n\n    if (!episodeId || !prompt || !projectId) {\n      return NextResponse.json(\n        { success: false, error: '缺少必要参数' },\n        { status: 400 }\n      )\n    }\n\n    // 🔒 先决条件检查：主要角色必须有参考图像\n    console.log('🔍 检查角色参考图像先决条件...')\n    const characterCheck = await checkCharacterImagePrerequisites(projectId)\n\n    if (!characterCheck.success) {\n      console.log('❌ 角色图像先决条件检查失败:', characterCheck.error)\n      return NextResponse.json(\n        {\n          success: false,\n          error: characterCheck.error,\n          missingCharacters: characterCheck.missingCharacters,\n          requirementType: 'character_images'\n        },\n        { status: 400 }\n      )\n    }\n\n    console.log('✅ 角色图像先决条件检查通过')\n\n    // 如果是重试特定片段，重定向到专门的重试API\n    if (retrySegmentId) {\n      console.log(`🔄 重试片段请求重定向到专门的重试API: ${retrySegmentId}`)\n\n      return NextResponse.json({\n        success: false,\n        error: '请使用专门的重试API: /api/ai/retry-failed-segment'\n      })\n    }\n\n    // 检查是否已有该剧集的视频生成记录，并在事务中清理\n    console.log(`🔍 检查剧集 ${episodeId} 是否已有视频生成记录...`)\n\n    let isRegeneration = false\n\n    // 使用事务确保删除操作的原子性\n    await prisma.$transaction(async (tx) => {\n      const existingStoryVideos = await tx.storyVideo.findMany({\n        where: { episodeId },\n        include: {\n          segments: true\n        }\n      })\n\n      if (existingStoryVideos.length > 0) {\n        isRegeneration = true\n        console.log(`🔄 检测到已有 ${existingStoryVideos.length} 个视频生成记录，准备清理并重新生成...`)\n\n        let totalDeletedSegments = 0\n\n        for (const existingVideo of existingStoryVideos) {\n          console.log(`   - 现有视频ID: ${existingVideo.id}`)\n          console.log(`   - 现有片段数量: ${existingVideo.segments.length}`)\n\n          // 删除所有相关的视频片段\n          const deletedSegments = await tx.videoSegment.deleteMany({\n            where: { storyVideoId: existingVideo.id }\n          })\n          totalDeletedSegments += deletedSegments.count\n          console.log(`   - 删除了 ${deletedSegments.count} 个视频片段`)\n\n          // 删除主视频记录\n          await tx.storyVideo.delete({\n            where: { id: existingVideo.id }\n          })\n          console.log(`   - 删除了主视频记录`)\n        }\n\n        console.log(`✅ 已清理 ${existingStoryVideos.length} 个视频记录和 ${totalDeletedSegments} 个片段，开始重新生成`)\n      } else {\n        console.log(`✨ 没有发现现有视频记录，开始首次生成`)\n      }\n    })\n\n    // 获取AI配置\n    const aiConfig = await prisma.aIConfig.findFirst({\n      where: { enabled: true }\n    })\n\n    if (!aiConfig) {\n      return NextResponse.json(\n        { success: false, error: '请先配置AI模型' },\n        { status: 400 }\n      )\n    }\n\n    // 检查是否有详细剧情信息\n    const plotInfo = await prisma.plotInfo.findUnique({\n      where: { episodeId }\n    })\n\n    if (!plotInfo || !plotInfo.generatedPrompt) {\n      return NextResponse.json(\n        { success: false, error: '请先提取具体剧情信息' },\n        { status: 400 }\n      )\n    }\n\n    // 使用已提取的详细剧情信息生成视频\n    const videoResult = await generateSegmentedStoryVideo(aiConfig, episodeId, projectId, plotInfo, isRegeneration, modelId, generationMode)\n    const message = isRegeneration\n      ? `剧情视频重新生成已启动，共${videoResult.totalSegments}个片段`\n      : `剧情视频分段生成已启动，共${videoResult.totalSegments}个片段`\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        videoId: videoResult.storyVideoId,\n        segments: videoResult.segments,\n        totalSegments: videoResult.totalSegments,\n        status: 'generating',\n        isRegeneration\n      },\n      message\n    })\n  } catch (error) {\n    console.error('生成剧情视频失败:', error)\n    return NextResponse.json(\n      { success: false, error: '生成失败，请重试' },\n      { status: 500 }\n    )\n  }\n}\n\n// 生成分段剧情视频\nasync function generateSegmentedStoryVideo(aiConfig: any, episodeId: string, projectId: string, plotInfo: any, isRegeneration: boolean = false, modelId?: string, generationMode: 'auto' | 'manual' = 'manual') {\n  try {\n    const deepSeekClient = new DeepSeekClient(aiConfig)\n\n    // 1. 使用已有的详细剧情信息，分解为视频片段\n    const segments = await createVideoSegmentsFromPlotInfo(deepSeekClient, plotInfo, episodeId)\n\n    // 2. 创建主视频记录\n    const storyVideo = await prisma.storyVideo.create({\n      data: {\n        episodeId,\n        projectId,\n        prompt: plotInfo.generatedPrompt || '',\n        status: 'generating',\n        metadata: JSON.stringify({\n          totalSegments: segments.length,\n          generatedAt: new Date().toISOString(),\n          basedOnDetailedPlot: true,\n          isRegeneration,\n          regeneratedAt: isRegeneration ? new Date().toISOString() : undefined\n        })\n      }\n    })\n\n    // 3. 创建视频片段记录\n    const videoSegments = await Promise.all(\n      segments.map((segment, index) =>\n        prisma.videoSegment.create({\n          data: {\n            storyVideoId: storyVideo.id,\n            episodeId,\n            projectId,\n            segmentIndex: index + 1,\n            title: segment.title,\n            description: segment.description,\n            prompt: segment.prompt,\n            segmentType: segment.type,\n            duration: segment.duration || 15, // 保存时长到数据库\n            status: 'pending',\n            metadata: JSON.stringify(segment.metadata || {})\n          }\n        })\n      )\n    )\n\n    // 4. 根据生成模式决定是否自动生成片段\n    if (generationMode === 'auto') {\n      // 自动模式：立即开始生成所有片段\n      generateSegmentsAsync(aiConfig, storyVideo.id, videoSegments, modelId)\n    }\n    // 手动模式：不自动生成，等待用户手动触发\n\n    return {\n      storyVideoId: storyVideo.id,\n      segments: videoSegments.map(seg => ({\n        id: seg.id,\n        title: seg.title,\n        description: seg.description,\n        segmentIndex: seg.segmentIndex,\n        status: seg.status,\n        segmentType: seg.segmentType\n      })),\n      totalSegments: segments.length\n    }\n  } catch (error) {\n    console.error('分段视频生成失败:', error)\n    throw new AIServiceError('分段视频生成失败', 'SEGMENTED_VIDEO_GENERATION_FAILED')\n  }\n}\n\n// 基于详细剧情信息创建视频片段\nasync function createVideoSegmentsFromPlotInfo(deepSeekClient: DeepSeekClient, plotInfo: any, episodeId?: string) {\n  try {\n    // 解析已有的详细剧情信息\n    const detailedCharacters = plotInfo.detailedCharacters ? JSON.parse(plotInfo.detailedCharacters) : []\n    const detailedScenes = plotInfo.detailedScenes ? JSON.parse(plotInfo.detailedScenes) : []\n    const plotSequences = plotInfo.plotSequences ? JSON.parse(plotInfo.plotSequences) : []\n    const emotionalArc = plotInfo.emotionalArc || ''\n    const generatedPrompt = plotInfo.generatedPrompt || ''\n\n    // 如果有剧情序列，智能拆分为多个短片段\n    if (plotSequences && plotSequences.length > 0) {\n      const allSegments = []\n\n      for (const sequence of plotSequences) {\n        const originalDuration = parseDuration(sequence.duration) || 20\n        const segmentDuration = 5 // 固定5秒片段（技术限制）\n        const segmentCount = Math.ceil(originalDuration / segmentDuration)\n\n        // 智能拆分为5秒片段\n        const subSegments = splitSequenceIntoFiveSecondSegments(sequence, segmentCount, detailedCharacters, detailedScenes)\n        allSegments.push(...subSegments)\n      }\n\n      console.log(`📊 拆分结果: ${plotSequences.length}个原始序列 → ${allSegments.length}个5秒片段`)\n\n      return allSegments\n    }\n\n    // 如果没有剧情序列，基于生成的提示词进行智能分段\n    const segmentPrompt = `基于以下已分析的详细剧情信息，将其分解为适合视频生成的短片段：\n\n角色信息：\n${JSON.stringify(detailedCharacters, null, 2)}\n\n场景信息：\n${JSON.stringify(detailedScenes, null, 2)}\n\n情感弧线：\n${emotionalArc}\n\n生成的剧情提示词：\n${generatedPrompt}\n\n请按照以下JSON格式返回分段结果：\n{\n  \"segments\": [\n    {\n      \"title\": \"片段标题\",\n      \"description\": \"片段描述\",\n      \"prompt\": \"适合AI视频生成的详细提示词\",\n      \"type\": \"scene|action|dialogue|transition\",\n      \"duration\": 预估时长秒数,\n      \"metadata\": {\n        \"characters\": [\"出现的角色\"],\n        \"location\": \"场景地点\",\n        \"mood\": \"情感氛围\",\n        \"cameraAngle\": \"镜头角度\"\n      }\n    }\n  ]\n}\n\n要求：\n1. 每个片段时长严格控制在5秒（技术限制）\n2. 充分利用已分析的角色和场景信息\n3. 确保片段之间有逻辑连贯性\n4. 提示词要具体且可视化\n5. 分段数量控制在12-16个（确保内容完整覆盖）\n6. 每个片段表达一个核心情节点\n7. 返回标准JSON格式`\n\n    const response = await deepSeekClient.callAPI(segmentPrompt, 4000)\n\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n    const result = JSON.parse(jsonMatch[0])\n    return result.segments || []\n\n  } catch (error) {\n    console.error('基于详细剧情信息创建视频片段失败:', error)\n    // 返回基于基础信息的默认分段\n    return await createFallbackSegments(plotInfo, episodeId)\n  }\n}\n\n// 智能拆分序列为5秒片段\nfunction splitSequenceIntoFiveSecondSegments(sequence: any, segmentCount: number, detailedCharacters: any[], detailedScenes: any[]) {\n  const segments = []\n  const baseAction = sequence.action || ''\n  const baseEmotion = sequence.emotion || 'neutral'\n  const keyMoments = sequence.keyMoments || []\n\n  // 根据动作类型进行智能拆分\n  if (baseAction.includes('遇险') || baseAction.includes('坠落') || baseAction.includes('失足')) {\n    // 遇险类动作拆分\n    const phases = ['接近危险', '察觉危险', '发生意外', '应对危机']\n    for (let i = 0; i < segmentCount; i++) {\n      const phase = phases[i] || phases[phases.length - 1]\n      segments.push({\n        title: `${baseAction} - ${phase}`,\n        description: `${baseAction}的${phase}阶段`,\n        prompt: generateVideoPrompt({\n          ...sequence,\n          action: `${baseAction}的${phase}部分：${getPhaseDescription(baseAction, phase, i, segmentCount)}`,\n          visualElements: sequence.visualElements\n        }, detailedCharacters, detailedScenes),\n        type: determineSegmentType(sequence),\n        duration: 5,\n        metadata: {\n          characters: extractCharactersFromSequence(sequence, detailedCharacters),\n          location: extractLocationFromSequence(sequence, detailedScenes),\n          mood: baseEmotion,\n          cameraAngle: sequence.visualElements || 'medium shot',\n          sequenceId: sequence.sequenceId,\n          segmentIndex: i + 1,\n          totalSegments: segmentCount,\n          phase: phase\n        }\n      })\n    }\n  } else if (baseAction.includes('救援') || baseAction.includes('帮助') || baseAction.includes('扛')) {\n    // 救援类动作拆分\n    const phases = ['发现情况', '准备救援', '执行救援', '完成救援']\n    for (let i = 0; i < segmentCount; i++) {\n      const phase = phases[i] || phases[phases.length - 1]\n      segments.push({\n        title: `${baseAction} - ${phase}`,\n        description: `${baseAction}的${phase}阶段`,\n        prompt: generateVideoPrompt({\n          ...sequence,\n          action: `${baseAction}的${phase}部分：${getPhaseDescription(baseAction, phase, i, segmentCount)}`,\n          visualElements: sequence.visualElements\n        }, detailedCharacters, detailedScenes),\n        type: determineSegmentType(sequence),\n        duration: 5,\n        metadata: {\n          characters: extractCharactersFromSequence(sequence, detailedCharacters),\n          location: extractLocationFromSequence(sequence, detailedScenes),\n          mood: baseEmotion,\n          cameraAngle: sequence.visualElements || 'medium shot',\n          sequenceId: sequence.sequenceId,\n          segmentIndex: i + 1,\n          totalSegments: segmentCount,\n          phase: phase\n        }\n      })\n    }\n  } else {\n    // 通用拆分：基于时间进度\n    for (let i = 0; i < segmentCount; i++) {\n      const progress = (i + 1) / segmentCount\n      const phaseDesc = progress <= 0.25 ? '开始' :\n                       progress <= 0.5 ? '发展' :\n                       progress <= 0.75 ? '高潮' : '结束'\n\n      segments.push({\n        title: `${baseAction} - 第${i + 1}部分`,\n        description: `${baseAction}的${phaseDesc}阶段`,\n        prompt: generateVideoPrompt({\n          ...sequence,\n          action: `${baseAction}的第${i + 1}部分（${phaseDesc}阶段）`,\n          visualElements: sequence.visualElements\n        }, detailedCharacters, detailedScenes),\n        type: determineSegmentType(sequence),\n        duration: 5,\n        metadata: {\n          characters: extractCharactersFromSequence(sequence, detailedCharacters),\n          location: extractLocationFromSequence(sequence, detailedScenes),\n          mood: baseEmotion,\n          cameraAngle: sequence.visualElements || 'medium shot',\n          sequenceId: sequence.sequenceId,\n          segmentIndex: i + 1,\n          totalSegments: segmentCount,\n          progress: progress\n        }\n      })\n    }\n  }\n\n  return segments\n}\n\n// 获取阶段描述\nfunction getPhaseDescription(baseAction: string, phase: string, index: number, total: number): string {\n  const actionMap: { [key: string]: { [key: string]: string } } = {\n    '遇险': {\n      '接近危险': '张三小心翼翼地走在悬崖边缘，脚下的石头看起来并不稳固',\n      '察觉危险': '张三感觉到脚下石头的松动，开始意识到危险的存在',\n      '发生意外': '石头突然松动，张三失去平衡开始坠落',\n      '应对危机': '张三在坠落过程中努力寻找可以抓住的东西'\n    },\n    '救援': {\n      '发现情况': '李四听到呼救声，迅速确定声音来源',\n      '准备救援': '李四准备救援工具，评估救援方案',\n      '执行救援': '李四开始实施救援行动',\n      '完成救援': '李四成功救援，确保安全'\n    }\n  }\n\n  // 查找匹配的动作类型\n  for (const [action, phases] of Object.entries(actionMap)) {\n    if (baseAction.includes(action)) {\n      return phases[phase] || `${baseAction}的${phase}部分`\n    }\n  }\n\n  return `${baseAction}的${phase}部分`\n}\n\n// 异步生成各个视频片段 - 串行生成，一个接一个\nasync function generateSegmentsAsync(aiConfig: any, storyVideoId: string, segments: any[], modelId?: string) {\n  console.log(`开始串行生成${segments.length}个视频片段...`)\n\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i]\n    console.log(`\\n🎬 开始生成第 ${i + 1}/${segments.length} 个片段: ${segment.title}`)\n\n    try {\n      // 更新片段状态为生成中\n      await prisma.videoSegment.update({\n        where: { id: segment.id },\n        data: {\n          status: 'generating',\n          metadata: JSON.stringify({\n            ...JSON.parse(segment.metadata || '{}'),\n            startTime: new Date().toISOString(),\n            segmentOrder: i + 1,\n            totalSegments: segments.length\n          })\n        }\n      })\n\n      console.log(`📝 片段 ${i + 1} 开始生成提示词优化...`)\n\n      // 调用真实的视频生成API\n      const videoResult = await generateSingleSegment(aiConfig, segment, modelId)\n\n      // 根据生成结果更新片段信息\n      const updateData: any = {\n        duration: videoResult.duration,\n        metadata: JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          startTime: new Date().toISOString(),\n          generatedAt: new Date().toISOString(),\n          optimizedPrompt: videoResult.optimizedPrompt,\n          segmentOrder: i + 1,\n          totalSegments: segments.length\n        })\n      }\n\n      // 根据生成结果更新状态和URL\n      if (videoResult.status === 'completed' && videoResult.videoUrl) {\n        updateData.videoUrl = videoResult.videoUrl\n        updateData.thumbnailUrl = videoResult.thumbnailUrl\n        updateData.status = 'completed'\n        console.log(`✅ 片段 ${i + 1} 生成完成: ${segment.title}`)\n\n        // 执行一致性验证\n        try {\n          const metadata = JSON.parse(segment.metadata || '{}')\n          const characterNames = metadata.characters || []\n\n          for (const characterName of characterNames) {\n            const character = await prisma.character.findFirst({\n              where: { name: characterName }\n            })\n\n            if (character) {\n              const consistencyResult = await validateBasicConsistency(\n                videoResult.videoUrl,\n                character,\n                segment\n              )\n\n              await saveConsistencyValidation(\n                character.id,\n                episodeId,\n                consistencyResult,\n                videoResult.videoUrl\n              )\n\n              console.log(`🔍 角色 ${characterName} 一致性验证: ${consistencyResult.overallConsistency.toFixed(2)} (${consistencyResult.passed ? '通过' : '未通过'})`)\n            }\n          }\n        } catch (error) {\n          console.warn('一致性验证失败，但不影响主流程:', error)\n        }\n      } else if (videoResult.status === 'generating') {\n        updateData.status = 'generating'\n        updateData.metadata = JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          taskId: videoResult.taskId,\n          startTime: new Date().toISOString(),\n          generatedAt: new Date().toISOString(),\n          optimizedPrompt: videoResult.optimizedPrompt,\n          segmentOrder: i + 1,\n          totalSegments: segments.length\n        })\n        console.log(`🔄 片段 ${i + 1} 已提交生成任务，等待完成: ${segment.title}`)\n\n        // 串行生成：等待当前片段完成后再开始下一个\n        if (videoResult.taskId) {\n          console.log(`⏳ 等待片段 ${i + 1} 完成生成...`)\n          await waitForSegmentCompletion(segment.id, videoResult.taskId, 600000) // 最多等待10分钟\n        }\n      } else if (videoResult.status === 'failed') {\n        updateData.status = 'failed'\n        updateData.metadata = JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          error: videoResult.error,\n          startTime: new Date().toISOString(),\n          generatedAt: new Date().toISOString(),\n          optimizedPrompt: videoResult.optimizedPrompt,\n          segmentOrder: i + 1,\n          totalSegments: segments.length\n        })\n        console.log(`❌ 片段 ${i + 1} 生成失败: ${videoResult.error}`)\n      } else {\n        updateData.status = 'pending'\n        console.log(`⏳ 片段 ${i + 1} 状态待定`)\n      }\n\n      await prisma.videoSegment.update({\n        where: { id: segment.id },\n        data: updateData\n      })\n\n      // 串行生成：等待当前片段完成后再开始下一个\n      if (i < segments.length - 1) {\n        console.log(`⏱️ 等待 3 秒后开始下一个片段...`)\n        await new Promise(resolve => setTimeout(resolve, 3000))\n      }\n\n    } catch (error) {\n      console.error(`❌ 片段 ${i + 1} 生成失败:`, error)\n\n      // 更新片段状态为失败\n      await prisma.videoSegment.update({\n        where: { id: segment.id },\n        data: {\n          status: 'failed',\n          metadata: JSON.stringify({\n            ...JSON.parse(segment.metadata || '{}'),\n            error: error instanceof Error ? error.message : '生成失败',\n            failedAt: new Date().toISOString(),\n            segmentOrder: i + 1,\n            totalSegments: segments.length\n          })\n        }\n      })\n\n      // 串行生成：即使失败也继续下一个片段\n      if (i < segments.length - 1) {\n        console.log(`⏱️ 片段失败，等待 2 秒后继续下一个片段...`)\n        await new Promise(resolve => setTimeout(resolve, 2000))\n      }\n    }\n  }\n\n  // 检查所有片段是否完成，更新主视频状态\n  const completedSegments = await prisma.videoSegment.findMany({\n    where: {\n      storyVideoId,\n      status: 'completed'\n    }\n  })\n\n  const totalSegments = segments.length\n  const isAllCompleted = completedSegments.length === totalSegments\n\n  await prisma.storyVideo.update({\n    where: { id: storyVideoId },\n    data: {\n      status: isAllCompleted ? 'completed' : 'generating',\n      metadata: JSON.stringify({\n        totalSegments,\n        completedSegments: completedSegments.length,\n        completedAt: isAllCompleted ? new Date().toISOString() : null\n      })\n    }\n  })\n\n  console.log(`剧情视频生成完成: ${completedSegments.length}/${totalSegments} 片段成功`)\n}\n\n// 辅助函数：为剧情序列生成视频提示词（增强版：支持角色一致性）\nfunction generateVideoPrompt(sequence: any, characters: any[], scenes: any[]): string {\n  const action = sequence.action || ''\n  const emotion = sequence.emotion || ''\n  const visualElements = sequence.visualElements || ''\n\n  // 查找相关角色信息\n  const relevantCharacters = characters.filter(char =>\n    action.includes(char.name) || (sequence.keyMoments && sequence.keyMoments.some((moment: string) => moment.includes(char.name)))\n  )\n\n  // 查找相关场景信息\n  const relevantScenes = scenes.filter(scene =>\n    action.includes(scene.location) || visualElements.includes(scene.location)\n  )\n\n  // 构建一致性约束的提示词\n  return buildConsistencyVideoPrompt({\n    action,\n    emotion,\n    visualElements,\n    relevantCharacters,\n    relevantScenes,\n    sequence\n  })\n}\n\n// 构建一致性视频提示词\nfunction buildConsistencyVideoPrompt(options: {\n  action: string\n  emotion: string\n  visualElements: string\n  relevantCharacters: any[]\n  relevantScenes: any[]\n  sequence: any\n}): string {\n  const { action, emotion, visualElements, relevantCharacters, relevantScenes } = options\n\n  const sections: string[] = []\n\n  // 1. 剧情动作描述\n  sections.push(`${action}`)\n\n  // 2. 角色一致性约束（详细）\n  if (relevantCharacters.length > 0) {\n    const characterConstraints = relevantCharacters.map(char => {\n      const features = []\n\n      // 收集角色特征\n      if (char.name) features.push(`角色：${char.name}`)\n      if (char.appearance) features.push(`外貌：${char.appearance}`)\n      if (char.facial) features.push(`面部：${char.facial}`)\n      if (char.hairstyle) features.push(`发型：${char.hairstyle}`)\n      if (char.clothing) features.push(`服装：${char.clothing}`)\n      if (char.physique) features.push(`体型：${char.physique}`)\n\n      // 添加一致性约束\n      features.push('【一致性要求：面部特征、发型、服装必须与之前片段完全一致，独特标识清晰可见】')\n\n      return features.join('，')\n    }).join('；')\n\n    sections.push(characterConstraints)\n  }\n\n  // 3. 场景约束\n  if (relevantScenes.length > 0) {\n    const sceneDesc = relevantScenes[0]\n    const sceneFeatures = []\n    if (sceneDesc.description) sceneFeatures.push(`场景：${sceneDesc.description}`)\n    if (sceneDesc.atmosphere) sceneFeatures.push(`氛围：${sceneDesc.atmosphere}`)\n    if (sceneFeatures.length > 0) {\n      sections.push(sceneFeatures.join('，'))\n    }\n  }\n\n  // 4. 情感和视觉要素\n  if (emotion) sections.push(`情感：${emotion}`)\n  if (visualElements) sections.push(`视觉：${visualElements}`)\n\n  // 5. 技术要求\n  sections.push('高质量电影级画面，5秒视频，角色外貌前后一致，细节丰富，光影效果佳')\n\n  return sections.join('。') + '。'\n}\n\n// 辅助函数：确定片段类型\nfunction determineSegmentType(sequence: any): string {\n  const action = (sequence.action || '').toLowerCase()\n  const visualElements = (sequence.visualElements || '').toLowerCase()\n\n  if (action.includes('对话') || action.includes('说话') || action.includes('交谈')) {\n    return 'dialogue'\n  } else if (action.includes('战斗') || action.includes('打斗') || action.includes('追逐')) {\n    return 'action'\n  } else if (visualElements.includes('转场') || action.includes('转向') || action.includes('切换')) {\n    return 'transition'\n  } else {\n    return 'scene'\n  }\n}\n\n// 辅助函数：解析时长\nfunction parseDuration(duration: any): number {\n  if (typeof duration === 'number') return duration\n  if (typeof duration === 'string') {\n    const match = duration.match(/(\\d+)/)\n    return match ? parseInt(match[1]) : 15\n  }\n  return 15\n}\n\n// 辅助函数：从序列中提取角色\nfunction extractCharactersFromSequence(sequence: any, characters: any[]): string[] {\n  const action = sequence.action || ''\n  const keyMoments = sequence.keyMoments || []\n\n  return characters\n    .filter(char => action.includes(char.name) || keyMoments.some((moment: string) => moment.includes(char.name)))\n    .map(char => char.name)\n}\n\n// 辅助函数：从序列中提取位置\nfunction extractLocationFromSequence(sequence: any, scenes: any[]): string {\n  const action = sequence.action || ''\n  const visualElements = sequence.visualElements || ''\n\n  const relevantScene = scenes.find(scene =>\n    action.includes(scene.location) || visualElements.includes(scene.location)\n  )\n\n  return relevantScene ? relevantScene.location : '未知场景'\n}\n\n// 辅助函数：创建备用片段（增强角色信息）\nasync function createFallbackSegments(plotInfo: any, episodeId?: string): Promise<any[]> {\n  const generatedPrompt = plotInfo.generatedPrompt || ''\n\n  // 获取项目中的角色信息\n  let projectCharacters = []\n  try {\n    const targetEpisodeId = episodeId || plotInfo.episodeId\n    if (targetEpisodeId) {\n      const episode = await prisma.episode.findUnique({\n        where: { id: targetEpisodeId },\n        include: { project: true }\n      })\n\n      if (episode?.project?.id) {\n        projectCharacters = await prisma.character.findMany({\n          where: { projectId: episode.project.id }\n        })\n        console.log(`📋 找到 ${projectCharacters.length} 个项目角色`)\n      }\n    }\n  } catch (error) {\n    console.warn('获取项目角色失败:', error)\n  }\n\n  // 生成结构化角色DNA描述\n  const characterDescriptions = projectCharacters.map(char => {\n    return buildStructuredCharacterDNA(char)\n  }).join('\\n\\n')\n\n  if (generatedPrompt.length > 100) {\n    // 将长提示词分成3个片段，并添加结构化角色DNA\n    const length = generatedPrompt.length\n    const segmentLength = Math.floor(length / 3)\n\n    return [\n      {\n        title: \"开场片段\",\n        description: \"剧情开始\",\n        prompt: buildStructuredPrompt(\n          generatedPrompt.substring(0, segmentLength),\n          characterDescriptions,\n          \"开场建立，展现角色初始状态\",\n          \"wide shot\",\n          8\n        ),\n        type: \"scene\",\n        duration: 8,\n        metadata: {\n          characters: projectCharacters.map(c => c.name),\n          location: \"场景1\",\n          mood: \"neutral\",\n          cameraAngle: \"wide shot\"\n        }\n      },\n      {\n        title: \"发展片段\",\n        description: \"剧情发展\",\n        prompt: buildStructuredPrompt(\n          generatedPrompt.substring(segmentLength, segmentLength * 2),\n          characterDescriptions,\n          \"剧情推进，角色互动发展\",\n          \"medium shot\",\n          8\n        ),\n        type: \"action\",\n        duration: 8,\n        metadata: {\n          characters: projectCharacters.map(c => c.name),\n          location: \"场景2\",\n          mood: \"tense\",\n          cameraAngle: \"medium shot\"\n        }\n      },\n      {\n        title: \"结尾片段\",\n        description: \"剧情结尾\",\n        prompt: buildStructuredPrompt(\n          generatedPrompt.substring(segmentLength * 2),\n          characterDescriptions,\n          \"剧情收尾，角色状态变化\",\n          \"close up\",\n          6\n        ),\n        type: \"scene\",\n        duration: 6,\n        metadata: {\n          characters: projectCharacters.map(c => c.name),\n          location: \"场景3\",\n          mood: \"resolution\",\n          cameraAngle: \"close up\"\n        }\n      }\n    ]\n  }\n\n  // 如果提示词太短，创建单个片段\n  return [\n    {\n      title: \"完整片段\",\n      description: \"完整剧情\",\n      prompt: buildStructuredPrompt(\n        generatedPrompt,\n        characterDescriptions,\n        \"完整剧情展现，角色完整呈现\",\n        \"medium shot\",\n        8\n      ),\n      type: \"scene\",\n      duration: 8,\n      metadata: {\n        characters: projectCharacters.map(c => c.name),\n        location: \"主场景\",\n        mood: \"neutral\",\n        cameraAngle: \"medium shot\"\n      }\n    }\n  ]\n}\n\n// 生成单个视频片段\nasync function generateSingleSegment(aiConfig: any, segment: any, modelId?: string) {\n  try {\n    // 1. 获取DeepSeek配置用于提示词优化\n    const deepSeekConfig = await prisma.aIConfig.findFirst({\n      where: {\n        provider: 'deepseek',\n        enabled: true\n      }\n    })\n\n    let optimizedPrompt = segment.prompt\n\n    if (deepSeekConfig) {\n      try {\n        const deepSeekClient = new DeepSeekClient(deepSeekConfig)\n        const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：\n\n片段标题：${segment.title}\n片段描述：${segment.description}\n原始提示词：${segment.prompt}\n\n请按照以下要求优化：\n1. 确保描述具体且可视化\n2. 添加适合的镜头运动和角度\n3. 强调画面质量和风格\n4. 控制在100字以内\n5. 适合${segment.segmentType}类型的片段\n\n优化后的提示词：`\n\n        optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800)\n        console.log(`✅ 使用DeepSeek优化提示词: ${optimizedPrompt.substring(0, 100)}...`)\n      } catch (error) {\n        console.warn('DeepSeek提示词优化失败，使用原始提示词:', error.message)\n        optimizedPrompt = segment.prompt\n      }\n    } else {\n      console.warn('未找到DeepSeek配置，跳过提示词优化')\n      optimizedPrompt = segment.prompt\n    }\n\n    // 2. 调用视频生成API\n    const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment, modelId)\n\n    return {\n      videoUrl: videoResult.videoUrl,\n      thumbnailUrl: videoResult.thumbnailUrl,\n      optimizedPrompt: optimizedPrompt.trim(),\n      duration: videoResult.duration || segment.duration || 15,\n      status: videoResult.status || 'completed'\n    }\n  } catch (error) {\n    console.error('单个视频片段生成失败:', error)\n    return {\n      videoUrl: null,\n      thumbnailUrl: null,\n      optimizedPrompt: segment.prompt,\n      duration: segment.duration || 15,\n      status: 'failed',\n      error: error.message\n    }\n  }\n}\n\n// 调用视频生成API\nasync function callVideoGenerationAPI(prompt: string, segment: any, modelId?: string) {\n  try {\n    // 检查是否有角色参考图像（用于一致性判断）\n    const hasCharacterImages = segment.metadata?.characters?.some((char: any) =>\n      char.imageUrl || char.referenceImages?.length > 0\n    )\n\n    // 获取用户选择的视频生成模型配置\n    let videoModel\n\n    if (modelId) {\n      // 使用用户指定的模型\n      videoModel = await prisma.aIConfig.findUnique({\n        where: { id: modelId }\n      })\n\n      if (!videoModel) {\n        throw new Error(`未找到指定的模型配置: ${modelId}`)\n      }\n\n      if (!videoModel.enabled) {\n        throw new Error(`指定的模型未启用: ${videoModel.name}`)\n      }\n\n      console.log('🎯 使用用户指定的模型:')\n      console.log(`   模型: ${videoModel.name} (${videoModel.model})`)\n      console.log(`   提供商: ${videoModel.provider}`)\n\n      // 检查角色一致性\n      const hasCharacterImages = segment.metadata?.characters?.some((char: any) =>\n        char.imageUrl || char.referenceImages?.length > 0\n      )\n\n      if (hasCharacterImages) {\n        if (videoModel.supportsImageToVideo) {\n          console.log('✅ 检测到角色参考图像，使用I2V模型确保人物一致性')\n        } else {\n          console.log('💡 提醒: 检测到角色参考图像，建议使用I2V模型以确保人物一致性')\n        }\n      }\n\n      // 人物一致性提醒\n      if (hasCharacterImages) {\n        if (videoModel.supportsImageToVideo) {\n          console.log('✅ 检测到角色参考图像，使用I2V模型确保人物一致性')\n        } else {\n          console.log('💡 提醒: 检测到角色参考图像，建议使用I2V模型以确保人物一致性')\n        }\n      }\n\n    } else {\n      // 如果没有指定模型，使用智能选择器（保持向后兼容）\n      const videoModels = await prisma.aIConfig.findMany({\n        where: {\n          enabled: true,\n          OR: [\n            { supportsVideo: true },\n            { supportsImageToVideo: true }\n          ]\n        }\n      })\n\n      if (videoModels.length === 0) {\n        throw new Error('未找到可用的视频生成模型')\n      }\n\n      // 使用智能模型选择器（增强版：优先考虑角色一致性）\n      const modelSelector = new SmartModelSelector(videoModels)\n      const characterImages = await getCharacterReferenceImages(segment)\n      const segmentInfo = SmartModelSelector.analyzeSegment(segment, characterImages)\n\n      // 如果有角色图像，优先选择I2V模型\n      if (characterImages.length > 0) {\n        const i2vModel = videoModels.find(model =>\n          model.provider === 'doubao' &&\n          model.model === 'ep-20250624195026-qjsmk' &&\n          model.supportsImageToVideo\n        )\n\n        if (i2vModel) {\n          videoModel = i2vModel\n          console.log('🎨 智能选择结果: 检测到角色图像，自动选择I2V模型确保人物一致性')\n          console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`)\n          console.log(`   角色图像数量: ${characterImages.length}`)\n        } else {\n          const modelSelection = modelSelector.selectBestModel(segmentInfo)\n          videoModel = modelSelection.selectedModel\n          console.log('⚠️ 未找到可用的I2V模型，使用智能选择器')\n          console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`)\n          console.log(`   选择理由: ${modelSelection.reason}`)\n        }\n      } else {\n        const modelSelection = modelSelector.selectBestModel(segmentInfo)\n        videoModel = modelSelection.selectedModel\n        console.log('🤖 智能模型选择结果:')\n        console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`)\n        console.log(`   选择理由: ${modelSelection.reason}`)\n      }\n    }\n\n    // 根据不同的视频生成服务调用相应的API\n    if (videoModel.provider === 'doubao') {\n      // 检查是否为图生视频模型\n      if (videoModel.model === 'ep-20250624195026-qjsmk') {\n        // Doubao-Seedance-1.0-lite-i2v - 图生视频\n        return await callDoubaoImageToVideoAPI(videoModel, prompt, segment)\n      } else if (videoModel.model === 'ep-20250624192345-5ccwj') {\n        // Doubao-Seedance-1.0-lite-t2v - 文生视频\n        return await callDoubaoTextToVideoAPI(videoModel, prompt, segment)\n      } else if (videoModel.model === 'ep-20250624192235-zttm6') {\n        // Doubao-Seedance-1.0-pro - 专业版视频\n        return await callDoubaoVideoAPI(videoModel, prompt, segment)\n      } else {\n        // 其他豆包模型，使用原有的API\n        return await callDoubaoVideoAPI(videoModel, prompt, segment)\n      }\n    } else if (videoModel.provider === 'minimax') {\n      return await callMinimaxHailuoAPI(videoModel, prompt, segment)\n    } else if (videoModel.provider === 'tongyi') {\n      return await callTongyiVideoAPI(videoModel, prompt, segment)\n    } else if (videoModel.provider === 'skyreels') {\n      return await callSkyReelsVideoAPI(videoModel, prompt, segment)\n    }\n\n    // 如果没有配置真实的视频生成API，返回占位符\n    console.warn('未配置真实的视频生成API，返回占位符数据')\n    return {\n      videoUrl: null,\n      thumbnailUrl: null,\n      duration: segment.duration || 15,\n      status: 'pending'\n    }\n  } catch (error) {\n    console.error('调用视频生成API失败:', error)\n    throw error\n  }\n}\n\n// 轮询通义万相任务状态\nasync function pollTongyiTaskStatus(config: any, taskId: string, segment: any) {\n  const maxAttempts = 120 // 最多轮询120次（10分钟）\n  let pollInterval = 3000 // 初始3秒轮询一次\n  const maxPollInterval = 10000 // 最大10秒间隔\n  const intervalIncrement = 1000 // 每次增加1秒\n\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      const startTime = Date.now()\n      console.log(`轮询通义万相任务状态 (${attempt}/${maxAttempts}): ${taskId} [间隔: ${pollInterval}ms]`)\n\n      const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${config.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      const responseTime = Date.now() - startTime\n\n      if (!response.ok) {\n        console.error(`轮询请求失败: ${response.status} ${response.statusText} [响应时间: ${responseTime}ms]`)\n        await new Promise(resolve => setTimeout(resolve, pollInterval))\n        // 智能调整轮询间隔\n        if (pollInterval < maxPollInterval) {\n          pollInterval = Math.min(pollInterval + intervalIncrement, maxPollInterval)\n        }\n        continue\n      }\n\n      const result = await response.json()\n      console.log(`任务状态响应 [响应时间: ${responseTime}ms]:`, JSON.stringify(result, null, 2))\n\n      if (result.output) {\n        const status = result.output.task_status\n\n        if (status === 'SUCCEEDED') {\n          // 任务完成，更新数据库\n          // 通义万相API返回的字段是 video_url，不是 results 数组\n          const videoUrl = result.output.video_url || result.output.results?.[0]?.url\n          const videoSize = result.output.video_size || result.output.results?.[0]?.size || 'unknown'\n          const duration = result.output.video_duration || result.output.results?.[0]?.duration || 5\n\n          console.log('🔍 解析视频URL:', {\n            video_url: result.output.video_url,\n            results: result.output.results,\n            finalUrl: videoUrl\n          })\n\n          if (videoUrl) {\n            const completedAt = new Date().toISOString()\n\n            // 立即自动下载视频到本地存储\n            console.log(`📥 开始自动下载视频到本地存储...`)\n            const localVideoPath = await downloadVideoToLocal(videoUrl, segment.id, segment.title)\n\n            await prisma.videoSegment.update({\n              where: { id: segment.id },\n              data: {\n                videoUrl: localVideoPath || videoUrl, // 优先使用本地路径，失败则使用原URL\n                status: 'completed',\n                duration: duration,\n                metadata: JSON.stringify({\n                  taskId: taskId,\n                  provider: 'tongyi',\n                  completedAt: completedAt,\n                  videoSize: videoSize,\n                  responseTime: responseTime,\n                  totalAttempts: attempt,\n                  generationTime: Date.now() - startTime,\n                  originalUrl: videoUrl,\n                  localPath: localVideoPath,\n                  autoDownloaded: !!localVideoPath\n                })\n              }\n            })\n            console.log(`🎉 片段 ${segment.segmentIndex} 通义万相视频生成完成!`)\n            console.log(`   原始URL: ${videoUrl}`)\n            console.log(`   本地路径: ${localVideoPath || '下载失败，使用原始URL'}`)\n            console.log(`   文件大小: ${videoSize}`)\n            console.log(`   生成耗时: ${attempt} 次轮询`)\n            console.log(`   完成时间: ${completedAt}`)\n          } else {\n            console.error('❌ 未找到视频URL，完整响应:', JSON.stringify(result.output, null, 2))\n          }\n          return\n        } else if (status === 'FAILED') {\n          // 任务失败\n          const errorMessage = result.output.message || result.output.code || '任务失败'\n          const errorDetails = {\n            taskId: taskId,\n            provider: 'tongyi',\n            error: errorMessage,\n            failedAt: new Date().toISOString(),\n            totalAttempts: attempt,\n            responseTime: responseTime,\n            fullResponse: result.output\n          }\n\n          await prisma.videoSegment.update({\n            where: { id: segment.id },\n            data: {\n              status: 'failed',\n              metadata: JSON.stringify(errorDetails)\n            }\n          })\n          console.error(`❌ 片段 ${segment.segmentIndex} 通义万相视频生成失败:`)\n          console.error(`   错误信息: ${errorMessage}`)\n          console.error(`   完整响应:`, result.output)\n          return\n        }\n        // 状态为 PENDING 或 RUNNING，继续轮询\n        console.log(`⏳ 任务状态: ${status}, 继续轮询...`)\n      }\n\n      // 智能调整轮询间隔：前10次保持快速，之后逐渐增加间隔\n      if (attempt > 10 && pollInterval < maxPollInterval) {\n        pollInterval = Math.min(pollInterval + intervalIncrement, maxPollInterval)\n      }\n\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    } catch (error) {\n      console.error(`轮询通义万相任务状态失败 (${attempt}/${maxAttempts}):`, error)\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n  }\n\n  // 超时处理\n  console.error(`通义万相任务 ${taskId} 轮询超时`)\n  await prisma.videoSegment.update({\n    where: { id: segment.id },\n    data: {\n      status: 'failed',\n      metadata: JSON.stringify({\n        taskId: taskId,\n        provider: 'tongyi',\n        error: '轮询超时',\n        timeoutAt: new Date().toISOString()\n      })\n    }\n  })\n}\n\n// 通义万相文生视频API调用\nasync function callTongyiVideoAPI(config: any, prompt: string, segment: any) {\n  try {\n    // 内容安全检查\n    const validation = validatePromptContent(prompt)\n    if (!validation.isValid) {\n      console.error(`片段 ${segment.segmentIndex} 内容检查失败:`, validation.reason)\n      throw new Error(`内容检查失败: ${validation.reason}`)\n    }\n\n    // 优化提示词\n    const optimizedPrompt = optimizePrompt(prompt)\n    console.log(`片段 ${segment.segmentIndex} 提示词优化:`)\n    console.log(`  原始: ${prompt.substring(0, 100)}...`)\n    console.log(`  优化: ${optimizedPrompt.substring(0, 100)}...`)\n\n    // 创建视频生成任务\n    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json',\n        'X-DashScope-Async': 'enable'\n      },\n      body: JSON.stringify({\n        model: config.model,\n        input: {\n          prompt: optimizedPrompt\n        },\n        parameters: {\n          size: '1280*720', // 720P分辨率\n          duration: segment.duration || 5, // 使用片段时长\n          prompt_extend: true // 开启智能改写\n        }\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error(`通义万相API调用失败: ${response.status} ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    console.log('通义万相API响应:', JSON.stringify(result, null, 2))\n\n    // 检查任务创建是否成功\n    if (result.output && result.output.task_id) {\n      // 启动轮询任务状态\n      const taskId = result.output.task_id\n      console.log(`通义万相任务创建成功，任务ID: ${taskId}，开始轮询状态...`)\n\n      // 异步轮询任务状态\n      pollTongyiTaskStatus(config, taskId, segment)\n\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: 5,\n        status: 'generating',\n        taskId: taskId,\n        provider: 'tongyi'\n      }\n    } else {\n      console.error('通义万相API返回格式异常，完整响应:', result)\n      throw new Error(`通义万相API返回格式异常: ${JSON.stringify(result)}`)\n    }\n  } catch (error) {\n    console.error('通义万相文生视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 豆包API直接使用endpoint ID作为模型名称\nfunction getDoubaoModelName(endpointId: string): string {\n  // 直接返回endpoint ID，不需要映射\n  return endpointId\n}\n\n// 豆包文生视频API调用 (Seedance 1.0 Lite T2V)\nasync function callDoubaoTextToVideoAPI(config: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 调用豆包文生视频API (T2V)，片段: ${segment.segmentIndex}`)\n\n    // 构建文生视频请求内容\n    const content = [\n      {\n        type: \"text\",\n        text: prompt\n      }\n    ]\n\n    console.log(`📝 文生视频生成，提示词长度: ${prompt.length}`)\n\n    // 豆包文生视频API调用\n    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        model: getDoubaoModelName(config.model),\n        content: content\n      })\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error(`豆包文生视频API调用失败: ${response.status} ${errorText}`)\n      throw new Error(`豆包文生视频API调用失败: ${response.status} ${errorText}`)\n    }\n\n    const result = await response.json()\n    console.log('豆包文生视频API响应:', JSON.stringify(result, null, 2))\n\n    // 处理任务ID\n    let taskId = null\n    if (result.data && result.data.task_id) {\n      taskId = result.data.task_id\n    } else if (result.task_id) {\n      taskId = result.task_id\n    } else if (result.id) {\n      taskId = result.id\n    } else if (result.data && result.data.id) {\n      taskId = result.data.id\n    }\n\n    if (taskId) {\n      console.log(`豆包文生视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`)\n\n      // 启动轮询任务状态\n      pollDoubaoTaskStatus(config, taskId, segment)\n\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 5,\n        status: 'generating',\n        taskId: taskId,\n        provider: 'doubao-t2v'\n      }\n    }\n\n    console.error('豆包文生视频API返回格式异常，完整响应:', result)\n    throw new Error(`豆包文生视频API返回格式异常: ${JSON.stringify(result)}`)\n  } catch (error) {\n    console.error('豆包文生视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 豆包图生视频API调用 (Seedance 1.0 Lite I2V)\nasync function callDoubaoImageToVideoAPI(config: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 调用豆包图生视频API，片段: ${segment.segmentIndex}`)\n\n    // 获取角色参考图像作为输入图像\n    const characterImages = await getCharacterReferenceImages(segment)\n\n    if (characterImages.length === 0) {\n      console.warn('⚠️ 图生视频需要输入图像，但未找到角色参考图像，回退到文生视频')\n      // 获取T2V模型配置进行回退\n      const t2vConfig = await prisma.aIConfig.findFirst({\n        where: {\n          provider: 'doubao',\n          model: 'ep-20250624192345-5ccwj', // T2V模型\n          enabled: true\n        }\n      })\n      if (!t2vConfig) {\n        throw new Error('未找到可用的豆包T2V模型配置')\n      }\n      return await callDoubaoTextToVideoAPI(t2vConfig, prompt, segment)\n    }\n\n    // 验证图像URL是否有效\n    const validImageUrl = await validateImageUrl(characterImages[0])\n    if (!validImageUrl) {\n      console.warn('⚠️ 角色参考图像URL无效，回退到文生视频')\n      // 获取T2V模型配置进行回退\n      const t2vConfig = await prisma.aIConfig.findFirst({\n        where: {\n          provider: 'doubao',\n          model: 'ep-20250624192345-5ccwj', // T2V模型\n          enabled: true\n        }\n      })\n      if (!t2vConfig) {\n        throw new Error('未找到可用的豆包T2V模型配置')\n      }\n      return await callDoubaoTextToVideoAPI(t2vConfig, prompt, segment)\n    }\n\n    console.log(`🖼️ 使用参考图像进行图生视频: ${validImageUrl.substring(0, 100)}...`)\n\n    // 构建图生视频请求内容 - 豆包I2V正确格式\n    const content = [\n      {\n        type: \"text\",\n        text: `${prompt} --ratio adaptive --dur ${segment.duration || 5}`\n      },\n      {\n        type: \"image_url\",\n        image_url: {\n          url: validImageUrl\n        }\n      }\n    ]\n\n    console.log(`🔄 图生视频生成，提示词长度: ${prompt.length}，输入图像: ${characterImages[0].substring(0, 100)}...`)\n\n    // 豆包图生视频API调用\n    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        model: getDoubaoModelName(config.model),\n        content: content\n      })\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error(`豆包图生视频API调用失败: ${response.status} ${errorText}`)\n      throw new Error(`豆包图生视频API调用失败: ${response.status} ${errorText}`)\n    }\n\n    const result = await response.json()\n    console.log('豆包图生视频API响应:', JSON.stringify(result, null, 2))\n\n    // 处理任务ID\n    let taskId = null\n    if (result.data && result.data.task_id) {\n      taskId = result.data.task_id\n    } else if (result.task_id) {\n      taskId = result.task_id\n    } else if (result.id) {\n      taskId = result.id\n    } else if (result.data && result.data.id) {\n      taskId = result.data.id\n    }\n\n    if (taskId) {\n      console.log(`豆包图生视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`)\n\n      // 启动轮询任务状态（复用现有的轮询函数）\n      pollDoubaoTaskStatus(config, taskId, segment)\n\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 5,\n        status: 'generating',\n        taskId: taskId,\n        provider: 'doubao-i2v',\n        inputImage: validImageUrl\n      }\n    }\n\n    console.error('豆包图生视频API返回格式异常，完整响应:', result)\n    throw new Error(`豆包图生视频API返回格式异常: ${JSON.stringify(result)}`)\n  } catch (error) {\n    console.error('豆包图生视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 豆包视频API调用\nasync function callDoubaoVideoAPI(config: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 调用豆包视频API，片段: ${segment.segmentIndex}`)\n\n    // 恢复视觉约束功能 - 双重约束系统核心\n    const characterImages = await getCharacterReferenceImages(segment)\n\n    // 构建内容数组（文本+图像双重约束）\n    const content = [\n      {\n        type: \"text\",\n        text: `${prompt}` // 文本约束\n      }\n    ]\n\n    // 添加视觉约束（参考图像）\n    if (characterImages.length > 0) {\n      console.log(`🖼️ 应用视觉约束：${characterImages.length} 个角色参考图像`)\n      content.push({\n        type: \"image_url\",\n        image_url: {\n          url: characterImages[0] // 使用主要角色的参考图像\n        }\n      })\n    } else {\n      console.log(`📝 仅使用文本约束（未找到角色参考图像）`)\n    }\n\n    console.log(`🔒 双重约束生成视频，提示词长度: ${prompt.length}，参考图像: ${characterImages.length}个`)\n\n    // 豆包视频生成API调用 - 使用正确的视频生成端点\n    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        model: getDoubaoModelName(config.model),\n        content: content\n        // 移除parameters，豆包API可能不支持这些参数\n      })\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error(`豆包API调用失败: ${response.status} ${errorText}`)\n      throw new Error(`豆包API调用失败: ${response.status} ${errorText}`)\n    }\n\n    const result = await response.json()\n    console.log('豆包API响应:', JSON.stringify(result, null, 2))\n\n    // 豆包视频生成API返回任务ID - 支持多种格式\n    let taskId = null\n\n    if (result.data && result.data.task_id) {\n      taskId = result.data.task_id\n    } else if (result.task_id) {\n      taskId = result.task_id\n    } else if (result.id) {\n      // 豆包API返回的是id字段\n      taskId = result.id\n    } else if (result.data && result.data.id) {\n      taskId = result.data.id\n    }\n\n    if (taskId) {\n      console.log(`豆包视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`)\n\n      // 启动轮询任务状态\n      pollDoubaoTaskStatus(config, taskId, segment)\n\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 5,\n        status: 'generating',\n        taskId: taskId,\n        provider: 'doubao'\n      }\n    }\n\n    console.error('豆包API返回格式异常，完整响应:', result)\n    throw new Error(`豆包API返回格式异常: ${JSON.stringify(result)}`)\n  } catch (error) {\n    console.error('豆包视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 轮询豆包任务状态\nasync function pollDoubaoTaskStatus(config: any, taskId: string, segment: any) {\n  const maxAttempts = 120\n  let pollInterval = 3000\n\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      console.log(`轮询豆包任务状态 (${attempt}/${maxAttempts}): ${taskId}`)\n\n      // 查询豆包任务状态\n      const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${config.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (!response.ok) {\n        console.error(`轮询请求失败: ${response.status} ${response.statusText}`)\n        await new Promise(resolve => setTimeout(resolve, pollInterval))\n        continue\n      }\n\n      const result = await response.json()\n      console.log(`豆包任务状态响应:`, JSON.stringify(result, null, 2))\n\n      // 豆包API实际响应格式：{ \"id\": \"xxx\", \"status\": \"succeeded\", \"content\": { \"video_url\": \"xxx\" } }\n      const status = result.status\n\n      if (status === 'succeeded' || status === 'completed') {\n        // 任务完成\n        const videoUrl = result.content?.video_url || result.video_url\n\n        if (videoUrl) {\n          // 尝试获取视频的实际时长\n          let actualDuration = segment.duration || 5\n          try {\n            actualDuration = await getVideoActualDuration(videoUrl)\n            console.log(`📏 获取到视频实际时长: ${actualDuration}秒 (预期: ${segment.duration}秒)`)\n          } catch (error) {\n            console.warn('获取视频实际时长失败，使用预设时长:', error.message)\n          }\n\n          await prisma.videoSegment.update({\n            where: { id: segment.id },\n            data: {\n              videoUrl: videoUrl,\n              status: 'completed',\n              duration: actualDuration,\n              metadata: JSON.stringify({\n                taskId: taskId,\n                provider: 'doubao',\n                completedAt: new Date().toISOString(),\n                totalAttempts: attempt,\n                responseFormat: 'direct',\n                expectedDuration: segment.duration,\n                actualDuration: actualDuration,\n                durationMatch: Math.abs(actualDuration - (segment.duration || 5)) <= 2\n              })\n            }\n          })\n          console.log(`🎉 片段 ${segment.segmentIndex} 豆包视频生成完成: ${videoUrl} (时长: ${actualDuration}秒)`)\n          return\n        } else {\n          console.error('❌ 任务成功但未找到视频URL，完整响应:', result)\n        }\n      } else if (status === 'failed' || status === 'error') {\n        // 任务失败\n        const errorMessage = result.message || result.error || '任务失败'\n        await prisma.videoSegment.update({\n          where: { id: segment.id },\n          data: {\n            status: 'failed',\n            metadata: JSON.stringify({\n              taskId: taskId,\n              provider: 'doubao',\n              error: errorMessage,\n              failedAt: new Date().toISOString(),\n              totalAttempts: attempt,\n              fullResponse: result\n            })\n          }\n        })\n        console.error(`❌ 片段 ${segment.segmentIndex} 豆包视频生成失败: ${errorMessage}`)\n        return\n      } else if (status === 'pending' || status === 'running' || status === 'processing') {\n        // 状态为进行中，继续轮询\n        console.log(`⏳ 任务状态: ${status}, 继续轮询...`)\n      } else {\n        console.log(`❓ 未知任务状态: ${status}, 继续轮询...`)\n      }\n\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    } catch (error) {\n      console.error(`轮询豆包任务状态失败 (${attempt}/${maxAttempts}):`, error)\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n  }\n\n  // 超时处理\n  console.error(`豆包任务 ${taskId} 轮询超时`)\n  await prisma.videoSegment.update({\n    where: { id: segment.id },\n    data: {\n      status: 'failed',\n      metadata: JSON.stringify({\n        taskId: taskId,\n        provider: 'doubao',\n        error: '轮询超时',\n        timeoutAt: new Date().toISOString()\n      })\n    }\n  })\n}\n\n// MiniMax Hailuo API调用\nasync function callMinimaxHailuoAPI(config: any, prompt: string, segment: any) {\n  try {\n    const response = await fetch('https://api.minimaxi.com/v1/video_generation', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        model: config.model,\n        prompt: prompt,\n        video_setting: {\n          video_duration: Math.min(segment.duration || 6, 6), // MiniMax最大支持6秒，使用片段时长但不超过6秒\n          video_aspect_ratio: '16:9'\n        }\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error(`MiniMax API调用失败: ${response.status} ${response.statusText}`)\n    }\n\n    const result = await response.json()\n\n    console.log('MiniMax API响应:', JSON.stringify(result, null, 2))\n\n    // 根据MiniMax API的响应格式处理结果\n    if (result.task_id) {\n      // 异步生成，需要轮询状态\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 15,\n        status: 'generating',\n        taskId: result.task_id\n      }\n    } else if (result.video_url) {\n      // 同步生成完成\n      return {\n        videoUrl: result.video_url,\n        thumbnailUrl: result.thumbnail_url || null,\n        duration: result.duration || segment.duration || 15,\n        status: 'completed'\n      }\n    } else if (result.data && result.data.task_id) {\n      // 可能的嵌套格式\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 15,\n        status: 'generating',\n        taskId: result.data.task_id\n      }\n    } else {\n      console.error('MiniMax API返回格式异常，完整响应:', result)\n      throw new Error(`MiniMax API返回格式异常: ${JSON.stringify(result)}`)\n    }\n  } catch (error) {\n    console.error('MiniMax Hailuo API调用失败:', error)\n    throw error\n  }\n}\n\n// 检查角色图像先决条件\nasync function checkCharacterImagePrerequisites(projectId: string): Promise<{\n  success: boolean\n  error?: string\n  missingCharacters?: string[]\n}> {\n  try {\n    console.log(`🔍 检查项目 ${projectId} 的角色图像先决条件`)\n\n    // 获取项目的所有角色\n    const characters = await prisma.character.findMany({\n      where: { projectId },\n      select: { id: true, name: true, generatedImages: true }\n    })\n\n    if (characters.length === 0) {\n      return {\n        success: false,\n        error: '项目中没有角色信息，请先创建角色',\n        missingCharacters: []\n      }\n    }\n\n    console.log(`📋 找到 ${characters.length} 个角色`)\n\n    // 检查每个角色是否有参考图像\n    const missingCharacters: string[] = []\n\n    for (const character of characters) {\n      console.log(`🎭 检查角色: ${character.name}`)\n\n      if (!character.generatedImages) {\n        console.log(`❌ 角色 ${character.name} 没有生成图像`)\n        missingCharacters.push(character.name)\n        continue\n      }\n\n      try {\n        const images = JSON.parse(character.generatedImages)\n        const hasAllImages = images.front && images.side && images.back\n\n        if (!hasAllImages) {\n          console.log(`❌ 角色 ${character.name} 的图像不完整`)\n          missingCharacters.push(character.name)\n        } else {\n          console.log(`✅ 角色 ${character.name} 有完整的参考图像`)\n        }\n      } catch (e) {\n        console.log(`❌ 角色 ${character.name} 的图像数据解析失败`)\n        missingCharacters.push(character.name)\n      }\n    }\n\n    if (missingCharacters.length > 0) {\n      const errorMessage = `以下角色缺少参考图像，请先生成角色形象：${missingCharacters.join('、')}`\n      console.log(`❌ 先决条件检查失败: ${errorMessage}`)\n\n      return {\n        success: false,\n        error: errorMessage,\n        missingCharacters\n      }\n    }\n\n    console.log('✅ 所有角色都有完整的参考图像')\n    return { success: true }\n\n  } catch (error) {\n    console.error('检查角色图像先决条件失败:', error)\n    return {\n      success: false,\n      error: '检查角色图像时发生错误，请稍后重试'\n    }\n  }\n}\n\n// 获取角色参考图像（视觉约束）- 增强版\nasync function getCharacterReferenceImages(segment: any): Promise<string[]> {\n  try {\n    console.log(`🔍 开始获取片段 ${segment.segmentIndex || 'unknown'} 的角色参考图像`)\n\n    const characterImages: string[] = []\n    let characterNames: string[] = []\n\n    // 1. 从segment的metadata中获取角色名称\n    if (segment.metadata) {\n      try {\n        const metadata = typeof segment.metadata === 'string' ? JSON.parse(segment.metadata) : segment.metadata\n        characterNames = metadata.characters || []\n        console.log(`📋 从metadata获取角色: ${characterNames.join(', ')}`)\n      } catch (e) {\n        console.warn('解析segment metadata失败:', e)\n      }\n    }\n\n    // 2. 智能角色名称提取（从文本中识别）\n    if (characterNames.length === 0) {\n      characterNames = await extractCharacterNamesFromText(segment)\n      console.log(`🤖 智能提取角色: ${characterNames.join(', ')}`)\n    }\n\n    // 3. 如果仍然没有角色，尝试获取项目的所有主要角色\n    if (characterNames.length === 0) {\n      characterNames = await getProjectMainCharacters(segment)\n      console.log(`📚 获取项目主要角色: ${characterNames.join(', ')}`)\n    }\n\n    console.log(`🎭 最终确定角色列表: ${characterNames.join(', ')}`)\n\n    // 4. 获取角色的最佳参考图像\n    if (characterNames.length > 0) {\n      const characters = await prisma.character.findMany({\n        where: {\n          name: { in: characterNames }\n        }\n      })\n\n      for (const character of characters) {\n        const bestImage = await selectBestReferenceImage(character, segment)\n        if (bestImage) {\n          characterImages.push(bestImage)\n          console.log(`✅ 为角色 ${character.name} 选择最佳参考图像`)\n        } else {\n          console.warn(`⚠️ 角色 ${character.name} 没有可用的参考图像`)\n        }\n      }\n    }\n\n    console.log(`🖼️ 总共获取 ${characterImages.length} 个角色参考图像`)\n    return characterImages\n\n  } catch (error) {\n    console.error('获取角色参考图像失败:', error)\n    return []\n  }\n}\n\n// 智能提取角色名称\nasync function extractCharacterNamesFromText(segment: any): Promise<string[]> {\n  const text = `${segment.title || ''} ${segment.description || ''} ${segment.prompt || ''}`.toLowerCase()\n  const characterNames: string[] = []\n\n  // 获取数据库中所有角色名称进行匹配\n  try {\n    const allCharacters = await prisma.character.findMany({\n      select: { name: true }\n    })\n\n    for (const character of allCharacters) {\n      if (text.includes(character.name.toLowerCase())) {\n        characterNames.push(character.name)\n      }\n    }\n  } catch (error) {\n    console.warn('获取角色列表失败:', error)\n\n    // 降级方案：硬编码常见角色名称\n    const commonNames = ['张三', '李四', '王五', '赵六', '小明', '小红']\n    for (const name of commonNames) {\n      if (text.includes(name.toLowerCase())) {\n        characterNames.push(name)\n      }\n    }\n  }\n\n  return [...new Set(characterNames)] // 去重\n}\n\n// 获取项目主要角色\nasync function getProjectMainCharacters(segment: any): Promise<string[]> {\n  try {\n    // 尝试从segment中获取项目信息\n    // 这里需要根据实际的数据结构调整\n    const characters = await prisma.character.findMany({\n      take: 3, // 最多获取3个主要角色\n      orderBy: { createdAt: 'asc' } // 按创建时间排序，假设先创建的是主要角色\n    })\n\n    return characters.map(c => c.name)\n  } catch (error) {\n    console.warn('获取项目主要角色失败:', error)\n    return []\n  }\n}\n\n// 选择最佳参考图像\nasync function selectBestReferenceImage(character: any, segment: any): Promise<string | null> {\n  try {\n    if (!character.generatedImages) {\n      return null\n    }\n\n    const images = JSON.parse(character.generatedImages)\n\n    // 解析参考图像集合\n    const referenceSet: ReferenceImageSet = {\n      standardPortrait: images.front,\n      profileView: images.side,\n      fullBody: images.back,\n      expressions: {\n        neutral: images.front\n      },\n      angles: {\n        front: images.front,\n        leftProfile: images.side,\n        rightProfile: images.side,\n        threeQuarter: images.front\n      }\n    }\n\n    // 根据场景类型选择最佳图像\n    const sceneType = segment.type || 'scene'\n    const cameraAngle = segment.metadata?.cameraAngle || 'medium shot'\n\n    // 选择策略\n    if (cameraAngle.includes('close') || sceneType === 'dialogue') {\n      // 特写镜头优先使用正面头像\n      return referenceSet.standardPortrait || referenceSet.angles?.front\n    } else if (cameraAngle.includes('profile') || sceneType === 'profile') {\n      // 侧面镜头使用侧面图像\n      return referenceSet.profileView || referenceSet.angles?.leftProfile\n    } else if (cameraAngle.includes('wide') || sceneType === 'action') {\n      // 广角镜头可以使用全身图像\n      return referenceSet.fullBody || referenceSet.standardPortrait\n    } else {\n      // 默认使用标准头像\n      return referenceSet.standardPortrait || referenceSet.angles?.front || referenceSet.profileView\n    }\n\n  } catch (error) {\n    console.warn(`选择角色 ${character.name} 最佳参考图像失败:`, error)\n    return null\n  }\n}\n\n// 等待片段完成生成\nasync function waitForSegmentCompletion(segmentId: string, taskId: string, maxWaitTime: number = 600000) {\n  const startTime = Date.now()\n  const checkInterval = 5000 // 每5秒检查一次\n\n  console.log(`⏳ 开始等待片段 ${segmentId} 完成，任务ID: ${taskId}`)\n\n  while (Date.now() - startTime < maxWaitTime) {\n    try {\n      // 检查数据库中的片段状态\n      const segment = await prisma.videoSegment.findUnique({\n        where: { id: segmentId }\n      })\n\n      if (!segment) {\n        console.error(`片段 ${segmentId} 不存在`)\n        return false\n      }\n\n      if (segment.status === 'completed') {\n        console.log(`✅ 片段 ${segmentId} 已完成生成`)\n        return true\n      } else if (segment.status === 'failed') {\n        console.log(`❌ 片段 ${segmentId} 生成失败`)\n        return false\n      }\n\n      // 继续等待\n      console.log(`⏳ 片段 ${segmentId} 仍在生成中，继续等待... (已等待 ${Math.round((Date.now() - startTime) / 1000)}s)`)\n      await new Promise(resolve => setTimeout(resolve, checkInterval))\n\n    } catch (error) {\n      console.error(`检查片段 ${segmentId} 状态失败:`, error)\n      await new Promise(resolve => setTimeout(resolve, checkInterval))\n    }\n  }\n\n  console.log(`⏰ 片段 ${segmentId} 等待超时 (${maxWaitTime / 1000}s)`)\n  return false\n}\n\n// 基础一致性验证\nasync function validateBasicConsistency(\n  generatedImageUrl: string,\n  characterInfo: any,\n  segmentInfo: any\n): Promise<ConsistencyResult> {\n  try {\n    console.log(`🔍 开始一致性验证，角色: ${characterInfo.name}`)\n\n    const result: ConsistencyResult = {\n      textConsistency: 0,\n      imageConsistency: 0,\n      overallConsistency: 0,\n      passed: false,\n      issues: [],\n      suggestions: []\n    }\n\n    // 1. 文本一致性验证\n    result.textConsistency = await validateTextConsistency(characterInfo, segmentInfo)\n\n    // 2. 图像一致性验证（简化版）\n    result.imageConsistency = await validateImageConsistency(generatedImageUrl, characterInfo)\n\n    // 3. 计算综合评分\n    const textWeight = 0.6  // 文本约束权重\n    const imageWeight = 0.4 // 图像约束权重\n\n    result.overallConsistency = (\n      result.textConsistency * textWeight +\n      result.imageConsistency * imageWeight\n    )\n\n    // 4. 判断是否通过验证\n    const threshold = 0.7 // 一致性阈值\n    result.passed = result.overallConsistency >= threshold\n\n    // 5. 生成问题和建议\n    if (!result.passed) {\n      if (result.textConsistency < 0.6) {\n        result.issues.push('文本约束一致性不足')\n        result.suggestions.push('增强角色DNA描述的详细程度')\n      }\n\n      if (result.imageConsistency < 0.6) {\n        result.issues.push('图像约束一致性不足')\n        result.suggestions.push('提供更清晰的角色参考图像')\n      }\n    }\n\n    console.log(`📊 一致性验证结果: 文本${result.textConsistency.toFixed(2)}, 图像${result.imageConsistency.toFixed(2)}, 综合${result.overallConsistency.toFixed(2)}`)\n\n    return result\n\n  } catch (error) {\n    console.error('一致性验证失败:', error)\n    return {\n      textConsistency: 0,\n      imageConsistency: 0,\n      overallConsistency: 0,\n      passed: false,\n      issues: ['验证过程出错'],\n      suggestions: ['请检查输入数据的完整性']\n    }\n  }\n}\n\n// 文本一致性验证\nasync function validateTextConsistency(characterInfo: any, segmentInfo: any): Promise<number> {\n  try {\n    let score = 0.5 // 基础分数\n\n    // 检查角色DNA完整性\n    const dna = parseCharacterDNA(characterInfo)\n    if (dna) {\n      score += 0.2 // 有详细DNA加分\n\n      // 检查关键特征是否在提示词中体现\n      const prompt = segmentInfo.prompt || ''\n      const lowerPrompt = prompt.toLowerCase()\n\n      // 检查面部特征\n      if (lowerPrompt.includes(dna.facial.faceShape.toLowerCase())) score += 0.1\n      if (lowerPrompt.includes(dna.facial.eyeColor.toLowerCase())) score += 0.1\n\n      // 检查独特标识\n      for (const identifier of dna.uniqueIdentifiers) {\n        if (lowerPrompt.includes(identifier.toLowerCase())) {\n          score += 0.05\n        }\n      }\n    }\n\n    // 检查角色名称是否正确\n    if (characterInfo.name && segmentInfo.prompt?.includes(characterInfo.name)) {\n      score += 0.1\n    }\n\n    return Math.min(score, 1.0) // 最高1.0分\n\n  } catch (error) {\n    console.warn('文本一致性验证失败:', error)\n    return 0.3 // 默认低分\n  }\n}\n\n// 图像一致性验证（简化版）\nasync function validateImageConsistency(generatedImageUrl: string, characterInfo: any): Promise<number> {\n  try {\n    // 简化版：基于角色是否有参考图像来评分\n    if (!characterInfo.generatedImages) {\n      return 0.3 // 没有参考图像，低分\n    }\n\n    try {\n      const images = JSON.parse(characterInfo.generatedImages)\n      let score = 0.5 // 基础分数\n\n      // 检查参考图像的完整性\n      if (images.front) score += 0.2\n      if (images.side) score += 0.1\n      if (images.back) score += 0.1\n\n      // 如果有生成的图像URL，假设一致性较好\n      if (generatedImageUrl && generatedImageUrl.length > 0) {\n        score += 0.1\n      }\n\n      return Math.min(score, 1.0)\n\n    } catch (e) {\n      console.warn('解析角色参考图像失败:', e)\n      return 0.4\n    }\n\n  } catch (error) {\n    console.warn('图像一致性验证失败:', error)\n    return 0.3 // 默认低分\n  }\n}\n\n// 保存一致性验证记录\nasync function saveConsistencyValidation(\n  characterId: string,\n  episodeId: string,\n  result: ConsistencyResult,\n  generatedImageUrl?: string\n): Promise<void> {\n  try {\n    await prisma.consistencyValidation.create({\n      data: {\n        characterId,\n        episodeId,\n        textConsistencyScore: result.textConsistency,\n        imageConsistencyScore: result.imageConsistency,\n        overallScore: result.overallConsistency,\n        validationDetails: JSON.stringify({\n          passed: result.passed,\n          issues: result.issues,\n          suggestions: result.suggestions\n        }),\n        issuesFound: JSON.stringify(result.issues),\n        textWeight: 0.6,\n        imageWeight: 0.4,\n        generatedImageUrl: generatedImageUrl\n      }\n    })\n\n    console.log(`💾 已保存角色 ${characterId} 的一致性验证记录`)\n\n  } catch (error) {\n    console.error('保存一致性验证记录失败:', error)\n  }\n}\n\n// 获取视频实际时长\nasync function getVideoActualDuration(videoUrl: string): Promise<number> {\n  try {\n    console.log(`📏 尝试获取视频实际时长: ${videoUrl.substring(0, 100)}...`)\n\n    // 方法1: 尝试通过HEAD请求获取Content-Range或其他元数据\n    const headResponse = await fetch(videoUrl, {\n      method: 'HEAD',\n      headers: {\n        'Range': 'bytes=0-1' // 请求很小的范围来获取元数据\n      }\n    })\n\n    // 检查响应头中是否有时长信息\n    const contentRange = headResponse.headers.get('content-range')\n    const contentLength = headResponse.headers.get('content-length')\n\n    if (contentRange) {\n      console.log(`📋 Content-Range: ${contentRange}`)\n    }\n\n    if (contentLength) {\n      const fileSizeBytes = parseInt(contentLength)\n      console.log(`📋 文件大小: ${(fileSizeBytes / 1024 / 1024).toFixed(2)} MB`)\n\n      // 基于文件大小估算时长（粗略估算）\n      // 假设1MB约等于1秒的高质量视频\n      const estimatedDuration = Math.round(fileSizeBytes / (1024 * 1024))\n\n      if (estimatedDuration > 0 && estimatedDuration <= 60) {\n        console.log(`📏 基于文件大小估算时长: ${estimatedDuration}秒`)\n        return estimatedDuration\n      }\n    }\n\n    // 方法2: 如果无法从HTTP头获取，尝试下载一小部分文件分析\n    // 这里暂时返回默认值，避免下载整个文件\n    console.log('📏 无法从HTTP头获取时长信息，使用默认估算')\n\n    // 基于URL模式的启发式估算\n    if (videoUrl.includes('doubao')) {\n      // 豆包生成的视频通常是5-10秒\n      return 8 // 默认8秒\n    }\n\n    return 5 // 默认5秒\n\n  } catch (error) {\n    console.warn('获取视频实际时长失败:', error.message)\n    return 5 // 默认5秒\n  }\n}\n\n// 高级视频时长检测（可选，需要额外依赖）\nasync function getVideoActualDurationAdvanced(videoUrl: string): Promise<number> {\n  try {\n    // 这个函数可以在未来实现更高级的视频分析\n    // 例如使用ffprobe或其他视频分析工具\n    // 目前返回基础估算\n\n    console.log('📏 使用高级时长检测（暂未实现）')\n    return 5\n\n  } catch (error) {\n    console.warn('高级视频时长检测失败:', error.message)\n    return 5\n  }\n}\n\n// SkyReels-V2 API调用\nasync function callSkyReelsVideoAPI(config: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 开始SkyReels-V2视频生成，片段 ${segment.segmentIndex}`)\n    console.log(`📝 提示词: ${prompt.substring(0, 200)}...`)\n\n    // 导入SkyReels客户端\n    const { SkyReelsClient } = await import('@/lib/skyreels')\n\n    // 创建SkyReels客户端\n    const skyreelsClient = new SkyReelsClient({\n      id: 'skyreels-temp',\n      provider: 'skyreels' as any,\n      apiKey: config.apiKey, // 这里是SkyReels API的baseUrl\n      model: config.model,\n      temperature: 0.7,\n      maxTokens: 4000,\n      topP: 0.9,\n      status: 'connected' as any,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n\n    // 计算视频参数\n    const duration = Math.min(segment.duration || 10, 30) // SkyReels支持最长30秒\n    const numFrames = duration <= 4 ? 97 : (duration <= 10 ? 257 : (duration <= 15 ? 377 : 737))\n\n    console.log(`⏱️ 视频时长: ${duration}秒, 帧数: ${numFrames}`)\n\n    // 生成视频\n    const videoPath = await skyreelsClient.generateAndWait({\n      prompt: prompt,\n      num_frames: numFrames,\n      guidance_scale: 6.0,\n      fps: 24,\n      resolution: '540P'\n    }, 1800000, 5000) // 30分钟超时，5秒轮询间隔\n\n    if (!videoPath) {\n      throw new Error('SkyReels视频生成失败：未返回视频路径')\n    }\n\n    console.log(`✅ SkyReels视频生成完成: ${videoPath}`)\n\n    // 更新片段状态\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: {\n        videoUrl: videoPath,\n        status: 'completed',\n        duration: duration,\n        metadata: JSON.stringify({\n          provider: 'skyreels',\n          model: config.model,\n          completedAt: new Date().toISOString(),\n          numFrames: numFrames,\n          guidanceScale: 6.0,\n          fps: 24,\n          resolution: '540P',\n          localGeneration: true\n        })\n      }\n    })\n\n    console.log(`💾 片段 ${segment.segmentIndex} SkyReels视频生成记录已更新`)\n\n    return {\n      videoUrl: videoPath,\n      duration: duration,\n      status: 'completed',\n      provider: 'skyreels'\n    }\n\n  } catch (error) {\n    console.error(`❌ SkyReels视频生成失败，片段 ${segment.segmentIndex}:`, error)\n\n    // 更新片段状态为失败\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: {\n        status: 'failed',\n        metadata: JSON.stringify({\n          provider: 'skyreels',\n          error: error.message,\n          failedAt: new Date().toISOString()\n        })\n      }\n    })\n\n    throw error\n  }\n}\n\n// 验证图像URL是否有效\nasync function validateImageUrl(imageUrl: string): Promise<boolean> {\n  try {\n    if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {\n      return false\n    }\n\n    // 检查URL格式\n    try {\n      new URL(imageUrl)\n    } catch (e) {\n      console.warn('图像URL格式无效:', imageUrl)\n      return false\n    }\n\n    // 发送HEAD请求检查图像是否存在\n    const response = await fetch(imageUrl, {\n      method: 'HEAD',\n      timeout: 10000 // 10秒超时\n    })\n\n    if (!response.ok) {\n      console.warn(`图像URL返回错误状态: ${response.status}`)\n      return false\n    }\n\n    // 检查内容类型是否为图像\n    const contentType = response.headers.get('content-type')\n    if (!contentType || !contentType.startsWith('image/')) {\n      console.warn(`URL内容类型不是图像: ${contentType}`)\n      return false\n    }\n\n    return true\n  } catch (error) {\n    console.warn('验证图像URL失败:', error.message)\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAQA;AACA;;;;;;;AAEA,SAAS;AACT,SAAS,sBAAsB,MAAc;IAC3C,SAAS;IACT,MAAM,iBAAiB;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAC3D,MAAM,iBAAiB,eAAe,IAAI,CAAC,CAAA,OAAQ,OAAO,QAAQ,CAAC;IACnE,IAAI,gBAAgB;QAClB,OAAO;YAAE,SAAS;YAAO,QAAQ,CAAC,QAAQ,EAAE,gBAAgB;QAAC;IAC/D;IAEA,OAAO;IACP,IAAI,OAAO,MAAM,GAAG,KAAK;QACvB,OAAO;YAAE,SAAS;YAAO,QAAQ;QAAkB;IACrD;IAEA,SAAS;IACT,IAAI,CAAC,OAAO,IAAI,IAAI;QAClB,OAAO;YAAE,SAAS;YAAO,QAAQ;QAAU;IAC7C;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,QAAQ;AACR,SAAS,eAAe,MAAc;IACpC,OAAO,OACJ,OAAO,CAAC,+CAA+C,IAAI,kBAAkB;KAC7E,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,SAAS,CAAC,GAAG,KAAK,aAAa;KAC/B,IAAI;AACT;AAEA,eAAe;AACf,SAAS,4BAA4B,SAAc;IACjD,MAAM,OAAO,UAAU,IAAI,IAAI;IAE/B,aAAa;IACb,MAAM,MAAM,kBAAkB;IAC9B,IAAI,CAAC,KAAK;QACR,OAAO,CAAC,MAAM,EAAE,KAAK,iBAAiB,CAAC;IACzC;IAEA,aAAa;IACb,OAAO,CAAC,SAAS,EAAE,KAAK;;KAErB,EAAE,KAAK;KACP,EAAE,UAAU,QAAQ,IAAI,KAAK;KAC7B,EAAE,UAAU,WAAW,IAAI,KAAK;;;KAGhC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC;KACvB,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC;KAChD,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC;KACvB,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC;KACxB,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC;OACpB,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC;;;KAG9B,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC;KACtB,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrB,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC;;;KAGvB,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrB,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;KAChC,EAAE,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,IAAI;;;AAGjD,EAAE,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAAA,KAAM,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;;;AAGxD,EAAE,IAAI,cAAc,CAAC;;;;;;cAMP,CAAC;AACf;AAEA,YAAY;AACZ,SAAS,eAAe,IAAY,EAAE,OAAe,EAAE,YAAoB;IACzE,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE;IAC/C,MAAM,QAAQ,KAAK,KAAK,CAAC;IAEzB,IAAI,OAAO;QACT,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,0EAA0E,IAAI,IAAI;IAC5G;IAEA,OAAO;AACT;AAEA,SAAS;AACT,SAAS,sBAAsB,MAAc,EAAE,QAAgB;IAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,WAAW;IAChD,MAAM,iBAAiB,EAAE;IAEzB,WAAW;IACX,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,eAAe,IAAI,CAAC;IAClE,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,OAAO,eAAe,IAAI,CAAC;IACnE,IAAI,KAAK,QAAQ,CAAC,OAAO,eAAe,IAAI,CAAC;IAC7C,IAAI,KAAK,QAAQ,CAAC,OAAO,eAAe,IAAI,CAAC;IAC7C,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,eAAe,IAAI,CAAC;IACpE,IAAI,KAAK,QAAQ,CAAC,OAAO,eAAe,IAAI,CAAC;IAE7C,OAAO,eAAe,MAAM,GAAG,IAAI,eAAe,IAAI,CAAC,OAAO;AAChE;AAEA,WAAW;AACX,SAAS,sBACP,WAAmB,EACnB,YAAoB,EACpB,gBAAwB,EACxB,WAAmB,EACnB,QAAiB;IAEjB,MAAM,eAAe,WAAW,GAAG,SAAS,CAAC,CAAC,GAAG;IAEjD,OAAO,CAAC;;;AAGV,EAAE,YAAY;;AAEd,EAAE,aAAa;;;OAGR,EAAE,iBAAiB;OACnB,EAAE,YAAY;OACd,EAAE,aAAa;;;;;IAKlB,EAAE,aAAa;;;;;;;;;WASR,EAAE,aAAa;;;;;;;;;cASZ,CAAC;AACf;AAEA,YAAY;AACZ,SAAS,kBAAkB,SAAc;IACvC,IAAI;QACF,IAAI,UAAU,WAAW,EAAE;YACzB,OAAO,KAAK,KAAK,CAAC,UAAU,WAAW;QACzC;QAEA,oBAAoB;QACpB,OAAO,iBAAiB;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC/C,OAAO,iBAAiB;IAC1B;AACF;AAEA,aAAa;AACb,SAAS,iBAAiB,SAAc;IACtC,OAAO;QACL,QAAQ;YACN,WAAW,eAAe,UAAU,MAAM,EAAE,MAAM;YAClD,UAAU,eAAe,UAAU,MAAM,EAAE,KAAK;YAChD,UAAU,eAAe,UAAU,MAAM,EAAE,SAAS;YACpD,WAAW,eAAe,UAAU,MAAM,EAAE,KAAK;YACjD,YAAY,eAAe,UAAU,MAAM,EAAE,OAAO;YACpD,UAAU,eAAe,UAAU,MAAM,EAAE,SAAS;YACpD,gBAAgB,sBAAsB,UAAU,MAAM,EAAE,UAAU,QAAQ;QAC5E;QACA,UAAU;YACR,QAAQ,eAAe,UAAU,QAAQ,EAAE,MAAM;YACjD,OAAO,eAAe,UAAU,QAAQ,EAAE,SAAS;YACnD,SAAS,eAAe,UAAU,QAAQ,EAAE,SAAS;QACvD;QACA,UAAU;YACR,OAAO,eAAe,UAAU,QAAQ,EAAE,SAAS;YACnD,QAAQ,cAAc,UAAU,QAAQ;YACxC,aAAa,mBAAmB,UAAU,QAAQ;QACpD;QACA,mBAAmB,yBAAyB;QAC5C,gBAAgB,uBAAuB;IACzC;AACF;AAEA,SAAS;AACT,SAAS,cAAc,QAAgB;IACrC,IAAI,CAAC,UAAU,OAAO;QAAC;KAAM;IAE7B,MAAM,SAAS,EAAE;IACjB,MAAM,WAAW;QACf,KAAK;QAAM,KAAK;QAAM,KAAK;QAAM,KAAK;QACtC,KAAK;QAAM,KAAK;QAAM,KAAK;QAAM,KAAK;IACxC;IAEA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;QACnD,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO,OAAO,MAAM,GAAG,IAAI,SAAS;QAAC;KAAM;AAC7C;AAEA,SAAS;AACT,SAAS,mBAAmB,QAAgB;IAC1C,IAAI,CAAC,UAAU,OAAO,EAAE;IAExB,MAAM,cAAc,EAAE;IACtB,MAAM,eAAe;QACnB,KAAK;QAAM,MAAM;QAAM,MAAM;QAAM,MAAM;QACzC,MAAM;QAAM,MAAM;QAAM,MAAM;QAAM,MAAM;IAC5C;IAEA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,cAAe;QACvD,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,OAAO;AACT;AAEA,UAAU;AACV,SAAS,yBAAyB,SAAc;IAC9C,MAAM,cAAc,EAAE;IACtB,MAAM,OAAO,GAAG,UAAU,MAAM,IAAI,GAAG,CAAC,EAAE,UAAU,QAAQ,IAAI,GAAG,CAAC,EAAE,UAAU,WAAW,IAAI,IAAI,CAAC,WAAW;IAE/G,SAAS;IACT,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAChE,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAChE,IAAI,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAC1C,IAAI,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAC1C,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IACjE,IAAI,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAE1C,gBAAgB;IAChB,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IACjE,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IACjE,IAAI,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,YAAY,IAAI,CAAC;IAEjE,OAAO,YAAY,MAAM,GAAG,IAAI,cAAc;QAAC;KAAU;AAC3D;AAEA,UAAU;AACV,SAAS,uBAAuB,SAAc;IAC5C,MAAM,OAAO,UAAU,IAAI,IAAI;IAC/B,MAAM,SAAS,UAAU,MAAM,IAAI;IACnC,MAAM,WAAW,UAAU,QAAQ,IAAI;IACvC,MAAM,WAAW,UAAU,QAAQ,IAAI;IAEvC,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,kBAAkB,CAAC;AACtE;AAEA,YAAY;AACZ,eAAe,qBAAqB,QAAgB,EAAE,SAAiB,EAAE,YAAoB;IAC3F,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,UAAU;QAEpC,SAAS;QACT,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,aAAa;QACpE,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,cAAc;YAC/B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,aAAa;gBAAE,WAAW;YAAK;QAC9C;QAEA,QAAQ;QACR,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,aAAa,OAAO,CAAC,8BAA8B,KAAK,IAAI,CAAC;QAC9F,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,aAAa;QACxC,MAAM,aAAa,CAAC,kBAAkB,EAAE,UAAU;QAElD,OAAO;QACP,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YACjE,OAAO;QACT;QAEA,QAAQ;QACR,MAAM,SAAS,MAAM,SAAS,WAAW;QACzC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU,OAAO,IAAI,CAAC;QAEvC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY;QACrC,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAA,UAAS,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,iBAAiB,QAAQ,EAAE,GAAG;QAE7F,IAAI,CAAC,cAAa,CAAC,UAAU,CAAC,WAAW;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,iBAAiB,MAAM,iCAAiC;QAE9D,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,QAAQ,GAAG,CAAC,mBAAmB,eAAe,KAAK;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO,eAAe,KAAK;gBAC3B,mBAAmB,eAAe,iBAAiB;gBACnD,iBAAiB;YACnB,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,gBAAgB;YAEtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT;QACF;QAEA,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAU,cAAc,CAAC;QAEhD,IAAI,iBAAiB;QAErB,iBAAiB;QACjB,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC/B,MAAM,sBAAsB,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC;gBACvD,OAAO;oBAAE,WAAA;gBAAU;gBACnB,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,IAAI,oBAAoB,MAAM,GAAG,GAAG;gBAClC,iBAAiB;gBACjB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,oBAAoB,MAAM,CAAC,qBAAqB,CAAC;gBAEzE,IAAI,uBAAuB;gBAE3B,KAAK,MAAM,iBAAiB,oBAAqB;oBAC/C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE,EAAE;oBAC9C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc,QAAQ,CAAC,MAAM,EAAE;oBAE3D,cAAc;oBACd,MAAM,kBAAkB,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;wBACvD,OAAO;4BAAE,cAAc,cAAc,EAAE;wBAAC;oBAC1C;oBACA,wBAAwB,gBAAgB,KAAK;oBAC7C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,gBAAgB,KAAK,CAAC,MAAM,CAAC;oBAErD,UAAU;oBACV,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;wBACzB,OAAO;4BAAE,IAAI,cAAc,EAAE;wBAAC;oBAChC;oBACA,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC;gBAC7B;gBAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,oBAAoB,MAAM,CAAC,QAAQ,EAAE,qBAAqB,WAAW,CAAC;YAC7F,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC;YACnC;QACF;QAEA,SAAS;QACT,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,WAAA;YAAU;QACrB;QAEA,IAAI,CAAC,YAAY,CAAC,SAAS,eAAe,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAa,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,cAAc,MAAM,4BAA4B,UAAU,YAAW,WAAW,UAAU,gBAAgB,SAAS;QACzH,MAAM,UAAU,iBACZ,CAAC,aAAa,EAAE,YAAY,aAAa,CAAC,GAAG,CAAC,GAC9C,CAAC,aAAa,EAAE,YAAY,aAAa,CAAC,GAAG,CAAC;QAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS,YAAY,YAAY;gBACjC,UAAU,YAAY,QAAQ;gBAC9B,eAAe,YAAY,aAAa;gBACxC,QAAQ;gBACR;YACF;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,eAAe,4BAA4B,QAAa,EAAE,UAAiB,EAAE,SAAiB,EAAE,QAAa,EAAE,iBAA0B,KAAK,EAAE,OAAgB,EAAE,iBAAoC,QAAQ;IAC5M,IAAI;QACF,MAAM,iBAAiB,IAAI,kHAAA,CAAA,iBAAc,CAAC;QAE1C,yBAAyB;QACzB,MAAM,WAAW,MAAM,gCAAgC,gBAAgB,UAAU;QAEjF,aAAa;QACb,MAAM,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAChD,MAAM;gBACJ,WAAA;gBACA;gBACA,QAAQ,SAAS,eAAe,IAAI;gBACpC,QAAQ;gBACR,UAAU,KAAK,SAAS,CAAC;oBACvB,eAAe,SAAS,MAAM;oBAC9B,aAAa,IAAI,OAAO,WAAW;oBACnC,qBAAqB;oBACrB;oBACA,eAAe,iBAAiB,IAAI,OAAO,WAAW,KAAK;gBAC7D;YACF;QACF;QAEA,cAAc;QACd,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,SAAS,GAAG,CAAC,CAAC,SAAS,QACrB,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACzB,MAAM;oBACJ,cAAc,WAAW,EAAE;oBAC3B,WAAA;oBACA;oBACA,cAAc,QAAQ;oBACtB,OAAO,QAAQ,KAAK;oBACpB,aAAa,QAAQ,WAAW;oBAChC,QAAQ,QAAQ,MAAM;oBACtB,aAAa,QAAQ,IAAI;oBACzB,UAAU,QAAQ,QAAQ,IAAI;oBAC9B,QAAQ;oBACR,UAAU,KAAK,SAAS,CAAC,QAAQ,QAAQ,IAAI,CAAC;gBAChD;YACF;QAIJ,sBAAsB;QACtB,IAAI,mBAAmB,QAAQ;YAC7B,kBAAkB;YAClB,sBAAsB,UAAU,WAAW,EAAE,EAAE,eAAe;QAChE;QACA,sBAAsB;QAEtB,OAAO;YACL,cAAc,WAAW,EAAE;YAC3B,UAAU,cAAc,GAAG,CAAC,CAAA,MAAO,CAAC;oBAClC,IAAI,IAAI,EAAE;oBACV,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,WAAW;oBAC5B,cAAc,IAAI,YAAY;oBAC9B,QAAQ,IAAI,MAAM;oBAClB,aAAa,IAAI,WAAW;gBAC9B,CAAC;YACD,eAAe,SAAS,MAAM;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,kHAAA,CAAA,iBAAc,CAAC,YAAY;IACvC;AACF;AAEA,iBAAiB;AACjB,eAAe,gCAAgC,cAA8B,EAAE,QAAa,EAAE,UAAkB;IAC9G,IAAI;QACF,cAAc;QACd,MAAM,qBAAqB,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,SAAS,kBAAkB,IAAI,EAAE;QACrG,MAAM,iBAAiB,SAAS,cAAc,GAAG,KAAK,KAAK,CAAC,SAAS,cAAc,IAAI,EAAE;QACzF,MAAM,gBAAgB,SAAS,aAAa,GAAG,KAAK,KAAK,CAAC,SAAS,aAAa,IAAI,EAAE;QACtF,MAAM,eAAe,SAAS,YAAY,IAAI;QAC9C,MAAM,kBAAkB,SAAS,eAAe,IAAI;QAEpD,qBAAqB;QACrB,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;YAC7C,MAAM,cAAc,EAAE;YAEtB,KAAK,MAAM,YAAY,cAAe;gBACpC,MAAM,mBAAmB,cAAc,SAAS,QAAQ,KAAK;gBAC7D,MAAM,kBAAkB,EAAE,eAAe;;gBACzC,MAAM,eAAe,KAAK,IAAI,CAAC,mBAAmB;gBAElD,YAAY;gBACZ,MAAM,cAAc,oCAAoC,UAAU,cAAc,oBAAoB;gBACpG,YAAY,IAAI,IAAI;YACtB;YAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,cAAc,MAAM,CAAC,QAAQ,EAAE,YAAY,MAAM,CAAC,KAAK,CAAC;YAEhF,OAAO;QACT;QAEA,0BAA0B;QAC1B,MAAM,gBAAgB,CAAC;;;AAG3B,EAAE,KAAK,SAAS,CAAC,oBAAoB,MAAM,GAAG;;;AAG9C,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;AAG1C,EAAE,aAAa;;;AAGf,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA4BL,CAAC;QAEV,MAAM,WAAW,MAAM,eAAe,OAAO,CAAC,eAAe;QAE7D,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,OAAO,OAAO,QAAQ,IAAI,EAAE;IAE9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,gBAAgB;QAChB,OAAO,MAAM,uBAAuB,UAAU;IAChD;AACF;AAEA,cAAc;AACd,SAAS,oCAAoC,QAAa,EAAE,YAAoB,EAAE,kBAAyB,EAAE,cAAqB;IAChI,MAAM,WAAW,EAAE;IACnB,MAAM,aAAa,SAAS,MAAM,IAAI;IACtC,MAAM,cAAc,SAAS,OAAO,IAAI;IACxC,MAAM,aAAa,SAAS,UAAU,IAAI,EAAE;IAE5C,eAAe;IACf,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,OAAO;QACvF,UAAU;QACV,MAAM,SAAS;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,MAAM,QAAQ,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACpD,SAAS,IAAI,CAAC;gBACZ,OAAO,GAAG,WAAW,GAAG,EAAE,OAAO;gBACjC,aAAa,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;gBACvC,QAAQ,oBAAoB;oBAC1B,GAAG,QAAQ;oBACX,QAAQ,GAAG,WAAW,CAAC,EAAE,MAAM,GAAG,EAAE,oBAAoB,YAAY,OAAO,GAAG,eAAe;oBAC7F,gBAAgB,SAAS,cAAc;gBACzC,GAAG,oBAAoB;gBACvB,MAAM,qBAAqB;gBAC3B,UAAU;gBACV,UAAU;oBACR,YAAY,8BAA8B,UAAU;oBACpD,UAAU,4BAA4B,UAAU;oBAChD,MAAM;oBACN,aAAa,SAAS,cAAc,IAAI;oBACxC,YAAY,SAAS,UAAU;oBAC/B,cAAc,IAAI;oBAClB,eAAe;oBACf,OAAO;gBACT;YACF;QACF;IACF,OAAO,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,MAAM;QAC7F,UAAU;QACV,MAAM,SAAS;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,MAAM,QAAQ,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACpD,SAAS,IAAI,CAAC;gBACZ,OAAO,GAAG,WAAW,GAAG,EAAE,OAAO;gBACjC,aAAa,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;gBACvC,QAAQ,oBAAoB;oBAC1B,GAAG,QAAQ;oBACX,QAAQ,GAAG,WAAW,CAAC,EAAE,MAAM,GAAG,EAAE,oBAAoB,YAAY,OAAO,GAAG,eAAe;oBAC7F,gBAAgB,SAAS,cAAc;gBACzC,GAAG,oBAAoB;gBACvB,MAAM,qBAAqB;gBAC3B,UAAU;gBACV,UAAU;oBACR,YAAY,8BAA8B,UAAU;oBACpD,UAAU,4BAA4B,UAAU;oBAChD,MAAM;oBACN,aAAa,SAAS,cAAc,IAAI;oBACxC,YAAY,SAAS,UAAU;oBAC/B,cAAc,IAAI;oBAClB,eAAe;oBACf,OAAO;gBACT;YACF;QACF;IACF,OAAO;QACL,cAAc;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI;YAC3B,MAAM,YAAY,YAAY,OAAO,OACpB,YAAY,MAAM,OAClB,YAAY,OAAO,OAAO;YAE3C,SAAS,IAAI,CAAC;gBACZ,OAAO,GAAG,WAAW,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;gBACpC,aAAa,GAAG,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC;gBAC3C,QAAQ,oBAAoB;oBAC1B,GAAG,QAAQ;oBACX,QAAQ,GAAG,WAAW,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,CAAC;oBACnD,gBAAgB,SAAS,cAAc;gBACzC,GAAG,oBAAoB;gBACvB,MAAM,qBAAqB;gBAC3B,UAAU;gBACV,UAAU;oBACR,YAAY,8BAA8B,UAAU;oBACpD,UAAU,4BAA4B,UAAU;oBAChD,MAAM;oBACN,aAAa,SAAS,cAAc,IAAI;oBACxC,YAAY,SAAS,UAAU;oBAC/B,cAAc,IAAI;oBAClB,eAAe;oBACf,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS;AACT,SAAS,oBAAoB,UAAkB,EAAE,KAAa,EAAE,KAAa,EAAE,KAAa;IAC1F,MAAM,YAA0D;QAC9D,MAAM;YACJ,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;QACA,MAAM;YACJ,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,YAAY;IACZ,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,OAAO,OAAO,CAAC,WAAY;QACxD,IAAI,WAAW,QAAQ,CAAC,SAAS;YAC/B,OAAO,MAAM,CAAC,MAAM,IAAI,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;QACpD;IACF;IAEA,OAAO,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;AACnC;AAEA,0BAA0B;AAC1B,eAAe,sBAAsB,QAAa,EAAE,YAAoB,EAAE,QAAe,EAAE,OAAgB;IACzG,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC,QAAQ,CAAC;IAE9C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,MAAM,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;QAE1E,IAAI;YACF,aAAa;YACb,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/B,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,MAAM;oBACJ,QAAQ;oBACR,UAAU,KAAK,SAAS,CAAC;wBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;wBACvC,WAAW,IAAI,OAAO,WAAW;wBACjC,cAAc,IAAI;wBAClB,eAAe,SAAS,MAAM;oBAChC;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa,CAAC;YAEzC,eAAe;YACf,MAAM,cAAc,MAAM,sBAAsB,UAAU,SAAS;YAEnE,eAAe;YACf,MAAM,aAAkB;gBACtB,UAAU,YAAY,QAAQ;gBAC9B,UAAU,KAAK,SAAS,CAAC;oBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,WAAW,IAAI,OAAO,WAAW;oBACjC,aAAa,IAAI,OAAO,WAAW;oBACnC,iBAAiB,YAAY,eAAe;oBAC5C,cAAc,IAAI;oBAClB,eAAe,SAAS,MAAM;gBAChC;YACF;YAEA,iBAAiB;YACjB,IAAI,YAAY,MAAM,KAAK,eAAe,YAAY,QAAQ,EAAE;gBAC9D,WAAW,QAAQ,GAAG,YAAY,QAAQ;gBAC1C,WAAW,YAAY,GAAG,YAAY,YAAY;gBAClD,WAAW,MAAM,GAAG;gBACpB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE;gBAElD,UAAU;gBACV,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI;oBAChD,MAAM,iBAAiB,SAAS,UAAU,IAAI,EAAE;oBAEhD,KAAK,MAAM,iBAAiB,eAAgB;wBAC1C,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,SAAS,CAAC;4BACjD,OAAO;gCAAE,MAAM;4BAAc;wBAC/B;wBAEA,IAAI,WAAW;4BACb,MAAM,oBAAoB,MAAM,yBAC9B,YAAY,QAAQ,EACpB,WACA;4BAGF,MAAM,0BACJ,UAAU,EAAE,EACZ,WACA,mBACA,YAAY,QAAQ;4BAGtB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,QAAQ,EAAE,kBAAkB,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,MAAM,GAAG,OAAO,MAAM,CAAC,CAAC;wBAC7I;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,oBAAoB;gBACnC;YACF,OAAO,IAAI,YAAY,MAAM,KAAK,cAAc;gBAC9C,WAAW,MAAM,GAAG;gBACpB,WAAW,QAAQ,GAAG,KAAK,SAAS,CAAC;oBACnC,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,QAAQ,YAAY,MAAM;oBAC1B,WAAW,IAAI,OAAO,WAAW;oBACjC,aAAa,IAAI,OAAO,WAAW;oBACnC,iBAAiB,YAAY,eAAe;oBAC5C,cAAc,IAAI;oBAClB,eAAe,SAAS,MAAM;gBAChC;gBACA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,KAAK,EAAE;gBAE3D,uBAAuB;gBACvB,IAAI,YAAY,MAAM,EAAE;oBACtB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC;oBACrC,MAAM,yBAAyB,QAAQ,EAAE,EAAE,YAAY,MAAM,EAAE,QAAQ,WAAW;;gBACpF;YACF,OAAO,IAAI,YAAY,MAAM,KAAK,UAAU;gBAC1C,WAAW,MAAM,GAAG;gBACpB,WAAW,QAAQ,GAAG,KAAK,SAAS,CAAC;oBACnC,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,OAAO,YAAY,KAAK;oBACxB,WAAW,IAAI,OAAO,WAAW;oBACjC,aAAa,IAAI,OAAO,WAAW;oBACnC,iBAAiB,YAAY,eAAe;oBAC5C,cAAc,IAAI;oBAClB,eAAe,SAAS,MAAM;gBAChC;gBACA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,KAAK,EAAE;YACxD,OAAO;gBACL,WAAW,MAAM,GAAG;gBACpB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;YAClC;YAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/B,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,MAAM;YACR;YAEA,uBAAuB;YACvB,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,CAAC;gBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE;YAErC,YAAY;YACZ,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/B,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,MAAM;oBACJ,QAAQ;oBACR,UAAU,KAAK,SAAS,CAAC;wBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;wBACvC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAChD,UAAU,IAAI,OAAO,WAAW;wBAChC,cAAc,IAAI;wBAClB,eAAe,SAAS,MAAM;oBAChC;gBACF;YACF;YAEA,oBAAoB;YACpB,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,CAAC;gBACvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;QAC3D,OAAO;YACL;YACA,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,iBAAiB,kBAAkB,MAAM,KAAK;IAEpD,MAAM,kHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QAC7B,OAAO;YAAE,IAAI;QAAa;QAC1B,MAAM;YACJ,QAAQ,iBAAiB,cAAc;YACvC,UAAU,KAAK,SAAS,CAAC;gBACvB;gBACA,mBAAmB,kBAAkB,MAAM;gBAC3C,aAAa,iBAAiB,IAAI,OAAO,WAAW,KAAK;YAC3D;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,kBAAkB,MAAM,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC;AAC3E;AAEA,iCAAiC;AACjC,SAAS,oBAAoB,QAAa,EAAE,UAAiB,EAAE,MAAa;IAC1E,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,MAAM,UAAU,SAAS,OAAO,IAAI;IACpC,MAAM,iBAAiB,SAAS,cAAc,IAAI;IAElD,WAAW;IACX,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,OAC3C,OAAO,QAAQ,CAAC,KAAK,IAAI,KAAM,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,CAAC,CAAC,SAAmB,OAAO,QAAQ,CAAC,KAAK,IAAI;IAG9H,WAAW;IACX,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QACnC,OAAO,QAAQ,CAAC,MAAM,QAAQ,KAAK,eAAe,QAAQ,CAAC,MAAM,QAAQ;IAG3E,cAAc;IACd,OAAO,4BAA4B;QACjC;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,aAAa;AACb,SAAS,4BAA4B,OAOpC;IACC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,cAAc,EAAE,GAAG;IAEhF,MAAM,WAAqB,EAAE;IAE7B,YAAY;IACZ,SAAS,IAAI,CAAC,GAAG,QAAQ;IAEzB,iBAAiB;IACjB,IAAI,mBAAmB,MAAM,GAAG,GAAG;QACjC,MAAM,uBAAuB,mBAAmB,GAAG,CAAC,CAAA;YAClD,MAAM,WAAW,EAAE;YAEnB,SAAS;YACT,IAAI,KAAK,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;YAC9C,IAAI,KAAK,UAAU,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE;YAC1D,IAAI,KAAK,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;YAClD,IAAI,KAAK,SAAS,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;YACxD,IAAI,KAAK,QAAQ,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;YACtD,IAAI,KAAK,QAAQ,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;YAEtD,UAAU;YACV,SAAS,IAAI,CAAC;YAEd,OAAO,SAAS,IAAI,CAAC;QACvB,GAAG,IAAI,CAAC;QAER,SAAS,IAAI,CAAC;IAChB;IAEA,UAAU;IACV,IAAI,eAAe,MAAM,GAAG,GAAG;QAC7B,MAAM,YAAY,cAAc,CAAC,EAAE;QACnC,MAAM,gBAAgB,EAAE;QACxB,IAAI,UAAU,WAAW,EAAE,cAAc,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,WAAW,EAAE;QAC3E,IAAI,UAAU,UAAU,EAAE,cAAc,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,UAAU,EAAE;QACzE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,SAAS,IAAI,CAAC,cAAc,IAAI,CAAC;QACnC;IACF;IAEA,aAAa;IACb,IAAI,SAAS,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,SAAS;IAC1C,IAAI,gBAAgB,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,gBAAgB;IAExD,UAAU;IACV,SAAS,IAAI,CAAC;IAEd,OAAO,SAAS,IAAI,CAAC,OAAO;AAC9B;AAEA,cAAc;AACd,SAAS,qBAAqB,QAAa;IACzC,MAAM,SAAS,CAAC,SAAS,MAAM,IAAI,EAAE,EAAE,WAAW;IAClD,MAAM,iBAAiB,CAAC,SAAS,cAAc,IAAI,EAAE,EAAE,WAAW;IAElE,IAAI,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,OAAO;QAC3E,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,OAAO;QAClF,OAAO;IACT,OAAO,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,OAAO;QAC1F,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,YAAY;AACZ,SAAS,cAAc,QAAa;IAClC,IAAI,OAAO,aAAa,UAAU,OAAO;IACzC,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,OAAO,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;IACtC;IACA,OAAO;AACT;AAEA,gBAAgB;AAChB,SAAS,8BAA8B,QAAa,EAAE,UAAiB;IACrE,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,MAAM,aAAa,SAAS,UAAU,IAAI,EAAE;IAE5C,OAAO,WACJ,MAAM,CAAC,CAAA,OAAQ,OAAO,QAAQ,CAAC,KAAK,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,SAAmB,OAAO,QAAQ,CAAC,KAAK,IAAI,IAC1G,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AAC1B;AAEA,gBAAgB;AAChB,SAAS,4BAA4B,QAAa,EAAE,MAAa;IAC/D,MAAM,SAAS,SAAS,MAAM,IAAI;IAClC,MAAM,iBAAiB,SAAS,cAAc,IAAI;IAElD,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,QAChC,OAAO,QAAQ,CAAC,MAAM,QAAQ,KAAK,eAAe,QAAQ,CAAC,MAAM,QAAQ;IAG3E,OAAO,gBAAgB,cAAc,QAAQ,GAAG;AAClD;AAEA,sBAAsB;AACtB,eAAe,uBAAuB,QAAa,EAAE,UAAkB;IACrE,MAAM,kBAAkB,SAAS,eAAe,IAAI;IAEpD,aAAa;IACb,IAAI,oBAAoB,EAAE;IAC1B,IAAI;QACF,MAAM,kBAAkB,cAAa,SAAS,SAAS;QACvD,IAAI,iBAAiB;YACnB,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI;gBAAgB;gBAC7B,SAAS;oBAAE,SAAS;gBAAK;YAC3B;YAEA,IAAI,SAAS,SAAS,IAAI;gBACxB,oBAAoB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAClD,OAAO;wBAAE,WAAW,QAAQ,OAAO,CAAC,EAAE;oBAAC;gBACzC;gBACA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,kBAAkB,MAAM,CAAC,MAAM,CAAC;YACvD;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,aAAa;IAC5B;IAEA,eAAe;IACf,MAAM,wBAAwB,kBAAkB,GAAG,CAAC,CAAA;QAClD,OAAO,4BAA4B;IACrC,GAAG,IAAI,CAAC;IAER,IAAI,gBAAgB,MAAM,GAAG,KAAK;QAChC,0BAA0B;QAC1B,MAAM,SAAS,gBAAgB,MAAM;QACrC,MAAM,gBAAgB,KAAK,KAAK,CAAC,SAAS;QAE1C,OAAO;YACL;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ,sBACN,gBAAgB,SAAS,CAAC,GAAG,gBAC7B,uBACA,iBACA,aACA;gBAEF,MAAM;gBACN,UAAU;gBACV,UAAU;oBACR,YAAY,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBAC7C,UAAU;oBACV,MAAM;oBACN,aAAa;gBACf;YACF;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ,sBACN,gBAAgB,SAAS,CAAC,eAAe,gBAAgB,IACzD,uBACA,eACA,eACA;gBAEF,MAAM;gBACN,UAAU;gBACV,UAAU;oBACR,YAAY,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBAC7C,UAAU;oBACV,MAAM;oBACN,aAAa;gBACf;YACF;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,QAAQ,sBACN,gBAAgB,SAAS,CAAC,gBAAgB,IAC1C,uBACA,eACA,YACA;gBAEF,MAAM;gBACN,UAAU;gBACV,UAAU;oBACR,YAAY,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBAC7C,UAAU;oBACV,MAAM;oBACN,aAAa;gBACf;YACF;SACD;IACH;IAEA,iBAAiB;IACjB,OAAO;QACL;YACE,OAAO;YACP,aAAa;YACb,QAAQ,sBACN,iBACA,uBACA,iBACA,eACA;YAEF,MAAM;YACN,UAAU;YACV,UAAU;gBACR,YAAY,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAC7C,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;QACF;KACD;AACH;AAEA,WAAW;AACX,eAAe,sBAAsB,QAAa,EAAE,OAAY,EAAE,OAAgB;IAChF,IAAI;QACF,yBAAyB;QACzB,MAAM,iBAAiB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,UAAU;gBACV,SAAS;YACX;QACF;QAEA,IAAI,kBAAkB,QAAQ,MAAM;QAEpC,IAAI,gBAAgB;YAClB,IAAI;gBACF,MAAM,iBAAiB,IAAI,kHAAA,CAAA,iBAAc,CAAC;gBAC1C,MAAM,qBAAqB,CAAC;;KAE/B,EAAE,QAAQ,KAAK,CAAC;KAChB,EAAE,QAAQ,WAAW,CAAC;MACrB,EAAE,QAAQ,MAAM,CAAC;;;;;;;KAOlB,EAAE,QAAQ,WAAW,CAAC;;QAEnB,CAAC;gBAED,kBAAkB,MAAM,eAAe,OAAO,CAAC,oBAAoB;gBACnE,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,gBAAgB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;YAC1E,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,4BAA4B,MAAM,OAAO;gBACtD,kBAAkB,QAAQ,MAAM;YAClC;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;YACb,kBAAkB,QAAQ,MAAM;QAClC;QAEA,eAAe;QACf,MAAM,cAAc,MAAM,uBAAuB,gBAAgB,IAAI,IAAI,SAAS;QAElF,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,cAAc,YAAY,YAAY;YACtC,iBAAiB,gBAAgB,IAAI;YACrC,UAAU,YAAY,QAAQ,IAAI,QAAQ,QAAQ,IAAI;YACtD,QAAQ,YAAY,MAAM,IAAI;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YACL,UAAU;YACV,cAAc;YACd,iBAAiB,QAAQ,MAAM;YAC/B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ;YACR,OAAO,MAAM,OAAO;QACtB;IACF;AACF;AAEA,YAAY;AACZ,eAAe,uBAAuB,MAAc,EAAE,OAAY,EAAE,OAAgB;IAClF,IAAI;QACF,uBAAuB;QACvB,MAAM,qBAAqB,QAAQ,QAAQ,EAAE,YAAY,KAAK,CAAC,OAC7D,KAAK,QAAQ,IAAI,KAAK,eAAe,EAAE,SAAS;QAGlD,kBAAkB;QAClB,IAAI;QAEJ,IAAI,SAAS;YACX,YAAY;YACZ,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC5C,OAAO;oBAAE,IAAI;gBAAQ;YACvB;YAEA,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS;YAC1C;YAEA,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,WAAW,IAAI,EAAE;YAChD;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC;YAC7D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,QAAQ,EAAE;YAE5C,UAAU;YACV,MAAM,qBAAqB,QAAQ,QAAQ,EAAE,YAAY,KAAK,CAAC,OAC7D,KAAK,QAAQ,IAAI,KAAK,eAAe,EAAE,SAAS;YAGlD,IAAI,oBAAoB;gBACtB,IAAI,WAAW,oBAAoB,EAAE;oBACnC,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,UAAU;YACV,IAAI,oBAAoB;gBACtB,IAAI,WAAW,oBAAoB,EAAE;oBACnC,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF;QAEF,OAAO;YACL,2BAA2B;YAC3B,MAAM,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjD,OAAO;oBACL,SAAS;oBACT,IAAI;wBACF;4BAAE,eAAe;wBAAK;wBACtB;4BAAE,sBAAsB;wBAAK;qBAC9B;gBACH;YACF;YAEA,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,IAAI,oIAAA,CAAA,qBAAkB,CAAC;YAC7C,MAAM,kBAAkB,MAAM,4BAA4B;YAC1D,MAAM,cAAc,oIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,SAAS;YAE/D,oBAAoB;YACpB,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,WAAW,YAAY,IAAI,CAAC,CAAA,QAChC,MAAM,QAAQ,KAAK,YACnB,MAAM,KAAK,KAAK,6BAChB,MAAM,oBAAoB;gBAG5B,IAAI,UAAU;oBACZ,aAAa;oBACb,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC;oBAC/D,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,gBAAgB,MAAM,EAAE;gBACpD,OAAO;oBACL,MAAM,iBAAiB,cAAc,eAAe,CAAC;oBACrD,aAAa,eAAe,aAAa;oBACzC,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC;oBAC/D,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,eAAe,MAAM,EAAE;gBACjD;YACF,OAAO;gBACL,MAAM,iBAAiB,cAAc,eAAe,CAAC;gBACrD,aAAa,eAAe,aAAa;gBACzC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,CAAC,CAAC,CAAC;gBAC/D,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,eAAe,MAAM,EAAE;YACjD;QACF;QAEA,sBAAsB;QACtB,IAAI,WAAW,QAAQ,KAAK,UAAU;YACpC,cAAc;YACd,IAAI,WAAW,KAAK,KAAK,2BAA2B;gBAClD,sCAAsC;gBACtC,OAAO,MAAM,0BAA0B,YAAY,QAAQ;YAC7D,OAAO,IAAI,WAAW,KAAK,KAAK,2BAA2B;gBACzD,sCAAsC;gBACtC,OAAO,MAAM,yBAAyB,YAAY,QAAQ;YAC5D,OAAO,IAAI,WAAW,KAAK,KAAK,2BAA2B;gBACzD,kCAAkC;gBAClC,OAAO,MAAM,mBAAmB,YAAY,QAAQ;YACtD,OAAO;gBACL,kBAAkB;gBAClB,OAAO,MAAM,mBAAmB,YAAY,QAAQ;YACtD;QACF,OAAO,IAAI,WAAW,QAAQ,KAAK,WAAW;YAC5C,OAAO,MAAM,qBAAqB,YAAY,QAAQ;QACxD,OAAO,IAAI,WAAW,QAAQ,KAAK,UAAU;YAC3C,OAAO,MAAM,mBAAmB,YAAY,QAAQ;QACtD,OAAO,IAAI,WAAW,QAAQ,KAAK,YAAY;YAC7C,OAAO,MAAM,qBAAqB,YAAY,QAAQ;QACxD;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,UAAU;YACV,cAAc;YACd,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM;IACR;AACF;AAEA,aAAa;AACb,eAAe,qBAAqB,MAAW,EAAE,MAAc,EAAE,OAAY;IAC3E,MAAM,cAAc,IAAI,iBAAiB;;IACzC,IAAI,eAAe,KAAK,WAAW;;IACnC,MAAM,kBAAkB,MAAM,UAAU;;IACxC,MAAM,oBAAoB,KAAK,SAAS;;IAExC,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,MAAM,YAAY,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,YAAY,GAAG,EAAE,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC;YAEvF,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,QAAQ,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,MAAM,eAAe,KAAK,GAAG,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,QAAQ,EAAE,aAAa,GAAG,CAAC;gBAC3F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,WAAW;gBACX,IAAI,eAAe,iBAAiB;oBAClC,eAAe,KAAK,GAAG,CAAC,eAAe,mBAAmB;gBAC5D;gBACA;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,aAAa,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM;YAE9E,IAAI,OAAO,MAAM,EAAE;gBACjB,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;gBAExC,IAAI,WAAW,aAAa;oBAC1B,aAAa;oBACb,wCAAwC;oBACxC,MAAM,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;oBACxE,MAAM,YAAY,OAAO,MAAM,CAAC,UAAU,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ;oBAClF,MAAM,WAAW,OAAO,MAAM,CAAC,cAAc,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,YAAY;oBAEzF,QAAQ,GAAG,CAAC,eAAe;wBACzB,WAAW,OAAO,MAAM,CAAC,SAAS;wBAClC,SAAS,OAAO,MAAM,CAAC,OAAO;wBAC9B,UAAU;oBACZ;oBAEA,IAAI,UAAU;wBACZ,MAAM,cAAc,IAAI,OAAO,WAAW;wBAE1C,gBAAgB;wBAChB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC;wBACjC,MAAM,iBAAiB,MAAM,qBAAqB,UAAU,QAAQ,EAAE,EAAE,QAAQ,KAAK;wBAErF,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;4BAC/B,OAAO;gCAAE,IAAI,QAAQ,EAAE;4BAAC;4BACxB,MAAM;gCACJ,UAAU,kBAAkB;gCAC5B,QAAQ;gCACR,UAAU;gCACV,UAAU,KAAK,SAAS,CAAC;oCACvB,QAAQ;oCACR,UAAU;oCACV,aAAa;oCACb,WAAW;oCACX,cAAc;oCACd,eAAe;oCACf,gBAAgB,KAAK,GAAG,KAAK;oCAC7B,aAAa;oCACb,WAAW;oCACX,gBAAgB,CAAC,CAAC;gCACpB;4BACF;wBACF;wBACA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,YAAY,CAAC,YAAY,CAAC;wBACvD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU;wBACnC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,kBAAkB,gBAAgB;wBAC1D,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW;wBACnC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC;wBACrC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa;oBACvC,OAAO;wBACL,QAAQ,KAAK,CAAC,oBAAoB,KAAK,SAAS,CAAC,OAAO,MAAM,EAAE,MAAM;oBACxE;oBACA;gBACF,OAAO,IAAI,WAAW,UAAU;oBAC9B,OAAO;oBACP,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI;oBACpE,MAAM,eAAe;wBACnB,QAAQ;wBACR,UAAU;wBACV,OAAO;wBACP,UAAU,IAAI,OAAO,WAAW;wBAChC,eAAe;wBACf,cAAc;wBACd,cAAc,OAAO,MAAM;oBAC7B;oBAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;wBAC/B,OAAO;4BAAE,IAAI,QAAQ,EAAE;wBAAC;wBACxB,MAAM;4BACJ,QAAQ;4BACR,UAAU,KAAK,SAAS,CAAC;wBAC3B;oBACF;oBACA,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,YAAY,CAAC;oBACxD,QAAQ,KAAK,CAAC,CAAC,SAAS,EAAE,cAAc;oBACxC,QAAQ,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,OAAO,MAAM;oBACvC;gBACF;gBACA,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,SAAS,CAAC;YAC1C;YAEA,6BAA6B;YAC7B,IAAI,UAAU,MAAM,eAAe,iBAAiB;gBAClD,eAAe,KAAK,GAAG,CAAC,eAAe,mBAAmB;YAC5D;YAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE;YAC3D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;IACP,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;IACrC,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE,IAAI,QAAQ,EAAE;QAAC;QACxB,MAAM;YACJ,QAAQ;YACR,UAAU,KAAK,SAAS,CAAC;gBACvB,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;AACF;AAEA,gBAAgB;AAChB,eAAe,mBAAmB,MAAW,EAAE,MAAc,EAAE,OAAY;IACzE,IAAI;QACF,SAAS;QACT,MAAM,aAAa,sBAAsB;QACzC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,QAAQ,CAAC,EAAE,WAAW,MAAM;YACrE,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,WAAW,MAAM,EAAE;QAChD;QAEA,QAAQ;QACR,MAAM,kBAAkB,eAAe;QACvC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC;QAC/C,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAClD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAE3D,WAAW;QACX,MAAM,WAAW,MAAM,MAAM,wFAAwF;YACnH,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;gBAChB,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,OAAO;oBACL,QAAQ;gBACV;gBACA,YAAY;oBACV,MAAM;oBACN,UAAU,QAAQ,QAAQ,IAAI;oBAC9B,eAAe,KAAK,SAAS;gBAC/B;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC1E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,cAAc,KAAK,SAAS,CAAC,QAAQ,MAAM;QAEvD,aAAa;QACb,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE;YAC1C,WAAW;YACX,MAAM,SAAS,OAAO,MAAM,CAAC,OAAO;YACpC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,UAAU,CAAC;YAElD,WAAW;YACX,qBAAqB,QAAQ,QAAQ;YAErC,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,UAAU;YACZ;QACF,OAAO;YACL,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,KAAK,SAAS,CAAC,SAAS;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,MAAM;IACR;AACF;AAEA,6BAA6B;AAC7B,SAAS,mBAAmB,UAAkB;IAC5C,wBAAwB;IACxB,OAAO;AACT;AAEA,sCAAsC;AACtC,eAAe,yBAAyB,MAAW,EAAE,MAAc,EAAE,OAAY;IAC/E,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,YAAY,EAAE;QAE9D,aAAa;QACb,MAAM,UAAU;YACd;gBACE,MAAM;gBACN,MAAM;YACR;SACD;QAED,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,MAAM,EAAE;QAE/C,cAAc;QACd,MAAM,WAAW,MAAM,MAAM,uEAAuE;YAClG,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,mBAAmB,OAAO,KAAK;gBACtC,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YAC9D,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;QAClE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,gBAAgB,KAAK,SAAS,CAAC,QAAQ,MAAM;QAEzD,SAAS;QACT,IAAI,SAAS;QACb,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACtC,SAAS,OAAO,IAAI,CAAC,OAAO;QAC9B,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,SAAS,OAAO,OAAO;QACzB,OAAO,IAAI,OAAO,EAAE,EAAE;YACpB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE;YACxC,SAAS,OAAO,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC;YAEpD,WAAW;YACX,qBAAqB,QAAQ,QAAQ;YAErC,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;gBACR,QAAQ;gBACR,UAAU;YACZ;QACF;QAEA,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS;IAC9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,MAAM;IACR;AACF;AAEA,sCAAsC;AACtC,eAAe,0BAA0B,MAAW,EAAE,MAAc,EAAE,OAAY;IAChF,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,YAAY,EAAE;QAExD,iBAAiB;QACjB,MAAM,kBAAkB,MAAM,4BAA4B;QAE1D,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,QAAQ,IAAI,CAAC;YACb,gBAAgB;YAChB,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAChD,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,SAAS;gBACX;YACF;YACA,IAAI,CAAC,WAAW;gBACd,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,yBAAyB,WAAW,QAAQ;QAC3D;QAEA,cAAc;QACd,MAAM,gBAAgB,MAAM,iBAAiB,eAAe,CAAC,EAAE;QAC/D,IAAI,CAAC,eAAe;YAClB,QAAQ,IAAI,CAAC;YACb,gBAAgB;YAChB,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAChD,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,SAAS;gBACX;YACF;YACA,IAAI,CAAC,WAAW;gBACd,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,MAAM,yBAAyB,WAAW,QAAQ;QAC3D;QAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,cAAc,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAErE,yBAAyB;QACzB,MAAM,UAAU;YACd;gBACE,MAAM;gBACN,MAAM,GAAG,OAAO,wBAAwB,EAAE,QAAQ,QAAQ,IAAI,GAAG;YACnE;YACA;gBACE,MAAM;gBACN,WAAW;oBACT,KAAK;gBACP;YACF;SACD;QAED,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAEhG,cAAc;QACd,MAAM,WAAW,MAAM,MAAM,uEAAuE;YAClG,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,mBAAmB,OAAO,KAAK;gBACtC,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YAC9D,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;QAClE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,gBAAgB,KAAK,SAAS,CAAC,QAAQ,MAAM;QAEzD,SAAS;QACT,IAAI,SAAS;QACb,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACtC,SAAS,OAAO,IAAI,CAAC,OAAO;QAC9B,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,SAAS,OAAO,OAAO;QACzB,OAAO,IAAI,OAAO,EAAE,EAAE;YACpB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE;YACxC,SAAS,OAAO,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC;YAEpD,sBAAsB;YACtB,qBAAqB,QAAQ,QAAQ;YAErC,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,YAAY;YACd;QACF;QAEA,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS;IAC9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,MAAM;IACR;AACF;AAEA,YAAY;AACZ,eAAe,mBAAmB,MAAW,EAAE,MAAc,EAAE,OAAY;IACzE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE;QAEtD,sBAAsB;QACtB,MAAM,kBAAkB,MAAM,4BAA4B;QAE1D,oBAAoB;QACpB,MAAM,UAAU;YACd;gBACE,MAAM;gBACN,MAAM,GAAG,QAAQ,CAAC,OAAO;YAC3B;SACD;QAED,eAAe;QACf,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,gBAAgB,MAAM,CAAC,QAAQ,CAAC;YAC1D,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,WAAW;oBACT,KAAK,eAAe,CAAC,EAAE,CAAC,cAAc;gBACxC;YACF;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,qBAAqB,CAAC;QACrC;QAEA,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE,gBAAgB,MAAM,CAAC,CAAC,CAAC;QAElF,4BAA4B;QAC5B,MAAM,WAAW,MAAM,MAAM,uEAAuE;YAClG,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,mBAAmB,OAAO,KAAK;gBACtC,SAAS;YAEX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YAC1D,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;QAC9D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;QAErD,2BAA2B;QAC3B,IAAI,SAAS;QAEb,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACtC,SAAS,OAAO,IAAI,CAAC,OAAO;QAC9B,OAAO,IAAI,OAAO,OAAO,EAAE;YACzB,SAAS,OAAO,OAAO;QACzB,OAAO,IAAI,OAAO,EAAE,EAAE;YACpB,gBAAgB;YAChB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE;YACxC,SAAS,OAAO,IAAI,CAAC,EAAE;QACzB;QAEA,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,UAAU,CAAC;YAElD,WAAW;YACX,qBAAqB,QAAQ,QAAQ;YAErC,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;gBACR,QAAQ;gBACR,UAAU;YACZ;QACF;QAEA,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS;IAC1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM;IACR;AACF;AAEA,WAAW;AACX,eAAe,qBAAqB,MAAW,EAAE,MAAc,EAAE,OAAY;IAC3E,MAAM,cAAc;IACpB,IAAI,eAAe;IAEnB,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,YAAY,GAAG,EAAE,QAAQ;YAE7D,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,oEAAoE,EAAE,QAAQ,EAAE;gBAC5G,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBACjE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM;YAEtD,wFAAwF;YACxF,MAAM,SAAS,OAAO,MAAM;YAE5B,IAAI,WAAW,eAAe,WAAW,aAAa;gBACpD,OAAO;gBACP,MAAM,WAAW,OAAO,OAAO,EAAE,aAAa,OAAO,SAAS;gBAE9D,IAAI,UAAU;oBACZ,cAAc;oBACd,IAAI,iBAAiB,QAAQ,QAAQ,IAAI;oBACzC,IAAI;wBACF,iBAAiB,MAAM,uBAAuB;wBAC9C,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,OAAO,EAAE,QAAQ,QAAQ,CAAC,EAAE,CAAC;oBAC3E,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,sBAAsB,MAAM,OAAO;oBAClD;oBAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;wBAC/B,OAAO;4BAAE,IAAI,QAAQ,EAAE;wBAAC;wBACxB,MAAM;4BACJ,UAAU;4BACV,QAAQ;4BACR,UAAU;4BACV,UAAU,KAAK,SAAS,CAAC;gCACvB,QAAQ;gCACR,UAAU;gCACV,aAAa,IAAI,OAAO,WAAW;gCACnC,eAAe;gCACf,gBAAgB;gCAChB,kBAAkB,QAAQ,QAAQ;gCAClC,gBAAgB;gCAChB,eAAe,KAAK,GAAG,CAAC,iBAAiB,CAAC,QAAQ,QAAQ,IAAI,CAAC,MAAM;4BACvE;wBACF;oBACF;oBACA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,YAAY,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE,eAAe,EAAE,CAAC;oBAC1F;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,yBAAyB;gBACzC;YACF,OAAO,IAAI,WAAW,YAAY,WAAW,SAAS;gBACpD,OAAO;gBACP,MAAM,eAAe,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI;gBACvD,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,OAAO;wBAAE,IAAI,QAAQ,EAAE;oBAAC;oBACxB,MAAM;wBACJ,QAAQ;wBACR,UAAU,KAAK,SAAS,CAAC;4BACvB,QAAQ;4BACR,UAAU;4BACV,OAAO;4BACP,UAAU,IAAI,OAAO,WAAW;4BAChC,eAAe;4BACf,cAAc;wBAChB;oBACF;gBACF;gBACA,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,WAAW,EAAE,cAAc;gBACtE;YACF,OAAO,IAAI,WAAW,aAAa,WAAW,aAAa,WAAW,cAAc;gBAClF,cAAc;gBACd,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,SAAS,CAAC;YAC1C,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,SAAS,CAAC;YAC5C;YAEA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE;YACzD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,OAAO;IACP,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC;IACnC,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;QAC/B,OAAO;YAAE,IAAI,QAAQ,EAAE;QAAC;QACxB,MAAM;YACJ,QAAQ;YACR,UAAU,KAAK,SAAS,CAAC;gBACvB,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;AACF;AAEA,uBAAuB;AACvB,eAAe,qBAAqB,MAAW,EAAE,MAAc,EAAE,OAAY;IAC3E,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,gDAAgD;YAC3E,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,QAAQ;gBACR,eAAe;oBACb,gBAAgB,KAAK,GAAG,CAAC,QAAQ,QAAQ,IAAI,GAAG;oBAChD,oBAAoB;gBACtB;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC9E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,QAAQ,MAAM;QAE3D,yBAAyB;QACzB,IAAI,OAAO,OAAO,EAAE;YAClB,cAAc;YACd,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;gBACR,QAAQ,OAAO,OAAO;YACxB;QACF,OAAO,IAAI,OAAO,SAAS,EAAE;YAC3B,SAAS;YACT,OAAO;gBACL,UAAU,OAAO,SAAS;gBAC1B,cAAc,OAAO,aAAa,IAAI;gBACtC,UAAU,OAAO,QAAQ,IAAI,QAAQ,QAAQ,IAAI;gBACjD,QAAQ;YACV;QACF,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YAC7C,UAAU;YACV,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;gBACR,QAAQ,OAAO,IAAI,CAAC,OAAO;YAC7B;QACF,OAAO;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,KAAK,SAAS,CAAC,SAAS;QAChE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEA,aAAa;AACb,eAAe,iCAAiC,SAAiB;IAK/D,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,UAAU,CAAC;QAE5C,YAAY;QACZ,MAAM,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,OAAO;gBAAE;YAAU;YACnB,QAAQ;gBAAE,IAAI;gBAAM,MAAM;gBAAM,iBAAiB;YAAK;QACxD;QAEA,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,mBAAmB,EAAE;YACvB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,MAAM,CAAC,IAAI,CAAC;QAE5C,gBAAgB;QAChB,MAAM,oBAA8B,EAAE;QAEtC,KAAK,MAAM,aAAa,WAAY;YAClC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;YAExC,IAAI,CAAC,UAAU,eAAe,EAAE;gBAC9B,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC;gBAC3C,kBAAkB,IAAI,CAAC,UAAU,IAAI;gBACrC;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU,eAAe;gBACnD,MAAM,eAAe,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI;gBAE/D,IAAI,CAAC,cAAc;oBACjB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC;oBAC3C,kBAAkB,IAAI,CAAC,UAAU,IAAI;gBACvC,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC;gBAC/C;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC;gBAC9C,kBAAkB,IAAI,CAAC,UAAU,IAAI;YACvC;QACF;QAEA,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,MAAM,eAAe,CAAC,oBAAoB,EAAE,kBAAkB,IAAI,CAAC,MAAM;YACzE,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc;YAEzC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP;YACF;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEA,sBAAsB;AACtB,eAAe,4BAA4B,OAAY;IACrD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,YAAY,IAAI,UAAU,QAAQ,CAAC;QAEpE,MAAM,kBAA4B,EAAE;QACpC,IAAI,iBAA2B,EAAE;QAEjC,8BAA8B;QAC9B,IAAI,QAAQ,QAAQ,EAAE;YACpB,IAAI;gBACF,MAAM,WAAW,OAAO,QAAQ,QAAQ,KAAK,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ;gBACvG,iBAAiB,SAAS,UAAU,IAAI,EAAE;gBAC1C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,eAAe,IAAI,CAAC,OAAO;YAC9D,EAAE,OAAO,GAAG;gBACV,QAAQ,IAAI,CAAC,yBAAyB;YACxC;QACF;QAEA,sBAAsB;QACtB,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,iBAAiB,MAAM,8BAA8B;YACrD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,eAAe,IAAI,CAAC,OAAO;QACvD;QAEA,4BAA4B;QAC5B,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,iBAAiB,MAAM,yBAAyB;YAChD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,eAAe,IAAI,CAAC,OAAO;QACzD;QAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,eAAe,IAAI,CAAC,OAAO;QAEvD,iBAAiB;QACjB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACjD,OAAO;oBACL,MAAM;wBAAE,IAAI;oBAAe;gBAC7B;YACF;YAEA,KAAK,MAAM,aAAa,WAAY;gBAClC,MAAM,YAAY,MAAM,yBAAyB,WAAW;gBAC5D,IAAI,WAAW;oBACb,gBAAgB,IAAI,CAAC;oBACrB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC;gBAChD,OAAO;oBACL,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC;gBAClD;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,QAAQ,CAAC;QACxD,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,eAAe,8BAA8B,OAAY;IACvD,MAAM,OAAO,GAAG,QAAQ,KAAK,IAAI,GAAG,CAAC,EAAE,QAAQ,WAAW,IAAI,GAAG,CAAC,EAAE,QAAQ,MAAM,IAAI,IAAI,CAAC,WAAW;IACtG,MAAM,iBAA2B,EAAE;IAEnC,mBAAmB;IACnB,IAAI;QACF,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpD,QAAQ;gBAAE,MAAM;YAAK;QACvB;QAEA,KAAK,MAAM,aAAa,cAAe;YACrC,IAAI,KAAK,QAAQ,CAAC,UAAU,IAAI,CAAC,WAAW,KAAK;gBAC/C,eAAe,IAAI,CAAC,UAAU,IAAI;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,aAAa;QAE1B,iBAAiB;QACjB,MAAM,cAAc;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACxD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,KAAK,QAAQ,CAAC,KAAK,WAAW,KAAK;gBACrC,eAAe,IAAI,CAAC;YACtB;QACF;IACF;IAEA,OAAO;WAAI,IAAI,IAAI;KAAgB,CAAC,KAAK;;AAC3C;AAEA,WAAW;AACX,eAAe,yBAAyB,OAAY;IAClD,IAAI;QACF,oBAAoB;QACpB,kBAAkB;QAClB,MAAM,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,MAAM;YACN,SAAS;gBAAE,WAAW;YAAM,EAAE,sBAAsB;QACtD;QAEA,OAAO,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,eAAe;QAC5B,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,eAAe,yBAAyB,SAAc,EAAE,OAAY;IAClE,IAAI;QACF,IAAI,CAAC,UAAU,eAAe,EAAE;YAC9B,OAAO;QACT;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU,eAAe;QAEnD,WAAW;QACX,MAAM,eAAkC;YACtC,kBAAkB,OAAO,KAAK;YAC9B,aAAa,OAAO,IAAI;YACxB,UAAU,OAAO,IAAI;YACrB,aAAa;gBACX,SAAS,OAAO,KAAK;YACvB;YACA,QAAQ;gBACN,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,IAAI;gBACxB,cAAc,OAAO,IAAI;gBACzB,cAAc,OAAO,KAAK;YAC5B;QACF;QAEA,eAAe;QACf,MAAM,YAAY,QAAQ,IAAI,IAAI;QAClC,MAAM,cAAc,QAAQ,QAAQ,EAAE,eAAe;QAErD,OAAO;QACP,IAAI,YAAY,QAAQ,CAAC,YAAY,cAAc,YAAY;YAC7D,eAAe;YACf,OAAO,aAAa,gBAAgB,IAAI,aAAa,MAAM,EAAE;QAC/D,OAAO,IAAI,YAAY,QAAQ,CAAC,cAAc,cAAc,WAAW;YACrE,aAAa;YACb,OAAO,aAAa,WAAW,IAAI,aAAa,MAAM,EAAE;QAC1D,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW,cAAc,UAAU;YACjE,eAAe;YACf,OAAO,aAAa,QAAQ,IAAI,aAAa,gBAAgB;QAC/D,OAAO;YACL,WAAW;YACX,OAAO,aAAa,gBAAgB,IAAI,aAAa,MAAM,EAAE,SAAS,aAAa,WAAW;QAChG;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC,EAAE;QACjD,OAAO;IACT;AACF;AAEA,WAAW;AACX,eAAe,yBAAyB,SAAiB,EAAE,MAAc,EAAE,cAAsB,MAAM;IACrG,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,gBAAgB,KAAK,UAAU;;IAErC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,UAAU,EAAE,QAAQ;IAEtD,MAAO,KAAK,GAAG,KAAK,YAAY,YAAa;QAC3C,IAAI;YACF,cAAc;YACd,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBACnD,OAAO;oBAAE,IAAI;gBAAU;YACzB;YAEA,IAAI,CAAC,SAAS;gBACZ,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,UAAU,IAAI,CAAC;gBACnC,OAAO;YACT;YAEA,IAAI,QAAQ,MAAM,KAAK,aAAa;gBAClC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,CAAC;gBACrC,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU;gBACtC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,KAAK,CAAC;gBACpC,OAAO;YACT;YAEA,OAAO;YACP,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,oBAAoB,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI,MAAM,EAAE,CAAC;YACnG,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,CAAC,EAAE;YACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,OAAO,EAAE,cAAc,KAAK,EAAE,CAAC;IAC7D,OAAO;AACT;AAEA,UAAU;AACV,eAAe,yBACb,iBAAyB,EACzB,aAAkB,EAClB,WAAgB;IAEhB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,cAAc,IAAI,EAAE;QAElD,MAAM,SAA4B;YAChC,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,QAAQ;YACR,QAAQ,EAAE;YACV,aAAa,EAAE;QACjB;QAEA,aAAa;QACb,OAAO,eAAe,GAAG,MAAM,wBAAwB,eAAe;QAEtE,kBAAkB;QAClB,OAAO,gBAAgB,GAAG,MAAM,yBAAyB,mBAAmB;QAE5E,YAAY;QACZ,MAAM,aAAa,IAAK,SAAS;;QACjC,MAAM,cAAc,IAAI,SAAS;;QAEjC,OAAO,kBAAkB,GACvB,OAAO,eAAe,GAAG,aACzB,OAAO,gBAAgB,GAAG;QAG5B,cAAc;QACd,MAAM,YAAY,IAAI,QAAQ;;QAC9B,OAAO,MAAM,GAAG,OAAO,kBAAkB,IAAI;QAE7C,aAAa;QACb,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,IAAI,OAAO,eAAe,GAAG,KAAK;gBAChC,OAAO,MAAM,CAAC,IAAI,CAAC;gBACnB,OAAO,WAAW,CAAC,IAAI,CAAC;YAC1B;YAEA,IAAI,OAAO,gBAAgB,GAAG,KAAK;gBACjC,OAAO,MAAM,CAAC,IAAI,CAAC;gBACnB,OAAO,WAAW,CAAC,IAAI,CAAC;YAC1B;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,eAAe,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,kBAAkB,CAAC,OAAO,CAAC,IAAI;QAEpJ,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;YACL,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,QAAQ;YACR,QAAQ;gBAAC;aAAS;YAClB,aAAa;gBAAC;aAAc;QAC9B;IACF;AACF;AAEA,UAAU;AACV,eAAe,wBAAwB,aAAkB,EAAE,WAAgB;IACzE,IAAI;QACF,IAAI,QAAQ,IAAI,OAAO;;QAEvB,aAAa;QACb,MAAM,MAAM,kBAAkB;QAC9B,IAAI,KAAK;YACP,SAAS,IAAI,WAAW;;YAExB,kBAAkB;YAClB,MAAM,SAAS,YAAY,MAAM,IAAI;YACrC,MAAM,cAAc,OAAO,WAAW;YAEtC,SAAS;YACT,IAAI,YAAY,QAAQ,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS;YACvE,IAAI,YAAY,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,KAAK,SAAS;YAEtE,SAAS;YACT,KAAK,MAAM,cAAc,IAAI,iBAAiB,CAAE;gBAC9C,IAAI,YAAY,QAAQ,CAAC,WAAW,WAAW,KAAK;oBAClD,SAAS;gBACX;YACF;QACF;QAEA,aAAa;QACb,IAAI,cAAc,IAAI,IAAI,YAAY,MAAM,EAAE,SAAS,cAAc,IAAI,GAAG;YAC1E,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,SAAS;;IAEvC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,cAAc;QAC3B,OAAO,IAAI,OAAO;;IACpB;AACF;AAEA,eAAe;AACf,eAAe,yBAAyB,iBAAyB,EAAE,aAAkB;IACnF,IAAI;QACF,qBAAqB;QACrB,IAAI,CAAC,cAAc,eAAe,EAAE;YAClC,OAAO,IAAI,YAAY;;QACzB;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,cAAc,eAAe;YACvD,IAAI,QAAQ,IAAI,OAAO;;YAEvB,aAAa;YACb,IAAI,OAAO,KAAK,EAAE,SAAS;YAC3B,IAAI,OAAO,IAAI,EAAE,SAAS;YAC1B,IAAI,OAAO,IAAI,EAAE,SAAS;YAE1B,sBAAsB;YACtB,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;gBACrD,SAAS;YACX;YAEA,OAAO,KAAK,GAAG,CAAC,OAAO;QAEzB,EAAE,OAAO,GAAG;YACV,QAAQ,IAAI,CAAC,eAAe;YAC5B,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,cAAc;QAC3B,OAAO,IAAI,OAAO;;IACpB;AACF;AAEA,YAAY;AACZ,eAAe,0BACb,WAAmB,EACnB,UAAiB,EACjB,MAAyB,EACzB,iBAA0B;IAE1B,IAAI;QACF,MAAM,kHAAA,CAAA,SAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACxC,MAAM;gBACJ;gBACA,WAAA;gBACA,sBAAsB,OAAO,eAAe;gBAC5C,uBAAuB,OAAO,gBAAgB;gBAC9C,cAAc,OAAO,kBAAkB;gBACvC,mBAAmB,KAAK,SAAS,CAAC;oBAChC,QAAQ,OAAO,MAAM;oBACrB,QAAQ,OAAO,MAAM;oBACrB,aAAa,OAAO,WAAW;gBACjC;gBACA,aAAa,KAAK,SAAS,CAAC,OAAO,MAAM;gBACzC,YAAY;gBACZ,aAAa;gBACb,mBAAmB;YACrB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,SAAS,CAAC;IAEhD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;IAChC;AACF;AAEA,WAAW;AACX,eAAe,uBAAuB,QAAgB;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAE7D,uCAAuC;QACvC,MAAM,eAAe,MAAM,MAAM,UAAU;YACzC,QAAQ;YACR,SAAS;gBACP,SAAS,YAAY,gBAAgB;YACvC;QACF;QAEA,gBAAgB;QAChB,MAAM,eAAe,aAAa,OAAO,CAAC,GAAG,CAAC;QAC9C,MAAM,gBAAgB,aAAa,OAAO,CAAC,GAAG,CAAC;QAE/C,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,cAAc;QACjD;QAEA,IAAI,eAAe;YACjB,MAAM,gBAAgB,SAAS;YAC/B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,gBAAgB,OAAO,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;YAErE,mBAAmB;YACnB,mBAAmB;YACnB,MAAM,oBAAoB,KAAK,KAAK,CAAC,gBAAgB,CAAC,OAAO,IAAI;YAEjE,IAAI,oBAAoB,KAAK,qBAAqB,IAAI;gBACpD,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;gBAClD,OAAO;YACT;QACF;QAEA,iCAAiC;QACjC,qBAAqB;QACrB,QAAQ,GAAG,CAAC;QAEZ,gBAAgB;QAChB,IAAI,SAAS,QAAQ,CAAC,WAAW;YAC/B,kBAAkB;YAClB,OAAO,EAAE,OAAO;;QAClB;QAEA,OAAO,EAAE,OAAO;;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,eAAe,MAAM,OAAO;QACzC,OAAO,EAAE,OAAO;;IAClB;AACF;AAEA,sBAAsB;AACtB,eAAe,+BAA+B,QAAgB;IAC5D,IAAI;QACF,sBAAsB;QACtB,uBAAuB;QACvB,WAAW;QAEX,QAAQ,GAAG,CAAC;QACZ,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,eAAe,MAAM,OAAO;QACzC,OAAO;IACT;AACF;AAEA,oBAAoB;AACpB,eAAe,qBAAqB,MAAW,EAAE,MAAc,EAAE,OAAY;IAC3E,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,YAAY,EAAE;QAC7D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAEpD,gBAAgB;QAChB,MAAM,EAAE,cAAc,EAAE,GAAG;QAE3B,gBAAgB;QAChB,MAAM,iBAAiB,IAAI,eAAe;YACxC,IAAI;YACJ,UAAU;YACV,QAAQ,OAAO,MAAM;YACrB,OAAO,OAAO,KAAK;YACnB,aAAa;YACb,WAAW;YACX,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QAEA,SAAS;QACT,MAAM,WAAW,KAAK,GAAG,CAAC,QAAQ,QAAQ,IAAI,IAAI,IAAI,kBAAkB;;QACxE,MAAM,YAAY,YAAY,IAAI,KAAM,YAAY,KAAK,MAAO,YAAY,KAAK,MAAM;QAEvF,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,OAAO,EAAE,WAAW;QAErD,OAAO;QACP,MAAM,YAAY,MAAM,eAAe,eAAe,CAAC;YACrD,QAAQ;YACR,YAAY;YACZ,gBAAgB;YAChB,KAAK;YACL,YAAY;QACd,GAAG,SAAS,MAAM,gBAAgB;;QAElC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW;QAE5C,SAAS;QACT,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;gBACJ,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,UAAU,KAAK,SAAS,CAAC;oBACvB,UAAU;oBACV,OAAO,OAAO,KAAK;oBACnB,aAAa,IAAI,OAAO,WAAW;oBACnC,WAAW;oBACX,eAAe;oBACf,KAAK;oBACL,YAAY;oBACZ,iBAAiB;gBACnB;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,YAAY,CAAC,kBAAkB,CAAC;QAE7D,OAAO;YACL,UAAU;YACV,UAAU;YACV,QAAQ;YACR,UAAU;QACZ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,YAAY,CAAC,CAAC,CAAC,EAAE;QAE9D,YAAY;QACZ,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;gBACJ,QAAQ;gBACR,UAAU,KAAK,SAAS,CAAC;oBACvB,UAAU;oBACV,OAAO,MAAM,OAAO;oBACpB,UAAU,IAAI,OAAO,WAAW;gBAClC;YACF;QACF;QAEA,MAAM;IACR;AACF;AAEA,cAAc;AACd,eAAe,iBAAiB,QAAgB;IAC9C,IAAI;QACF,IAAI,CAAC,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,IAAI;YACvE,OAAO;QACT;QAEA,UAAU;QACV,IAAI;YACF,IAAI,IAAI;QACV,EAAE,OAAO,GAAG;YACV,QAAQ,IAAI,CAAC,cAAc;YAC3B,OAAO;QACT;QAEA,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS,MAAM,QAAQ;QACzB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,MAAM,EAAE;YAC9C,OAAO;QACT;QAEA,cAAc;QACd,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,eAAe,CAAC,YAAY,UAAU,CAAC,WAAW;YACrD,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,aAAa;YAC1C,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,cAAc,MAAM,OAAO;QACxC,OAAO;IACT;AACF", "debugId": null}}]}