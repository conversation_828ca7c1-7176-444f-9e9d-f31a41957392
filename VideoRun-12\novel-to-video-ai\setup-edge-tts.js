const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupEdgeTTS() {
  try {
    console.log('🎵 设置Edge TTS配置...');
    
    // 1. 检查是否已有Edge TTS配置
    const existingTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'edge-tts',
        supportsTTS: true
      }
    });
    
    if (existingTTSConfig) {
      console.log('⚠️ 已有Edge TTS配置存在');
      console.log(`现有配置: ${existingTTSConfig.name} (${existingTTSConfig.model})`);
      
      // 更新现有配置以确保支持TTS
      await prisma.aIConfig.update({
        where: { id: existingTTSConfig.id },
        data: {
          supportsTTS: true,
          enabled: true,
          status: 'connected'
        }
      });
      console.log('✅ 已更新现有配置支持TTS');
      return;
    }

    // 2. 添加Edge TTS配置
    const edgeTTSConfig = await prisma.aIConfig.create({
      data: {
        provider: 'edge-tts',
        apiKey: 'edge-tts-free', // Edge TTS是免费的，不需要真实API密钥
        model: 'edge-tts-v1',
        name: 'Microsoft Edge TTS (免费)',
        description: '基于Microsoft Edge的免费中文语音合成服务，支持多种高质量中文声音',
        enabled: true,
        supportsVideo: false,
        supportsImage: false,
        supportsImageToVideo: false,
        supportsTTS: true, // 关键：标记为支持TTS
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      }
    });

    console.log('✅ 已添加Edge TTS配置:', edgeTTSConfig.name);

    // 3. 创建一些示例角色声音配置
    console.log('🎭 创建示例角色声音配置...');
    
    // 获取项目中的角色
    const characters = await prisma.character.findMany({
      take: 10 // 处理前10个角色
    });

    if (characters.length > 0) {
      // Edge TTS 中文声音配置
      const voiceConfigs = [
        { voiceId: 'zh-CN-XiaoxiaoNeural', voiceName: '晓晓（女声，温柔）', gender: 'female', style: 'gentle' },
        { voiceId: 'zh-CN-YunxiNeural', voiceName: '云希（男声，成熟）', gender: 'male', style: 'mature' },
        { voiceId: 'zh-CN-YunyangNeural', voiceName: '云扬（男声，阳光）', gender: 'male', style: 'cheerful' },
        { voiceId: 'zh-CN-XiaoyiNeural', voiceName: '晓伊（女声，甜美）', gender: 'female', style: 'sweet' },
        { voiceId: 'zh-CN-YunjianNeural', voiceName: '云健（男声，稳重）', gender: 'male', style: 'calm' },
        { voiceId: 'zh-CN-XiaochenNeural', voiceName: '晓辰（女声，活泼）', gender: 'female', style: 'lively' },
        { voiceId: 'zh-CN-XiaohanNeural', voiceName: '晓涵（女声，知性）', gender: 'female', style: 'intellectual' },
        { voiceId: 'zh-CN-XiaomengNeural', voiceName: '晓梦（女声，梦幻）', gender: 'female', style: 'dreamy' },
        { voiceId: 'zh-CN-XiaomoNeural', voiceName: '晓墨（女声，文艺）', gender: 'female', style: 'artistic' },
        { voiceId: 'zh-CN-XiaoqiuNeural', voiceName: '晓秋（女声，温暖）', gender: 'female', style: 'warm' }
      ];

      for (let i = 0; i < Math.min(characters.length, voiceConfigs.length); i++) {
        const character = characters[i];
        const voiceConfig = voiceConfigs[i];

        // 检查是否已有声音配置
        const existingVoice = await prisma.characterVoice.findFirst({
          where: {
            characterId: character.id,
            ttsConfigId: edgeTTSConfig.id
          }
        });

        if (!existingVoice) {
          await prisma.characterVoice.create({
            data: {
              characterId: character.id,
              ttsConfigId: edgeTTSConfig.id,
              voiceId: voiceConfig.voiceId,
              voiceName: voiceConfig.voiceName,
              basePitch: 0,
              baseSpeed: 1.0,
              baseVolume: 80,
              emotionMappings: JSON.stringify({
                neutral: { pitchAdjust: 0, speedAdjust: 0, volumeAdjust: 0 },
                happy: { pitchAdjust: 2, speedAdjust: 0.1, volumeAdjust: 5 },
                sad: { pitchAdjust: -2, speedAdjust: -0.1, volumeAdjust: -5 },
                angry: { pitchAdjust: 3, speedAdjust: 0.2, volumeAdjust: 10 },
                excited: { pitchAdjust: 4, speedAdjust: 0.15, volumeAdjust: 8 },
                worried: { pitchAdjust: -1, speedAdjust: -0.05, volumeAdjust: -3 },
                surprised: { pitchAdjust: 5, speedAdjust: 0.1, volumeAdjust: 5 },
                gentle: { pitchAdjust: -1, speedAdjust: -0.1, volumeAdjust: -2 }
              }),
              enabled: true
            }
          });

          console.log(`✅ 为角色 "${character.name}" 配置声音: ${voiceConfig.voiceName}`);
        }
      }
    } else {
      console.log('⚠️ 未找到角色，跳过声音配置创建');
    }

    console.log('\n🎉 Edge TTS配置完成！');
    console.log('现在可以使用以下功能：');
    console.log('- 调用 /api/ai/generate-tts 生成语音（使用Edge TTS）');
    console.log('- 调用 GET /api/ai/generate-tts 获取可用声音列表');
    console.log('- 为角色配置专属声音');
    console.log('- 免费使用，无需API密钥');
    
    // 4. 显示可用的中文声音列表
    console.log('\n🎤 Edge TTS 可用中文声音：');
    const displayVoices = [
      { voiceId: 'zh-CN-XiaoxiaoNeural', voiceName: '晓晓（女声，温柔）' },
      { voiceId: 'zh-CN-YunxiNeural', voiceName: '云希（男声，成熟）' },
      { voiceId: 'zh-CN-YunyangNeural', voiceName: '云扬（男声，阳光）' },
      { voiceId: 'zh-CN-XiaoyiNeural', voiceName: '晓伊（女声，甜美）' },
      { voiceId: 'zh-CN-YunjianNeural', voiceName: '云健（男声，稳重）' },
      { voiceId: 'zh-CN-XiaochenNeural', voiceName: '晓辰（女声，活泼）' },
      { voiceId: 'zh-CN-XiaohanNeural', voiceName: '晓涵（女声，知性）' },
      { voiceId: 'zh-CN-XiaomengNeural', voiceName: '晓梦（女声，梦幻）' },
      { voiceId: 'zh-CN-XiaomoNeural', voiceName: '晓墨（女声，文艺）' },
      { voiceId: 'zh-CN-XiaoqiuNeural', voiceName: '晓秋（女声，温暖）' }
    ];
    displayVoices.forEach((voice, index) => {
      console.log(`  ${index + 1}. ${voice.voiceName} (${voice.voiceId})`);
    });
    
  } catch (error) {
    console.error('❌ Edge TTS配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupEdgeTTS();
