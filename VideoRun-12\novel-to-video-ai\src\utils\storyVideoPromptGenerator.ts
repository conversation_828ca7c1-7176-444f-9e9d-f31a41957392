// 小说剧情视频提示词生成器
// 专门用于生成保持角色和场景一致性的视频提示词

interface CharacterDNA {
  id: string
  name: string
  coreFeatures: {
    age: string
    gender: string
    profession: string
    faceShape: string
    eyes: string
    hair: string
    build: string
    signatureStyle: string
  }
  consistentElements: {
    facialStructure: string
    eyeCharacteristics: string
    hairFeatures: string
    bodyProportions: string
  }
}

interface SceneDNA {
  id: string
  name: string
  baseDescription: {
    type: string
    layout: string
    furniture: string
    lighting: string
    colorScheme: string
    atmosphere: string
  }
  fixedElements: {
    architecture: string
    furnitureLayout: string
    lightingSetup: string
    colorPalette: string
  }
  timeStates: {
    [timeOfDay: string]: {
      lighting: string
      activityLevel: string
      atmosphere: string
    }
  }
}

interface PlotSequence {
  sequenceId: string
  action: string
  duration: string
  camera: string
  emotion: string
  keyMoments: string[]
}

interface StoryVideoPromptOptions {
  characters: CharacterDNA[]
  scene: SceneDNA
  timeOfDay: string
  plotSequences: PlotSequence[]
  emotionalArc: string
  customEnhancement?: string
  style?: 'cinematic' | 'documentary' | 'artistic'
  quality?: 'standard' | 'high' | 'cinematic'
  // 新增：一致性约束选项
  consistencyMode?: 'strict' | 'balanced' | 'creative'
  referenceImages?: { [characterId: string]: string[] }
  consistencyWeights?: {
    textWeight: number
    imageWeight: number
  }
}

// 角色DNA数据库
const CHARACTER_DNA_DATABASE: Record<string, CharacterDNA> = {
  'lin_xiaoyu': {
    id: 'lin_xiaoyu',
    name: '林晓雨',
    coreFeatures: {
      age: '25 years old',
      gender: 'Asian woman',
      profession: 'architect',
      faceShape: 'oval delicate face',
      eyes: 'large intelligent brown eyes',
      hair: 'long straight black hair',
      build: 'tall and slender build',
      signatureStyle: 'professional business attire'
    },
    consistentElements: {
      facialStructure: 'same facial bone structure',
      eyeCharacteristics: 'same eye shape and color',
      hairFeatures: 'consistent hair texture and length',
      bodyProportions: 'identical body proportions'
    }
  }
}

// 场景DNA数据库
const SCENE_DNA_DATABASE: Record<string, SceneDNA> = {
  'modern_office': {
    id: 'modern_office',
    name: '现代办公室',
    baseDescription: {
      type: 'modern office interior',
      layout: 'open floor plan with glass partitions',
      furniture: 'white desks, ergonomic chairs, large windows',
      lighting: 'natural daylight with warm LED supplements',
      colorScheme: 'white, gray, and blue tones',
      atmosphere: 'professional and clean'
    },
    fixedElements: {
      architecture: 'floor-to-ceiling windows on east wall',
      furnitureLayout: 'desks arranged in rows facing windows',
      lightingSetup: 'consistent overhead LED grid',
      colorPalette: 'same neutral color scheme'
    },
    timeStates: {
      morning: {
        lighting: 'soft golden sunlight streaming through windows',
        activityLevel: 'quiet, few people arriving',
        atmosphere: 'fresh and energetic'
      },
      midday: {
        lighting: 'bright natural light, LED supplements on',
        activityLevel: 'busy, people working at desks',
        atmosphere: 'focused and productive'
      },
      evening: {
        lighting: 'warm LED lighting, sunset glow',
        activityLevel: 'some people leaving, quieter',
        atmosphere: 'calm and reflective'
      }
    }
  }
}

/**
 * 生成小说剧情视频提示词
 */
export function generateStoryVideoPrompt(options: StoryVideoPromptOptions): string {
  const {
    characters,
    scene,
    timeOfDay,
    plotSequences,
    emotionalArc,
    customEnhancement,
    style = 'cinematic',
    quality = 'high',
    consistencyMode = 'balanced',
    referenceImages = {},
    consistencyWeights = { textWeight: 0.7, imageWeight: 0.3 }
  } = options

  const promptSections: string[] = []

  // 1. 一致性约束声明（新增）
  promptSections.push(buildConsistencyDeclarationSection(consistencyMode, consistencyWeights))

  // 2. 故事场景描述
  promptSections.push(buildStorySceneSection(plotSequences))

  // 3. 角色一致性描述（增强版）
  promptSections.push(buildEnhancedCharacterConsistencySection(characters, referenceImages, consistencyMode))

  // 4. 场景一致性描述
  promptSections.push(buildSceneConsistencySection(scene, timeOfDay))

  // 5. 动作序列描述
  promptSections.push(buildActionSequenceSection(plotSequences, emotionalArc))

  // 6. 叙事元素描述
  promptSections.push(buildNarrativeElementsSection(plotSequences, emotionalArc))

  // 7. 技术要求描述（增强版）
  promptSections.push(buildEnhancedTechnicalRequirementsSection(style, quality, consistencyMode))

  // 8. 一致性验证要求（新增）
  promptSections.push(buildConsistencyValidationSection(characters, consistencyMode))

  // 9. 自定义增强
  if (customEnhancement && customEnhancement.trim()) {
    promptSections.push(`CUSTOM ENHANCEMENT: ${customEnhancement}`)
  }

  return promptSections.join('\n\n')
}

/**
 * 构建故事场景部分
 */
function buildStorySceneSection(plotSequences: PlotSequence[]): string {
  const mainAction = plotSequences.map(seq => seq.action).join(', then ')
  return `STORY SCENE: ${mainAction}`
}

/**
 * 构建角色一致性部分
 */
function buildCharacterConsistencySection(characters: CharacterDNA[]): string {
  const characterDescriptions = characters.map(char => {
    const { coreFeatures, consistentElements } = char
    return `${char.name}, same ${coreFeatures.age} ${coreFeatures.gender} ${coreFeatures.profession} as previous scenes,
${coreFeatures.faceShape}, ${coreFeatures.eyes},
${coreFeatures.hair}, ${coreFeatures.build},
${consistentElements.facialStructure},
${consistentElements.eyeCharacteristics},
maintaining identical facial features and professional demeanor`
  })

  return `CHARACTER CONSISTENCY:\n${characterDescriptions.join('\n\n')}`
}

/**
 * 构建场景一致性部分
 */
function buildSceneConsistencySection(scene: SceneDNA, timeOfDay: string): string {
  const { baseDescription, fixedElements, timeStates } = scene
  const timeState = timeStates[timeOfDay] || timeStates.midday

  return `SCENE CONSISTENCY:
Same ${baseDescription.type} as established,
${fixedElements.architecture},
${fixedElements.furnitureLayout},
maintaining identical ${fixedElements.lightingSetup},
${fixedElements.colorPalette},
${timeState.lighting},
${timeState.atmosphere},
consistent office environment design`
}

/**
 * 构建动作序列部分
 */
function buildActionSequenceSection(plotSequences: PlotSequence[], emotionalArc: string): string {
  const actionDescriptions = plotSequences.map((seq, index) => {
    const timing = index === 0 ? 'starts' : index === plotSequences.length - 1 ? 'ends' : 'then'
    return `${timing} ${seq.action} with ${seq.emotion}`
  })

  return `ACTION SEQUENCE:
${actionDescriptions.join(',\n')},
smooth emotional progression: ${emotionalArc}`
}

/**
 * 构建叙事元素部分
 */
function buildNarrativeElementsSection(plotSequences: PlotSequence[], emotionalArc: string): string {
  const keyMoments = plotSequences.flatMap(seq => seq.keyMoments).join(', ')
  
  return `NARRATIVE ELEMENTS:
capturing ${keyMoments},
showing the journey through ${emotionalArc},
emphasizing professional expertise and emotional depth,
visual storytelling of character development`
}

/**
 * 构建技术要求部分
 */
function buildTechnicalRequirementsSection(style: string, quality: string): string {
  const styleDescriptions = {
    cinematic: 'cinematic quality, professional cinematography',
    documentary: 'documentary style, natural lighting',
    artistic: 'artistic style, creative camera work'
  }

  const qualityDescriptions = {
    standard: 'good quality, stable footage',
    high: 'high quality, 4K resolution, sharp focus',
    cinematic: 'cinematic quality, professional grade, ultra high definition'
  }

  return `TECHNICAL REQUIREMENTS:
${styleDescriptions[style]},
${qualityDescriptions[quality]},
consistent lighting throughout,
smooth camera movements following emotional beats,
maintaining character and scene consistency across all frames`
}

/**
 * 为角色创建DNA档案
 */
export function createCharacterDNA(characterInfo: any): CharacterDNA {
  return {
    id: characterInfo.id || generateId(),
    name: characterInfo.name,
    coreFeatures: {
      age: extractAge(characterInfo.identity) || '25 years old',
      gender: extractGender(characterInfo.identity) || 'person',
      profession: extractProfession(characterInfo.identity) || 'professional',
      faceShape: extractFaceShape(characterInfo.facial) || 'oval face',
      eyes: extractEyeFeatures(characterInfo.facial) || 'expressive eyes',
      hair: extractHairFeatures(characterInfo.hairstyle) || 'neat hair',
      build: extractBuildFeatures(characterInfo.physique) || 'average build',
      signatureStyle: extractClothingStyle(characterInfo.clothing) || 'casual attire'
    },
    consistentElements: {
      facialStructure: 'same facial bone structure',
      eyeCharacteristics: 'same eye shape and color',
      hairFeatures: 'consistent hair texture and length',
      bodyProportions: 'identical body proportions'
    }
  }
}

/**
 * 为场景创建DNA档案
 */
export function createSceneDNA(sceneInfo: any): SceneDNA {
  return {
    id: sceneInfo.id || generateId(),
    name: sceneInfo.name,
    baseDescription: {
      type: sceneInfo.location || 'interior space',
      layout: sceneInfo.description || 'organized layout',
      furniture: extractFurniture(sceneInfo.description) || 'basic furniture',
      lighting: extractLighting(sceneInfo.atmosphere) || 'natural lighting',
      colorScheme: extractColorScheme(sceneInfo.description) || 'neutral colors',
      atmosphere: sceneInfo.atmosphere || 'comfortable atmosphere'
    },
    fixedElements: {
      architecture: 'consistent architectural elements',
      furnitureLayout: 'same furniture arrangement',
      lightingSetup: 'identical lighting setup',
      colorPalette: 'consistent color palette'
    },
    timeStates: {
      morning: {
        lighting: 'soft morning light',
        activityLevel: 'quiet and peaceful',
        atmosphere: 'fresh and energetic'
      },
      midday: {
        lighting: 'bright natural light',
        activityLevel: 'active and busy',
        atmosphere: 'focused and productive'
      },
      evening: {
        lighting: 'warm evening light',
        activityLevel: 'calm and relaxed',
        atmosphere: 'peaceful and reflective'
      }
    }
  }
}

/**
 * 生成一致性检查提示词
 */
export function generateConsistencyCheckPrompt(
  previousVideoPrompt: string,
  currentVideoPrompt: string
): string {
  return `CONSISTENCY CHECK:
Ensure the following elements remain identical between videos:

PREVIOUS SCENE REFERENCE:
${previousVideoPrompt}

CURRENT SCENE REQUIREMENTS:
${currentVideoPrompt}

CONSISTENCY REQUIREMENTS:
- Same character facial features and body proportions
- Identical scene layout and architectural elements
- Consistent lighting setup and color scheme
- Matching furniture arrangement and spatial relationships
- Same character clothing and styling (unless story requires change)
- Continuous environmental details and atmosphere

MAINTAIN VISUAL CONTINUITY across all generated content`
}

// 辅助函数
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

function extractAge(identity: string): string {
  const ageMatch = identity?.match(/(\d+)岁|(\d+)\s*years?\s*old/i)
  return ageMatch ? `${ageMatch[1] || ageMatch[2]} years old` : ''
}

function extractGender(identity: string): string {
  if (identity?.includes('女') || identity?.includes('woman') || identity?.includes('female')) {
    return 'woman'
  } else if (identity?.includes('男') || identity?.includes('man') || identity?.includes('male')) {
    return 'man'
  }
  return 'person'
}

function extractProfession(identity: string): string {
  const professions = ['architect', 'doctor', 'teacher', 'engineer', 'designer', 'manager']
  for (const prof of professions) {
    if (identity?.toLowerCase().includes(prof)) {
      return prof
    }
  }
  return 'professional'
}

function extractFaceShape(facial: string): string {
  if (facial?.includes('瓜子脸')) return 'oval face'
  if (facial?.includes('圆脸')) return 'round face'
  if (facial?.includes('方脸')) return 'square face'
  return 'oval face'
}

function extractEyeFeatures(facial: string): string {
  if (facial?.includes('大眼睛')) return 'large expressive eyes'
  if (facial?.includes('小眼睛')) return 'small bright eyes'
  return 'expressive eyes'
}

function extractHairFeatures(hairstyle: string): string {
  if (hairstyle?.includes('长发')) return 'long hair'
  if (hairstyle?.includes('短发')) return 'short hair'
  if (hairstyle?.includes('卷发')) return 'curly hair'
  return 'neat hair'
}

function extractBuildFeatures(physique: string): string {
  if (physique?.includes('高挑')) return 'tall and slender build'
  if (physique?.includes('娇小')) return 'petite build'
  return 'average build'
}

function extractClothingStyle(clothing: string): string {
  if (clothing?.includes('职业装')) return 'professional business attire'
  if (clothing?.includes('休闲')) return 'casual wear'
  return 'neat clothing'
}

function extractFurniture(description: string): string {
  return 'modern furniture arrangement'
}

function extractLighting(atmosphere: string): string {
  return 'natural lighting'
}

function extractColorScheme(description: string): string {
  return 'neutral color scheme'
}

/**
 * 构建一致性约束声明（新增）
 */
function buildConsistencyDeclarationSection(mode: string, weights: { textWeight: number, imageWeight: number }): string {
  const modeDescriptions = {
    strict: 'STRICT CONSISTENCY MODE: Absolute adherence to character and scene specifications. Zero tolerance for deviations.',
    balanced: 'BALANCED CONSISTENCY MODE: High fidelity to specifications with minor creative adaptations allowed.',
    creative: 'CREATIVE CONSISTENCY MODE: Core character identity maintained while allowing artistic interpretation.'
  }

  return `CONSISTENCY CONSTRAINT SYSTEM ACTIVATED
${modeDescriptions[mode as keyof typeof modeDescriptions]}

DUAL CONSTRAINT WEIGHTS:
- Text Constraint Weight: ${(weights.textWeight * 100).toFixed(0)}%
- Visual Constraint Weight: ${(weights.imageWeight * 100).toFixed(0)}%

CONSISTENCY PRIORITY: Character identity > Scene continuity > Artistic style`
}

/**
 * 构建增强的角色一致性描述
 */
function buildEnhancedCharacterConsistencySection(
  characters: CharacterDNA[],
  referenceImages: { [characterId: string]: string[] },
  consistencyMode: string
): string {
  if (characters.length === 0) return ''

  const characterDescriptions = characters.map(char => {
    const features = char.coreFeatures
    const consistent = char.consistentElements
    const hasReferenceImages = referenceImages[char.id] && referenceImages[char.id].length > 0

    let description = `${char.name}: ${features.age} ${features.gender}, ${features.profession}.
FACIAL DNA: ${features.faceShape}, ${features.eyes}, ${features.hair}.
BODY DNA: ${features.build}.
STYLE DNA: ${features.signatureStyle}.
CONSISTENCY ANCHORS: ${consistent.facialStructure}, ${consistent.eyeCharacteristics}, ${consistent.hairFeatures}, ${consistent.bodyProportions}.`

    if (hasReferenceImages) {
      description += `
VISUAL REFERENCE: Character has ${referenceImages[char.id].length} reference image(s) for visual consistency validation.`
    }

    if (consistencyMode === 'strict') {
      description += `
STRICT MODE: ZERO deviation allowed from specified features. Exact replication required.`
    }

    return description
  }).join('\n\n')

  return `CHARACTER CONSISTENCY MATRIX:
${characterDescriptions}

CONSISTENCY ENFORCEMENT:
- Facial features must remain identical across all frames
- Hair style and color must be consistent
- Body proportions and posture must match specifications
- Clothing style must align with character DNA
- Expression changes allowed only within character personality bounds

VALIDATION CHECKPOINTS:
✓ Frame-by-frame facial feature verification
✓ Hair consistency across camera angles
✓ Body proportion maintenance
✓ Clothing style adherence`
}

/**
 * 构建增强的技术要求描述
 */
function buildEnhancedTechnicalRequirementsSection(style: string, quality: string, consistencyMode: string): string {
  const styleDescriptions = {
    cinematic: 'cinematic quality, professional cinematography, film-grade lighting',
    documentary: 'documentary style, natural lighting, authentic feel',
    artistic: 'artistic style, creative camera work, stylized visuals'
  }

  const qualityDescriptions = {
    standard: 'good quality, stable footage, clear details',
    high: 'high quality, 4K resolution, sharp focus, professional grade',
    cinematic: 'cinematic quality, ultra high definition, film-grade production'
  }

  const consistencyRequirements = {
    strict: 'STRICT: Pixel-perfect consistency, identical lighting conditions, exact camera positioning',
    balanced: 'BALANCED: High consistency with natural variations, smooth transitions',
    creative: 'CREATIVE: Core consistency maintained with artistic freedom in presentation'
  }

  return `ENHANCED TECHNICAL REQUIREMENTS:
${styleDescriptions[style]},
${qualityDescriptions[quality]},
${consistencyRequirements[consistencyMode as keyof typeof consistencyRequirements]},

CONSISTENCY TECHNICAL SPECS:
- Maintain identical character proportions across all frames
- Consistent lighting temperature and direction
- Smooth camera movements preserving character visibility
- Color grading consistency for scene continuity
- Frame-by-frame character feature validation
- Temporal consistency in motion and expression`
}

/**
 * 构建一致性验证要求
 */
function buildConsistencyValidationSection(characters: CharacterDNA[], consistencyMode: string): string {
  const characterNames = characters.map(char => char.name).join(', ')

  const validationLevels = {
    strict: 'STRICT VALIDATION: Every frame must pass character identity verification',
    balanced: 'BALANCED VALIDATION: Core features verified with tolerance for natural variation',
    creative: 'CREATIVE VALIDATION: Character essence maintained with artistic interpretation allowed'
  }

  return `CONSISTENCY VALIDATION PROTOCOL:
${validationLevels[consistencyMode as keyof typeof validationLevels]}

VALIDATION TARGETS: ${characterNames}

AUTOMATED CHECKS:
1. Facial feature consistency (eyes, nose, mouth, face shape)
2. Hair style and color maintenance
3. Body proportion verification
4. Clothing style adherence
5. Scene element continuity
6. Lighting consistency
7. Color palette maintenance

QUALITY GATES:
- Pre-generation: Prompt consistency verification
- Mid-generation: Frame sampling validation
- Post-generation: Full video consistency audit

FAILURE HANDLING:
- Inconsistency detected → Regeneration with enhanced constraints
- Partial inconsistency → Selective frame regeneration
- Systematic inconsistency → DNA profile refinement`
}
