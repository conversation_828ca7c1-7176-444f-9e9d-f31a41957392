import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { DeepSeekClient, AIServiceError, handleAIRequest } from '@/lib/ai'
import { generateStoryVideoPrompt, createCharacterDNA, createSceneDNA } from '@/utils/storyVideoPromptGenerator'

// 生成视频脚本和指令
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { episodeId, customPrompt } = body

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '剧集ID不能为空' },
        { status: 400 }
      )
    }

    // 获取完整的剧集信息
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      include: {
        project: {
          include: {
            characters: true,
          },
        },
        plotInfo: true,
      },
    })

    if (!episode) {
      return NextResponse.json(
        { success: false, error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 获取可用的AI配置（优先使用视频生成模型）
    let aiConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'minimax',
        enabled: true
      }
    })

    // 如果没有MiniMax配置，使用其他配置
    if (!aiConfig) {
      aiConfig = await prisma.aIConfig.findFirst({
        where: { enabled: true }
      })
    }

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型' },
        { status: 400 }
      )
    }

    if (!episode.plotInfo) {
      return NextResponse.json(
        { success: false, error: '请先提取剧情信息' },
        { status: 400 }
      )
    }

    try {
      // 整合多维度信息
      const multiDimensionalInfo = await buildMultiDimensionalInfo(episode)

      let result: any

      // 根据模型类型选择不同的处理方式
      if (aiConfig.provider === 'minimax') {
        // 使用MiniMax生成视频
        result = await generateVideoWithMinimax(aiConfig, multiDimensionalInfo, customPrompt)
      } else {
        // 使用其他模型生成视频脚本
        const aiClient = new DeepSeekClient(aiConfig)
        const videoScript = await handleAIRequest(() =>
          generateVideoScript(aiClient, multiDimensionalInfo, customPrompt)
        )
        result = { videoScript, multiDimensionalInfo }
      }

      // 更新剧集状态
      await prisma.episode.update({
        where: { id: episodeId },
        data: { status: 'video_generated' },
      })

      return NextResponse.json({
        success: true,
        data: result,
        message: aiConfig.provider === 'minimax' ? '视频生成完成' : '视频脚本生成完成',
      })
    } catch (error) {
      if (error instanceof AIServiceError) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: error.statusCode }
        )
      }

      throw error
    }
  } catch (error) {
    console.error('视频生成失败:', error)
    return NextResponse.json(
      { success: false, error: '视频生成失败，请重试' },
      { status: 500 }
    )
  }
}

// 构建多维度信息
async function buildMultiDimensionalInfo(episode: any) {
  const plotInfo = {
    characters: JSON.parse(episode.plotInfo.characters),
    scenes: JSON.parse(episode.plotInfo.scenes),
    events: JSON.parse(episode.plotInfo.events),
  }

  // 获取角色详细信息
  const characterDetails = episode.project.characters.reduce((acc: any, char: any) => {
    if (plotInfo.characters.includes(char.name)) {
      acc[char.name] = {
        appearance: JSON.parse(char.appearance),
        identity: char.identity,
        personality: char.personality,
        hiddenLines: char.hiddenLines,
      }
    }
    return acc
  }, {})

  return {
    episode: {
      title: episode.title,
      content: episode.content,
      orderIndex: episode.orderIndex,
    },
    characters: characterDetails,
    scenes: plotInfo.scenes,
    events: plotInfo.events,
  }
}

// 使用MiniMax生成视频
async function generateVideoWithMinimax(aiConfig: any, info: any, customPrompt?: string): Promise<any> {
  try {
    // 从剧情分析结果中获取时长
    let duration = 6 // 默认6秒
    if (info.episode?.plotInfo?.plotSequences) {
      try {
        const plotSequences = JSON.parse(info.episode.plotInfo.plotSequences)
        if (plotSequences && plotSequences.length > 0) {
          const firstSequence = plotSequences[0]
          if (firstSequence.duration) {
            const match = firstSequence.duration.match(/(\d+)/)
            duration = match ? Math.min(parseInt(match[1]), 6) : 6 // MiniMax最大支持6秒
          }
        }
      } catch (e) {
        console.warn('解析剧情序列时长失败，使用默认时长')
      }
    }

    console.log(`📏 MiniMax使用视频时长: ${duration}秒`)

    // 构建视频生成提示词
    const prompt = buildVideoPrompt(info, customPrompt, duration)

    // 调用MiniMax API
    const response = await fetch('https://api.minimax.chat/v1/video_generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${aiConfig.apiKey}`
      },
      body: JSON.stringify({
        model: aiConfig.model,
        prompt: prompt,
        video_setting: {
          video_duration: duration,
          video_aspect_ratio: '16:9',
          video_quality: 'high'
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`MiniMax API错误: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    return {
      taskId: result.task_id,
      status: result.status,
      videoUrl: result.video_url,
      prompt: prompt,
      multiDimensionalInfo: info
    }
  } catch (error) {
    console.error('MiniMax视频生成失败:', error)
    throw new AIServiceError(
      'MiniMax视频生成失败，请重试',
      'MINIMAX_VIDEO_ERROR',
      500
    )
  }
}

// 生成视频脚本
async function generateVideoScript(aiClient: DeepSeekClient, info: any, customPrompt?: string): Promise<any> {
  const prompt = buildVideoScriptPrompt(info, customPrompt)
  const response = await aiClient.callAPI(prompt, 6000)

  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }
    return JSON.parse(jsonMatch[0])
  } catch (error) {
    console.error('解析视频脚本响应失败:', error)
    throw new AIServiceError(
      '视频脚本生成失败，请重试',
      'SCRIPT_PARSE_ERROR',
      500
    )
  }
}

// 构建MiniMax视频生成提示词（使用新的剧情视频生成器）
function buildVideoPrompt(info: any, customPrompt?: string, duration: number = 6): string {
  try {
    // 创建角色DNA档案
    const characterDNAs = Object.entries(info.characters).map(([name, details]: [string, any]) =>
      createCharacterDNA({
        id: name.toLowerCase().replace(/\s+/g, '_'),
        name: name,
        identity: details.identity,
        facial: details.appearance?.face,
        hairstyle: details.appearance?.body,
        physique: details.appearance?.body,
        clothing: details.appearance?.clothing,
        personality: details.personality
      })
    )

    // 创建场景DNA档案
    const mainScene = info.scenes?.[0]
    const sceneDNA = createSceneDNA({
      id: 'main_scene',
      name: mainScene?.location || '场景',
      location: mainScene?.location,
      description: mainScene?.description,
      atmosphere: mainScene?.atmosphere
    })

    // 分析剧情序列
    const plotSequences = [{
      sequenceId: 'main_sequence',
      action: `acting out the story: ${info.episode.content}`,
      duration: `${duration} seconds`,
      camera: 'cinematic camera movement',
      emotion: 'appropriate to story context',
      keyMoments: info.events?.map((e: any) => e.normal || e.conflict || e.escalation).filter(Boolean) || []
    }]

    // 生成专业的剧情视频提示词
    const storyPrompt = generateStoryVideoPrompt({
      characters: characterDNAs,
      scene: sceneDNA,
      timeOfDay: 'midday',
      plotSequences: plotSequences,
      emotionalArc: 'natural story progression',
      customEnhancement: customPrompt,
      style: 'cinematic',
      quality: 'high'
    })

    return storyPrompt
  } catch (error) {
    console.error('生成剧情视频提示词失败:', error)
    // 降级到简单提示词
    return buildSimpleVideoPrompt(info, customPrompt, duration)
  }
}

// 简单视频提示词（备用方案）
function buildSimpleVideoPrompt(info: any, customPrompt?: string, duration: number = 6): string {
  let basePrompt = `根据以下剧情信息生成视频：

剧集：${info.episode.title}
内容：${info.episode.content}

主要角色：${Object.keys(info.characters).join('、')}
场景：${info.scenes.map((s: any) => s.location).join('、')}

视频要求：
- 时长${duration}秒
- 高质量画面
- 符合剧情氛围
- 角色形象准确
- 保持人物和场景一致性`

  if (customPrompt && customPrompt.trim()) {
    basePrompt += `\n\n增强要求：${customPrompt}`
  }

  return basePrompt
}

// 构建视频脚本生成提示词
function buildVideoScriptPrompt(info: any, customPrompt?: string): string {
  let basePrompt = `你是一个专业的视频制作导演。请根据以下多维度信息，生成详细的视频制作脚本。`

  if (customPrompt && customPrompt.trim()) {
    basePrompt += `\n\n增强要求：${customPrompt}\n`
  }

  basePrompt += `
  return `你是一个专业的视频制作导演。请根据以下多维度信息，生成详细的视频制作脚本。

## 剧集信息
标题：${info.episode.title}
内容：${info.episode.content}

## 角色信息
${Object.entries(info.characters).map(([name, details]: [string, any]) => `
**${name}**
- 外貌：${details.appearance.face} | ${details.appearance.body} | ${details.appearance.clothing}
- 身份：${details.identity}
- 性格：${details.personality}
- 背景：${details.hiddenLines}
`).join('\n')}

## 场景信息
${info.scenes.map((scene: any, index: number) => `
场景${index + 1}：${scene.location}
环境：${scene.description}
氛围：${scene.atmosphere}
`).join('\n')}

## 事件信息
${info.events.map((event: any, index: number) => `
事件${index + 1}：
- 正常状态：${event.normal}
- 矛盾冲突：${event.conflict}
- 升级事件：${event.escalation}
- 参与角色：${event.participants.join('、')}
- 发生地点：${event.location}
- 具体行为：${event.actions.join('、')}
`).join('\n')}

请生成包含以下内容的视频制作脚本，严格按JSON格式返回：

{
  "videoScript": {
    "title": "视频标题",
    "duration": "预计时长（分钟）",
    "scenes": [
      {
        "sceneNumber": 1,
        "location": "拍摄地点",
        "timeOfDay": "时间（白天/夜晚等）",
        "characters": ["出场角色"],
        "visualDescription": "画面描述（镜头、构图、色彩等）",
        "dialogue": "对话内容",
        "action": "动作描述",
        "mood": "情绪氛围",
        "cameraWork": "镜头运用",
        "lighting": "灯光要求",
        "props": ["道具清单"],
        "costume": "服装要求",
        "makeup": "化妆要求",
        "soundEffects": "音效要求",
        "music": "配乐建议"
      }
    ],
    "overallStyle": "整体风格",
    "colorPalette": "色彩基调",
    "targetAudience": "目标观众",
    "keyMessages": ["核心信息"],
    "technicalRequirements": {
      "equipment": ["设备需求"],
      "software": ["后期软件"],
      "specialEffects": ["特效需求"]
    }
  }
}`
}

// 为DeepSeekClient添加callAPI方法
declare module '@/lib/ai' {
  interface DeepSeekClient {
    callAPI(prompt: string, maxTokens?: number): Promise<string>
  }
}
