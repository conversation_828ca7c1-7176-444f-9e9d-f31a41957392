{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-single-segment/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\n// POST - 生成单个视频片段\nexport async function POST(request: NextRequest) {\n  try {\n    const { segmentId, modelId } = await request.json()\n\n    console.log('🎬 开始生成单个视频片段')\n    console.log('📋 参数:', { segmentId, modelId })\n\n    // 获取当前请求的host信息\n    const host = request.headers.get('host') || 'localhost:3000'\n    const protocol = request.headers.get('x-forwarded-proto') || 'http'\n\n    if (!segmentId) {\n      return NextResponse.json(\n        { success: false, error: '缺少片段ID' },\n        { status: 400 }\n      )\n    }\n\n    // 获取片段信息\n    const segment = await prisma.videoSegment.findUnique({\n      where: { id: segmentId },\n      include: {\n        storyVideo: true\n      }\n    })\n\n    if (!segment) {\n      return NextResponse.json(\n        { success: false, error: '片段不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 获取项目ID - 直接从segment中获取\n    const projectId = segment.projectId\n\n    if (!projectId) {\n      return NextResponse.json(\n        { success: false, error: '片段缺少项目ID' },\n        { status: 400 }\n      )\n    }\n\n    // 🔒 先决条件检查：主要角色必须有参考图像\n    console.log('🔍 检查角色参考图像先决条件...')\n    const characterCheck = await checkCharacterImagePrerequisites(projectId)\n    \n    if (!characterCheck.success) {\n      console.log('❌ 角色图像先决条件检查失败:', characterCheck.error)\n      return NextResponse.json(\n        { \n          success: false, \n          error: characterCheck.error,\n          missingCharacters: characterCheck.missingCharacters,\n          requirementType: 'character_images'\n        },\n        { status: 400 }\n      )\n    }\n    \n    console.log('✅ 角色图像先决条件检查通过')\n\n    // 检查片段状态\n    if (segment.status === 'generating') {\n      return NextResponse.json(\n        { success: false, error: '该片段正在生成中，请稍后重试' },\n        { status: 400 }\n      )\n    }\n\n    if (segment.status === 'completed' && segment.videoUrl) {\n      return NextResponse.json(\n        { success: false, error: '该片段已经生成完成' },\n        { status: 400 }\n      )\n    }\n\n    // 更新片段状态为生成中\n    await prisma.videoSegment.update({\n      where: { id: segmentId },\n      data: { \n        status: 'generating',\n        updatedAt: new Date()\n      }\n    })\n\n    console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`)\n\n    // 异步调用内部生成API（不等待完成）\n    // 使用当前请求的host信息构建内部API URL\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${protocol}://${host}`\n    const internalApiUrl = `${baseUrl}/api/ai/generate-story-video/segment`\n    console.log(`🔗 调用内部生成API: ${internalApiUrl}`)\n\n    fetch(internalApiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        segmentId: segment.id,\n        modelId\n      })\n    })\n    .then(response => {\n      console.log(`📡 内部API响应状态: ${response.status}`)\n      if (!response.ok) {\n        return response.text().then(text => {\n          throw new Error(`内部API调用失败: ${response.status} ${text}`)\n        })\n      }\n      return response.json()\n    })\n    .then(data => {\n      console.log(`✅ 内部API调用成功:`, data)\n    })\n    .catch(error => {\n      console.error(`❌ 片段 ${segment.segmentIndex} 内部API调用失败:`, error)\n\n      // 更新片段状态为失败\n      prisma.videoSegment.update({\n        where: { id: segment.id },\n        data: {\n          status: 'failed',\n          updatedAt: new Date()\n        }\n      }).catch(dbError => {\n        console.error('更新片段状态失败:', dbError)\n      })\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: `片段 ${segment.segmentIndex} 开始生成`,\n        segmentId: segment.id,\n        segmentIndex: segment.segmentIndex,\n        title: segment.title\n      }\n    })\n\n  } catch (error) {\n    console.error('生成单个视频片段失败:', error)\n    return NextResponse.json(\n      { success: false, error: '生成视频片段失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 检查角色图像先决条件（复用主API的逻辑）\nasync function checkCharacterImagePrerequisites(projectId: string): Promise<{\n  success: boolean\n  error?: string\n  missingCharacters?: string[]\n}> {\n  try {\n    console.log(`🔍 检查项目 ${projectId} 的角色图像先决条件`)\n\n    // 获取项目的所有角色\n    const characters = await prisma.character.findMany({\n      where: { projectId },\n      select: { id: true, name: true, generatedImages: true }\n    })\n\n    if (characters.length === 0) {\n      return {\n        success: false,\n        error: '项目中没有角色信息，请先创建角色',\n        missingCharacters: []\n      }\n    }\n\n    console.log(`📋 找到 ${characters.length} 个角色`)\n\n    // 检查每个角色是否有参考图像\n    const missingCharacters: string[] = []\n    \n    for (const character of characters) {\n      console.log(`🎭 检查角色: ${character.name}`)\n      \n      if (!character.generatedImages) {\n        console.log(`❌ 角色 ${character.name} 没有生成图像`)\n        missingCharacters.push(character.name)\n        continue\n      }\n\n      try {\n        const images = JSON.parse(character.generatedImages)\n        const hasAllImages = images.front && images.side && images.back\n        \n        if (!hasAllImages) {\n          console.log(`❌ 角色 ${character.name} 的图像不完整`)\n          missingCharacters.push(character.name)\n        } else {\n          console.log(`✅ 角色 ${character.name} 有完整的参考图像`)\n        }\n      } catch (e) {\n        console.log(`❌ 角色 ${character.name} 的图像数据解析失败`)\n        missingCharacters.push(character.name)\n      }\n    }\n\n    if (missingCharacters.length > 0) {\n      const errorMessage = `以下角色缺少参考图像，请先生成角色形象：${missingCharacters.join('、')}`\n      console.log(`❌ 先决条件检查失败: ${errorMessage}`)\n      \n      return {\n        success: false,\n        error: errorMessage,\n        missingCharacters\n      }\n    }\n\n    console.log('✅ 所有角色都有完整的参考图像')\n    return { success: true }\n\n  } catch (error) {\n    console.error('检查角色图像先决条件失败:', error)\n    return {\n      success: false,\n      error: '检查角色图像时发生错误，请稍后重试'\n    }\n  }\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,UAAU;YAAE;YAAW;QAAQ;QAE3C,gBAAgB;QAChB,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;QAC5C,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB;QAE7D,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,YAAY;YACd;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,YAAY,QAAQ,SAAS;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,iBAAiB,MAAM,iCAAiC;QAE9D,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,QAAQ,GAAG,CAAC,mBAAmB,eAAe,KAAK;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO,eAAe,KAAK;gBAC3B,mBAAmB,eAAe,iBAAiB;gBACnD,iBAAiB;YACnB,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC;QAEZ,SAAS;QACT,IAAI,QAAQ,MAAM,KAAK,cAAc;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAiB,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,EAAE;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ,QAAQ;gBACR,WAAW,IAAI;YACjB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,YAAY,CAAC,EAAE,EAAE,QAAQ,KAAK,EAAE;QAEjE,qBAAqB;QACrB,2BAA2B;QAC3B,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,GAAG,SAAS,GAAG,EAAE,MAAM;QAC3E,MAAM,iBAAiB,GAAG,QAAQ,oCAAoC,CAAC;QACvE,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,gBAAgB;QAE7C,MAAM,gBAAgB;YACpB,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW,QAAQ,EAAE;gBACrB;YACF;QACF,GACC,IAAI,CAAC,CAAA;YACJ,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;YAC9C,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,OAAO,SAAS,IAAI,GAAG,IAAI,CAAC,CAAA;oBAC1B,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,MAAM;gBACzD;YACF;YACA,OAAO,SAAS,IAAI;QACtB,GACC,IAAI,CAAC,CAAA;YACJ,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;QAC9B,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,WAAW,CAAC,EAAE;YAEzD,YAAY;YACZ,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBACzB,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,MAAM;oBACJ,QAAQ;oBACR,WAAW,IAAI;gBACjB;YACF,GAAG,KAAK,CAAC,CAAA;gBACP,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,KAAK,CAAC;gBAC1C,WAAW,QAAQ,EAAE;gBACrB,cAAc,QAAQ,YAAY;gBAClC,OAAO,QAAQ,KAAK;YACtB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,wBAAwB;AACxB,eAAe,iCAAiC,SAAiB;IAK/D,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,UAAU,CAAC;QAE5C,YAAY;QACZ,MAAM,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,OAAO;gBAAE;YAAU;YACnB,QAAQ;gBAAE,IAAI;gBAAM,MAAM;gBAAM,iBAAiB;YAAK;QACxD;QAEA,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,mBAAmB,EAAE;YACvB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,MAAM,CAAC,IAAI,CAAC;QAE5C,gBAAgB;QAChB,MAAM,oBAA8B,EAAE;QAEtC,KAAK,MAAM,aAAa,WAAY;YAClC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;YAExC,IAAI,CAAC,UAAU,eAAe,EAAE;gBAC9B,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC;gBAC3C,kBAAkB,IAAI,CAAC,UAAU,IAAI;gBACrC;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU,eAAe;gBACnD,MAAM,eAAe,OAAO,KAAK,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI;gBAE/D,IAAI,CAAC,cAAc;oBACjB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC;oBAC3C,kBAAkB,IAAI,CAAC,UAAU,IAAI;gBACvC,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC;gBAC/C;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,UAAU,CAAC;gBAC9C,kBAAkB,IAAI,CAAC,UAAU,IAAI;YACvC;QACF;QAEA,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,MAAM,eAAe,CAAC,oBAAoB,EAAE,kBAAkB,IAAI,CAAC,MAAM;YACzE,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc;YAEzC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP;YACF;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF", "debugId": null}}]}