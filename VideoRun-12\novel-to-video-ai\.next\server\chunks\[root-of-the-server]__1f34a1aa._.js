module.exports = {

"[project]/.next-internal/server/app/api/ai/video-progress/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/app/api/ai/video-progress/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const episodeId = searchParams.get('episodeId');
        if (!episodeId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少episodeId参数'
            }, {
                status: 400
            });
        }
        // 获取视频片段详细信息
        const segments = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.findMany({
            where: {
                episodeId
            },
            orderBy: {
                segmentIndex: 'asc'
            },
            select: {
                id: true,
                segmentIndex: true,
                title: true,
                status: true,
                duration: true,
                videoUrl: true,
                metadata: true,
                createdAt: true,
                updatedAt: true
            }
        });
        // 计算详细进度信息
        const totalSegments = segments.length;
        const completedSegments = segments.filter((s)=>s.status === 'completed').length;
        const generatingSegments = segments.filter((s)=>s.status === 'generating').length;
        const failedSegments = segments.filter((s)=>s.status === 'failed').length;
        const pendingSegments = segments.filter((s)=>s.status === 'pending').length;
        // 计算进度百分比
        const progress = totalSegments > 0 ? Math.round(completedSegments / totalSegments * 100) : 0;
        // 估算剩余时间（基于已完成片段的平均生成时间）
        let estimatedRemainingTime = null;
        const completedWithMetadata = segments.filter((s)=>{
            if (s.status !== 'completed' || !s.metadata) return false;
            try {
                const metadata = JSON.parse(s.metadata);
                return metadata.generationTime && metadata.completedAt;
            } catch  {
                return false;
            }
        });
        if (completedWithMetadata.length > 0) {
            const avgGenerationTime = completedWithMetadata.reduce((sum, segment)=>{
                try {
                    const metadata = JSON.parse(segment.metadata);
                    return sum + (metadata.generationTime || 0);
                } catch  {
                    return sum;
                }
            }, 0) / completedWithMetadata.length;
            const remainingSegments = totalSegments - completedSegments;
            estimatedRemainingTime = Math.round(avgGenerationTime * remainingSegments / 1000) // 转换为秒
            ;
        }
        // 获取当前处理的片段信息
        const currentProcessingSegments = segments.filter((s)=>s.status === 'generating').map((s)=>({
                index: s.segmentIndex,
                title: s.title,
                startTime: s.updatedAt,
                metadata: s.metadata ? JSON.parse(s.metadata) : null
            }));
        // 计算总视频大小
        let totalVideoSize = 0;
        let totalVideoDuration = 0;
        segments.forEach((segment)=>{
            if (segment.status === 'completed' && segment.metadata) {
                try {
                    const metadata = JSON.parse(segment.metadata);
                    if (metadata.videoSize && typeof metadata.videoSize === 'number') {
                        totalVideoSize += metadata.videoSize;
                    }
                } catch  {}
            }
            if (segment.duration) {
                totalVideoDuration += segment.duration;
            }
        });
        // 获取队列状态
        const queueStatus = {
            total: totalSegments,
            completed: completedSegments,
            processing: generatingSegments,
            pending: pendingSegments,
            failed: failedSegments
        };
        // 详细的片段信息
        const detailedSegments = segments.map((segment)=>{
            let metadata = null;
            try {
                metadata = segment.metadata ? JSON.parse(segment.metadata) : null;
            } catch  {}
            return {
                id: segment.id,
                index: segment.segmentIndex,
                title: segment.title,
                status: segment.status,
                duration: segment.duration,
                hasVideo: !!segment.videoUrl,
                videoUrl: segment.videoUrl,
                createdAt: segment.createdAt,
                updatedAt: segment.updatedAt,
                metadata: {
                    taskId: metadata?.taskId,
                    provider: metadata?.provider,
                    videoSize: metadata?.videoSize,
                    responseTime: metadata?.responseTime,
                    totalAttempts: metadata?.totalAttempts,
                    generationTime: metadata?.generationTime,
                    completedAt: metadata?.completedAt,
                    error: metadata?.error
                }
            };
        });
        const response = {
            success: true,
            episodeId,
            progress: {
                percentage: progress,
                completed: completedSegments,
                total: totalSegments,
                estimatedRemainingTime,
                currentProcessing: currentProcessingSegments,
                queueStatus
            },
            statistics: {
                totalVideoSize,
                totalVideoDuration,
                averageSegmentDuration: totalSegments > 0 ? totalVideoDuration / totalSegments : 0
            },
            segments: detailedSegments,
            lastUpdated: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('获取视频生成进度失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取进度信息失败'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1f34a1aa._.js.map