import React from 'react'
import { AlertTriangle, Refresh<PERSON><PERSON>, Zap, Clock } from 'lucide-react'

interface ServiceFallbackProps {
  serviceName: string
  error: string
  onRetry?: () => void
  onSwitchModel?: (provider: string, model: string) => void
}

export default function ServiceFallback({ 
  serviceName, 
  error, 
  onRetry, 
  onSwitchModel 
}: ServiceFallbackProps) {
  const isInternalError = error.includes('internal error')
  const isTemporaryError = isInternalError || error.includes('timeout') || error.includes('unavailable')

  const fallbackModels = [
    {
      provider: 'deepseek',
      model: 'deepseek-chat',
      name: 'DeepSeek Chat',
      description: '高性能对话模型，响应快速',
      status: 'stable'
    },
    {
      provider: 'deepseek', 
      model: 'deepseek-reasoner',
      name: 'DeepSeek Reasoner',
      description: '强大的推理能力，适合复杂任务',
      status: 'stable'
    },
    {
      provider: 'tongyi',
      model: 'qwen-turbo',
      name: '通义千问 Turbo',
      description: '阿里云大模型，中文优化',
      status: 'stable'
    }
  ]

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-4">
      {/* 错误提示 */}
      <div className="flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            {serviceName} 服务暂时不可用
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            {error}
          </p>
        </div>
      </div>

      {/* 临时错误的特殊提示 */}
      {isTemporaryError && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">临时服务问题</span>
          </div>
          <p className="text-sm text-blue-700 mt-1">
            这通常是服务端的临时问题，预计在几分钟到几小时内恢复正常。
          </p>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-2">
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-1.5" />
            重试连接
          </button>
        )}
        
        <button
          onClick={() => window.open('https://console.volcengine.com/', '_blank')}
          className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-800 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 transition-colors"
        >
          检查服务状态
        </button>
      </div>

      {/* 备用模型建议 */}
      <div className="border-t border-yellow-200 pt-4">
        <div className="flex items-center space-x-2 mb-3">
          <Zap className="w-4 h-4 text-green-600" />
          <span className="text-sm font-medium text-gray-800">建议使用备用模型</span>
        </div>
        
        <div className="grid gap-2">
          {fallbackModels.map((model) => (
            <div
              key={`${model.provider}-${model.model}`}
              className="flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md hover:border-green-300 transition-colors"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900">
                    {model.name}
                  </span>
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    稳定
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-0.5">
                  {model.description}
                </p>
              </div>
              
              {onSwitchModel && (
                <button
                  onClick={() => onSwitchModel(model.provider, model.model)}
                  className="ml-3 px-2 py-1 text-xs font-medium text-green-800 bg-green-100 border border-green-300 rounded hover:bg-green-200 transition-colors"
                >
                  切换
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 技术支持信息 */}
      <div className="border-t border-yellow-200 pt-3">
        <p className="text-xs text-gray-600">
          如果问题持续存在，请访问{' '}
          <a 
            href="https://console.volcengine.com/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            火山引擎控制台
          </a>
          {' '}联系技术支持或查看服务状态。
        </p>
      </div>
    </div>
  )
}
