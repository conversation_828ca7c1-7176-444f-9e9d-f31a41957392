import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 剧情分析提示词 - 按照需求文档的三大维度
const PLOT_ANALYSIS_PROMPT = `
你是一个专业的剧情分析师，请对以下剧集内容进行深度分析，提取三大信息维度：

## 分析要求：

### 1. 本集人物
- 识别当前剧集中出场的所有角色
- 分析每个角色在本集中的作用和重要性
- 关联角色的详细信息和关系

### 2. 场景信息
- 故事发生的具体地点
- 环境描述（时间、天气、氛围等）
- 场景的情感色彩和象征意义

### 3. 事件三要素
- **结构分析**: 正常状态 → 矛盾冲突 → 升级事件
- **核心定义**: 哪几个人在什么地方做什么
- **分析维度**: 参与人物、发生地点、具体行为/事件

请严格按照以下JSON格式返回分析结果：

{
  "characters": [
    "角色名1",
    "角色名2"
  ],
  "scenes": [
    {
      "location": "具体地点名称",
      "description": "环境详细描述",
      "atmosphere": "情感氛围（如：紧张、温馨、悲伤等）",
      "time": "时间设定",
      "significance": "场景在剧情中的重要性"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与人物1", "参与人物2"],
      "location": "事件发生地点",
      "actions": ["具体行为1", "具体行为2"],
      "significance": "事件重要性和影响"
    }
  ]
}

## 剧集内容：
`

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; episodeId: string }> }
) {
  try {
    const { id: projectId, episodeId } = await params
    let body = {}
    try {
      body = await request.json()
    } catch (error) {
      // 如果没有body或body为空，使用默认值
      body = {}
    }
    const { modelId } = body

    // 获取项目信息
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 获取剧集信息
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId }
    })

    if (!episode) {
      return NextResponse.json(
        { success: false, error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 获取AI配置 - 优先使用指定模型，否则默认使用DeepSeek Reasoner
    let aiConfig
    if (modelId) {
      aiConfig = await prisma.aIConfig.findUnique({
        where: { id: modelId }
      })
    } else {
      // 默认使用DeepSeek Reasoner模型
      aiConfig = await prisma.aIConfig.findFirst({
        where: {
          provider: 'deepseek',
          model: 'deepseek-reasoner',
          enabled: true
        },
      })
    }

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置DeepSeek Reasoner模型或指定其他可用模型' },
        { status: 400 }
      )
    }

    if (!aiConfig.enabled) {
      return NextResponse.json(
        { success: false, error: '指定的AI模型未启用' },
        { status: 400 }
      )
    }

    console.log(`开始使用${aiConfig.name}分析剧集《${episode.title}》的剧情信息...`)

    // 调用AI分析剧情
    const plotResponse = await callAIModel(
      aiConfig,
      PLOT_ANALYSIS_PROMPT + episode.content
    )

    console.log('AI分析完成，开始解析结果...')

    // 解析AI响应
    let plotData
    try {
      const cleanedResponse = cleanJsonResponse(plotResponse)
      plotData = JSON.parse(cleanedResponse)
    } catch (parseError) {
      console.error('剧情信息解析失败:', parseError)
      console.error('原始响应:', plotResponse)
      return NextResponse.json(
        { success: false, error: '剧情信息解析失败，请重试' },
        { status: 500 }
      )
    }

    // 验证返回数据结构
    if (!plotData.characters || !plotData.scenes || !plotData.events) {
      console.error('AI返回数据不完整:', plotData)
      return NextResponse.json(
        { success: false, error: 'AI返回数据不完整，请重试' },
        { status: 500 }
      )
    }

    console.log('剧情分析结果:', {
      characters: plotData.characters.length,
      scenes: plotData.scenes.length,
      events: plotData.events.length
    })

    // 保存剧情信息到数据库
    const plotInfo = await prisma.plotInfo.upsert({
      where: { episodeId },
      update: {
        characters: JSON.stringify(plotData.characters || []),
        scenes: JSON.stringify(plotData.scenes || []),
        events: JSON.stringify(plotData.events || []),
        updatedAt: new Date()
      },
      create: {
        episodeId,
        characters: JSON.stringify(plotData.characters || []),
        scenes: JSON.stringify(plotData.scenes || []),
        events: JSON.stringify(plotData.events || [])
      }
    })

    // 更新剧集状态
    await prisma.episode.update({
      where: { id: episodeId },
      data: {
        status: 'plot_analyzed',
        updatedAt: new Date()
      }
    })

    console.log(`剧集《${episode.title}》剧情分析完成并保存到数据库`)

    return NextResponse.json({
      success: true,
      data: {
        characters: JSON.parse(plotInfo.characters),
        scenes: JSON.parse(plotInfo.scenes),
        events: JSON.parse(plotInfo.events)
      },
      message: '剧情分析完成'
    })

  } catch (error) {
    console.error('剧情分析失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '剧情分析失败，请重试'
      },
      { status: 500 }
    )
  }
}

// 清理JSON响应，移除可能导致解析失败的字符
function cleanJsonResponse(response: string): string {
  // 移除可能的前后缀文字，只保留JSON部分
  let cleaned = response.trim()

  // 查找JSON开始和结束位置
  const jsonStart = cleaned.indexOf('{')
  const jsonEnd = cleaned.lastIndexOf('}')

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    cleaned = cleaned.substring(jsonStart, jsonEnd + 1)
  }

  // 更安全的JSON清理方法
  try {
    // 尝试直接解析，如果成功就返回
    JSON.parse(cleaned)
    return cleaned
  } catch (error) {
    // 如果解析失败，进行更激进的清理
    console.log('JSON解析失败，进行清理:', error.message)
    
    // 使用更简单的方法：逐字符处理，正确处理字符串边界
    let result = ''
    let inString = false
    let escapeNext = false
    
    for (let i = 0; i < cleaned.length; i++) {
      const char = cleaned[i]
      const prevChar = i > 0 ? cleaned[i - 1] : ''
      
      if (escapeNext) {
        result += char
        escapeNext = false
        continue
      }
      
      if (char === '\\') {
        result += char
        escapeNext = true
        continue
      }
      
      if (char === '"' && prevChar !== '\\') {
        inString = !inString
        result += char
        continue
      }
      
      if (inString) {
        // 在字符串内部，转义特殊字符
        if (char === '\n') {
          result += '\\n'
        } else if (char === '\r') {
          result += '\\r'
        } else if (char === '\t') {
          result += '\\t'
        } else {
          result += char
        }
      } else {
        // 在字符串外部，正常处理
        result += char
      }
    }
    
    // 移除末尾逗号
    result = result
      .replace(/,\s*}/g, '}')
      .replace(/,\s*]/g, ']')
    
    return result
  }
}

// 调用AI模型生成内容
async function callAIModel(aiConfig: any, prompt: string) {
  const { provider, apiKey, model } = aiConfig

  switch (provider) {
    case 'deepseek':
      return await callDeepSeek(apiKey, model, prompt)
    case 'openai':
      return await callOpenAI(apiKey, model, prompt)
    case 'claude':
      return await callClaude(apiKey, model, prompt)
    case 'doubao':
      return await callDoubao(apiKey, model, prompt)
    default:
      throw new Error(`不支持的AI提供商: ${provider}`)
  }
}

// DeepSeek API调用
async function callDeepSeek(apiKey: string, model: string, prompt: string) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 300000) // 5分钟超时

  try {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 8000
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`DeepSeek API调用失败: ${response.statusText}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('AI返回内容为空')
    }

    return content
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('AI调用超时，请稍后重试')
    }
    throw error
  }
}

// OpenAI API调用
async function callOpenAI(apiKey: string, model: string, prompt: string) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 4000
    })
  })

  if (!response.ok) {
    throw new Error(`OpenAI API调用失败: ${response.statusText}`)
  }

  const data = await response.json()
  const content = data.choices[0]?.message?.content

  if (!content) {
    throw new Error('AI返回内容为空')
  }

  return content
}

// Claude API调用
async function callClaude(apiKey: string, model: string, prompt: string) {
  // Claude API实现
  throw new Error('Claude API暂未实现')
}

// 豆包 (Doubao) API调用
async function callDoubao(apiKey: string, model: string, prompt: string) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 300000) // 5分钟超时

  try {
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 8000
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      let errorData: any = {}
      try {
        errorData = JSON.parse(errorText)
      } catch (e) {
        throw new Error(`豆包API调用失败: ${errorText || response.statusText}`)
      }

      if (errorData.error?.code === 'invalid_api_key') {
        throw new Error('豆包API密钥无效，请检查配置')
      } else {
        throw new Error(`豆包API调用失败: ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('豆包AI返回内容为空')
    }

    return content
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('豆包AI调用超时，请稍后重试')
    }
    throw error
  }
}
