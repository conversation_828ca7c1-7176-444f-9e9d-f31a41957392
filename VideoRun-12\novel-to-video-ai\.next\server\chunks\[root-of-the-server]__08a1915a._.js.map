{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\n// 获取项目列表\nexport async function GET(request: NextRequest) {\n  try {\n    const projects = await prisma.project.findMany({\n      orderBy: {\n        createdAt: 'desc'\n      },\n      include: {\n        _count: {\n          select: {\n            characters: true,\n            episodes: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: projects,\n    })\n  } catch (error) {\n    console.error('获取项目列表失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取项目列表失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 创建新项目\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { name, description } = body\n\n    if (!name || !name.trim()) {\n      return NextResponse.json(\n        { success: false, error: '项目名称不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 检查项目名称是否已存在\n    const existingProject = await prisma.project.findFirst({\n      where: {\n        name: name.trim()\n      }\n    })\n\n    if (existingProject) {\n      return NextResponse.json(\n        { success: false, error: '项目名称已存在，请使用其他名称' },\n        { status: 400 }\n      )\n    }\n\n    // 创建项目\n    const project = await prisma.project.create({\n      data: {\n        name: name.trim(),\n        description: description?.trim() || null,\n        status: 'created'\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: project,\n      message: '项目创建成功'\n    })\n  } catch (error) {\n    console.error('创建项目失败:', error)\n    return NextResponse.json(\n      { success: false, error: '创建项目失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,SAAS;gBACP,WAAW;YACb;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,YAAY;wBACZ,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;QAE9B,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,IAAI,iBAAiB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkB,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,aAAa,aAAa,UAAU;gBACpC,QAAQ;YACV;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}