import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { DeepSeekClient } from '@/lib/ai'

// POST - 生成单个视频片段（内部API）
export async function POST(request: NextRequest) {
  try {
    const { segmentId, modelId } = await request.json()

    console.log('🎬 内部API：生成单个视频片段')
    console.log('📋 参数:', { segmentId, modelId })

    if (!segmentId) {
      return NextResponse.json(
        { success: false, error: '缺少片段ID' },
        { status: 400 }
      )
    }

    // 获取片段信息
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId }
    })

    if (!segment) {
      return NextResponse.json(
        { success: false, error: '片段不存在' },
        { status: 404 }
      )
    }

    console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`)

    // 更新片段状态为生成中
    await prisma.videoSegment.update({
      where: { id: segmentId },
      data: {
        status: 'generating',
        updatedAt: new Date()
      }
    })

    // 异步调用真实的视频生成逻辑
    generateSingleSegmentAsync(segment, modelId)
      .then(() => {
        console.log(`✅ 片段 ${segment.segmentIndex} 生成完成`)
      })
      .catch(async (error) => {
        console.error(`❌ 片段 ${segment.segmentIndex} 生成失败:`, error)

        // 更新数据库状态为失败
        try {
          await prisma.videoSegment.update({
            where: { id: segment.id },
            data: {
              status: 'failed',
              updatedAt: new Date()
            }
          })
          console.log(`📝 已更新片段 ${segment.segmentIndex} 状态为失败`)
        } catch (dbError) {
          console.error(`❌ 更新片段状态失败:`, dbError)
        }
      })

    return NextResponse.json({
      success: true,
      data: {
        message: `片段 ${segment.segmentIndex} 开始生成`,
        segmentId: segment.id,
        segmentIndex: segment.segmentIndex,
        title: segment.title
      }
    })

  } catch (error) {
    console.error('生成单个视频片段失败:', error)
    return NextResponse.json(
      { success: false, error: '生成视频片段失败' },
      { status: 500 }
    )
  }
}

// 异步生成单个片段
async function generateSingleSegmentAsync(segment: any, modelId?: string) {
  try {
    console.log(`🎬 开始异步生成片段: ${segment.title}`)

    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    })

    if (!aiConfig) {
      throw new Error('请先配置AI模型')
    }

    // 调用简化的视频生成函数
    const videoResult = await generateSingleSegmentSimplified(aiConfig, segment, modelId)

    // 根据生成结果更新片段信息
    const updateData: any = {
      duration: videoResult.duration,
      metadata: JSON.stringify({
        ...JSON.parse(segment.metadata || '{}'),
        generatedAt: new Date().toISOString(),
        optimizedPrompt: videoResult.optimizedPrompt
      })
    }

    if (videoResult.videoUrl) {
      updateData.status = 'completed'
      updateData.videoUrl = videoResult.videoUrl
      updateData.thumbnailUrl = videoResult.thumbnailUrl
    } else {
      updateData.status = 'failed'
    }

    await prisma.videoSegment.update({
      where: { id: segment.id },
      data: updateData
    })

    console.log(`✅ 片段 ${segment.segmentIndex} 异步生成完成`)

  } catch (error) {
    console.error(`❌ 片段异步生成失败:`, error)

    // 更新片段状态为失败
    await prisma.videoSegment.update({
      where: { id: segment.id },
      data: {
        status: 'failed',
        metadata: JSON.stringify({
          ...JSON.parse(segment.metadata || '{}'),
          error: error instanceof Error ? error.message : '生成失败',
          failedAt: new Date().toISOString()
        })
      }
    })
  }
}

// 简化版的单个片段生成函数
async function generateSingleSegmentSimplified(aiConfig: any, segment: any, modelId?: string) {
  try {
    console.log(`🎬 开始生成片段: ${segment.title}`)

    // 1. 获取视频生成模型配置
    let videoModel
    if (modelId) {
      // 如果指定了模型ID，使用该模型
      videoModel = await prisma.aIConfig.findUnique({
        where: { id: modelId }
      })

      if (!videoModel) {
        throw new Error(`指定的模型 ${modelId} 不存在`)
      }

      if (!videoModel.enabled) {
        throw new Error(`指定的模型 ${videoModel.name} 未启用`)
      }

      if (!videoModel.supportsVideo && !videoModel.supportsImageToVideo) {
        throw new Error(`指定的模型 ${videoModel.name} 不支持视频生成`)
      }

      console.log(`🎯 使用用户选择的模型: ${videoModel.name} (${videoModel.provider})`)
    } else {
      // 如果没有指定模型ID，使用第一个启用的视频模型作为后备
      videoModel = await prisma.aIConfig.findFirst({
        where: {
          enabled: true,
          OR: [
            { supportsVideo: true },
            { supportsImageToVideo: true }
          ]
        }
      })

      if (!videoModel) {
        throw new Error('未找到可用的视频生成模型')
      }

      console.log(`⚠️ 未指定模型，使用默认模型: ${videoModel.name} (${videoModel.provider})`)
    }

    // 2. 优化提示词（可选）
    let optimizedPrompt = segment.prompt
    const deepSeekConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'deepseek',
        enabled: true
      }
    })

    if (deepSeekConfig) {
      try {
        const deepSeekClient = new DeepSeekClient(deepSeekConfig)
        const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：

片段标题：${segment.title}
片段描述：${segment.description}
原始提示词：${segment.prompt}

请按照以下要求优化：
1. 确保描述具体且可视化
2. 添加适合的镜头运动和角度
3. 强调画面质量和风格
4. 控制在100字以内

优化后的提示词：`

        optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800)
        console.log(`✅ 提示词优化完成`)
      } catch (error) {
        console.warn('提示词优化失败，使用原始提示词:', error.message)
      }
    }

    // 3. 调用视频生成API
    const videoResult = await callVideoAPI(videoModel, optimizedPrompt.trim(), segment)

    return {
      videoUrl: videoResult.videoUrl,
      thumbnailUrl: videoResult.thumbnailUrl,
      optimizedPrompt: optimizedPrompt.trim(),
      duration: videoResult.duration || segment.duration || 5,
      status: videoResult.status || 'completed'
    }

  } catch (error) {
    console.error('视频生成失败:', error)
    return {
      videoUrl: null,
      thumbnailUrl: null,
      optimizedPrompt: segment.prompt,
      duration: segment.duration || 5,
      status: 'failed',
      error: error.message
    }
  }
}

// 简化版的视频API调用函数
async function callVideoAPI(videoModel: any, prompt: string, segment: any) {
  try {
    console.log(`🎬 调用${videoModel.provider}视频API`)

    // 根据不同的提供商调用相应的API
    if (videoModel.provider === 'doubao') {
      return await callDoubaoVideoAPISimplified(videoModel, prompt, segment)
    } else if (videoModel.provider === 'minimax') {
      return await callMinimaxVideoAPISimplified(videoModel, prompt, segment)
    } else if (videoModel.provider === 'tongyi') {
      return await callTongyiVideoAPISimplified(videoModel, prompt, segment)
    } else {
      // 对于其他提供商，返回模拟结果
      console.warn(`暂不支持${videoModel.provider}提供商，返回模拟结果`)
      return {
        videoUrl: null,
        thumbnailUrl: null,
        duration: segment.duration || 5,
        status: 'pending'
      }
    }
  } catch (error) {
    console.error('视频API调用失败:', error)
    throw error
  }
}

// 简化版豆包视频API调用
async function callDoubaoVideoAPISimplified(config: any, prompt: string, segment: any) {
  try {
    console.log(`🎬 调用豆包视频API，片段: ${segment.segmentIndex}`)

    // 构建请求内容
    const content = [
      {
        type: "text",
        text: prompt
      }
    ]

    // 创建视频生成任务
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: config.model,
        content: content
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`豆包API调用失败: ${response.status} ${errorText}`)
      throw new Error(`豆包视频生成失败: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('豆包API响应:', result)

    // 豆包API返回的是 id 字段，不是 task_id
    const taskId = result.id || result.task_id
    if (taskId) {
      // 开始轮询任务状态
      const videoResult = await pollDoubaoTaskStatus(config.apiKey, taskId, segment)
      return videoResult
    } else {
      throw new Error('豆包API未返回任务ID')
    }

  } catch (error) {
    console.error('豆包视频API调用失败:', error)
    throw error
  }
}

// 轮询豆包任务状态
async function pollDoubaoTaskStatus(apiKey: string, taskId: string, segment: any) {
  const maxAttempts = 60 // 最多轮询60次（10分钟）
  const pollInterval = 10000 // 10秒轮询一次

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 轮询豆包任务状态 (${attempt}/${maxAttempts}): ${taskId}`)

      const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`轮询失败: ${response.status}`)
      }

      const result = await response.json()
      console.log(`📊 任务状态: ${result.status}`)

      // 豆包API的实际响应格式
      if (result.status === 'succeeded' && result.content?.video_url) {
        console.log(`✅ 豆包视频生成完成: ${result.content.video_url}`)
        return {
          videoUrl: result.content.video_url,
          thumbnailUrl: result.content.thumbnail_url || null,
          duration: segment.duration || 5,
          status: 'completed'
        }
      } else if (result.status === 'failed') {
        throw new Error(`豆包视频生成失败: ${result.error || '未知错误'}`)
      }

      // 如果还在处理中，等待后继续轮询
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }

    } catch (error) {
      console.error(`轮询第${attempt}次失败:`, error)
      if (attempt === maxAttempts) {
        throw error
      }
    }
  }

  throw new Error('豆包视频生成超时')
}

// 简化版MiniMax视频API调用（占位符）
async function callMinimaxVideoAPISimplified(config: any, prompt: string, segment: any) {
  console.warn('MiniMax视频生成暂未实现，返回模拟结果')
  return {
    videoUrl: null,
    thumbnailUrl: null,
    duration: segment.duration || 5,
    status: 'pending'
  }
}

// 简化版通义万相视频API调用（占位符）
async function callTongyiVideoAPISimplified(config: any, prompt: string, segment: any) {
  console.warn('通义万相视频生成暂未实现，返回模拟结果')
  return {
    videoUrl: null,
    thumbnailUrl: null,
    duration: segment.duration || 5,
    status: 'pending'
  }
}
