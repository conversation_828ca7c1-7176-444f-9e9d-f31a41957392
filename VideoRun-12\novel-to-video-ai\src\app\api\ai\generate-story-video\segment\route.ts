import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// POST - 生成单个视频片段（内部API）
export async function POST(request: NextRequest) {
  try {
    const { segmentId, modelId } = await request.json()

    console.log('🎬 内部API：生成单个视频片段')
    console.log('📋 参数:', { segmentId, modelId })

    if (!segmentId) {
      return NextResponse.json(
        { success: false, error: '缺少片段ID' },
        { status: 400 }
      )
    }

    // 获取片段信息
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId },
      include: {
        storyVideo: {
          include: {
            episode: {
              include: {
                project: true
              }
            }
          }
        }
      }
    })

    if (!segment) {
      return NextResponse.json(
        { success: false, error: '片段不存在' },
        { status: 404 }
      )
    }

    console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`)

    // 更新片段状态为生成中
    await prisma.videoSegment.update({
      where: { id: segmentId },
      data: { 
        status: 'generating',
        updatedAt: new Date()
      }
    })

    // 这里应该调用实际的视频生成逻辑
    // 为了演示，我们模拟一个生成过程
    setTimeout(async () => {
      try {
        // 模拟生成成功
        await prisma.videoSegment.update({
          where: { id: segmentId },
          data: { 
            status: 'completed',
            videoUrl: `https://example.com/video/${segmentId}.mp4`,
            thumbnailUrl: `https://example.com/thumbnail/${segmentId}.jpg`,
            duration: 8,
            updatedAt: new Date()
          }
        })
        console.log(`✅ 片段 ${segment.segmentIndex} 模拟生成完成`)
      } catch (error) {
        console.error(`❌ 片段 ${segment.segmentIndex} 生成失败:`, error)
        await prisma.videoSegment.update({
          where: { id: segmentId },
          data: { 
            status: 'failed',
            updatedAt: new Date()
          }
        })
      }
    }, 5000) // 5秒后完成

    return NextResponse.json({
      success: true,
      data: {
        message: `片段 ${segment.segmentIndex} 开始生成`,
        segmentId: segment.id,
        segmentIndex: segment.segmentIndex,
        title: segment.title
      }
    })

  } catch (error) {
    console.error('生成单个视频片段失败:', error)
    return NextResponse.json(
      { success: false, error: '生成视频片段失败' },
      { status: 500 }
    )
  }
}
