import { AIConfig } from '@/types'

// SkyReels-V2 API响应类型
export interface SkyReelsTaskResponse {
  task_id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  message: string
  video_path?: string
  error?: string
}

export interface SkyReelsStatusResponse {
  task_id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress: number
  message: string
  video_path?: string
  error?: string
}

export interface SkyReelsHealthResponse {
  status: 'healthy' | 'unhealthy'
  model_loaded: boolean
  timestamp: string
}

export interface SkyReelsGenerationParams {
  prompt: string
  num_frames?: number
  guidance_scale?: number
  seed?: number
  fps?: number
  resolution?: string
}

// SkyReels-V2 API客户端
export class SkyReelsClient {
  private baseUrl: string
  private apiKey: string
  private model: string

  constructor(config: AIConfig) {
    // SkyReels是本地API，apiKey用作baseUrl
    this.baseUrl = config.apiKey || 'http://localhost:8000'
    this.apiKey = config.apiKey
    this.model = config.model || 'SkyReels-V2-DF-1.3B-540P'
  }

  // 测试API连接
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data: SkyReelsHealthResponse = await response.json()
      return data.status === 'healthy' && data.model_loaded
    } catch (error) {
      console.error('SkyReels连接测试失败:', error)
      return false
    }
  }

  // 生成视频
  async generateVideo(params: SkyReelsGenerationParams): Promise<SkyReelsTaskResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: params.prompt,
          num_frames: params.num_frames || 257, // 默认10秒视频
          guidance_scale: params.guidance_scale || 6.0,
          seed: params.seed,
          fps: params.fps || 24,
          resolution: params.resolution || '540P',
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data: SkyReelsTaskResponse = await response.json()
      return data
    } catch (error) {
      console.error('SkyReels视频生成失败:', error)
      throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 查询任务状态
  async getTaskStatus(taskId: string): Promise<SkyReelsStatusResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/status/${taskId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data: SkyReelsStatusResponse = await response.json()
      return data
    } catch (error) {
      console.error('SkyReels状态查询失败:', error)
      throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 下载视频
  async downloadVideo(taskId: string): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/download/${taskId}`, {
        method: 'GET',
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('SkyReels视频下载失败:', error)
      throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 等待任务完成并返回视频URL
  async generateAndWait(
    params: SkyReelsGenerationParams,
    maxWaitTime: number = 1800000, // 30分钟
    pollInterval: number = 5000 // 5秒
  ): Promise<string> {
    console.log('🎬 开始SkyReels视频生成...')
    
    // 开始生成
    const task = await this.generateVideo(params)
    console.log(`📝 任务已创建: ${task.task_id}`)

    const startTime = Date.now()
    
    // 轮询状态直到完成
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getTaskStatus(task.task_id)
      console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`)

      if (status.status === 'completed') {
        console.log('✅ 视频生成完成!')
        return status.video_path || ''
      } else if (status.status === 'failed') {
        throw new Error(`视频生成失败: ${status.error || '未知错误'}`)
      }

      // 等待下次轮询
      await new Promise(resolve => setTimeout(resolve, pollInterval))
    }

    throw new Error('视频生成超时')
  }

  // 获取所有任务列表
  async getTasks(): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/tasks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return data.tasks || []
    } catch (error) {
      console.error('SkyReels任务列表获取失败:', error)
      throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

// AI服务错误类
export class SkyReelsServiceError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message)
    this.name = 'SkyReelsServiceError'
  }
}
