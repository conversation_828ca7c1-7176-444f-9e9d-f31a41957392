// 测试角色API
async function testCharactersAPI() {
  try {
    console.log('测试角色API...');
    
    const response = await fetch('http://localhost:3000/api/characters?projectId=cmc5yxtyl0000vmcsfqkr7qoi');
    
    console.log('响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API响应成功');
      console.log('角色数量:', data.data?.length || 0);
      
      if (data.data && data.data.length > 0) {
        console.log('\n角色列表:');
        data.data.forEach((character, index) => {
          console.log(`${index + 1}. ${character.name}`);
          console.log(`   身份: ${character.identity || '未设置'}`);
          console.log(`   一致性评分: ${character.consistencyScore || '未设置'}`);
          console.log(`   详细DNA: ${character.detailedDNA ? '已设置' : '未设置'}`);
          console.log(`   参考图像: ${character.referenceImages ? '已设置' : '未设置'}`);
          console.log('');
        });
      }
    } else {
      const errorText = await response.text();
      console.log('❌ API响应失败:', errorText);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testCharactersAPI();
