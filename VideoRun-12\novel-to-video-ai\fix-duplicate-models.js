const { PrismaClient } = require('@prisma/client');

async function fixDuplicateModels() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧹 清理重复的豆包模型配置...');
    
    // 1. 删除所有豆包配置
    const deleteResult = await prisma.aIConfig.deleteMany({
      where: {
        provider: 'doubao'
      }
    });
    
    console.log(`🗑️ 已删除 ${deleteResult.count} 个旧配置`);
    
    // 2. 只添加3个清晰命名的配置
    const correctModels = [
      {
        provider: 'doubao',
        model: 'ep-20250624192345-5ccwj',
        name: '豆包T2V (文生视频)',
        description: '豆包文生视频模型，支持纯文本生成5-8秒视频，适合创意场景和剧情动画',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624195026-qjsmk',
        name: '豆包I2V (图生视频)',
        description: '豆包图生视频模型，支持基于图像生成5-8秒视频，适合角色动作和场景动画',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: true,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624192235-zttm6',
        name: '豆包Pro (专业版)',
        description: '豆包专业版视频生成模型，支持高质量文生视频，已通过API连接测试',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      }
    ];
    
    // 3. 添加清晰命名的配置
    for (const model of correctModels) {
      await prisma.aIConfig.create({ data: model });
      console.log(`✅ 已添加: ${model.name}`);
    }
    
    console.log('\n🎉 豆包模型配置清理完成！');
    console.log('现在下拉菜单中的选项将是：');
    console.log('1. 豆包T2V (文生视频) - 用于纯文本生成视频');
    console.log('2. 豆包I2V (图生视频) - 用于基于图像生成视频');
    console.log('3. 豆包Pro (专业版) - 用于高质量视频生成');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDuplicateModels();
