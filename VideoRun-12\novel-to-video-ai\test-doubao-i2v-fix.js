const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoI2VFix() {
  try {
    console.log('🔧 测试豆包图生视频API修复...\n');
    
    // 1. 检查I2V模型配置
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    if (!i2vModel) {
      console.log('❌ 未找到豆包I2V模型配置');
      return;
    }
    
    console.log('✅ 找到豆包I2V模型配置:');
    console.log(`   名称: ${i2vModel.name}`);
    console.log(`   模型: ${i2vModel.model}`);
    console.log(`   启用状态: ${i2vModel.enabled}`);
    console.log(`   API密钥: ${i2vModel.apiKey ? '已配置' : '未配置'}`);
    
    if (!i2vModel.enabled || !i2vModel.apiKey) {
      console.log('⚠️ 模型未启用或API密钥未配置，无法测试');
      return;
    }
    
    // 2. 检查角色参考图像
    console.log('\n🖼️ 检查角色参考图像...');
    
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 1
    });
    
    if (charactersWithImages.length === 0) {
      console.log('❌ 没有找到有参考图像的角色，无法测试图生视频');
      return;
    }
    
    const character = charactersWithImages[0];
    console.log(`✅ 找到角色: ${character.name}`);
    
    let referenceImageUrl = null;
    try {
      const images = JSON.parse(character.generatedImages);
      referenceImageUrl = images.front || images.side || images.back;
      console.log(`📸 参考图像URL: ${referenceImageUrl ? '有' : '无'}`);
    } catch (e) {
      console.log('❌ 解析角色图像失败:', e.message);
      return;
    }
    
    if (!referenceImageUrl) {
      console.log('❌ 角色没有可用的参考图像');
      return;
    }
    
    // 3. 测试API调用格式
    console.log('\n🧪 测试豆包I2V API调用格式...');
    
    const testPrompt = '角色在雪夜中缓缓转身，表情严肃警惕，眼神坚定';
    
    // 构建修复后的API请求
    const content = [
      {
        type: "image_url",
        image_url: {
          url: referenceImageUrl
        }
      },
      {
        type: "text",
        text: testPrompt
      }
    ];
    
    console.log('📋 API请求格式:');
    console.log('   端点:', i2vModel.model);
    console.log('   内容格式:', JSON.stringify(content, null, 2));
    
    // 4. 实际API调用测试
    console.log('\n📡 执行API调用测试...');
    
    try {
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${i2vModel.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: i2vModel.model,
          content: content
        })
      });
      
      console.log(`📊 API响应状态: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ API调用成功!');
        console.log('📋 响应数据:', JSON.stringify(result, null, 2));
        
        // 检查任务ID
        const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
        if (taskId) {
          console.log(`🎯 任务ID: ${taskId}`);
          console.log('✅ 图生视频任务创建成功！');
        } else {
          console.log('⚠️ 响应中未找到任务ID');
        }
        
      } else {
        const errorText = await response.text();
        console.log('❌ API调用失败:');
        console.log(`   状态码: ${response.status}`);
        console.log(`   错误信息: ${errorText}`);
        
        // 分析错误原因
        if (errorText.includes('image to video models require image in content')) {
          console.log('🔍 错误分析: 内容格式仍然不正确');
        } else if (errorText.includes('authentication')) {
          console.log('🔍 错误分析: API密钥认证问题');
        } else if (errorText.includes('model')) {
          console.log('🔍 错误分析: 模型配置问题');
        } else {
          console.log('🔍 错误分析: 其他未知问题');
        }
      }
      
    } catch (error) {
      console.error('❌ API调用异常:', error.message);
    }
    
    // 5. 对比修复前后的格式
    console.log('\n📊 修复前后格式对比:');
    
    console.log('修复前的错误格式:');
    console.log(`   [
     { type: "text", text: "基于提供的角色图像生成视频：..." },
     { type: "image_url", image_url: { url: "..." } }
   ]`);
   
    console.log('\n修复后的正确格式:');
    console.log(`   [
     { type: "image_url", image_url: { url: "..." } },
     { type: "text", text: "..." }
   ]`);
   
    console.log('\n🔧 关键修复点:');
    console.log('   1. 图像内容放在第一位');
    console.log('   2. 移除了"基于提供的角色图像生成视频"前缀');
    console.log('   3. 使用正确的端点ID: ep-20250624013749-dbrbn');
    
    // 6. 使用建议
    console.log('\n💡 使用建议:');
    console.log('   - 确保角色有高质量的参考图像');
    console.log('   - 提示词要简洁明确，描述具体动作');
    console.log('   - 图生视频适合角色特写和情感表达');
    console.log('   - 可以与文生视频模型混合使用');
    
    console.log('\n🎬 豆包图生视频API修复测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoI2VFix();
