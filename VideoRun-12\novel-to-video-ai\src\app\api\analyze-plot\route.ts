import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { DeepSeekClient, AIServiceError, handleAIRequest } from '@/lib/ai'

// 分析单个剧集的剧情信息
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { episodeId } = body

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '剧集ID不能为空' },
        { status: 400 }
      )
    }

    // 获取剧集信息
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      include: {
        plotInfo: true,
      },
    })

    if (!episode) {
      return NextResponse.json(
        { success: false, error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 获取全局AI配置
    const aiConfig = await prisma.aIConfig.findUnique({
      where: { id: 'global' },
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型' },
        { status: 400 }
      )
    }

    // 检查是否已经分析过
    if (episode.plotInfo) {
      return NextResponse.json({
        success: true,
        data: episode.plotInfo,
        message: '剧情信息已存在',
      })
    }

    try {
      // 创建AI客户端
      const aiClient = new DeepSeekClient(aiConfig)

      // 调用AI分析剧情
      const plotAnalysis = await handleAIRequest(() =>
        aiClient.analyzePlot(episode.content)
      )

      // 保存剧情信息
      const plotInfo = await prisma.plotInfo.create({
        data: {
          episodeId: episodeId,
          characters: plotAnalysis.characters,
          scenes: plotAnalysis.scenes,
          events: plotAnalysis.events,
        },
      })

      // 更新剧集状态
      await prisma.episode.update({
        where: { id: episodeId },
        data: { status: 'analyzed' },
      })

      return NextResponse.json({
        success: true,
        data: plotInfo,
        message: '剧情信息提取完成',
      })
    } catch (error) {
      if (error instanceof AIServiceError) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: error.statusCode }
        )
      }

      throw error
    }
  } catch (error) {
    console.error('剧情分析失败:', error)
    return NextResponse.json(
      { success: false, error: '剧情分析失败，请重试' },
      { status: 500 }
    )
  }
}

// 获取剧情信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '剧集ID不能为空' },
        { status: 400 }
      )
    }

    const plotInfo = await prisma.plotInfo.findUnique({
      where: { episodeId },
      include: {
        episode: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
      },
    })

    if (!plotInfo) {
      return NextResponse.json(
        { success: false, error: '剧情信息不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: plotInfo,
    })
  } catch (error) {
    console.error('获取剧情信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取剧情信息失败' },
      { status: 500 }
    )
  }
}
