const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkErrors() {
  try {
    const segments = await prisma.videoSegment.findMany({
      where: { status: 'failed' },
      orderBy: { updatedAt: 'desc' },
      take: 3
    });

    console.log('最新失败记录:');
    segments.forEach((segment, i) => {
      console.log('片段' + (i+1) + ':');
      if (segment.metadata) {
        try {
          const meta = JSON.parse(segment.metadata);
          console.log('错误:', meta.error);
        } catch (e) {
          console.log('原始:', segment.metadata);
        }
      }
      console.log('---');
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkErrors();
