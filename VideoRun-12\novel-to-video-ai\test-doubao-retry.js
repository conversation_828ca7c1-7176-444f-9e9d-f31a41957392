// 测试豆包API重试机制
async function testDoubaoWithRetry() {
  try {
    console.log('🔄 测试豆包API重试机制...');
    
    const modelName = 'doubao-seedance-1-0-pro-250528';
    const maxRetries = 3;
    const retryDelay = 2000; // 2秒
    
    console.log('\n📝 测试配置:');
    console.log('   模型名称:', modelName);
    console.log('   最大重试次数:', maxRetries);
    console.log('   重试间隔:', retryDelay / 1000, '秒');
    
    // 测试函数
    async function testConnection(attempt = 1) {
      console.log(`\n🧪 第 ${attempt} 次尝试连接...`);
      
      try {
        const response = await fetch('http://localhost:3000/api/models/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            provider: 'doubao',
            model: modelName,
            apiKey: 'test-placeholder-key' // 使用测试密钥
          })
        });
        
        console.log('📊 响应状态:', response.status);
        
        const result = await response.json();
        
        if (result.success) {
          console.log('✅ 连接成功！');
          return { success: true, result };
        } else {
          console.log('❌ 连接失败:', result.error);
          
          // 检查错误类型
          if (result.error.includes('internal error')) {
            console.log('🔍 检测到内部错误，这通常是临时问题');
            return { success: false, error: result.error, retryable: true };
          } else if (result.error.includes('API key')) {
            console.log('🔑 API密钥问题（这是预期的，因为使用测试密钥）');
            return { success: false, error: result.error, retryable: false };
          } else if (result.error.includes('does not exist')) {
            console.log('🚫 模型不存在或无权访问');
            return { success: false, error: result.error, retryable: false };
          } else {
            console.log('❓ 其他错误类型');
            return { success: false, error: result.error, retryable: true };
          }
        }
      } catch (error) {
        console.log('💥 请求异常:', error.message);
        return { success: false, error: error.message, retryable: true };
      }
    }
    
    // 执行重试逻辑
    let lastResult = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      lastResult = await testConnection(attempt);
      
      if (lastResult.success) {
        console.log('\n🎉 测试成功完成！');
        break;
      }
      
      if (!lastResult.retryable) {
        console.log('\n⏹️ 错误不可重试，停止尝试');
        break;
      }
      
      if (attempt < maxRetries) {
        console.log(`⏳ 等待 ${retryDelay / 1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
    
    // 分析结果
    console.log('\n📊 测试结果分析:');
    
    if (lastResult.success) {
      console.log('✅ 状态: 连接成功');
    } else {
      console.log('❌ 状态: 连接失败');
      console.log('🔍 最终错误:', lastResult.error);
      
      if (lastResult.error.includes('internal error')) {
        console.log('\n💡 内部错误解决建议:');
        console.log('   1. 这通常是火山引擎服务端的临时问题');
        console.log('   2. 建议等待几分钟后重试');
        console.log('   3. 检查火山引擎服务状态页面');
        console.log('   4. 如果持续出现，联系火山引擎技术支持');
        
      } else if (lastResult.error.includes('API key')) {
        console.log('\n💡 API密钥问题解决建议:');
        console.log('   1. 确保使用有效的API密钥');
        console.log('   2. 检查密钥权限设置');
        console.log('   3. 确认密钥未过期');
        
      } else if (lastResult.error.includes('does not exist')) {
        console.log('\n💡 模型权限问题解决建议:');
        console.log('   1. 在火山方舟控制台开通模型权限');
        console.log('   2. 确认账户类型符合要求');
        console.log('   3. 等待权限审核通过');
      }
    }
    
    // 显示服务状态检查建议
    console.log('\n🔍 服务状态检查:');
    console.log('   1. 火山引擎控制台: https://console.volcengine.com/');
    console.log('   2. 火山方舟服务状态: 检查是否有维护公告');
    console.log('   3. 技术支持: 如果问题持续，可联系技术支持');
    
    // 显示替代方案
    console.log('\n🔄 临时替代方案:');
    console.log('   1. 使用其他可用的AI模型（如DeepSeek）');
    console.log('   2. 等待服务恢复后重试');
    console.log('   3. 检查是否有模型更新或替代版本');
    
    // 显示监控建议
    console.log('\n📈 监控建议:');
    console.log('   1. 实现自动重试机制');
    console.log('   2. 设置服务状态监控');
    console.log('   3. 配置多个备用AI服务');
    console.log('   4. 记录错误日志用于分析');
    
    console.log('\n🎯 总结:');
    if (lastResult.error && lastResult.error.includes('internal error')) {
      console.log('   ⚠️ 检测到火山引擎内部错误');
      console.log('   🕐 建议等待服务恢复');
      console.log('   🔄 可以稍后重试');
    } else {
      console.log('   ✅ 技术集成正确');
      console.log('   🔑 主要是权限或配置问题');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoWithRetry();
