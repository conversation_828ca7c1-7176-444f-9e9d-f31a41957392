/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlbuilder";
exports.ids = ["vendor-chunks/xmlbuilder"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlbuilder/lib/Utility.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/Utility.js ***!
  \************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/Utility.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLAttribute.js ***!
  \*****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute;\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.options = parent.options;\n      this.stringify = parent.stringify;\n      this.parent = parent;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing attribute value. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.attName(name);\n      this.value = this.stringify.attValue(value);\n    }\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.set(options).attribute(this);\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCData.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.set(options).cdata(this);\n    };\n\n    return XMLCData;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLComment.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLComment, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.text = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.set(options).comment(this);\n    };\n\n    return XMLComment;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDAttList.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.eleName(elementName);\n      this.attributeName = this.stringify.attName(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdAttList(this);\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDElement.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.eleName(name);\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdElement(this);\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDEntity.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.eleName(name);\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdEntity(this);\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDNotation.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.eleName(name);\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.set(options).dtdNotation(this);\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDeclaration.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.set(options).declaration(this);\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocType.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var ref, ref1;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.name = \"!DOCTYPE\";\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref = pubID, pubID = ref.pubID, sysID = ref.sysID;\n      }\n      if (sysID == null) {\n        ref1 = [pubID, sysID], sysID = ref1[0], pubID = ref1[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.set(options).docType(this);\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocument.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\").isPlainObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n      this.isDocument = true;\n    }\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer.set(writerOptions);\n      }\n      return writer.document(this);\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.set(options).document(this);\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocumentCB.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter(options);\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter(writerOptions);\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1, ref2;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.dtdElement.apply(this, arguments);\n      } else {\n        return this.node(name, attributes, text);\n      }\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.currentNode.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node instanceof XMLElement) {\n          this.root = node;\n        }\n        this.onData(this.writer.openNode(node, this.currentLevel), this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      if (!node.isClosed) {\n        this.onData(this.writer.closeNode(node, this.currentLevel), this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode instanceof XMLDocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDummy.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.isDummy = true;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRHVtbXkuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLDBCQUEwQiwyREFBMkQsa0JBQWtCLDRCQUE0QixtQ0FBbUMsOEJBQThCLG9DQUFvQyxlQUFlO0FBQzlSLGdCQUFnQjs7QUFFaEIsWUFBWSxtQkFBTyxDQUFDLGlFQUFXOztBQUUvQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxub2RlX21vZHVsZXNcXHhtbGJ1aWxkZXJcXGxpYlxcWE1MRHVtbXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTER1bW15LCBYTUxOb2RlLFxuICAgIGV4dGVuZCA9IGZ1bmN0aW9uKGNoaWxkLCBwYXJlbnQpIHsgZm9yICh2YXIga2V5IGluIHBhcmVudCkgeyBpZiAoaGFzUHJvcC5jYWxsKHBhcmVudCwga2V5KSkgY2hpbGRba2V5XSA9IHBhcmVudFtrZXldOyB9IGZ1bmN0aW9uIGN0b3IoKSB7IHRoaXMuY29uc3RydWN0b3IgPSBjaGlsZDsgfSBjdG9yLnByb3RvdHlwZSA9IHBhcmVudC5wcm90b3R5cGU7IGNoaWxkLnByb3RvdHlwZSA9IG5ldyBjdG9yKCk7IGNoaWxkLl9fc3VwZXJfXyA9IHBhcmVudC5wcm90b3R5cGU7IHJldHVybiBjaGlsZDsgfSxcbiAgICBoYXNQcm9wID0ge30uaGFzT3duUHJvcGVydHk7XG5cbiAgWE1MTm9kZSA9IHJlcXVpcmUoJy4vWE1MTm9kZScpO1xuXG4gIG1vZHVsZS5leHBvcnRzID0gWE1MRHVtbXkgPSAoZnVuY3Rpb24oc3VwZXJDbGFzcykge1xuICAgIGV4dGVuZChYTUxEdW1teSwgc3VwZXJDbGFzcyk7XG5cbiAgICBmdW5jdGlvbiBYTUxEdW1teShwYXJlbnQpIHtcbiAgICAgIFhNTER1bW15Ll9fc3VwZXJfXy5jb25zdHJ1Y3Rvci5jYWxsKHRoaXMsIHBhcmVudCk7XG4gICAgICB0aGlzLmlzRHVtbXkgPSB0cnVlO1xuICAgIH1cblxuICAgIFhNTER1bW15LnByb3RvdHlwZS5jbG9uZSA9IGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIE9iamVjdC5jcmVhdGUodGhpcyk7XG4gICAgfTtcblxuICAgIFhNTER1bW15LnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uKG9wdGlvbnMpIHtcbiAgICAgIHJldHVybiAnJztcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTER1bW15O1xuXG4gIH0pKFhNTE5vZGUpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLElement.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLAttribute, XMLElement, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.eleName(name);\n      this.attributes = {};\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.isDocument) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n      }\n    }\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attributes = {};\n      ref1 = this.attributes;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attributes[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (!this.options.skipNullAttributes || (value != null)) {\n          this.attributes[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, i, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (i = 0, len = name.length; i < len; i++) {\n          attName = name[i];\n          delete this.attributes[attName];\n        }\n      } else {\n        delete this.attributes[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.set(options).element(this);\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNode.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNode, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isEmpty = ref.isEmpty, getValue = ref.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.children = [];\n      if (!XMLElement) {\n        XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n        XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n        XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n        XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n        XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n        XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n        XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n        XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n        XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n      }\n    }\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref1, ref2, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref1 = [{}, null], attributes = ref1[0], text = ref1[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if ((isObject(val)) && (isEmpty(val))) {\n            val = null;\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            lastChild = this.element(key);\n            lastChild.element(val);\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (this.options.skipNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref1;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref1 = [])), ref1;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref1;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children[0] instanceof XMLDeclaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.doctype = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref1, ref2;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref1 = doc.children;\n      for (i = j = 0, len = ref1.length; j < len; i = ++j) {\n        child = ref1[i];\n        if (child instanceof XMLDocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref2 = doc.children;\n      for (i = k = 0, len1 = ref2.length; k < len1; i = ++k) {\n        child = ref2[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.isDocument) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i > 0 && this.parent.children[i - 1].isDummy) {\n        i = i - 1;\n      }\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      while (i < this.parent.children.length - 1 && this.parent.children[i + 1].isDummy) {\n        i = i + 1;\n      }\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref1, ref2;\n      name = name || this.name;\n      if ((name == null) && !((ref1 = this.parent) != null ? ref1.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      return this.doctype(pubID, sysID);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MTm9kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7O0FBRWhCLFFBQVEsbUJBQU8sQ0FBQyxpRUFBVzs7QUFFM0I7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLG1CQUFPLENBQUMsdUVBQWM7QUFDM0MsbUJBQW1CLG1CQUFPLENBQUMsbUVBQVk7QUFDdkMscUJBQXFCLG1CQUFPLENBQUMsdUVBQWM7QUFDM0MseUJBQXlCLG1CQUFPLENBQUMsK0VBQWtCO0FBQ25ELHFCQUFxQixtQkFBTyxDQUFDLHVFQUFjO0FBQzNDLGlCQUFpQixtQkFBTyxDQUFDLCtEQUFVO0FBQ25DLGtCQUFrQixtQkFBTyxDQUFDLGlFQUFXO0FBQ3JDLG1DQUFtQyxtQkFBTyxDQUFDLG1HQUE0QjtBQUN2RSxtQkFBbUIsbUJBQU8sQ0FBQyxtRUFBWTtBQUN2QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxTQUFTO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLDJDQUEyQyxVQUFVO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxTQUFTO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLFNBQVM7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsVUFBVTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLEdBQUc7O0FBRUgsQ0FBQyIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFx4bWxidWlsZGVyXFxsaWJcXFhNTE5vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTENEYXRhLCBYTUxDb21tZW50LCBYTUxEZWNsYXJhdGlvbiwgWE1MRG9jVHlwZSwgWE1MRHVtbXksIFhNTEVsZW1lbnQsIFhNTE5vZGUsIFhNTFByb2Nlc3NpbmdJbnN0cnVjdGlvbiwgWE1MUmF3LCBYTUxUZXh0LCBnZXRWYWx1ZSwgaXNFbXB0eSwgaXNGdW5jdGlvbiwgaXNPYmplY3QsIHJlZixcbiAgICBoYXNQcm9wID0ge30uaGFzT3duUHJvcGVydHk7XG5cbiAgcmVmID0gcmVxdWlyZSgnLi9VdGlsaXR5JyksIGlzT2JqZWN0ID0gcmVmLmlzT2JqZWN0LCBpc0Z1bmN0aW9uID0gcmVmLmlzRnVuY3Rpb24sIGlzRW1wdHkgPSByZWYuaXNFbXB0eSwgZ2V0VmFsdWUgPSByZWYuZ2V0VmFsdWU7XG5cbiAgWE1MRWxlbWVudCA9IG51bGw7XG5cbiAgWE1MQ0RhdGEgPSBudWxsO1xuXG4gIFhNTENvbW1lbnQgPSBudWxsO1xuXG4gIFhNTERlY2xhcmF0aW9uID0gbnVsbDtcblxuICBYTUxEb2NUeXBlID0gbnVsbDtcblxuICBYTUxSYXcgPSBudWxsO1xuXG4gIFhNTFRleHQgPSBudWxsO1xuXG4gIFhNTFByb2Nlc3NpbmdJbnN0cnVjdGlvbiA9IG51bGw7XG5cbiAgWE1MRHVtbXkgPSBudWxsO1xuXG4gIG1vZHVsZS5leHBvcnRzID0gWE1MTm9kZSA9IChmdW5jdGlvbigpIHtcbiAgICBmdW5jdGlvbiBYTUxOb2RlKHBhcmVudCkge1xuICAgICAgdGhpcy5wYXJlbnQgPSBwYXJlbnQ7XG4gICAgICBpZiAodGhpcy5wYXJlbnQpIHtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gdGhpcy5wYXJlbnQub3B0aW9ucztcbiAgICAgICAgdGhpcy5zdHJpbmdpZnkgPSB0aGlzLnBhcmVudC5zdHJpbmdpZnk7XG4gICAgICB9XG4gICAgICB0aGlzLmNoaWxkcmVuID0gW107XG4gICAgICBpZiAoIVhNTEVsZW1lbnQpIHtcbiAgICAgICAgWE1MRWxlbWVudCA9IHJlcXVpcmUoJy4vWE1MRWxlbWVudCcpO1xuICAgICAgICBYTUxDRGF0YSA9IHJlcXVpcmUoJy4vWE1MQ0RhdGEnKTtcbiAgICAgICAgWE1MQ29tbWVudCA9IHJlcXVpcmUoJy4vWE1MQ29tbWVudCcpO1xuICAgICAgICBYTUxEZWNsYXJhdGlvbiA9IHJlcXVpcmUoJy4vWE1MRGVjbGFyYXRpb24nKTtcbiAgICAgICAgWE1MRG9jVHlwZSA9IHJlcXVpcmUoJy4vWE1MRG9jVHlwZScpO1xuICAgICAgICBYTUxSYXcgPSByZXF1aXJlKCcuL1hNTFJhdycpO1xuICAgICAgICBYTUxUZXh0ID0gcmVxdWlyZSgnLi9YTUxUZXh0Jyk7XG4gICAgICAgIFhNTFByb2Nlc3NpbmdJbnN0cnVjdGlvbiA9IHJlcXVpcmUoJy4vWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uJyk7XG4gICAgICAgIFhNTER1bW15ID0gcmVxdWlyZSgnLi9YTUxEdW1teScpO1xuICAgICAgfVxuICAgIH1cblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmVsZW1lbnQgPSBmdW5jdGlvbihuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KSB7XG4gICAgICB2YXIgY2hpbGROb2RlLCBpdGVtLCBqLCBrLCBrZXksIGxhc3RDaGlsZCwgbGVuLCBsZW4xLCByZWYxLCByZWYyLCB2YWw7XG4gICAgICBsYXN0Q2hpbGQgPSBudWxsO1xuICAgICAgaWYgKGF0dHJpYnV0ZXMgPT09IG51bGwgJiYgKHRleHQgPT0gbnVsbCkpIHtcbiAgICAgICAgcmVmMSA9IFt7fSwgbnVsbF0sIGF0dHJpYnV0ZXMgPSByZWYxWzBdLCB0ZXh0ID0gcmVmMVsxXTtcbiAgICAgIH1cbiAgICAgIGlmIChhdHRyaWJ1dGVzID09IG51bGwpIHtcbiAgICAgICAgYXR0cmlidXRlcyA9IHt9O1xuICAgICAgfVxuICAgICAgYXR0cmlidXRlcyA9IGdldFZhbHVlKGF0dHJpYnV0ZXMpO1xuICAgICAgaWYgKCFpc09iamVjdChhdHRyaWJ1dGVzKSkge1xuICAgICAgICByZWYyID0gW2F0dHJpYnV0ZXMsIHRleHRdLCB0ZXh0ID0gcmVmMlswXSwgYXR0cmlidXRlcyA9IHJlZjJbMV07XG4gICAgICB9XG4gICAgICBpZiAobmFtZSAhPSBudWxsKSB7XG4gICAgICAgIG5hbWUgPSBnZXRWYWx1ZShuYW1lKTtcbiAgICAgIH1cbiAgICAgIGlmIChBcnJheS5pc0FycmF5KG5hbWUpKSB7XG4gICAgICAgIGZvciAoaiA9IDAsIGxlbiA9IG5hbWUubGVuZ3RoOyBqIDwgbGVuOyBqKyspIHtcbiAgICAgICAgICBpdGVtID0gbmFtZVtqXTtcbiAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmVsZW1lbnQoaXRlbSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoaXNGdW5jdGlvbihuYW1lKSkge1xuICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmVsZW1lbnQobmFtZS5hcHBseSgpKTtcbiAgICAgIH0gZWxzZSBpZiAoaXNPYmplY3QobmFtZSkpIHtcbiAgICAgICAgZm9yIChrZXkgaW4gbmFtZSkge1xuICAgICAgICAgIGlmICghaGFzUHJvcC5jYWxsKG5hbWUsIGtleSkpIGNvbnRpbnVlO1xuICAgICAgICAgIHZhbCA9IG5hbWVba2V5XTtcbiAgICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWwpKSB7XG4gICAgICAgICAgICB2YWwgPSB2YWwuYXBwbHkoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKChpc09iamVjdCh2YWwpKSAmJiAoaXNFbXB0eSh2YWwpKSkge1xuICAgICAgICAgICAgdmFsID0gbnVsbDtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0QXR0S2V5ICYmIGtleS5pbmRleE9mKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRBdHRLZXkpID09PSAwKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmF0dHJpYnV0ZShrZXkuc3Vic3RyKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRBdHRLZXkubGVuZ3RoKSwgdmFsKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuc2VwYXJhdGVBcnJheUl0ZW1zICYmIEFycmF5LmlzQXJyYXkodmFsKSkge1xuICAgICAgICAgICAgZm9yIChrID0gMCwgbGVuMSA9IHZhbC5sZW5ndGg7IGsgPCBsZW4xOyBrKyspIHtcbiAgICAgICAgICAgICAgaXRlbSA9IHZhbFtrXTtcbiAgICAgICAgICAgICAgY2hpbGROb2RlID0ge307XG4gICAgICAgICAgICAgIGNoaWxkTm9kZVtrZXldID0gaXRlbTtcbiAgICAgICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5lbGVtZW50KGNoaWxkTm9kZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIGlmIChpc09iamVjdCh2YWwpKSB7XG4gICAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmVsZW1lbnQoa2V5KTtcbiAgICAgICAgICAgIGxhc3RDaGlsZC5lbGVtZW50KHZhbCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMuZWxlbWVudChrZXksIHZhbCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHRoaXMub3B0aW9ucy5za2lwTnVsbE5vZGVzICYmIHRleHQgPT09IG51bGwpIHtcbiAgICAgICAgbGFzdENoaWxkID0gdGhpcy5kdW1teSgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0VGV4dEtleSAmJiBuYW1lLmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydFRleHRLZXkpID09PSAwKSB7XG4gICAgICAgICAgbGFzdENoaWxkID0gdGhpcy50ZXh0KHRleHQpO1xuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0Q0RhdGFLZXkgJiYgbmFtZS5pbmRleE9mKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRDRGF0YUtleSkgPT09IDApIHtcbiAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmNkYXRhKHRleHQpO1xuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0Q29tbWVudEtleSAmJiBuYW1lLmluZGV4T2YodGhpcy5zdHJpbmdpZnkuY29udmVydENvbW1lbnRLZXkpID09PSAwKSB7XG4gICAgICAgICAgbGFzdENoaWxkID0gdGhpcy5jb21tZW50KHRleHQpO1xuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0UmF3S2V5ICYmIG5hbWUuaW5kZXhPZih0aGlzLnN0cmluZ2lmeS5jb252ZXJ0UmF3S2V5KSA9PT0gMCkge1xuICAgICAgICAgIGxhc3RDaGlsZCA9IHRoaXMucmF3KHRleHQpO1xuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm9wdGlvbnMuaWdub3JlRGVjb3JhdG9ycyAmJiB0aGlzLnN0cmluZ2lmeS5jb252ZXJ0UElLZXkgJiYgbmFtZS5pbmRleE9mKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRQSUtleSkgPT09IDApIHtcbiAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLmluc3RydWN0aW9uKG5hbWUuc3Vic3RyKHRoaXMuc3RyaW5naWZ5LmNvbnZlcnRQSUtleS5sZW5ndGgpLCB0ZXh0KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBsYXN0Q2hpbGQgPSB0aGlzLm5vZGUobmFtZSwgYXR0cmlidXRlcywgdGV4dCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGlmIChsYXN0Q2hpbGQgPT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZCBub3QgY3JlYXRlIGFueSBlbGVtZW50cyB3aXRoOiBcIiArIG5hbWUgKyBcIi4gXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBsYXN0Q2hpbGQ7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmluc2VydEJlZm9yZSA9IGZ1bmN0aW9uKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgIHZhciBjaGlsZCwgaSwgcmVtb3ZlZDtcbiAgICAgIGlmICh0aGlzLmlzUm9vdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgaW5zZXJ0IGVsZW1lbnRzIGF0IHJvb3QgbGV2ZWwuIFwiICsgdGhpcy5kZWJ1Z0luZm8obmFtZSkpO1xuICAgICAgfVxuICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkpO1xuICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5lbGVtZW50KG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpO1xuICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgcmV0dXJuIGNoaWxkO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5pbnNlcnRBZnRlciA9IGZ1bmN0aW9uKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgIHZhciBjaGlsZCwgaSwgcmVtb3ZlZDtcbiAgICAgIGlmICh0aGlzLmlzUm9vdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgaW5zZXJ0IGVsZW1lbnRzIGF0IHJvb3QgbGV2ZWwuIFwiICsgdGhpcy5kZWJ1Z0luZm8obmFtZSkpO1xuICAgICAgfVxuICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkgKyAxKTtcbiAgICAgIGNoaWxkID0gdGhpcy5wYXJlbnQuZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICAgIEFycmF5LnByb3RvdHlwZS5wdXNoLmFwcGx5KHRoaXMucGFyZW50LmNoaWxkcmVuLCByZW1vdmVkKTtcbiAgICAgIHJldHVybiBjaGlsZDtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUucmVtb3ZlID0gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgaSwgcmVmMTtcbiAgICAgIGlmICh0aGlzLmlzUm9vdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgcmVtb3ZlIHRoZSByb290IGVsZW1lbnQuIFwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG4gICAgICBpID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uaW5kZXhPZih0aGlzKTtcbiAgICAgIFtdLnNwbGljZS5hcHBseSh0aGlzLnBhcmVudC5jaGlsZHJlbiwgW2ksIGkgLSBpICsgMV0uY29uY2F0KHJlZjEgPSBbXSkpLCByZWYxO1xuICAgICAgcmV0dXJuIHRoaXMucGFyZW50O1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5ub2RlID0gZnVuY3Rpb24obmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgdmFyIGNoaWxkLCByZWYxO1xuICAgICAgaWYgKG5hbWUgIT0gbnVsbCkge1xuICAgICAgICBuYW1lID0gZ2V0VmFsdWUobmFtZSk7XG4gICAgICB9XG4gICAgICBhdHRyaWJ1dGVzIHx8IChhdHRyaWJ1dGVzID0ge30pO1xuICAgICAgYXR0cmlidXRlcyA9IGdldFZhbHVlKGF0dHJpYnV0ZXMpO1xuICAgICAgaWYgKCFpc09iamVjdChhdHRyaWJ1dGVzKSkge1xuICAgICAgICByZWYxID0gW2F0dHJpYnV0ZXMsIHRleHRdLCB0ZXh0ID0gcmVmMVswXSwgYXR0cmlidXRlcyA9IHJlZjFbMV07XG4gICAgICB9XG4gICAgICBjaGlsZCA9IG5ldyBYTUxFbGVtZW50KHRoaXMsIG5hbWUsIGF0dHJpYnV0ZXMpO1xuICAgICAgaWYgKHRleHQgIT0gbnVsbCkge1xuICAgICAgICBjaGlsZC50ZXh0KHRleHQpO1xuICAgICAgfVxuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiBjaGlsZDtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUudGV4dCA9IGZ1bmN0aW9uKHZhbHVlKSB7XG4gICAgICB2YXIgY2hpbGQ7XG4gICAgICBjaGlsZCA9IG5ldyBYTUxUZXh0KHRoaXMsIHZhbHVlKTtcbiAgICAgIHRoaXMuY2hpbGRyZW4ucHVzaChjaGlsZCk7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuY2RhdGEgPSBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgdmFyIGNoaWxkO1xuICAgICAgY2hpbGQgPSBuZXcgWE1MQ0RhdGEodGhpcywgdmFsdWUpO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5jb21tZW50ID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZDtcbiAgICAgIGNoaWxkID0gbmV3IFhNTENvbW1lbnQodGhpcywgdmFsdWUpO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5jb21tZW50QmVmb3JlID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZCwgaSwgcmVtb3ZlZDtcbiAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgcmVtb3ZlZCA9IHRoaXMucGFyZW50LmNoaWxkcmVuLnNwbGljZShpKTtcbiAgICAgIGNoaWxkID0gdGhpcy5wYXJlbnQuY29tbWVudCh2YWx1ZSk7XG4gICAgICBBcnJheS5wcm90b3R5cGUucHVzaC5hcHBseSh0aGlzLnBhcmVudC5jaGlsZHJlbiwgcmVtb3ZlZCk7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuY29tbWVudEFmdGVyID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHZhciBjaGlsZCwgaSwgcmVtb3ZlZDtcbiAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgcmVtb3ZlZCA9IHRoaXMucGFyZW50LmNoaWxkcmVuLnNwbGljZShpICsgMSk7XG4gICAgICBjaGlsZCA9IHRoaXMucGFyZW50LmNvbW1lbnQodmFsdWUpO1xuICAgICAgQXJyYXkucHJvdG90eXBlLnB1c2guYXBwbHkodGhpcy5wYXJlbnQuY2hpbGRyZW4sIHJlbW92ZWQpO1xuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLnJhdyA9IGZ1bmN0aW9uKHZhbHVlKSB7XG4gICAgICB2YXIgY2hpbGQ7XG4gICAgICBjaGlsZCA9IG5ldyBYTUxSYXcodGhpcywgdmFsdWUpO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNoaWxkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5kdW1teSA9IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIGNoaWxkO1xuICAgICAgY2hpbGQgPSBuZXcgWE1MRHVtbXkodGhpcyk7XG4gICAgICB0aGlzLmNoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgcmV0dXJuIGNoaWxkO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5pbnN0cnVjdGlvbiA9IGZ1bmN0aW9uKHRhcmdldCwgdmFsdWUpIHtcbiAgICAgIHZhciBpbnNUYXJnZXQsIGluc1ZhbHVlLCBpbnN0cnVjdGlvbiwgaiwgbGVuO1xuICAgICAgaWYgKHRhcmdldCAhPSBudWxsKSB7XG4gICAgICAgIHRhcmdldCA9IGdldFZhbHVlKHRhcmdldCk7XG4gICAgICB9XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCkge1xuICAgICAgICB2YWx1ZSA9IGdldFZhbHVlKHZhbHVlKTtcbiAgICAgIH1cbiAgICAgIGlmIChBcnJheS5pc0FycmF5KHRhcmdldCkpIHtcbiAgICAgICAgZm9yIChqID0gMCwgbGVuID0gdGFyZ2V0Lmxlbmd0aDsgaiA8IGxlbjsgaisrKSB7XG4gICAgICAgICAgaW5zVGFyZ2V0ID0gdGFyZ2V0W2pdO1xuICAgICAgICAgIHRoaXMuaW5zdHJ1Y3Rpb24oaW5zVGFyZ2V0KTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChpc09iamVjdCh0YXJnZXQpKSB7XG4gICAgICAgIGZvciAoaW5zVGFyZ2V0IGluIHRhcmdldCkge1xuICAgICAgICAgIGlmICghaGFzUHJvcC5jYWxsKHRhcmdldCwgaW5zVGFyZ2V0KSkgY29udGludWU7XG4gICAgICAgICAgaW5zVmFsdWUgPSB0YXJnZXRbaW5zVGFyZ2V0XTtcbiAgICAgICAgICB0aGlzLmluc3RydWN0aW9uKGluc1RhcmdldCwgaW5zVmFsdWUpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWx1ZSkpIHtcbiAgICAgICAgICB2YWx1ZSA9IHZhbHVlLmFwcGx5KCk7XG4gICAgICAgIH1cbiAgICAgICAgaW5zdHJ1Y3Rpb24gPSBuZXcgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uKHRoaXMsIHRhcmdldCwgdmFsdWUpO1xuICAgICAgICB0aGlzLmNoaWxkcmVuLnB1c2goaW5zdHJ1Y3Rpb24pO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmluc3RydWN0aW9uQmVmb3JlID0gZnVuY3Rpb24odGFyZ2V0LCB2YWx1ZSkge1xuICAgICAgdmFyIGNoaWxkLCBpLCByZW1vdmVkO1xuICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkpO1xuICAgICAgY2hpbGQgPSB0aGlzLnBhcmVudC5pbnN0cnVjdGlvbih0YXJnZXQsIHZhbHVlKTtcbiAgICAgIEFycmF5LnByb3RvdHlwZS5wdXNoLmFwcGx5KHRoaXMucGFyZW50LmNoaWxkcmVuLCByZW1vdmVkKTtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5pbnN0cnVjdGlvbkFmdGVyID0gZnVuY3Rpb24odGFyZ2V0LCB2YWx1ZSkge1xuICAgICAgdmFyIGNoaWxkLCBpLCByZW1vdmVkO1xuICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICByZW1vdmVkID0gdGhpcy5wYXJlbnQuY2hpbGRyZW4uc3BsaWNlKGkgKyAxKTtcbiAgICAgIGNoaWxkID0gdGhpcy5wYXJlbnQuaW5zdHJ1Y3Rpb24odGFyZ2V0LCB2YWx1ZSk7XG4gICAgICBBcnJheS5wcm90b3R5cGUucHVzaC5hcHBseSh0aGlzLnBhcmVudC5jaGlsZHJlbiwgcmVtb3ZlZCk7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuZGVjbGFyYXRpb24gPSBmdW5jdGlvbih2ZXJzaW9uLCBlbmNvZGluZywgc3RhbmRhbG9uZSkge1xuICAgICAgdmFyIGRvYywgeG1sZGVjO1xuICAgICAgZG9jID0gdGhpcy5kb2N1bWVudCgpO1xuICAgICAgeG1sZGVjID0gbmV3IFhNTERlY2xhcmF0aW9uKGRvYywgdmVyc2lvbiwgZW5jb2RpbmcsIHN0YW5kYWxvbmUpO1xuICAgICAgaWYgKGRvYy5jaGlsZHJlblswXSBpbnN0YW5jZW9mIFhNTERlY2xhcmF0aW9uKSB7XG4gICAgICAgIGRvYy5jaGlsZHJlblswXSA9IHhtbGRlYztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGRvYy5jaGlsZHJlbi51bnNoaWZ0KHhtbGRlYyk7XG4gICAgICB9XG4gICAgICByZXR1cm4gZG9jLnJvb3QoKSB8fCBkb2M7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmRvY3R5cGUgPSBmdW5jdGlvbihwdWJJRCwgc3lzSUQpIHtcbiAgICAgIHZhciBjaGlsZCwgZG9jLCBkb2N0eXBlLCBpLCBqLCBrLCBsZW4sIGxlbjEsIHJlZjEsIHJlZjI7XG4gICAgICBkb2MgPSB0aGlzLmRvY3VtZW50KCk7XG4gICAgICBkb2N0eXBlID0gbmV3IFhNTERvY1R5cGUoZG9jLCBwdWJJRCwgc3lzSUQpO1xuICAgICAgcmVmMSA9IGRvYy5jaGlsZHJlbjtcbiAgICAgIGZvciAoaSA9IGogPSAwLCBsZW4gPSByZWYxLmxlbmd0aDsgaiA8IGxlbjsgaSA9ICsraikge1xuICAgICAgICBjaGlsZCA9IHJlZjFbaV07XG4gICAgICAgIGlmIChjaGlsZCBpbnN0YW5jZW9mIFhNTERvY1R5cGUpIHtcbiAgICAgICAgICBkb2MuY2hpbGRyZW5baV0gPSBkb2N0eXBlO1xuICAgICAgICAgIHJldHVybiBkb2N0eXBlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZWYyID0gZG9jLmNoaWxkcmVuO1xuICAgICAgZm9yIChpID0gayA9IDAsIGxlbjEgPSByZWYyLmxlbmd0aDsgayA8IGxlbjE7IGkgPSArK2spIHtcbiAgICAgICAgY2hpbGQgPSByZWYyW2ldO1xuICAgICAgICBpZiAoY2hpbGQuaXNSb290KSB7XG4gICAgICAgICAgZG9jLmNoaWxkcmVuLnNwbGljZShpLCAwLCBkb2N0eXBlKTtcbiAgICAgICAgICByZXR1cm4gZG9jdHlwZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgZG9jLmNoaWxkcmVuLnB1c2goZG9jdHlwZSk7XG4gICAgICByZXR1cm4gZG9jdHlwZTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUudXAgPSBmdW5jdGlvbigpIHtcbiAgICAgIGlmICh0aGlzLmlzUm9vdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGUgcm9vdCBub2RlIGhhcyBubyBwYXJlbnQuIFVzZSBkb2MoKSBpZiB5b3UgbmVlZCB0byBnZXQgdGhlIGRvY3VtZW50IG9iamVjdC5cIik7XG4gICAgICB9XG4gICAgICByZXR1cm4gdGhpcy5wYXJlbnQ7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLnJvb3QgPSBmdW5jdGlvbigpIHtcbiAgICAgIHZhciBub2RlO1xuICAgICAgbm9kZSA9IHRoaXM7XG4gICAgICB3aGlsZSAobm9kZSkge1xuICAgICAgICBpZiAobm9kZS5pc0RvY3VtZW50KSB7XG4gICAgICAgICAgcmV0dXJuIG5vZGUucm9vdE9iamVjdDtcbiAgICAgICAgfSBlbHNlIGlmIChub2RlLmlzUm9vdCkge1xuICAgICAgICAgIHJldHVybiBub2RlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG5vZGUgPSBub2RlLnBhcmVudDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5kb2N1bWVudCA9IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIG5vZGU7XG4gICAgICBub2RlID0gdGhpcztcbiAgICAgIHdoaWxlIChub2RlKSB7XG4gICAgICAgIGlmIChub2RlLmlzRG9jdW1lbnQpIHtcbiAgICAgICAgICByZXR1cm4gbm9kZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBub2RlID0gbm9kZS5wYXJlbnQ7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuZW5kID0gZnVuY3Rpb24ob3B0aW9ucykge1xuICAgICAgcmV0dXJuIHRoaXMuZG9jdW1lbnQoKS5lbmQob3B0aW9ucyk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLnByZXYgPSBmdW5jdGlvbigpIHtcbiAgICAgIHZhciBpO1xuICAgICAgaSA9IHRoaXMucGFyZW50LmNoaWxkcmVuLmluZGV4T2YodGhpcyk7XG4gICAgICB3aGlsZSAoaSA+IDAgJiYgdGhpcy5wYXJlbnQuY2hpbGRyZW5baSAtIDFdLmlzRHVtbXkpIHtcbiAgICAgICAgaSA9IGkgLSAxO1xuICAgICAgfVxuICAgICAgaWYgKGkgPCAxKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkFscmVhZHkgYXQgdGhlIGZpcnN0IG5vZGUuIFwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gdGhpcy5wYXJlbnQuY2hpbGRyZW5baSAtIDFdO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5uZXh0ID0gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgaTtcbiAgICAgIGkgPSB0aGlzLnBhcmVudC5jaGlsZHJlbi5pbmRleE9mKHRoaXMpO1xuICAgICAgd2hpbGUgKGkgPCB0aGlzLnBhcmVudC5jaGlsZHJlbi5sZW5ndGggLSAxICYmIHRoaXMucGFyZW50LmNoaWxkcmVuW2kgKyAxXS5pc0R1bW15KSB7XG4gICAgICAgIGkgPSBpICsgMTtcbiAgICAgIH1cbiAgICAgIGlmIChpID09PSAtMSB8fCBpID09PSB0aGlzLnBhcmVudC5jaGlsZHJlbi5sZW5ndGggLSAxKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkFscmVhZHkgYXQgdGhlIGxhc3Qgbm9kZS4gXCIgKyB0aGlzLmRlYnVnSW5mbygpKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0aGlzLnBhcmVudC5jaGlsZHJlbltpICsgMV07XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmltcG9ydERvY3VtZW50ID0gZnVuY3Rpb24oZG9jKSB7XG4gICAgICB2YXIgY2xvbmVkUm9vdDtcbiAgICAgIGNsb25lZFJvb3QgPSBkb2Mucm9vdCgpLmNsb25lKCk7XG4gICAgICBjbG9uZWRSb290LnBhcmVudCA9IHRoaXM7XG4gICAgICBjbG9uZWRSb290LmlzUm9vdCA9IGZhbHNlO1xuICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKGNsb25lZFJvb3QpO1xuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmRlYnVnSW5mbyA9IGZ1bmN0aW9uKG5hbWUpIHtcbiAgICAgIHZhciByZWYxLCByZWYyO1xuICAgICAgbmFtZSA9IG5hbWUgfHwgdGhpcy5uYW1lO1xuICAgICAgaWYgKChuYW1lID09IG51bGwpICYmICEoKHJlZjEgPSB0aGlzLnBhcmVudCkgIT0gbnVsbCA/IHJlZjEubmFtZSA6IHZvaWQgMCkpIHtcbiAgICAgICAgcmV0dXJuIFwiXCI7XG4gICAgICB9IGVsc2UgaWYgKG5hbWUgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gXCJwYXJlbnQ6IDxcIiArIHRoaXMucGFyZW50Lm5hbWUgKyBcIj5cIjtcbiAgICAgIH0gZWxzZSBpZiAoISgocmVmMiA9IHRoaXMucGFyZW50KSAhPSBudWxsID8gcmVmMi5uYW1lIDogdm9pZCAwKSkge1xuICAgICAgICByZXR1cm4gXCJub2RlOiA8XCIgKyBuYW1lICsgXCI+XCI7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gXCJub2RlOiA8XCIgKyBuYW1lICsgXCI+LCBwYXJlbnQ6IDxcIiArIHRoaXMucGFyZW50Lm5hbWUgKyBcIj5cIjtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuZWxlID0gZnVuY3Rpb24obmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgcmV0dXJuIHRoaXMuZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUubm9kID0gZnVuY3Rpb24obmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgcmV0dXJuIHRoaXMubm9kZShuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUudHh0ID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLnRleHQodmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5kYXQgPSBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMuY2RhdGEodmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5jb20gPSBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMuY29tbWVudCh2YWx1ZSk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmlucyA9IGZ1bmN0aW9uKHRhcmdldCwgdmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLmluc3RydWN0aW9uKHRhcmdldCwgdmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5kb2MgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiB0aGlzLmRvY3VtZW50KCk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmRlYyA9IGZ1bmN0aW9uKHZlcnNpb24sIGVuY29kaW5nLCBzdGFuZGFsb25lKSB7XG4gICAgICByZXR1cm4gdGhpcy5kZWNsYXJhdGlvbih2ZXJzaW9uLCBlbmNvZGluZywgc3RhbmRhbG9uZSk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmR0ZCA9IGZ1bmN0aW9uKHB1YklELCBzeXNJRCkge1xuICAgICAgcmV0dXJuIHRoaXMuZG9jdHlwZShwdWJJRCwgc3lzSUQpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5lID0gZnVuY3Rpb24obmFtZSwgYXR0cmlidXRlcywgdGV4dCkge1xuICAgICAgcmV0dXJuIHRoaXMuZWxlbWVudChuYW1lLCBhdHRyaWJ1dGVzLCB0ZXh0KTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUubiA9IGZ1bmN0aW9uKG5hbWUsIGF0dHJpYnV0ZXMsIHRleHQpIHtcbiAgICAgIHJldHVybiB0aGlzLm5vZGUobmFtZSwgYXR0cmlidXRlcywgdGV4dCk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLnQgPSBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMudGV4dCh2YWx1ZSk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmQgPSBmdW5jdGlvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMuY2RhdGEodmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5jID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLmNvbW1lbnQodmFsdWUpO1xuICAgIH07XG5cbiAgICBYTUxOb2RlLnByb3RvdHlwZS5yID0gZnVuY3Rpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiB0aGlzLnJhdyh2YWx1ZSk7XG4gICAgfTtcblxuICAgIFhNTE5vZGUucHJvdG90eXBlLmkgPSBmdW5jdGlvbih0YXJnZXQsIHZhbHVlKSB7XG4gICAgICByZXR1cm4gdGhpcy5pbnN0cnVjdGlvbih0YXJnZXQsIHZhbHVlKTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUudSA9IGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIHRoaXMudXAoKTtcbiAgICB9O1xuXG4gICAgWE1MTm9kZS5wcm90b3R5cGUuaW1wb3J0WE1MQnVpbGRlciA9IGZ1bmN0aW9uKGRvYykge1xuICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0RG9jdW1lbnQoZG9jKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTE5vZGU7XG5cbiAgfSkoKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js":
/*!*****************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js ***!
  \*****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.target = this.stringify.insTarget(target);\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.set(options).processingInstruction(this);\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js":
/*!***********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLRaw.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.set(options).raw(this);\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStreamWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStreamWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      XMLStreamWriter.__super__.constructor.call(this, options);\n      this.stream = stream;\n    }\n\n    XMLStreamWriter.prototype.document = function(doc) {\n      var child, i, j, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        child.isLastRootNode = false;\n      }\n      doc.children[doc.children.length - 1].isLastRootNode = true;\n      ref1 = doc.children;\n      results = [];\n      for (j = 0, len1 = ref1.length; j < len1; j++) {\n        child = ref1[j];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        switch (false) {\n          case !(child instanceof XMLDeclaration):\n            results.push(this.declaration(child));\n            break;\n          case !(child instanceof XMLDocType):\n            results.push(this.docType(child));\n            break;\n          case !(child instanceof XMLComment):\n            results.push(this.comment(child));\n            break;\n          case !(child instanceof XMLProcessingInstruction):\n            results.push(this.processingInstruction(child));\n            break;\n          default:\n            results.push(this.element(child));\n        }\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att) {\n      return this.stream.write(' ' + att.name + '=\"' + att.value + '\"');\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, level) {\n      return this.stream.write(this.space(level) + '<![CDATA[' + node.text + ']]>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, level) {\n      return this.stream.write(this.space(level) + '<!-- ' + node.text + ' -->' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, level) {\n      this.stream.write(this.space(level));\n      this.stream.write('<?xml version=\"' + node.version + '\"');\n      if (node.encoding != null) {\n        this.stream.write(' encoding=\"' + node.encoding + '\"');\n      }\n      if (node.standalone != null) {\n        this.stream.write(' standalone=\"' + node.standalone + '\"');\n      }\n      this.stream.write(this.spacebeforeslash + '?>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, level) {\n      var child, i, len, ref;\n      level || (level = 0);\n      this.stream.write(this.space(level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node));\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          switch (false) {\n            case !(child instanceof XMLDTDAttList):\n              this.dtdAttList(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDElement):\n              this.dtdElement(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDEntity):\n              this.dtdEntity(child, level + 1);\n              break;\n            case !(child instanceof XMLDTDNotation):\n              this.dtdNotation(child, level + 1);\n              break;\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            default:\n              throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(']');\n      }\n      this.stream.write(this.spacebeforeslash + '>');\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.element = function(node, level) {\n      var att, child, i, len, name, ref, ref1, space;\n      level || (level = 0);\n      space = this.space(level);\n      this.stream.write(space + '<' + node.name);\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          this.stream.write('></' + node.name + '>');\n        } else {\n          this.stream.write(this.spacebeforeslash + '/>');\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        this.stream.write('>');\n        this.stream.write(node.children[0].value);\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.newline);\n        ref1 = node.children;\n        for (i = 0, len = ref1.length; i < len; i++) {\n          child = ref1[i];\n          switch (false) {\n            case !(child instanceof XMLCData):\n              this.cdata(child, level + 1);\n              break;\n            case !(child instanceof XMLComment):\n              this.comment(child, level + 1);\n              break;\n            case !(child instanceof XMLElement):\n              this.element(child, level + 1);\n              break;\n            case !(child instanceof XMLRaw):\n              this.raw(child, level + 1);\n              break;\n            case !(child instanceof XMLText):\n              this.text(child, level + 1);\n              break;\n            case !(child instanceof XMLProcessingInstruction):\n              this.processingInstruction(child, level + 1);\n              break;\n            case !(child instanceof XMLDummy):\n              '';\n              break;\n            default:\n              throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n          }\n        }\n        this.stream.write(space + '</' + node.name + '>');\n      }\n      return this.stream.write(this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, level) {\n      this.stream.write(this.space(level) + '<?' + node.target);\n      if (node.value) {\n        this.stream.write(' ' + node.value);\n      }\n      return this.stream.write(this.spacebeforeslash + '?>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, level) {\n      return this.stream.write(this.space(level) + node.value + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, level) {\n      this.stream.write(this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType);\n      if (node.defaultValueType !== '#DEFAULT') {\n        this.stream.write(' ' + node.defaultValueType);\n      }\n      if (node.defaultValue) {\n        this.stream.write(' \"' + node.defaultValue + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, level) {\n      this.stream.write(this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value);\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, level) {\n      this.stream.write(this.space(level) + '<!ENTITY');\n      if (node.pe) {\n        this.stream.write(' %');\n      }\n      this.stream.write(' ' + node.name);\n      if (node.value) {\n        this.stream.write(' \"' + node.value + '\"');\n      } else {\n        if (node.pubID && node.sysID) {\n          this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n        } else if (node.sysID) {\n          this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n        }\n        if (node.nData) {\n          this.stream.write(' NDATA ' + node.nData);\n        }\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, level) {\n      this.stream.write(this.space(level) + '<!NOTATION ' + node.name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.pubID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      return this.stream.write(this.spacebeforeslash + '>' + this.endline(node));\n    };\n\n    XMLStreamWriter.prototype.endline = function(node) {\n      if (!node.isLastRootNode) {\n        return this.newline;\n      } else {\n        return '';\n      }\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLText, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc) {\n      var child, i, len, r, ref;\n      this.textispresent = false;\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        if (child instanceof XMLDummy) {\n          continue;\n        }\n        r += (function() {\n          switch (false) {\n            case !(child instanceof XMLDeclaration):\n              return this.declaration(child);\n            case !(child instanceof XMLDocType):\n              return this.docType(child);\n            case !(child instanceof XMLComment):\n              return this.comment(child);\n            case !(child instanceof XMLProcessingInstruction):\n              return this.processingInstruction(child);\n            default:\n              return this.element(child, 0);\n          }\n        }).call(this);\n      }\n      if (this.pretty && r.slice(-this.newline.length) === this.newline) {\n        r = r.slice(0, -this.newline.length);\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.attribute = function(att) {\n      return ' ' + att.name + '=\"' + att.value + '\"';\n    };\n\n    XMLStringWriter.prototype.cdata = function(node, level) {\n      return this.space(level) + '<![CDATA[' + node.text + ']]>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.comment = function(node, level) {\n      return this.space(level) + '<!-- ' + node.text + ' -->' + this.newline;\n    };\n\n    XMLStringWriter.prototype.declaration = function(node, level) {\n      var r;\n      r = this.space(level);\n      r += '<?xml version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      r += this.spacebeforeslash + '?>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.docType = function(node, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      r = this.space(level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.newline;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLDTDAttList):\n                return this.dtdAttList(child, level + 1);\n              case !(child instanceof XMLDTDElement):\n                return this.dtdElement(child, level + 1);\n              case !(child instanceof XMLDTDEntity):\n                return this.dtdEntity(child, level + 1);\n              case !(child instanceof XMLDTDNotation):\n                return this.dtdNotation(child, level + 1);\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              default:\n                throw new Error(\"Unknown DTD node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        r += ']';\n      }\n      r += this.spacebeforeslash + '>';\n      r += this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.element = function(node, level) {\n      var att, child, i, j, len, len1, name, r, ref, ref1, ref2, space, textispresentwasset;\n      level || (level = 0);\n      textispresentwasset = false;\n      if (this.textispresent) {\n        this.newline = '';\n        this.pretty = false;\n      } else {\n        this.newline = this.newlinedefault;\n        this.pretty = this.prettydefault;\n      }\n      space = this.space(level);\n      r = '';\n      r += space + '<' + node.name;\n      ref = node.attributes;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att);\n      }\n      if (node.children.length === 0 || node.children.every(function(e) {\n        return e.value === '';\n      })) {\n        if (this.allowEmpty) {\n          r += '></' + node.name + '>' + this.newline;\n        } else {\n          r += this.spacebeforeslash + '/>' + this.newline;\n        }\n      } else if (this.pretty && node.children.length === 1 && (node.children[0].value != null)) {\n        r += '>';\n        r += node.children[0].value;\n        r += '</' + node.name + '>' + this.newline;\n      } else {\n        if (this.dontprettytextnodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if (child.value != null) {\n              this.textispresent++;\n              textispresentwasset = true;\n              break;\n            }\n          }\n        }\n        if (this.textispresent) {\n          this.newline = '';\n          this.pretty = false;\n          space = this.space(level);\n        }\n        r += '>' + this.newline;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += (function() {\n            switch (false) {\n              case !(child instanceof XMLCData):\n                return this.cdata(child, level + 1);\n              case !(child instanceof XMLComment):\n                return this.comment(child, level + 1);\n              case !(child instanceof XMLElement):\n                return this.element(child, level + 1);\n              case !(child instanceof XMLRaw):\n                return this.raw(child, level + 1);\n              case !(child instanceof XMLText):\n                return this.text(child, level + 1);\n              case !(child instanceof XMLProcessingInstruction):\n                return this.processingInstruction(child, level + 1);\n              case !(child instanceof XMLDummy):\n                return '';\n              default:\n                throw new Error(\"Unknown XML node type: \" + child.constructor.name);\n            }\n          }).call(this);\n        }\n        if (textispresentwasset) {\n          this.textispresent--;\n        }\n        if (!this.textispresent) {\n          this.newline = this.newlinedefault;\n          this.pretty = this.prettydefault;\n        }\n        r += space + '</' + node.name + '>' + this.newline;\n      }\n      return r;\n    };\n\n    XMLStringWriter.prototype.processingInstruction = function(node, level) {\n      var r;\n      r = this.space(level) + '<?' + node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      r += this.spacebeforeslash + '?>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.raw = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.text = function(node, level) {\n      return this.space(level) + node.value + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdAttList = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ATTLIST ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdElement = function(node, level) {\n      return this.space(level) + '<!ELEMENT ' + node.name + ' ' + node.value + this.spacebeforeslash + '>' + this.newline;\n    };\n\n    XMLStringWriter.prototype.dtdEntity = function(node, level) {\n      var r;\n      r = this.space(level) + '<!ENTITY';\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.dtdNotation = function(node, level) {\n      var r;\n      r = this.space(level) + '<!NOTATION ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      r += this.spacebeforeslash + '>' + this.newline;\n      return r;\n    };\n\n    XMLStringWriter.prototype.openNode = function(node, level) {\n      var att, name, r, ref;\n      level || (level = 0);\n      if (node instanceof XMLElement) {\n        r = this.space(level) + '<' + node.name;\n        ref = node.attributes;\n        for (name in ref) {\n          if (!hasProp.call(ref, name)) continue;\n          att = ref[name];\n          r += this.attribute(att);\n        }\n        r += (node.children ? '>' : '/>') + this.newline;\n        return r;\n      } else {\n        r = this.space(level) + '<!DOCTYPE ' + node.rootNodeName;\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        r += (node.children ? ' [' : '>') + this.newline;\n        return r;\n      }\n    };\n\n    XMLStringWriter.prototype.closeNode = function(node, level) {\n      level || (level = 0);\n      switch (false) {\n        case !(node instanceof XMLElement):\n          return this.space(level) + '</' + node.name + '>' + this.newline;\n        case !(node instanceof XMLDocType):\n          return this.space(level) + ']>' + this.newline;\n      }\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringifier.js ***!
  \*******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.noDoubleEncoding = options.noDoubleEncoding;\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.eleName = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.eleText = function(val) {\n      val = '' + val || '';\n      return this.assertLegalChar(this.elEscape(val));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attName = function(val) {\n      return val = '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      val = '' + val || '';\n      return this.attEscape(val);\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (val != null) {\n        return '' + val || '';\n      } else {\n        return val;\n      }\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var res;\n      res = str.match(/[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/);\n      if (res) {\n        throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.elEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      ampregex = this.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLStringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLText.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLText.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNode, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.value = this.stringify.eleText(text);\n    }\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.set(options).text(this);\n    };\n\n    return XMLText;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLText.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLWriterBase.js ***!
  \******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLWriterBase,\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, ref1, ref2, ref3, ref4, ref5, ref6, value;\n      options || (options = {});\n      this.pretty = options.pretty || false;\n      this.allowEmpty = (ref = options.allowEmpty) != null ? ref : false;\n      if (this.pretty) {\n        this.indent = (ref1 = options.indent) != null ? ref1 : '  ';\n        this.newline = (ref2 = options.newline) != null ? ref2 : '\\n';\n        this.offset = (ref3 = options.offset) != null ? ref3 : 0;\n        this.dontprettytextnodes = (ref4 = options.dontprettytextnodes) != null ? ref4 : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = (ref5 = options.spacebeforeslash) != null ? ref5 : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref6 = options.writer || {};\n      for (key in ref6) {\n        if (!hasProp.call(ref6, key)) continue;\n        value = ref6[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.set = function(options) {\n      var key, ref, value;\n      options || (options = {});\n      if (\"pretty\" in options) {\n        this.pretty = options.pretty;\n      }\n      if (\"allowEmpty\" in options) {\n        this.allowEmpty = options.allowEmpty;\n      }\n      if (this.pretty) {\n        this.indent = \"indent\" in options ? options.indent : '  ';\n        this.newline = \"newline\" in options ? options.newline : '\\n';\n        this.offset = \"offset\" in options ? options.offset : 0;\n        this.dontprettytextnodes = \"dontprettytextnodes\" in options ? options.dontprettytextnodes : 0;\n      } else {\n        this.indent = '';\n        this.newline = '';\n        this.offset = 0;\n        this.dontprettytextnodes = 0;\n      }\n      this.spacebeforeslash = \"spacebeforeslash\" in options ? options.spacebeforeslash : '';\n      if (this.spacebeforeslash === true) {\n        this.spacebeforeslash = ' ';\n      }\n      this.newlinedefault = this.newline;\n      this.prettydefault = this.pretty;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n      return this;\n    };\n\n    XMLWriterBase.prototype.space = function(level) {\n      var indent;\n      if (this.pretty) {\n        indent = (level || 0) + this.offset + 1;\n        if (indent > 0) {\n          return new Array(indent).join(this.indent);\n        } else {\n          return '';\n        }\n      } else {\n        return '';\n      }\n    };\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlbuilder/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/index.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/xmlbuilder/lib/Utility.js\"), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLDocumentCB = __webpack_require__(/*! ./XMLDocumentCB */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  XMLStreamWriter = __webpack_require__(/*! ./XMLStreamWriter */ \"(ssr)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\");\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.doctype(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlbuilder/lib/index.js\n");

/***/ })

};
;