'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Play, Download, Merge, Clock, Film, Zap } from 'lucide-react'

interface VideoSegment {
  id: string
  title: string
  duration: number
  videoUrl: string
  status: string
  type?: string
}

interface VideoMergerProps {
  storyVideoId: string
  onMergeComplete?: (mergedVideoUrl: string) => void
}

const TRANSITION_TYPES = {
  cut: { name: '直切', icon: '✂️', description: '快速切换，适合动作场景' },
  fade: { name: '淡入淡出', icon: '🌅', description: '柔和过渡，适合情感场景' },
  dissolve: { name: '溶解', icon: '💫', description: '梦幻效果，适合回忆场景' },
  wipe: { name: '擦除', icon: '🔄', description: '方向性过渡，适合空间转换' },
  zoom: { name: '缩放', icon: '🔍', description: '聚焦效果，适合重点突出' }
}

const SEGMENT_TYPES = {
  environment: { name: '环境建立', color: 'bg-blue-100 text-blue-800', icon: '🏔️' },
  character: { name: '角色引入', color: 'bg-green-100 text-green-800', icon: '👤' },
  action: { name: '动作核心', color: 'bg-red-100 text-red-800', icon: '⚡' },
  emotion: { name: '情感转折', color: 'bg-purple-100 text-purple-800', icon: '💭' },
  dialogue: { name: '对话互动', color: 'bg-yellow-100 text-yellow-800', icon: '💬' },
  suspense: { name: '悬念结尾', color: 'bg-gray-100 text-gray-800', icon: '❓' }
}

export default function VideoMerger({ storyVideoId, onMergeComplete }: VideoMergerProps) {
  const [segments, setSegments] = useState<VideoSegment[]>([])
  const [mergedVideoUrl, setMergedVideoUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isMerging, setIsMerging] = useState(false)
  const [customTransitions, setCustomTransitions] = useState<string[]>([])
  const [totalDuration, setTotalDuration] = useState(0)
  const [outputFormat, setOutputFormat] = useState('mp4')
  const [quality, setQuality] = useState('high')

  useEffect(() => {
    loadSegments()
  }, [storyVideoId])

  const loadSegments = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/video/merge-segments?storyVideoId=${storyVideoId}`)
      const result = await response.json()

      if (result.success) {
        setSegments(result.data.segments)
        setMergedVideoUrl(result.data.mergedVideoUrl)
        setTotalDuration(result.data.totalDuration || 0)
        
        // 初始化转场设置
        if (result.data.segments.length > 1) {
          setCustomTransitions(new Array(result.data.segments.length - 1).fill('cut'))
        }
      }
    } catch (error) {
      console.error('加载片段失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMerge = async () => {
    try {
      setIsMerging(true)
      
      const response = await fetch('/api/video/merge-segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          storyVideoId,
          customTransitions,
          outputFormat,
          quality
        })
      })

      const result = await response.json()

      if (result.success) {
        setMergedVideoUrl(result.data.mergedVideoUrl)
        setTotalDuration(result.data.totalDuration)
        onMergeComplete?.(result.data.mergedVideoUrl)
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('视频合并失败:', error)
      alert('视频合并失败，请重试')
    } finally {
      setIsMerging(false)
    }
  }

  const updateTransition = (index: number, transition: string) => {
    const newTransitions = [...customTransitions]
    newTransitions[index] = transition
    setCustomTransitions(newTransitions)
  }

  const getSegmentType = (segment: VideoSegment) => {
    // 简单的类型推断
    const title = segment.title.toLowerCase()
    if (title.includes('环境') || title.includes('场景')) return 'environment'
    if (title.includes('角色') || title.includes('登场')) return 'character'
    if (title.includes('动作') || title.includes('跑') || title.includes('扛')) return 'action'
    if (title.includes('情感') || title.includes('表情')) return 'emotion'
    if (title.includes('对话') || title.includes('讨论')) return 'dialogue'
    if (title.includes('悬念') || title.includes('威胁')) return 'suspense'
    return 'action'
  }

  const calculateTotalDuration = () => {
    return segments.reduce((sum, segment) => sum + segment.duration, 0)
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">加载视频片段...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 合并状态卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="h-5 w-5" />
            微剧集合并器
          </CardTitle>
          <CardDescription>
            将多个5-8秒片段合并为完整剧集视频
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                总时长: {calculateTotalDuration()}秒
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Film className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                片段数: {segments.length}个
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                转场数: {Math.max(0, segments.length - 1)}个
              </span>
            </div>
          </div>

          {/* 输出设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="text-sm font-medium mb-2 block">输出格式</label>
              <Select value={outputFormat} onValueChange={setOutputFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mp4">MP4 (推荐)</SelectItem>
                  <SelectItem value="webm">WebM</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">视频质量</label>
              <Select value={quality} onValueChange={setQuality}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">高质量 (慢)</SelectItem>
                  <SelectItem value="medium">中等质量</SelectItem>
                  <SelectItem value="low">快速处理</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 片段列表 */}
      <Card>
        <CardHeader>
          <CardTitle>视频片段序列</CardTitle>
          <CardDescription>
            调整转场效果以获得最佳观看体验
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {segments.map((segment, index) => {
              const segmentType = getSegmentType(segment)
              const typeInfo = SEGMENT_TYPES[segmentType as keyof typeof SEGMENT_TYPES]
              
              return (
                <div key={segment.id}>
                  {/* 片段卡片 */}
                  <div className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <span className="text-lg">{typeInfo.icon}</span>
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">{segment.title}</h4>
                        <Badge className={typeInfo.color}>
                          {typeInfo.name}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{segment.duration}秒</span>
                        <span>#{index + 1}</span>
                      </div>
                    </div>

                    <div className="flex-shrink-0">
                      {segment.videoUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(segment.videoUrl, '_blank')}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* 转场设置 */}
                  {index < segments.length - 1 && (
                    <div className="flex items-center justify-center py-2">
                      <div className="flex items-center gap-2 bg-gray-50 rounded-lg p-2">
                        <span className="text-sm text-gray-600">转场:</span>
                        <Select
                          value={customTransitions[index] || 'cut'}
                          onValueChange={(value) => updateTransition(index, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(TRANSITION_TYPES).map(([key, info]) => (
                              <SelectItem key={key} value={key}>
                                <span className="flex items-center gap-2">
                                  <span>{info.icon}</span>
                                  <span>{info.name}</span>
                                </span>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 合并操作 */}
      <Card>
        <CardContent className="p-6">
          {mergedVideoUrl ? (
            <div className="text-center space-y-4">
              <div className="text-green-600 font-medium">
                ✅ 视频合并完成！
              </div>
              <div className="flex justify-center gap-4">
                <Button
                  onClick={() => window.open(mergedVideoUrl, '_blank')}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  播放合并视频
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    const a = document.createElement('a')
                    a.href = mergedVideoUrl
                    a.download = `merged-video-${storyVideoId}.${outputFormat}`
                    a.click()
                  }}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  下载视频
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <Button
                onClick={handleMerge}
                disabled={isMerging || segments.length === 0}
                className="flex items-center gap-2"
                size="lg"
              >
                <Merge className="h-4 w-4" />
                {isMerging ? '合并中...' : '开始合并视频'}
              </Button>
              
              {isMerging && (
                <div className="space-y-2">
                  <Progress value={50} className="w-full" />
                  <p className="text-sm text-gray-600">
                    正在处理视频片段，请稍候...
                  </p>
                </div>
              )}
              
              <p className="text-sm text-gray-500">
                将 {segments.length} 个片段合并为完整的 {calculateTotalDuration()} 秒剧集视频
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
