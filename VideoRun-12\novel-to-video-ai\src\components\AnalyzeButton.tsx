'use client'

import { useState, useEffect } from 'react'
import { Brain, Loader2, ChevronDown } from 'lucide-react'

interface AnalyzeButtonProps {
  projectId: string
  hasAIConfig: boolean
  onAnalysisComplete: (result: any) => void
  disabled?: boolean
}

export default function AnalyzeButton({
  projectId,
  hasAIConfig,
  onAnalysisComplete,
  disabled = false,
}: AnalyzeButtonProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState('')
  const [error, setError] = useState<string | null>(null)

  // 增强提示词相关状态
  const [showPromptDropdown, setShowPromptDropdown] = useState(false)
  const [enhancePrompt, setEnhancePrompt] = useState('')
  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')

  const handleAnalyze = async () => {
    if (!hasAIConfig) {
      setError('请先配置AI模型')
      return
    }

    setIsAnalyzing(true)
    setError(null)
    setProgress('开始分析...')

    try {
      // 开始分析
      setProgress('正在调用AI模型分析小说内容...')

      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          customPrompt: savedEnhancePrompt || undefined
        }),
      })

      const data = await response.json()

      if (data.success) {
        setProgress('分析完成！')
        onAnalysisComplete(data.data)
        
        // 显示成功信息
        setTimeout(() => {
          setProgress('')
        }, 2000)
      } else {
        throw new Error(data.error || '分析失败')
      }
    } catch (error) {
      console.error('分析失败:', error)
      setError(error instanceof Error ? error.message : '分析失败，请重试')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 保存增强提示词
  const handleSaveEnhancePrompt = () => {
    setSavedEnhancePrompt(enhancePrompt)
    setShowPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_analyze_${projectId}`
    localStorage.setItem(storageKey, enhancePrompt)
  }

  // 从localStorage加载增强提示词
  useEffect(() => {
    if (projectId) {
      const storageKey = `enhance_prompt_analyze_${projectId}`
      const savedPrompt = localStorage.getItem(storageKey)
      if (savedPrompt) {
        setSavedEnhancePrompt(savedPrompt)
        setEnhancePrompt(savedPrompt)
      }
    }
  }, [projectId])

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold mb-2 flex items-center">
            <Brain className="mr-2" size={20} />
            智能分析
          </h2>
          <p className="text-gray-600 text-sm">
            AI将自动提取角色信息并按章节拆分剧集
          </p>
        </div>

        <div className="relative flex">
          <button
            onClick={handleAnalyze}
            disabled={disabled || isAnalyzing || !hasAIConfig}
            className={`
              px-6 py-3 rounded-l-lg font-medium transition-all duration-200
              ${
                disabled || !hasAIConfig
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : isAnalyzing
                  ? 'bg-blue-400 text-white cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600 hover:shadow-lg'
              }
            `}
          >
            {isAnalyzing ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 animate-spin" size={16} />
                分析中...
              </div>
            ) : (
              '自动分析人物剧情'
            )}
          </button>

          <button
            onClick={() => setShowPromptDropdown(!showPromptDropdown)}
            disabled={disabled || isAnalyzing || !hasAIConfig}
            className={`
              px-2 py-3 rounded-r-lg border-l border-opacity-30 font-medium transition-all duration-200
              ${
                disabled || !hasAIConfig
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed border-gray-400'
                  : isAnalyzing
                  ? 'bg-blue-400 text-white cursor-not-allowed border-blue-300'
                  : 'bg-blue-500 text-white hover:bg-blue-600 hover:shadow-lg border-blue-400'
              }
            `}
          >
            <ChevronDown size={16} />
          </button>

          {/* 增强提示词下拉框 */}
          {showPromptDropdown && (
            <div className="absolute top-full left-0 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              <div className="p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  增强提示词设置
                </label>
                <textarea
                  value={enhancePrompt}
                  onChange={(e) => setEnhancePrompt(e.target.value)}
                  placeholder="输入增强提示词，用于优化AI分析效果..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 resize-none"
                  rows={4}
                />

                {/* 保存按钮 */}
                <div className="mt-3">
                  <button
                    onClick={handleSaveEnhancePrompt}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    保存增强提示词
                  </button>
                </div>

                {/* 操作按钮 */}
                <div className="flex justify-end mt-3">
                  <button
                    onClick={() => setShowPromptDropdown(false)}
                    className="text-sm text-gray-600 hover:text-gray-800"
                  >
                    关闭
                  </button>
                </div>

                {/* 当前保存的提示词预览 */}
                {savedEnhancePrompt && (
                  <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                    <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                    <div className="max-h-16 overflow-y-auto">
                      {savedEnhancePrompt}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 进度信息 */}
      {progress && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            {isAnalyzing && <Loader2 className="mr-2 animate-spin" size={16} />}
            <span className="text-blue-700">{progress}</span>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      {/* 提示信息 */}
      {!hasAIConfig && (
        <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg">
          ⚠️ 请先在上方配置AI模型才能进行分析
        </div>
      )}
    </div>
  )
}
