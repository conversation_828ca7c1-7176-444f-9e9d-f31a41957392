# 豆包 Seedream 3.0 T2I 图像生成模型接入说明

## 📖 概述

系统已成功接入豆包 Seedream 3.0 T2I 图像生成模型，这是火山引擎推出的高质量文生图模型，特别适合角色形象生成和场景可视化。

## 🎯 模型信息

### 基本信息
- **模型名称**: `doubao-seedream-3.0-t2i-250415`
- **提供商**: 豆包 (火山引擎)
- **类型**: 图像生成模型
- **发布日期**: 2025年4月15日
- **API端点**: `https://ark.cn-beijing.volces.com/api/v3/images/generations`

### 技术特点
- ✅ 高质量文生图
- ✅ 支持1024x1024分辨率
- ✅ 可调节引导强度
- ✅ 支持水印控制
- ✅ 快速响应时间

## 🔑 配置步骤

### 步骤1: 获取API密钥
1. 访问火山引擎控制台: https://console.volcengine.com/
2. 进入火山方舟平台
3. 在模型广场中搜索"Seedream"
4. 找到 `doubao-seedream-3.0-t2i-250415` 模型
5. 申请使用权限并等待审核通过
6. 创建API密钥并妥善保存

### 步骤2: 系统配置
1. 在系统中访问"模型配置"页面
2. 找到"豆包 (火山引擎)"分组
3. 选择"豆包 Seedream 3.0 T2I"模型
4. 输入您的API密钥
5. 点击"测试"验证连接
6. 测试成功后启用模型
7. 保存配置

## 🎨 使用场景

### 1. 角色形象生成
在角色管理页面使用"AI一键生成角色形象"功能：
- 选择"豆包 Seedream 3.0 T2I"模型
- 系统会自动生成角色的正面、侧面、背面三视图
- 支持基于角色描述的一致性生成

### 2. 场景图像生成
适用于：
- 小说场景可视化
- 角色设定插图
- 故事背景图像
- 概念设计图

## 🔧 API参数说明

### 请求参数
```json
{
  "model": "doubao-seedream-3.0-t2i-250415",
  "prompt": "角色或场景描述",
  "response_format": "url",
  "size": "1024x1024",
  "guidance_scale": 3,
  "watermark": true
}
```

### 参数详解
- **model**: 模型名称，固定为 `doubao-seedream-3.0-t2i-250415`
- **prompt**: 图像生成提示词，支持中英文
- **response_format**: 返回格式，固定为 `url`
- **size**: 图像尺寸，支持 `1024x1024`
- **guidance_scale**: 引导强度，范围1-10，默认3
- **watermark**: 是否添加水印，默认true

## 🆚 模型对比

| 模型 | 类型 | 优势 | 适用场景 |
|------|------|------|----------|
| 豆包 Seedream 3.0 T2I | 图像生成 | 高质量、快速 | 角色形象、场景图 |
| 豆包 Seedance 1.0 Pro | 视频生成 | 动态效果 | 小说转视频 |
| 智谱AI CogView | 图像生成 | 通用性强 | 各类图像生成 |
| 通义万相 | 图像生成 | 稳定可靠 | 商业应用 |

## ✅ 配置检查清单

在配置豆包Seedream图像生成时，请按以下清单逐项检查：

### 数据库配置检查
- [ ] 确认模型名称为 `Doubao-Seedream-3.0-t2i`
- [ ] 确认端点为 `ep-20250626132353-nlrtf` (或最新端点)
- [ ] 确认API密钥格式为UUID: `e7fc00da-28b5-4628-9c59-588d559cdf1c`
- [ ] 确认 `enabled` 字段为 `true`
- [ ] 确认 `supportsImage` 字段为 `true`

### 代码实现检查
- [ ] 使用 `prisma.aIConfig.findFirst()` (注意大小写)
- [ ] 访问 `doubaoConfig.model` 而不是 `doubaoConfig.endpoint`
- [ ] 使用真实API调用而不是模拟响应
- [ ] 实现错误处理和回退机制
- [ ] 在提示词中包含站立姿态描述

### API调用检查
- [ ] 端点URL: `https://ark.cn-beijing.volces.com/api/v3/images/generations`
- [ ] 请求方法: `POST`
- [ ] 认证头: `Authorization: Bearer ${apiKey}`
- [ ] 内容类型: `Content-Type: application/json`
- [ ] 请求体包含正确的模型名称

### 功能验证
- [ ] 三视图生成返回真实图像URL (不是SVG占位图)
- [ ] 提供商显示为 `doubao` (不是 `doubao-mock` 或 `doubao-fallback`)
- [ ] 生成的角色为站立姿态
- [ ] API响应时间在3-8秒范围内
- [ ] 错误时能正确回退到占位图

## 💡 最佳实践

### 提示词优化
1. **角色描述**: 包含外貌、服装、表情等详细信息
2. **姿态指定**: 明确添加"站立姿态，挺直身体，双脚着地，标准站姿"
3. **风格指定**: 可添加"动漫风格"、"写实风格"等
4. **质量提升**: 添加"高质量"、"精细"等关键词
5. **一致性**: 使用相同的角色特征描述

### 示例提示词
```
正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，
站立姿态，挺直身体，双脚着地，标准站姿，
一个年轻的女性角色，长黑发，穿着白色连衣裙，
温和的笑容，动漫风格，高质量，角色设计图，白色背景，全身图
```

## 🔍 故障排除

### 常见错误和解决方案

#### 1. 数据库模型调用错误
**错误**: `TypeError: Cannot read properties of undefined (reading 'findFirst')`
**原因**: Prisma模型名称大小写错误
**解决**:
```typescript
// ❌ 错误写法
const config = await prisma.aiConfig.findFirst({...})

// ✅ 正确写法
const config = await prisma.aIConfig.findFirst({...})
```
**注意**: 数据库模型名称是 `AIConfig`，不是 `aiConfig`

#### 2. API密钥认证失败
**错误**: `401 AuthenticationError - The API key in the request is missing or invalid`
**原因**: 使用了错误的API密钥格式
**解决**:
- 豆包图像生成需要使用与视频生成相同的API密钥
- 正确格式: `e7fc00da-28b5-4628-9c59-588d559cdf1c` (UUID格式)
- 错误格式: `AKLTOTgwMzIxY2VlNDIxNDNiMWFlZjAzOWY1OTU3ZDIwOWE` (AccessKey格式)

#### 3. 模型端点配置错误
**错误**: `model: undefined` 在API请求参数中
**原因**: 访问了不存在的配置字段
**解决**:
```typescript
// ❌ 错误写法
model: doubaoConfig.endpoint

// ✅ 正确写法
model: doubaoConfig.model
```
**注意**: 数据库字段是 `model`，不是 `endpoint`

#### 4. 三视图生成姿态问题
**问题**: 生成的角色都是蹲着的，不是站立的
**原因**: 提示词缺少明确的姿态描述
**解决**: 在提示词中添加站立姿态关键词
```typescript
// ✅ 修复后的提示词
front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}...`
```

#### 5. 模拟API与真实API混淆
**问题**: 一直使用占位图而不是真实图像
**原因**: 代码中使用了模拟响应而不是真实API调用
**解决**:
```typescript
// ❌ 模拟响应
console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证')
const mockImageUrl = generateSmartMockImage(...)

// ✅ 真实API调用
const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${doubaoConfig.apiKey}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestBody)
})
```

#### 6. 传统错误处理
**错误**: "豆包API密钥无效，请检查配置"
**解决**:
- 检查API密钥是否正确
- 确认模型权限已开通
- 验证账户状态

#### 7. 模型不存在
**错误**: "模型不存在或无权访问"
**解决**:
- 在火山方舟控制台申请模型权限
- 等待审核通过
- 确认使用正确的模型名称

#### 8. 生成失败
**错误**: "图像生成失败"
**解决**:
- 检查提示词是否合规
- 确认账户余额充足
- 重试请求

## 📊 性能指标

### 响应时间
- 平均生成时间: 3-8秒
- 图像分辨率: 1024x1024
- 支持并发: 根据API配额

### 质量评估
- 图像清晰度: ⭐⭐⭐⭐⭐
- 提示词理解: ⭐⭐⭐⭐⭐
- 风格一致性: ⭐⭐⭐⭐⭐
- 角色一致性: ⭐⭐⭐⭐⭐

## 🎉 总结

豆包 Seedream 3.0 T2I 模型的成功接入为系统增加了强大的图像生成能力：

✅ **完整支持**: 从配置到使用的完整流程
✅ **高质量**: 专业级图像生成效果
✅ **易用性**: 简单的配置和使用流程
✅ **一致性**: 支持角色形象一致性生成
✅ **灵活性**: 可与其他模型配合使用
✅ **真实API**: 调用真实豆包API而不是模拟响应
✅ **站立姿态**: 生成的角色为标准站立姿态
✅ **错误处理**: 完善的错误处理和回退机制

## 📝 更新日志

### 2025-06-26 重要修复
- 🔧 修复了数据库模型调用错误 (`prisma.aIConfig` vs `prisma.aiConfig`)
- 🔑 修复了API密钥认证问题 (使用正确的UUID格式密钥)
- 🎯 修复了模型端点配置错误 (`model` vs `endpoint` 字段)
- 🧍‍♂️ 修复了角色姿态问题 (添加站立姿态描述)
- 🌐 实现了真实API调用替代模拟响应
- ⚡ 优化了错误处理和回退机制

现在您可以在三视图生成功能中选择豆包模型，享受高质量的真实图像生成服务！
