# 豆包 Seedream 3.0 T2I 图像生成模型接入说明

## 📖 概述

系统已成功接入豆包 Seedream 3.0 T2I 图像生成模型，这是火山引擎推出的高质量文生图模型，特别适合角色形象生成和场景可视化。

## 🎯 模型信息

### 基本信息
- **模型名称**: `doubao-seedream-3.0-t2i-250415`
- **提供商**: 豆包 (火山引擎)
- **类型**: 图像生成模型
- **发布日期**: 2025年4月15日
- **API端点**: `https://ark.cn-beijing.volces.com/api/v3/images/generations`

### 技术特点
- ✅ 高质量文生图
- ✅ 支持1024x1024分辨率
- ✅ 可调节引导强度
- ✅ 支持水印控制
- ✅ 快速响应时间

## 🔑 配置步骤

### 步骤1: 获取API密钥
1. 访问火山引擎控制台: https://console.volcengine.com/
2. 进入火山方舟平台
3. 在模型广场中搜索"Seedream"
4. 找到 `doubao-seedream-3.0-t2i-250415` 模型
5. 申请使用权限并等待审核通过
6. 创建API密钥并妥善保存

### 步骤2: 系统配置
1. 在系统中访问"模型配置"页面
2. 找到"豆包 (火山引擎)"分组
3. 选择"豆包 Seedream 3.0 T2I"模型
4. 输入您的API密钥
5. 点击"测试"验证连接
6. 测试成功后启用模型
7. 保存配置

## 🎨 使用场景

### 1. 角色形象生成
在角色管理页面使用"AI一键生成角色形象"功能：
- 选择"豆包 Seedream 3.0 T2I"模型
- 系统会自动生成角色的正面、侧面、背面三视图
- 支持基于角色描述的一致性生成

### 2. 场景图像生成
适用于：
- 小说场景可视化
- 角色设定插图
- 故事背景图像
- 概念设计图

## 🔧 API参数说明

### 请求参数
```json
{
  "model": "doubao-seedream-3.0-t2i-250415",
  "prompt": "角色或场景描述",
  "response_format": "url",
  "size": "1024x1024",
  "guidance_scale": 3,
  "watermark": true
}
```

### 参数详解
- **model**: 模型名称，固定为 `doubao-seedream-3.0-t2i-250415`
- **prompt**: 图像生成提示词，支持中英文
- **response_format**: 返回格式，固定为 `url`
- **size**: 图像尺寸，支持 `1024x1024`
- **guidance_scale**: 引导强度，范围1-10，默认3
- **watermark**: 是否添加水印，默认true

## 🆚 模型对比

| 模型 | 类型 | 优势 | 适用场景 |
|------|------|------|----------|
| 豆包 Seedream 3.0 T2I | 图像生成 | 高质量、快速 | 角色形象、场景图 |
| 豆包 Seedance 1.0 Pro | 视频生成 | 动态效果 | 小说转视频 |
| 智谱AI CogView | 图像生成 | 通用性强 | 各类图像生成 |
| 通义万相 | 图像生成 | 稳定可靠 | 商业应用 |

## 💡 最佳实践

### 提示词优化
1. **角色描述**: 包含外貌、服装、表情等详细信息
2. **风格指定**: 可添加"动漫风格"、"写实风格"等
3. **质量提升**: 添加"高质量"、"精细"等关键词
4. **一致性**: 使用相同的角色特征描述

### 示例提示词
```
一个年轻的女性角色，长黑发，穿着白色连衣裙，
温和的笑容，动漫风格，高质量，角色设计图
```

## 🔍 故障排除

### 常见问题

#### 1. API密钥无效
**错误**: "豆包API密钥无效，请检查配置"
**解决**: 
- 检查API密钥是否正确
- 确认模型权限已开通
- 验证账户状态

#### 2. 模型不存在
**错误**: "模型不存在或无权访问"
**解决**:
- 在火山方舟控制台申请模型权限
- 等待审核通过
- 确认使用正确的模型名称

#### 3. 生成失败
**错误**: "图像生成失败"
**解决**:
- 检查提示词是否合规
- 确认账户余额充足
- 重试请求

## 📊 性能指标

### 响应时间
- 平均生成时间: 3-8秒
- 图像分辨率: 1024x1024
- 支持并发: 根据API配额

### 质量评估
- 图像清晰度: ⭐⭐⭐⭐⭐
- 提示词理解: ⭐⭐⭐⭐⭐
- 风格一致性: ⭐⭐⭐⭐⭐
- 角色一致性: ⭐⭐⭐⭐⭐

## 🎉 总结

豆包 Seedream 3.0 T2I 模型的成功接入为系统增加了强大的图像生成能力：

✅ **完整支持**: 从配置到使用的完整流程
✅ **高质量**: 专业级图像生成效果
✅ **易用性**: 简单的配置和使用流程
✅ **一致性**: 支持角色形象一致性生成
✅ **灵活性**: 可与其他模型配合使用

现在您可以在三视图生成功能中选择豆包模型，享受高质量的图像生成服务！
