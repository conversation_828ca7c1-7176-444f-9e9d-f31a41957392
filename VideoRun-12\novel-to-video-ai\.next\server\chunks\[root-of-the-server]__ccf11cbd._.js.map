{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/skyreels.ts"], "sourcesContent": ["import { AIConfig } from '@/types'\n\n// SkyReels-V2 API响应类型\nexport interface SkyReelsTaskResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsStatusResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  progress: number\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsHealthResponse {\n  status: 'healthy' | 'unhealthy'\n  model_loaded: boolean\n  timestamp: string\n}\n\nexport interface SkyReelsGenerationParams {\n  prompt: string\n  num_frames?: number\n  guidance_scale?: number\n  seed?: number\n  fps?: number\n  resolution?: string\n}\n\n// SkyReels-V2 API客户端\nexport class SkyReelsClient {\n  private baseUrl: string\n  private apiKey: string\n  private model: string\n\n  constructor(config: AIConfig) {\n    // SkyReels是本地API，apiKey用作baseUrl\n    this.baseUrl = config.apiKey || 'http://localhost:8000'\n    this.apiKey = config.apiKey\n    this.model = config.model || 'SkyReels-V2-DF-1.3B-540P'\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(`${this.baseUrl}/health`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data: SkyReelsHealthResponse = await response.json()\n      return data.status === 'healthy' && data.model_loaded\n    } catch (error) {\n      console.error('SkyReels连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 生成视频\n  async generateVideo(params: SkyReelsGenerationParams): Promise<SkyReelsTaskResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: params.prompt,\n          num_frames: params.num_frames || 257, // 默认10秒视频\n          guidance_scale: params.guidance_scale || 6.0,\n          seed: params.seed,\n          fps: params.fps || 24,\n          resolution: params.resolution || '540P',\n        }),\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsTaskResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels视频生成失败:', error)\n      throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 查询任务状态\n  async getTaskStatus(taskId: string): Promise<SkyReelsStatusResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/status/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsStatusResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels状态查询失败:', error)\n      throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 下载视频\n  async downloadVideo(taskId: string): Promise<Blob> {\n    try {\n      const response = await fetch(`${this.baseUrl}/download/${taskId}`, {\n        method: 'GET',\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      return await response.blob()\n    } catch (error) {\n      console.error('SkyReels视频下载失败:', error)\n      throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 等待任务完成并返回视频URL\n  async generateAndWait(\n    params: SkyReelsGenerationParams,\n    maxWaitTime: number = 1800000, // 30分钟\n    pollInterval: number = 5000 // 5秒\n  ): Promise<string> {\n    console.log('🎬 开始SkyReels视频生成...')\n    \n    // 开始生成\n    const task = await this.generateVideo(params)\n    console.log(`📝 任务已创建: ${task.task_id}`)\n\n    const startTime = Date.now()\n    \n    // 轮询状态直到完成\n    while (Date.now() - startTime < maxWaitTime) {\n      const status = await this.getTaskStatus(task.task_id)\n      console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`)\n\n      if (status.status === 'completed') {\n        console.log('✅ 视频生成完成!')\n        return status.video_path || ''\n      } else if (status.status === 'failed') {\n        throw new Error(`视频生成失败: ${status.error || '未知错误'}`)\n      }\n\n      // 等待下次轮询\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n\n    throw new Error('视频生成超时')\n  }\n\n  // 获取所有任务列表\n  async getTasks(): Promise<any[]> {\n    try {\n      const response = await fetch(`${this.baseUrl}/tasks`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data = await response.json()\n      return data.tasks || []\n    } catch (error) {\n      console.error('SkyReels任务列表获取失败:', error)\n      throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n}\n\n// AI服务错误类\nexport class SkyReelsServiceError extends Error {\n  constructor(message: string, public statusCode?: number) {\n    super(message)\n    this.name = 'SkyReelsServiceError'\n  }\n}\n"], "names": [], "mappings": ";;;;AAoCO,MAAM;IACH,QAAe;IACf,OAAc;IACd,MAAa;IAErB,YAAY,MAAgB,CAAE;QAC5B,iCAAiC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,IAAI;QAChC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;IAC/B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO,KAAK,MAAM,KAAK,aAAa,KAAK,YAAY;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAgC,EAAiC;QACnF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,OAAO,MAAM;oBACrB,YAAY,OAAO,UAAU,IAAI;oBACjC,gBAAgB,OAAO,cAAc,IAAI;oBACzC,MAAM,OAAO,IAAI;oBACjB,KAAK,OAAO,GAAG,IAAI;oBACnB,YAAY,OAAO,UAAU,IAAI;gBACnC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAAc,EAAmC;QACnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAc,EAAiB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,iBAAiB;IACjB,MAAM,gBACJ,MAAgC,EAChC,cAAsB,OAAO,EAC7B,eAAuB,KAAK,KAAK;IAAN,EACV;QACjB,QAAQ,GAAG,CAAC;QAEZ,OAAO;QACP,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;QACtC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAEvC,MAAM,YAAY,KAAK,GAAG;QAE1B,WAAW;QACX,MAAO,KAAK,GAAG,KAAK,YAAY,YAAa;YAC3C,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,OAAO;YACpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,OAAO,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,UAAU,IAAI;YAC9B,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU;gBACrC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,QAAQ;YACrD;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,WAAW;IACX,MAAM,WAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,KAAK,IAAI,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;AACF;AAGO,MAAM,6BAA6B;;IACxC,YAAY,OAAe,EAAE,AAAO,UAAmB,CAAE;QACvD,KAAK,CAAC,eAD4B,aAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/ai.ts"], "sourcesContent": ["import { AIConfig, Character, Episode, PlotInfo, AnalysisResponse } from '@/types'\nimport { SkyReelsClient } from './skyreels'\n\n// AI服务错误类\nexport class AIServiceError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode: number = 500\n  ) {\n    super(message)\n    this.name = 'AIServiceError'\n  }\n}\n\n// DeepSeek API客户端\nexport class DeepSeekClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string = 'https://api.deepseek.com/v1/chat/completions'\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: '测试连接' }],\n          max_tokens: 10,\n        }),\n      })\n\n      return response.ok\n    } catch (error) {\n      console.error('DeepSeek连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 调用AI API的通用方法（公开方法）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: prompt }],\n          max_tokens: maxTokens,\n          temperature: 0.7,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new AIServiceError(\n          errorData.error?.message || 'API调用失败',\n          'API_ERROR',\n          response.status\n        )\n      }\n\n      const data = await response.json()\n      return data.choices[0]?.message?.content || ''\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        'AI服务调用失败，请检查网络连接',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 生成测试响应\n  private generateTestResponse(prompt: string): string {\n    if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {\n      // 角色一致性分析的测试响应\n      return `{\n        \"characters\": [\n          {\n            \"name\": \"张小雅\",\n            \"identity\": \"高中生\",\n            \"appearance\": \"马尾辫，甜美笑容，青春活泼\",\n            \"personality\": \"开朗活泼，善良纯真\",\n            \"clothing\": \"校服或休闲装\",\n            \"role\": \"主要角色\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"zhang_xiaoya\",\n              \"consistencyMatch\": 0.95,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持马尾辫和甜美笑容的标志性特征\"\n            }\n          },\n          {\n            \"name\": \"王美丽\",\n            \"identity\": \"咖啡店老板\",\n            \"appearance\": \"瓜子脸，波浪卷发，温柔气质\",\n            \"personality\": \"温柔善良，热情好客\",\n            \"clothing\": \"简约优雅的服装\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"wang_meili\",\n              \"consistencyMatch\": 0.88,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持瓜子脸和波浪卷发的特征\"\n            }\n          },\n          {\n            \"name\": \"李明轩\",\n            \"identity\": \"大学教授\",\n            \"appearance\": \"方脸，花白短发，学者气质\",\n            \"personality\": \"温文尔雅，博学睿智\",\n            \"clothing\": \"正式的学者装扮\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"li_mingxuan\",\n              \"consistencyMatch\": 0.92,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持方脸和花白短发的学者形象\"\n            }\n          },\n          {\n            \"name\": \"林志强\",\n            \"identity\": \"程序员\",\n            \"appearance\": \"高瘦身材，黑框眼镜，简约穿着\",\n            \"personality\": \"内向专注，技术宅\",\n            \"clothing\": \"白色T恤，牛仔裤\",\n            \"role\": \"新角色\",\n            \"isKnownCharacter\": false,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": null,\n              \"consistencyMatch\": 0.0,\n              \"differences\": [\"新出现的角色\"],\n              \"consistencyConstraints\": \"建立新的角色DNA档案\"\n            }\n          }\n        ]\n      }`\n    } else if (prompt.includes('场景信息')) {\n      return `{\n        \"scenes\": [\n          {\n            \"location\": \"温馨咖啡店\",\n            \"description\": \"装修温馨的小型咖啡店，木质桌椅，暖色调灯光\",\n            \"atmosphere\": \"温馨舒适，充满生活气息\",\n            \"timeOfDay\": \"下午\",\n            \"lighting\": \"暖色调室内灯光\",\n            \"keyElements\": \"咖啡香味，轻柔音乐，温馨装饰\"\n          }\n        ]\n      }`\n    } else if (prompt.includes('情节序列')) {\n      return `{\n        \"plotSequences\": [\n          {\n            \"sequenceId\": \"reunion_1\",\n            \"action\": \"张小雅进入咖啡店与王美丽重逢\",\n            \"emotion\": \"惊喜和温暖\",\n            \"duration\": \"2分钟\",\n            \"keyMoments\": [\"进门\", \"相认\", \"拥抱\"],\n            \"visualElements\": \"特写表情变化，温馨的重逢场面\"\n          },\n          {\n            \"sequenceId\": \"professor_arrival\",\n            \"action\": \"李明轩教授进入咖啡店\",\n            \"emotion\": \"温和友善\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"打招呼\"],\n            \"visualElements\": \"学者风度，温和笑容\"\n          },\n          {\n            \"sequenceId\": \"new_character\",\n            \"action\": \"林志强询问WiFi\",\n            \"emotion\": \"略显紧张的初次见面\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"询问\", \"介绍\"],\n            \"visualElements\": \"新角色登场，技术宅形象\"\n          }\n        ]\n      }`\n    } else {\n      return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽'\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n    \n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析AI响应失败:', error)\n      throw new AIServiceError(\n        'AI响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n    \n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// 豆包 (Doubao) API客户端\nexport class DoubaoClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string\n  private isVideoModel: boolean\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model || 'doubao-seedance-1.0-pro'\n    // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID\n    this.isVideoModel = this.model.includes('seedance') ||\n                       this.model.includes('video') ||\n                       this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式\n\n    // 根据模型类型选择正确的API端点\n    if (this.isVideoModel) {\n      // 豆包视频生成使用专门的视频生成API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'\n    } else {\n      // 文本模型使用chat completions API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'\n    }\n\n    if (config.baseUrl) {\n      this.baseUrl = config.baseUrl\n    }\n  }\n\n  // 测试API连接（带重试机制）\n  async testConnection(): Promise<boolean> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: \"测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p\"\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: '测试连接' }],\n            max_tokens: 10\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          return true\n        }\n\n        // 检查是否是可重试的错误\n        const errorText = await response.text()\n        if (errorText.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        return false\n      } catch (error) {\n        console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error)\n        if (attempt < maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n        return false\n      }\n    }\n\n    return false\n  }\n\n  // 调用AI API的通用方法（带重试机制）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: prompt }],\n            max_tokens: maxTokens,\n            temperature: 0.7\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          const data = await response.json()\n\n          if (this.isVideoModel) {\n            // 视频生成返回任务信息\n            return JSON.stringify({\n              task_id: data.task_id,\n              status: data.status || 'submitted',\n              message: '视频生成任务已提交，请稍后查询结果'\n            })\n          } else {\n            // 文本生成返回内容\n            return data.choices[0]?.message?.content || ''\n          }\n        }\n\n        const errorData = await response.json()\n        const errorMessage = errorData.error?.message || '豆包API调用失败'\n\n        // 检查是否是可重试的内部错误\n        if (errorMessage.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        // 不可重试的错误，直接抛出\n        throw new AIServiceError(\n          errorMessage,\n          'API_ERROR',\n          response.status\n        )\n      } catch (error) {\n        if (error instanceof AIServiceError) {\n          // 如果是已知的API错误且不可重试，直接抛出\n          throw error\n        }\n\n        // 网络错误等，可以重试\n        if (attempt < maxRetries) {\n          console.log(`豆包API调用失败，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        throw new AIServiceError(\n          '豆包服务调用失败，请检查网络连接和API密钥',\n          'NETWORK_ERROR',\n          500\n        )\n      }\n    }\n\n    throw new AIServiceError(\n      '豆包服务调用失败，已达到最大重试次数',\n      'MAX_RETRIES_EXCEEDED',\n      500\n    )\n  }\n\n  // 专门的视频生成方法\n  async generateVideo(prompt: string, duration: number = 5): Promise<string> {\n    if (!this.isVideoModel) {\n      throw new Error('此模型不支持视频生成')\n    }\n\n    try {\n      const requestBody = {\n        model: this.model,\n        prompt: prompt,\n        video_setting: {\n          video_duration: duration,\n          video_aspect_ratio: '16:9',\n          video_resolution: '720p'\n        }\n      }\n\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        return JSON.stringify({\n          task_id: data.task_id,\n          status: data.status || 'submitted',\n          message: '视频生成任务已提交，请稍后查询结果'\n        })\n      }\n\n      const errorData = await response.json()\n      throw new AIServiceError(\n        errorData.error?.message || '视频生成失败',\n        'VIDEO_GENERATION_ERROR',\n        response.status\n      )\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        '视频生成服务调用失败',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n\n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析豆包响应失败:', error)\n      throw new AIServiceError(\n        '豆包响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n\n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析豆包剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '豆包剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// AI客户端工厂函数\nexport function createAIClient(config: AIConfig): DeepSeekClient | DoubaoClient | SkyReelsClient {\n  switch (config.provider) {\n    case 'deepseek':\n      return new DeepSeekClient(config)\n    case 'doubao':\n      return new DoubaoClient(config)\n    case 'skyreels':\n      return new SkyReelsClient(config)\n    default:\n      // 默认使用DeepSeek客户端，但可以扩展支持其他提供商\n      return new DeepSeekClient(config)\n  }\n}\n\n// 错误处理包装器\nexport async function handleAIRequest<T>(\n  request: () => Promise<T>\n): Promise<T> {\n  try {\n    return await request()\n  } catch (error) {\n    if (error instanceof AIServiceError) {\n      throw error\n    }\n    \n    // 网络错误\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new AIServiceError(\n        'AI服务连接失败，请检查网络连接',\n        'CONNECTION_ERROR',\n        503\n      )\n    }\n    \n    // 通用错误\n    throw new AIServiceError(\n      'AI服务处理失败，请重试',\n      'UNKNOWN_ERROR',\n      500\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;AAGO,MAAM,uBAAuB;;;IAClC,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,aAAqB,GAAG,CAC/B;QACA,KAAK,CAAC,eAHC,OAAA,WACA,aAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,UAAkB,+CAA8C;IAExE,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;IAC3B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QAEvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;oBACZ,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,WAC5B,aACA,SAAS,MAAM;YAEnB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAC9C,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,oBACA,iBACA;QAEJ;IACF;IAEA,SAAS;IACD,qBAAqB,MAAc,EAAU;QACnD,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,UAAU;YACvD,eAAe;YACf,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+DP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;OAWP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BP,CAAC;QACJ,OAAO;YACL,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,eACR,cACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,QAAe;IACf,aAAqB;IAE7B,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;QAC7B,sDAAsD;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YACpB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,uBAAuB;;QAEvE,mBAAmB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,qBAAqB;YACrB,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,6BAA6B;YAC7B,IAAI,CAAC,OAAO,GAAG;QACjB;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC/B;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAmC;QACvC,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM;4BACR;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;oBACd;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,OAAO;gBACT;gBAEA,cAAc;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBAChE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;gBACzD,IAAI,UAAU,YAAY;oBACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBACA,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QACvE,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,GAAG,OAAO,gDAAgD,CAAC;4BACnE;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;wBACZ,aAAa;oBACf;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,IAAI,CAAC,YAAY,EAAE;wBACrB,aAAa;wBACb,OAAO,KAAK,SAAS,CAAC;4BACpB,SAAS,KAAK,OAAO;4BACrB,QAAQ,KAAK,MAAM,IAAI;4BACvB,SAAS;wBACX;oBACF,OAAO;wBACL,WAAW;wBACX,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;oBAC9C;gBACF;gBAEA,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,eAAe,UAAU,KAAK,EAAE,WAAW;gBAEjD,gBAAgB;gBAChB,IAAI,aAAa,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBACnE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,eAAe;gBACf,MAAM,IAAI,eACR,cACA,aACA,SAAS,MAAM;YAEnB,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,gBAAgB;oBACnC,wBAAwB;oBACxB,MAAM;gBACR;gBAEA,aAAa;gBACb,IAAI,UAAU,YAAY;oBACxB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,MAAM,IAAI,eACR,0BACA,iBACA;YAEJ;QACF;QAEA,MAAM,IAAI,eACR,sBACA,wBACA;IAEJ;IAEA,YAAY;IACZ,MAAM,cAAc,MAAc,EAAE,WAAmB,CAAC,EAAmB;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,IAAI,CAAC,KAAK;gBACjB,QAAQ;gBACR,eAAe;oBACb,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,SAAS,CAAC;oBACpB,SAAS,KAAK,OAAO;oBACrB,QAAQ,KAAK,MAAM,IAAI;oBACvB,SAAS;gBACX;YACF;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,UAC5B,0BACA,SAAS,MAAM;QAEnB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,cACA,iBACA;QAEJ;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,eACR,gBACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,SAAS,eAAe,MAAgB;IAC7C,OAAQ,OAAO,QAAQ;QACrB,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B,KAAK;YACH,OAAO,IAAI,aAAa;QAC1B,KAAK;YACH,OAAO,IAAI,wHAAA,CAAA,iBAAc,CAAC;QAC5B;YACE,+BAA+B;YAC/B,OAAO,IAAI,eAAe;IAC9B;AACF;AAGO,eAAe,gBACpB,OAAyB;IAEzB,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,gBAAgB;YACnC,MAAM;QACR;QAEA,OAAO;QACP,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,MAAM,IAAI,eACR,oBACA,oBACA;QAEJ;QAEA,OAAO;QACP,MAAM,IAAI,eACR,gBACA,iBACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/utils/storyVideoPromptGenerator.ts"], "sourcesContent": ["// 小说剧情视频提示词生成器\n// 专门用于生成保持角色和场景一致性的视频提示词\n\ninterface CharacterDNA {\n  id: string\n  name: string\n  coreFeatures: {\n    age: string\n    gender: string\n    profession: string\n    faceShape: string\n    eyes: string\n    hair: string\n    build: string\n    signatureStyle: string\n  }\n  consistentElements: {\n    facialStructure: string\n    eyeCharacteristics: string\n    hairFeatures: string\n    bodyProportions: string\n  }\n}\n\ninterface SceneDNA {\n  id: string\n  name: string\n  baseDescription: {\n    type: string\n    layout: string\n    furniture: string\n    lighting: string\n    colorScheme: string\n    atmosphere: string\n  }\n  fixedElements: {\n    architecture: string\n    furnitureLayout: string\n    lightingSetup: string\n    colorPalette: string\n  }\n  timeStates: {\n    [timeOfDay: string]: {\n      lighting: string\n      activityLevel: string\n      atmosphere: string\n    }\n  }\n}\n\ninterface PlotSequence {\n  sequenceId: string\n  action: string\n  duration: string\n  camera: string\n  emotion: string\n  keyMoments: string[]\n}\n\ninterface StoryVideoPromptOptions {\n  characters: CharacterDNA[]\n  scene: SceneDNA\n  timeOfDay: string\n  plotSequences: PlotSequence[]\n  emotionalArc: string\n  customEnhancement?: string\n  style?: 'cinematic' | 'documentary' | 'artistic'\n  quality?: 'standard' | 'high' | 'cinematic'\n  // 新增：一致性约束选项\n  consistencyMode?: 'strict' | 'balanced' | 'creative'\n  referenceImages?: { [characterId: string]: string[] }\n  consistencyWeights?: {\n    textWeight: number\n    imageWeight: number\n  }\n}\n\n// 角色DNA数据库\nconst CHARACTER_DNA_DATABASE: Record<string, CharacterDNA> = {\n  'lin_xiaoyu': {\n    id: 'lin_xiaoyu',\n    name: '林晓雨',\n    coreFeatures: {\n      age: '25 years old',\n      gender: 'Asian woman',\n      profession: 'architect',\n      faceShape: 'oval delicate face',\n      eyes: 'large intelligent brown eyes',\n      hair: 'long straight black hair',\n      build: 'tall and slender build',\n      signatureStyle: 'professional business attire'\n    },\n    consistentElements: {\n      facialStructure: 'same facial bone structure',\n      eyeCharacteristics: 'same eye shape and color',\n      hairFeatures: 'consistent hair texture and length',\n      bodyProportions: 'identical body proportions'\n    }\n  }\n}\n\n// 场景DNA数据库\nconst SCENE_DNA_DATABASE: Record<string, SceneDNA> = {\n  'modern_office': {\n    id: 'modern_office',\n    name: '现代办公室',\n    baseDescription: {\n      type: 'modern office interior',\n      layout: 'open floor plan with glass partitions',\n      furniture: 'white desks, ergonomic chairs, large windows',\n      lighting: 'natural daylight with warm LED supplements',\n      colorScheme: 'white, gray, and blue tones',\n      atmosphere: 'professional and clean'\n    },\n    fixedElements: {\n      architecture: 'floor-to-ceiling windows on east wall',\n      furnitureLayout: 'desks arranged in rows facing windows',\n      lightingSetup: 'consistent overhead LED grid',\n      colorPalette: 'same neutral color scheme'\n    },\n    timeStates: {\n      morning: {\n        lighting: 'soft golden sunlight streaming through windows',\n        activityLevel: 'quiet, few people arriving',\n        atmosphere: 'fresh and energetic'\n      },\n      midday: {\n        lighting: 'bright natural light, LED supplements on',\n        activityLevel: 'busy, people working at desks',\n        atmosphere: 'focused and productive'\n      },\n      evening: {\n        lighting: 'warm LED lighting, sunset glow',\n        activityLevel: 'some people leaving, quieter',\n        atmosphere: 'calm and reflective'\n      }\n    }\n  }\n}\n\n/**\n * 生成小说剧情视频提示词\n */\nexport function generateStoryVideoPrompt(options: StoryVideoPromptOptions): string {\n  const {\n    characters,\n    scene,\n    timeOfDay,\n    plotSequences,\n    emotionalArc,\n    customEnhancement,\n    style = 'cinematic',\n    quality = 'high',\n    consistencyMode = 'balanced',\n    referenceImages = {},\n    consistencyWeights = { textWeight: 0.7, imageWeight: 0.3 }\n  } = options\n\n  const promptSections: string[] = []\n\n  // 1. 一致性约束声明（新增）\n  promptSections.push(buildConsistencyDeclarationSection(consistencyMode, consistencyWeights))\n\n  // 2. 故事场景描述\n  promptSections.push(buildStorySceneSection(plotSequences))\n\n  // 3. 角色一致性描述（增强版）\n  promptSections.push(buildEnhancedCharacterConsistencySection(characters, referenceImages, consistencyMode))\n\n  // 4. 场景一致性描述\n  promptSections.push(buildSceneConsistencySection(scene, timeOfDay))\n\n  // 5. 动作序列描述\n  promptSections.push(buildActionSequenceSection(plotSequences, emotionalArc))\n\n  // 6. 叙事元素描述\n  promptSections.push(buildNarrativeElementsSection(plotSequences, emotionalArc))\n\n  // 7. 技术要求描述（增强版）\n  promptSections.push(buildEnhancedTechnicalRequirementsSection(style, quality, consistencyMode))\n\n  // 8. 一致性验证要求（新增）\n  promptSections.push(buildConsistencyValidationSection(characters, consistencyMode))\n\n  // 9. 自定义增强\n  if (customEnhancement && customEnhancement.trim()) {\n    promptSections.push(`CUSTOM ENHANCEMENT: ${customEnhancement}`)\n  }\n\n  return promptSections.join('\\n\\n')\n}\n\n/**\n * 构建故事场景部分\n */\nfunction buildStorySceneSection(plotSequences: PlotSequence[]): string {\n  const mainAction = plotSequences.map(seq => seq.action).join(', then ')\n  return `STORY SCENE: ${mainAction}`\n}\n\n/**\n * 构建角色一致性部分\n */\nfunction buildCharacterConsistencySection(characters: CharacterDNA[]): string {\n  const characterDescriptions = characters.map(char => {\n    const { coreFeatures, consistentElements } = char\n    return `${char.name}, same ${coreFeatures.age} ${coreFeatures.gender} ${coreFeatures.profession} as previous scenes,\n${coreFeatures.faceShape}, ${coreFeatures.eyes},\n${coreFeatures.hair}, ${coreFeatures.build},\n${consistentElements.facialStructure},\n${consistentElements.eyeCharacteristics},\nmaintaining identical facial features and professional demeanor`\n  })\n\n  return `CHARACTER CONSISTENCY:\\n${characterDescriptions.join('\\n\\n')}`\n}\n\n/**\n * 构建场景一致性部分\n */\nfunction buildSceneConsistencySection(scene: SceneDNA, timeOfDay: string): string {\n  const { baseDescription, fixedElements, timeStates } = scene\n  const timeState = timeStates[timeOfDay] || timeStates.midday\n\n  return `SCENE CONSISTENCY:\nSame ${baseDescription.type} as established,\n${fixedElements.architecture},\n${fixedElements.furnitureLayout},\nmaintaining identical ${fixedElements.lightingSetup},\n${fixedElements.colorPalette},\n${timeState.lighting},\n${timeState.atmosphere},\nconsistent office environment design`\n}\n\n/**\n * 构建动作序列部分\n */\nfunction buildActionSequenceSection(plotSequences: PlotSequence[], emotionalArc: string): string {\n  const actionDescriptions = plotSequences.map((seq, index) => {\n    const timing = index === 0 ? 'starts' : index === plotSequences.length - 1 ? 'ends' : 'then'\n    return `${timing} ${seq.action} with ${seq.emotion}`\n  })\n\n  return `ACTION SEQUENCE:\n${actionDescriptions.join(',\\n')},\nsmooth emotional progression: ${emotionalArc}`\n}\n\n/**\n * 构建叙事元素部分\n */\nfunction buildNarrativeElementsSection(plotSequences: PlotSequence[], emotionalArc: string): string {\n  const keyMoments = plotSequences.flatMap(seq => seq.keyMoments).join(', ')\n  \n  return `NARRATIVE ELEMENTS:\ncapturing ${keyMoments},\nshowing the journey through ${emotionalArc},\nemphasizing professional expertise and emotional depth,\nvisual storytelling of character development`\n}\n\n/**\n * 构建技术要求部分\n */\nfunction buildTechnicalRequirementsSection(style: string, quality: string): string {\n  const styleDescriptions = {\n    cinematic: 'cinematic quality, professional cinematography',\n    documentary: 'documentary style, natural lighting',\n    artistic: 'artistic style, creative camera work'\n  }\n\n  const qualityDescriptions = {\n    standard: 'good quality, stable footage',\n    high: 'high quality, 4K resolution, sharp focus',\n    cinematic: 'cinematic quality, professional grade, ultra high definition'\n  }\n\n  return `TECHNICAL REQUIREMENTS:\n${styleDescriptions[style]},\n${qualityDescriptions[quality]},\nconsistent lighting throughout,\nsmooth camera movements following emotional beats,\nmaintaining character and scene consistency across all frames`\n}\n\n/**\n * 为角色创建DNA档案\n */\nexport function createCharacterDNA(characterInfo: any): CharacterDNA {\n  return {\n    id: characterInfo.id || generateId(),\n    name: characterInfo.name,\n    coreFeatures: {\n      age: extractAge(characterInfo.identity) || '25 years old',\n      gender: extractGender(characterInfo.identity) || 'person',\n      profession: extractProfession(characterInfo.identity) || 'professional',\n      faceShape: extractFaceShape(characterInfo.facial) || 'oval face',\n      eyes: extractEyeFeatures(characterInfo.facial) || 'expressive eyes',\n      hair: extractHairFeatures(characterInfo.hairstyle) || 'neat hair',\n      build: extractBuildFeatures(characterInfo.physique) || 'average build',\n      signatureStyle: extractClothingStyle(characterInfo.clothing) || 'casual attire'\n    },\n    consistentElements: {\n      facialStructure: 'same facial bone structure',\n      eyeCharacteristics: 'same eye shape and color',\n      hairFeatures: 'consistent hair texture and length',\n      bodyProportions: 'identical body proportions'\n    }\n  }\n}\n\n/**\n * 为场景创建DNA档案\n */\nexport function createSceneDNA(sceneInfo: any): SceneDNA {\n  return {\n    id: sceneInfo.id || generateId(),\n    name: sceneInfo.name,\n    baseDescription: {\n      type: sceneInfo.location || 'interior space',\n      layout: sceneInfo.description || 'organized layout',\n      furniture: extractFurniture(sceneInfo.description) || 'basic furniture',\n      lighting: extractLighting(sceneInfo.atmosphere) || 'natural lighting',\n      colorScheme: extractColorScheme(sceneInfo.description) || 'neutral colors',\n      atmosphere: sceneInfo.atmosphere || 'comfortable atmosphere'\n    },\n    fixedElements: {\n      architecture: 'consistent architectural elements',\n      furnitureLayout: 'same furniture arrangement',\n      lightingSetup: 'identical lighting setup',\n      colorPalette: 'consistent color palette'\n    },\n    timeStates: {\n      morning: {\n        lighting: 'soft morning light',\n        activityLevel: 'quiet and peaceful',\n        atmosphere: 'fresh and energetic'\n      },\n      midday: {\n        lighting: 'bright natural light',\n        activityLevel: 'active and busy',\n        atmosphere: 'focused and productive'\n      },\n      evening: {\n        lighting: 'warm evening light',\n        activityLevel: 'calm and relaxed',\n        atmosphere: 'peaceful and reflective'\n      }\n    }\n  }\n}\n\n/**\n * 生成一致性检查提示词\n */\nexport function generateConsistencyCheckPrompt(\n  previousVideoPrompt: string,\n  currentVideoPrompt: string\n): string {\n  return `CONSISTENCY CHECK:\nEnsure the following elements remain identical between videos:\n\nPREVIOUS SCENE REFERENCE:\n${previousVideoPrompt}\n\nCURRENT SCENE REQUIREMENTS:\n${currentVideoPrompt}\n\nCONSISTENCY REQUIREMENTS:\n- Same character facial features and body proportions\n- Identical scene layout and architectural elements\n- Consistent lighting setup and color scheme\n- Matching furniture arrangement and spatial relationships\n- Same character clothing and styling (unless story requires change)\n- Continuous environmental details and atmosphere\n\nMAINTAIN VISUAL CONTINUITY across all generated content`\n}\n\n// 辅助函数\nfunction generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nfunction extractAge(identity: string): string {\n  const ageMatch = identity?.match(/(\\d+)岁|(\\d+)\\s*years?\\s*old/i)\n  return ageMatch ? `${ageMatch[1] || ageMatch[2]} years old` : ''\n}\n\nfunction extractGender(identity: string): string {\n  if (identity?.includes('女') || identity?.includes('woman') || identity?.includes('female')) {\n    return 'woman'\n  } else if (identity?.includes('男') || identity?.includes('man') || identity?.includes('male')) {\n    return 'man'\n  }\n  return 'person'\n}\n\nfunction extractProfession(identity: string): string {\n  const professions = ['architect', 'doctor', 'teacher', 'engineer', 'designer', 'manager']\n  for (const prof of professions) {\n    if (identity?.toLowerCase().includes(prof)) {\n      return prof\n    }\n  }\n  return 'professional'\n}\n\nfunction extractFaceShape(facial: string): string {\n  if (facial?.includes('瓜子脸')) return 'oval face'\n  if (facial?.includes('圆脸')) return 'round face'\n  if (facial?.includes('方脸')) return 'square face'\n  return 'oval face'\n}\n\nfunction extractEyeFeatures(facial: string): string {\n  if (facial?.includes('大眼睛')) return 'large expressive eyes'\n  if (facial?.includes('小眼睛')) return 'small bright eyes'\n  return 'expressive eyes'\n}\n\nfunction extractHairFeatures(hairstyle: string): string {\n  if (hairstyle?.includes('长发')) return 'long hair'\n  if (hairstyle?.includes('短发')) return 'short hair'\n  if (hairstyle?.includes('卷发')) return 'curly hair'\n  return 'neat hair'\n}\n\nfunction extractBuildFeatures(physique: string): string {\n  if (physique?.includes('高挑')) return 'tall and slender build'\n  if (physique?.includes('娇小')) return 'petite build'\n  return 'average build'\n}\n\nfunction extractClothingStyle(clothing: string): string {\n  if (clothing?.includes('职业装')) return 'professional business attire'\n  if (clothing?.includes('休闲')) return 'casual wear'\n  return 'neat clothing'\n}\n\nfunction extractFurniture(description: string): string {\n  return 'modern furniture arrangement'\n}\n\nfunction extractLighting(atmosphere: string): string {\n  return 'natural lighting'\n}\n\nfunction extractColorScheme(description: string): string {\n  return 'neutral color scheme'\n}\n\n/**\n * 构建一致性约束声明（新增）\n */\nfunction buildConsistencyDeclarationSection(mode: string, weights: { textWeight: number, imageWeight: number }): string {\n  const modeDescriptions = {\n    strict: 'STRICT CONSISTENCY MODE: Absolute adherence to character and scene specifications. Zero tolerance for deviations.',\n    balanced: 'BALANCED CONSISTENCY MODE: High fidelity to specifications with minor creative adaptations allowed.',\n    creative: 'CREATIVE CONSISTENCY MODE: Core character identity maintained while allowing artistic interpretation.'\n  }\n\n  return `CONSISTENCY CONSTRAINT SYSTEM ACTIVATED\n${modeDescriptions[mode as keyof typeof modeDescriptions]}\n\nDUAL CONSTRAINT WEIGHTS:\n- Text Constraint Weight: ${(weights.textWeight * 100).toFixed(0)}%\n- Visual Constraint Weight: ${(weights.imageWeight * 100).toFixed(0)}%\n\nCONSISTENCY PRIORITY: Character identity > Scene continuity > Artistic style`\n}\n\n/**\n * 构建增强的角色一致性描述\n */\nfunction buildEnhancedCharacterConsistencySection(\n  characters: CharacterDNA[],\n  referenceImages: { [characterId: string]: string[] },\n  consistencyMode: string\n): string {\n  if (characters.length === 0) return ''\n\n  const characterDescriptions = characters.map(char => {\n    const features = char.coreFeatures\n    const consistent = char.consistentElements\n    const hasReferenceImages = referenceImages[char.id] && referenceImages[char.id].length > 0\n\n    let description = `${char.name}: ${features.age} ${features.gender}, ${features.profession}.\nFACIAL DNA: ${features.faceShape}, ${features.eyes}, ${features.hair}.\nBODY DNA: ${features.build}.\nSTYLE DNA: ${features.signatureStyle}.\nCONSISTENCY ANCHORS: ${consistent.facialStructure}, ${consistent.eyeCharacteristics}, ${consistent.hairFeatures}, ${consistent.bodyProportions}.`\n\n    if (hasReferenceImages) {\n      description += `\nVISUAL REFERENCE: Character has ${referenceImages[char.id].length} reference image(s) for visual consistency validation.`\n    }\n\n    if (consistencyMode === 'strict') {\n      description += `\nSTRICT MODE: ZERO deviation allowed from specified features. Exact replication required.`\n    }\n\n    return description\n  }).join('\\n\\n')\n\n  return `CHARACTER CONSISTENCY MATRIX:\n${characterDescriptions}\n\nCONSISTENCY ENFORCEMENT:\n- Facial features must remain identical across all frames\n- Hair style and color must be consistent\n- Body proportions and posture must match specifications\n- Clothing style must align with character DNA\n- Expression changes allowed only within character personality bounds\n\nVALIDATION CHECKPOINTS:\n✓ Frame-by-frame facial feature verification\n✓ Hair consistency across camera angles\n✓ Body proportion maintenance\n✓ Clothing style adherence`\n}\n\n/**\n * 构建增强的技术要求描述\n */\nfunction buildEnhancedTechnicalRequirementsSection(style: string, quality: string, consistencyMode: string): string {\n  const styleDescriptions = {\n    cinematic: 'cinematic quality, professional cinematography, film-grade lighting',\n    documentary: 'documentary style, natural lighting, authentic feel',\n    artistic: 'artistic style, creative camera work, stylized visuals'\n  }\n\n  const qualityDescriptions = {\n    standard: 'good quality, stable footage, clear details',\n    high: 'high quality, 4K resolution, sharp focus, professional grade',\n    cinematic: 'cinematic quality, ultra high definition, film-grade production'\n  }\n\n  const consistencyRequirements = {\n    strict: 'STRICT: Pixel-perfect consistency, identical lighting conditions, exact camera positioning',\n    balanced: 'BALANCED: High consistency with natural variations, smooth transitions',\n    creative: 'CREATIVE: Core consistency maintained with artistic freedom in presentation'\n  }\n\n  return `ENHANCED TECHNICAL REQUIREMENTS:\n${styleDescriptions[style]},\n${qualityDescriptions[quality]},\n${consistencyRequirements[consistencyMode as keyof typeof consistencyRequirements]},\n\nCONSISTENCY TECHNICAL SPECS:\n- Maintain identical character proportions across all frames\n- Consistent lighting temperature and direction\n- Smooth camera movements preserving character visibility\n- Color grading consistency for scene continuity\n- Frame-by-frame character feature validation\n- Temporal consistency in motion and expression`\n}\n\n/**\n * 构建一致性验证要求\n */\nfunction buildConsistencyValidationSection(characters: CharacterDNA[], consistencyMode: string): string {\n  const characterNames = characters.map(char => char.name).join(', ')\n\n  const validationLevels = {\n    strict: 'STRICT VALIDATION: Every frame must pass character identity verification',\n    balanced: 'BALANCED VALIDATION: Core features verified with tolerance for natural variation',\n    creative: 'CREATIVE VALIDATION: Character essence maintained with artistic interpretation allowed'\n  }\n\n  return `CONSISTENCY VALIDATION PROTOCOL:\n${validationLevels[consistencyMode as keyof typeof validationLevels]}\n\nVALIDATION TARGETS: ${characterNames}\n\nAUTOMATED CHECKS:\n1. Facial feature consistency (eyes, nose, mouth, face shape)\n2. Hair style and color maintenance\n3. Body proportion verification\n4. Clothing style adherence\n5. Scene element continuity\n6. Lighting consistency\n7. Color palette maintenance\n\nQUALITY GATES:\n- Pre-generation: Prompt consistency verification\n- Mid-generation: Frame sampling validation\n- Post-generation: Full video consistency audit\n\nFAILURE HANDLING:\n- Inconsistency detected → Regeneration with enhanced constraints\n- Partial inconsistency → Selective frame regeneration\n- Systematic inconsistency → DNA profile refinement`\n}\n"], "names": [], "mappings": "AAAA,eAAe;AACf,yBAAyB;;;;;;;AA4EzB,WAAW;AACX,MAAM,yBAAuD;IAC3D,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,cAAc;YACZ,KAAK;YACL,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,MAAM;YACN,MAAM;YACN,OAAO;YACP,gBAAgB;QAClB;QACA,oBAAoB;YAClB,iBAAiB;YACjB,oBAAoB;YACpB,cAAc;YACd,iBAAiB;QACnB;IACF;AACF;AAEA,WAAW;AACX,MAAM,qBAA+C;IACnD,iBAAiB;QACf,IAAI;QACJ,MAAM;QACN,iBAAiB;YACf,MAAM;YACN,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,YAAY;QACd;QACA,eAAe;YACb,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,cAAc;QAChB;QACA,YAAY;YACV,SAAS;gBACP,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;YACA,QAAQ;gBACN,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;YACA,SAAS;gBACP,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;QACF;IACF;AACF;AAKO,SAAS,yBAAyB,OAAgC;IACvE,MAAM,EACJ,UAAU,EACV,KAAK,EACL,SAAS,EACT,aAAa,EACb,YAAY,EACZ,iBAAiB,EACjB,QAAQ,WAAW,EACnB,UAAU,MAAM,EAChB,kBAAkB,UAAU,EAC5B,kBAAkB,CAAC,CAAC,EACpB,qBAAqB;QAAE,YAAY;QAAK,aAAa;IAAI,CAAC,EAC3D,GAAG;IAEJ,MAAM,iBAA2B,EAAE;IAEnC,iBAAiB;IACjB,eAAe,IAAI,CAAC,mCAAmC,iBAAiB;IAExE,YAAY;IACZ,eAAe,IAAI,CAAC,uBAAuB;IAE3C,kBAAkB;IAClB,eAAe,IAAI,CAAC,yCAAyC,YAAY,iBAAiB;IAE1F,aAAa;IACb,eAAe,IAAI,CAAC,6BAA6B,OAAO;IAExD,YAAY;IACZ,eAAe,IAAI,CAAC,2BAA2B,eAAe;IAE9D,YAAY;IACZ,eAAe,IAAI,CAAC,8BAA8B,eAAe;IAEjE,iBAAiB;IACjB,eAAe,IAAI,CAAC,0CAA0C,OAAO,SAAS;IAE9E,iBAAiB;IACjB,eAAe,IAAI,CAAC,kCAAkC,YAAY;IAElE,WAAW;IACX,IAAI,qBAAqB,kBAAkB,IAAI,IAAI;QACjD,eAAe,IAAI,CAAC,CAAC,oBAAoB,EAAE,mBAAmB;IAChE;IAEA,OAAO,eAAe,IAAI,CAAC;AAC7B;AAEA;;CAEC,GACD,SAAS,uBAAuB,aAA6B;IAC3D,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM,EAAE,IAAI,CAAC;IAC7D,OAAO,CAAC,aAAa,EAAE,YAAY;AACrC;AAEA;;CAEC,GACD,SAAS,iCAAiC,UAA0B;IAClE,MAAM,wBAAwB,WAAW,GAAG,CAAC,CAAA;QAC3C,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,GAAG;QAC7C,OAAO,GAAG,KAAK,IAAI,CAAC,OAAO,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,aAAa,UAAU,CAAC;AACpG,EAAE,aAAa,SAAS,CAAC,EAAE,EAAE,aAAa,IAAI,CAAC;AAC/C,EAAE,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC;AAC3C,EAAE,mBAAmB,eAAe,CAAC;AACrC,EAAE,mBAAmB,kBAAkB,CAAC;+DACuB,CAAC;IAC9D;IAEA,OAAO,CAAC,wBAAwB,EAAE,sBAAsB,IAAI,CAAC,SAAS;AACxE;AAEA;;CAEC,GACD,SAAS,6BAA6B,KAAe,EAAE,SAAiB;IACtE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG;IACvD,MAAM,YAAY,UAAU,CAAC,UAAU,IAAI,WAAW,MAAM;IAE5D,OAAO,CAAC;KACL,EAAE,gBAAgB,IAAI,CAAC;AAC5B,EAAE,cAAc,YAAY,CAAC;AAC7B,EAAE,cAAc,eAAe,CAAC;sBACV,EAAE,cAAc,aAAa,CAAC;AACpD,EAAE,cAAc,YAAY,CAAC;AAC7B,EAAE,UAAU,QAAQ,CAAC;AACrB,EAAE,UAAU,UAAU,CAAC;oCACa,CAAC;AACrC;AAEA;;CAEC,GACD,SAAS,2BAA2B,aAA6B,EAAE,YAAoB;IACrF,MAAM,qBAAqB,cAAc,GAAG,CAAC,CAAC,KAAK;QACjD,MAAM,SAAS,UAAU,IAAI,WAAW,UAAU,cAAc,MAAM,GAAG,IAAI,SAAS;QACtF,OAAO,GAAG,OAAO,CAAC,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,OAAO,EAAE;IACtD;IAEA,OAAO,CAAC;AACV,EAAE,mBAAmB,IAAI,CAAC,OAAO;8BACH,EAAE,cAAc;AAC9C;AAEA;;CAEC,GACD,SAAS,8BAA8B,aAA6B,EAAE,YAAoB;IACxF,MAAM,aAAa,cAAc,OAAO,CAAC,CAAA,MAAO,IAAI,UAAU,EAAE,IAAI,CAAC;IAErE,OAAO,CAAC;UACA,EAAE,WAAW;4BACK,EAAE,aAAa;;4CAEC,CAAC;AAC7C;AAEA;;CAEC,GACD,SAAS,kCAAkC,KAAa,EAAE,OAAe;IACvE,MAAM,oBAAoB;QACxB,WAAW;QACX,aAAa;QACb,UAAU;IACZ;IAEA,MAAM,sBAAsB;QAC1B,UAAU;QACV,MAAM;QACN,WAAW;IACb;IAEA,OAAO,CAAC;AACV,EAAE,iBAAiB,CAAC,MAAM,CAAC;AAC3B,EAAE,mBAAmB,CAAC,QAAQ,CAAC;;;6DAG8B,CAAC;AAC9D;AAKO,SAAS,mBAAmB,aAAkB;IACnD,OAAO;QACL,IAAI,cAAc,EAAE,IAAI;QACxB,MAAM,cAAc,IAAI;QACxB,cAAc;YACZ,KAAK,WAAW,cAAc,QAAQ,KAAK;YAC3C,QAAQ,cAAc,cAAc,QAAQ,KAAK;YACjD,YAAY,kBAAkB,cAAc,QAAQ,KAAK;YACzD,WAAW,iBAAiB,cAAc,MAAM,KAAK;YACrD,MAAM,mBAAmB,cAAc,MAAM,KAAK;YAClD,MAAM,oBAAoB,cAAc,SAAS,KAAK;YACtD,OAAO,qBAAqB,cAAc,QAAQ,KAAK;YACvD,gBAAgB,qBAAqB,cAAc,QAAQ,KAAK;QAClE;QACA,oBAAoB;YAClB,iBAAiB;YACjB,oBAAoB;YACpB,cAAc;YACd,iBAAiB;QACnB;IACF;AACF;AAKO,SAAS,eAAe,SAAc;IAC3C,OAAO;QACL,IAAI,UAAU,EAAE,IAAI;QACpB,MAAM,UAAU,IAAI;QACpB,iBAAiB;YACf,MAAM,UAAU,QAAQ,IAAI;YAC5B,QAAQ,UAAU,WAAW,IAAI;YACjC,WAAW,iBAAiB,UAAU,WAAW,KAAK;YACtD,UAAU,gBAAgB,UAAU,UAAU,KAAK;YACnD,aAAa,mBAAmB,UAAU,WAAW,KAAK;YAC1D,YAAY,UAAU,UAAU,IAAI;QACtC;QACA,eAAe;YACb,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,cAAc;QAChB;QACA,YAAY;YACV,SAAS;gBACP,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;YACA,QAAQ;gBACN,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;YACA,SAAS;gBACP,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;QACF;IACF;AACF;AAKO,SAAS,+BACd,mBAA2B,EAC3B,kBAA0B;IAE1B,OAAO,CAAC;;;;AAIV,EAAE,oBAAoB;;;AAGtB,EAAE,mBAAmB;;;;;;;;;;uDAUkC,CAAC;AACxD;AAEA,OAAO;AACP,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEA,SAAS,WAAW,QAAgB;IAClC,MAAM,WAAW,UAAU,MAAM;IACjC,OAAO,WAAW,GAAG,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG;AAChE;AAEA,SAAS,cAAc,QAAgB;IACrC,IAAI,UAAU,SAAS,QAAQ,UAAU,SAAS,YAAY,UAAU,SAAS,WAAW;QAC1F,OAAO;IACT,OAAO,IAAI,UAAU,SAAS,QAAQ,UAAU,SAAS,UAAU,UAAU,SAAS,SAAS;QAC7F,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,QAAgB;IACzC,MAAM,cAAc;QAAC;QAAa;QAAU;QAAW;QAAY;QAAY;KAAU;IACzF,KAAK,MAAM,QAAQ,YAAa;QAC9B,IAAI,UAAU,cAAc,SAAS,OAAO;YAC1C,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,MAAc;IACtC,IAAI,QAAQ,SAAS,QAAQ,OAAO;IACpC,IAAI,QAAQ,SAAS,OAAO,OAAO;IACnC,IAAI,QAAQ,SAAS,OAAO,OAAO;IACnC,OAAO;AACT;AAEA,SAAS,mBAAmB,MAAc;IACxC,IAAI,QAAQ,SAAS,QAAQ,OAAO;IACpC,IAAI,QAAQ,SAAS,QAAQ,OAAO;IACpC,OAAO;AACT;AAEA,SAAS,oBAAoB,SAAiB;IAC5C,IAAI,WAAW,SAAS,OAAO,OAAO;IACtC,IAAI,WAAW,SAAS,OAAO,OAAO;IACtC,IAAI,WAAW,SAAS,OAAO,OAAO;IACtC,OAAO;AACT;AAEA,SAAS,qBAAqB,QAAgB;IAC5C,IAAI,UAAU,SAAS,OAAO,OAAO;IACrC,IAAI,UAAU,SAAS,OAAO,OAAO;IACrC,OAAO;AACT;AAEA,SAAS,qBAAqB,QAAgB;IAC5C,IAAI,UAAU,SAAS,QAAQ,OAAO;IACtC,IAAI,UAAU,SAAS,OAAO,OAAO;IACrC,OAAO;AACT;AAEA,SAAS,iBAAiB,WAAmB;IAC3C,OAAO;AACT;AAEA,SAAS,gBAAgB,UAAkB;IACzC,OAAO;AACT;AAEA,SAAS,mBAAmB,WAAmB;IAC7C,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,mCAAmC,IAAY,EAAE,OAAoD;IAC5G,MAAM,mBAAmB;QACvB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IAEA,OAAO,CAAC;AACV,EAAE,gBAAgB,CAAC,KAAsC,CAAC;;;0BAGhC,EAAE,CAAC,QAAQ,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG;4BACtC,EAAE,CAAC,QAAQ,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG;;4EAEO,CAAC;AAC7E;AAEA;;CAEC,GACD,SAAS,yCACP,UAA0B,EAC1B,eAAoD,EACpD,eAAuB;IAEvB,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,MAAM,wBAAwB,WAAW,GAAG,CAAC,CAAA;QAC3C,MAAM,WAAW,KAAK,YAAY;QAClC,MAAM,aAAa,KAAK,kBAAkB;QAC1C,MAAM,qBAAqB,eAAe,CAAC,KAAK,EAAE,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG;QAEzF,IAAI,cAAc,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,CAAC;YACnF,EAAE,SAAS,SAAS,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC;UAC3D,EAAE,SAAS,KAAK,CAAC;WAChB,EAAE,SAAS,cAAc,CAAC;qBAChB,EAAE,WAAW,eAAe,CAAC,EAAE,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,WAAW,eAAe,CAAC,CAAC,CAAC;QAE7I,IAAI,oBAAoB;YACtB,eAAe,CAAC;gCACU,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,sDAAsD,CAAC;QACrH;QAEA,IAAI,oBAAoB,UAAU;YAChC,eAAe,CAAC;wFACkE,CAAC;QACrF;QAEA,OAAO;IACT,GAAG,IAAI,CAAC;IAER,OAAO,CAAC;AACV,EAAE,sBAAsB;;;;;;;;;;;;;0BAaE,CAAC;AAC3B;AAEA;;CAEC,GACD,SAAS,0CAA0C,KAAa,EAAE,OAAe,EAAE,eAAuB;IACxG,MAAM,oBAAoB;QACxB,WAAW;QACX,aAAa;QACb,UAAU;IACZ;IAEA,MAAM,sBAAsB;QAC1B,UAAU;QACV,MAAM;QACN,WAAW;IACb;IAEA,MAAM,0BAA0B;QAC9B,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IAEA,OAAO,CAAC;AACV,EAAE,iBAAiB,CAAC,MAAM,CAAC;AAC3B,EAAE,mBAAmB,CAAC,QAAQ,CAAC;AAC/B,EAAE,uBAAuB,CAAC,gBAAwD,CAAC;;;;;;;;+CAQpC,CAAC;AAChD;AAEA;;CAEC,GACD,SAAS,kCAAkC,UAA0B,EAAE,eAAuB;IAC5F,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IAE9D,MAAM,mBAAmB;QACvB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IAEA,OAAO,CAAC;AACV,EAAE,gBAAgB,CAAC,gBAAiD,CAAC;;oBAEjD,EAAE,eAAe;;;;;;;;;;;;;;;;;;;mDAmBc,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/analyze-detailed-plot/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { createAIClient, AIServiceError, handleAIRequest } from '@/lib/ai'\nimport { generateStoryVideoPrompt, createCharacterDNA, createSceneDNA } from '@/utils/storyVideoPromptGenerator'\n\n// GET - 获取已保存的详细剧情信息\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const episodeId = searchParams.get('episodeId')\n\n    if (!episodeId) {\n      return NextResponse.json(\n        { success: false, error: '缺少episodeId参数' },\n        { status: 400 }\n      )\n    }\n\n    // 查找已保存的详细剧情信息\n    const plotInfo = await prisma.plotInfo.findUnique({\n      where: { episodeId }\n    })\n\n    if (!plotInfo) {\n      return NextResponse.json({\n        success: false,\n        error: '未找到详细剧情信息'\n      })\n    }\n\n    // 检查是否有任何详细信息\n    const hasDetailedInfo = plotInfo.detailedCharacters ||\n                           plotInfo.detailedScenes ||\n                           plotInfo.plotSequences ||\n                           plotInfo.emotionalArc ||\n                           plotInfo.generatedPrompt\n\n    if (!hasDetailedInfo) {\n      return NextResponse.json({\n        success: false,\n        error: '未找到详细剧情信息'\n      })\n    }\n\n    // 解析保存的数据\n    let characters = []\n    let scenes = []\n    let plotSequences = []\n\n    try {\n      characters = plotInfo.detailedCharacters ? JSON.parse(plotInfo.detailedCharacters) : []\n    } catch (error) {\n      console.error('解析detailedCharacters失败:', error)\n      characters = []\n    }\n\n    try {\n      scenes = plotInfo.detailedScenes ? JSON.parse(plotInfo.detailedScenes) : []\n    } catch (error) {\n      console.error('解析detailedScenes失败:', error)\n      scenes = []\n    }\n\n    try {\n      plotSequences = plotInfo.plotSequences ? JSON.parse(plotInfo.plotSequences) : []\n    } catch (error) {\n      console.error('解析plotSequences失败:', error)\n      plotSequences = []\n    }\n\n    const analysisResult = {\n      characters,\n      scenes,\n      plotSequences,\n      emotionalArc: plotInfo.emotionalArc || '',\n      generatedPrompt: plotInfo.generatedPrompt || ''\n    }\n\n\n    return NextResponse.json({\n      success: true,\n      data: analysisResult,\n      message: '详细剧情信息获取成功'\n    })\n  } catch (error) {\n    console.error('获取详细剧情信息失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取失败，请重试' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - 分析详细剧情信息\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('📥 收到POST请求')\n    const body = await request.json()\n    console.log('📥 请求体:', { episodeId: body.episodeId, hasContent: !!body.episodeContent })\n    const { episodeId, episodeContent, customPrompt } = body\n\n    if (!episodeId || !episodeContent) {\n      console.log('❌ 缺少必要参数')\n      return NextResponse.json(\n        { success: false, error: '缺少必要参数' },\n        { status: 400 }\n      )\n    }\n\n    // 获取AI配置\n    const aiConfig = await prisma.aIConfig.findFirst({\n      where: { enabled: true }\n    })\n\n    if (!aiConfig) {\n      return NextResponse.json(\n        { success: false, error: '请先配置AI模型' },\n        { status: 400 }\n      )\n    }\n\n    // 分析详细剧情信息（传入episodeId用于一致性约束）\n    let analysisResult\n    try {\n      console.log('开始调用analyzeDetailedPlotInfo...')\n      analysisResult = await analyzeDetailedPlotInfo(aiConfig, episodeContent, customPrompt, episodeId)\n      console.log('分析完成，结果:', analysisResult)\n    } catch (error) {\n      console.error('分析过程出错:', error)\n      console.error('错误详情:', error.stack)\n      throw error\n    }\n\n    // 保存详细剧情信息到数据库\n    try {\n      await saveDetailedPlotInfo(episodeId, analysisResult)\n      console.log('保存完成')\n    } catch (error) {\n      console.error('保存过程出错:', error)\n      // 继续执行，不影响返回结果\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: analysisResult,\n      message: '剧情信息分析完成'\n    })\n  } catch (error) {\n    console.error('分析详细剧情失败:', error)\n    // 返回更详细的错误信息用于调试\n    const errorMessage = error instanceof Error ? error.message : '分析失败，请重试'\n    return NextResponse.json(\n      { success: false, error: errorMessage, details: error },\n      { status: 500 }\n    )\n  }\n}\n\n// 分析详细剧情信息（增强版：集成角色一致性约束）\nasync function analyzeDetailedPlotInfo(aiConfig: any, episodeContent: string, customPrompt?: string, episodeId?: string) {\n  try {\n    console.log('开始详细剧情分析（一致性增强版）...')\n    console.log('AI配置:', { provider: aiConfig.provider, model: aiConfig.model, hasApiKey: !!aiConfig.apiKey })\n\n    const aiClient = createAIClient(aiConfig)\n\n    // 1. 获取项目角色库（用于一致性约束）\n    let projectCharacters: any[] = []\n    if (episodeId) {\n      try {\n        const episode = await prisma.episode.findUnique({\n          where: { id: episodeId },\n          include: {\n            project: {\n              include: {\n                characters: true\n              }\n            }\n          }\n        })\n        projectCharacters = episode?.project?.characters || []\n        console.log('获取项目角色库，角色数量:', projectCharacters.length)\n      } catch (error) {\n        console.error('获取项目角色库失败:', error)\n      }\n    }\n\n    // 强制进行真实的AI分析，不使用任何测试数据\n    console.log('正在调用真实AI API进行分析...')\n\n    // 2. 分析角色信息（增强版：包含一致性约束）\n    console.log('开始分析角色信息（一致性增强）...')\n    const charactersResult = await handleAIRequest(() =>\n      analyzeCharactersWithConsistency(aiClient, episodeContent, projectCharacters)\n    )\n    console.log('角色分析完成，结果数量:', charactersResult.length)\n\n    // 2. 分析场景信息\n    console.log('开始分析场景信息...')\n    const scenesResult = await handleAIRequest(() =>\n      analyzeScenes(aiClient, episodeContent)\n    )\n    console.log('场景分析完成，结果数量:', scenesResult.length)\n\n    // 3. 分析情节序列\n    console.log('开始分析情节序列...')\n    const plotSequencesResult = await handleAIRequest(() =>\n      analyzePlotSequences(aiClient, episodeContent)\n    )\n    console.log('情节序列分析完成，结果数量:', plotSequencesResult.length)\n\n    // 4. 分析情感弧线\n    console.log('开始分析情感弧线...')\n    const emotionalArcResult = await handleAIRequest(() =>\n      analyzeEmotionalArc(aiClient, episodeContent)\n    )\n    console.log('情感弧线分析完成，长度:', emotionalArcResult.length)\n\n    // 5. 创建角色DNA档案（增强版：包含一致性信息）\n    const characterDNAs = charactersResult.map((char: any) => {\n      const dna = createCharacterDNA({\n        id: char.name?.toLowerCase().replace(/\\s+/g, '_') || 'character',\n        name: char.name || '角色',\n        identity: char.identity,\n        facial: char.appearance,\n        personality: char.personality,\n        clothing: char.clothing\n      })\n\n      // 添加一致性信息\n      if (char.consistencyInfo) {\n        dna.consistencyInfo = char.consistencyInfo\n      }\n\n      return dna\n    })\n\n    // 6. 创建场景DNA档案\n    const sceneDNAs = scenesResult.map((scene: any) =>\n      createSceneDNA({\n        id: scene.location?.toLowerCase().replace(/\\s+/g, '_') || 'scene',\n        name: scene.location || '场景',\n        location: scene.location,\n        description: scene.description,\n        atmosphere: scene.atmosphere\n      })\n    )\n\n    // 7. 生成专业视频提示词\n    const generatedPrompt = generateStoryVideoPrompt({\n      characters: characterDNAs,\n      scene: sceneDNAs[0] || createSceneDNA({ id: 'default', name: '默认场景' }),\n      timeOfDay: 'midday',\n      plotSequences: plotSequencesResult,\n      emotionalArc: emotionalArcResult,\n      customEnhancement: customPrompt,\n      style: 'cinematic',\n      quality: 'high'\n    })\n\n    return {\n      characters: charactersResult,\n      scenes: scenesResult,\n      plotSequences: plotSequencesResult,\n      emotionalArc: emotionalArcResult,\n      characterDNAs,\n      sceneDNAs,\n      generatedPrompt\n    }\n  } catch (error) {\n    console.error('详细剧情分析失败:', error)\n    throw error\n  }\n}\n\n// 分析角色信息\nasync function analyzeCharacters(aiClient: any, episodeContent: string) {\n  const prompt = `你是一个专业的小说分析师。请仔细分析以下剧集内容，提取其中出现的所有角色信息。\n\n剧集内容：\n${episodeContent}\n\n请按照以下JSON格式返回角色信息：\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"identity\": \"身份职业和基本信息\",\n      \"appearance\": \"外貌特征描述\",\n      \"personality\": \"性格特点\",\n      \"clothing\": \"服装描述\",\n      \"role\": \"在本集中的作用\"\n    }\n  ]\n}\n\n要求：\n1. 只提取在本集中实际出现的角色\n2. 外貌描述要具体详细\n3. 性格分析要基于文本中的行为表现\n4. 如果文中没有明确描述某项信息，可以合理推断但要标注\n5. 返回标准JSON格式`\n\n  const response = await aiClient.callAPI(prompt, 4000)\n  \n  try {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n    const result = JSON.parse(jsonMatch[0])\n    return result.characters || []\n  } catch (error) {\n    console.error('解析角色分析响应失败:', error)\n    return []\n  }\n}\n\n// 分析场景信息\nasync function analyzeScenes(aiClient: any, episodeContent: string) {\n  const prompt = `你是一个专业的场景设计师。请仔细分析以下剧集内容，提取其中的场景信息。\n\n剧集内容：\n${episodeContent}\n\n请按照以下JSON格式返回场景信息：\n{\n  \"scenes\": [\n    {\n      \"location\": \"场景地点名称\",\n      \"description\": \"场景详细描述\",\n      \"atmosphere\": \"氛围和环境特点\",\n      \"timeOfDay\": \"时间段\",\n      \"lighting\": \"光线条件\",\n      \"keyElements\": \"关键环境元素\"\n    }\n  ]\n}\n\n要求：\n1. 提取所有在本集中出现的场景\n2. 描述要具体详细，包含空间布局\n3. 氛围描述要体现情感色彩\n4. 注意光线和时间的描述\n5. 返回标准JSON格式`\n\n  const response = await aiClient.callAPI(prompt, 3000)\n  \n  try {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n    const result = JSON.parse(jsonMatch[0])\n    return result.scenes || []\n  } catch (error) {\n    console.error('解析场景分析响应失败:', error)\n    return []\n  }\n}\n\n// 分析情节序列\nasync function analyzePlotSequences(aiClient: any, episodeContent: string) {\n  const prompt = `你是一个专业的剧本分析师。请仔细分析以下剧集内容，将情节分解为可视化的动作序列。\n\n剧集内容：\n${episodeContent}\n\n请按照以下JSON格式返回情节序列：\n{\n  \"plotSequences\": [\n    {\n      \"sequenceId\": \"序列编号\",\n      \"action\": \"具体动作描述\",\n      \"emotion\": \"情感状态\",\n      \"duration\": \"预估时长\",\n      \"keyMoments\": [\"关键时刻1\", \"关键时刻2\"],\n      \"visualElements\": \"视觉表现要点\"\n    }\n  ]\n}\n\n要求：\n1. 将剧情分解为3-5个主要序列\n2. 每个序列要有明确的动作和情感\n3. 关键时刻要具体可视化\n4. 考虑视频制作的可行性\n5. 返回标准JSON格式`\n\n  const response = await aiClient.callAPI(prompt, 3000)\n  \n  try {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n    const result = JSON.parse(jsonMatch[0])\n    return result.plotSequences || []\n  } catch (error) {\n    console.error('解析情节序列响应失败:', error)\n    return []\n  }\n}\n\n// 分析情感弧线\nasync function analyzeEmotionalArc(aiClient: any, episodeContent: string) {\n  const prompt = `你是一个专业的情感分析师。请仔细分析以下剧集内容，总结整体的情感发展弧线。\n\n剧集内容：\n${episodeContent}\n\n请分析并返回情感弧线描述，格式如下：\n开始情感 → 发展过程 → 转折点 → 高潮情感 → 结束情感\n\n要求：\n1. 分析主要角色的情感变化\n2. 识别关键的情感转折点\n3. 描述要简洁明了\n4. 适合视频表现的情感节奏\n5. 用箭头连接不同阶段\n\n示例：平静专注 → 遇到困难 → 灵感突现 → 兴奋创作 → 满足成就`\n\n  const response = await aiClient.callAPI(prompt, 2000)\n  \n  // 直接返回文本，不需要JSON解析\n  return response.trim()\n}\n\n// 分析角色信息（增强版：包含一致性约束）\nasync function analyzeCharactersWithConsistency(aiClient: any, episodeContent: string, projectCharacters: any[]) {\n  // 构建包含项目角色库信息的提示词\n  let characterConstraints = ''\n  if (projectCharacters.length > 0) {\n    characterConstraints = `\\n\\n已知项目角色库：\\n${projectCharacters.map(char => {\n      const details = []\n      if (char.identity) details.push(`身份: ${char.identity}`)\n      if (char.facial) details.push(`外貌: ${char.facial}`)\n      if (char.personality) details.push(`性格: ${char.personality}`)\n      if (char.consistencyScore) details.push(`一致性评分: ${char.consistencyScore}`)\n\n      return `- ${char.name}: ${details.join(', ')}`\n    }).join('\\n')}\\n\\n请在分析时：\n1. 如果剧集中的角色与已知角色匹配，请保持一致性约束\n2. 标注角色是否为已知角色（isKnownCharacter: true/false）\n3. 如果是已知角色，提供一致性匹配度评分（consistencyMatch: 0.0-1.0）\n4. 指出与已知角色的差异（如有）`\n  }\n\n  const prompt = `你是一个专业的小说分析师和角色一致性专家。请仔细分析以下剧集内容，提取其中出现的所有角色信息，并进行一致性约束分析。\n\n剧集内容：\n${episodeContent}${characterConstraints}\n\n请按照以下JSON格式返回角色信息：\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"identity\": \"身份职业和基本信息\",\n      \"appearance\": \"外貌特征描述\",\n      \"personality\": \"性格特点\",\n      \"clothing\": \"服装描述\",\n      \"role\": \"在本集中的作用\",\n      \"isKnownCharacter\": true/false,\n      \"consistencyInfo\": {\n        \"matchedCharacterId\": \"匹配的已知角色ID（如果有）\",\n        \"consistencyMatch\": 0.95,\n        \"differences\": [\"与已知角色的差异列表\"],\n        \"consistencyConstraints\": \"一致性约束建议\"\n      }\n    }\n  ]\n}\n\n要求：\n1. 只提取在本集中实际出现的角色\n2. 外貌描述要具体详细，符合一致性要求\n3. 性格分析要基于文本中的行为表现\n4. 如果是已知角色，必须保持与项目角色库的一致性\n5. 提供详细的一致性分析和约束建议\n6. 返回标准JSON格式`\n\n  const response = await aiClient.callAPI(prompt, 5000)\n\n  try {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n    const result = JSON.parse(jsonMatch[0])\n    return result.characters || []\n  } catch (error) {\n    console.error('解析角色分析响应失败:', error)\n    // 降级到原始分析方法\n    return await analyzeCharacters(aiClient, episodeContent)\n  }\n}\n\n// 保存详细剧情信息到数据库 - 使用与基础剧情信息相同的存储方式\nasync function saveDetailedPlotInfo(episodeId: string, analysisResult: any) {\n  try {\n    // 使用upsert操作，与基础剧情分析保持一致\n    await prisma.plotInfo.upsert({\n      where: { episodeId },\n      update: {\n        // 更新详细信息字段\n        detailedCharacters: JSON.stringify(analysisResult.characters || []),\n        detailedScenes: JSON.stringify(analysisResult.scenes || []),\n        plotSequences: JSON.stringify(analysisResult.plotSequences || []),\n        emotionalArc: analysisResult.emotionalArc || '',\n        generatedPrompt: analysisResult.generatedPrompt || '',\n        updatedAt: new Date()\n      },\n      create: {\n        episodeId,\n        // 基础字段，保持与基础剧情分析的兼容性\n        characters: '[]',\n        scenes: '[]',\n        events: '[]',\n        // 详细信息字段\n        detailedCharacters: JSON.stringify(analysisResult.characters || []),\n        detailedScenes: JSON.stringify(analysisResult.scenes || []),\n        plotSequences: JSON.stringify(analysisResult.plotSequences || []),\n        emotionalArc: analysisResult.emotionalArc || '',\n        generatedPrompt: analysisResult.generatedPrompt || ''\n      }\n    })\n  } catch (error) {\n    console.error('保存详细剧情信息失败:', error)\n    // 不抛出错误，避免影响主流程\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgB,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAU;QACrB;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT;QACF;QAEA,cAAc;QACd,MAAM,kBAAkB,SAAS,kBAAkB,IAC5B,SAAS,cAAc,IACvB,SAAS,aAAa,IACtB,SAAS,YAAY,IACrB,SAAS,eAAe;QAE/C,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT;QACF;QAEA,UAAU;QACV,IAAI,aAAa,EAAE;QACnB,IAAI,SAAS,EAAE;QACf,IAAI,gBAAgB,EAAE;QAEtB,IAAI;YACF,aAAa,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,SAAS,kBAAkB,IAAI,EAAE;QACzF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,aAAa,EAAE;QACjB;QAEA,IAAI;YACF,SAAS,SAAS,cAAc,GAAG,KAAK,KAAK,CAAC,SAAS,cAAc,IAAI,EAAE;QAC7E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,EAAE;QACb;QAEA,IAAI;YACF,gBAAgB,SAAS,aAAa,GAAG,KAAK,KAAK,CAAC,SAAS,aAAa,IAAI,EAAE;QAClF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,gBAAgB,EAAE;QACpB;QAEA,MAAM,iBAAiB;YACrB;YACA;YACA;YACA,cAAc,SAAS,YAAY,IAAI;YACvC,iBAAiB,SAAS,eAAe,IAAI;QAC/C;QAGA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,WAAW;YAAE,WAAW,KAAK,SAAS;YAAE,YAAY,CAAC,CAAC,KAAK,cAAc;QAAC;QACtF,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG;QAEpD,IAAI,CAAC,aAAa,CAAC,gBAAgB;YACjC,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,IAAI;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,iBAAiB,MAAM,wBAAwB,UAAU,gBAAgB,cAAc;YACvF,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,QAAQ,KAAK,CAAC,SAAS,MAAM,KAAK;YAClC,MAAM;QACR;QAEA,eAAe;QACf,IAAI;YACF,MAAM,qBAAqB,WAAW;YACtC,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QACzB,eAAe;QACjB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,iBAAiB;QACjB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;YAAc,SAAS;QAAM,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,0BAA0B;AAC1B,eAAe,wBAAwB,QAAa,EAAE,cAAsB,EAAE,YAAqB,EAAE,SAAkB;IACrH,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,SAAS;YAAE,UAAU,SAAS,QAAQ;YAAE,OAAO,SAAS,KAAK;YAAE,WAAW,CAAC,CAAC,SAAS,MAAM;QAAC;QAExG,MAAM,WAAW,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE;QAEhC,sBAAsB;QACtB,IAAI,oBAA2B,EAAE;QACjC,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC9C,OAAO;wBAAE,IAAI;oBAAU;oBACvB,SAAS;wBACP,SAAS;4BACP,SAAS;gCACP,YAAY;4BACd;wBACF;oBACF;gBACF;gBACA,oBAAoB,SAAS,SAAS,cAAc,EAAE;gBACtD,QAAQ,GAAG,CAAC,iBAAiB,kBAAkB,MAAM;YACvD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,cAAc;YAC9B;QACF;QAEA,wBAAwB;QACxB,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,mBAAmB,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,IAC7C,iCAAiC,UAAU,gBAAgB;QAE7D,QAAQ,GAAG,CAAC,gBAAgB,iBAAiB,MAAM;QAEnD,YAAY;QACZ,QAAQ,GAAG,CAAC;QACZ,MAAM,eAAe,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,IACzC,cAAc,UAAU;QAE1B,QAAQ,GAAG,CAAC,gBAAgB,aAAa,MAAM;QAE/C,YAAY;QACZ,QAAQ,GAAG,CAAC;QACZ,MAAM,sBAAsB,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,IAChD,qBAAqB,UAAU;QAEjC,QAAQ,GAAG,CAAC,kBAAkB,oBAAoB,MAAM;QAExD,YAAY;QACZ,QAAQ,GAAG,CAAC;QACZ,MAAM,qBAAqB,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,IAC/C,oBAAoB,UAAU;QAEhC,QAAQ,GAAG,CAAC,gBAAgB,mBAAmB,MAAM;QAErD,4BAA4B;QAC5B,MAAM,gBAAgB,iBAAiB,GAAG,CAAC,CAAC;YAC1C,MAAM,MAAM,CAAA,GAAA,2IAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC7B,IAAI,KAAK,IAAI,EAAE,cAAc,QAAQ,QAAQ,QAAQ;gBACrD,MAAM,KAAK,IAAI,IAAI;gBACnB,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,UAAU;gBACvB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;YACzB;YAEA,UAAU;YACV,IAAI,KAAK,eAAe,EAAE;gBACxB,IAAI,eAAe,GAAG,KAAK,eAAe;YAC5C;YAEA,OAAO;QACT;QAEA,eAAe;QACf,MAAM,YAAY,aAAa,GAAG,CAAC,CAAC,QAClC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;gBACb,IAAI,MAAM,QAAQ,EAAE,cAAc,QAAQ,QAAQ,QAAQ;gBAC1D,MAAM,MAAM,QAAQ,IAAI;gBACxB,UAAU,MAAM,QAAQ;gBACxB,aAAa,MAAM,WAAW;gBAC9B,YAAY,MAAM,UAAU;YAC9B;QAGF,eAAe;QACf,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,2BAAwB,AAAD,EAAE;YAC/C,YAAY;YACZ,OAAO,SAAS,CAAC,EAAE,IAAI,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,IAAI;gBAAW,MAAM;YAAO;YACpE,WAAW;YACX,eAAe;YACf,cAAc;YACd,mBAAmB;YACnB,OAAO;YACP,SAAS;QACX;QAEA,OAAO;YACL,YAAY;YACZ,QAAQ;YACR,eAAe;YACf,cAAc;YACd;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAEA,SAAS;AACT,eAAe,kBAAkB,QAAa,EAAE,cAAsB;IACpE,MAAM,SAAS,CAAC;;;AAGlB,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;aAqBJ,CAAC;IAEZ,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,QAAQ;IAEhD,IAAI;QACF,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,OAAO,OAAO,UAAU,IAAI,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,EAAE;IACX;AACF;AAEA,SAAS;AACT,eAAe,cAAc,QAAa,EAAE,cAAsB;IAChE,MAAM,SAAS,CAAC;;;AAGlB,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;aAqBJ,CAAC;IAEZ,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,QAAQ;IAEhD,IAAI;QACF,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,OAAO,OAAO,MAAM,IAAI,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,EAAE;IACX;AACF;AAEA,SAAS;AACT,eAAe,qBAAqB,QAAa,EAAE,cAAsB;IACvE,MAAM,SAAS,CAAC;;;AAGlB,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;aAqBJ,CAAC;IAEZ,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,QAAQ;IAEhD,IAAI;QACF,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,OAAO,OAAO,aAAa,IAAI,EAAE;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,EAAE;IACX;AACF;AAEA,SAAS;AACT,eAAe,oBAAoB,QAAa,EAAE,cAAsB;IACtE,MAAM,SAAS,CAAC;;;AAGlB,EAAE,eAAe;;;;;;;;;;;;mCAYkB,CAAC;IAElC,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,QAAQ;IAEhD,mBAAmB;IACnB,OAAO,SAAS,IAAI;AACtB;AAEA,sBAAsB;AACtB,eAAe,iCAAiC,QAAa,EAAE,cAAsB,EAAE,iBAAwB;IAC7G,kBAAkB;IAClB,IAAI,uBAAuB;IAC3B,IAAI,kBAAkB,MAAM,GAAG,GAAG;QAChC,uBAAuB,CAAC,cAAc,EAAE,kBAAkB,GAAG,CAAC,CAAA;YAC5D,MAAM,UAAU,EAAE;YAClB,IAAI,KAAK,QAAQ,EAAE,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,QAAQ,EAAE;YACtD,IAAI,KAAK,MAAM,EAAE,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;YAClD,IAAI,KAAK,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,WAAW,EAAE;YAC5D,IAAI,KAAK,gBAAgB,EAAE,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,gBAAgB,EAAE;YAEzE,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,OAAO;QAChD,GAAG,IAAI,CAAC,MAAM;;;;iBAID,CAAC;IAChB;IAEA,MAAM,SAAS,CAAC;;;AAGlB,EAAE,iBAAiB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA6B3B,CAAC;IAEZ,MAAM,WAAW,MAAM,SAAS,OAAO,CAAC,QAAQ;IAEhD,IAAI;QACF,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QACtC,OAAO,OAAO,UAAU,IAAI,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,YAAY;QACZ,OAAO,MAAM,kBAAkB,UAAU;IAC3C;AACF;AAEA,kCAAkC;AAClC,eAAe,qBAAqB,SAAiB,EAAE,cAAmB;IACxE,IAAI;QACF,yBAAyB;QACzB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE;YAAU;YACnB,QAAQ;gBACN,WAAW;gBACX,oBAAoB,KAAK,SAAS,CAAC,eAAe,UAAU,IAAI,EAAE;gBAClE,gBAAgB,KAAK,SAAS,CAAC,eAAe,MAAM,IAAI,EAAE;gBAC1D,eAAe,KAAK,SAAS,CAAC,eAAe,aAAa,IAAI,EAAE;gBAChE,cAAc,eAAe,YAAY,IAAI;gBAC7C,iBAAiB,eAAe,eAAe,IAAI;gBACnD,WAAW,IAAI;YACjB;YACA,QAAQ;gBACN;gBACA,qBAAqB;gBACrB,YAAY;gBACZ,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,oBAAoB,KAAK,SAAS,CAAC,eAAe,UAAU,IAAI,EAAE;gBAClE,gBAAgB,KAAK,SAAS,CAAC,eAAe,MAAM,IAAI,EAAE;gBAC1D,eAAe,KAAK,SAAS,CAAC,eAAe,aAAa,IAAI,EAAE;gBAChE,cAAc,eAAe,YAAY,IAAI;gBAC7C,iBAAiB,eAAe,eAAe,IAAI;YACrD;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC7B,gBAAgB;IAClB;AACF", "debugId": null}}]}