/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-appearance/route";
exports.ids = ["app/api/ai/generate-appearance/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-appearance%2Froute&page=%2Fapi%2Fai%2Fgenerate-appearance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-appearance%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-appearance%2Froute&page=%2Fapi%2Fai%2Fgenerate-appearance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-appearance%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_appearance_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-appearance/route.ts */ \"(rsc)/./src/app/api/ai/generate-appearance/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-appearance/route\",\n        pathname: \"/api/ai/generate-appearance\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-appearance/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-appearance\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_appearance_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhaSUyRmdlbmVyYXRlLWFwcGVhcmFuY2UlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFpJTJGZ2VuZXJhdGUtYXBwZWFyYW5jZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFpJTJGZ2VuZXJhdGUtYXBwZWFyYW5jZSUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDJUU5JUExJUI5JUU3JTlCJUFFJTVDVmlkZW9SdW4tMTIlNUNWaWRlb1J1bi0xMiU1Q25vdmVsLXRvLXZpZGVvLWFpJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDJUU5JUExJUI5JUU3JTlCJUFFJTVDVmlkZW9SdW4tMTIlNUNWaWRlb1J1bi0xMiU1Q25vdmVsLXRvLXZpZGVvLWFpJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNxRDtBQUNsSTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxc6aG555uuXFxcXFZpZGVvUnVuLTEyXFxcXFZpZGVvUnVuLTEyXFxcXG5vdmVsLXRvLXZpZGVvLWFpXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFpXFxcXGdlbmVyYXRlLWFwcGVhcmFuY2VcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FpL2dlbmVyYXRlLWFwcGVhcmFuY2Uvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9haS9nZW5lcmF0ZS1hcHBlYXJhbmNlXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9haS9nZW5lcmF0ZS1hcHBlYXJhbmNlL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxc6aG555uuXFxcXFZpZGVvUnVuLTEyXFxcXFZpZGVvUnVuLTEyXFxcXG5vdmVsLXRvLXZpZGVvLWFpXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFpXFxcXGdlbmVyYXRlLWFwcGVhcmFuY2VcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-appearance%2Froute&page=%2Fapi%2Fai%2Fgenerate-appearance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-appearance%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-appearance/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/ai/generate-appearance/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/promptGenerator */ \"(rsc)/./src/utils/promptGenerator.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { modelId, character, customPrompt, // 新增：一致性相关参数\n        generateDetailedDNA = false, useConsistencyConstraints = false, consistencyMode = 'hybrid' } = await request.json();\n        if (!modelId || !character) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少必要参数'\n            }, {\n                status: 400\n            });\n        }\n        // 获取AI配置（使用指定的模型ID或第一个启用的模型）\n        let aiConfig;\n        if (modelId) {\n            // 如果指定了模型ID，使用该模型\n            aiConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.findUnique({\n                where: {\n                    id: modelId\n                }\n            });\n        } else {\n            // 否则使用第一个启用的模型\n            aiConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.findFirst({\n                where: {\n                    enabled: true\n                }\n            });\n        }\n        if (!aiConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到AI配置，请先配置AI模型'\n            }, {\n                status: 404\n            });\n        }\n        // 使用找到的AI配置，不需要覆盖模型名称\n        const configWithSelectedModel = aiConfig;\n        // 暂时移除状态检查，因为测试API不会更新状态\n        // if (aiConfig.status !== 'connected') {\n        //   return NextResponse.json(\n        //     { success: false, error: 'AI模型未连接，请检查配置' },\n        //     { status: 400 }\n        //   )\n        // }\n        // 1. 如果需要，先生成详细DNA\n        let detailedDNA = character.detailedDNA;\n        if (generateDetailedDNA || !detailedDNA) {\n            detailedDNA = await generateDetailedCharacterDNA(character);\n        }\n        // 2. 基于详细DNA和一致性约束生成图像\n        const generatedImages = await generateConsistentCharacterImages(configWithSelectedModel, character, detailedDNA, customPrompt, useConsistencyConstraints, consistencyMode);\n        // 3. 验证一致性（如果启用了一致性约束）\n        let consistencyScore = 0;\n        if (useConsistencyConstraints && generatedImages.front) {\n            consistencyScore = await validateImageConsistency(generatedImages, detailedDNA);\n        }\n        // 4. 更新角色数据库记录\n        if (character.id) {\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.character.update({\n                where: {\n                    id: character.id\n                },\n                data: {\n                    detailedDNA: JSON.stringify(detailedDNA),\n                    consistencyScore: consistencyScore,\n                    referenceImages: JSON.stringify({\n                        front: generatedImages.front,\n                        side: generatedImages.side,\n                        back: generatedImages.back,\n                        consistencyScore: consistencyScore\n                    })\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...generatedImages,\n                detailedDNA,\n                consistencyScore,\n                consistencyMode\n            }\n        });\n    } catch (error) {\n        console.error('AI形象生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'AI形象生成失败，请重试'\n        }, {\n            status: 500\n        });\n    }\n}\n// 生成详细角色DNA\nasync function generateDetailedCharacterDNA(character) {\n    const prompt = `\n请基于以下角色信息，生成详细的外貌特征描述：\n\n角色名称：${character.name}\n基础描述：${character.facial || ''}\n身份：${character.identity || ''}\n性格：${character.personality || ''}\n身材：${character.physique || ''}\n发型：${character.hairstyle || ''}\n服装：${character.clothing || ''}\n\n请按照以下JSON格式返回详细信息：\n{\n  \"facial\": {\n    \"faceShape\": \"具体脸型（如：瓜子脸、圆脸、方脸）\",\n    \"eyeShape\": \"具体眼型（如：丹凤眼、杏眼、桃花眼）\",\n    \"eyeColor\": \"具体眼色（如：深邃的黑色、明亮的棕色）\",\n    \"noseShape\": \"具体鼻型（如：高挺的鼻梁、小巧的鼻子）\",\n    \"mouthShape\": \"具体嘴型（如：樱桃小嘴、薄唇）\",\n    \"skinTone\": \"具体肤色（如：白皙透亮、健康的小麦色）\",\n    \"facialFeatures\": \"独特面部特征（如：左脸颊有一颗小痣、眉间有疤痕）\"\n  },\n  \"physique\": {\n    \"height\": \"身高描述\",\n    \"build\": \"体型描述\",\n    \"posture\": \"体态特征\"\n  },\n  \"clothing\": {\n    \"style\": \"服装风格\",\n    \"colors\": [\"常用颜色1\", \"常用颜色2\"],\n    \"accessories\": [\"配饰1\", \"配饰2\"]\n  },\n  \"uniqueIdentifiers\": [\"独特标识1\", \"独特标识2\"],\n  \"standardPrompt\": \"标准化的AI生成提示词\"\n}\n\n要求：\n1. 描述要具体、明确，避免模糊词汇\n2. 独特标识要能够作为强约束条件\n3. 标准化提示词要适合AI图像生成模型使用\n`;\n    try {\n        // 这里应该调用AI模型生成详细DNA，暂时返回基础结构\n        const detailedDNA = {\n            facial: {\n                faceShape: character.facial?.includes('圆') ? '圆脸' : character.facial?.includes('瓜子') ? '瓜子脸' : '椭圆脸',\n                eyeShape: character.facial?.includes('大眼') ? '杏眼' : '丹凤眼',\n                eyeColor: '深邃的黑色',\n                noseShape: '高挺的鼻梁',\n                mouthShape: '樱桃小嘴',\n                skinTone: '白皙透亮',\n                facialFeatures: character.facial || '清秀的五官'\n            },\n            physique: {\n                height: character.physique?.includes('高') ? '身材高挑' : '中等身材',\n                build: character.physique || '匀称的身材',\n                posture: '优雅的体态'\n            },\n            clothing: {\n                style: character.clothing || '现代休闲风格',\n                colors: [\n                    '白色',\n                    '蓝色'\n                ],\n                accessories: [\n                    '简约耳环'\n                ]\n            },\n            uniqueIdentifiers: [\n                character.facial || '清秀面容',\n                character.hairstyle || '自然发型'\n            ],\n            standardPrompt: `${character.name}, ${character.facial || ''}, ${character.identity || ''}, 高质量角色设计图`\n        };\n        return detailedDNA;\n    } catch (error) {\n        console.error('生成详细DNA失败:', error);\n        throw new Error('详细DNA生成失败');\n    }\n}\n// 生成一致性约束的角色图像\nasync function generateConsistentCharacterImages(aiConfig, character, detailedDNA, customPrompt, useConsistencyConstraints = false, consistencyMode = 'hybrid') {\n    try {\n        let basePrompt = '';\n        if (useConsistencyConstraints && detailedDNA) {\n            // 使用详细DNA构建一致性约束提示词\n            basePrompt = buildConsistencyPrompt(detailedDNA, customPrompt);\n        } else {\n            // 使用原有的提示词生成器\n            basePrompt = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCharacterPrompt)(character, {\n                artStyle: 'anime',\n                quality: 'masterpiece',\n                background: 'white',\n                customEnhancement: customPrompt\n            });\n        }\n        // 根据不同的AI提供商选择不同的生成方式\n        switch(aiConfig.provider){\n            case 'zhipu':\n                return await generateWithZhipu(aiConfig, basePrompt);\n            case 'tongyi':\n                const tongyiPrompts = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__.generateConsistencyPrompt)(character, basePrompt);\n                return await generateWithTongyi(aiConfig, tongyiPrompts);\n            case 'doubao':\n                const doubaoPrompts = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__.generateConsistencyPrompt)(character, basePrompt);\n                return await generateWithDoubao(aiConfig, doubaoPrompts);\n            default:\n                throw new Error(`不支持的AI提供商: ${aiConfig.provider}`);\n        }\n    } catch (error) {\n        console.error('生成一致性角色图像失败:', error);\n        throw new Error('一致性角色图像生成失败');\n    }\n}\n// 构建一致性约束提示词\nfunction buildConsistencyPrompt(detailedDNA, customPrompt) {\n    const dna = detailedDNA;\n    const prompt = `\n高质量角色设计图，专业角色设计：\n\n【面部特征】\n- 脸型：${dna.facial.faceShape}\n- 眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}\n- 鼻型：${dna.facial.noseShape}\n- 嘴型：${dna.facial.mouthShape}\n- 肤色：${dna.facial.skinTone}\n- 特殊标识：${dna.facial.facialFeatures}\n\n【体型特征】\n- 身高：${dna.physique.height}\n- 体型：${dna.physique.build}\n- 体态：${dna.physique.posture}\n\n【服装风格】\n- 风格：${dna.clothing.style}\n- 主色调：${dna.clothing.colors.join('、')}\n- 配饰：${dna.clothing.accessories.join('、')}\n\n【独特标识】\n${dna.uniqueIdentifiers.join('，')}\n\n【技术要求】\n- 高清晰度，4K质量\n- 专业角色设计风格\n- 干净的白色背景\n- 确保独特标识清晰可见\n- 严格按照上述特征生成\n\n${customPrompt ? `【自定义要求】\\n${customPrompt}` : ''}\n`;\n    return prompt;\n}\n// 验证图像一致性\nasync function validateImageConsistency(generatedImages, detailedDNA) {\n    try {\n        // 这里应该实现真正的图像一致性验证算法\n        // 暂时返回一个基于DNA完整性的评分\n        let score = 0.8 // 基础分\n        ;\n        // 检查是否有详细DNA\n        if (detailedDNA && detailedDNA.facial && detailedDNA.uniqueIdentifiers) {\n            score += 0.1;\n        }\n        // 检查是否生成了多个视图\n        if (generatedImages.front && generatedImages.side && generatedImages.back) {\n            score += 0.1;\n        }\n        return Math.min(score, 1.0);\n    } catch (error) {\n        console.error('一致性验证失败:', error);\n        return 0.6 // 默认评分\n        ;\n    }\n}\n// 使用智谱AI生成角色形象\nasync function generateWithZhipu(aiConfig, basePrompt) {\n    try {\n        // 生成三个视图的提示词\n        const prompts = {\n            front: `${basePrompt}, front view, facing camera, character design sheet`,\n            side: `${basePrompt}, side view, 90-degree profile, character design sheet`,\n            back: `${basePrompt}, back view, rear angle, character design sheet`\n        };\n        // 调用智谱AI API生成三个视图\n        const [frontImage, sideImage, backImage] = await Promise.all([\n            callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.front),\n            callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.side),\n            callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.back)\n        ]);\n        return {\n            front: frontImage,\n            side: sideImage,\n            back: backImage,\n            character: {\n                name: '角色名称',\n                description: basePrompt\n            },\n            prompts: prompts\n        };\n    } catch (error) {\n        console.error('智谱AI生成失败:', error);\n        throw error;\n    }\n}\n// 使用通义万相生成角色形象\nasync function generateWithTongyi(aiConfig, prompts) {\n    try {\n        // 生成三个视图\n        const [frontImage, sideImage, backImage] = await Promise.all([\n            callTongyiAPI(aiConfig.apiKey, prompts.front),\n            callTongyiAPI(aiConfig.apiKey, prompts.side),\n            callTongyiAPI(aiConfig.apiKey, prompts.back)\n        ]);\n        return {\n            front: frontImage,\n            side: sideImage,\n            back: backImage,\n            character: {\n                name: '角色名称',\n                description: prompts.front\n            },\n            prompts: prompts\n        };\n    } catch (error) {\n        console.error('通义万相生成失败:', error);\n        throw error;\n    }\n}\n// 使用豆包生成角色形象\nasync function generateWithDoubao(aiConfig, prompts) {\n    try {\n        // 生成三个视图\n        const [frontImage, sideImage, backImage] = await Promise.all([\n            callDoubaoAPI(aiConfig.apiKey, aiConfig.model, prompts.front),\n            callDoubaoAPI(aiConfig.apiKey, aiConfig.model, prompts.side),\n            callDoubaoAPI(aiConfig.apiKey, aiConfig.model, prompts.back)\n        ]);\n        return {\n            front: frontImage,\n            side: sideImage,\n            back: backImage,\n            character: {\n                name: '角色名称',\n                description: prompts.front\n            },\n            prompts: prompts\n        };\n    } catch (error) {\n        console.error('豆包生成失败:', error);\n        throw error;\n    }\n}\n// 调用智谱AI API\nasync function callZhipuAPI(apiKey, model, prompt) {\n    try {\n        const response = await fetch('https://open.bigmodel.cn/api/paas/v4/images/generations', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model: model,\n                prompt: prompt,\n                size: '1024x1024',\n                quality: 'hd',\n                n: 1\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`智谱AI API错误: ${response.status} ${errorText}`);\n        }\n        const result = await response.json();\n        const imageUrl = result.data?.[0]?.url;\n        if (!imageUrl) {\n            throw new Error('未获取到生成的图像');\n        }\n        return {\n            url: imageUrl,\n            format: 'url'\n        };\n    } catch (error) {\n        console.error('智谱AI API调用失败:', error);\n        // 返回占位图\n        const svgPlaceholder = generateCharacterSVG(prompt);\n        return {\n            url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n            format: 'svg',\n            error: `智谱AI图像生成失败: ${error.message}`\n        };\n    }\n}\n// 调用通义万相API\nasync function callTongyiAPI(apiKey, prompt) {\n    // 这里使用现有的通义万相实现\n    return await generateWithTongyiSingle(apiKey, 'wanx2.0-t2i-turbo', prompt);\n}\n// 调用豆包API\nasync function callDoubaoAPI(apiKey, model, prompt) {\n    try {\n        // 检查是否为图像生成模型（endpoint ID或模型名称）\n        if (model.includes('seedream') || model.includes('t2i') || model.startsWith('ep-') && model === 'ep-20250623162000-p9zzw') {\n            // 对于豆包图像生成，如果是特定的图像生成endpoint ID，需要使用实际的模型名称\n            let actualModel = model;\n            if (model === 'ep-20250623162000-p9zzw') {\n                // 使用豆包 Seedream 3.0 T2I 的实际模型名称（官方文档格式）\n                actualModel = 'doubao-seedream-3-0-t2i-250415';\n            }\n            // 豆包图像生成模型\n            const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKey}`\n                },\n                body: JSON.stringify({\n                    model: actualModel,\n                    prompt: prompt,\n                    response_format: 'url',\n                    size: '1024x1024',\n                    guidance_scale: 3,\n                    watermark: true\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`豆包图像生成API错误: ${response.status} ${errorText}`);\n            }\n            const result = await response.json();\n            const imageUrl = result.data?.[0]?.url;\n            if (!imageUrl) {\n                throw new Error('未获取到生成的图像');\n            }\n            return {\n                url: imageUrl,\n                format: 'url'\n            };\n        } else {\n            // 非图像生成模型，返回提示\n            console.log('豆包模型不支持图像生成，返回占位图');\n            const svgPlaceholder = generateCharacterSVG(prompt);\n            return {\n                url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n                format: 'svg',\n                error: `豆包模型 ${model} 主要用于视频生成，不支持图像生成。请选择豆包 Seedream 3.0 T2I 模型进行图像生成。`\n            };\n        }\n    } catch (error) {\n        console.error('豆包API调用失败:', error);\n        // 返回占位图\n        const svgPlaceholder = generateCharacterSVG(prompt);\n        return {\n            url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n            format: 'svg',\n            error: `豆包API调用失败: ${error.message}`\n        };\n    }\n}\n// 构建角色描述\nfunction buildCharacterDescription(character) {\n    const parts = [];\n    if (character.identity) parts.push(`身份：${character.identity}`);\n    if (character.personality) parts.push(`性格：${character.personality}`);\n    if (character.physique) parts.push(`身材特征：${character.physique}`);\n    if (character.facial) parts.push(`五官特征：${character.facial}`);\n    if (character.hairstyle) parts.push(`发型样式：${character.hairstyle}`);\n    if (character.clothing) parts.push(`服饰风格：${character.clothing}`);\n    return parts.join('；');\n}\n// 构建图像生成提示词\nfunction buildImagePrompt(characterDescription, viewType, customPrompt) {\n    const viewDescriptions = {\n        front: '正面视图，面向镜头',\n        side: '侧面视图，90度侧身',\n        back: '背面视图，背对镜头'\n    };\n    // 如果有自定义提示词，将其加入到基础提示词中\n    const basePrompt = `高质量角色设计图，${viewDescriptions[viewType]}，${characterDescription}，\n专业角色设计，干净的白色背景，全身图，高清晰度，\n动漫风格，角色设计参考图，三视图设计，\nlighting: soft studio lighting,\nstyle: professional character design, clean lines, detailed features,\nquality: high resolution, 4K, masterpiece`;\n    if (customPrompt && customPrompt.trim()) {\n        return `${basePrompt}，${customPrompt}`;\n    }\n    return basePrompt;\n}\n// 生成图像\nasync function generateImage(aiConfig, prompt) {\n    const { provider, apiKey, model } = aiConfig;\n    switch(provider){\n        case 'zhipu':\n            // 智谱AI CogView图像生成\n            return await callZhipuAPI(apiKey, model, prompt);\n        case 'tongyi':\n            // 通义万相文生图\n            return await generateWithTongyiSingle(apiKey, model, prompt);\n        case 'doubao':\n            // 豆包图像生成\n            return await callDoubaoAPI(apiKey, model, prompt);\n        case 'deepseek':\n            // DeepSeek不支持图像生成，使用Stable Diffusion API\n            return await generateWithStableDiffusion(prompt);\n        case 'openai':\n            return await generateWithDALLE(apiKey, prompt);\n        default:\n            // 默认使用免费的图像生成服务\n            return await generateWithStableDiffusion(prompt);\n    }\n}\n// 使用Stable Diffusion生成图像（免费服务）\nasync function generateWithStableDiffusion(prompt) {\n    try {\n        // 设置较短的超时时间\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000) // 10秒超时\n        ;\n        // 使用Hugging Face的免费Stable Diffusion API\n        const response = await fetch('https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                inputs: prompt,\n                parameters: {\n                    num_inference_steps: 15,\n                    guidance_scale: 7.5,\n                    width: 512,\n                    height: 768\n                }\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            throw new Error(`图像生成失败: ${response.statusText}`);\n        }\n        const imageBlob = await response.blob();\n        const imageBuffer = await imageBlob.arrayBuffer();\n        const base64Image = Buffer.from(imageBuffer).toString('base64');\n        return {\n            url: `data:image/png;base64,${base64Image}`,\n            format: 'base64'\n        };\n    } catch (error) {\n        console.error('Stable Diffusion生成失败:', error);\n        // 生成一个简单的SVG占位图，包含角色描述\n        const svgPlaceholder = generateCharacterSVG(prompt);\n        return {\n            url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n            format: 'svg',\n            error: '图像生成服务暂时不可用，显示描述图'\n        };\n    }\n}\n// 生成包含角色描述的SVG占位图\nfunction generateCharacterSVG(prompt) {\n    // 提取角色特征关键词\n    const features = prompt.match(/[\\u4e00-\\u9fa5]+/g)?.slice(0, 6) || [\n        '角色',\n        '设计',\n        '图'\n    ];\n    return `\n    <svg width=\"512\" height=\"768\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:#f3f4f6;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:#e5e7eb;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"512\" height=\"768\" fill=\"url(#bg)\"/>\n\n      <!-- 人物轮廓 -->\n      <ellipse cx=\"256\" cy=\"200\" rx=\"80\" ry=\"100\" fill=\"#d1d5db\" opacity=\"0.5\"/>\n      <rect x=\"176\" y=\"280\" width=\"160\" height=\"300\" rx=\"20\" fill=\"#d1d5db\" opacity=\"0.5\"/>\n      <ellipse cx=\"256\" cy=\"650\" rx=\"100\" ry=\"60\" fill=\"#d1d5db\" opacity=\"0.3\"/>\n\n      <!-- 特征文字 -->\n      <text x=\"256\" y=\"100\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"24\" fill=\"#374151\">角色设计图</text>\n\n      ${features.map((feature, index)=>`\n        <text x=\"256\" y=\"${140 + index * 30}\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"16\" fill=\"#6b7280\">${feature}</text>\n      `).join('')}\n\n      <text x=\"256\" y=\"720\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"#9ca3af\">图像生成服务暂时不可用</text>\n    </svg>\n  `;\n}\n// 使用通义万相生成图像（单张）\nasync function generateWithTongyiSingle(apiKey, model, prompt) {\n    // 添加重试机制\n    const maxRetries = 3;\n    let lastError = null;\n    for(let retry = 0; retry < maxRetries; retry++){\n        try {\n            // 如果是重试，等待一段时间避免频率限制\n            if (retry > 0) {\n                const waitTime = Math.pow(2, retry) * 1000 // 指数退避：2s, 4s, 8s\n                ;\n                console.log(`通义万相重试 ${retry}/${maxRetries}，等待 ${waitTime}ms`);\n                await new Promise((resolve)=>setTimeout(resolve, waitTime));\n            }\n            // 第一步：创建任务\n            const createTaskResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKey}`,\n                    'X-DashScope-Async': 'enable'\n                },\n                body: JSON.stringify({\n                    model: model,\n                    input: {\n                        prompt: prompt\n                    },\n                    parameters: {\n                        size: '1024*1024',\n                        n: 1,\n                        seed: Math.floor(Math.random() * 1000000),\n                        style: '<auto>',\n                        ref_mode: 'repaint'\n                    }\n                })\n            });\n            if (!createTaskResponse.ok) {\n                const errorText = await createTaskResponse.text();\n                console.error(`通义万相API错误 (${createTaskResponse.status}):`, errorText);\n                // 如果是频率限制，继续重试\n                if (createTaskResponse.status === 429) {\n                    lastError = new Error(`API调用频率限制，重试中...`);\n                    continue;\n                }\n                throw new Error(`通义万相任务创建失败: ${createTaskResponse.statusText}`);\n            }\n            const taskData = await createTaskResponse.json();\n            const taskId = taskData.output?.task_id;\n            if (!taskId) {\n                throw new Error('未获取到任务ID');\n            }\n            console.log(`通义万相任务创建成功，任务ID: ${taskId}`);\n            // 第二步：轮询任务状态直到完成\n            let attempts = 0;\n            const maxAttempts = 60 // 最多等待5分钟\n            ;\n            while(attempts < maxAttempts){\n                await new Promise((resolve)=>setTimeout(resolve, 5000)) // 等待5秒\n                ;\n                const statusResponse = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`\n                    }\n                });\n                if (!statusResponse.ok) {\n                    throw new Error(`查询任务状态失败: ${statusResponse.statusText}`);\n                }\n                const statusData = await statusResponse.json();\n                const taskStatus = statusData.output?.task_status;\n                console.log(`通义万相任务状态: ${taskStatus} (${attempts + 1}/${maxAttempts})`);\n                if (taskStatus === 'SUCCEEDED') {\n                    const imageUrl = statusData.output?.results?.[0]?.url;\n                    if (imageUrl) {\n                        console.log(`通义万相图像生成成功: ${imageUrl}`);\n                        return {\n                            url: imageUrl,\n                            format: 'url'\n                        };\n                    } else {\n                        throw new Error('任务成功但未获取到图像URL');\n                    }\n                } else if (taskStatus === 'FAILED') {\n                    throw new Error(`任务执行失败: ${statusData.output?.message || '未知错误'}`);\n                }\n                attempts++;\n            }\n            throw new Error('任务执行超时');\n        } catch (error) {\n            console.error(`通义万相生成失败 (尝试 ${retry + 1}/${maxRetries}):`, error);\n            lastError = error;\n            // 如果不是最后一次重试，继续\n            if (retry < maxRetries - 1) {\n                continue;\n            }\n        }\n    }\n    // 所有重试都失败了，生成SVG占位图\n    console.log('通义万相所有重试都失败，生成SVG占位图');\n    const svgPlaceholder = generateCharacterSVG(prompt);\n    return {\n        url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n        format: 'svg',\n        error: `通义万相图像生成服务暂时不可用: ${lastError?.message || '未知错误'}`\n    };\n}\n// 使用DALL-E生成图像\nasync function generateWithDALLE(apiKey, prompt) {\n    try {\n        const response = await fetch('https://api.openai.com/v1/images/generations', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                prompt: prompt,\n                n: 1,\n                size: '512x768',\n                quality: 'standard'\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`DALL-E生成失败: ${response.statusText}`);\n        }\n        const data = await response.json();\n        const imageUrl = data.data[0]?.url;\n        if (!imageUrl) {\n            throw new Error('未获取到生成的图像');\n        }\n        return {\n            url: imageUrl,\n            format: 'url'\n        };\n    } catch (error) {\n        console.error('DALL-E生成失败:', error);\n        return {\n            url: '/placeholder-character.svg',\n            format: 'placeholder',\n            error: '图像生成服务暂时不可用'\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-appearance/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPVxuICBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/promptGenerator.ts":
/*!**************************************!*\
  !*** ./src/utils/promptGenerator.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCharacterPrompt: () => (/* binding */ generateCharacterPrompt),\n/* harmony export */   generateConsistencyPrompt: () => (/* binding */ generateConsistencyPrompt),\n/* harmony export */   generateNegativePrompt: () => (/* binding */ generateNegativePrompt)\n/* harmony export */ });\n// 角色形象提示词生成器\n// 专门用于从小说角色信息生成高质量的图像生成提示词\n// 中文特征词汇到英文的映射\nconst FEATURE_MAPPING = {\n    // 年龄相关\n    '少女': 'young girl',\n    '青年女性': 'young woman',\n    '中年女性': 'middle-aged woman',\n    '少年': 'young boy',\n    '青年男性': 'young man',\n    '中年男性': 'middle-aged man',\n    // 脸型\n    '瓜子脸': 'oval face',\n    '圆脸': 'round face',\n    '方脸': 'square face',\n    '长脸': 'long face',\n    '心形脸': 'heart-shaped face',\n    // 眼睛\n    '大眼睛': 'large eyes',\n    '小眼睛': 'small eyes',\n    '丹凤眼': 'phoenix eyes',\n    '杏眼': 'almond eyes',\n    '圆眼': 'round eyes',\n    '深邃': 'deep-set eyes',\n    // 发型\n    '长发': 'long hair',\n    '短发': 'short hair',\n    '中长发': 'medium length hair',\n    '卷发': 'curly hair',\n    '直发': 'straight hair',\n    '波浪发': 'wavy hair',\n    '马尾': 'ponytail',\n    '双马尾': 'twin tails',\n    '刘海': 'bangs',\n    // 身材\n    '高挑': 'tall and slender',\n    '娇小': 'petite',\n    '匀称': 'well-proportioned',\n    '丰满': 'full-figured',\n    '苗条': 'slim',\n    '健美': 'athletic build',\n    // 气质\n    '温柔': 'gentle',\n    '冷酷': 'cold',\n    '活泼': 'lively',\n    '优雅': 'elegant',\n    '知性': 'intellectual',\n    '神秘': 'mysterious',\n    '坚强': 'strong-willed',\n    '可爱': 'cute',\n    // 服装\n    '职业装': 'business attire',\n    '休闲装': 'casual wear',\n    '正装': 'formal wear',\n    '校服': 'school uniform',\n    '古装': 'traditional costume',\n    '现代装': 'modern clothing'\n};\n// 艺术风格配置\nconst ART_STYLES = {\n    anime: {\n        keywords: 'anime style, manga style, cel shading, clean lines',\n        description: '日式动漫风格'\n    },\n    realistic: {\n        keywords: 'photorealistic, hyperrealistic, professional photography',\n        description: '写实摄影风格'\n    },\n    'semi-realistic': {\n        keywords: 'semi-realistic, digital art, detailed illustration',\n        description: '半写实插画风格'\n    },\n    'concept-art': {\n        keywords: 'concept art, character design, professional illustration',\n        description: '概念设计风格'\n    }\n};\n// 视角配置\nconst VIEW_TYPES = {\n    front: 'front view, facing camera, looking at viewer',\n    side: 'side view, 90-degree profile, side angle',\n    back: 'back view, rear angle, showing back',\n    'three-quarter': 'three-quarter view, slight angle'\n};\n// 质量等级配置\nconst QUALITY_LEVELS = {\n    standard: 'good quality, detailed',\n    high: 'high quality, high resolution, detailed, sharp focus',\n    masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'\n};\n/**\n * 从角色信息生成图像提示词\n */ function generateCharacterPrompt(character, options = {}) {\n    const { artStyle = 'anime', viewType = 'front', background = 'white', quality = 'high', customEnhancement = '' } = options;\n    // 构建提示词各部分\n    const parts = [];\n    // 1. 质量和风格\n    parts.push(QUALITY_LEVELS[quality]);\n    parts.push(ART_STYLES[artStyle].keywords);\n    // 2. 基础信息提取\n    const basicInfo = extractBasicInfo(character);\n    if (basicInfo) parts.push(basicInfo);\n    // 3. 外貌特征\n    const appearance = extractAppearanceFeatures(character);\n    if (appearance) parts.push(appearance);\n    // 4. 服装风格\n    const clothing = extractClothingStyle(character);\n    if (clothing) parts.push(clothing);\n    // 5. 气质表现\n    const temperament = extractTemperament(character);\n    if (temperament) parts.push(temperament);\n    // 6. 视角和姿态\n    parts.push(VIEW_TYPES[viewType]);\n    parts.push('character design sheet, reference pose');\n    // 7. 背景设置\n    const backgroundDesc = getBackgroundDescription(background);\n    parts.push(backgroundDesc);\n    // 8. 技术参数\n    parts.push('professional character design, clean composition');\n    // 9. 自定义增强\n    if (customEnhancement.trim()) {\n        parts.push(customEnhancement);\n    }\n    return parts.filter(Boolean).join(', ');\n}\n/**\n * 提取基础信息（年龄、性别、身份）\n */ function extractBasicInfo(character) {\n    const info = [];\n    if (character.identity) {\n        // 从身份信息中提取年龄和职业\n        const ageMatch = character.identity.match(/(\\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/);\n        if (ageMatch) {\n            const age = ageMatch[1] || getAgeFromDescription(ageMatch[0]);\n            info.push(`${age} years old`);\n        }\n        // 提取性别（如果身份中包含）\n        if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {\n            info.push('woman');\n        } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {\n            info.push('man');\n        }\n        // 提取职业\n        const profession = extractProfession(character.identity);\n        if (profession) info.push(profession);\n    }\n    return info.join(' ');\n}\n/**\n * 提取外貌特征\n */ function extractAppearanceFeatures(character) {\n    const features = [];\n    // 处理五官特征\n    if (character.facial) {\n        const facialFeatures = translateFeatures(character.facial);\n        features.push(facialFeatures);\n    }\n    // 处理身材特征\n    if (character.physique) {\n        const bodyFeatures = translateFeatures(character.physique);\n        features.push(bodyFeatures);\n    }\n    // 处理发型\n    if (character.hairstyle) {\n        const hairFeatures = translateFeatures(character.hairstyle);\n        features.push(hairFeatures);\n    }\n    return features.filter(Boolean).join(', ');\n}\n/**\n * 提取服装风格\n */ function extractClothingStyle(character) {\n    if (!character.clothing) return '';\n    return translateFeatures(character.clothing);\n}\n/**\n * 提取气质特征\n */ function extractTemperament(character) {\n    if (!character.personality) return '';\n    const temperamentWords = [];\n    // 从性格描述中提取气质关键词\n    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{\n        if (character.personality.includes(chinese)) {\n            temperamentWords.push(english);\n        }\n    });\n    // 添加表情描述\n    if (character.personality.includes('温柔') || character.personality.includes('善良')) {\n        temperamentWords.push('gentle smile', 'warm expression');\n    } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {\n        temperamentWords.push('serious expression', 'cold gaze');\n    } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {\n        temperamentWords.push('cheerful smile', 'bright expression');\n    }\n    return temperamentWords.join(', ');\n}\n/**\n * 翻译特征描述\n */ function translateFeatures(chineseText) {\n    let result = chineseText;\n    // 使用映射表进行翻译\n    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{\n        const regex = new RegExp(chinese, 'g');\n        result = result.replace(regex, english);\n    });\n    // 清理和优化\n    result = result.replace(/[，。；：]/g, ',') // 替换中文标点\n    .replace(/\\s+/g, ' ') // 合并空格\n    .replace(/,+/g, ',') // 合并逗号\n    .trim();\n    return result;\n}\n/**\n * 从描述中提取年龄\n */ function getAgeFromDescription(ageDesc) {\n    const ageMap = {\n        '十几岁': '16',\n        '二十多岁': '25',\n        '三十多岁': '35',\n        '四十多岁': '45'\n    };\n    return ageMap[ageDesc] || '25';\n}\n/**\n * 提取职业信息\n */ function extractProfession(identity) {\n    const professions = {\n        '学生': 'student',\n        '老师': 'teacher',\n        '医生': 'doctor',\n        '护士': 'nurse',\n        '律师': 'lawyer',\n        '工程师': 'engineer',\n        '设计师': 'designer',\n        '程序员': 'programmer',\n        '经理': 'manager',\n        '秘书': 'secretary',\n        '销售': 'salesperson',\n        '警察': 'police officer',\n        '军人': 'soldier',\n        '艺术家': 'artist',\n        '作家': 'writer',\n        '记者': 'journalist'\n    };\n    for (const [chinese, english] of Object.entries(professions)){\n        if (identity.includes(chinese)) {\n            return english;\n        }\n    }\n    return '';\n}\n/**\n * 获取背景描述\n */ function getBackgroundDescription(background) {\n    const backgrounds = {\n        white: 'clean white background',\n        transparent: 'transparent background',\n        simple: 'simple background',\n        detailed: 'detailed background scene'\n    };\n    return backgrounds[background] || 'clean white background';\n}\n/**\n * 生成负面提示词\n */ function generateNegativePrompt() {\n    return [\n        'blurry',\n        'low quality',\n        'pixelated',\n        'deformed',\n        'distorted',\n        'extra limbs',\n        'missing limbs',\n        'extra fingers',\n        'missing fingers',\n        'bad anatomy',\n        'bad proportions',\n        'ugly',\n        'duplicate',\n        'watermark',\n        'signature',\n        'text',\n        'logo',\n        'oversaturated',\n        'undersaturated',\n        'overexposed',\n        'underexposed'\n    ].join(', ');\n}\n/**\n * 生成角色一致性提示词（用于三视图）\n */ function generateConsistencyPrompt(character, basePrompt) {\n    // 提取身材和服饰特征（保持一致）\n    const consistentFeatures = extractConsistentFeatures(character);\n    // 添加强化细节一致性的约束\n    const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories';\n    return {\n        // 正面：完整脸部可见\n        front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n        // 侧面：90度侧脸轮廓\n        side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n        // 背面：看不到脸\n        back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`\n    };\n}\n/**\n * 提取一致性特征（身材、服饰、发型等保持不变的特征）\n */ function extractConsistentFeatures(character) {\n    const features = [];\n    // 身材特征（保持一致）\n    if (character.physique) {\n        features.push(character.physique);\n    }\n    // 发型和发色（保持一致）\n    if (character.hairstyle) {\n        features.push(character.hairstyle);\n    }\n    // 服饰风格（保持一致，强调细节一致性）\n    if (character.clothing) {\n        features.push(character.clothing);\n        // 添加细节一致性约束\n        features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理');\n    }\n    // 身份特征（保持一致）\n    if (character.identity) {\n        features.push(character.identity);\n    }\n    // 添加强化一致性约束\n    features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰');\n    return features.join('，');\n}\nfunction extractFaceShape(facial) {\n    for (const [chinese, english] of Object.entries(FEATURE_MAPPING)){\n        if (facial.includes(chinese) && chinese.includes('脸')) {\n            return english;\n        }\n    }\n    return '';\n}\nfunction extractHairStyle(hairstyle) {\n    const hairFeatures = [];\n    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{\n        if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {\n            hairFeatures.push(english);\n        }\n    });\n    return hairFeatures.join(' ');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvcHJvbXB0R2VuZXJhdG9yLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLGFBQWE7QUFDYiwyQkFBMkI7QUFvQjNCLGVBQWU7QUFDZixNQUFNQSxrQkFBa0I7SUFDdEIsT0FBTztJQUNQLE1BQU07SUFDTixRQUFRO0lBQ1IsUUFBUTtJQUNSLE1BQU07SUFDTixRQUFRO0lBQ1IsUUFBUTtJQUVSLEtBQUs7SUFDTCxPQUFPO0lBQ1AsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sT0FBTztJQUVQLEtBQUs7SUFDTCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFFTixLQUFLO0lBQ0wsTUFBTTtJQUNOLE1BQU07SUFDTixPQUFPO0lBQ1AsTUFBTTtJQUNOLE1BQU07SUFDTixPQUFPO0lBQ1AsTUFBTTtJQUNOLE9BQU87SUFDUCxNQUFNO0lBRU4sS0FBSztJQUNMLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUVOLEtBQUs7SUFDTCxNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUVOLEtBQUs7SUFDTCxPQUFPO0lBQ1AsT0FBTztJQUNQLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE9BQU87QUFDVDtBQUVBLFNBQVM7QUFDVCxNQUFNQyxhQUFhO0lBQ2pCQyxPQUFPO1FBQ0xDLFVBQVU7UUFDVkMsYUFBYTtJQUNmO0lBQ0FDLFdBQVc7UUFDVEYsVUFBVTtRQUNWQyxhQUFhO0lBQ2Y7SUFDQSxrQkFBa0I7UUFDaEJELFVBQVU7UUFDVkMsYUFBYTtJQUNmO0lBQ0EsZUFBZTtRQUNiRCxVQUFVO1FBQ1ZDLGFBQWE7SUFDZjtBQUNGO0FBRUEsT0FBTztBQUNQLE1BQU1FLGFBQWE7SUFDakJDLE9BQU87SUFDUEMsTUFBTTtJQUNOQyxNQUFNO0lBQ04saUJBQWlCO0FBQ25CO0FBRUEsU0FBUztBQUNULE1BQU1DLGlCQUFpQjtJQUNyQkMsVUFBVTtJQUNWQyxNQUFNO0lBQ05DLGFBQWE7QUFDZjtBQUVBOztDQUVDLEdBQ00sU0FBU0Msd0JBQ2RDLFNBQXdCLEVBQ3hCQyxVQUF5QixDQUFDLENBQUM7SUFFM0IsTUFBTSxFQUNKQyxXQUFXLE9BQU8sRUFDbEJDLFdBQVcsT0FBTyxFQUNsQkMsYUFBYSxPQUFPLEVBQ3BCQyxVQUFVLE1BQU0sRUFDaEJDLG9CQUFvQixFQUFFLEVBQ3ZCLEdBQUdMO0lBRUosV0FBVztJQUNYLE1BQU1NLFFBQWtCLEVBQUU7SUFFMUIsV0FBVztJQUNYQSxNQUFNQyxJQUFJLENBQUNiLGNBQWMsQ0FBQ1UsUUFBUTtJQUNsQ0UsTUFBTUMsSUFBSSxDQUFDdEIsVUFBVSxDQUFDZ0IsU0FBUyxDQUFDZCxRQUFRO0lBRXhDLFlBQVk7SUFDWixNQUFNcUIsWUFBWUMsaUJBQWlCVjtJQUNuQyxJQUFJUyxXQUFXRixNQUFNQyxJQUFJLENBQUNDO0lBRTFCLFVBQVU7SUFDVixNQUFNRSxhQUFhQywwQkFBMEJaO0lBQzdDLElBQUlXLFlBQVlKLE1BQU1DLElBQUksQ0FBQ0c7SUFFM0IsVUFBVTtJQUNWLE1BQU1FLFdBQVdDLHFCQUFxQmQ7SUFDdEMsSUFBSWEsVUFBVU4sTUFBTUMsSUFBSSxDQUFDSztJQUV6QixVQUFVO0lBQ1YsTUFBTUUsY0FBY0MsbUJBQW1CaEI7SUFDdkMsSUFBSWUsYUFBYVIsTUFBTUMsSUFBSSxDQUFDTztJQUU1QixXQUFXO0lBQ1hSLE1BQU1DLElBQUksQ0FBQ2pCLFVBQVUsQ0FBQ1ksU0FBUztJQUMvQkksTUFBTUMsSUFBSSxDQUFDO0lBRVgsVUFBVTtJQUNWLE1BQU1TLGlCQUFpQkMseUJBQXlCZDtJQUNoREcsTUFBTUMsSUFBSSxDQUFDUztJQUVYLFVBQVU7SUFDVlYsTUFBTUMsSUFBSSxDQUFDO0lBRVgsV0FBVztJQUNYLElBQUlGLGtCQUFrQmEsSUFBSSxJQUFJO1FBQzVCWixNQUFNQyxJQUFJLENBQUNGO0lBQ2I7SUFFQSxPQUFPQyxNQUFNYSxNQUFNLENBQUNDLFNBQVNDLElBQUksQ0FBQztBQUNwQztBQUVBOztDQUVDLEdBQ0QsU0FBU1osaUJBQWlCVixTQUF3QjtJQUNoRCxNQUFNdUIsT0FBaUIsRUFBRTtJQUV6QixJQUFJdkIsVUFBVXdCLFFBQVEsRUFBRTtRQUN0QixnQkFBZ0I7UUFDaEIsTUFBTUMsV0FBV3pCLFVBQVV3QixRQUFRLENBQUNFLEtBQUssQ0FBQztRQUMxQyxJQUFJRCxVQUFVO1lBQ1osTUFBTUUsTUFBTUYsUUFBUSxDQUFDLEVBQUUsSUFBSUcsc0JBQXNCSCxRQUFRLENBQUMsRUFBRTtZQUM1REYsS0FBS2YsSUFBSSxDQUFDLEdBQUdtQixJQUFJLFVBQVUsQ0FBQztRQUM5QjtRQUVBLGdCQUFnQjtRQUNoQixJQUFJM0IsVUFBVXdCLFFBQVEsQ0FBQ0ssUUFBUSxDQUFDLFFBQVE3QixVQUFVd0IsUUFBUSxDQUFDSyxRQUFRLENBQUMsU0FBUzdCLFVBQVV3QixRQUFRLENBQUNLLFFBQVEsQ0FBQyxPQUFPO1lBQzlHTixLQUFLZixJQUFJLENBQUM7UUFDWixPQUFPLElBQUlSLFVBQVV3QixRQUFRLENBQUNLLFFBQVEsQ0FBQyxRQUFRN0IsVUFBVXdCLFFBQVEsQ0FBQ0ssUUFBUSxDQUFDLFNBQVM3QixVQUFVd0IsUUFBUSxDQUFDSyxRQUFRLENBQUMsT0FBTztZQUNySE4sS0FBS2YsSUFBSSxDQUFDO1FBQ1o7UUFFQSxPQUFPO1FBQ1AsTUFBTXNCLGFBQWFDLGtCQUFrQi9CLFVBQVV3QixRQUFRO1FBQ3ZELElBQUlNLFlBQVlQLEtBQUtmLElBQUksQ0FBQ3NCO0lBQzVCO0lBRUEsT0FBT1AsS0FBS0QsSUFBSSxDQUFDO0FBQ25CO0FBRUE7O0NBRUMsR0FDRCxTQUFTViwwQkFBMEJaLFNBQXdCO0lBQ3pELE1BQU1nQyxXQUFxQixFQUFFO0lBRTdCLFNBQVM7SUFDVCxJQUFJaEMsVUFBVWlDLE1BQU0sRUFBRTtRQUNwQixNQUFNQyxpQkFBaUJDLGtCQUFrQm5DLFVBQVVpQyxNQUFNO1FBQ3pERCxTQUFTeEIsSUFBSSxDQUFDMEI7SUFDaEI7SUFFQSxTQUFTO0lBQ1QsSUFBSWxDLFVBQVVvQyxRQUFRLEVBQUU7UUFDdEIsTUFBTUMsZUFBZUYsa0JBQWtCbkMsVUFBVW9DLFFBQVE7UUFDekRKLFNBQVN4QixJQUFJLENBQUM2QjtJQUNoQjtJQUVBLE9BQU87SUFDUCxJQUFJckMsVUFBVXNDLFNBQVMsRUFBRTtRQUN2QixNQUFNQyxlQUFlSixrQkFBa0JuQyxVQUFVc0MsU0FBUztRQUMxRE4sU0FBU3hCLElBQUksQ0FBQytCO0lBQ2hCO0lBRUEsT0FBT1AsU0FBU1osTUFBTSxDQUFDQyxTQUFTQyxJQUFJLENBQUM7QUFDdkM7QUFFQTs7Q0FFQyxHQUNELFNBQVNSLHFCQUFxQmQsU0FBd0I7SUFDcEQsSUFBSSxDQUFDQSxVQUFVYSxRQUFRLEVBQUUsT0FBTztJQUVoQyxPQUFPc0Isa0JBQWtCbkMsVUFBVWEsUUFBUTtBQUM3QztBQUVBOztDQUVDLEdBQ0QsU0FBU0csbUJBQW1CaEIsU0FBd0I7SUFDbEQsSUFBSSxDQUFDQSxVQUFVd0MsV0FBVyxFQUFFLE9BQU87SUFFbkMsTUFBTUMsbUJBQTZCLEVBQUU7SUFFckMsZ0JBQWdCO0lBQ2hCQyxPQUFPQyxPQUFPLENBQUMxRCxpQkFBaUIyRCxPQUFPLENBQUMsQ0FBQyxDQUFDQyxTQUFTQyxRQUFRO1FBQ3pELElBQUk5QyxVQUFVd0MsV0FBVyxDQUFFWCxRQUFRLENBQUNnQixVQUFVO1lBQzVDSixpQkFBaUJqQyxJQUFJLENBQUNzQztRQUN4QjtJQUNGO0lBRUEsU0FBUztJQUNULElBQUk5QyxVQUFVd0MsV0FBVyxDQUFDWCxRQUFRLENBQUMsU0FBUzdCLFVBQVV3QyxXQUFXLENBQUNYLFFBQVEsQ0FBQyxPQUFPO1FBQ2hGWSxpQkFBaUJqQyxJQUFJLENBQUMsZ0JBQWdCO0lBQ3hDLE9BQU8sSUFBSVIsVUFBVXdDLFdBQVcsQ0FBQ1gsUUFBUSxDQUFDLFNBQVM3QixVQUFVd0MsV0FBVyxDQUFDWCxRQUFRLENBQUMsT0FBTztRQUN2RlksaUJBQWlCakMsSUFBSSxDQUFDLHNCQUFzQjtJQUM5QyxPQUFPLElBQUlSLFVBQVV3QyxXQUFXLENBQUNYLFFBQVEsQ0FBQyxTQUFTN0IsVUFBVXdDLFdBQVcsQ0FBQ1gsUUFBUSxDQUFDLE9BQU87UUFDdkZZLGlCQUFpQmpDLElBQUksQ0FBQyxrQkFBa0I7SUFDMUM7SUFFQSxPQUFPaUMsaUJBQWlCbkIsSUFBSSxDQUFDO0FBQy9CO0FBRUE7O0NBRUMsR0FDRCxTQUFTYSxrQkFBa0JZLFdBQW1CO0lBQzVDLElBQUlDLFNBQVNEO0lBRWIsWUFBWTtJQUNaTCxPQUFPQyxPQUFPLENBQUMxRCxpQkFBaUIyRCxPQUFPLENBQUMsQ0FBQyxDQUFDQyxTQUFTQyxRQUFRO1FBQ3pELE1BQU1HLFFBQVEsSUFBSUMsT0FBT0wsU0FBUztRQUNsQ0csU0FBU0EsT0FBT0csT0FBTyxDQUFDRixPQUFPSDtJQUNqQztJQUVBLFFBQVE7SUFDUkUsU0FBU0EsT0FDTkcsT0FBTyxDQUFDLFdBQVcsS0FBSyxTQUFTO0tBQ2pDQSxPQUFPLENBQUMsUUFBUSxLQUFLLE9BQU87S0FDNUJBLE9BQU8sQ0FBQyxPQUFPLEtBQUssT0FBTztLQUMzQmhDLElBQUk7SUFFUCxPQUFPNkI7QUFDVDtBQUVBOztDQUVDLEdBQ0QsU0FBU3BCLHNCQUFzQndCLE9BQWU7SUFDNUMsTUFBTUMsU0FBaUM7UUFDckMsT0FBTztRQUNQLFFBQVE7UUFDUixRQUFRO1FBQ1IsUUFBUTtJQUNWO0lBQ0EsT0FBT0EsTUFBTSxDQUFDRCxRQUFRLElBQUk7QUFDNUI7QUFFQTs7Q0FFQyxHQUNELFNBQVNyQixrQkFBa0JQLFFBQWdCO0lBQ3pDLE1BQU04QixjQUFzQztRQUMxQyxNQUFNO1FBQ04sTUFBTTtRQUNOLE1BQU07UUFDTixNQUFNO1FBQ04sTUFBTTtRQUNOLE9BQU87UUFDUCxPQUFPO1FBQ1AsT0FBTztRQUNQLE1BQU07UUFDTixNQUFNO1FBQ04sTUFBTTtRQUNOLE1BQU07UUFDTixNQUFNO1FBQ04sT0FBTztRQUNQLE1BQU07UUFDTixNQUFNO0lBQ1I7SUFFQSxLQUFLLE1BQU0sQ0FBQ1QsU0FBU0MsUUFBUSxJQUFJSixPQUFPQyxPQUFPLENBQUNXLGFBQWM7UUFDNUQsSUFBSTlCLFNBQVNLLFFBQVEsQ0FBQ2dCLFVBQVU7WUFDOUIsT0FBT0M7UUFDVDtJQUNGO0lBRUEsT0FBTztBQUNUO0FBRUE7O0NBRUMsR0FDRCxTQUFTNUIseUJBQXlCZCxVQUFrQjtJQUNsRCxNQUFNbUQsY0FBc0M7UUFDMUNDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1JDLFVBQVU7SUFDWjtJQUVBLE9BQU9KLFdBQVcsQ0FBQ25ELFdBQVcsSUFBSTtBQUNwQztBQUVBOztDQUVDLEdBQ00sU0FBU3dEO0lBQ2QsT0FBTztRQUNMO1FBQVU7UUFBZTtRQUFhO1FBQVk7UUFDbEQ7UUFBZTtRQUFpQjtRQUFpQjtRQUNqRDtRQUFlO1FBQW1CO1FBQVE7UUFDMUM7UUFBYTtRQUFhO1FBQVE7UUFDbEM7UUFBaUI7UUFBa0I7UUFBZTtLQUNuRCxDQUFDdEMsSUFBSSxDQUFDO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVN1QywwQkFBMEI3RCxTQUF3QixFQUFFOEQsVUFBa0I7SUFLcEYsa0JBQWtCO0lBQ2xCLE1BQU1DLHFCQUFxQkMsMEJBQTBCaEU7SUFFckQsZUFBZTtJQUNmLE1BQU1pRSxvQkFBb0I7SUFFMUIsT0FBTztRQUNMLFlBQVk7UUFDWnpFLE9BQU8sQ0FBQyw2QkFBNkIsRUFBRXVFLG1CQUFtQixDQUFDLEVBQUVFLGtCQUFrQix3QkFBd0IsQ0FBQztRQUV4RyxhQUFhO1FBQ2J4RSxNQUFNLENBQUMsa0NBQWtDLEVBQUVzRSxtQkFBbUIsQ0FBQyxFQUFFRSxrQkFBa0Isd0JBQXdCLENBQUM7UUFFNUcsVUFBVTtRQUNWdkUsTUFBTSxDQUFDLCtCQUErQixFQUFFcUUsbUJBQW1CLENBQUMsRUFBRUUsa0JBQWtCLHdCQUF3QixDQUFDO0lBQzNHO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELFNBQVNELDBCQUEwQmhFLFNBQXdCO0lBQ3pELE1BQU1nQyxXQUFxQixFQUFFO0lBRTdCLGFBQWE7SUFDYixJQUFJaEMsVUFBVW9DLFFBQVEsRUFBRTtRQUN0QkosU0FBU3hCLElBQUksQ0FBQ1IsVUFBVW9DLFFBQVE7SUFDbEM7SUFFQSxjQUFjO0lBQ2QsSUFBSXBDLFVBQVVzQyxTQUFTLEVBQUU7UUFDdkJOLFNBQVN4QixJQUFJLENBQUNSLFVBQVVzQyxTQUFTO0lBQ25DO0lBRUEscUJBQXFCO0lBQ3JCLElBQUl0QyxVQUFVYSxRQUFRLEVBQUU7UUFDdEJtQixTQUFTeEIsSUFBSSxDQUFDUixVQUFVYSxRQUFRO1FBQ2hDLFlBQVk7UUFDWm1CLFNBQVN4QixJQUFJLENBQUMsV0FBVyxXQUFXO0lBQ3RDO0lBRUEsYUFBYTtJQUNiLElBQUlSLFVBQVV3QixRQUFRLEVBQUU7UUFDdEJRLFNBQVN4QixJQUFJLENBQUNSLFVBQVV3QixRQUFRO0lBQ2xDO0lBRUEsWUFBWTtJQUNaUSxTQUFTeEIsSUFBSSxDQUFDLFFBQVEsV0FBVyxRQUFRLFdBQVc7SUFFcEQsT0FBT3dCLFNBQVNWLElBQUksQ0FBQztBQUN2QjtBQUVBLFNBQVM0QyxpQkFBaUJqQyxNQUFjO0lBQ3RDLEtBQUssTUFBTSxDQUFDWSxTQUFTQyxRQUFRLElBQUlKLE9BQU9DLE9BQU8sQ0FBQzFELGlCQUFrQjtRQUNoRSxJQUFJZ0QsT0FBT0osUUFBUSxDQUFDZ0IsWUFBWUEsUUFBUWhCLFFBQVEsQ0FBQyxNQUFNO1lBQ3JELE9BQU9pQjtRQUNUO0lBQ0Y7SUFDQSxPQUFPO0FBQ1Q7QUFFQSxTQUFTcUIsaUJBQWlCN0IsU0FBaUI7SUFDekMsTUFBTUMsZUFBeUIsRUFBRTtJQUVqQ0csT0FBT0MsT0FBTyxDQUFDMUQsaUJBQWlCMkQsT0FBTyxDQUFDLENBQUMsQ0FBQ0MsU0FBU0MsUUFBUTtRQUN6RCxJQUFJUixVQUFVVCxRQUFRLENBQUNnQixZQUFhQSxDQUFBQSxRQUFRaEIsUUFBUSxDQUFDLFFBQVFnQixRQUFRaEIsUUFBUSxDQUFDLEtBQUksR0FBSTtZQUNwRlUsYUFBYS9CLElBQUksQ0FBQ3NDO1FBQ3BCO0lBQ0Y7SUFFQSxPQUFPUCxhQUFhakIsSUFBSSxDQUFDO0FBQzNCIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXHV0aWxzXFxwcm9tcHRHZW5lcmF0b3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g6KeS6Imy5b2i6LGh5o+Q56S66K+N55Sf5oiQ5ZmoXG4vLyDkuJPpl6jnlKjkuo7ku47lsI/or7Top5LoibLkv6Hmga/nlJ/miJDpq5jotKjph4/nmoTlm77lg4/nlJ/miJDmj5DnpLror41cblxuaW50ZXJmYWNlIENoYXJhY3RlckluZm8ge1xuICBuYW1lOiBzdHJpbmdcbiAgaWRlbnRpdHk/OiBzdHJpbmdcbiAgcGVyc29uYWxpdHk/OiBzdHJpbmdcbiAgcGh5c2lxdWU/OiBzdHJpbmdcbiAgZmFjaWFsPzogc3RyaW5nXG4gIGhhaXJzdHlsZT86IHN0cmluZ1xuICBjbG90aGluZz86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgUHJvbXB0T3B0aW9ucyB7XG4gIGFydFN0eWxlPzogJ2FuaW1lJyB8ICdyZWFsaXN0aWMnIHwgJ3NlbWktcmVhbGlzdGljJyB8ICdjb25jZXB0LWFydCdcbiAgdmlld1R5cGU/OiAnZnJvbnQnIHwgJ3NpZGUnIHwgJ2JhY2snIHwgJ3RocmVlLXF1YXJ0ZXInXG4gIGJhY2tncm91bmQ/OiAnd2hpdGUnIHwgJ3RyYW5zcGFyZW50JyB8ICdzaW1wbGUnIHwgJ2RldGFpbGVkJ1xuICBxdWFsaXR5PzogJ3N0YW5kYXJkJyB8ICdoaWdoJyB8ICdtYXN0ZXJwaWVjZSdcbiAgY3VzdG9tRW5oYW5jZW1lbnQ/OiBzdHJpbmdcbn1cblxuLy8g5Lit5paH54m55b6B6K+N5rGH5Yiw6Iux5paH55qE5pig5bCEXG5jb25zdCBGRUFUVVJFX01BUFBJTkcgPSB7XG4gIC8vIOW5tOm+hOebuOWFs1xuICAn5bCR5aWzJzogJ3lvdW5nIGdpcmwnLFxuICAn6Z2S5bm05aWz5oCnJzogJ3lvdW5nIHdvbWFuJywgXG4gICfkuK3lubTlpbPmgKcnOiAnbWlkZGxlLWFnZWQgd29tYW4nLFxuICAn5bCR5bm0JzogJ3lvdW5nIGJveScsXG4gICfpnZLlubTnlLfmgKcnOiAneW91bmcgbWFuJyxcbiAgJ+S4reW5tOeUt+aApyc6ICdtaWRkbGUtYWdlZCBtYW4nLFxuICBcbiAgLy8g6IS45Z6LXG4gICfnk5zlrZDohLgnOiAnb3ZhbCBmYWNlJyxcbiAgJ+WchuiEuCc6ICdyb3VuZCBmYWNlJyxcbiAgJ+aWueiEuCc6ICdzcXVhcmUgZmFjZScsXG4gICfplb/ohLgnOiAnbG9uZyBmYWNlJyxcbiAgJ+W/g+W9ouiEuCc6ICdoZWFydC1zaGFwZWQgZmFjZScsXG4gIFxuICAvLyDnnLznnZtcbiAgJ+Wkp+ecvOedmyc6ICdsYXJnZSBleWVzJyxcbiAgJ+Wwj+ecvOedmyc6ICdzbWFsbCBleWVzJyxcbiAgJ+S4ueWHpOecvCc6ICdwaG9lbml4IGV5ZXMnLFxuICAn5p2P55y8JzogJ2FsbW9uZCBleWVzJyxcbiAgJ+WchuecvCc6ICdyb3VuZCBleWVzJyxcbiAgJ+a3semCgyc6ICdkZWVwLXNldCBleWVzJyxcbiAgXG4gIC8vIOWPkeWei1xuICAn6ZW/5Y+RJzogJ2xvbmcgaGFpcicsXG4gICfnn63lj5EnOiAnc2hvcnQgaGFpcicsXG4gICfkuK3plb/lj5EnOiAnbWVkaXVtIGxlbmd0aCBoYWlyJyxcbiAgJ+WNt+WPkSc6ICdjdXJseSBoYWlyJyxcbiAgJ+ebtOWPkSc6ICdzdHJhaWdodCBoYWlyJyxcbiAgJ+azoua1quWPkSc6ICd3YXZ5IGhhaXInLFxuICAn6ams5bC+JzogJ3Bvbnl0YWlsJyxcbiAgJ+WPjOmprOWwvic6ICd0d2luIHRhaWxzJyxcbiAgJ+WImOa1tyc6ICdiYW5ncycsXG4gIFxuICAvLyDouqvmnZBcbiAgJ+mrmOaMkSc6ICd0YWxsIGFuZCBzbGVuZGVyJyxcbiAgJ+Woh+Wwjyc6ICdwZXRpdGUnLFxuICAn5YyA56ewJzogJ3dlbGwtcHJvcG9ydGlvbmVkJyxcbiAgJ+S4sOa7oSc6ICdmdWxsLWZpZ3VyZWQnLFxuICAn6IuX5p2hJzogJ3NsaW0nLFxuICAn5YGl576OJzogJ2F0aGxldGljIGJ1aWxkJyxcbiAgXG4gIC8vIOawlOi0qFxuICAn5rip5p+UJzogJ2dlbnRsZScsXG4gICflhrfphbcnOiAnY29sZCcsXG4gICfmtLvms7wnOiAnbGl2ZWx5JyxcbiAgJ+S8mOmbhSc6ICdlbGVnYW50JyxcbiAgJ+efpeaApyc6ICdpbnRlbGxlY3R1YWwnLFxuICAn56We56eYJzogJ215c3RlcmlvdXMnLFxuICAn5Z2a5by6JzogJ3N0cm9uZy13aWxsZWQnLFxuICAn5Y+v54ixJzogJ2N1dGUnLFxuICBcbiAgLy8g5pyN6KOFXG4gICfogYzkuJroo4UnOiAnYnVzaW5lc3MgYXR0aXJlJyxcbiAgJ+S8kemXsuijhSc6ICdjYXN1YWwgd2VhcicsXG4gICfmraPoo4UnOiAnZm9ybWFsIHdlYXInLFxuICAn5qCh5pyNJzogJ3NjaG9vbCB1bmlmb3JtJyxcbiAgJ+WPpOijhSc6ICd0cmFkaXRpb25hbCBjb3N0dW1lJyxcbiAgJ+eOsOS7o+ijhSc6ICdtb2Rlcm4gY2xvdGhpbmcnXG59XG5cbi8vIOiJuuacr+mjjuagvOmFjee9rlxuY29uc3QgQVJUX1NUWUxFUyA9IHtcbiAgYW5pbWU6IHtcbiAgICBrZXl3b3JkczogJ2FuaW1lIHN0eWxlLCBtYW5nYSBzdHlsZSwgY2VsIHNoYWRpbmcsIGNsZWFuIGxpbmVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ+aXpeW8j+WKqOa8q+mjjuagvCdcbiAgfSxcbiAgcmVhbGlzdGljOiB7XG4gICAga2V5d29yZHM6ICdwaG90b3JlYWxpc3RpYywgaHlwZXJyZWFsaXN0aWMsIHByb2Zlc3Npb25hbCBwaG90b2dyYXBoeScsXG4gICAgZGVzY3JpcHRpb246ICflhpnlrp7mkYTlvbHpo47moLwnXG4gIH0sXG4gICdzZW1pLXJlYWxpc3RpYyc6IHtcbiAgICBrZXl3b3JkczogJ3NlbWktcmVhbGlzdGljLCBkaWdpdGFsIGFydCwgZGV0YWlsZWQgaWxsdXN0cmF0aW9uJyxcbiAgICBkZXNjcmlwdGlvbjogJ+WNiuWGmeWunuaPkueUu+mjjuagvCdcbiAgfSxcbiAgJ2NvbmNlcHQtYXJ0Jzoge1xuICAgIGtleXdvcmRzOiAnY29uY2VwdCBhcnQsIGNoYXJhY3RlciBkZXNpZ24sIHByb2Zlc3Npb25hbCBpbGx1c3RyYXRpb24nLFxuICAgIGRlc2NyaXB0aW9uOiAn5qaC5b+16K6+6K6h6aOO5qC8J1xuICB9XG59XG5cbi8vIOinhuinkumFjee9rlxuY29uc3QgVklFV19UWVBFUyA9IHtcbiAgZnJvbnQ6ICdmcm9udCB2aWV3LCBmYWNpbmcgY2FtZXJhLCBsb29raW5nIGF0IHZpZXdlcicsXG4gIHNpZGU6ICdzaWRlIHZpZXcsIDkwLWRlZ3JlZSBwcm9maWxlLCBzaWRlIGFuZ2xlJyxcbiAgYmFjazogJ2JhY2sgdmlldywgcmVhciBhbmdsZSwgc2hvd2luZyBiYWNrJyxcbiAgJ3RocmVlLXF1YXJ0ZXInOiAndGhyZWUtcXVhcnRlciB2aWV3LCBzbGlnaHQgYW5nbGUnXG59XG5cbi8vIOi0qOmHj+etiee6p+mFjee9rlxuY29uc3QgUVVBTElUWV9MRVZFTFMgPSB7XG4gIHN0YW5kYXJkOiAnZ29vZCBxdWFsaXR5LCBkZXRhaWxlZCcsXG4gIGhpZ2g6ICdoaWdoIHF1YWxpdHksIGhpZ2ggcmVzb2x1dGlvbiwgZGV0YWlsZWQsIHNoYXJwIGZvY3VzJyxcbiAgbWFzdGVycGllY2U6ICdtYXN0ZXJwaWVjZSwgYmVzdCBxdWFsaXR5LCB1bHRyYSBoaWdoIHJlc29sdXRpb24sIDRLLCBoaWdobHkgZGV0YWlsZWQsIHNoYXJwIGZvY3VzJ1xufVxuXG4vKipcbiAqIOS7juinkuiJsuS/oeaBr+eUn+aIkOWbvuWDj+aPkOekuuivjVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVDaGFyYWN0ZXJQcm9tcHQoXG4gIGNoYXJhY3RlcjogQ2hhcmFjdGVySW5mbywgXG4gIG9wdGlvbnM6IFByb21wdE9wdGlvbnMgPSB7fVxuKTogc3RyaW5nIHtcbiAgY29uc3Qge1xuICAgIGFydFN0eWxlID0gJ2FuaW1lJyxcbiAgICB2aWV3VHlwZSA9ICdmcm9udCcsXG4gICAgYmFja2dyb3VuZCA9ICd3aGl0ZScsXG4gICAgcXVhbGl0eSA9ICdoaWdoJyxcbiAgICBjdXN0b21FbmhhbmNlbWVudCA9ICcnXG4gIH0gPSBvcHRpb25zXG5cbiAgLy8g5p6E5bu65o+Q56S66K+N5ZCE6YOo5YiGXG4gIGNvbnN0IHBhcnRzOiBzdHJpbmdbXSA9IFtdXG5cbiAgLy8gMS4g6LSo6YeP5ZKM6aOO5qC8XG4gIHBhcnRzLnB1c2goUVVBTElUWV9MRVZFTFNbcXVhbGl0eV0pXG4gIHBhcnRzLnB1c2goQVJUX1NUWUxFU1thcnRTdHlsZV0ua2V5d29yZHMpXG5cbiAgLy8gMi4g5Z+656GA5L+h5oGv5o+Q5Y+WXG4gIGNvbnN0IGJhc2ljSW5mbyA9IGV4dHJhY3RCYXNpY0luZm8oY2hhcmFjdGVyKVxuICBpZiAoYmFzaWNJbmZvKSBwYXJ0cy5wdXNoKGJhc2ljSW5mbylcblxuICAvLyAzLiDlpJbosoznibnlvoFcbiAgY29uc3QgYXBwZWFyYW5jZSA9IGV4dHJhY3RBcHBlYXJhbmNlRmVhdHVyZXMoY2hhcmFjdGVyKVxuICBpZiAoYXBwZWFyYW5jZSkgcGFydHMucHVzaChhcHBlYXJhbmNlKVxuXG4gIC8vIDQuIOacjeijhemjjuagvFxuICBjb25zdCBjbG90aGluZyA9IGV4dHJhY3RDbG90aGluZ1N0eWxlKGNoYXJhY3RlcilcbiAgaWYgKGNsb3RoaW5nKSBwYXJ0cy5wdXNoKGNsb3RoaW5nKVxuXG4gIC8vIDUuIOawlOi0qOihqOeOsFxuICBjb25zdCB0ZW1wZXJhbWVudCA9IGV4dHJhY3RUZW1wZXJhbWVudChjaGFyYWN0ZXIpXG4gIGlmICh0ZW1wZXJhbWVudCkgcGFydHMucHVzaCh0ZW1wZXJhbWVudClcblxuICAvLyA2LiDop4bop5Llkozlp7/mgIFcbiAgcGFydHMucHVzaChWSUVXX1RZUEVTW3ZpZXdUeXBlXSlcbiAgcGFydHMucHVzaCgnY2hhcmFjdGVyIGRlc2lnbiBzaGVldCwgcmVmZXJlbmNlIHBvc2UnKVxuXG4gIC8vIDcuIOiDjOaZr+iuvue9rlxuICBjb25zdCBiYWNrZ3JvdW5kRGVzYyA9IGdldEJhY2tncm91bmREZXNjcmlwdGlvbihiYWNrZ3JvdW5kKVxuICBwYXJ0cy5wdXNoKGJhY2tncm91bmREZXNjKVxuXG4gIC8vIDguIOaKgOacr+WPguaVsFxuICBwYXJ0cy5wdXNoKCdwcm9mZXNzaW9uYWwgY2hhcmFjdGVyIGRlc2lnbiwgY2xlYW4gY29tcG9zaXRpb24nKVxuXG4gIC8vIDkuIOiHquWumuS5ieWinuW8ulxuICBpZiAoY3VzdG9tRW5oYW5jZW1lbnQudHJpbSgpKSB7XG4gICAgcGFydHMucHVzaChjdXN0b21FbmhhbmNlbWVudClcbiAgfVxuXG4gIHJldHVybiBwYXJ0cy5maWx0ZXIoQm9vbGVhbikuam9pbignLCAnKVxufVxuXG4vKipcbiAqIOaPkOWPluWfuuehgOS/oeaBr++8iOW5tOm+hOOAgeaAp+WIq+OAgei6q+S7ve+8iVxuICovXG5mdW5jdGlvbiBleHRyYWN0QmFzaWNJbmZvKGNoYXJhY3RlcjogQ2hhcmFjdGVySW5mbyk6IHN0cmluZyB7XG4gIGNvbnN0IGluZm86IHN0cmluZ1tdID0gW11cbiAgXG4gIGlmIChjaGFyYWN0ZXIuaWRlbnRpdHkpIHtcbiAgICAvLyDku47ouqvku73kv6Hmga/kuK3mj5Dlj5blubTpvoTlkozogYzkuJpcbiAgICBjb25zdCBhZ2VNYXRjaCA9IGNoYXJhY3Rlci5pZGVudGl0eS5tYXRjaCgvKFxcZCsp5bKBfOWNgeWHoOWygXzkuozljYHlpJrlsoF85LiJ5Y2B5aSa5bKBfOWbm+WNgeWkmuWygS8pXG4gICAgaWYgKGFnZU1hdGNoKSB7XG4gICAgICBjb25zdCBhZ2UgPSBhZ2VNYXRjaFsxXSB8fCBnZXRBZ2VGcm9tRGVzY3JpcHRpb24oYWdlTWF0Y2hbMF0pXG4gICAgICBpbmZvLnB1c2goYCR7YWdlfSB5ZWFycyBvbGRgKVxuICAgIH1cbiAgICBcbiAgICAvLyDmj5Dlj5bmgKfliKvvvIjlpoLmnpzouqvku73kuK3ljIXlkKvvvIlcbiAgICBpZiAoY2hhcmFjdGVyLmlkZW50aXR5LmluY2x1ZGVzKCflpbMnKSB8fCBjaGFyYWN0ZXIuaWRlbnRpdHkuaW5jbHVkZXMoJ+WnkeWomCcpIHx8IGNoYXJhY3Rlci5pZGVudGl0eS5pbmNsdWRlcygn5bCP5aeQJykpIHtcbiAgICAgIGluZm8ucHVzaCgnd29tYW4nKVxuICAgIH0gZWxzZSBpZiAoY2hhcmFjdGVyLmlkZW50aXR5LmluY2x1ZGVzKCfnlLcnKSB8fCBjaGFyYWN0ZXIuaWRlbnRpdHkuaW5jbHVkZXMoJ+WFiOeUnycpIHx8IGNoYXJhY3Rlci5pZGVudGl0eS5pbmNsdWRlcygn5bCP5LyZJykpIHtcbiAgICAgIGluZm8ucHVzaCgnbWFuJylcbiAgICB9XG4gICAgXG4gICAgLy8g5o+Q5Y+W6IGM5LiaXG4gICAgY29uc3QgcHJvZmVzc2lvbiA9IGV4dHJhY3RQcm9mZXNzaW9uKGNoYXJhY3Rlci5pZGVudGl0eSlcbiAgICBpZiAocHJvZmVzc2lvbikgaW5mby5wdXNoKHByb2Zlc3Npb24pXG4gIH1cbiAgXG4gIHJldHVybiBpbmZvLmpvaW4oJyAnKVxufVxuXG4vKipcbiAqIOaPkOWPluWkluiyjOeJueW+gVxuICovXG5mdW5jdGlvbiBleHRyYWN0QXBwZWFyYW5jZUZlYXR1cmVzKGNoYXJhY3RlcjogQ2hhcmFjdGVySW5mbyk6IHN0cmluZyB7XG4gIGNvbnN0IGZlYXR1cmVzOiBzdHJpbmdbXSA9IFtdXG4gIFxuICAvLyDlpITnkIbkupTlrpjnibnlvoFcbiAgaWYgKGNoYXJhY3Rlci5mYWNpYWwpIHtcbiAgICBjb25zdCBmYWNpYWxGZWF0dXJlcyA9IHRyYW5zbGF0ZUZlYXR1cmVzKGNoYXJhY3Rlci5mYWNpYWwpXG4gICAgZmVhdHVyZXMucHVzaChmYWNpYWxGZWF0dXJlcylcbiAgfVxuICBcbiAgLy8g5aSE55CG6Lqr5p2Q54m55b6BXG4gIGlmIChjaGFyYWN0ZXIucGh5c2lxdWUpIHtcbiAgICBjb25zdCBib2R5RmVhdHVyZXMgPSB0cmFuc2xhdGVGZWF0dXJlcyhjaGFyYWN0ZXIucGh5c2lxdWUpXG4gICAgZmVhdHVyZXMucHVzaChib2R5RmVhdHVyZXMpXG4gIH1cbiAgXG4gIC8vIOWkhOeQhuWPkeWei1xuICBpZiAoY2hhcmFjdGVyLmhhaXJzdHlsZSkge1xuICAgIGNvbnN0IGhhaXJGZWF0dXJlcyA9IHRyYW5zbGF0ZUZlYXR1cmVzKGNoYXJhY3Rlci5oYWlyc3R5bGUpXG4gICAgZmVhdHVyZXMucHVzaChoYWlyRmVhdHVyZXMpXG4gIH1cbiAgXG4gIHJldHVybiBmZWF0dXJlcy5maWx0ZXIoQm9vbGVhbikuam9pbignLCAnKVxufVxuXG4vKipcbiAqIOaPkOWPluacjeijhemjjuagvFxuICovXG5mdW5jdGlvbiBleHRyYWN0Q2xvdGhpbmdTdHlsZShjaGFyYWN0ZXI6IENoYXJhY3RlckluZm8pOiBzdHJpbmcge1xuICBpZiAoIWNoYXJhY3Rlci5jbG90aGluZykgcmV0dXJuICcnXG4gIFxuICByZXR1cm4gdHJhbnNsYXRlRmVhdHVyZXMoY2hhcmFjdGVyLmNsb3RoaW5nKVxufVxuXG4vKipcbiAqIOaPkOWPluawlOi0qOeJueW+gVxuICovXG5mdW5jdGlvbiBleHRyYWN0VGVtcGVyYW1lbnQoY2hhcmFjdGVyOiBDaGFyYWN0ZXJJbmZvKTogc3RyaW5nIHtcbiAgaWYgKCFjaGFyYWN0ZXIucGVyc29uYWxpdHkpIHJldHVybiAnJ1xuICBcbiAgY29uc3QgdGVtcGVyYW1lbnRXb3Jkczogc3RyaW5nW10gPSBbXVxuICBcbiAgLy8g5LuO5oCn5qC85o+P6L+w5Lit5o+Q5Y+W5rCU6LSo5YWz6ZSu6K+NXG4gIE9iamVjdC5lbnRyaWVzKEZFQVRVUkVfTUFQUElORykuZm9yRWFjaCgoW2NoaW5lc2UsIGVuZ2xpc2hdKSA9PiB7XG4gICAgaWYgKGNoYXJhY3Rlci5wZXJzb25hbGl0eSEuaW5jbHVkZXMoY2hpbmVzZSkpIHtcbiAgICAgIHRlbXBlcmFtZW50V29yZHMucHVzaChlbmdsaXNoKVxuICAgIH1cbiAgfSlcbiAgXG4gIC8vIOa3u+WKoOihqOaDheaPj+i/sFxuICBpZiAoY2hhcmFjdGVyLnBlcnNvbmFsaXR5LmluY2x1ZGVzKCfmuKnmn5QnKSB8fCBjaGFyYWN0ZXIucGVyc29uYWxpdHkuaW5jbHVkZXMoJ+WWhOiJrycpKSB7XG4gICAgdGVtcGVyYW1lbnRXb3Jkcy5wdXNoKCdnZW50bGUgc21pbGUnLCAnd2FybSBleHByZXNzaW9uJylcbiAgfSBlbHNlIGlmIChjaGFyYWN0ZXIucGVyc29uYWxpdHkuaW5jbHVkZXMoJ+WGt+mFtycpIHx8IGNoYXJhY3Rlci5wZXJzb25hbGl0eS5pbmNsdWRlcygn5Lil6IKDJykpIHtcbiAgICB0ZW1wZXJhbWVudFdvcmRzLnB1c2goJ3NlcmlvdXMgZXhwcmVzc2lvbicsICdjb2xkIGdhemUnKVxuICB9IGVsc2UgaWYgKGNoYXJhY3Rlci5wZXJzb25hbGl0eS5pbmNsdWRlcygn5rS75rO8JykgfHwgY2hhcmFjdGVyLnBlcnNvbmFsaXR5LmluY2x1ZGVzKCflvIDmnJcnKSkge1xuICAgIHRlbXBlcmFtZW50V29yZHMucHVzaCgnY2hlZXJmdWwgc21pbGUnLCAnYnJpZ2h0IGV4cHJlc3Npb24nKVxuICB9XG4gIFxuICByZXR1cm4gdGVtcGVyYW1lbnRXb3Jkcy5qb2luKCcsICcpXG59XG5cbi8qKlxuICog57+76K+R54m55b6B5o+P6L+wXG4gKi9cbmZ1bmN0aW9uIHRyYW5zbGF0ZUZlYXR1cmVzKGNoaW5lc2VUZXh0OiBzdHJpbmcpOiBzdHJpbmcge1xuICBsZXQgcmVzdWx0ID0gY2hpbmVzZVRleHRcbiAgXG4gIC8vIOS9v+eUqOaYoOWwhOihqOi/m+ihjOe/u+ivkVxuICBPYmplY3QuZW50cmllcyhGRUFUVVJFX01BUFBJTkcpLmZvckVhY2goKFtjaGluZXNlLCBlbmdsaXNoXSkgPT4ge1xuICAgIGNvbnN0IHJlZ2V4ID0gbmV3IFJlZ0V4cChjaGluZXNlLCAnZycpXG4gICAgcmVzdWx0ID0gcmVzdWx0LnJlcGxhY2UocmVnZXgsIGVuZ2xpc2gpXG4gIH0pXG4gIFxuICAvLyDmuIXnkIblkozkvJjljJZcbiAgcmVzdWx0ID0gcmVzdWx0XG4gICAgLnJlcGxhY2UoL1vvvIzjgILvvJvvvJpdL2csICcsJykgLy8g5pu/5o2i5Lit5paH5qCH54K5XG4gICAgLnJlcGxhY2UoL1xccysvZywgJyAnKSAvLyDlkIjlubbnqbrmoLxcbiAgICAucmVwbGFjZSgvLCsvZywgJywnKSAvLyDlkIjlubbpgJflj7dcbiAgICAudHJpbSgpXG4gIFxuICByZXR1cm4gcmVzdWx0XG59XG5cbi8qKlxuICog5LuO5o+P6L+w5Lit5o+Q5Y+W5bm06b6EXG4gKi9cbmZ1bmN0aW9uIGdldEFnZUZyb21EZXNjcmlwdGlvbihhZ2VEZXNjOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBhZ2VNYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgJ+WNgeWHoOWygSc6ICcxNicsXG4gICAgJ+S6jOWNgeWkmuWygSc6ICcyNScsIFxuICAgICfkuInljYHlpJrlsoEnOiAnMzUnLFxuICAgICflm5vljYHlpJrlsoEnOiAnNDUnXG4gIH1cbiAgcmV0dXJuIGFnZU1hcFthZ2VEZXNjXSB8fCAnMjUnXG59XG5cbi8qKlxuICog5o+Q5Y+W6IGM5Lia5L+h5oGvXG4gKi9cbmZ1bmN0aW9uIGV4dHJhY3RQcm9mZXNzaW9uKGlkZW50aXR5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBwcm9mZXNzaW9uczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAn5a2m55SfJzogJ3N0dWRlbnQnLFxuICAgICfogIHluIgnOiAndGVhY2hlcicsXG4gICAgJ+WMu+eUnyc6ICdkb2N0b3InLFxuICAgICfmiqTlo6snOiAnbnVyc2UnLFxuICAgICflvovluIgnOiAnbGF3eWVyJyxcbiAgICAn5bel56iL5biIJzogJ2VuZ2luZWVyJyxcbiAgICAn6K6+6K6h5biIJzogJ2Rlc2lnbmVyJyxcbiAgICAn56iL5bqP5ZGYJzogJ3Byb2dyYW1tZXInLFxuICAgICfnu4/nkIYnOiAnbWFuYWdlcicsXG4gICAgJ+enmOS5pic6ICdzZWNyZXRhcnknLFxuICAgICfplIDllK4nOiAnc2FsZXNwZXJzb24nLFxuICAgICforablr58nOiAncG9saWNlIG9mZmljZXInLFxuICAgICflhpvkuronOiAnc29sZGllcicsXG4gICAgJ+iJuuacr+Wutic6ICdhcnRpc3QnLFxuICAgICfkvZzlrrYnOiAnd3JpdGVyJyxcbiAgICAn6K6w6ICFJzogJ2pvdXJuYWxpc3QnXG4gIH1cbiAgXG4gIGZvciAoY29uc3QgW2NoaW5lc2UsIGVuZ2xpc2hdIG9mIE9iamVjdC5lbnRyaWVzKHByb2Zlc3Npb25zKSkge1xuICAgIGlmIChpZGVudGl0eS5pbmNsdWRlcyhjaGluZXNlKSkge1xuICAgICAgcmV0dXJuIGVuZ2xpc2hcbiAgICB9XG4gIH1cbiAgXG4gIHJldHVybiAnJ1xufVxuXG4vKipcbiAqIOiOt+WPluiDjOaZr+aPj+i/sFxuICovXG5mdW5jdGlvbiBnZXRCYWNrZ3JvdW5kRGVzY3JpcHRpb24oYmFja2dyb3VuZDogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgYmFja2dyb3VuZHM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgd2hpdGU6ICdjbGVhbiB3aGl0ZSBiYWNrZ3JvdW5kJyxcbiAgICB0cmFuc3BhcmVudDogJ3RyYW5zcGFyZW50IGJhY2tncm91bmQnLFxuICAgIHNpbXBsZTogJ3NpbXBsZSBiYWNrZ3JvdW5kJyxcbiAgICBkZXRhaWxlZDogJ2RldGFpbGVkIGJhY2tncm91bmQgc2NlbmUnXG4gIH1cbiAgXG4gIHJldHVybiBiYWNrZ3JvdW5kc1tiYWNrZ3JvdW5kXSB8fCAnY2xlYW4gd2hpdGUgYmFja2dyb3VuZCdcbn1cblxuLyoqXG4gKiDnlJ/miJDotJ/pnaLmj5DnpLror41cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlTmVnYXRpdmVQcm9tcHQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIFtcbiAgICAnYmx1cnJ5JywgJ2xvdyBxdWFsaXR5JywgJ3BpeGVsYXRlZCcsICdkZWZvcm1lZCcsICdkaXN0b3J0ZWQnLFxuICAgICdleHRyYSBsaW1icycsICdtaXNzaW5nIGxpbWJzJywgJ2V4dHJhIGZpbmdlcnMnLCAnbWlzc2luZyBmaW5nZXJzJyxcbiAgICAnYmFkIGFuYXRvbXknLCAnYmFkIHByb3BvcnRpb25zJywgJ3VnbHknLCAnZHVwbGljYXRlJyxcbiAgICAnd2F0ZXJtYXJrJywgJ3NpZ25hdHVyZScsICd0ZXh0JywgJ2xvZ28nLFxuICAgICdvdmVyc2F0dXJhdGVkJywgJ3VuZGVyc2F0dXJhdGVkJywgJ292ZXJleHBvc2VkJywgJ3VuZGVyZXhwb3NlZCdcbiAgXS5qb2luKCcsICcpXG59XG5cbi8qKlxuICog55Sf5oiQ6KeS6Imy5LiA6Ie05oCn5o+Q56S66K+N77yI55So5LqO5LiJ6KeG5Zu+77yJXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUNvbnNpc3RlbmN5UHJvbXB0KGNoYXJhY3RlcjogQ2hhcmFjdGVySW5mbywgYmFzZVByb21wdDogc3RyaW5nKToge1xuICBmcm9udDogc3RyaW5nXG4gIHNpZGU6IHN0cmluZ1xuICBiYWNrOiBzdHJpbmdcbn0ge1xuICAvLyDmj5Dlj5bouqvmnZDlkozmnI3ppbDnibnlvoHvvIjkv53mjIHkuIDoh7TvvIlcbiAgY29uc3QgY29uc2lzdGVudEZlYXR1cmVzID0gZXh0cmFjdENvbnNpc3RlbnRGZWF0dXJlcyhjaGFyYWN0ZXIpXG5cbiAgLy8g5re75Yqg5by65YyW57uG6IqC5LiA6Ie05oCn55qE57qm5p2fXG4gIGNvbnN0IGRldGFpbENvbnNpc3RlbmN5ID0gJ2NoYXJhY3RlciBzaGVldCwgbW9kZWwgc2hlZXQsIHNhbWUgb3V0Zml0IGRldGFpbHMsIGlkZW50aWNhbCBwYXR0ZXJucywgY29uc2lzdGVudCBkZWNvcmF0aW9ucywgc2FtZSBhcm1vciBkZXNpZ24sIGlkZW50aWNhbCBhY2Nlc3NvcmllcydcblxuICByZXR1cm4ge1xuICAgIC8vIOato+mdou+8muWujOaVtOiEuOmDqOWPr+ingVxuICAgIGZyb250OiBg5q2j6Z2i6KeG5Zu+77yM6KeS6Imy6Z2i5ZCR6ZWc5aS077yM5a6M5pW055qE6IS46YOo54m55b6B5riF5pmw5Y+v6KeB77yM55u06KeG5YmN5pa577yMJHtjb25zaXN0ZW50RmVhdHVyZXN977yMJHtkZXRhaWxDb25zaXN0ZW5jeX3vvIzop5LoibLorr7orqHlm77vvIznmb3oibLog4zmma/vvIzlhajouqvlm77vvIzpq5jotKjph4/vvIzliqjmvKvpo47moLxgLFxuXG4gICAgLy8g5L6n6Z2i77yaOTDluqbkvqfohLjova7lu5NcbiAgICBzaWRlOiBg5L6n6Z2i6KeG5Zu+77yM6KeS6ImyOTDluqbkvqfouqvvvIzlroznvo7nmoTkvqfohLjova7lu5PvvIzkvqfpnaLliarlvbHvvIzpnaLlkJHlt6bkvqfmiJblj7PkvqfvvIwke2NvbnNpc3RlbnRGZWF0dXJlc33vvIwke2RldGFpbENvbnNpc3RlbmN5fe+8jOinkuiJsuiuvuiuoeWbvu+8jOeZveiJsuiDjOaZr++8jOWFqOi6q+Wbvu+8jOmrmOi0qOmHj++8jOWKqOa8q+mjjuagvGAsXG5cbiAgICAvLyDog4zpnaLvvJrnnIvkuI3liLDohLhcbiAgICBiYWNrOiBg6IOM6Z2i6KeG5Zu+77yM6KeS6Imy6IOM5a+56ZWc5aS077yM55yL5LiN5Yiw6IS46YOo77yM5Y+q5pi+56S65ZCO6ISR5Yu65ZKM6IOM6YOo77yM6IOM5b2x77yMJHtjb25zaXN0ZW50RmVhdHVyZXN977yMJHtkZXRhaWxDb25zaXN0ZW5jeX3vvIzop5LoibLorr7orqHlm77vvIznmb3oibLog4zmma/vvIzlhajouqvlm77vvIzpq5jotKjph4/vvIzliqjmvKvpo47moLxgXG4gIH1cbn1cblxuLyoqXG4gKiDmj5Dlj5bkuIDoh7TmgKfnibnlvoHvvIjouqvmnZDjgIHmnI3ppbDjgIHlj5HlnovnrYnkv53mjIHkuI3lj5jnmoTnibnlvoHvvIlcbiAqL1xuZnVuY3Rpb24gZXh0cmFjdENvbnNpc3RlbnRGZWF0dXJlcyhjaGFyYWN0ZXI6IENoYXJhY3RlckluZm8pOiBzdHJpbmcge1xuICBjb25zdCBmZWF0dXJlczogc3RyaW5nW10gPSBbXVxuXG4gIC8vIOi6q+adkOeJueW+ge+8iOS/neaMgeS4gOiHtO+8iVxuICBpZiAoY2hhcmFjdGVyLnBoeXNpcXVlKSB7XG4gICAgZmVhdHVyZXMucHVzaChjaGFyYWN0ZXIucGh5c2lxdWUpXG4gIH1cblxuICAvLyDlj5Hlnovlkozlj5HoibLvvIjkv53mjIHkuIDoh7TvvIlcbiAgaWYgKGNoYXJhY3Rlci5oYWlyc3R5bGUpIHtcbiAgICBmZWF0dXJlcy5wdXNoKGNoYXJhY3Rlci5oYWlyc3R5bGUpXG4gIH1cblxuICAvLyDmnI3ppbDpo47moLzvvIjkv53mjIHkuIDoh7TvvIzlvLrosIPnu4boioLkuIDoh7TmgKfvvIlcbiAgaWYgKGNoYXJhY3Rlci5jbG90aGluZykge1xuICAgIGZlYXR1cmVzLnB1c2goY2hhcmFjdGVyLmNsb3RoaW5nKVxuICAgIC8vIOa3u+WKoOe7huiKguS4gOiHtOaAp+e6puadn1xuICAgIGZlYXR1cmVzLnB1c2goJ+ebuOWQjOeahOijhemlsOWbvuahiCcsICfkuIDoh7TnmoToirHnurnnu4boioInLCAn55u45ZCM55qE5pyN6KOF57q555CGJylcbiAgfVxuXG4gIC8vIOi6q+S7veeJueW+ge+8iOS/neaMgeS4gOiHtO+8iVxuICBpZiAoY2hhcmFjdGVyLmlkZW50aXR5KSB7XG4gICAgZmVhdHVyZXMucHVzaChjaGFyYWN0ZXIuaWRlbnRpdHkpXG4gIH1cblxuICAvLyDmt7vliqDlvLrljJbkuIDoh7TmgKfnuqbmnZ9cbiAgZmVhdHVyZXMucHVzaCgn55u45ZCM6KeS6ImyJywgJ+WujOWFqOS4gOiHtOeahOWkluingicsICflkIzkuIDkurrniaknLCAn55u45ZCM55qE6KOF5aSH57uG6IqCJywgJ+S4gOiHtOeahOmFjemlsCcpXG5cbiAgcmV0dXJuIGZlYXR1cmVzLmpvaW4oJ++8jCcpXG59XG5cbmZ1bmN0aW9uIGV4dHJhY3RGYWNlU2hhcGUoZmFjaWFsOiBzdHJpbmcpOiBzdHJpbmcge1xuICBmb3IgKGNvbnN0IFtjaGluZXNlLCBlbmdsaXNoXSBvZiBPYmplY3QuZW50cmllcyhGRUFUVVJFX01BUFBJTkcpKSB7XG4gICAgaWYgKGZhY2lhbC5pbmNsdWRlcyhjaGluZXNlKSAmJiBjaGluZXNlLmluY2x1ZGVzKCfohLgnKSkge1xuICAgICAgcmV0dXJuIGVuZ2xpc2hcbiAgICB9XG4gIH1cbiAgcmV0dXJuICcnXG59XG5cbmZ1bmN0aW9uIGV4dHJhY3RIYWlyU3R5bGUoaGFpcnN0eWxlOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBoYWlyRmVhdHVyZXM6IHN0cmluZ1tdID0gW11cbiAgXG4gIE9iamVjdC5lbnRyaWVzKEZFQVRVUkVfTUFQUElORykuZm9yRWFjaCgoW2NoaW5lc2UsIGVuZ2xpc2hdKSA9PiB7XG4gICAgaWYgKGhhaXJzdHlsZS5pbmNsdWRlcyhjaGluZXNlKSAmJiAoY2hpbmVzZS5pbmNsdWRlcygn5Y+RJykgfHwgY2hpbmVzZS5pbmNsdWRlcygn5aS05Y+RJykpKSB7XG4gICAgICBoYWlyRmVhdHVyZXMucHVzaChlbmdsaXNoKVxuICAgIH1cbiAgfSlcbiAgXG4gIHJldHVybiBoYWlyRmVhdHVyZXMuam9pbignICcpXG59XG4iXSwibmFtZXMiOlsiRkVBVFVSRV9NQVBQSU5HIiwiQVJUX1NUWUxFUyIsImFuaW1lIiwia2V5d29yZHMiLCJkZXNjcmlwdGlvbiIsInJlYWxpc3RpYyIsIlZJRVdfVFlQRVMiLCJmcm9udCIsInNpZGUiLCJiYWNrIiwiUVVBTElUWV9MRVZFTFMiLCJzdGFuZGFyZCIsImhpZ2giLCJtYXN0ZXJwaWVjZSIsImdlbmVyYXRlQ2hhcmFjdGVyUHJvbXB0IiwiY2hhcmFjdGVyIiwib3B0aW9ucyIsImFydFN0eWxlIiwidmlld1R5cGUiLCJiYWNrZ3JvdW5kIiwicXVhbGl0eSIsImN1c3RvbUVuaGFuY2VtZW50IiwicGFydHMiLCJwdXNoIiwiYmFzaWNJbmZvIiwiZXh0cmFjdEJhc2ljSW5mbyIsImFwcGVhcmFuY2UiLCJleHRyYWN0QXBwZWFyYW5jZUZlYXR1cmVzIiwiY2xvdGhpbmciLCJleHRyYWN0Q2xvdGhpbmdTdHlsZSIsInRlbXBlcmFtZW50IiwiZXh0cmFjdFRlbXBlcmFtZW50IiwiYmFja2dyb3VuZERlc2MiLCJnZXRCYWNrZ3JvdW5kRGVzY3JpcHRpb24iLCJ0cmltIiwiZmlsdGVyIiwiQm9vbGVhbiIsImpvaW4iLCJpbmZvIiwiaWRlbnRpdHkiLCJhZ2VNYXRjaCIsIm1hdGNoIiwiYWdlIiwiZ2V0QWdlRnJvbURlc2NyaXB0aW9uIiwiaW5jbHVkZXMiLCJwcm9mZXNzaW9uIiwiZXh0cmFjdFByb2Zlc3Npb24iLCJmZWF0dXJlcyIsImZhY2lhbCIsImZhY2lhbEZlYXR1cmVzIiwidHJhbnNsYXRlRmVhdHVyZXMiLCJwaHlzaXF1ZSIsImJvZHlGZWF0dXJlcyIsImhhaXJzdHlsZSIsImhhaXJGZWF0dXJlcyIsInBlcnNvbmFsaXR5IiwidGVtcGVyYW1lbnRXb3JkcyIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JFYWNoIiwiY2hpbmVzZSIsImVuZ2xpc2giLCJjaGluZXNlVGV4dCIsInJlc3VsdCIsInJlZ2V4IiwiUmVnRXhwIiwicmVwbGFjZSIsImFnZURlc2MiLCJhZ2VNYXAiLCJwcm9mZXNzaW9ucyIsImJhY2tncm91bmRzIiwid2hpdGUiLCJ0cmFuc3BhcmVudCIsInNpbXBsZSIsImRldGFpbGVkIiwiZ2VuZXJhdGVOZWdhdGl2ZVByb21wdCIsImdlbmVyYXRlQ29uc2lzdGVuY3lQcm9tcHQiLCJiYXNlUHJvbXB0IiwiY29uc2lzdGVudEZlYXR1cmVzIiwiZXh0cmFjdENvbnNpc3RlbnRGZWF0dXJlcyIsImRldGFpbENvbnNpc3RlbmN5IiwiZXh0cmFjdEZhY2VTaGFwZSIsImV4dHJhY3RIYWlyU3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/promptGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-appearance%2Froute&page=%2Fapi%2Fai%2Fgenerate-appearance%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-appearance%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();