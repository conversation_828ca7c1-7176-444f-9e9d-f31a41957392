// 检查角色参考图的脚本
async function checkCharacters() {
  try {
    // 使用内置的fetch API
    const response = await fetch('http://localhost:3001/api/characters?projectId=cmc8xt0j9000bvm8ktp77lcdx');
    const data = await response.json();

    if (data.success && data.data) {
      console.log('=== 角色参考图检查结果 ===\n');

      data.data.forEach(character => {
        console.log(`角色名称: ${character.name}`);
        console.log(`角色ID: ${character.id}`);

        if (character.generatedImages) {
          try {
            const images = JSON.parse(character.generatedImages);
            console.log('✅ 已生成参考图:');
            console.log(`  - 正面图: ${images.front ? '有' : '无'}`);
            console.log(`  - 侧面图: ${images.side ? '有' : '无'}`);
            console.log(`  - 背面图: ${images.back ? '有' : '无'}`);

            // 显示图像URL的前50个字符
            if (images.front) console.log(`  - 正面图URL: ${images.front.substring(0, 50)}...`);
            if (images.side) console.log(`  - 侧面图URL: ${images.side.substring(0, 50)}...`);
            if (images.back) console.log(`  - 背面图URL: ${images.back.substring(0, 50)}...`);
          } catch (e) {
            console.log('❌ 参考图数据解析失败:', e.message);
          }
        } else {
          console.log('❌ 未生成参考图 (generatedImages字段为空)');
        }

        if (character.referenceImages) {
          console.log('✅ 有额外参考图像');
        }

        console.log('---\n');
      });

      // 特别检查李四
      const lisi = data.data.find(char => char.name === '李四');
      if (lisi) {
        console.log('🔍 李四角色详细检查:');
        console.log(`  - generatedImages字段: ${lisi.generatedImages ? '有数据' : '无数据'}`);
        if (lisi.generatedImages) {
          try {
            const images = JSON.parse(lisi.generatedImages);
            console.log(`  - 解析结果: front=${!!images.front}, side=${!!images.side}, back=${!!images.back}`);
          } catch (e) {
            console.log(`  - 解析失败: ${e.message}`);
          }
        }
      } else {
        console.log('❌ 未找到李四角色');
      }
    } else {
      console.log('获取角色数据失败:', data);
    }
  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkCharacters();
