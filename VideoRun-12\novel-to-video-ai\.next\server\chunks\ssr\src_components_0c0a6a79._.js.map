{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/ModelSelector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { ChevronDown, Check, Settings, AlertCircle } from 'lucide-react'\nimport { AIConfig } from '@/types'\n\ninterface ModelOption {\n  id: string\n  name: string\n  model: string\n  provider: string\n  description: string\n}\n\ninterface ModelSelectorProps {\n  selectedModel?: string\n  onModelChange?: (modelId: string) => void\n  onModelSelect?: (modelId: string) => void\n  className?: string\n}\n\nexport default function ModelSelector({\n  selectedModel,\n  onModelChange,\n  onModelSelect,\n  className = ''\n}: ModelSelectorProps) {\n  // 统一处理回调函数\n  const handleModelSelect = (modelId: string) => {\n    onModelChange?.(modelId)\n    onModelSelect?.(modelId)\n  }\n  const [isOpen, setIsOpen] = useState(false)\n  const [models, setModels] = useState<ModelOption[]>([])\n  const [loading, setLoading] = useState(true)\n  const [hasConfig, setHasConfig] = useState(false)\n\n  // 获取已配置的模型列表\n  useEffect(() => {\n    fetchModels()\n  }, [])\n\n  const fetchModels = async () => {\n    try {\n      const response = await fetch('/api/models')\n      const data = await response.json()\n\n      if (data.success && data.data.length > 0) {\n        // 只显示已启用且已配置API密钥的模型\n        const enabledModels = data.data.filter((model: any) =>\n          model.enabled &&\n          model.apiKey &&\n          model.apiKey.trim() !== ''\n        )\n\n        if (enabledModels.length > 0) {\n          setHasConfig(true)\n\n          // 转换为ModelOption格式\n          const modelOptions: ModelOption[] = enabledModels.map((model: any) => ({\n            id: model.id,\n            name: model.name,\n            model: model.model,\n            provider: model.provider,\n            description: model.description || '已配置模型'\n          }))\n\n          setModels(modelOptions)\n\n          // 如果没有选中模型，默认选择第一个启用的模型\n          if (!selectedModel && modelOptions.length > 0) {\n            handleModelSelect(modelOptions[0].id)\n          }\n        } else {\n          setHasConfig(false)\n          setModels([])\n        }\n      } else {\n        setHasConfig(false)\n        setModels([])\n      }\n    } catch (error) {\n      console.error('获取模型列表失败:', error)\n      setHasConfig(false)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 获取当前选中的模型信息\n  const selectedModelInfo = models.find(model => model.id === selectedModel)\n\n  // 获取模型提供商显示名称\n  const getProviderName = (provider: string) => {\n    switch (provider) {\n      case 'deepseek': return 'DeepSeek'\n      case 'openai': return 'OpenAI'\n      case 'claude': return 'Claude'\n      case 'doubao': return '豆包'\n      case 'tongyi': return '通义'\n      case 'minimax': return 'MiniMax'\n      case 'zhipu': return '智谱AI'\n      case 'skyreels': return 'SkyReels-V2'\n      default: return provider\n    }\n  }\n\n  // 获取模型状态颜色\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'connected': return 'text-green-600'\n      case 'disconnected': return 'text-gray-400'\n      case 'error': return 'text-red-600'\n      default: return 'text-gray-400'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className={`inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-gray-50 ${className}`}>\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 mr-2\"></div>\n        <span className=\"text-sm text-gray-500\">加载中...</span>\n      </div>\n    )\n  }\n\n  if (!hasConfig) {\n    return (\n      <div className={`inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-gray-50 ${className}`}>\n        <AlertCircle className=\"text-orange-500 mr-2\" size={16} />\n        <span className=\"text-sm text-gray-500\">请先配置AI模型</span>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`relative ${className}`}>\n      {/* 选择按钮 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500\"\n      >\n        <Settings className=\"text-gray-400 mr-2\" size={14} />\n        <span className=\"text-sm text-gray-700\">\n          {selectedModelInfo ? (\n            <>\n              {selectedModelInfo.name}\n            </>\n          ) : (\n            '选择模型'\n          )}\n        </span>\n        <ChevronDown className=\"ml-2 text-gray-400\" size={14} />\n      </button>\n\n      {/* 下拉菜单 */}\n      {isOpen && (\n        <>\n          {/* 背景遮罩 */}\n          <div \n            className=\"fixed inset-0 z-40\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* 下拉选项 */}\n          <div className=\"absolute right-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50\">\n            <div className=\"py-1\">\n              <div className=\"px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100\">\n                选择AI模型\n              </div>\n\n              {/* 模型列表容器 - 添加最大高度和滚动条 */}\n              <div className=\"max-h-60 overflow-y-auto\">\n                {models.map((model) => (\n                  <button\n                    key={model.id}\n                    onClick={() => {\n                      handleModelSelect(model.id)\n                      setIsOpen(false)\n                    }}\n                    className={`\n                      w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between\n                      ${selectedModel === model.id ? 'bg-purple-50 text-purple-700' : 'text-gray-700'}\n                    `}\n                  >\n                    <div className=\"flex-1\">\n                      <div className=\"font-medium\">\n                        {model.name}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {model.description}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center\">\n                      {selectedModel === model.id && (\n                        <Check className=\"text-purple-600\" size={16} />\n                      )}\n                    </div>\n                  </button>\n                ))}\n\n                {models.length === 0 && (\n                  <div className=\"px-3 py-2 text-sm text-gray-500 text-center\">\n                    暂无可用模型\n                  </div>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"border-t border-gray-100 px-3 py-2\">\n              <a\n                href=\"/models\"\n                className=\"text-xs text-purple-600 hover:text-purple-700\"\n                onClick={() => setIsOpen(false)}\n              >\n                配置更多模型 →\n              </a>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAqBe,SAAS,cAAc,EACpC,aAAa,EACb,aAAa,EACb,aAAa,EACb,YAAY,EAAE,EACK;IACnB,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;gBACxC,qBAAqB;gBACrB,MAAM,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,QACtC,MAAM,OAAO,IACb,MAAM,MAAM,IACZ,MAAM,MAAM,CAAC,IAAI,OAAO;gBAG1B,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,aAAa;oBAEb,mBAAmB;oBACnB,MAAM,eAA8B,cAAc,GAAG,CAAC,CAAC,QAAe,CAAC;4BACrE,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,OAAO,MAAM,KAAK;4BAClB,UAAU,MAAM,QAAQ;4BACxB,aAAa,MAAM,WAAW,IAAI;wBACpC,CAAC;oBAED,UAAU;oBAEV,wBAAwB;oBACxB,IAAI,CAAC,iBAAiB,aAAa,MAAM,GAAG,GAAG;wBAC7C,kBAAkB,YAAY,CAAC,EAAE,CAAC,EAAE;oBACtC;gBACF,OAAO;oBACL,aAAa;oBACb,UAAU,EAAE;gBACd;YACF,OAAO;gBACL,aAAa;gBACb,UAAU,EAAE;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAa;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,cAAc;IACd,MAAM,oBAAoB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE5D,cAAc;IACd,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,gFAAgF,EAAE,WAAW;;8BAC5G,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAW,CAAC,gFAAgF,EAAE,WAAW;;8BAC5G,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAuB,MAAM;;;;;;8BACpD,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAqB,MAAM;;;;;;kCAC/C,8OAAC;wBAAK,WAAU;kCACb,kCACC;sCACG,kBAAkB,IAAI;4CAGzB;;;;;;kCAGJ,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;wBAAqB,MAAM;;;;;;;;;;;;YAInD,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuE;;;;;;kDAKtF,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oDAEC,SAAS;wDACP,kBAAkB,MAAM,EAAE;wDAC1B,UAAU;oDACZ;oDACA,WAAW,CAAC;;sBAEV,EAAE,kBAAkB,MAAM,EAAE,GAAG,iCAAiC,gBAAgB;oBAClF,CAAC;;sEAED,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,MAAM,IAAI;;;;;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,MAAM,WAAW;;;;;;;;;;;;sEAItB,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,MAAM,EAAE,kBACzB,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;gEAAkB,MAAM;;;;;;;;;;;;mDArBxC,MAAM,EAAE;;;;;4CA2BhB,OAAO,MAAM,KAAK,mBACjB,8OAAC;gDAAI,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAOnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Home,\n  FolderOpen,\n  Settings,\n  User,\n  Menu,\n  X,\n  Sparkles\n} from 'lucide-react'\n\ninterface LayoutProps {\n  children: React.ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navigation = [\n    {\n      name: '首页',\n      href: '/',\n      icon: Home,\n      current: pathname === '/'\n    },\n    {\n      name: '项目',\n      href: '/projects',\n      icon: FolderOpen,\n      current: pathname.startsWith('/projects')\n    },\n    {\n      name: '模型配置',\n      href: '/models',\n      icon: Settings,\n      current: pathname === '/models'\n    },\n    {\n      name: '账户',\n      href: '/account',\n      icon: User,\n      current: pathname === '/account',\n      disabled: true\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* 左侧 Logo */}\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <Sparkles className=\"text-purple-600 mr-2\" size={24} />\n                <span className=\"text-lg font-semibold text-gray-900 whitespace-nowrap\">\n                  灵犀Ai——小说转视频神器\n                </span>\n              </Link>\n            </div>\n\n            {/* 右侧导航菜单 */}\n            <div className=\"hidden md:flex md:items-center md:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    inline-flex items-center text-lg font-semibold\n                    ${item.current\n                      ? 'text-purple-600'\n                      : 'text-gray-900 hover:text-purple-600'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                >\n                  <item.icon className=\"mr-2\" size={20} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden flex items-center\">\n              <button\n                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <Menu size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端菜单 */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    flex items-center px-3 py-2 text-sm font-medium border-l-4\n                    ${item.current\n                      ? 'bg-purple-50 border-purple-500 text-purple-700'\n                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <item.icon className=\"mr-2\" size={16} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* 主内容区域 */}\n      <main className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAmBe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;YACV,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,SAAS,aAAa;YACtB,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,MAAM;;;;;;0DACjD,8OAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;;;;;;8CAO5E,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,oBACA,sCACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;;8DAED,8OAAC,KAAK,IAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDACjC,KAAK,IAAI;;2CAZL,KAAK,IAAI;;;;;;;;;;8CAkBpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,mDACA,wEACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;oCACD,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC,KAAK,IAAI;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCACjC,KAAK,IAAI;;mCAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BAsB1B,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/ProjectFileUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useCallback, useState, useEffect } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { Upload, FileText, X, CheckCircle, AlertCircle, ChevronDown } from 'lucide-react'\nimport { FileParser, FileParseError } from '@/lib/fileParser'\nimport ModelSelector from './ModelSelector'\n\ninterface ProjectFileUploadProps {\n  projectId: string\n  project?: any\n  onUploadSuccess: (project: any) => void\n  onUploadError: (error: string) => void\n}\n\nexport default function ProjectFileUpload({\n  projectId,\n  project,\n  onUploadSuccess,\n  onUploadError\n}: ProjectFileUploadProps) {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null)\n  const [fileContent, setFileContent] = useState<string>('')\n  const [isReading, setIsReading] = useState(false)\n  const [isUploading, setIsUploading] = useState(false)\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [uploadSuccess, setUploadSuccess] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false)\n  const [selectedModel, setSelectedModel] = useState<string>('')\n\n  // 增强提示词相关状态\n  const [showPromptDropdown, setShowPromptDropdown] = useState(false)\n  const [enhancePrompt, setEnhancePrompt] = useState('')\n  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')\n\n  // 文件解析函数\n  const parseFileContent = useCallback(async (file: File): Promise<string> => {\n    try {\n      return await FileParser.parseFile(file)\n    } catch (error) {\n      if (error instanceof FileParseError) {\n        throw new Error(error.message)\n      }\n      throw new Error('文件解析失败')\n    }\n  }, [])\n\n  // 文件拖拽处理\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    const file = acceptedFiles[0]\n    if (!file) return\n\n    setError(null)\n    setIsReading(true)\n\n    try {\n      // 解析文件内容（包含格式验证）\n      const content = await parseFileContent(file)\n\n      setSelectedFile(file)\n      setFileContent(content)\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '文件读取失败')\n    } finally {\n      setIsReading(false)\n    }\n  }, [parseFileContent])\n\n  // 配置 dropzone\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'text/plain': ['.txt'],\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\n      'application/msword': ['.doc'],\n    },\n    multiple: false,\n    disabled: isUploading || isReading || isAnalyzing,\n  })\n\n  // 检查是否需要确认重新上传\n  const handleUploadClick = () => {\n    if (!selectedFile || !fileContent) return\n\n    // 如果项目已有内容，显示确认对话框\n    if (project?.content && project.content.trim()) {\n      setShowConfirmDialog(true)\n    } else {\n      handleUpload()\n    }\n  }\n\n  // 确认重新上传\n  const handleConfirmUpload = () => {\n    setShowConfirmDialog(false)\n    handleUpload()\n  }\n\n  // 取消重新上传\n  const handleCancelUpload = () => {\n    setShowConfirmDialog(false)\n  }\n\n  // 上传文件\n  const handleUpload = async () => {\n    if (!selectedFile || !fileContent) return\n\n    setIsUploading(true)\n    setError(null)\n\n    try {\n      // 1. 上传文件到项目\n      const uploadResponse = await fetch(`/api/projects/${projectId}/upload`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          fileName: selectedFile.name,\n          content: fileContent,\n        }),\n      })\n\n      const uploadData = await uploadResponse.json()\n\n      if (!uploadData.success) {\n        throw new Error(uploadData.error || '文件上传失败')\n      }\n\n      setUploadSuccess(true)\n      setIsAnalyzing(true)\n\n      // 2. 启动AI分析\n      const analyzeResponse = await fetch(`/api/projects/${projectId}/analyze`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          modelId: selectedModel,\n          customPrompt: savedEnhancePrompt || undefined\n        })\n      })\n\n      const analyzeData = await analyzeResponse.json()\n\n      if (analyzeData.success) {\n        setIsAnalyzing(false)\n        onUploadSuccess(analyzeData.data)\n      } else {\n        throw new Error(analyzeData.error || 'AI分析失败')\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '上传失败'\n      setError(errorMessage)\n      onUploadError(errorMessage)\n      setIsAnalyzing(false)\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  // 清除选择的文件\n  const clearFile = () => {\n    setSelectedFile(null)\n    setFileContent('')\n    setError(null)\n    setUploadSuccess(false)\n  }\n\n  // 保存增强提示词\n  const handleSaveEnhancePrompt = () => {\n    setSavedEnhancePrompt(enhancePrompt)\n    setShowPromptDropdown(false)\n    // 保存到localStorage\n    const storageKey = `enhance_prompt_upload_${projectId}`\n    localStorage.setItem(storageKey, enhancePrompt)\n  }\n\n  // 从localStorage加载增强提示词\n  useEffect(() => {\n    if (projectId) {\n      const storageKey = `enhance_prompt_upload_${projectId}`\n      const savedPrompt = localStorage.getItem(storageKey)\n      if (savedPrompt) {\n        setSavedEnhancePrompt(savedPrompt)\n        setEnhancePrompt(savedPrompt)\n      }\n    }\n  }, [projectId])\n\n  // 如果上传成功，显示成功状态\n  if (uploadSuccess && !isAnalyzing) {\n    return (\n      <div className=\"text-center py-8\">\n        <CheckCircle className=\"mx-auto h-12 w-12 text-green-500\" />\n        <h3 className=\"mt-2 text-lg font-medium text-gray-900\">上传成功！</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          文件已上传并完成AI分析，请查看角色和剧集信息\n        </p>\n      </div>\n    )\n  }\n\n  // 如果正在分析，显示分析状态\n  if (isAnalyzing) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n        <h3 className=\"mt-2 text-lg font-medium text-gray-900\">AI分析中...</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          正在提取角色信息和拆分剧集，请稍候\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 确认重新上传对话框 */}\n      {showConfirmDialog && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3 text-center\">\n              <AlertCircle className=\"mx-auto h-12 w-12 text-yellow-400\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mt-2\">确认重新上传</h3>\n              <div className=\"mt-2 px-7 py-3\">\n                <p className=\"text-sm text-gray-500\">\n                  小说已上传，如果点击确认重新上传将删掉之前的人物信息、剧情信息。\n                </p>\n              </div>\n              <div className=\"items-center px-4 py-3\">\n                <button\n                  onClick={handleCancelUpload}\n                  className=\"px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-400\"\n                >\n                  取消\n                </button>\n                <button\n                  onClick={handleConfirmUpload}\n                  className=\"px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700\"\n                >\n                  确认重新上传\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"rounded-md bg-red-50 p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">上传失败</h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                <p>{error}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {!selectedFile ? (\n        /* 文件选择区域 */\n        <div\n          {...getRootProps()}\n          className={`\n            relative border-2 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent cursor-pointer\n            ${isDragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'}\n            ${(isUploading || isReading) ? 'opacity-50 cursor-not-allowed' : ''}\n          `}\n        >\n          <input {...getInputProps()} />\n          \n          {isReading ? (\n            <div className=\"space-y-2\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto\"></div>\n              <p className=\"text-sm text-gray-600\">正在读取文件...</p>\n            </div>\n          ) : (\n            <div className=\"space-y-2\">\n              <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n              {isDragActive ? (\n                <p className=\"text-sm text-purple-600\">\n                  松开鼠标上传文件\n                </p>\n              ) : (\n                <>\n                  <p className=\"text-gray-600 mb-2\">\n                    拖拽文件到此处，或点击选择文件\n                  </p>\n                  <p className=\"text-sm text-gray-500\">\n                    支持 .txt、.docx 格式，最大 50MB\n                  </p>\n                </>\n              )}\n            </div>\n          )}\n        </div>\n      ) : (\n        /* 文件信息和上传 */\n        <div className=\"space-y-4\">\n          {/* 文件信息 */}\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center\">\n              <FileText className=\"mr-3 text-blue-500\" size={20} />\n              <div>\n                <p className=\"font-medium\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-500\">\n                  {FileParser.getFileTypeDescription(selectedFile)} • {FileParser.formatFileSize(selectedFile.size)}\n                </p>\n              </div>\n            </div>\n            <button\n              onClick={clearFile}\n              className=\"p-1 text-gray-400 hover:text-gray-600\"\n              disabled={isUploading}\n            >\n              <X size={20} />\n            </button>\n          </div>\n\n          {/* 内容预览 */}\n          <div className=\"bg-white border rounded-lg p-4\">\n            <h4 className=\"text-sm font-medium text-gray-900 mb-2\">内容预览</h4>\n            <div className=\"text-sm text-gray-600 max-h-32 overflow-y-auto\">\n              {fileContent.substring(0, 500)}\n              {fileContent.length > 500 && '...'}\n            </div>\n            <p className=\"text-xs text-gray-500 mt-2\">\n              共 {fileContent.length.toLocaleString()} 个字符\n            </p>\n          </div>\n\n          {/* 模型选择器 */}\n          <div className=\"flex items-center space-x-2 mb-4\">\n            <span className=\"text-sm font-medium text-gray-700\">分析模型:</span>\n            <ModelSelector\n              selectedModel={selectedModel}\n              onModelChange={setSelectedModel}\n              className=\"flex-1\"\n            />\n          </div>\n\n          {/* 上传按钮 */}\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              onClick={clearFile}\n              disabled={isUploading}\n              className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              重新选择\n            </button>\n\n            <div className=\"relative flex\">\n              <button\n                onClick={handleUploadClick}\n                disabled={isUploading || !fileContent || !selectedModel}\n                className=\"px-4 py-2 border border-transparent rounded-l-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isUploading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block\"></div>\n                    上传中...\n                  </>\n                ) : (\n                  '上传并分析'\n                )}\n              </button>\n\n              <button\n                onClick={() => setShowPromptDropdown(!showPromptDropdown)}\n                disabled={isUploading || !fileContent || !selectedModel}\n                className=\"px-2 py-2 border border-transparent rounded-r-md border-l border-purple-500 shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ChevronDown size={14} />\n              </button>\n\n              {/* 增强提示词下拉框 */}\n              {showPromptDropdown && (\n                <div className=\"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-md shadow-lg z-10\">\n                  <div className=\"p-4\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      增强提示词设置\n                    </label>\n                    <textarea\n                      value={enhancePrompt}\n                      onChange={(e) => setEnhancePrompt(e.target.value)}\n                      placeholder=\"输入增强提示词，用于优化AI分析效果...\"\n                      className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none\"\n                      rows={4}\n                    />\n\n                    {/* 保存按钮 */}\n                    <div className=\"mt-3\">\n                      <button\n                        onClick={handleSaveEnhancePrompt}\n                        className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2\"\n                      >\n                        保存增强提示词\n                      </button>\n                    </div>\n\n                    {/* 操作按钮 */}\n                    <div className=\"flex justify-end mt-3\">\n                      <button\n                        onClick={() => setShowPromptDropdown(false)}\n                        className=\"text-sm text-gray-600 hover:text-gray-800\"\n                      >\n                        关闭\n                      </button>\n                    </div>\n\n                    {/* 当前保存的提示词预览 */}\n                    {savedEnhancePrompt && (\n                      <div className=\"mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600\">\n                        <div className=\"font-medium mb-1\">当前已保存的增强提示词：</div>\n                        <div className=\"max-h-16 overflow-y-auto\">\n                          {savedEnhancePrompt}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 上传说明 */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n          上传后AI将自动：\n        </h4>\n        <ul className=\"text-sm text-blue-700 space-y-1\">\n          <li>• 提取角色信息（名称、五官、身份、外貌、性格、隐线）</li>\n          <li>• 按章节拆分剧集</li>\n          <li>• 生成故事标题和详细剧情</li>\n          <li>• 为后续视频生成做准备</li>\n        </ul>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAee,SAAS,kBAAkB,EACxC,SAAS,EACT,OAAO,EACP,eAAe,EACf,aAAa,EACU;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,YAAY;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,SAAS;IACT,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,OAAO,MAAM,wHAAA,CAAA,aAAU,CAAC,SAAS,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,wHAAA,CAAA,iBAAc,EAAE;gBACnC,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YACA,MAAM,IAAI,MAAM;QAClB;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,MAAM,OAAO,aAAa,CAAC,EAAE;QAC7B,IAAI,CAAC,MAAM;QAEX,SAAS;QACT,aAAa;QAEb,IAAI;YACF,iBAAiB;YACjB,MAAM,UAAU,MAAM,iBAAiB;YAEvC,gBAAgB;YAChB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;KAAiB;IAErB,cAAc;IACd,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,cAAc;gBAAC;aAAO;YACtB,2EAA2E;gBAAC;aAAQ;YACpF,sBAAsB;gBAAC;aAAO;QAChC;QACA,UAAU;QACV,UAAU,eAAe,aAAa;IACxC;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnC,mBAAmB;QACnB,IAAI,SAAS,WAAW,QAAQ,OAAO,CAAC,IAAI,IAAI;YAC9C,qBAAqB;QACvB,OAAO;YACL;QACF;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB;QAC1B,qBAAqB;QACrB;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnC,eAAe;QACf,SAAS;QAET,IAAI;YACF,aAAa;YACb,MAAM,iBAAiB,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,OAAO,CAAC,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,aAAa,IAAI;oBAC3B,SAAS;gBACX;YACF;YAEA,MAAM,aAAa,MAAM,eAAe,IAAI;YAE5C,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;YACtC;YAEA,iBAAiB;YACjB,eAAe;YAEf,YAAY;YACZ,MAAM,kBAAkB,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,QAAQ,CAAC,EAAE;gBACxE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,cAAc,sBAAsB;gBACtC;YACF;YAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAE9C,IAAI,YAAY,OAAO,EAAE;gBACvB,eAAe;gBACf,gBAAgB,YAAY,IAAI;YAClC,OAAO;gBACL,MAAM,IAAI,MAAM,YAAY,KAAK,IAAI;YACvC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS;YACT,cAAc;YACd,eAAe;QACjB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,UAAU;IACV,MAAM,YAAY;QAChB,gBAAgB;QAChB,eAAe;QACf,SAAS;QACT,iBAAiB;IACnB;IAEA,UAAU;IACV,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,sBAAsB;QACtB,kBAAkB;QAClB,MAAM,aAAa,CAAC,sBAAsB,EAAE,WAAW;QACvD,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,aAAa,CAAC,sBAAsB,EAAE,WAAW;YACvD,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,sBAAsB;gBACtB,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,IAAI,iBAAiB,CAAC,aAAa;QACjC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAKhD;IAEA,gBAAgB;IAChB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAKhD;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOb,CAAC,eACA,UAAU,iBACV,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;YAEV,EAAE,eAAe,mCAAmC,kBAAkB;YACtE,EAAE,AAAC,eAAe,YAAa,kCAAkC,GAAG;UACtE,CAAC;;kCAED,8OAAC;wBAAO,GAAG,eAAe;;;;;;oBAEzB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;6CAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,6BACC,8OAAC;gCAAE,WAAU;0CAA0B;;;;;qDAIvC;;kDACE,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;uBAS/C,WAAW,iBACX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAqB,MAAM;;;;;;kDAC/C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAe,aAAa,IAAI;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;;oDACV,wHAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC;oDAAc;oDAAI,wHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,aAAa,IAAI;;;;;;;;;;;;;;;;;;;0CAItG,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,UAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,YAAY,SAAS,CAAC,GAAG;oCACzB,YAAY,MAAM,GAAG,OAAO;;;;;;;0CAE/B,8OAAC;gCAAE,WAAU;;oCAA6B;oCACrC,YAAY,MAAM,CAAC,cAAc;oCAAG;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;0CACpD,8OAAC,mIAAA,CAAA,UAAa;gCACZ,eAAe;gCACf,eAAe;gCACf,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,UAAU,eAAe,CAAC,eAAe,CAAC;wCAC1C,WAAU;kDAET,4BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAAoF;;2DAIrG;;;;;;kDAIJ,8OAAC;wCACC,SAAS,IAAM,sBAAsB,CAAC;wCACtC,UAAU,eAAe,CAAC,eAAe,CAAC;wCAC1C,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;oCAIpB,oCACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,aAAY;oDACZ,WAAU;oDACV,MAAM;;;;;;8DAIR,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;8DAMH,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;kEACX;;;;;;;;;;;gDAMF,oCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmB;;;;;;sEAClC,8OAAC;4DAAI,WAAU;sEACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAarB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/AppearancePreview.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { X, Save, Refresh<PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'\n\ninterface AppearanceData {\n  front: {\n    url: string\n    format: string\n    error?: string\n  }\n  side: {\n    url: string\n    format: string\n    error?: string\n  }\n  back: {\n    url: string\n    format: string\n    error?: string\n  }\n  character: {\n    name: string\n    description: string\n  }\n}\n\ninterface AppearancePreviewProps {\n  isOpen: boolean\n  onClose: () => void\n  onSave: (data: AppearanceData) => void\n  onRegenerate: (customPrompt?: string) => void\n  data: AppearanceData | null\n  isGenerating: boolean\n}\n\nexport default function AppearancePreview({\n  isOpen,\n  onClose,\n  onSave,\n  onRegenerate,\n  data,\n  isGenerating\n}: AppearancePreviewProps) {\n  const [showPromptInput, setShowPromptInput] = useState(false)\n  const [customPrompt, setCustomPrompt] = useState('')\n\n  if (!isOpen) return null\n\n  const handleSave = () => {\n    if (data) {\n      onSave(data)\n      onClose()\n    }\n  }\n\n  const handleRegenerateWithPrompt = () => {\n    onRegenerate(customPrompt)\n    setShowPromptInput(false)\n    setCustomPrompt('')\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* 背景遮罩 */}\n      <div \n        className=\"fixed inset-0 bg-black bg-opacity-50\"\n        onClick={onClose}\n      />\n      \n      {/* 弹窗内容 */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div className=\"relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\">\n          {/* 头部 */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <Sparkles className=\"text-purple-600 mr-3\" size={24} />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">AI生成的角色三视图</h3>\n                <p className=\"text-sm text-gray-500\">正面、侧面、背面三视图，确认后保存到形象设置</p>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600\"\n            >\n              <X size={20} />\n            </button>\n          </div>\n\n          {/* 内容区域 */}\n          <div className=\"p-6 overflow-y-auto max-h-[60vh]\">\n            {isGenerating ? (\n              /* 生成中状态 */\n              <div className=\"text-center py-12\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4\"></div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">AI正在生成角色三视图...</h4>\n                <p className=\"text-sm text-gray-500\">\n                  正在基于角色信息生成正面、侧面、背面三视图，请稍候\n                </p>\n              </div>\n            ) : data ? (\n              /* 生成结果展示 */\n              <div className=\"space-y-6\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n                  <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n                    ✨ AI生成完成\n                  </h4>\n                  <p className=\"text-sm text-blue-700\">\n                    以下是基于角色信息生成的正、侧、背三视图，您可以预览后选择保存到形象设置中。\n                  </p>\n                </div>\n\n                {/* 角色信息 */}\n                <div className=\"bg-gray-50 p-4 rounded-lg mb-6\">\n                  <h5 className=\"text-sm font-medium text-gray-700 mb-2\">角色：{data.character.name}</h5>\n                  <p className=\"text-xs text-gray-600\">{data.character.description}</p>\n                </div>\n\n                {/* 三视图展示 */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  {/* 正面视图 */}\n                  <div className=\"space-y-3\">\n                    <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                      <div className=\"w-3 h-3 bg-purple-500 rounded-full mr-2\"></div>\n                      正面视图\n                    </h5>\n                    <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                      {data.front.error ? (\n                        <div className=\"h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center\">\n                          <div>\n                            <div className=\"text-red-500 mb-2\">⚠️</div>\n                            <div>{data.front.error}</div>\n                          </div>\n                        </div>\n                      ) : (\n                        <img\n                          src={data.front.url}\n                          alt=\"正面视图\"\n                          className=\"w-full h-full object-cover\"\n                          onError={(e) => {\n                            const target = e.target as HTMLImageElement\n                            target.src = '/placeholder-character.svg'\n                          }}\n                        />\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 侧面视图 */}\n                  <div className=\"space-y-3\">\n                    <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                      <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n                      侧面视图\n                    </h5>\n                    <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                      {data.side.error ? (\n                        <div className=\"h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center\">\n                          <div>\n                            <div className=\"text-red-500 mb-2\">⚠️</div>\n                            <div>{data.side.error}</div>\n                          </div>\n                        </div>\n                      ) : (\n                        <img\n                          src={data.side.url}\n                          alt=\"侧面视图\"\n                          className=\"w-full h-full object-cover\"\n                          onError={(e) => {\n                            const target = e.target as HTMLImageElement\n                            target.src = '/placeholder-character.svg'\n                          }}\n                        />\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 背面视图 */}\n                  <div className=\"space-y-3\">\n                    <h5 className=\"text-sm font-medium text-gray-700 text-center flex items-center justify-center\">\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                      背面视图\n                    </h5>\n                    <div className=\"bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]\">\n                      {data.back.error ? (\n                        <div className=\"h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center\">\n                          <div>\n                            <div className=\"text-red-500 mb-2\">⚠️</div>\n                            <div>{data.back.error}</div>\n                          </div>\n                        </div>\n                      ) : (\n                        <img\n                          src={data.back.url}\n                          alt=\"背面视图\"\n                          className=\"w-full h-full object-cover\"\n                          onError={(e) => {\n                            const target = e.target as HTMLImageElement\n                            target.src = '/placeholder-character.svg'\n                          }}\n                        />\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              /* 错误状态 */\n              <div className=\"text-center py-12\">\n                <div className=\"text-red-500 mb-4\">\n                  <X size={48} className=\"mx-auto\" />\n                </div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">生成失败</h4>\n                <p className=\"text-sm text-gray-500 mb-4\">\n                  AI三视图生成过程中出现错误，请重试\n                </p>\n                <button\n                  onClick={() => onRegenerate()}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700\"\n                >\n                  <RefreshCw className=\"mr-2\" size={16} />\n                  重新生成\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* 底部操作按钮 */}\n          {!isGenerating && data && (\n            <div className=\"p-6 border-t border-gray-200 bg-gray-50 space-y-4\">\n              {/* 修改提示词区域 */}\n              {showPromptInput ? (\n                <div className=\"space-y-3\">\n                  <label className=\"block text-sm font-medium text-gray-700\">\n                    修改提示词\n                  </label>\n                  <textarea\n                    value={customPrompt}\n                    onChange={(e) => setCustomPrompt(e.target.value)}\n                    placeholder=\"输入自定义提示词来调整角色形象生成...\"\n                    className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none\"\n                    rows={3}\n                  />\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={() => setShowPromptInput(false)}\n                      className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                    >\n                      取消\n                    </button>\n                    <button\n                      onClick={handleRegenerateWithPrompt}\n                      className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700\"\n                    >\n                      <RefreshCw className=\"mr-2\" size={16} />\n                      重新生成\n                    </button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={() => setShowPromptInput(true)}\n                      className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                    >\n                      <RefreshCw className=\"mr-2\" size={16} />\n                      修改提示词\n                    </button>\n                  </div>\n\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={onClose}\n                      className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                    >\n                      取消\n                    </button>\n                    <button\n                      onClick={handleSave}\n                      className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700\"\n                    >\n                      <Save className=\"mr-2\" size={16} />\n                      保存三视图\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAoCe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,YAAY,EACW;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB,IAAI,MAAM;YACR,OAAO;YACP;QACF;IACF;IAEA,MAAM,6BAA6B;QACjC,aAAa;QACb,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAuB,MAAM;;;;;;sDACjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKb,8OAAC;4BAAI,WAAU;sCACZ,eACC,SAAS,iBACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;uCAIrC,OACF,UAAU,iBACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAMvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDAAyC;oDAAI,KAAK,SAAS,CAAC,IAAI;;;;;;;0DAC9E,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,SAAS,CAAC,WAAW;;;;;;;;;;;;kDAIlE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAAgD;;;;;;;kEAGjE,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK,CAAC,KAAK,iBACf,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAoB;;;;;;kFACnC,8OAAC;kFAAK,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;iFAI1B,8OAAC;4DACC,KAAK,KAAK,KAAK,CAAC,GAAG;4DACnB,KAAI;4DACJ,WAAU;4DACV,SAAS,CAAC;gEACR,MAAM,SAAS,EAAE,MAAM;gEACvB,OAAO,GAAG,GAAG;4DACf;;;;;;;;;;;;;;;;;0DAOR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,KAAK,iBACd,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAoB;;;;;;kFACnC,8OAAC;kFAAK,KAAK,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;iFAIzB,8OAAC;4DACC,KAAK,KAAK,IAAI,CAAC,GAAG;4DAClB,KAAI;4DACJ,WAAU;4DACV,SAAS,CAAC;gEACR,MAAM,SAAS,EAAE,MAAM;gEACvB,OAAO,GAAG,GAAG;4DACf;;;;;;;;;;;;;;;;;0DAOR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA+C;;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,KAAK,iBACd,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAoB;;;;;;kFACnC,8OAAC;kFAAK,KAAK,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;iFAIzB,8OAAC;4DACC,KAAK,KAAK,IAAI,CAAC,GAAG;4DAClB,KAAI;4DACJ,WAAU;4DACV,SAAS,CAAC;gEACR,MAAM,SAAS,EAAE,MAAM;gEACvB,OAAO,GAAG,GAAG;4DACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAQZ,QAAQ,iBACR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCACC,SAAS,IAAM;wCACf,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;wBAQ/C,CAAC,gBAAgB,sBAChB,8OAAC;4BAAI,WAAU;sCAEZ,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA0C;;;;;;kDAG3D,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,aAAY;wCACZ,WAAU;wCACV,MAAM;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;;;;;;;;;;;;qDAM9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD", "debugId": null}}, {"offset": {"line": 2080, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/PromptPreview.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Eye, Copy, Edit, Wand2, RefreshCw, X } from 'lucide-react'\nimport { generateCharacterPrompt, generateNegativePrompt, generateConsistencyPrompt } from '@/utils/promptGenerator'\n\ninterface Character {\n  id: string\n  name: string\n  identity?: string\n  personality?: string\n  physique?: string\n  facial?: string\n  hairstyle?: string\n  clothing?: string\n}\n\ninterface PromptPreviewProps {\n  character: Character\n  customPrompt?: string\n  onPromptChange?: (prompt: string) => void\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function PromptPreview({ \n  character, \n  customPrompt = '', \n  onPromptChange,\n  isOpen,\n  onClose \n}: PromptPreviewProps) {\n  const [artStyle, setArtStyle] = useState<'anime' | 'realistic' | 'semi-realistic' | 'concept-art'>('anime')\n  const [quality, setQuality] = useState<'standard' | 'high' | 'masterpiece'>('masterpiece')\n  const [background, setBackground] = useState<'white' | 'transparent' | 'simple'>('white')\n  const [activeTab, setActiveTab] = useState<'front' | 'side' | 'back'>('front')\n  const [generatedPrompts, setGeneratedPrompts] = useState<{\n    front: string\n    side: string\n    back: string\n  }>({ front: '', side: '', back: '' })\n  const [negativePrompt, setNegativePrompt] = useState('')\n\n  // 生成提示词\n  useEffect(() => {\n    if (character) {\n      const basePrompt = generateCharacterPrompt(character, {\n        artStyle,\n        quality,\n        background,\n        customEnhancement: customPrompt\n      })\n\n      const consistencyPrompts = generateConsistencyPrompt(character, basePrompt)\n      setGeneratedPrompts(consistencyPrompts)\n      setNegativePrompt(generateNegativePrompt())\n    }\n  }, [character, artStyle, quality, background, customPrompt])\n\n  // 复制到剪贴板\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text)\n    // 可以添加提示消息\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <Eye className=\"text-purple-600 mr-2\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              提示词预览 - {character.name}\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors\"\n            title=\"关闭\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        <div className=\"flex h-[calc(90vh-80px)]\">\n          {/* 左侧配置面板 */}\n          <div className=\"w-1/3 p-6 border-r border-gray-200 overflow-y-auto\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">生成配置</h3>\n            \n            {/* 艺术风格 */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                艺术风格\n              </label>\n              <select\n                value={artStyle}\n                onChange={(e) => setArtStyle(e.target.value as any)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              >\n                <option value=\"anime\">动漫风格</option>\n                <option value=\"realistic\">写实风格</option>\n                <option value=\"semi-realistic\">半写实风格</option>\n                <option value=\"concept-art\">概念设计</option>\n              </select>\n            </div>\n\n            {/* 质量等级 */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                质量等级\n              </label>\n              <select\n                value={quality}\n                onChange={(e) => setQuality(e.target.value as any)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              >\n                <option value=\"standard\">标准质量</option>\n                <option value=\"high\">高质量</option>\n                <option value=\"masterpiece\">大师级</option>\n              </select>\n            </div>\n\n            {/* 背景设置 */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                背景设置\n              </label>\n              <select\n                value={background}\n                onChange={(e) => setBackground(e.target.value as any)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              >\n                <option value=\"white\">白色背景</option>\n                <option value=\"transparent\">透明背景</option>\n                <option value=\"simple\">简单背景</option>\n              </select>\n            </div>\n\n            {/* 角色信息预览 */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-900 mb-2\">角色信息</h4>\n              <div className=\"space-y-2 text-sm\">\n                {character.identity && (\n                  <div>\n                    <span className=\"font-medium text-gray-600\">身份：</span>\n                    <span className=\"text-gray-800\">{character.identity.substring(0, 50)}...</span>\n                  </div>\n                )}\n                {character.facial && (\n                  <div>\n                    <span className=\"font-medium text-gray-600\">五官：</span>\n                    <span className=\"text-gray-800\">{character.facial.substring(0, 50)}...</span>\n                  </div>\n                )}\n                {character.hairstyle && (\n                  <div>\n                    <span className=\"font-medium text-gray-600\">发型：</span>\n                    <span className=\"text-gray-800\">{character.hairstyle.substring(0, 50)}...</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* 右侧提示词预览 */}\n          <div className=\"flex-1 p-6 overflow-y-auto\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">生成的提示词</h3>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => copyToClipboard(generatedPrompts[activeTab])}\n                  className=\"inline-flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200\"\n                >\n                  <Copy size={14} className=\"mr-1\" />\n                  复制\n                </button>\n              </div>\n            </div>\n\n            {/* 视图切换标签 */}\n            <div className=\"flex space-x-1 mb-4\">\n              {(['front', 'side', 'back'] as const).map((view) => (\n                <button\n                  key={view}\n                  onClick={() => setActiveTab(view)}\n                  className={`px-4 py-2 text-sm font-medium rounded-md ${\n                    activeTab === view\n                      ? 'bg-purple-100 text-purple-700'\n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {view === 'front' ? '正面' : view === 'side' ? '侧面' : '背面'}\n                </button>\n              ))}\n            </div>\n\n            {/* 正面提示词 */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                正面提示词 (Positive Prompt)\n              </label>\n              <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4\">\n                <pre className=\"text-sm text-gray-800 whitespace-pre-wrap font-mono\">\n                  {generatedPrompts[activeTab]}\n                </pre>\n              </div>\n            </div>\n\n            {/* 负面提示词 */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                负面提示词 (Negative Prompt)\n              </label>\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n                <pre className=\"text-sm text-red-800 whitespace-pre-wrap font-mono\">\n                  {negativePrompt}\n                </pre>\n              </div>\n            </div>\n\n            {/* 提示词分析 */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-900 mb-2\">提示词分析</h4>\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"font-medium text-blue-900\">总长度：</span>\n                    <span className=\"text-blue-800\">{generatedPrompts[activeTab].length} 字符</span>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-blue-900\">关键词数：</span>\n                    <span className=\"text-blue-800\">{generatedPrompts[activeTab].split(',').length} 个</span>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-blue-900\">艺术风格：</span>\n                    <span className=\"text-blue-800\">{artStyle}</span>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-blue-900\">质量等级：</span>\n                    <span className=\"text-blue-800\">{quality}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 优化建议 */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-900 mb-2\">优化建议</h4>\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4\">\n                <ul className=\"text-sm text-yellow-800 space-y-1\">\n                  <li>• 确保角色特征描述足够详细</li>\n                  <li>• 三视图使用相同的核心特征保持一致性</li>\n                  <li>• 可以在增强提示词中添加特定要求</li>\n                  <li>• 建议使用高质量或大师级设置获得最佳效果</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 底部操作按钮 */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200\">\n          <div className=\"text-sm text-gray-500\">\n            提示词已根据角色信息自动生成并优化\n          </div>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200\"\n            >\n              关闭\n            </button>\n            <button\n              onClick={() => {\n                if (onPromptChange) {\n                  onPromptChange(generatedPrompts[activeTab])\n                }\n                onClose()\n              }}\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700\"\n            >\n              使用此提示词\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAyBe,SAAS,cAAc,EACpC,SAAS,EACT,eAAe,EAAE,EACjB,cAAc,EACd,MAAM,EACN,OAAO,EACY;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4D;IACnG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIpD;QAAE,OAAO;QAAI,MAAM;QAAI,MAAM;IAAG;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;gBACpD;gBACA;gBACA;gBACA,mBAAmB;YACrB;YAEA,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;YAChE,oBAAoB;YACpB,kBAAkB,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD;QACzC;IACF,GAAG;QAAC;QAAW;QAAU;QAAS;QAAY;KAAa;IAE3D,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,WAAW;IACb;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;oCAAuB,MAAM;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;wCACzC,UAAU,IAAI;;;;;;;;;;;;;sCAG3B,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAO,OAAM;8DAAc;;;;;;;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;;;;;;;;;;;;;8CAK3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;gDACZ,UAAU,QAAQ,kBACjB,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;;gEAAiB,UAAU,QAAQ,CAAC,SAAS,CAAC,GAAG;gEAAI;;;;;;;;;;;;;gDAGxE,UAAU,MAAM,kBACf,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;;gEAAiB,UAAU,MAAM,CAAC,SAAS,CAAC,GAAG;gEAAI;;;;;;;;;;;;;gDAGtE,UAAU,SAAS,kBAClB,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,8OAAC;4DAAK,WAAU;;gEAAiB,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG;gEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS,IAAM,gBAAgB,gBAAgB,CAAC,UAAU;gDAC1D,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;8CAOzC,8OAAC;oCAAI,WAAU;8CACZ,AAAC;wCAAC;wCAAS;wCAAQ;qCAAO,CAAW,GAAG,CAAC,CAAC,qBACzC,8OAAC;4CAEC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,OACV,kCACA,qCACJ;sDAED,SAAS,UAAU,OAAO,SAAS,SAAS,OAAO;2CAR/C;;;;;;;;;;8CAcX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,CAAC,UAAU;;;;;;;;;;;;;;;;;8CAMlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,8OAAC;gEAAK,WAAU;;oEAAiB,gBAAgB,CAAC,UAAU,CAAC,MAAM;oEAAC;;;;;;;;;;;;;kEAEtE,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,8OAAC;gEAAK,WAAU;;oEAAiB,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,MAAM;oEAAC;;;;;;;;;;;;;kEAEjF,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;;;;;;kEAEnC,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP,IAAI,gBAAgB;4CAClB,eAAe,gBAAgB,CAAC,UAAU;wCAC5C;wCACA;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2885, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/CharacterManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Users, User } from 'lucide-react'\nimport { Character } from '@/types'\nimport CharacterDetail from './CharacterDetail'\n\ninterface CharacterManagerProps {\n  projectId: string\n  characters: Character[]\n  onCharactersUpdate?: () => void\n  onCharacterUpdate?: (character: Character) => void\n}\n\nexport default function CharacterManager({ \n  projectId, \n  characters, \n  onCharactersUpdate, \n  onCharacterUpdate \n}: CharacterManagerProps) {\n  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)\n  const [isDetailOpen, setIsDetailOpen] = useState(false)\n\n  const openCharacterDetail = (character: Character) => {\n    setSelectedCharacter(character)\n    setIsDetailOpen(true)\n  }\n\n  const closeCharacterDetail = () => {\n    setIsDetailOpen(false)\n    setSelectedCharacter(null)\n  }\n\n  const handleCharacterUpdate = (updatedCharacter: Character) => {\n    onCharacterUpdate?.(updatedCharacter)\n    onCharactersUpdate?.()\n    setSelectedCharacter(updatedCharacter)\n  }\n\n  if (characters.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Users className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无角色信息</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          上传小说文件后，AI将自动提取角色信息\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Users className=\"text-purple-600 mr-2\" size={20} />\n            <h3 className=\"text-lg font-medium text-gray-900\">角色信息</h3>\n          </div>\n          <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n            共 {characters.length} 个角色\n          </span>\n        </div>\n        <p className=\"mt-2 text-sm text-gray-600\">\n          AI自动提取的角色信息，包含五官、身份、外貌、性格、隐线等维度\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {characters.map((character) => (\n          <div\n            key={character.id}\n            className=\"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer\"\n            onClick={() => openCharacterDetail(character)}\n          >\n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"w-16 h-16 rounded-full mb-4 shadow-lg overflow-hidden\">\n                <div className=\"w-full h-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center\">\n                  <User className=\"text-white\" size={28} />\n                </div>\n              </div>\n\n              <h4 className=\"text-lg font-medium text-gray-900 mb-2 hover:text-purple-600 transition-colors\">\n                {character.name}\n              </h4>\n\n              {character.identity && (\n                <span className=\"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mb-3\">\n                  {character.identity}\n                </span>\n              )}\n\n              <div className=\"text-xs text-gray-500 space-y-1\">\n                {character.personality && (\n                  <p className=\"overflow-hidden line-clamp-2\">\n                    {character.personality}\n                  </p>\n                )}\n                \n                <p className=\"text-gray-400\">\n                  点击查看详细信息\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {selectedCharacter && (\n        <CharacterDetail\n          character={selectedCharacter}\n          isOpen={isDetailOpen}\n          onClose={closeCharacterDetail}\n          onUpdate={handleCharacterUpdate}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AALA;;;;;AAce,SAAS,iBAAiB,EACvC,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,iBAAiB,EACK;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB,CAAC;QAC3B,qBAAqB;QACrB,gBAAgB;IAClB;IAEA,MAAM,uBAAuB;QAC3B,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB;QACA,qBAAqB;IACvB;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAKhD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAuB,MAAM;;;;;;kDAC9C,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAK,WAAU;;oCAA+E;oCAC1F,WAAW,MAAM;oCAAC;;;;;;;;;;;;;kCAGzB,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,oBAAoB;kCAEnC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;4CAAa,MAAM;;;;;;;;;;;;;;;;8CAIvC,8OAAC;oCAAG,WAAU;8CACX,UAAU,IAAI;;;;;;gCAGhB,UAAU,QAAQ,kBACjB,8OAAC;oCAAK,WAAU;8CACb,UAAU,QAAQ;;;;;;8CAIvB,8OAAC;oCAAI,WAAU;;wCACZ,UAAU,WAAW,kBACpB,8OAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;sDAI1B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;uBA5B5B,UAAU,EAAE;;;;;;;;;;YAqCtB,mCACC,8OAAC,qIAAA,CAAA,UAAe;gBACd,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,UAAU;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/PlotAnalysisModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Users, MapPin, Zap, ChevronRight, Clock, Eye, Target } from 'lucide-react'\n\ninterface PlotAnalysisModalProps {\n  episodeId: string\n  episodeTitle: string\n  isOpen: boolean\n  onClose: () => void\n  plotInfo?: any\n}\n\ninterface PlotInfo {\n  characters: string[]\n  scenes: Array<{\n    location: string\n    description: string\n    atmosphere: string\n    time: string\n    significance: string\n  }>\n  events: Array<{\n    normal: string\n    conflict: string\n    escalation: string\n    participants: string[]\n    location: string\n    actions: string[]\n    significance: string\n  }>\n}\n\nexport default function PlotAnalysisModal({ \n  episodeId, \n  episodeTitle, \n  isOpen, \n  onClose,\n  plotInfo: initialPlotInfo \n}: PlotAnalysisModalProps) {\n  const [plotInfo, setPlotInfo] = useState<PlotInfo | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'characters' | 'scenes' | 'events'>('characters')\n\n  useEffect(() => {\n    if (isOpen && initialPlotInfo) {\n      try {\n        // 解析传入的剧情信息\n        const parsedInfo: PlotInfo = {\n          characters: typeof initialPlotInfo.characters === 'string' \n            ? JSON.parse(initialPlotInfo.characters) \n            : initialPlotInfo.characters || [],\n          scenes: typeof initialPlotInfo.scenes === 'string' \n            ? JSON.parse(initialPlotInfo.scenes) \n            : initialPlotInfo.scenes || [],\n          events: typeof initialPlotInfo.events === 'string' \n            ? JSON.parse(initialPlotInfo.events) \n            : initialPlotInfo.events || []\n        }\n        setPlotInfo(parsedInfo)\n      } catch (error) {\n        console.error('解析剧情信息失败:', error)\n        setError('剧情信息格式错误')\n      }\n    }\n  }, [isOpen, initialPlotInfo])\n\n  if (!isOpen) return null\n\n  const tabs = [\n    {\n      id: 'characters' as const,\n      name: '本集人物',\n      icon: Users,\n      description: '当前剧集中出场的所有角色'\n    },\n    {\n      id: 'scenes' as const,\n      name: '场景信息',\n      icon: MapPin,\n      description: '故事发生的地点、环境描述、氛围设定'\n    },\n    {\n      id: 'events' as const,\n      name: '事件三要素',\n      icon: Zap,\n      description: '正常状态 → 矛盾冲突 → 升级事件的结构分析'\n    }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b bg-gradient-to-r from-purple-500 to-blue-500 text-white\">\n          <div>\n            <h2 className=\"text-xl font-semibold flex items-center\">\n              <Zap className=\"mr-2\" size={24} />\n              {episodeTitle} - 剧情信息分析\n            </h2>\n            <p className=\"text-purple-100 text-sm mt-1\">\n              基于DeepSeek Reasoner的专业剧情分析结果\n            </p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* 标签页导航 */}\n        <div className=\"border-b border-gray-200 bg-gray-50\">\n          <nav className=\"flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const isActive = activeTab === tab.id\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    isActive\n                      ? 'border-purple-500 text-purple-600 bg-white'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <tab.icon className=\"inline mr-2\" size={16} />\n                  {tab.name}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 内容区域 */}\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-200px)]\">\n          {error ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-red-500 mb-2\">\n                <X size={48} className=\"mx-auto\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">解析失败</h3>\n              <p className=\"text-gray-500\">{error}</p>\n            </div>\n          ) : !plotInfo ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 mb-2\">\n                <Eye size={48} className=\"mx-auto\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">暂无剧情信息</h3>\n              <p className=\"text-gray-500\">请先提取剧情信息</p>\n            </div>\n          ) : (\n            <>\n              {/* 本集人物 */}\n              {activeTab === 'characters' && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-blue-50 rounded-lg p-4\">\n                    <h3 className=\"font-semibold text-blue-800 mb-2 flex items-center\">\n                      <Users className=\"mr-2\" size={20} />\n                      角色识别结果\n                    </h3>\n                    <p className=\"text-blue-700 text-sm\">\n                      AI识别出本集中出场的 <strong>{plotInfo.characters.length}</strong> 个角色\n                    </p>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    {plotInfo.characters.map((character, index) => (\n                      <div key={index} className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3\">\n                            <Users className=\"text-purple-600\" size={20} />\n                          </div>\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">{character}</h4>\n                            <p className=\"text-xs text-gray-500\">第 {index + 1} 个角色</p>\n                          </div>\n                        </div>\n                        <div className=\"text-sm text-gray-600\">\n                          在本集中出场的角色，参与了重要剧情发展\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* 场景信息 */}\n              {activeTab === 'scenes' && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-green-50 rounded-lg p-4\">\n                    <h3 className=\"font-semibold text-green-800 mb-2 flex items-center\">\n                      <MapPin className=\"mr-2\" size={20} />\n                      场景分析结果\n                    </h3>\n                    <p className=\"text-green-700 text-sm\">\n                      AI分析出本集涉及 <strong>{plotInfo.scenes.length}</strong> 个主要场景\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    {plotInfo.scenes.map((scene, index) => (\n                      <div key={index} className=\"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                        <div className=\"flex items-start justify-between mb-4\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4\">\n                              <MapPin className=\"text-green-600\" size={24} />\n                            </div>\n                            <div>\n                              <h4 className=\"text-lg font-semibold text-gray-900\">{scene.location}</h4>\n                              <div className=\"flex items-center text-sm text-gray-500 mt-1\">\n                                <Clock className=\"mr-1\" size={14} />\n                                {scene.time}\n                              </div>\n                            </div>\n                          </div>\n                          <span className=\"bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full\">\n                            场景 {index + 1}\n                          </span>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                          <div>\n                            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">环境描述</h5>\n                            <p className=\"text-sm text-gray-600 bg-gray-50 p-3 rounded\">\n                              {scene.description}\n                            </p>\n                          </div>\n                          <div>\n                            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">氛围设定</h5>\n                            <p className=\"text-sm text-gray-600 bg-purple-50 p-3 rounded\">\n                              {scene.atmosphere}\n                            </p>\n                          </div>\n                        </div>\n\n                        <div className=\"mt-4\">\n                          <h5 className=\"text-sm font-medium text-gray-700 mb-2\">剧情意义</h5>\n                          <p className=\"text-sm text-gray-600 bg-blue-50 p-3 rounded\">\n                            {scene.significance}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* 事件三要素 */}\n              {activeTab === 'events' && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-orange-50 rounded-lg p-4\">\n                    <h3 className=\"font-semibold text-orange-800 mb-2 flex items-center\">\n                      <Zap className=\"mr-2\" size={20} />\n                      事件结构分析\n                    </h3>\n                    <p className=\"text-orange-700 text-sm\">\n                      基于\"正常状态 → 矛盾冲突 → 升级事件\"的三要素结构分析\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-6\">\n                    {plotInfo.events.map((event, index) => (\n                      <div key={index} className=\"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                        <div className=\"flex items-center justify-between mb-6\">\n                          <h4 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                            <Target className=\"mr-2 text-orange-600\" size={20} />\n                            事件 {index + 1}\n                          </h4>\n                        </div>\n\n                        {/* 三要素结构 */}\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n                          <div className=\"bg-blue-50 p-4 rounded-lg\">\n                            <h5 className=\"text-sm font-semibold text-blue-800 mb-2 flex items-center\">\n                              <div className=\"w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold\">1</div>\n                              正常状态\n                            </h5>\n                            <p className=\"text-sm text-blue-700\">{event.normal}</p>\n                          </div>\n                          \n                          <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                            <h5 className=\"text-sm font-semibold text-yellow-800 mb-2 flex items-center\">\n                              <div className=\"w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold\">2</div>\n                              矛盾冲突\n                            </h5>\n                            <p className=\"text-sm text-yellow-700\">{event.conflict}</p>\n                          </div>\n                          \n                          <div className=\"bg-red-50 p-4 rounded-lg\">\n                            <h5 className=\"text-sm font-semibold text-red-800 mb-2 flex items-center\">\n                              <div className=\"w-6 h-6 bg-red-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold\">3</div>\n                              升级事件\n                            </h5>\n                            <p className=\"text-sm text-red-700\">{event.escalation}</p>\n                          </div>\n                        </div>\n\n                        {/* 详细信息 */}\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200\">\n                          <div>\n                            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">参与人物</h5>\n                            <div className=\"flex flex-wrap gap-1\">\n                              {event.participants.map((participant, pIndex) => (\n                                <span key={pIndex} className=\"bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded\">\n                                  {participant}\n                                </span>\n                              ))}\n                            </div>\n                          </div>\n                          \n                          <div>\n                            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">发生地点</h5>\n                            <p className=\"text-sm text-gray-600 bg-gray-50 p-2 rounded\">{event.location}</p>\n                          </div>\n                          \n                          <div>\n                            <h5 className=\"text-sm font-medium text-gray-700 mb-2\">具体行为</h5>\n                            <div className=\"space-y-1\">\n                              {event.actions.map((action, aIndex) => (\n                                <div key={aIndex} className=\"text-xs text-gray-600 bg-gray-50 p-1 rounded flex items-center\">\n                                  <ChevronRight size={12} className=\"mr-1 text-gray-400\" />\n                                  {action}\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* 事件意义 */}\n                        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                          <h5 className=\"text-sm font-medium text-gray-700 mb-2\">事件重要性和影响</h5>\n                          <p className=\"text-sm text-gray-600 bg-indigo-50 p-3 rounded\">\n                            {event.significance}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n\n        {/* 底部说明 */}\n        <div className=\"border-t border-gray-200 bg-gray-50 px-6 py-4\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            <p>本分析结果由 DeepSeek Reasoner 模型生成，基于专业的剧情分析算法</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiCe,SAAS,kBAAkB,EACxC,SAAS,EACT,YAAY,EACZ,MAAM,EACN,OAAO,EACP,UAAU,eAAe,EACF;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAE/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,iBAAiB;YAC7B,IAAI;gBACF,YAAY;gBACZ,MAAM,aAAuB;oBAC3B,YAAY,OAAO,gBAAgB,UAAU,KAAK,WAC9C,KAAK,KAAK,CAAC,gBAAgB,UAAU,IACrC,gBAAgB,UAAU,IAAI,EAAE;oBACpC,QAAQ,OAAO,gBAAgB,MAAM,KAAK,WACtC,KAAK,KAAK,CAAC,gBAAgB,MAAM,IACjC,gBAAgB,MAAM,IAAI,EAAE;oBAChC,QAAQ,OAAO,gBAAgB,MAAM,KAAK,WACtC,KAAK,KAAK,CAAC,gBAAgB,MAAM,IACjC,gBAAgB,MAAM,IAAI,EAAE;gBAClC;gBACA,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAQ;KAAgB;IAE5B,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,0MAAA,CAAA,SAAM;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,gMAAA,CAAA,MAAG;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAC3B;wCAAa;;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CAA+B;;;;;;;;;;;;sCAI9C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,WAAW,cAAc,IAAI,EAAE;4BACrC,qBACE,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,2DAA2D,EACrE,WACI,+CACA,8EACJ;;kDAEF,8OAAC,IAAI,IAAI;wCAAC,WAAU;wCAAc,MAAM;;;;;;oCACvC,IAAI,IAAI;;+BATJ,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACZ,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;+BAE9B,CAAC,yBACH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE3B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B;;4BAEG,cAAc,8BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;0DAGtC,8OAAC;gDAAE,WAAU;;oDAAwB;kEACvB,8OAAC;kEAAQ,SAAS,UAAU,CAAC,MAAM;;;;;;oDAAU;;;;;;;;;;;;;kDAI7D,8OAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;oEAAkB,MAAM;;;;;;;;;;;0EAE3C,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAG,QAAQ;4EAAE;;;;;;;;;;;;;;;;;;;kEAGtD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;+CAV/B;;;;;;;;;;;;;;;;4BAoBjB,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;0DAGvC,8OAAC;gDAAE,WAAU;;oDAAyB;kEAC1B,8OAAC;kEAAQ,SAAS,MAAM,CAAC,MAAM;;;;;;oDAAU;;;;;;;;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;4EAAiB,MAAM;;;;;;;;;;;kFAE3C,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAuC,MAAM,QAAQ;;;;;;0FACnE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;wFAAO,MAAM;;;;;;oFAC7B,MAAM,IAAI;;;;;;;;;;;;;;;;;;;0EAIjB,8OAAC;gEAAK,WAAU;;oEAA6E;oEACvF,QAAQ;;;;;;;;;;;;;kEAIhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,8OAAC;wEAAE,WAAU;kFACV,MAAM,WAAW;;;;;;;;;;;;0EAGtB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,8OAAC;wEAAE,WAAU;kFACV,MAAM,UAAU;;;;;;;;;;;;;;;;;;kEAKvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EACV,MAAM,YAAY;;;;;;;;;;;;;+CArCf;;;;;;;;;;;;;;;;4BA+CjB,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;0DAGpC,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAKzC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;oEAAuB,MAAM;;;;;;gEAAM;gEACjD,QAAQ;;;;;;;;;;;;kEAKhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAI,WAAU;0FAA2F;;;;;;4EAAO;;;;;;;kFAGnH,8OAAC;wEAAE,WAAU;kFAAyB,MAAM,MAAM;;;;;;;;;;;;0EAGpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAI,WAAU;0FAA6F;;;;;;4EAAO;;;;;;;kFAGrH,8OAAC;wEAAE,WAAU;kFAA2B,MAAM,QAAQ;;;;;;;;;;;;0EAGxD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAI,WAAU;0FAA0F;;;;;;4EAAO;;;;;;;kFAGlH,8OAAC;wEAAE,WAAU;kFAAwB,MAAM,UAAU;;;;;;;;;;;;;;;;;;kEAKzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,8OAAC;wEAAI,WAAU;kFACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,uBACpC,8OAAC;gFAAkB,WAAU;0FAC1B;+EADQ;;;;;;;;;;;;;;;;0EAOjB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,8OAAC;wEAAE,WAAU;kFAAgD,MAAM,QAAQ;;;;;;;;;;;;0EAG7E,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,8OAAC;wEAAI,WAAU;kFACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,uBAC1B,8OAAC;gFAAiB,WAAU;;kGAC1B,8OAAC,sNAAA,CAAA,eAAY;wFAAC,MAAM;wFAAI,WAAU;;;;;;oFACjC;;+EAFO;;;;;;;;;;;;;;;;;;;;;;kEAUlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;0EACV,MAAM,YAAY;;;;;;;;;;;;;+CAtEf;;;;;;;;;;;;;;;;;;;;;;;8BAmFxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 4104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/EpisodeManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Film, Play, Eye, EyeOff, Clock, CheckCircle, Zap, Search } from 'lucide-react'\nimport { Episode } from '@/types'\nimport PlotAnalysisModal from './PlotAnalysisModal'\n\ninterface EpisodeManagerProps {\n  episodes: Episode[]\n  onAnalyzePlot?: (episodeId: string) => void\n  onGenerateVideo?: (episodeId: string) => void\n  onExtractDetailedPlot?: (episodeId: string) => void\n}\n\nexport default function EpisodeManager({\n  episodes,\n  onAnalyzePlot,\n  onGenerateVideo,\n  onExtractDetailedPlot\n}: EpisodeManagerProps) {\n  const [expandedEpisode, setExpandedEpisode] = useState<string | null>(null)\n  const [analyzingEpisode, setAnalyzingEpisode] = useState<string | null>(null)\n  const [plotModalOpen, setPlotModalOpen] = useState(false)\n  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null)\n\n  // 切换剧集详情展开/收起\n  const toggleEpisodeExpansion = (episodeId: string) => {\n    setExpandedEpisode(expandedEpisode === episodeId ? null : episodeId)\n  }\n\n  // 分析剧情\n  const handleAnalyzePlot = async (episodeId: string) => {\n    setAnalyzingEpisode(episodeId)\n    try {\n      await onAnalyzePlot?.(episodeId)\n    } finally {\n      setAnalyzingEpisode(null)\n    }\n  }\n\n  // 打开剧情信息模态框\n  const handleViewPlotInfo = (episode: Episode) => {\n    setSelectedEpisode(episode)\n    setPlotModalOpen(true)\n  }\n\n  // 关闭剧情信息模态框\n  const handleClosePlotModal = () => {\n    setPlotModalOpen(false)\n    setSelectedEpisode(null)\n  }\n\n  // 获取剧集状态显示\n  const getEpisodeStatusDisplay = (episode: Episode) => {\n    if (episode.plotInfo) {\n      return {\n        text: '已分析',\n        color: 'bg-green-100 text-green-800',\n        icon: CheckCircle\n      }\n    }\n    return {\n      text: '未分析',\n      color: 'bg-gray-100 text-gray-800',\n      icon: Clock\n    }\n  }\n\n  if (episodes.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <Film className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无剧集信息</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          上传小说文件后，AI将自动按章节拆分剧集\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 剧集统计 */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <Film className=\"text-purple-600 mr-2\" size={20} />\n            <h3 className=\"text-lg font-medium text-gray-900\">剧集管理</h3>\n          </div>\n          <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n            共 {episodes.length} 集\n          </span>\n        </div>\n        <p className=\"mt-1 text-sm text-gray-600\">\n          AI按章节自动拆分的剧集，可以提取剧情信息用于视频生成\n        </p>\n      </div>\n\n      {/* 剧集列表 */}\n      <div className=\"space-y-4\">\n        {episodes.map((episode, index) => {\n          const isExpanded = expandedEpisode === episode.id\n          const isAnalyzing = analyzingEpisode === episode.id\n          const statusDisplay = getEpisodeStatusDisplay(episode)\n\n          return (\n            <div key={episode.id} className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n              {/* 剧集头部 */}\n              <div className=\"p-4 border-b border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded mr-3\">\n                      第{index + 1}集\n                    </span>\n                    <h4 className=\"text-lg font-medium text-gray-900\">{episode.title}</h4>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.color}`}>\n                      <statusDisplay.icon className=\"mr-1\" size={12} />\n                      {statusDisplay.text}\n                    </span>\n                    <button\n                      onClick={() => toggleEpisodeExpansion(episode.id)}\n                      className=\"p-1 text-gray-400 hover:text-gray-600\"\n                      title={isExpanded ? \"收起详情\" : \"展开详情\"}\n                    >\n                      {isExpanded ? <EyeOff size={16} /> : <Eye size={16} />}\n                    </button>\n                  </div>\n                </div>\n\n                {/* 剧集简介 */}\n                <p className=\"mt-2 text-sm text-gray-600 line-clamp-2\">\n                  {episode.content.substring(0, 200)}...\n                </p>\n\n                {/* 操作按钮 */}\n                <div className=\"mt-3 flex items-center space-x-3\">\n                  {!episode.plotInfo ? (\n                    <button\n                      onClick={() => handleAnalyzePlot(episode.id)}\n                      disabled={isAnalyzing}\n                      className=\"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {isAnalyzing ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600 mr-1\"></div>\n                          分析中...\n                        </>\n                      ) : (\n                        <>\n                          <Zap className=\"mr-1\" size={12} />\n                          提取剧情信息\n                        </>\n                      )}\n                    </button>\n                  ) : (\n                    <>\n                      <button\n                        onClick={() => handleViewPlotInfo(episode)}\n                        className=\"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200\"\n                      >\n                        <Eye className=\"mr-1\" size={12} />\n                        查看剧情信息\n                      </button>\n                      <button\n                        onClick={() => onGenerateVideo?.(episode.id)}\n                        className=\"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200\"\n                      >\n                        <Play className=\"mr-1\" size={12} />\n                        生成视频脚本\n                      </button>\n                      <button\n                        onClick={() => onExtractDetailedPlot?.(episode.id)}\n                        className=\"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200\"\n                      >\n                        <Search className=\"mr-1\" size={12} />\n                        提取具体剧情信息\n                      </button>\n                    </>\n                  )}\n                </div>\n              </div>\n\n              {/* 剧集详情 */}\n              {isExpanded && (\n                <div className=\"p-4 bg-gray-50\">\n                  {/* 剧集内容 */}\n                  <div className=\"mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-700 mb-2\">详细剧情</h5>\n                    <div className=\"text-sm text-gray-600 max-h-64 overflow-y-auto bg-white p-3 rounded border\">\n                      {episode.content}\n                    </div>\n                  </div>\n\n                  {/* 剧情信息简要显示 */}\n                  {episode.plotInfo && (\n                    <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                          <span className=\"text-sm font-medium text-green-800\">剧情信息已提取</span>\n                        </div>\n                        <button\n                          onClick={() => handleViewPlotInfo(episode)}\n                          className=\"text-xs text-green-700 hover:text-green-900 underline\"\n                        >\n                          查看详情\n                        </button>\n                      </div>\n                      <p className=\"text-xs text-green-700 mt-1\">\n                        包含人物、场景、事件三要素等详细分析结果\n                      </p>\n                    </div>\n                  )}\n\n                  {/* 创建时间 */}\n                  <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                    <p className=\"text-xs text-gray-500\">\n                      创建时间：{new Date(episode.createdAt).toLocaleString('zh-CN')}\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          )\n        })}\n      </div>\n\n      {/* 剧集分析说明 */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n          剧情分析说明\n        </h4>\n        <div className=\"text-sm text-blue-700 space-y-1\">\n          <p><strong>本集人物：</strong>该集中出现的主要角色</p>\n          <p><strong>场景信息：</strong>故事发生的地点和环境描述</p>\n          <p><strong>事件三要素：</strong></p>\n          <ul className=\"list-disc list-inside ml-4 space-y-1\">\n            <li><strong>正常状态：</strong>故事的初始状态或平衡点</li>\n            <li><strong>矛盾冲突：</strong>打破平衡的问题或冲突</li>\n            <li><strong>升级事件：</strong>推动故事发展的关键事件</li>\n          </ul>\n        </div>\n      </div>\n\n      {/* 剧情信息模态框 */}\n      {selectedEpisode && (\n        <PlotAnalysisModal\n          episodeId={selectedEpisode.id}\n          episodeTitle={selectedEpisode.title}\n          isOpen={plotModalOpen}\n          onClose={handleClosePlotModal}\n          plotInfo={selectedEpisode.plotInfo}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAce,SAAS,eAAe,EACrC,QAAQ,EACR,aAAa,EACb,eAAe,EACf,qBAAqB,EACD;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,cAAc;IACd,MAAM,yBAAyB,CAAC;QAC9B,mBAAmB,oBAAoB,YAAY,OAAO;IAC5D;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,oBAAoB;QACpB,IAAI;YACF,MAAM,gBAAgB;QACxB,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,YAAY;IACZ,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,YAAY;IACZ,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,WAAW;IACX,MAAM,0BAA0B,CAAC;QAC/B,IAAI,QAAQ,QAAQ,EAAE;YACpB,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,MAAM,2NAAA,CAAA,cAAW;YACnB;QACF;QACA,OAAO;YACL,MAAM;YACN,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;QACb;IACF;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAKhD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAuB,MAAM;;;;;;kDAC7C,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAK,WAAU;;oCAA+E;oCAC1F,SAAS,MAAM;oCAAC;;;;;;;;;;;;;kCAGvB,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oBACtB,MAAM,aAAa,oBAAoB,QAAQ,EAAE;oBACjD,MAAM,cAAc,qBAAqB,QAAQ,EAAE;oBACnD,MAAM,gBAAgB,wBAAwB;oBAE9C,qBACE,8OAAC;wBAAqB,WAAU;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAA2E;4DACvF,QAAQ;4DAAE;;;;;;;kEAEd,8OAAC;wDAAG,WAAU;kEAAqC,QAAQ,KAAK;;;;;;;;;;;;0DAElE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,oEAAoE,EAAE,cAAc,KAAK,EAAE;;0EAC3G,8OAAC,cAAc,IAAI;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAC1C,cAAc,IAAI;;;;;;;kEAErB,8OAAC;wDACC,SAAS,IAAM,uBAAuB,QAAQ,EAAE;wDAChD,WAAU;wDACV,OAAO,aAAa,SAAS;kEAE5B,2BAAa,8OAAC,0MAAA,CAAA,SAAM;4DAAC,MAAM;;;;;iFAAS,8OAAC,gMAAA,CAAA,MAAG;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMtD,8OAAC;wCAAE,WAAU;;4CACV,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG;4CAAK;;;;;;;kDAIrC,8OAAC;wCAAI,WAAU;kDACZ,CAAC,QAAQ,QAAQ,iBAChB,8OAAC;4CACC,SAAS,IAAM,kBAAkB,QAAQ,EAAE;4CAC3C,UAAU;4CACV,WAAU;sDAET,4BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;oDAA4E;;6EAI7F;;kEACE,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;iEAMxC;;8DACE,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;;sEAEV,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;4DAAO,MAAM;;;;;;wDAAM;;;;;;;8DAGpC,8OAAC;oDACC,SAAS,IAAM,kBAAkB,QAAQ,EAAE;oDAC3C,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAO,MAAM;;;;;;wDAAM;;;;;;;8DAGrC,8OAAC;oDACC,SAAS,IAAM,wBAAwB,QAAQ,EAAE;oDACjD,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;4DAAO,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;4BAS9C,4BACC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO;;;;;;;;;;;;oCAKnB,QAAQ,QAAQ,kBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;gEAAsB,MAAM;;;;;;0EACnD,8OAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;kEAEvD,8OAAC;wDACC,SAAS,IAAM,mBAAmB;wDAClC,WAAU;kEACX;;;;;;;;;;;;0DAIH,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAO/C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC7B,IAAI,KAAK,QAAQ,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;uBAjHjD,QAAQ,EAAE;;;;;gBAwHxB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAc;;;;;;;0CACzB,8OAAC;;kDAAE,8OAAC;kDAAO;;;;;;oCAAc;;;;;;;0CACzB,8OAAC;0CAAE,cAAA,8OAAC;8CAAO;;;;;;;;;;;0CACX,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;;0DAAG,8OAAC;0DAAO;;;;;;4CAAc;;;;;;;kDAC1B,8OAAC;;0DAAG,8OAAC;0DAAO;;;;;;4CAAc;;;;;;;kDAC1B,8OAAC;;0DAAG,8OAAC;0DAAO;;;;;;4CAAc;;;;;;;;;;;;;;;;;;;;;;;;;YAM/B,iCACC,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,WAAW,gBAAgB,EAAE;gBAC7B,cAAc,gBAAgB,KAAK;gBACnC,QAAQ;gBACR,SAAS;gBACT,UAAU,gBAAgB,QAAQ;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 4758, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/VideoGenerationProgress.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Clock, Video, HardDrive, Zap, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'\n\ninterface VideoProgressProps {\n  episodeId: string\n  isGenerating: boolean\n  onComplete?: () => void\n}\n\ninterface ProgressData {\n  progress: {\n    percentage: number\n    completed: number\n    total: number\n    estimatedRemainingTime: number | null\n    currentProcessing: Array<{\n      index: number\n      title: string\n      startTime: string\n    }>\n    queueStatus: {\n      total: number\n      completed: number\n      processing: number\n      pending: number\n      failed: number\n    }\n  }\n  statistics: {\n    totalVideoSize: number\n    totalVideoDuration: number\n    averageSegmentDuration: number\n  }\n  segments: Array<{\n    id: string\n    index: number\n    title: string\n    status: string\n    duration: number\n    hasVideo: boolean\n    metadata: {\n      videoSize?: number\n      responseTime?: number\n      totalAttempts?: number\n      generationTime?: number\n      completedAt?: string\n      error?: string\n    }\n  }>\n  lastUpdated: string\n}\n\nexport default function VideoGenerationProgress({ episodeId, isGenerating, onComplete }: VideoProgressProps) {\n  const [progressData, setProgressData] = useState<ProgressData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchProgress = async () => {\n    try {\n      const response = await fetch(`/api/ai/video-progress?episodeId=${episodeId}`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setProgressData(data)\n        setError(null)\n        \n        // 检查是否全部完成\n        if (data.progress.percentage === 100 && onComplete) {\n          onComplete()\n        }\n      } else {\n        setError(data.error || '获取进度失败')\n      }\n    } catch (err) {\n      setError('网络请求失败')\n      console.error('获取进度失败:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchProgress()\n    \n    // 如果正在生成，设置定时刷新\n    let interval: NodeJS.Timeout | null = null\n    if (isGenerating) {\n      interval = setInterval(fetchProgress, 3000) // 每3秒刷新一次\n    }\n    \n    return () => {\n      if (interval) clearInterval(interval)\n    }\n  }, [episodeId, isGenerating])\n\n  const formatTime = (seconds: number | null) => {\n    if (!seconds) return '计算中...'\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}分${secs}秒`\n  }\n\n  const formatSize = (bytes: number) => {\n    if (bytes === 0) return '0 B'\n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'generating':\n        return <Loader2 className=\"w-4 h-4 text-blue-500 animate-spin\" />\n      case 'failed':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      default:\n        return <Clock className=\"w-4 h-4 text-gray-400\" />\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800'\n      case 'generating':\n        return 'bg-blue-100 text-blue-800'\n      case 'failed':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg border shadow-sm p-6\">\n        <div className=\"flex items-center justify-center\">\n          <Loader2 className=\"w-6 h-6 animate-spin mr-2\" />\n          <span>加载进度信息...</span>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg border shadow-sm p-6\">\n        <div className=\"flex items-center text-red-600\">\n          <AlertCircle className=\"w-5 h-5 mr-2\" />\n          <span>{error}</span>\n        </div>\n      </div>\n    )\n  }\n\n  if (!progressData) return null\n\n  const { progress, statistics, segments } = progressData\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 总体进度 */}\n      <div className=\"bg-white rounded-lg border shadow-sm\">\n        <div className=\"flex flex-col space-y-1.5 p-6\">\n          <h3 className=\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\">\n            <Video className=\"w-5 h-5\" />\n            视频生成进度\n          </h3>\n        </div>\n        <div className=\"p-6 pt-0 space-y-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span>总体进度</span>\n              <span>{progress.percentage}%</span>\n            </div>\n            <div className=\"relative h-2 w-full overflow-hidden rounded-full bg-gray-200\">\n              <div\n                className=\"h-full w-full flex-1 bg-blue-600 transition-all\"\n                style={{ width: `${progress.percentage}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500\">\n              <span>{progress.completed}/{progress.total} 片段完成</span>\n              {progress.estimatedRemainingTime && (\n                <span>预计剩余: {formatTime(progress.estimatedRemainingTime)}</span>\n              )}\n            </div>\n          </div>\n\n          {/* 统计信息 */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-semibold text-green-600\">{progress.queueStatus.completed}</div>\n              <div className=\"text-xs text-gray-500\">已完成</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-semibold text-blue-600\">{progress.queueStatus.processing}</div>\n              <div className=\"text-xs text-gray-500\">生成中</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-semibold text-gray-600\">{progress.queueStatus.pending}</div>\n              <div className=\"text-xs text-gray-500\">等待中</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-semibold text-red-600\">{progress.queueStatus.failed}</div>\n              <div className=\"text-xs text-gray-500\">失败</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 当前处理状态 */}\n      {progress.currentProcessing.length > 0 && (\n        <div className=\"bg-white rounded-lg border shadow-sm\">\n          <div className=\"flex flex-col space-y-1.5 p-6\">\n            <h3 className=\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\">\n              <Zap className=\"w-5 h-5\" />\n              正在处理\n            </h3>\n          </div>\n          <div className=\"p-6 pt-0\">\n            <div className=\"space-y-2\">\n              {progress.currentProcessing.map((item) => (\n                <div key={item.index} className=\"flex items-center gap-2 p-2 bg-blue-50 rounded\">\n                  <Loader2 className=\"w-4 h-4 animate-spin text-blue-500\" />\n                  <span className=\"text-sm\">片段 {item.index}: {item.title}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 详细片段列表 */}\n      <div className=\"bg-white rounded-lg border shadow-sm\">\n        <div className=\"flex flex-col space-y-1.5 p-6\">\n          <h3 className=\"text-2xl font-semibold leading-none tracking-tight\">片段详情</h3>\n        </div>\n        <div className=\"p-6 pt-0\">\n          <div className=\"space-y-2\">\n            {segments.map((segment) => (\n              <div key={segment.id} className=\"flex items-center justify-between p-3 border rounded\">\n                <div className=\"flex items-center gap-3\">\n                  {getStatusIcon(segment.status)}\n                  <div>\n                    <div className=\"font-medium\">片段 {segment.index}</div>\n                    <div className=\"text-sm text-gray-500 truncate max-w-md\">{segment.title}</div>\n                  </div>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${getStatusColor(segment.status)}`}>\n                    {segment.status === 'completed' ? '已完成' :\n                     segment.status === 'generating' ? '生成中' :\n                     segment.status === 'failed' ? '失败' : '等待中'}\n                  </span>\n                  {segment.metadata.videoSize && (\n                    <span className=\"text-xs text-gray-500\">\n                      {formatSize(segment.metadata.videoSize)}\n                    </span>\n                  )}\n                  {segment.duration > 0 && (\n                    <span className=\"text-xs text-gray-500\">\n                      {segment.duration}s\n                    </span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      {statistics.totalVideoSize > 0 && (\n        <div className=\"bg-white rounded-lg border shadow-sm\">\n          <div className=\"flex flex-col space-y-1.5 p-6\">\n            <h3 className=\"text-2xl font-semibold leading-none tracking-tight flex items-center gap-2\">\n              <HardDrive className=\"w-5 h-5\" />\n              生成统计\n            </h3>\n          </div>\n          <div className=\"p-6 pt-0\">\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n              <div>\n                <div className=\"text-sm text-gray-500\">总文件大小</div>\n                <div className=\"font-semibold\">{formatSize(statistics.totalVideoSize)}</div>\n              </div>\n              <div>\n                <div className=\"text-sm text-gray-500\">总时长</div>\n                <div className=\"font-semibold\">{statistics.totalVideoDuration}秒</div>\n              </div>\n              <div>\n                <div className=\"text-sm text-gray-500\">平均时长</div>\n                <div className=\"font-semibold\">{statistics.averageSegmentDuration.toFixed(1)}秒</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAsDe,SAAS,wBAAwB,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAsB;IACzG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,WAAW;YAC5E,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB;gBAChB,SAAS;gBAET,WAAW;gBACX,IAAI,KAAK,QAAQ,CAAC,UAAU,KAAK,OAAO,YAAY;oBAClD;gBACF;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,gBAAgB;QAChB,IAAI,WAAkC;QACtC,IAAI,cAAc;YAChB,WAAW,YAAY,eAAe,MAAM,UAAU;;QACxD;QAEA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC;QAAW;KAAa;IAE5B,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;kCAAM;;;;;;;;;;;;;;;;;IAIf;IAEA,IAAI,CAAC,cAAc,OAAO;IAE1B,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,SAAS,UAAU;oDAAC;;;;;;;;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAG9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,SAAS,SAAS;oDAAC;oDAAE,SAAS,KAAK;oDAAC;;;;;;;4CAC1C,SAAS,sBAAsB,kBAC9B,8OAAC;;oDAAK;oDAAO,WAAW,SAAS,sBAAsB;;;;;;;;;;;;;;;;;;;0CAM7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwC,SAAS,WAAW,CAAC,SAAS;;;;;;0DACrF,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAuC,SAAS,WAAW,CAAC,UAAU;;;;;;0DACrF,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAuC,SAAS,WAAW,CAAC,OAAO;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC,SAAS,WAAW,CAAC,MAAM;;;;;;0DAChF,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9C,SAAS,iBAAiB,CAAC,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,iBAAiB,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;;gDAAU;gDAAI,KAAK,KAAK;gDAAC;gDAAG,KAAK,KAAK;;;;;;;;mCAF9C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAW9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;;;;;;kCAErE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,QAAQ,MAAM;8DAC7B,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;gEAAc;gEAAI,QAAQ,KAAK;;;;;;;sEAC9C,8OAAC;4DAAI,WAAU;sEAA2C,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,iFAAiF,EAAE,eAAe,QAAQ,MAAM,GAAG;8DAClI,QAAQ,MAAM,KAAK,cAAc,QACjC,QAAQ,MAAM,KAAK,eAAe,QAClC,QAAQ,MAAM,KAAK,WAAW,OAAO;;;;;;gDAEvC,QAAQ,QAAQ,CAAC,SAAS,kBACzB,8OAAC;oDAAK,WAAU;8DACb,WAAW,QAAQ,QAAQ,CAAC,SAAS;;;;;;gDAGzC,QAAQ,QAAQ,GAAG,mBAClB,8OAAC;oDAAK,WAAU;;wDACb,QAAQ,QAAQ;wDAAC;;;;;;;;;;;;;;mCArBhB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YAgC3B,WAAW,cAAc,GAAG,mBAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,8OAAC;4CAAI,WAAU;sDAAiB,WAAW,WAAW,cAAc;;;;;;;;;;;;8CAEtE,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,8OAAC;4CAAI,WAAU;;gDAAiB,WAAW,kBAAkB;gDAAC;;;;;;;;;;;;;8CAEhE,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,8OAAC;4CAAI,WAAU;;gDAAiB,WAAW,sBAAsB,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F", "debugId": null}}, {"offset": {"line": 5519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/VideoSegmentViewer.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Play, Download, Refresh<PERSON><PERSON>, Clock, CheckCircle, XCircle, Loader, Film, BarChart3, RotateCcw, X, Volume2, VolumeX } from 'lucide-react'\nimport VideoGenerationProgress from './VideoGenerationProgress'\nimport ModelSelector from './ModelSelector'\n\ninterface VideoSegment {\n  id: string\n  segmentIndex: number\n  title: string\n  description: string\n  videoUrl?: string\n  thumbnailUrl?: string\n  duration?: number\n  status: 'pending' | 'generating' | 'completed' | 'failed'\n  segmentType: string\n  createdAt: string\n  updatedAt: string\n  metadata: any\n}\n\ninterface AudioInfo {\n  audioUrl: string\n  duration: number\n  provider: string\n  generatedAt: string\n}\n\ninterface VideoSegmentData {\n  storyVideoId: string\n  episodeId: string\n  segments: VideoSegment[]\n  summary: {\n    totalSegments: number\n    completedSegments: number\n    generatingSegments: number\n    failedSegments: number\n    pendingSegments: number\n    progress: number\n    overallStatus: string\n  }\n}\n\ninterface VideoSegmentViewerProps {\n  episodeId: string\n  projectId: string\n  isOpen: boolean\n  onClose: () => void\n  selectedModelId?: string // 用户选择的模型ID\n}\n\nexport default function VideoSegmentViewer({ episodeId, projectId, isOpen, onClose, selectedModelId }: VideoSegmentViewerProps) {\n  const [segmentData, setSegmentData] = useState<VideoSegmentData | null>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [showDetailedProgress, setShowDetailedProgress] = useState(true) // 默认显示详细进度\n  const [generatingSegments, setGeneratingSegments] = useState<Set<string>>(new Set()) // 正在生成的片段ID\n  const [currentModelId, setCurrentModelId] = useState<string>(selectedModelId || '') // 当前选择的模型ID\n\n  // 加载视频片段数据\n  const loadSegmentData = async () => {\n    if (!episodeId) return\n\n    setLoading(true)\n    setError(null)\n\n    try {\n      const response = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setSegmentData(data.data)\n      } else {\n        setError(data.error || '加载失败')\n      }\n    } catch (error) {\n      console.error('加载视频片段失败:', error)\n      setError('加载失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 生成单个片段\n  const handleGenerateSingleSegment = async (segmentId: string) => {\n    try {\n      // 检查是否选择了模型\n      if (!currentModelId) {\n        alert('请先选择视频生成模型')\n        return\n      }\n\n      // 添加到正在生成的集合\n      setGeneratingSegments(prev => new Set([...prev, segmentId]))\n\n      console.log(`🎬 开始生成单个片段: ${segmentId}，使用模型: ${currentModelId}`)\n\n      const response = await fetch('/api/ai/generate-single-segment', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          segmentId,\n          modelId: currentModelId // 传递用户选择的模型ID\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        console.log(`✅ 片段 ${data.data.segmentIndex} 开始生成`)\n\n        // 更新本地状态\n        if (segmentData) {\n          setSegmentData(prev => {\n            if (!prev) return prev\n            return {\n              ...prev,\n              segments: prev.segments.map(seg =>\n                seg.id === segmentId\n                  ? { ...seg, status: 'generating' as const }\n                  : seg\n              )\n            }\n          })\n        }\n\n        // 显示成功消息\n        alert(`片段 \"${data.data.title}\" 开始生成，请稍后查看进度`)\n      } else {\n        console.error('生成片段失败:', data.error)\n\n        // 处理角色图像先决条件错误\n        if (data.requirementType === 'character_images') {\n          const missingChars = data.missingCharacters?.join('、') || '某些角色'\n          alert(`❌ 生成失败\\n\\n${data.error}\\n\\n请前往\"角色管理\"页面为 ${missingChars} 生成角色形象后再试。`)\n        } else {\n          alert(`生成失败: ${data.error}`)\n        }\n      }\n    } catch (error) {\n      console.error('生成单个片段失败:', error)\n      alert('生成片段时发生错误，请稍后重试')\n    } finally {\n      // 从正在生成的集合中移除\n      setGeneratingSegments(prev => {\n        const newSet = new Set(prev)\n        newSet.delete(segmentId)\n        return newSet\n      })\n    }\n  }\n\n  // 重试失败的片段\n  const retryFailedSegment = async (segmentId: string) => {\n    try {\n      console.log(`🔄 开始重试片段: ${segmentId}`)\n\n      // 调用专门的重试API\n      const response = await fetch('/api/ai/retry-failed-segment', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          segmentId: segmentId\n        })\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        console.log('✅ 重试请求成功提交')\n        alert('重试已启动，片段正在重新生成中...')\n        loadSegmentData() // 刷新数据\n      } else {\n        console.error('❌ 重试请求失败:', data.error)\n        alert(`重试失败: ${data.error || '请稍后再试'}`)\n      }\n    } catch (error) {\n      console.error('❌ 重试请求异常:', error)\n      alert('重试失败，请检查网络连接后再试')\n    }\n  }\n\n  // 自动刷新正在生成的片段\n  useEffect(() => {\n    if (!isOpen || !episodeId) return\n\n    loadSegmentData()\n\n    // 如果有正在生成的片段，定期刷新\n    const interval = setInterval(() => {\n      if (segmentData && (segmentData.summary.generatingSegments > 0 || segmentData.summary.pendingSegments > 0)) {\n        loadSegmentData()\n      }\n    }, 5000) // 每5秒刷新一次\n\n    return () => clearInterval(interval)\n  }, [isOpen, episodeId, segmentData?.summary.generatingSegments, segmentData?.summary.pendingSegments])\n\n  // 获取状态图标和颜色\n  const getStatusDisplay = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return { icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-50', text: '已完成' }\n      case 'generating':\n        return { icon: Loader, color: 'text-blue-600', bgColor: 'bg-blue-50', text: '生成中' }\n      case 'failed':\n        return { icon: XCircle, color: 'text-red-600', bgColor: 'bg-red-50', text: '失败' }\n      case 'pending':\n        return { icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', text: '等待中' }\n      default:\n        return { icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', text: '未知' }\n    }\n  }\n\n  // 获取片段类型显示\n  const getSegmentTypeDisplay = (type: string) => {\n    const typeMap: Record<string, string> = {\n      scene: '场景',\n      action: '动作',\n      dialogue: '对话',\n      transition: '转场'\n    }\n    return typeMap[type] || type\n  }\n\n  // 解析音频信息\n  const getAudioInfo = (segment: VideoSegment): AudioInfo | null => {\n    try {\n      if (!segment.metadata) return null\n      const metadata = JSON.parse(segment.metadata)\n      return metadata.audio || null\n    } catch (error) {\n      console.error('解析音频信息失败:', error)\n      return null\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <Film className=\"text-purple-600 mr-2\" size={24} />\n            <div>\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                剧情视频片段\n                {showDetailedProgress && (\n                  <span className=\"ml-2 text-sm font-normal text-purple-600\">• 详细进度监控</span>\n                )}\n              </h2>\n              <div className=\"flex items-center mt-1 space-x-4\">\n                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                  手动生成模式\n                </span>\n                <span className=\"text-xs text-gray-500\">\n                  点击片段上的生成按钮来逐个生成视频\n                </span>\n              </div>\n              {/* 模型选择器 */}\n              <div className=\"flex items-center mt-3 space-x-3\">\n                <span className=\"text-sm font-medium text-gray-700\">生成模型:</span>\n                <div className=\"w-64\">\n                  <ModelSelector\n                    selectedModel={currentModelId}\n                    onModelSelect={setCurrentModelId}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => setShowDetailedProgress(!showDetailedProgress)}\n              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${\n                showDetailedProgress\n                  ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n              title={showDetailedProgress ? \"切换到简化视图\" : \"切换到详细进度\"}\n            >\n              <BarChart3 size={16} className=\"mr-1\" />\n              {showDetailedProgress ? \"简化视图\" : \"详细进度\"}\n            </button>\n            <button\n              onClick={loadSegmentData}\n              disabled={loading}\n              className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n              title=\"刷新\"\n            >\n              <RefreshCw className={loading ? 'animate-spin' : ''} size={20} />\n            </button>\n            <button\n              onClick={onClose}\n              className=\"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors\"\n              title=\"关闭\"\n            >\n              <X size={20} />\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[calc(90vh-80px)] overflow-y-auto\">\n          {loading && !segmentData ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">加载视频片段中...</p>\n              </div>\n            </div>\n          ) : error ? (\n            <div className=\"text-center py-12\">\n              <XCircle className=\"mx-auto h-12 w-12 text-red-400 mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">加载失败</h3>\n              <p className=\"text-gray-600 mb-4\">{error}</p>\n              <button\n                onClick={loadSegmentData}\n                className=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700\"\n              >\n                重试\n              </button>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {/* 详细进度组件 - 总是显示，即使没有segmentData */}\n              {showDetailedProgress && (\n                <VideoGenerationProgress\n                  episodeId={episodeId}\n                  isGenerating={segmentData ? (segmentData.summary.generatingSegments > 0 || segmentData.summary.pendingSegments > 0) : true}\n                  onComplete={() => {\n                    loadSegmentData()\n                  }}\n                />\n              )}\n\n              {segmentData ? (\n                <>\n                  {/* 简化的进度概览 */}\n                  {!showDetailedProgress && (\n                    <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <h3 className=\"text-lg font-medium text-gray-900\">生成进度</h3>\n                      <span className=\"text-sm text-gray-600\">\n                        {segmentData.summary.completedSegments}/{segmentData.summary.totalSegments} 片段完成\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2 mb-2\">\n                      <div\n                        className=\"bg-purple-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${segmentData.summary.progress}%` }}\n                      ></div>\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                      <span>进度: {segmentData.summary.progress}%</span>\n                      <div className=\"flex space-x-4\">\n                        <span>生成中: {segmentData.summary.generatingSegments}</span>\n                        <span>等待: {segmentData.summary.pendingSegments}</span>\n                        <span>失败: {segmentData.summary.failedSegments}</span>\n                      </div>\n                    </div>\n                  </div>\n                  )}\n\n                  {/* 视频片段列表 */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    {segmentData.segments.map((segment) => {\n                      const statusDisplay = getStatusDisplay(segment.status)\n\n                      return (\n                        <div key={segment.id} className=\"border border-gray-200 rounded-lg overflow-hidden\">\n                          {/* 缩略图区域 */}\n                          <div className=\"aspect-video bg-gray-100 relative\">\n                            {segment.thumbnailUrl ? (\n                              <img\n                                src={segment.thumbnailUrl}\n                                alt={segment.title}\n                                className=\"w-full h-full object-cover\"\n                              />\n                            ) : (\n                              <div className=\"w-full h-full flex items-center justify-center\">\n                                <Film className=\"text-gray-400\" size={48} />\n                              </div>\n                            )}\n\n                            {/* 状态覆盖层 */}\n                            <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bgColor} ${statusDisplay.color}`}>\n                              <statusDisplay.icon className=\"inline mr-1\" size={12} />\n                              {statusDisplay.text}\n                            </div>\n\n                            {/* 播放按钮 */}\n                            {segment.status === 'completed' && segment.videoUrl && (\n                              <div className=\"absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity\">\n                                <button\n                                  onClick={() => window.open(segment.videoUrl, '_blank')}\n                                  className=\"bg-black bg-opacity-50 text-white rounded-full p-3 hover:bg-opacity-70 transition-all\"\n                                >\n                                  <Play size={24} />\n                                </button>\n                              </div>\n                            )}\n\n                            {/* 生成按钮 - 针对失败、待生成或等待中的片段 */}\n                            {(segment.status === 'failed' || segment.status === 'pending' || segment.status === 'waiting') && !generatingSegments.has(segment.id) && (\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 transition-all duration-200\">\n                                <button\n                                  onClick={() => handleGenerateSingleSegment(segment.id)}\n                                  className=\"bg-purple-600 text-white rounded-full p-4 hover:bg-purple-700 hover:scale-110 transition-all duration-200 shadow-xl border-2 border-white\"\n                                  title={\n                                    segment.status === 'failed' ? '重新生成' :\n                                    segment.status === 'waiting' ? '立即生成' :\n                                    '开始生成'\n                                  }\n                                >\n                                  <RefreshCw size={28} />\n                                </button>\n                                <div className=\"absolute bottom-2 left-2 right-2 text-center\">\n                                  <span className=\"bg-purple-600 text-white text-xs px-2 py-1 rounded-full\">\n                                    {segment.status === 'failed' ? '重新生成' :\n                                     segment.status === 'waiting' ? '立即生成' :\n                                     '点击生成'}\n                                  </span>\n                                </div>\n                              </div>\n                            )}\n\n                            {/* 正在生成的按钮状态 */}\n                            {generatingSegments.has(segment.id) && (\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30\">\n                                <div className=\"text-white text-center\">\n                                  <Loader className=\"animate-spin mx-auto mb-2\" size={24} />\n                                  <span className=\"text-sm\">启动中...</span>\n                                </div>\n                              </div>\n                            )}\n\n                            {/* 生成中动画 */}\n                            {segment.status === 'generating' && (\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30\">\n                                <div className=\"text-white text-center\">\n                                  <Loader className=\"animate-spin mx-auto mb-2\" size={24} />\n                                  <span className=\"text-sm\">生成中...</span>\n                                </div>\n                              </div>\n                            )}\n                          </div>\n\n                          {/* 片段信息 */}\n                          <div className=\"p-4\">\n                            <div className=\"flex items-center justify-between mb-2\">\n                              <h4 className=\"font-medium text-gray-900 truncate\">{segment.title}</h4>\n                              <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                                {getSegmentTypeDisplay(segment.segmentType)}\n                              </span>\n                            </div>\n\n                            <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">{segment.description}</p>\n\n                            <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                              <span>片段 {segment.segmentIndex}</span>\n                              {segment.duration && (\n                                <span>{segment.duration}秒</span>\n                              )}\n                            </div>\n\n                            {/* 音频信息显示 */}\n                            {(() => {\n                              const audioInfo = getAudioInfo(segment)\n                              if (audioInfo) {\n                                return (\n                                  <div className=\"mt-2 p-2 bg-green-50 border border-green-200 rounded\">\n                                    <div className=\"flex items-center justify-between\">\n                                      <span className=\"text-xs text-green-700\">🎵 音频已生成</span>\n                                      <span className=\"text-xs text-green-600\">{audioInfo.duration.toFixed(1)}秒</span>\n                                    </div>\n                                    <audio\n                                      controls\n                                      className=\"w-full mt-1\"\n                                      style={{ height: '30px' }}\n                                      preload=\"metadata\"\n                                    >\n                                      <source src={audioInfo.audioUrl} type=\"audio/mpeg\" />\n                                      您的浏览器不支持音频播放。\n                                    </audio>\n                                  </div>\n                                )\n                              }\n                              return null\n                            })()}\n\n                            {/* 操作按钮 */}\n                            {segment.status === 'completed' && segment.videoUrl && (\n                              <div className=\"mt-3 flex space-x-2 flex-wrap gap-2\">\n                                <button\n                                  onClick={() => {\n                                    try {\n                                      console.log('视频URL:', segment.videoUrl)\n                                      const videoUrl = segment.videoUrl\n\n                                      // 如果是本地路径，直接在新窗口中播放\n                                      if (videoUrl.startsWith('/downloads/')) {\n                                        window.open(videoUrl, '_blank')\n                                      } else {\n                                        // 如果是远程URL，下载文件\n                                        const cleanUrl = videoUrl.replace(/&amp;/g, '&')\n                                        const link = document.createElement('a')\n                                        link.href = cleanUrl\n                                        link.download = `${segment.title}.mp4`\n                                        document.body.appendChild(link)\n                                        link.click()\n                                        document.body.removeChild(link)\n                                      }\n                                    } catch (error) {\n                                      console.error('播放/下载视频失败:', error)\n                                      alert('播放/下载视频失败，请稍后重试')\n                                    }\n                                  }}\n                                  className=\"flex-1 px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700\"\n                                >\n                                  {segment.videoUrl.startsWith('/downloads/') ? (\n                                    <>\n                                      <Play className=\"inline mr-1\" size={12} />\n                                      播放\n                                    </>\n                                  ) : (\n                                    <>\n                                      <Download className=\"inline mr-1\" size={12} />\n                                      下载\n                                    </>\n                                  )}\n                                </button>\n                                <button\n                                  onClick={() => {\n                                    try {\n                                      const cleanUrl = segment.videoUrl.replace(/&amp;/g, '&')\n                                      // 复制链接到剪贴板\n                                      navigator.clipboard.writeText(cleanUrl).then(() => {\n                                        alert('视频链接已复制到剪贴板，您可以在浏览器中直接粘贴下载')\n                                      }).catch(() => {\n                                        // 如果复制失败，显示链接\n                                        prompt('视频链接（请手动复制）:', cleanUrl)\n                                      })\n                                    } catch (error) {\n                                      console.error('复制链接失败:', error)\n                                      alert('复制链接失败，请稍后重试')\n                                    }\n                                  }}\n                                  className=\"px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50\"\n                                >\n                                  <Download className=\"inline mr-1\" size={12} />\n                                  复制链接\n                                </button>\n\n                                {/* 音频播放按钮 */}\n                                {(() => {\n                                  const audioInfo = getAudioInfo(segment)\n                                  if (audioInfo) {\n                                    return (\n                                      <button\n                                        onClick={() => {\n                                          // 创建音频播放器\n                                          const audio = new Audio(audioInfo.audioUrl)\n                                          audio.play().catch(error => {\n                                            console.error('音频播放失败:', error)\n                                            alert('音频播放失败，请稍后重试')\n                                          })\n                                        }}\n                                        className=\"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 flex items-center\"\n                                        title={`播放音频 (${audioInfo.duration.toFixed(1)}秒)`}\n                                      >\n                                        <Volume2 className=\"inline mr-1\" size={12} />\n                                        播放音频\n                                      </button>\n                                    )\n                                  } else {\n                                    return (\n                                      <button\n                                        disabled\n                                        className=\"px-3 py-1 bg-gray-300 text-gray-500 text-sm rounded cursor-not-allowed flex items-center\"\n                                        title=\"音频未生成\"\n                                      >\n                                        <VolumeX className=\"inline mr-1\" size={12} />\n                                        无音频\n                                      </button>\n                                    )\n                                  }\n                                })()}\n                              </div>\n                            )}\n\n                            {/* 失败重试按钮 */}\n                            {segment.status === 'failed' && (\n                              <div className=\"mt-3 space-y-2\">\n                                {/* 显示失败原因 */}\n                                {segment.metadata && (() => {\n                                  try {\n                                    const metadata = JSON.parse(segment.metadata)\n                                    if (metadata.error) {\n                                      return (\n                                        <div className=\"p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700\">\n                                          <strong>失败原因:</strong> {metadata.error}\n                                        </div>\n                                      )\n                                    }\n                                  } catch (e) {\n                                    return null\n                                  }\n                                  return null\n                                })()}\n\n                                <button\n                                  onClick={() => retryFailedSegment(segment.id)}\n                                  className=\"w-full px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 flex items-center justify-center\"\n                                >\n                                  <RotateCcw className=\"inline mr-1\" size={12} />\n                                  重新生成\n                                </button>\n                              </div>\n                            )}\n\n                            {/* 视频使用提示 */}\n                            {segment.status === 'completed' && segment.videoUrl && (\n                              <div className=\"mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700\">\n                                💡 {segment.videoUrl.startsWith('/downloads/')\n                                  ? '视频已自动下载到本地，点击\"播放\"直接观看。'\n                                  : '点击\"下载\"获取视频文件，或\"复制链接\"获取下载地址。'}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      )\n                    })}\n                  </div>\n                </>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <Film className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">暂无视频片段</h3>\n                  <p className=\"text-gray-600\">请先生成剧情视频</p>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAoDe,SAAS,mBAAmB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAA2B;IAC5H,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,WAAW;;IAClF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI,OAAO,YAAY;;IACjG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,mBAAmB,IAAI,YAAY;;IAEhG,WAAW;IACX,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW;QAEhB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,WAAW;YAC5E,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,IAAI;YAC1B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,8BAA8B,OAAO;QACzC,IAAI;YACF,YAAY;YACZ,IAAI,CAAC,gBAAgB;gBACnB,MAAM;gBACN;YACF;YAEA,aAAa;YACb,sBAAsB,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM;iBAAU;YAE1D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU,OAAO,EAAE,gBAAgB;YAE/D,MAAM,WAAW,MAAM,MAAM,mCAAmC;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS,eAAe,cAAc;gBACxC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;gBAEjD,SAAS;gBACT,IAAI,aAAa;oBACf,eAAe,CAAA;wBACb,IAAI,CAAC,MAAM,OAAO;wBAClB,OAAO;4BACL,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,MAC1B,IAAI,EAAE,KAAK,YACP;oCAAE,GAAG,GAAG;oCAAE,QAAQ;gCAAsB,IACxC;wBAER;oBACF;gBACF;gBAEA,SAAS;gBACT,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;gBAEnC,eAAe;gBACf,IAAI,KAAK,eAAe,KAAK,oBAAoB;oBAC/C,MAAM,eAAe,KAAK,iBAAiB,EAAE,KAAK,QAAQ;oBAC1D,MAAM,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,aAAa,WAAW,CAAC;gBAC5E,OAAO;oBACL,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;gBAC7B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR,SAAU;YACR,cAAc;YACd,sBAAsB,CAAA;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;QACF;IACF;IAEA,UAAU;IACV,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;YAErC,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW;gBACb;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,MAAM;gBACN,kBAAkB,OAAO;;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,KAAK,KAAK;gBACrC,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,IAAI,SAAS;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,CAAC,WAAW;QAE3B;QAEA,kBAAkB;QAClB,MAAM,WAAW,YAAY;YAC3B,IAAI,eAAe,CAAC,YAAY,OAAO,CAAC,kBAAkB,GAAG,KAAK,YAAY,OAAO,CAAC,eAAe,GAAG,CAAC,GAAG;gBAC1G;YACF;QACF,GAAG,MAAM,UAAU;;QAEnB,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAQ;QAAW,aAAa,QAAQ;QAAoB,aAAa,QAAQ;KAAgB;IAErG,YAAY;IACZ,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM,2NAAA,CAAA,cAAW;oBAAE,OAAO;oBAAkB,SAAS;oBAAe,MAAM;gBAAM;YAC3F,KAAK;gBACH,OAAO;oBAAE,MAAM,sMAAA,CAAA,SAAM;oBAAE,OAAO;oBAAiB,SAAS;oBAAc,MAAM;gBAAM;YACpF,KAAK;gBACH,OAAO;oBAAE,MAAM,4MAAA,CAAA,UAAO;oBAAE,OAAO;oBAAgB,SAAS;oBAAa,MAAM;gBAAK;YAClF,KAAK;gBACH,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAiB,SAAS;oBAAc,MAAM;gBAAM;YACnF;gBACE,OAAO;oBAAE,MAAM,oMAAA,CAAA,QAAK;oBAAE,OAAO;oBAAiB,SAAS;oBAAc,MAAM;gBAAK;QACpF;IACF;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;QAC7B,MAAM,UAAkC;YACtC,OAAO;YACP,QAAQ;YACR,UAAU;YACV,YAAY;QACd;QACA,OAAO,OAAO,CAAC,KAAK,IAAI;IAC1B;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,IAAI;YACF,IAAI,CAAC,QAAQ,QAAQ,EAAE,OAAO;YAC9B,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ;YAC5C,OAAO,SAAS,KAAK,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;QACT;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAuB,MAAM;;;;;;8CAC7C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAsC;gDAEjD,sCACC,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;sDAG/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgG;;;;;;8DAGhH,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAK1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;8DACpD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;wDACZ,eAAe;wDACf,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,wBAAwB,CAAC;oCACxC,WAAW,CAAC,2DAA2D,EACrE,uBACI,sDACA,+CACJ;oCACF,OAAO,uBAAuB,YAAY;;sDAE1C,8OAAC,kNAAA,CAAA,YAAS;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAC9B,uBAAuB,SAAS;;;;;;;8CAEnC,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,UAAU,iBAAiB;wCAAI,MAAM;;;;;;;;;;;8CAE7D,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACZ,WAAW,CAAC,4BACX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;+BAG/B,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;6CAKH,8OAAC;wBAAI,WAAU;;4BAEZ,sCACC,8OAAC,6IAAA,CAAA,UAAuB;gCACtB,WAAW;gCACX,cAAc,cAAe,YAAY,OAAO,CAAC,kBAAkB,GAAG,KAAK,YAAY,OAAO,CAAC,eAAe,GAAG,IAAK;gCACtH,YAAY;oCACV;gCACF;;;;;;4BAIH,4BACC;;oCAEG,CAAC,sCACA,8OAAC;wCAAI,WAAU;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAK,WAAU;;4DACb,YAAY,OAAO,CAAC,iBAAiB;4DAAC;4DAAE,YAAY,OAAO,CAAC,aAAa;4DAAC;;;;;;;;;;;;;0DAG/E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAK,YAAY,OAAO,CAAC,QAAQ;4DAAC;;;;;;;kEACxC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAM,YAAY,OAAO,CAAC,kBAAkB;;;;;;;0EAClD,8OAAC;;oEAAK;oEAAK,YAAY,OAAO,CAAC,eAAe;;;;;;;0EAC9C,8OAAC;;oEAAK;oEAAK,YAAY,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;kDAOnD,8OAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC;4CACzB,MAAM,gBAAgB,iBAAiB,QAAQ,MAAM;4CAErD,qBACE,8OAAC;gDAAqB,WAAU;;kEAE9B,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,YAAY,iBACnB,8OAAC;gEACC,KAAK,QAAQ,YAAY;gEACzB,KAAK,QAAQ,KAAK;gEAClB,WAAU;;;;;qFAGZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;oEAAgB,MAAM;;;;;;;;;;;0EAK1C,8OAAC;gEAAI,WAAW,CAAC,kEAAkE,EAAE,cAAc,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE;;kFACjI,8OAAC,cAAc,IAAI;wEAAC,WAAU;wEAAc,MAAM;;;;;;oEACjD,cAAc,IAAI;;;;;;;4DAIpB,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,kBACjD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;oEAC7C,WAAU;8EAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,MAAM;;;;;;;;;;;;;;;;4DAMjB,CAAC,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,KAAK,aAAa,QAAQ,MAAM,KAAK,SAAS,KAAK,CAAC,mBAAmB,GAAG,CAAC,QAAQ,EAAE,mBAClI,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,4BAA4B,QAAQ,EAAE;wEACrD,WAAU;wEACV,OACE,QAAQ,MAAM,KAAK,WAAW,SAC9B,QAAQ,MAAM,KAAK,YAAY,SAC/B;kFAGF,cAAA,8OAAC,gNAAA,CAAA,YAAS;4EAAC,MAAM;;;;;;;;;;;kFAEnB,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFACb,QAAQ,MAAM,KAAK,WAAW,SAC9B,QAAQ,MAAM,KAAK,YAAY,SAC/B;;;;;;;;;;;;;;;;;4DAOR,mBAAmB,GAAG,CAAC,QAAQ,EAAE,mBAChC,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;4EAA4B,MAAM;;;;;;sFACpD,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;4DAM/B,QAAQ,MAAM,KAAK,8BAClB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;4EAA4B,MAAM;;;;;;sFACpD,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;kEAOlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAsC,QAAQ,KAAK;;;;;;kFACjE,8OAAC;wEAAK,WAAU;kFACb,sBAAsB,QAAQ,WAAW;;;;;;;;;;;;0EAI9C,8OAAC;gEAAE,WAAU;0EAA2C,QAAQ,WAAW;;;;;;0EAE3E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK;4EAAI,QAAQ,YAAY;;;;;;;oEAC7B,QAAQ,QAAQ,kBACf,8OAAC;;4EAAM,QAAQ,QAAQ;4EAAC;;;;;;;;;;;;;4DAK3B,CAAC;gEACA,MAAM,YAAY,aAAa;gEAC/B,IAAI,WAAW;oEACb,qBACE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAyB;;;;;;kGACzC,8OAAC;wFAAK,WAAU;;4FAA0B,UAAU,QAAQ,CAAC,OAAO,CAAC;4FAAG;;;;;;;;;;;;;0FAE1E,8OAAC;gFACC,QAAQ;gFACR,WAAU;gFACV,OAAO;oFAAE,QAAQ;gFAAO;gFACxB,SAAQ;;kGAER,8OAAC;wFAAO,KAAK,UAAU,QAAQ;wFAAE,MAAK;;;;;;oFAAe;;;;;;;;;;;;;gEAK7D;gEACA,OAAO;4DACT,CAAC;4DAGA,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,kBACjD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS;4EACP,IAAI;gFACF,QAAQ,GAAG,CAAC,UAAU,QAAQ,QAAQ;gFACtC,MAAM,WAAW,QAAQ,QAAQ;gFAEjC,oBAAoB;gFACpB,IAAI,SAAS,UAAU,CAAC,gBAAgB;oFACtC,OAAO,IAAI,CAAC,UAAU;gFACxB,OAAO;oFACL,gBAAgB;oFAChB,MAAM,WAAW,SAAS,OAAO,CAAC,UAAU;oFAC5C,MAAM,OAAO,SAAS,aAAa,CAAC;oFACpC,KAAK,IAAI,GAAG;oFACZ,KAAK,QAAQ,GAAG,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC;oFACtC,SAAS,IAAI,CAAC,WAAW,CAAC;oFAC1B,KAAK,KAAK;oFACV,SAAS,IAAI,CAAC,WAAW,CAAC;gFAC5B;4EACF,EAAE,OAAO,OAAO;gFACd,QAAQ,KAAK,CAAC,cAAc;gFAC5B,MAAM;4EACR;wEACF;wEACA,WAAU;kFAET,QAAQ,QAAQ,CAAC,UAAU,CAAC,+BAC3B;;8FACE,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;oFAAc,MAAM;;;;;;gFAAM;;yGAI5C;;8FACE,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;oFAAc,MAAM;;;;;;gFAAM;;;;;;;;kFAKpD,8OAAC;wEACC,SAAS;4EACP,IAAI;gFACF,MAAM,WAAW,QAAQ,QAAQ,CAAC,OAAO,CAAC,UAAU;gFACpD,WAAW;gFACX,UAAU,SAAS,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC;oFAC3C,MAAM;gFACR,GAAG,KAAK,CAAC;oFACP,cAAc;oFACd,OAAO,gBAAgB;gFACzB;4EACF,EAAE,OAAO,OAAO;gFACd,QAAQ,KAAK,CAAC,WAAW;gFACzB,MAAM;4EACR;wEACF;wEACA,WAAU;;0FAEV,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;gFAAc,MAAM;;;;;;4EAAM;;;;;;;oEAK/C,CAAC;wEACA,MAAM,YAAY,aAAa;wEAC/B,IAAI,WAAW;4EACb,qBACE,8OAAC;gFACC,SAAS;oFACP,UAAU;oFACV,MAAM,QAAQ,IAAI,MAAM,UAAU,QAAQ;oFAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAA;wFACjB,QAAQ,KAAK,CAAC,WAAW;wFACzB,MAAM;oFACR;gFACF;gFACA,WAAU;gFACV,OAAO,CAAC,MAAM,EAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;;kGAEjD,8OAAC,4MAAA,CAAA,UAAO;wFAAC,WAAU;wFAAc,MAAM;;;;;;oFAAM;;;;;;;wEAInD,OAAO;4EACL,qBACE,8OAAC;gFACC,QAAQ;gFACR,WAAU;gFACV,OAAM;;kGAEN,8OAAC,4MAAA,CAAA,UAAO;wFAAC,WAAU;wFAAc,MAAM;;;;;;oFAAM;;;;;;;wEAInD;oEACF,CAAC;;;;;;;4DAKJ,QAAQ,MAAM,KAAK,0BAClB,8OAAC;gEAAI,WAAU;;oEAEZ,QAAQ,QAAQ,IAAI,CAAC;wEACpB,IAAI;4EACF,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ;4EAC5C,IAAI,SAAS,KAAK,EAAE;gFAClB,qBACE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;sGAAO;;;;;;wFAAc;wFAAE,SAAS,KAAK;;;;;;;4EAG5C;wEACF,EAAE,OAAO,GAAG;4EACV,OAAO;wEACT;wEACA,OAAO;oEACT,CAAC;kFAED,8OAAC;wEACC,SAAS,IAAM,mBAAmB,QAAQ,EAAE;wEAC5C,WAAU;;0FAEV,8OAAC,gNAAA,CAAA,YAAS;gFAAC,WAAU;gFAAc,MAAM;;;;;;4EAAM;;;;;;;;;;;;;4DAOpD,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,kBACjD,8OAAC;gEAAI,WAAU;;oEAA2E;oEACpF,QAAQ,QAAQ,CAAC,UAAU,CAAC,iBAC5B,2BACA;;;;;;;;;;;;;;+CAhQF,QAAQ,EAAE;;;;;wCAsQxB;;;;;;;6DAIJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}, {"offset": {"line": 6738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/EpisodeVideoCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Video, Play, Clock, Download, Eye, RotateCcw, Shield } from 'lucide-react'\n\ninterface Episode {\n  id: string\n  title: string\n  content: string\n}\n\ninterface VideoSegment {\n  id: string\n  title: string\n  videoUrl: string\n  duration: number\n  createdAt: string\n  status: 'generating' | 'completed' | 'failed'\n}\n\ninterface EpisodeVideoCardProps {\n  episode: Episode\n  episodeIndex: number\n  projectId: string\n  onViewSegments: () => void\n  onGenerateConsistencyVideo?: () => void\n}\n\nexport default function EpisodeVideoCard({ episode, episodeIndex, projectId, onViewSegments, onGenerateConsistencyVideo }: EpisodeVideoCardProps) {\n  const [videoSegments, setVideoSegments] = useState<VideoSegment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [totalDuration, setTotalDuration] = useState(0)\n\n  // 加载视频片段\n  useEffect(() => {\n    loadVideoSegments()\n  }, [episode.id])\n\n  const loadVideoSegments = async () => {\n    try {\n      setLoading(true)\n      // 调用API获取该剧集的视频片段\n      const response = await fetch(`/api/ai/video-segments?episodeId=${episode.id}`)\n      if (response.ok) {\n        const data = await response.json()\n        console.log('API返回的完整数据:', data) // 调试日志\n\n        // 修复数据结构访问\n        const segments = data.data?.segments || data.segments || []\n        setVideoSegments(segments)\n\n        // 调试信息\n        console.log(`Episode ${episode.title} segments:`, segments)\n        console.log(`Generating segments count:`, segments.filter((s: VideoSegment) => s.status === 'generating').length)\n\n        // 计算总时长\n        const total = segments.reduce((sum: number, segment: VideoSegment) =>\n          sum + (segment.duration || 0), 0)\n        setTotalDuration(total)\n      } else {\n        console.error('Failed to load video segments:', response.status)\n        setVideoSegments([])\n        setTotalDuration(0)\n      }\n    } catch (error) {\n      console.error('加载视频片段失败:', error)\n      setVideoSegments([])\n      setTotalDuration(0)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatDuration = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  // 重试失败的片段\n  const retryFailedSegment = async (segmentId: string) => {\n    try {\n      console.log(`🔄 开始重试片段: ${segmentId}`)\n\n      // 调用专门的重试API\n      const response = await fetch('/api/ai/retry-failed-segment', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          segmentId: segmentId\n        })\n      })\n\n      const data = await response.json()\n      if (data.success) {\n        console.log('✅ 重试请求成功提交')\n        alert('重试已启动，片段正在重新生成中...')\n        loadVideoSegments() // 刷新数据\n      } else {\n        console.error('❌ 重试请求失败:', data.error)\n        alert(`重试失败: ${data.error || '请稍后再试'}`)\n      }\n    } catch (error) {\n      console.error('❌ 重试请求异常:', error)\n      alert('重试失败，请检查网络连接后再试')\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'text-green-600 bg-green-100'\n      case 'generating': return 'text-yellow-600 bg-yellow-100'\n      case 'failed': return 'text-red-600 bg-red-100'\n      default: return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return '已完成'\n      case 'generating': return '生成中'\n      case 'failed': return '生成失败'\n      default: return '未知状态'\n    }\n  }\n\n  const completedSegments = videoSegments.filter(segment => segment.status === 'completed')\n  const generatingSegments = videoSegments.filter(segment => segment.status === 'generating')\n\n  // 确保数据一致性：如果没有任何片段，则生成中的片段数量应该为0\n  const actualGeneratingCount = videoSegments.length === 0 ? 0 : generatingSegments.length\n\n  return (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n      {/* 剧集标题 */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full mr-3\">\n            第{episodeIndex + 1}集\n          </span>\n          <h4 className=\"text-lg font-semibold text-gray-900\">{episode.title}</h4>\n        </div>\n        <div className=\"flex space-x-2\">\n          {onGenerateConsistencyVideo && (\n            <button\n              onClick={onGenerateConsistencyVideo}\n              className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors\"\n            >\n              <Shield className=\"mr-2\" size={16} />\n              一致性视频\n            </button>\n          )}\n          <button\n            onClick={onViewSegments}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 transition-colors\"\n          >\n            <Eye className=\"mr-2\" size={16} />\n            查看所有片段\n          </button>\n        </div>\n      </div>\n\n      {loading ? (\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600\"></div>\n          <span className=\"ml-2 text-gray-600\">加载视频片段...</span>\n        </div>\n      ) : (\n        <>\n          {/* 视频统计信息 */}\n          <div className=\"grid grid-cols-3 gap-4 mb-4\">\n            <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-gray-900\">{videoSegments.length}</div>\n              <div className=\"text-sm text-gray-600\">总片段数</div>\n            </div>\n            <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-green-600\">{completedSegments.length}</div>\n              <div className=\"text-sm text-gray-600\">已完成</div>\n            </div>\n            <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-blue-600\">{formatDuration(totalDuration)}</div>\n              <div className=\"text-sm text-gray-600\">总时长</div>\n            </div>\n          </div>\n\n          {/* 视频片段预览 */}\n          {videoSegments.length > 0 ? (\n            <div className=\"space-y-3\">\n              <h5 className=\"text-sm font-medium text-gray-700 mb-2\">视频片段预览</h5>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                {videoSegments.slice(0, 6).map((segment, index) => (\n                  <div key={segment.id} className=\"border border-gray-200 rounded-lg p-3 hover:bg-gray-50\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-xs font-medium text-gray-500\">片段 {index + 1}</span>\n                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(segment.status)}`}>\n                        {getStatusText(segment.status)}\n                      </span>\n                    </div>\n                    <div className=\"text-sm font-medium text-gray-900 mb-1 truncate\">\n                      {segment.title}\n                    </div>\n                    <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                      <span className=\"flex items-center\">\n                        <Clock size={12} className=\"mr-1\" />\n                        {formatDuration(segment.duration)}\n                      </span>\n                      {segment.status === 'completed' && segment.videoUrl && (\n                        <button\n                          onClick={() => {\n                            try {\n                              const videoUrl = segment.videoUrl\n\n                              // 如果是本地路径，直接在新窗口中播放\n                              if (videoUrl.startsWith('/downloads/')) {\n                                window.open(videoUrl, '_blank')\n                              } else {\n                                // 如果是远程URL，下载文件\n                                const cleanUrl = videoUrl.replace(/&amp;/g, '&')\n                                const link = document.createElement('a')\n                                link.href = cleanUrl\n                                link.download = `${segment.title}.mp4`\n                                document.body.appendChild(link)\n                                link.click()\n                                document.body.removeChild(link)\n                              }\n                            } catch (error) {\n                              console.error('播放/下载视频失败:', error)\n                              alert('播放/下载视频失败，请稍后重试')\n                            }\n                          }}\n                          className=\"flex items-center text-purple-600 hover:text-purple-800\"\n                        >\n                          {segment.videoUrl.startsWith('/downloads/') ? (\n                            <>\n                              <Play size={12} className=\"mr-1\" />\n                              播放\n                            </>\n                          ) : (\n                            <>\n                              <Download size={12} className=\"mr-1\" />\n                              下载\n                            </>\n                          )}\n                        </button>\n                      )}\n                      {segment.status === 'failed' && (\n                        <button\n                          onClick={() => retryFailedSegment(segment.id)}\n                          className=\"flex items-center text-red-600 hover:text-red-800\"\n                          title=\"重新生成\"\n                        >\n                          <RotateCcw size={12} className=\"mr-1\" />\n                          重试\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n              \n              {videoSegments.length > 6 && (\n                <div className=\"text-center\">\n                  <button\n                    onClick={onViewSegments}\n                    className=\"text-sm text-purple-600 hover:text-purple-800 font-medium\"\n                  >\n                    查看全部 {videoSegments.length} 个片段 →\n                  </button>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-gray-500\">\n              <Video size={48} className=\"mx-auto mb-2 text-gray-300\" />\n              <p>暂无视频片段</p>\n              <p className=\"text-sm\">请先生成视频内容</p>\n            </div>\n          )}\n\n          {/* 生成中的提示 - 只有在真正有生成中的片段时才显示 */}\n          {actualGeneratingCount > 0 && (\n            <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2\"></div>\n                <span className=\"text-sm text-yellow-800\">\n                  正在生成 {actualGeneratingCount} 个视频片段...\n                </span>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA4Be,SAAS,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,0BAA0B,EAAyB;IAC9I,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,QAAQ,EAAE;KAAC;IAEf,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,kBAAkB;YAClB,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,QAAQ,EAAE,EAAE;YAC7E,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,GAAG,CAAC,eAAe,MAAM,OAAO;;gBAExC,WAAW;gBACX,MAAM,WAAW,KAAK,IAAI,EAAE,YAAY,KAAK,QAAQ,IAAI,EAAE;gBAC3D,iBAAiB;gBAEjB,OAAO;gBACP,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,KAAK,CAAC,UAAU,CAAC,EAAE;gBAClD,QAAQ,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAE,SAAS,MAAM,CAAC,CAAC,IAAoB,EAAE,MAAM,KAAK,cAAc,MAAM;gBAEhH,QAAQ;gBACR,MAAM,QAAQ,SAAS,MAAM,CAAC,CAAC,KAAa,UAC1C,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,GAAG;gBACjC,iBAAiB;YACnB,OAAO;gBACL,QAAQ,KAAK,CAAC,kCAAkC,SAAS,MAAM;gBAC/D,iBAAiB,EAAE;gBACnB,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,iBAAiB,EAAE;YACnB,iBAAiB;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,UAAU;IACV,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;YAErC,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW;gBACb;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,MAAM;gBACN,oBAAoB,OAAO;;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC,aAAa,KAAK,KAAK;gBACrC,MAAM,CAAC,MAAM,EAAE,KAAK,KAAK,IAAI,SAAS;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAC7E,MAAM,qBAAqB,cAAc,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;IAE9E,iCAAiC;IACjC,MAAM,wBAAwB,cAAc,MAAM,KAAK,IAAI,IAAI,mBAAmB,MAAM;IAExF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAgF;oCAC5F,eAAe;oCAAE;;;;;;;0CAErB,8OAAC;gCAAG,WAAU;0CAAuC,QAAQ,KAAK;;;;;;;;;;;;kCAEpE,8OAAC;wBAAI,WAAU;;4BACZ,4CACC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;0CAIzC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;YAMvC,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;qCAGvC;;kCAEE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoC,cAAc,MAAM;;;;;;kDACvE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC,kBAAkB,MAAM;;;;;;kDAC5E,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoC,eAAe;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAK1C,cAAc,MAAM,GAAG,kBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACvC,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAoC;4DAAI,QAAQ;;;;;;;kEAChE,8OAAC;wDAAK,WAAW,CAAC,+BAA+B,EAAE,eAAe,QAAQ,MAAM,GAAG;kEAChF,cAAc,QAAQ,MAAM;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,oMAAA,CAAA,QAAK;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAC1B,eAAe,QAAQ,QAAQ;;;;;;;oDAEjC,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,kBACjD,8OAAC;wDACC,SAAS;4DACP,IAAI;gEACF,MAAM,WAAW,QAAQ,QAAQ;gEAEjC,oBAAoB;gEACpB,IAAI,SAAS,UAAU,CAAC,gBAAgB;oEACtC,OAAO,IAAI,CAAC,UAAU;gEACxB,OAAO;oEACL,gBAAgB;oEAChB,MAAM,WAAW,SAAS,OAAO,CAAC,UAAU;oEAC5C,MAAM,OAAO,SAAS,aAAa,CAAC;oEACpC,KAAK,IAAI,GAAG;oEACZ,KAAK,QAAQ,GAAG,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC;oEACtC,SAAS,IAAI,CAAC,WAAW,CAAC;oEAC1B,KAAK,KAAK;oEACV,SAAS,IAAI,CAAC,WAAW,CAAC;gEAC5B;4DACF,EAAE,OAAO,OAAO;gEACd,QAAQ,KAAK,CAAC,cAAc;gEAC5B,MAAM;4DACR;wDACF;wDACA,WAAU;kEAET,QAAQ,QAAQ,CAAC,UAAU,CAAC,+BAC3B;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAS;;yFAIrC;;8EACE,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAS;;;;;;;;oDAM9C,QAAQ,MAAM,KAAK,0BAClB,8OAAC;wDACC,SAAS,IAAM,mBAAmB,QAAQ,EAAE;wDAC5C,WAAU;wDACV,OAAM;;0EAEN,8OAAC,gNAAA,CAAA,YAAS;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAS;;;;;;;;;;;;;;uCA5DtC,QAAQ,EAAE;;;;;;;;;;4BAqEvB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;wCACX;wCACO,cAAc,MAAM;wCAAC;;;;;;;;;;;;;;;;;6CAMnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC3B,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;oBAK1B,wBAAwB,mBACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;;wCAA0B;wCAClC;wCAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 7324, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/ConsistencyValidator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Shield, CheckCircle, AlertTriangle, XCircle, Play, BarChart3, Eye, RefreshCw } from 'lucide-react'\n\ninterface ConsistencyValidatorProps {\n  generationId: string\n  videoUrl?: string\n  onValidationComplete?: (results: any) => void\n}\n\nexport default function ConsistencyValidator({ \n  generationId, \n  videoUrl, \n  onValidationComplete \n}: ConsistencyValidatorProps) {\n  const [isValidating, setIsValidating] = useState(false)\n  const [validationResults, setValidationResults] = useState<any>(null)\n  const [error, setError] = useState<string | null>(null)\n  \n  // 验证配置\n  const [validationType, setValidationType] = useState<'full' | 'character' | 'scene'>('full')\n  const [strictMode, setStrictMode] = useState(false)\n\n  // 执行一致性验证\n  const handleValidateConsistency = async () => {\n    try {\n      setIsValidating(true)\n      setError(null)\n      setValidationResults(null)\n\n      console.log('🔍 开始一致性验证...')\n\n      const response = await fetch('/api/ai/validate-consistency', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          generationId,\n          videoUrl,\n          validationType,\n          strictMode\n        })\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || '验证失败')\n      }\n\n      if (result.success) {\n        setValidationResults(result.data)\n        onValidationComplete?.(result.data)\n        console.log('✅ 一致性验证成功:', result.data)\n      } else {\n        throw new Error(result.error || '验证失败')\n      }\n\n    } catch (error) {\n      console.error('❌ 一致性验证失败:', error)\n      setError(error instanceof Error ? error.message : '验证失败，请重试')\n    } finally {\n      setIsValidating(false)\n    }\n  }\n\n  // 获取评分颜色\n  const getScoreColor = (score: number) => {\n    if (score >= 0.9) return 'text-green-600 bg-green-100'\n    if (score >= 0.7) return 'text-yellow-600 bg-yellow-100'\n    return 'text-red-600 bg-red-100'\n  }\n\n  // 获取评分文本\n  const getScoreText = (score: number) => {\n    if (score >= 0.9) return '优秀'\n    if (score >= 0.7) return '良好'\n    if (score >= 0.5) return '一般'\n    return '需改进'\n  }\n\n  // 获取问题严重程度颜色\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'high': return 'text-red-600 bg-red-100'\n      case 'medium': return 'text-yellow-600 bg-yellow-100'\n      case 'low': return 'text-blue-600 bg-blue-100'\n      default: return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  return (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n      {/* 标题 */}\n      <div className=\"flex items-center mb-6\">\n        <Shield className=\"text-purple-600 mr-3\" size={24} />\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900\">一致性验证</h3>\n          <p className=\"text-sm text-gray-600\">验证生成的视频是否符合角色和场景一致性约束</p>\n        </div>\n      </div>\n\n      {/* 验证配置 */}\n      <div className=\"space-y-4 mb-6\">\n        <div className=\"grid grid-cols-2 gap-4\">\n          {/* 验证类型 */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">验证类型</label>\n            <select\n              value={validationType}\n              onChange={(e) => setValidationType(e.target.value as any)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500\"\n            >\n              <option value=\"full\">完整验证</option>\n              <option value=\"character\">仅角色验证</option>\n              <option value=\"scene\">仅场景验证</option>\n            </select>\n          </div>\n\n          {/* 严格模式 */}\n          <div>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={strictMode}\n                onChange={(e) => setStrictMode(e.target.checked)}\n                className=\"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n              />\n              <span className=\"ml-2 text-sm font-medium text-gray-700\">严格模式</span>\n            </label>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              启用后将使用更严格的一致性标准\n            </p>\n          </div>\n        </div>\n\n        {/* 验证按钮 */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500\">\n            生成ID: <span className=\"font-mono text-xs\">{generationId}</span>\n          </div>\n          <button\n            onClick={handleValidateConsistency}\n            disabled={isValidating}\n            className={`inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors ${\n              isValidating\n                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                : 'bg-purple-600 text-white hover:bg-purple-700'\n            }`}\n          >\n            {isValidating ? (\n              <>\n                <RefreshCw className=\"animate-spin mr-2\" size={16} />\n                验证中...\n              </>\n            ) : (\n              <>\n                <Shield className=\"mr-2\" size={16} />\n                开始验证\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"mb-6 p-3 bg-red-50 border border-red-200 rounded-md\">\n          <div className=\"flex items-center\">\n            <XCircle className=\"text-red-500 mr-2\" size={16} />\n            <span className=\"text-sm text-red-700\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* 验证结果 */}\n      {validationResults && (\n        <div className=\"space-y-6\">\n          {/* 总体评分 */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"font-medium text-gray-900\">总体一致性评分</h4>\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(validationResults.summary.overallScore)}`}>\n                {(validationResults.summary.overallScore * 100).toFixed(1)}% - {getScoreText(validationResults.summary.overallScore)}\n              </span>\n            </div>\n            \n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-lg font-semibold text-gray-900\">\n                  {(validationResults.summary.characterConsistency * 100).toFixed(1)}%\n                </div>\n                <div className=\"text-xs text-gray-500\">角色一致性</div>\n              </div>\n              <div>\n                <div className=\"text-lg font-semibold text-gray-900\">\n                  {(validationResults.summary.sceneConsistency * 100).toFixed(1)}%\n                </div>\n                <div className=\"text-xs text-gray-500\">场景一致性</div>\n              </div>\n              <div>\n                <div className=\"text-lg font-semibold text-gray-900\">\n                  {validationResults.summary.issuesFound}\n                </div>\n                <div className=\"text-xs text-gray-500\">发现问题</div>\n              </div>\n            </div>\n          </div>\n\n          {/* 详细结果 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* 角色验证结果 */}\n            {validationResults.results.characterValidation && (\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center mb-3\">\n                  <CheckCircle className=\"text-green-600 mr-2\" size={16} />\n                  <h5 className=\"font-medium text-gray-900\">角色一致性</h5>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  {validationResults.results.characterValidation.characterScores.map((char: any, index: number) => (\n                    <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                      <span className=\"text-sm text-gray-700\">{char.characterName}</span>\n                      <div className=\"flex items-center space-x-2\">\n                        {char.hasProjectReference && (\n                          <span className=\"text-xs text-blue-600\">已关联</span>\n                        )}\n                        <span className={`text-xs px-2 py-1 rounded ${getScoreColor(char.score)}`}>\n                          {(char.score * 100).toFixed(0)}%\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* 场景验证结果 */}\n            {validationResults.results.sceneValidation && (\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center mb-3\">\n                  <Eye className=\"text-blue-600 mr-2\" size={16} />\n                  <h5 className=\"font-medium text-gray-900\">场景一致性</h5>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  {validationResults.results.sceneValidation.sceneScores.map((scene: any, index: number) => (\n                    <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                      <span className=\"text-sm text-gray-700\">{scene.sceneName}</span>\n                      <span className={`text-xs px-2 py-1 rounded ${getScoreColor(scene.score)}`}>\n                        {(scene.score * 100).toFixed(0)}%\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 发现的问题 */}\n          {validationResults.results.issuesFound && validationResults.results.issuesFound.length > 0 && (\n            <div className=\"border border-orange-200 rounded-lg p-4 bg-orange-50\">\n              <div className=\"flex items-center mb-3\">\n                <AlertTriangle className=\"text-orange-600 mr-2\" size={16} />\n                <h5 className=\"font-medium text-orange-800\">发现的问题</h5>\n              </div>\n              \n              <div className=\"space-y-2\">\n                {validationResults.results.issuesFound.map((issue: any, index: number) => (\n                  <div key={index} className=\"flex items-start space-x-2\">\n                    <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(issue.severity)}`}>\n                      {issue.severity}\n                    </span>\n                    <span className=\"text-sm text-gray-700 flex-1\">{issue.description}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 改进建议 */}\n          {validationResults.results.recommendations && validationResults.results.recommendations.length > 0 && (\n            <div className=\"border border-blue-200 rounded-lg p-4 bg-blue-50\">\n              <div className=\"flex items-center mb-3\">\n                <BarChart3 className=\"text-blue-600 mr-2\" size={16} />\n                <h5 className=\"font-medium text-blue-800\">改进建议</h5>\n              </div>\n              \n              <div className=\"space-y-2\">\n                {validationResults.results.recommendations.map((rec: any, index: number) => (\n                  <div key={index} className=\"flex items-start space-x-2\">\n                    <span className={`text-xs px-2 py-1 rounded ${\n                      rec.priority === 'high' ? 'bg-red-100 text-red-800' :\n                      rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-green-100 text-green-800'\n                    }`}>\n                      {rec.priority}\n                    </span>\n                    <span className=\"text-sm text-gray-700 flex-1\">{rec.description}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* 验证详情 */}\n          <div className=\"text-xs text-gray-500 space-y-1\">\n            <p>验证ID: {validationResults.validationId}</p>\n            <p>验证类型: {validationResults.validationType}</p>\n            <p>严格模式: {validationResults.strictMode ? '是' : '否'}</p>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACM;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,OAAO;IACP,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,UAAU;IACV,MAAM,4BAA4B;QAChC,IAAI;YACF,gBAAgB;YAChB,SAAS;YACT,qBAAqB;YAErB,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,qBAAqB,OAAO,IAAI;gBAChC,uBAAuB,OAAO,IAAI;gBAClC,QAAQ,GAAG,CAAC,cAAc,OAAO,IAAI;YACvC,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,aAAa;IACb,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAuB,MAAM;;;;;;kCAC/C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;gDAC/C,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAE3D,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAO9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAwB;kDAC/B,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;;0CAE7C,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,4EAA4E,EACtF,eACI,iDACA,gDACJ;0CAED,6BACC;;sDACE,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAoB,MAAM;;;;;;wCAAM;;iEAIvD;;sDACE,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCAAM;;;;;;;;;;;;;;;;;;;;YAS9C,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;4BAAoB,MAAM;;;;;;sCAC7C,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM7C,mCACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,kBAAkB,OAAO,CAAC,YAAY,GAAG;;4CACnH,CAAC,kBAAkB,OAAO,CAAC,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;4CAAK,aAAa,kBAAkB,OAAO,CAAC,YAAY;;;;;;;;;;;;;0CAIvH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDACZ,CAAC,kBAAkB,OAAO,CAAC,oBAAoB,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;0DAErE,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDACZ,CAAC,kBAAkB,OAAO,CAAC,gBAAgB,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;0DAEjE,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,OAAO,CAAC,WAAW;;;;;;0DAExC,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;;4BAEZ,kBAAkB,OAAO,CAAC,mBAAmB,kBAC5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;gDAAsB,MAAM;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC7E,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAK,WAAU;kEAAyB,KAAK,aAAa;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,mBAAmB,kBACvB,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EAE1C,8OAAC;gEAAK,WAAW,CAAC,0BAA0B,EAAE,cAAc,KAAK,KAAK,GAAG;;oEACtE,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;+CAP3B;;;;;;;;;;;;;;;;4BAiBjB,kBAAkB,OAAO,CAAC,eAAe,kBACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;gDAAqB,MAAM;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAY,sBACtE,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAK,WAAU;kEAAyB,MAAM,SAAS;;;;;;kEACxD,8OAAC;wDAAK,WAAW,CAAC,0BAA0B,EAAE,cAAc,MAAM,KAAK,GAAG;;4DACvE,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;+CAH1B;;;;;;;;;;;;;;;;;;;;;;oBAanB,kBAAkB,OAAO,CAAC,WAAW,IAAI,kBAAkB,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,mBACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;wCAAuB,MAAM;;;;;;kDACtD,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;;;;;;;0CAG9C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAY,sBACtD,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAK,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,MAAM,QAAQ,GAAG;0DAC7E,MAAM,QAAQ;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAgC,MAAM,WAAW;;;;;;;uCAJzD;;;;;;;;;;;;;;;;oBAYjB,kBAAkB,OAAO,CAAC,eAAe,IAAI,kBAAkB,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,mBAC/F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAqB,MAAM;;;;;;kDAChD,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAU,sBACxD,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAK,WAAW,CAAC,0BAA0B,EAC1C,IAAI,QAAQ,KAAK,SAAS,4BAC1B,IAAI,QAAQ,KAAK,WAAW,kCAC5B,+BACA;0DACC,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAK,WAAU;0DAAgC,IAAI,WAAW;;;;;;;uCARvD;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAO,kBAAkB,YAAY;;;;;;;0CACxC,8OAAC;;oCAAE;oCAAO,kBAAkB,cAAc;;;;;;;0CAC1C,8OAAC;;oCAAE;oCAAO,kBAAkB,UAAU,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}, {"offset": {"line": 8141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/ConsistencyVideoGenerator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, Settings, Shield, Image, Sparkles, Clock, CheckCircle, AlertTriangle, Loader2, BarChart3 } from 'lucide-react'\nimport ConsistencyValidator from './ConsistencyValidator'\n\ninterface ConsistencyVideoGeneratorProps {\n  episodeId: string\n  episodeTitle: string\n  onVideoGenerated?: (videoData: any) => void\n}\n\nexport default function ConsistencyVideoGenerator({ \n  episodeId, \n  episodeTitle, \n  onVideoGenerated \n}: ConsistencyVideoGeneratorProps) {\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [generationResult, setGenerationResult] = useState<any>(null)\n  const [error, setError] = useState<string | null>(null)\n\n  // 一致性验证相关状态\n  const [showValidator, setShowValidator] = useState(false)\n  const [validationResults, setValidationResults] = useState<any>(null)\n  \n  // 一致性配置\n  const [consistencyMode, setConsistencyMode] = useState<'strict' | 'balanced' | 'creative'>('balanced')\n  const [includeReferenceImages, setIncludeReferenceImages] = useState(true)\n  const [style, setStyle] = useState<'cinematic' | 'documentary' | 'artistic'>('cinematic')\n  const [quality, setQuality] = useState<'standard' | 'high' | 'cinematic'>('high')\n  const [customEnhancement, setCustomEnhancement] = useState('')\n\n  // 生成一致性视频\n  const handleGenerateVideo = async () => {\n    try {\n      setIsGenerating(true)\n      setError(null)\n      setGenerationResult(null)\n\n      console.log('🎬 开始生成一致性视频...')\n\n      const response = await fetch('/api/ai/generate-video-with-consistency', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          episodeId,\n          consistencyMode,\n          includeReferenceImages,\n          style,\n          quality,\n          customEnhancement: customEnhancement.trim() || undefined\n        })\n      })\n\n      const result = await response.json()\n\n      if (!response.ok) {\n        throw new Error(result.error || '生成失败')\n      }\n\n      if (result.success) {\n        setGenerationResult(result.data)\n        onVideoGenerated?.(result.data)\n        setShowValidator(true) // 自动显示验证器\n        console.log('✅ 一致性视频生成成功:', result.data)\n      } else {\n        throw new Error(result.error || '生成失败')\n      }\n\n    } catch (error) {\n      console.error('❌ 一致性视频生成失败:', error)\n      setError(error instanceof Error ? error.message : '生成失败，请重试')\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  return (\n    <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n      {/* 标题 */}\n      <div className=\"flex items-center mb-6\">\n        <Shield className=\"text-purple-600 mr-3\" size={24} />\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900\">一致性视频生成</h3>\n          <p className=\"text-sm text-gray-600\">为《{episodeTitle}》生成具有角色一致性约束的视频</p>\n        </div>\n      </div>\n\n      {/* 一致性配置 */}\n      <div className=\"space-y-6 mb-6\">\n        {/* 一致性模式 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Shield className=\"inline mr-1\" size={14} />\n            一致性模式\n          </label>\n          <div className=\"grid grid-cols-3 gap-3\">\n            {[\n              { value: 'strict', label: '严格模式', desc: '零容忍偏差', color: 'red' },\n              { value: 'balanced', label: '平衡模式', desc: '高保真度', color: 'blue' },\n              { value: 'creative', label: '创意模式', desc: '艺术自由', color: 'green' }\n            ].map(mode => (\n              <button\n                key={mode.value}\n                onClick={() => setConsistencyMode(mode.value as any)}\n                className={`p-3 rounded-lg border text-left transition-colors ${\n                  consistencyMode === mode.value\n                    ? `border-${mode.color}-500 bg-${mode.color}-50 text-${mode.color}-700`\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n              >\n                <div className=\"font-medium text-sm\">{mode.label}</div>\n                <div className=\"text-xs text-gray-500\">{mode.desc}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* 参考图像 */}\n        <div>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={includeReferenceImages}\n              onChange={(e) => setIncludeReferenceImages(e.target.checked)}\n              className=\"rounded border-gray-300 text-purple-600 focus:ring-purple-500\"\n            />\n            <Image className=\"ml-2 mr-1\" size={14} />\n            <span className=\"text-sm font-medium text-gray-700\">使用参考图像约束</span>\n          </label>\n          <p className=\"text-xs text-gray-500 ml-6\">\n            启用后将使用项目角色库中的参考图像进行视觉一致性约束\n          </p>\n        </div>\n\n        {/* 视频风格 */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">视频风格</label>\n            <select\n              value={style}\n              onChange={(e) => setStyle(e.target.value as any)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500\"\n            >\n              <option value=\"cinematic\">电影级</option>\n              <option value=\"documentary\">纪录片</option>\n              <option value=\"artistic\">艺术风格</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">视频质量</label>\n            <select\n              value={quality}\n              onChange={(e) => setQuality(e.target.value as any)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500\"\n            >\n              <option value=\"standard\">标准质量</option>\n              <option value=\"high\">高质量</option>\n              <option value=\"cinematic\">电影级质量</option>\n            </select>\n          </div>\n        </div>\n\n        {/* 自定义增强 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Sparkles className=\"inline mr-1\" size={14} />\n            自定义增强要求\n          </label>\n          <textarea\n            value={customEnhancement}\n            onChange={(e) => setCustomEnhancement(e.target.value)}\n            placeholder=\"例如：强调角色表情变化，增加特写镜头...\"\n            className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500\"\n            rows={3}\n          />\n        </div>\n      </div>\n\n      {/* 生成按钮 */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"text-sm text-gray-500\">\n          <Clock className=\"inline mr-1\" size={14} />\n          预计生成时间：2-5分钟\n        </div>\n        <button\n          onClick={handleGenerateVideo}\n          disabled={isGenerating}\n          className={`inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors ${\n            isGenerating\n              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n              : 'bg-purple-600 text-white hover:bg-purple-700'\n          }`}\n        >\n          {isGenerating ? (\n            <>\n              <Loader2 className=\"animate-spin mr-2\" size={16} />\n              生成中...\n            </>\n          ) : (\n            <>\n              <Play className=\"mr-2\" size={16} />\n              生成一致性视频\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n          <div className=\"flex items-center\">\n            <AlertTriangle className=\"text-red-500 mr-2\" size={16} />\n            <span className=\"text-sm text-red-700\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* 生成结果 */}\n      {generationResult && (\n        <div className=\"mt-6 p-4 bg-green-50 border border-green-200 rounded-md\">\n          <div className=\"flex items-center mb-3\">\n            <CheckCircle className=\"text-green-600 mr-2\" size={20} />\n            <h4 className=\"font-medium text-green-800\">一致性视频生成成功</h4>\n          </div>\n          \n          <div className=\"space-y-2 text-sm\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <span className=\"text-gray-600\">生成ID：</span>\n                <span className=\"font-mono text-xs\">{generationResult.generationId}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">一致性模式：</span>\n                <span className=\"font-medium\">{generationResult.consistencyInfo.mode}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">角色数量：</span>\n                <span className=\"font-medium\">{generationResult.consistencyInfo.characterCount}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">参考图像：</span>\n                <span className=\"font-medium\">{generationResult.consistencyInfo.referenceImageCount} 个</span>\n              </div>\n            </div>\n            \n            <div className=\"mt-3\">\n              <span className=\"text-gray-600\">一致性特性：</span>\n              <div className=\"flex flex-wrap gap-2 mt-1\">\n                {Object.entries(generationResult.consistencyInfo.consistencyFeatures).map(([key, enabled]) => (\n                  enabled && (\n                    <span key={key} className=\"inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                      <CheckCircle className=\"mr-1\" size={10} />\n                      {key === 'characterDNA' ? '角色DNA' :\n                       key === 'sceneDNA' ? '场景DNA' :\n                       key === 'dualConstraints' ? '双重约束' :\n                       key === 'validationProtocol' ? '验证协议' : key}\n                    </span>\n                  )\n                ))}\n              </div>\n            </div>\n\n            <div className=\"mt-3 p-2 bg-white rounded border\">\n              <span className=\"text-gray-600 text-xs\">生成的提示词长度：</span>\n              <span className=\"font-medium text-xs\">{generationResult.prompt.length} 字符</span>\n              <details className=\"mt-1\">\n                <summary className=\"text-xs text-blue-600 cursor-pointer\">查看提示词</summary>\n                <pre className=\"text-xs text-gray-600 mt-1 whitespace-pre-wrap max-h-32 overflow-y-auto\">\n                  {generationResult.prompt}\n                </pre>\n              </details>\n            </div>\n\n            {/* 一致性验证控制 */}\n            <div className=\"mt-3 flex items-center justify-between\">\n              <span className=\"text-xs text-gray-600\">一致性验证：</span>\n              <button\n                onClick={() => setShowValidator(!showValidator)}\n                className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${\n                  showValidator\n                    ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                <BarChart3 className=\"mr-1\" size={12} />\n                {showValidator ? '隐藏验证器' : '显示验证器'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 一致性验证器 */}\n      {generationResult && showValidator && (\n        <div className=\"mt-6\">\n          <ConsistencyValidator\n            generationId={generationResult.generationId}\n            onValidationComplete={(results) => {\n              setValidationResults(results)\n              console.log('✅ 一致性验证完成:', results)\n            }}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAYe,SAAS,0BAA0B,EAChD,SAAS,EACT,YAAY,EACZ,gBAAgB,EACe;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,YAAY;IACZ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEhE,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAC3F,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,UAAU;IACV,MAAM,sBAAsB;QAC1B,IAAI;YACF,gBAAgB;YAChB,SAAS;YACT,oBAAoB;YAEpB,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,2CAA2C;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;oBACA;oBACA,mBAAmB,kBAAkB,IAAI,MAAM;gBACjD;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,oBAAoB,OAAO,IAAI;gBAC/B,mBAAmB,OAAO,IAAI;gBAC9B,iBAAiB,MAAM,UAAU;;gBACjC,QAAQ,GAAG,CAAC,gBAAgB,OAAO,IAAI;YACzC,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAuB,MAAM;;;;;;kCAC/C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;;oCAAwB;oCAAG;oCAAa;;;;;;;;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;kDACf,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAc,MAAM;;;;;;oCAAM;;;;;;;0CAG9C,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAU,OAAO;wCAAQ,MAAM;wCAAS,OAAO;oCAAM;oCAC9D;wCAAE,OAAO;wCAAY,OAAO;wCAAQ,MAAM;wCAAQ,OAAO;oCAAO;oCAChE;wCAAE,OAAO;wCAAY,OAAO;wCAAQ,MAAM;wCAAQ,OAAO;oCAAQ;iCAClE,CAAC,GAAG,CAAC,CAAA,qBACJ,8OAAC;wCAEC,SAAS,IAAM,mBAAmB,KAAK,KAAK;wCAC5C,WAAW,CAAC,kDAAkD,EAC5D,oBAAoB,KAAK,KAAK,GAC1B,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,GACrE,yCACJ;;0DAEF,8OAAC;gDAAI,WAAU;0DAAuB,KAAK,KAAK;;;;;;0DAChD,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;uCAT5C,KAAK,KAAK;;;;;;;;;;;;;;;;kCAgBvB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,OAAO;wCAC3D,WAAU;;;;;;kDAEZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;wCAAY,MAAM;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAM5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;0CAI7B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC1C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;kDACf,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAc,MAAM;;;;;;oCAAM;;;;;;;0CAGhD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,aAAY;gCACZ,WAAU;gCACV,MAAM;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;gCAAc,MAAM;;;;;;4BAAM;;;;;;;kCAG7C,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,4EAA4E,EACtF,eACI,iDACA,gDACJ;kCAED,6BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;oCAAoB,MAAM;;;;;;gCAAM;;yDAIrD;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;;;;;;;;YAQ1C,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;4BAAoB,MAAM;;;;;;sCACnD,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM7C,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;gCAAsB,MAAM;;;;;;0CACnD,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAqB,iBAAiB,YAAY;;;;;;;;;;;;kDAEpE,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAe,iBAAiB,eAAe,CAAC,IAAI;;;;;;;;;;;;kDAEtE,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAe,iBAAiB,eAAe,CAAC,cAAc;;;;;;;;;;;;kDAEhF,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAe,iBAAiB,eAAe,CAAC,mBAAmB;oDAAC;;;;;;;;;;;;;;;;;;;0CAIxF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,iBAAiB,eAAe,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,GACvF,yBACE,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDACnC,QAAQ,iBAAiB,UACzB,QAAQ,aAAa,UACrB,QAAQ,oBAAoB,SAC5B,QAAQ,uBAAuB,SAAS;;+CALhC;;;;;;;;;;;;;;;;0CAYnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAuB,iBAAiB,MAAM,CAAC,MAAM;4CAAC;;;;;;;kDACtE,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAQ,WAAU;0DAAuC;;;;;;0DAC1D,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCACC,SAAS,IAAM,iBAAiB,CAAC;wCACjC,WAAW,CAAC,oFAAoF,EAC9F,gBACI,sDACA,+CACJ;;0DAEF,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CACjC,gBAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;YAQpC,oBAAoB,+BACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,UAAoB;oBACnB,cAAc,iBAAiB,YAAY;oBAC3C,sBAAsB,CAAC;wBACrB,qBAAqB;wBACrB,QAAQ,GAAG,CAAC,cAAc;oBAC5B;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}