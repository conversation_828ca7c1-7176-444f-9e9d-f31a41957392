{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/promises.js"], "sourcesContent": ["var _ = require(\"underscore\");\nvar bluebird = require(\"bluebird/js/release/promise\")();\n\nexports.defer = defer;\nexports.when = bluebird.resolve;\nexports.resolve = bluebird.resolve;\nexports.all = bluebird.all;\nexports.props = bluebird.props;\nexports.reject = bluebird.reject;\nexports.promisify = bluebird.promisify;\nexports.mapSeries = bluebird.mapSeries;\nexports.attempt = bluebird.attempt;\n\nexports.nfcall = function(func) {\n    var args = Array.prototype.slice.call(arguments, 1);\n    var promisedFunc = bluebird.promisify(func);\n    return promisedFunc.apply(null, args);\n};\n\nbluebird.prototype.fail = bluebird.prototype.caught;\n\nbluebird.prototype.also = function(func) {\n    return this.then(function(value) {\n        var returnValue = _.extend({}, value, func(value));\n        return bluebird.props(returnValue);\n    });\n};\n\nfunction defer() {\n    var resolve;\n    var reject;\n    var promise = new bluebird.Promise(function(resolveArg, rejectArg) {\n        resolve = resolveArg;\n        reject = rejectArg;\n    });\n\n    return {\n        resolve: resolve,\n        reject: reject,\n        promise: promise\n    };\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,WAAW;AAEf,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG,SAAS,OAAO;AAC/B,QAAQ,OAAO,GAAG,SAAS,OAAO;AAClC,QAAQ,GAAG,GAAG,SAAS,GAAG;AAC1B,QAAQ,KAAK,GAAG,SAAS,KAAK;AAC9B,QAAQ,MAAM,GAAG,SAAS,MAAM;AAChC,QAAQ,SAAS,GAAG,SAAS,SAAS;AACtC,QAAQ,SAAS,GAAG,SAAS,SAAS;AACtC,QAAQ,OAAO,GAAG,SAAS,OAAO;AAElC,QAAQ,MAAM,GAAG,SAAS,IAAI;IAC1B,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACjD,IAAI,eAAe,SAAS,SAAS,CAAC;IACtC,OAAO,aAAa,KAAK,CAAC,MAAM;AACpC;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAS,SAAS,CAAC,MAAM;AAEnD,SAAS,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI;IACnC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK;QAC3B,IAAI,cAAc,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,KAAK;QAC3C,OAAO,SAAS,KAAK,CAAC;IAC1B;AACJ;AAEA,SAAS;IACL,IAAI;IACJ,IAAI;IACJ,IAAI,UAAU,IAAI,SAAS,OAAO,CAAC,SAAS,UAAU,EAAE,SAAS;QAC7D,UAAU;QACV,SAAS;IACb;IAEA,OAAO;QACH,SAAS;QACT,QAAQ;QACR,SAAS;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/documents.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar types = exports.types = {\n    document: \"document\",\n    paragraph: \"paragraph\",\n    run: \"run\",\n    text: \"text\",\n    tab: \"tab\",\n    checkbox: \"checkbox\",\n    hyperlink: \"hyperlink\",\n    noteReference: \"noteReference\",\n    image: \"image\",\n    note: \"note\",\n    commentReference: \"commentReference\",\n    comment: \"comment\",\n    table: \"table\",\n    tableRow: \"tableRow\",\n    tableCell: \"tableCell\",\n    \"break\": \"break\",\n    bookmarkStart: \"bookmarkStart\"\n};\n\nfunction Document(children, options) {\n    options = options || {};\n    return {\n        type: types.document,\n        children: children,\n        notes: options.notes || new Notes({}),\n        comments: options.comments || []\n    };\n}\n\nfunction Paragraph(children, properties) {\n    properties = properties || {};\n    var indent = properties.indent || {};\n    return {\n        type: types.paragraph,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null,\n        numbering: properties.numbering || null,\n        alignment: properties.alignment || null,\n        indent: {\n            start: indent.start || null,\n            end: indent.end || null,\n            firstLine: indent.firstLine || null,\n            hanging: indent.hanging || null\n        }\n    };\n}\n\nfunction Run(children, properties) {\n    properties = properties || {};\n    return {\n        type: types.run,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null,\n        isBold: !!properties.isBold,\n        isUnderline: !!properties.isUnderline,\n        isItalic: !!properties.isItalic,\n        isStrikethrough: !!properties.isStrikethrough,\n        isAllCaps: !!properties.isAllCaps,\n        isSmallCaps: !!properties.isSmallCaps,\n        verticalAlignment: properties.verticalAlignment || verticalAlignment.baseline,\n        font: properties.font || null,\n        fontSize: properties.fontSize || null,\n        highlight: properties.highlight || null\n    };\n}\n\nvar verticalAlignment = {\n    baseline: \"baseline\",\n    superscript: \"superscript\",\n    subscript: \"subscript\"\n};\n\nfunction Text(value) {\n    return {\n        type: types.text,\n        value: value\n    };\n}\n\nfunction Tab() {\n    return {\n        type: types.tab\n    };\n}\n\nfunction Checkbox(options) {\n    return {\n        type: types.checkbox,\n        checked: options.checked\n    };\n}\n\nfunction Hyperlink(children, options) {\n    return {\n        type: types.hyperlink,\n        children: children,\n        href: options.href,\n        anchor: options.anchor,\n        targetFrame: options.targetFrame\n    };\n}\n\nfunction NoteReference(options) {\n    return {\n        type: types.noteReference,\n        noteType: options.noteType,\n        noteId: options.noteId\n    };\n}\n\nfunction Notes(notes) {\n    this._notes = _.indexBy(notes, function(note) {\n        return noteKey(note.noteType, note.noteId);\n    });\n}\n\nNotes.prototype.resolve = function(reference) {\n    return this.findNoteByKey(noteKey(reference.noteType, reference.noteId));\n};\n\nNotes.prototype.findNoteByKey = function(key) {\n    return this._notes[key] || null;\n};\n\nfunction Note(options) {\n    return {\n        type: types.note,\n        noteType: options.noteType,\n        noteId: options.noteId,\n        body: options.body\n    };\n}\n\nfunction commentReference(options) {\n    return {\n        type: types.commentReference,\n        commentId: options.commentId\n    };\n}\n\nfunction comment(options) {\n    return {\n        type: types.comment,\n        commentId: options.commentId,\n        body: options.body,\n        authorName: options.authorName,\n        authorInitials: options.authorInitials\n    };\n}\n\nfunction noteKey(noteType, id) {\n    return noteType + \"-\" + id;\n}\n\nfunction Image(options) {\n    return {\n        type: types.image,\n        // `read` is retained for backwards compatibility, but other read\n        // methods should be preferred.\n        read: function(encoding) {\n            if (encoding) {\n                return options.readImage(encoding);\n            } else {\n                return options.readImage().then(function(arrayBuffer) {\n                    return Buffer.from(arrayBuffer);\n                });\n            }\n        },\n        readAsArrayBuffer: function() {\n            return options.readImage();\n        },\n        readAsBase64String: function() {\n            return options.readImage(\"base64\");\n        },\n        readAsBuffer: function() {\n            return options.readImage().then(function(arrayBuffer) {\n                return Buffer.from(arrayBuffer);\n            });\n        },\n        altText: options.altText,\n        contentType: options.contentType\n    };\n}\n\nfunction Table(children, properties) {\n    properties = properties || {};\n    return {\n        type: types.table,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null\n    };\n}\n\nfunction TableRow(children, options) {\n    options = options || {};\n    return {\n        type: types.tableRow,\n        children: children,\n        isHeader: options.isHeader || false\n    };\n}\n\nfunction TableCell(children, options) {\n    options = options || {};\n    return {\n        type: types.tableCell,\n        children: children,\n        colSpan: options.colSpan == null ? 1 : options.colSpan,\n        rowSpan: options.rowSpan == null ? 1 : options.rowSpan\n    };\n}\n\nfunction Break(breakType) {\n    return {\n        type: types[\"break\"],\n        breakType: breakType\n    };\n}\n\nfunction BookmarkStart(options) {\n    return {\n        type: types.bookmarkStart,\n        name: options.name\n    };\n}\n\nexports.document = exports.Document = Document;\nexports.paragraph = exports.Paragraph = Paragraph;\nexports.run = exports.Run = Run;\nexports.text = exports.Text = Text;\nexports.tab = exports.Tab = Tab;\nexports.checkbox = exports.Checkbox = Checkbox;\nexports.Hyperlink = Hyperlink;\nexports.noteReference = exports.NoteReference = NoteReference;\nexports.Notes = Notes;\nexports.Note = Note;\nexports.commentReference = commentReference;\nexports.comment = comment;\nexports.Image = Image;\nexports.Table = Table;\nexports.TableRow = TableRow;\nexports.TableCell = TableCell;\nexports.lineBreak = Break(\"line\");\nexports.pageBreak = Break(\"page\");\nexports.columnBreak = Break(\"column\");\nexports.BookmarkStart = BookmarkStart;\n\nexports.verticalAlignment = verticalAlignment;\n"], "names": [], "mappings": "AAyK2B;AAzK3B,IAAI;AAEJ,IAAI,QAAQ,QAAQ,KAAK,GAAG;IACxB,UAAU;IACV,WAAW;IACX,KAAK;IACL,MAAM;IACN,KAAK;IACL,UAAU;IACV,WAAW;IACX,eAAe;IACf,OAAO;IACP,MAAM;IACN,kBAAkB;IAClB,SAAS;IACT,OAAO;IACP,UAAU;IACV,WAAW;IACX,SAAS;IACT,eAAe;AACnB;AAEA,SAAS,SAAS,QAAQ,EAAE,OAAO;IAC/B,UAAU,WAAW,CAAC;IACtB,OAAO;QACH,MAAM,MAAM,QAAQ;QACpB,UAAU;QACV,OAAO,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC;QACnC,UAAU,QAAQ,QAAQ,IAAI,EAAE;IACpC;AACJ;AAEA,SAAS,UAAU,QAAQ,EAAE,UAAU;IACnC,aAAa,cAAc,CAAC;IAC5B,IAAI,SAAS,WAAW,MAAM,IAAI,CAAC;IACnC,OAAO;QACH,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,SAAS,WAAW,OAAO,IAAI;QAC/B,WAAW,WAAW,SAAS,IAAI;QACnC,WAAW,WAAW,SAAS,IAAI;QACnC,WAAW,WAAW,SAAS,IAAI;QACnC,QAAQ;YACJ,OAAO,OAAO,KAAK,IAAI;YACvB,KAAK,OAAO,GAAG,IAAI;YACnB,WAAW,OAAO,SAAS,IAAI;YAC/B,SAAS,OAAO,OAAO,IAAI;QAC/B;IACJ;AACJ;AAEA,SAAS,IAAI,QAAQ,EAAE,UAAU;IAC7B,aAAa,cAAc,CAAC;IAC5B,OAAO;QACH,MAAM,MAAM,GAAG;QACf,UAAU;QACV,SAAS,WAAW,OAAO,IAAI;QAC/B,WAAW,WAAW,SAAS,IAAI;QACnC,QAAQ,CAAC,CAAC,WAAW,MAAM;QAC3B,aAAa,CAAC,CAAC,WAAW,WAAW;QACrC,UAAU,CAAC,CAAC,WAAW,QAAQ;QAC/B,iBAAiB,CAAC,CAAC,WAAW,eAAe;QAC7C,WAAW,CAAC,CAAC,WAAW,SAAS;QACjC,aAAa,CAAC,CAAC,WAAW,WAAW;QACrC,mBAAmB,WAAW,iBAAiB,IAAI,kBAAkB,QAAQ;QAC7E,MAAM,WAAW,IAAI,IAAI;QACzB,UAAU,WAAW,QAAQ,IAAI;QACjC,WAAW,WAAW,SAAS,IAAI;IACvC;AACJ;AAEA,IAAI,oBAAoB;IACpB,UAAU;IACV,aAAa;IACb,WAAW;AACf;AAEA,SAAS,KAAK,KAAK;IACf,OAAO;QACH,MAAM,MAAM,IAAI;QAChB,OAAO;IACX;AACJ;AAEA,SAAS;IACL,OAAO;QACH,MAAM,MAAM,GAAG;IACnB;AACJ;AAEA,SAAS,SAAS,OAAO;IACrB,OAAO;QACH,MAAM,MAAM,QAAQ;QACpB,SAAS,QAAQ,OAAO;IAC5B;AACJ;AAEA,SAAS,UAAU,QAAQ,EAAE,OAAO;IAChC,OAAO;QACH,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,MAAM,QAAQ,IAAI;QAClB,QAAQ,QAAQ,MAAM;QACtB,aAAa,QAAQ,WAAW;IACpC;AACJ;AAEA,SAAS,cAAc,OAAO;IAC1B,OAAO;QACH,MAAM,MAAM,aAAa;QACzB,UAAU,QAAQ,QAAQ;QAC1B,QAAQ,QAAQ,MAAM;IAC1B;AACJ;AAEA,SAAS,MAAM,KAAK;IAChB,IAAI,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,OAAO,SAAS,IAAI;QACxC,OAAO,QAAQ,KAAK,QAAQ,EAAE,KAAK,MAAM;IAC7C;AACJ;AAEA,MAAM,SAAS,CAAC,OAAO,GAAG,SAAS,SAAS;IACxC,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,QAAQ,EAAE,UAAU,MAAM;AAC1E;AAEA,MAAM,SAAS,CAAC,aAAa,GAAG,SAAS,GAAG;IACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI;AAC/B;AAEA,SAAS,KAAK,OAAO;IACjB,OAAO;QACH,MAAM,MAAM,IAAI;QAChB,UAAU,QAAQ,QAAQ;QAC1B,QAAQ,QAAQ,MAAM;QACtB,MAAM,QAAQ,IAAI;IACtB;AACJ;AAEA,SAAS,iBAAiB,OAAO;IAC7B,OAAO;QACH,MAAM,MAAM,gBAAgB;QAC5B,WAAW,QAAQ,SAAS;IAChC;AACJ;AAEA,SAAS,QAAQ,OAAO;IACpB,OAAO;QACH,MAAM,MAAM,OAAO;QACnB,WAAW,QAAQ,SAAS;QAC5B,MAAM,QAAQ,IAAI;QAClB,YAAY,QAAQ,UAAU;QAC9B,gBAAgB,QAAQ,cAAc;IAC1C;AACJ;AAEA,SAAS,QAAQ,QAAQ,EAAE,EAAE;IACzB,OAAO,WAAW,MAAM;AAC5B;AAEA,SAAS,MAAM,OAAO;IAClB,OAAO;QACH,MAAM,MAAM,KAAK;QACjB,iEAAiE;QACjE,+BAA+B;QAC/B,MAAM,SAAS,QAAQ;YACnB,IAAI,UAAU;gBACV,OAAO,QAAQ,SAAS,CAAC;YAC7B,OAAO;gBACH,OAAO,QAAQ,SAAS,GAAG,IAAI,CAAC,SAAS,WAAW;oBAChD,OAAO,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC;gBACvB;YACJ;QACJ;QACA,mBAAmB;YACf,OAAO,QAAQ,SAAS;QAC5B;QACA,oBAAoB;YAChB,OAAO,QAAQ,SAAS,CAAC;QAC7B;QACA,cAAc;YACV,OAAO,QAAQ,SAAS,GAAG,IAAI,CAAC,SAAS,WAAW;gBAChD,OAAO,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC;YACvB;QACJ;QACA,SAAS,QAAQ,OAAO;QACxB,aAAa,QAAQ,WAAW;IACpC;AACJ;AAEA,SAAS,MAAM,QAAQ,EAAE,UAAU;IAC/B,aAAa,cAAc,CAAC;IAC5B,OAAO;QACH,MAAM,MAAM,KAAK;QACjB,UAAU;QACV,SAAS,WAAW,OAAO,IAAI;QAC/B,WAAW,WAAW,SAAS,IAAI;IACvC;AACJ;AAEA,SAAS,SAAS,QAAQ,EAAE,OAAO;IAC/B,UAAU,WAAW,CAAC;IACtB,OAAO;QACH,MAAM,MAAM,QAAQ;QACpB,UAAU;QACV,UAAU,QAAQ,QAAQ,IAAI;IAClC;AACJ;AAEA,SAAS,UAAU,QAAQ,EAAE,OAAO;IAChC,UAAU,WAAW,CAAC;IACtB,OAAO;QACH,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,SAAS,QAAQ,OAAO,IAAI,OAAO,IAAI,QAAQ,OAAO;QACtD,SAAS,QAAQ,OAAO,IAAI,OAAO,IAAI,QAAQ,OAAO;IAC1D;AACJ;AAEA,SAAS,MAAM,SAAS;IACpB,OAAO;QACH,MAAM,KAAK,CAAC,QAAQ;QACpB,WAAW;IACf;AACJ;AAEA,SAAS,cAAc,OAAO;IAC1B,OAAO;QACH,MAAM,MAAM,aAAa;QACzB,MAAM,QAAQ,IAAI;IACtB;AACJ;AAEA,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG;AACtC,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG;AACxC,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG;AAC5B,QAAQ,IAAI,GAAG,QAAQ,IAAI,GAAG;AAC9B,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG;AAC5B,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG;AACtC,QAAQ,SAAS,GAAG;AACpB,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG;AAChD,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG;AACf,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,OAAO,GAAG;AAClB,QAAQ,KAAK,GAAG;AAChB,QAAQ,KAAK,GAAG;AAChB,QAAQ,QAAQ,GAAG;AACnB,QAAQ,SAAS,GAAG;AACpB,QAAQ,SAAS,GAAG,MAAM;AAC1B,QAAQ,SAAS,GAAG,MAAM;AAC1B,QAAQ,WAAW,GAAG,MAAM;AAC5B,QAAQ,aAAa,GAAG;AAExB,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/results.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\n\nexports.Result = Result;\nexports.success = success;\nexports.warning = warning;\nexports.error = error;\n\n\nfunction Result(value, messages) {\n    this.value = value;\n    this.messages = messages || [];\n}\n\nResult.prototype.map = function(func) {\n    return new Result(func(this.value), this.messages);\n};\n\nResult.prototype.flatMap = function(func) {\n    var funcResult = func(this.value);\n    return new Result(funcResult.value, combineMessages([this, funcResult]));\n};\n\nResult.prototype.flatMapThen = function(func) {\n    var that = this;\n    return func(this.value).then(function(otherResult) {\n        return new Result(otherResult.value, combineMessages([that, otherResult]));\n    });\n};\n\nResult.combine = function(results) {\n    var values = _.flatten(_.pluck(results, \"value\"));\n    var messages = combineMessages(results);\n    return new Result(values, messages);\n};\n\nfunction success(value) {\n    return new Result(value, []);\n}\n\nfunction warning(message) {\n    return {\n        type: \"warning\",\n        message: message\n    };\n}\n\nfunction error(exception) {\n    return {\n        type: \"error\",\n        message: exception.message,\n        error: exception\n    };\n}\n\nfunction combineMessages(results) {\n    var messages = [];\n    _.flatten(_.pluck(results, \"messages\"), true).forEach(function(message) {\n        if (!containsMessage(messages, message)) {\n            messages.push(message);\n        }\n    });\n    return messages;\n}\n\nfunction containsMessage(messages, message) {\n    return _.find(messages, isSameMessage.bind(null, message)) !== undefined;\n}\n\nfunction isSameMessage(first, second) {\n    return first.type === second.type && first.message === second.message;\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAGJ,QAAQ,MAAM,GAAG;AACjB,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG;AAClB,QAAQ,KAAK,GAAG;AAGhB,SAAS,OAAO,KAAK,EAAE,QAAQ;IAC3B,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE;AAClC;AAEA,OAAO,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IAChC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ;AACrD;AAEA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI;IACpC,IAAI,aAAa,KAAK,IAAI,CAAC,KAAK;IAChC,OAAO,IAAI,OAAO,WAAW,KAAK,EAAE,gBAAgB;QAAC,IAAI;QAAE;KAAW;AAC1E;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,IAAI;IACxC,IAAI,OAAO,IAAI;IACf,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,WAAW;QAC7C,OAAO,IAAI,OAAO,YAAY,KAAK,EAAE,gBAAgB;YAAC;YAAM;SAAY;IAC5E;AACJ;AAEA,OAAO,OAAO,GAAG,SAAS,OAAO;IAC7B,IAAI,SAAS,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS;IACxC,IAAI,WAAW,gBAAgB;IAC/B,OAAO,IAAI,OAAO,QAAQ;AAC9B;AAEA,SAAS,QAAQ,KAAK;IAClB,OAAO,IAAI,OAAO,OAAO,EAAE;AAC/B;AAEA,SAAS,QAAQ,OAAO;IACpB,OAAO;QACH,MAAM;QACN,SAAS;IACb;AACJ;AAEA,SAAS,MAAM,SAAS;IACpB,OAAO;QACH,MAAM;QACN,SAAS,UAAU,OAAO;QAC1B,OAAO;IACX;AACJ;AAEA,SAAS,gBAAgB,OAAO;IAC5B,IAAI,WAAW,EAAE;IACjB,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,OAAO,CAAC,SAAS,OAAO;QAClE,IAAI,CAAC,gBAAgB,UAAU,UAAU;YACrC,SAAS,IAAI,CAAC;QAClB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,OAAO,EAAE,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC,MAAM,cAAc;AACnE;AAEA,SAAS,cAAc,KAAK,EAAE,MAAM;IAChC,OAAO,MAAM,IAAI,KAAK,OAAO,IAAI,IAAI,MAAM,OAAO,KAAK,OAAO,OAAO;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/zipfile.js"], "sourcesContent": ["var base64js = require(\"base64-js\");\nvar JSZip = require(\"jszip\");\n\nexports.openArrayBuffer = openArrayBuffer;\nexports.splitPath = splitPath;\nexports.joinPath = joinPath;\n\nfunction openArrayBuffer(arrayBuffer) {\n    return JSZip.loadAsync(arrayBuffer).then(function(zipFile) {\n        function exists(name) {\n            return zipFile.file(name) !== null;\n        }\n\n        function read(name, encoding) {\n            return zipFile.file(name).async(\"uint8array\").then(function(array) {\n                if (encoding === \"base64\") {\n                    return base64js.fromByteArray(array);\n                } else if (encoding) {\n                    var decoder = new TextDecoder(encoding);\n                    return decoder.decode(array);\n                } else {\n                    return array;\n                }\n            });\n        }\n\n        function write(name, contents) {\n            zipFile.file(name, contents);\n        }\n\n        function toArrayBuffer() {\n            return zipFile.generateAsync({type: \"arraybuffer\"});\n        }\n\n        return {\n            exists: exists,\n            read: read,\n            write: write,\n            toArrayBuffer: toArrayBuffer\n        };\n    });\n}\n\nfunction splitPath(path) {\n    var lastIndex = path.lastIndexOf(\"/\");\n    if (lastIndex === -1) {\n        return {dirname: \"\", basename: path};\n    } else {\n        return {\n            dirname: path.substring(0, lastIndex),\n            basename: path.substring(lastIndex + 1)\n        };\n    }\n}\n\nfunction joinPath() {\n    var nonEmptyPaths = Array.prototype.filter.call(arguments, function(path) {\n        return path;\n    });\n\n    var relevantPaths = [];\n\n    nonEmptyPaths.forEach(function(path) {\n        if (/^\\//.test(path)) {\n            relevantPaths = [path];\n        } else {\n            relevantPaths.push(path);\n        }\n    });\n\n    return relevantPaths.join(\"/\");\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,QAAQ,eAAe,GAAG;AAC1B,QAAQ,SAAS,GAAG;AACpB,QAAQ,QAAQ,GAAG;AAEnB,SAAS,gBAAgB,WAAW;IAChC,OAAO,MAAM,SAAS,CAAC,aAAa,IAAI,CAAC,SAAS,OAAO;QACrD,SAAS,OAAO,IAAI;YAChB,OAAO,QAAQ,IAAI,CAAC,UAAU;QAClC;QAEA,SAAS,KAAK,IAAI,EAAE,QAAQ;YACxB,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAK,CAAC,cAAc,IAAI,CAAC,SAAS,KAAK;gBAC7D,IAAI,aAAa,UAAU;oBACvB,OAAO,SAAS,aAAa,CAAC;gBAClC,OAAO,IAAI,UAAU;oBACjB,IAAI,UAAU,IAAI,YAAY;oBAC9B,OAAO,QAAQ,MAAM,CAAC;gBAC1B,OAAO;oBACH,OAAO;gBACX;YACJ;QACJ;QAEA,SAAS,MAAM,IAAI,EAAE,QAAQ;YACzB,QAAQ,IAAI,CAAC,MAAM;QACvB;QAEA,SAAS;YACL,OAAO,QAAQ,aAAa,CAAC;gBAAC,MAAM;YAAa;QACrD;QAEA,OAAO;YACH,QAAQ;YACR,MAAM;YACN,OAAO;YACP,eAAe;QACnB;IACJ;AACJ;AAEA,SAAS,UAAU,IAAI;IACnB,IAAI,YAAY,KAAK,WAAW,CAAC;IACjC,IAAI,cAAc,CAAC,GAAG;QAClB,OAAO;YAAC,SAAS;YAAI,UAAU;QAAI;IACvC,OAAO;QACH,OAAO;YACH,SAAS,KAAK,SAAS,CAAC,GAAG;YAC3B,UAAU,KAAK,SAAS,CAAC,YAAY;QACzC;IACJ;AACJ;AAEA,SAAS;IACL,IAAI,gBAAgB,MAAM,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,SAAS,IAAI;QACpE,OAAO;IACX;IAEA,IAAI,gBAAgB,EAAE;IAEtB,cAAc,OAAO,CAAC,SAAS,IAAI;QAC/B,IAAI,MAAM,IAAI,CAAC,OAAO;YAClB,gBAAgB;gBAAC;aAAK;QAC1B,OAAO;YACH,cAAc,IAAI,CAAC;QACvB;IACJ;IAEA,OAAO,cAAc,IAAI,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/xml/nodes.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\n\nexports.Element = Element;\nexports.element = function(name, attributes, children) {\n    return new Element(name, attributes, children);\n};\nexports.text = function(value) {\n    return {\n        type: \"text\",\n        value: value\n    };\n};\n\n\nvar emptyElement = exports.emptyElement = {\n    first: function() {\n        return null;\n    },\n    firstOrEmpty: function() {\n        return emptyElement;\n    },\n    attributes: {},\n    children: []\n};\n\nfunction Element(name, attributes, children) {\n    this.type = \"element\";\n    this.name = name;\n    this.attributes = attributes || {};\n    this.children = children || [];\n}\n\nElement.prototype.first = function(name) {\n    return _.find(this.children, function(child) {\n        return child.name === name;\n    });\n};\n\nElement.prototype.firstOrEmpty = function(name) {\n    return this.first(name) || emptyElement;\n};\n\nElement.prototype.getElementsByTagName = function(name) {\n    var elements = _.filter(this.children, function(child) {\n        return child.name === name;\n    });\n    return toElementList(elements);\n};\n\nElement.prototype.text = function() {\n    if (this.children.length === 0) {\n        return \"\";\n    } else if (this.children.length !== 1 || this.children[0].type !== \"text\") {\n        throw new Error(\"Not implemented\");\n    }\n    return this.children[0].value;\n};\n\nvar elementListPrototype = {\n    getElementsByTagName: function(name) {\n        return toElementList(_.flatten(this.map(function(element) {\n            return element.getElementsByTagName(name);\n        }, true)));\n    }\n};\n\nfunction toElementList(array) {\n    return _.extend(array, elementListPrototype);\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAGJ,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG,SAAS,IAAI,EAAE,UAAU,EAAE,QAAQ;IACjD,OAAO,IAAI,QAAQ,MAAM,YAAY;AACzC;AACA,QAAQ,IAAI,GAAG,SAAS,KAAK;IACzB,OAAO;QACH,MAAM;QACN,OAAO;IACX;AACJ;AAGA,IAAI,eAAe,QAAQ,YAAY,GAAG;IACtC,OAAO;QACH,OAAO;IACX;IACA,cAAc;QACV,OAAO;IACX;IACA,YAAY,CAAC;IACb,UAAU,EAAE;AAChB;AAEA,SAAS,QAAQ,IAAI,EAAE,UAAU,EAAE,QAAQ;IACvC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;IACjC,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE;AAClC;AAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAS,IAAI;IACnC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,KAAK;QACvC,OAAO,MAAM,IAAI,KAAK;IAC1B;AACJ;AAEA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,IAAI;IAC1C,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;AAC/B;AAEA,QAAQ,SAAS,CAAC,oBAAoB,GAAG,SAAS,IAAI;IAClD,IAAI,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,KAAK;QACjD,OAAO,MAAM,IAAI,KAAK;IAC1B;IACA,OAAO,cAAc;AACzB;AAEA,QAAQ,SAAS,CAAC,IAAI,GAAG;IACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG;QAC5B,OAAO;IACX,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,QAAQ;QACvE,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK;AACjC;AAEA,IAAI,uBAAuB;IACvB,sBAAsB,SAAS,IAAI;QAC/B,OAAO,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,OAAO;YACpD,OAAO,QAAQ,oBAAoB,CAAC;QACxC,GAAG;IACP;AACJ;AAEA,SAAS,cAAc,KAAK;IACxB,OAAO,EAAE,MAAM,CAAC,OAAO;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/xml/xmldom.js"], "sourcesContent": ["var xmldom = require(\"@xmldom/xmldom\");\nvar dom = require(\"@xmldom/xmldom/lib/dom\");\n\nfunction parseFromString(string) {\n    var error = null;\n\n    var domParser = new xmldom.DOMParser({\n        errorHandler: function(level, message) {\n            error = {level: level, message: message};\n        }\n    });\n\n    var document = domParser.parseFromString(string);\n\n    if (error === null) {\n        return document;\n    } else {\n        throw new Error(error.level + \": \" + error.message);\n    }\n}\n\nexports.parseFromString = parseFromString;\nexports.Node = dom.Node;\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,SAAS,gBAAgB,MAAM;IAC3B,IAAI,QAAQ;IAEZ,IAAI,YAAY,IAAI,OAAO,SAAS,CAAC;QACjC,cAAc,SAAS,KAAK,EAAE,OAAO;YACjC,QAAQ;gBAAC,OAAO;gBAAO,SAAS;YAAO;QAC3C;IACJ;IAEA,IAAI,WAAW,UAAU,eAAe,CAAC;IAEzC,IAAI,UAAU,MAAM;QAChB,OAAO;IACX,OAAO;QACH,MAAM,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,OAAO;IACtD;AACJ;AAEA,QAAQ,eAAe,GAAG;AAC1B,QAAQ,IAAI,GAAG,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/xml/reader.js"], "sourcesContent": ["var promises = require(\"../promises\");\nvar _ = require(\"underscore\");\n\nvar xmldom = require(\"./xmldom\");\nvar nodes = require(\"./nodes\");\nvar Element = nodes.Element;\n\nexports.readString = readString;\n\nvar Node = xmldom.Node;\n\nfunction readString(xmlString, namespaceMap) {\n    namespaceMap = namespaceMap || {};\n\n    try {\n        var document = xmldom.parseFromString(xmlString, \"text/xml\");\n    } catch (error) {\n        return promises.reject(error);\n    }\n\n    if (document.documentElement.tagName === \"parsererror\") {\n        return promises.resolve(new Error(document.documentElement.textContent));\n    }\n\n    function convertNode(node) {\n        switch (node.nodeType) {\n        case Node.ELEMENT_NODE:\n            return convertElement(node);\n        case Node.TEXT_NODE:\n            return nodes.text(node.nodeValue);\n        }\n    }\n\n    function convertElement(element) {\n        var convertedName = convertName(element);\n\n        var convertedChildren = [];\n        _.forEach(element.childNodes, function(childNode) {\n            var convertedNode = convertNode(childNode);\n            if (convertedNode) {\n                convertedChildren.push(convertedNode);\n            }\n        });\n\n        var convertedAttributes = {};\n        _.forEach(element.attributes, function(attribute) {\n            convertedAttributes[convertName(attribute)] = attribute.value;\n        });\n\n        return new Element(convertedName, convertedAttributes, convertedChildren);\n    }\n\n    function convertName(node) {\n        if (node.namespaceURI) {\n            var mappedPrefix = namespaceMap[node.namespaceURI];\n            var prefix;\n            if (mappedPrefix) {\n                prefix = mappedPrefix + \":\";\n            } else {\n                prefix = \"{\" + node.namespaceURI + \"}\";\n            }\n            return prefix + node.localName;\n        } else {\n            return node.localName;\n        }\n    }\n\n    return promises.resolve(convertNode(document.documentElement));\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI,UAAU,MAAM,OAAO;AAE3B,QAAQ,UAAU,GAAG;AAErB,IAAI,OAAO,OAAO,IAAI;AAEtB,SAAS,WAAW,SAAS,EAAE,YAAY;IACvC,eAAe,gBAAgB,CAAC;IAEhC,IAAI;QACA,IAAI,WAAW,OAAO,eAAe,CAAC,WAAW;IACrD,EAAE,OAAO,OAAO;QACZ,OAAO,SAAS,MAAM,CAAC;IAC3B;IAEA,IAAI,SAAS,eAAe,CAAC,OAAO,KAAK,eAAe;QACpD,OAAO,SAAS,OAAO,CAAC,IAAI,MAAM,SAAS,eAAe,CAAC,WAAW;IAC1E;IAEA,SAAS,YAAY,IAAI;QACrB,OAAQ,KAAK,QAAQ;YACrB,KAAK,KAAK,YAAY;gBAClB,OAAO,eAAe;YAC1B,KAAK,KAAK,SAAS;gBACf,OAAO,MAAM,IAAI,CAAC,KAAK,SAAS;QACpC;IACJ;IAEA,SAAS,eAAe,OAAO;QAC3B,IAAI,gBAAgB,YAAY;QAEhC,IAAI,oBAAoB,EAAE;QAC1B,EAAE,OAAO,CAAC,QAAQ,UAAU,EAAE,SAAS,SAAS;YAC5C,IAAI,gBAAgB,YAAY;YAChC,IAAI,eAAe;gBACf,kBAAkB,IAAI,CAAC;YAC3B;QACJ;QAEA,IAAI,sBAAsB,CAAC;QAC3B,EAAE,OAAO,CAAC,QAAQ,UAAU,EAAE,SAAS,SAAS;YAC5C,mBAAmB,CAAC,YAAY,WAAW,GAAG,UAAU,KAAK;QACjE;QAEA,OAAO,IAAI,QAAQ,eAAe,qBAAqB;IAC3D;IAEA,SAAS,YAAY,IAAI;QACrB,IAAI,KAAK,YAAY,EAAE;YACnB,IAAI,eAAe,YAAY,CAAC,KAAK,YAAY,CAAC;YAClD,IAAI;YACJ,IAAI,cAAc;gBACd,SAAS,eAAe;YAC5B,OAAO;gBACH,SAAS,MAAM,KAAK,YAAY,GAAG;YACvC;YACA,OAAO,SAAS,KAAK,SAAS;QAClC,OAAO;YACH,OAAO,KAAK,SAAS;QACzB;IACJ;IAEA,OAAO,SAAS,OAAO,CAAC,YAAY,SAAS,eAAe;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/xml/writer.js"], "sourcesContent": ["var _ = require(\"underscore\");\nvar xmlbuilder = require(\"xmlbuilder\");\n\n\nexports.writeString = writeString;\n\n\nfunction writeString(root, namespaces) {\n    var uriToPrefix = _.invert(namespaces);\n    \n    var nodeWriters = {\n        element: writeElement,\n        text: writeTextNode\n    };\n\n    function writeNode(builder, node) {\n        return nodeWriters[node.type](builder, node);\n    }\n\n    function writeElement(builder, element) {\n        var elementBuilder = builder.element(mapElementName(element.name), element.attributes);\n        element.children.forEach(function(child) {\n            writeNode(elementBuilder, child);\n        });\n    }\n    \n    function mapElementName(name) {\n        var longFormMatch = /^\\{(.*)\\}(.*)$/.exec(name);\n        if (longFormMatch) {\n            var prefix = uriToPrefix[longFormMatch[1]];\n            return prefix + (prefix === \"\" ? \"\" : \":\") + longFormMatch[2];\n        } else {\n            return name;\n        }\n    }\n    \n    function writeDocument(root) {\n        var builder = xmlbuilder\n            .create(mapElementName(root.name), {\n                version: '1.0',\n                encoding: 'UTF-8',\n                standalone: true\n            });\n        \n        _.forEach(namespaces, function(uri, prefix) {\n            var key = \"xmlns\" + (prefix === \"\" ? \"\" : \":\" + prefix);\n            builder.attribute(key, uri);\n        });\n        \n        root.children.forEach(function(child) {\n            writeNode(builder, child);\n        });\n        return builder.end();\n    }\n\n    return writeDocument(root);\n}\n\nfunction writeTextNode(builder, node) {\n    builder.text(node.value);\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAGJ,QAAQ,WAAW,GAAG;AAGtB,SAAS,YAAY,IAAI,EAAE,UAAU;IACjC,IAAI,cAAc,EAAE,MAAM,CAAC;IAE3B,IAAI,cAAc;QACd,SAAS;QACT,MAAM;IACV;IAEA,SAAS,UAAU,OAAO,EAAE,IAAI;QAC5B,OAAO,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS;IAC3C;IAEA,SAAS,aAAa,OAAO,EAAE,OAAO;QAClC,IAAI,iBAAiB,QAAQ,OAAO,CAAC,eAAe,QAAQ,IAAI,GAAG,QAAQ,UAAU;QACrF,QAAQ,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;YACnC,UAAU,gBAAgB;QAC9B;IACJ;IAEA,SAAS,eAAe,IAAI;QACxB,IAAI,gBAAgB,iBAAiB,IAAI,CAAC;QAC1C,IAAI,eAAe;YACf,IAAI,SAAS,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC,WAAW,KAAK,KAAK,GAAG,IAAI,aAAa,CAAC,EAAE;QACjE,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,cAAc,IAAI;QACvB,IAAI,UAAU,WACT,MAAM,CAAC,eAAe,KAAK,IAAI,GAAG;YAC/B,SAAS;YACT,UAAU;YACV,YAAY;QAChB;QAEJ,EAAE,OAAO,CAAC,YAAY,SAAS,GAAG,EAAE,MAAM;YACtC,IAAI,MAAM,UAAU,CAAC,WAAW,KAAK,KAAK,MAAM,MAAM;YACtD,QAAQ,SAAS,CAAC,KAAK;QAC3B;QAEA,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;YAChC,UAAU,SAAS;QACvB;QACA,OAAO,QAAQ,GAAG;IACtB;IAEA,OAAO,cAAc;AACzB;AAEA,SAAS,cAAc,OAAO,EAAE,IAAI;IAChC,QAAQ,IAAI,CAAC,KAAK,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/xml/index.js"], "sourcesContent": ["var nodes = require(\"./nodes\");\n\nexports.Element = nodes.Element;\nexports.element = nodes.element;\nexports.emptyElement = nodes.emptyElement;\nexports.text = nodes.text;\nexports.readString = require(\"./reader\").readString;\nexports.writeString = require(\"./writer\").writeString;\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,OAAO,GAAG,MAAM,OAAO;AAC/B,QAAQ,OAAO,GAAG,MAAM,OAAO;AAC/B,QAAQ,YAAY,GAAG,MAAM,YAAY;AACzC,QAAQ,IAAI,GAAG,MAAM,IAAI;AACzB,QAAQ,UAAU,GAAG,sGAAoB,UAAU;AACnD,QAAQ,WAAW,GAAG,sGAAoB,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/office-xml-reader.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar promises = require(\"../promises\");\nvar xml = require(\"../xml\");\n\n\nexports.read = read;\nexports.readXmlFromZipFile = readXmlFromZipFile;\n\nvar xmlNamespaceMap = {\n    // Transitional format\n    \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\": \"w\",\n    \"http://schemas.openxmlformats.org/officeDocument/2006/relationships\": \"r\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\": \"wp\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/main\": \"a\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/picture\": \"pic\",\n\n    // Strict format\n    \"http://purl.oclc.org/ooxml/wordprocessingml/main\": \"w\",\n    \"http://purl.oclc.org/ooxml/officeDocument/relationships\": \"r\",\n    \"http://purl.oclc.org/ooxml/drawingml/wordprocessingDrawing\": \"wp\",\n    \"http://purl.oclc.org/ooxml/drawingml/main\": \"a\",\n    \"http://purl.oclc.org/ooxml/drawingml/picture\": \"pic\",\n\n    // Common\n    \"http://schemas.openxmlformats.org/package/2006/content-types\": \"content-types\",\n    \"http://schemas.openxmlformats.org/package/2006/relationships\": \"relationships\",\n    \"http://schemas.openxmlformats.org/markup-compatibility/2006\": \"mc\",\n    \"urn:schemas-microsoft-com:vml\": \"v\",\n    \"urn:schemas-microsoft-com:office:word\": \"office-word\",\n\n    // [MS-DOCX]: Word Extensions to the Office Open XML (.docx) File Format\n    // https://learn.microsoft.com/en-us/openspecs/office_standards/ms-docx/b839fe1f-e1ca-4fa6-8c26-5954d0abbccd\n    \"http://schemas.microsoft.com/office/word/2010/wordml\": \"wordml\"\n};\n\n\nfunction read(xmlString) {\n    return xml.readString(xmlString, xmlNamespaceMap)\n        .then(function(document) {\n            return collapseAlternateContent(document)[0];\n        });\n}\n\n\nfunction readXmlFromZipFile(docxFile, path) {\n    if (docxFile.exists(path)) {\n        return docxFile.read(path, \"utf-8\")\n            .then(stripUtf8Bom)\n            .then(read);\n    } else {\n        return promises.resolve(null);\n    }\n}\n\n\nfunction stripUtf8Bom(xmlString) {\n    return xmlString.replace(/^\\uFEFF/g, '');\n}\n\n\nfunction collapseAlternateContent(node) {\n    if (node.type === \"element\") {\n        if (node.name === \"mc:AlternateContent\") {\n            return node.firstOrEmpty(\"mc:Fallback\").children;\n        } else {\n            node.children = _.flatten(node.children.map(collapseAlternateContent, true));\n            return [node];\n        }\n    } else {\n        return [node];\n    }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AACJ,IAAI;AAGJ,QAAQ,IAAI,GAAG;AACf,QAAQ,kBAAkB,GAAG;AAE7B,IAAI,kBAAkB;IAClB,sBAAsB;IACtB,gEAAgE;IAChE,uEAAuE;IACvE,0EAA0E;IAC1E,yDAAyD;IACzD,4DAA4D;IAE5D,gBAAgB;IAChB,oDAAoD;IACpD,2DAA2D;IAC3D,8DAA8D;IAC9D,6CAA6C;IAC7C,gDAAgD;IAEhD,SAAS;IACT,gEAAgE;IAChE,gEAAgE;IAChE,+DAA+D;IAC/D,iCAAiC;IACjC,yCAAyC;IAEzC,wEAAwE;IACxE,4GAA4G;IAC5G,wDAAwD;AAC5D;AAGA,SAAS,KAAK,SAAS;IACnB,OAAO,IAAI,UAAU,CAAC,WAAW,iBAC5B,IAAI,CAAC,SAAS,QAAQ;QACnB,OAAO,yBAAyB,SAAS,CAAC,EAAE;IAChD;AACR;AAGA,SAAS,mBAAmB,QAAQ,EAAE,IAAI;IACtC,IAAI,SAAS,MAAM,CAAC,OAAO;QACvB,OAAO,SAAS,IAAI,CAAC,MAAM,SACtB,IAAI,CAAC,cACL,IAAI,CAAC;IACd,OAAO;QACH,OAAO,SAAS,OAAO,CAAC;IAC5B;AACJ;AAGA,SAAS,aAAa,SAAS;IAC3B,OAAO,UAAU,OAAO,CAAC,YAAY;AACzC;AAGA,SAAS,yBAAyB,IAAI;IAClC,IAAI,KAAK,IAAI,KAAK,WAAW;QACzB,IAAI,KAAK,IAAI,KAAK,uBAAuB;YACrC,OAAO,KAAK,YAAY,CAAC,eAAe,QAAQ;QACpD,OAAO;YACH,KAAK,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,0BAA0B;YACtE,OAAO;gBAAC;aAAK;QACjB;IACJ,OAAO;QACH,OAAO;YAAC;SAAK;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/uris.js"], "sourcesContent": ["exports.uriToZipEntryName = uriToZipEntryName;\nexports.replaceFragment = replaceFragment;\n\nfunction uriToZipEntryName(base, uri) {\n    if (uri.charAt(0) === \"/\") {\n        return uri.substr(1);\n    } else {\n        // In general, we should check first and second for trailing and leading slashes,\n        // but in our specific case this seems to be sufficient\n        return base + \"/\" + uri;\n    }\n}\n\n\nfunction replaceFragment(uri, fragment) {\n    var hashIndex = uri.indexOf(\"#\");\n    if (hashIndex !== -1) {\n        uri = uri.substring(0, hashIndex);\n    }\n    return uri + \"#\" + fragment;\n}\n"], "names": [], "mappings": "AAAA,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,eAAe,GAAG;AAE1B,SAAS,kBAAkB,IAAI,EAAE,GAAG;IAChC,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK;QACvB,OAAO,IAAI,MAAM,CAAC;IACtB,OAAO;QACH,iFAAiF;QACjF,uDAAuD;QACvD,OAAO,OAAO,MAAM;IACxB;AACJ;AAGA,SAAS,gBAAgB,GAAG,EAAE,QAAQ;IAClC,IAAI,YAAY,IAAI,OAAO,CAAC;IAC5B,IAAI,cAAc,CAAC,GAAG;QAClB,MAAM,IAAI,SAAS,CAAC,GAAG;IAC3B;IACA,OAAO,MAAM,MAAM;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/body-reader.js"], "sourcesContent": ["exports.createBodyReader = createBodyReader;\nexports._readNumberingProperties = readNumberingProperties;\n\nvar dingbatToUnicode = require(\"dingbat-to-unicode\");\nvar _ = require(\"underscore\");\n\nvar documents = require(\"../documents\");\nvar Result = require(\"../results\").Result;\nvar warning = require(\"../results\").warning;\nvar xml = require(\"../xml\");\nvar uris = require(\"./uris\");\n\nfunction createBodyReader(options) {\n    return {\n        readXmlElement: function(element) {\n            return new BodyReader(options).readXmlElement(element);\n        },\n        readXmlElements: function(elements) {\n            return new BodyReader(options).readXmlElements(elements);\n        }\n    };\n}\n\nfunction BodyReader(options) {\n    var complexFieldStack = [];\n    var currentInstrText = [];\n\n    // When a paragraph is marked as deleted, its contents should be combined\n    // with the following paragraph. See ********** del (Deleted Paragraph) of\n    // ECMA-376 4th edition Part 1.\n    var deletedParagraphContents = [];\n\n    var relationships = options.relationships;\n    var contentTypes = options.contentTypes;\n    var docxFile = options.docxFile;\n    var files = options.files;\n    var numbering = options.numbering;\n    var styles = options.styles;\n\n    function readXmlElements(elements) {\n        var results = elements.map(readXmlElement);\n        return combineResults(results);\n    }\n\n    function readXmlElement(element) {\n        if (element.type === \"element\") {\n            var handler = xmlElementReaders[element.name];\n            if (handler) {\n                return handler(element);\n            } else if (!Object.prototype.hasOwnProperty.call(ignoreElements, element.name)) {\n                var message = warning(\"An unrecognised element was ignored: \" + element.name);\n                return emptyResultWithMessages([message]);\n            }\n        }\n        return emptyResult();\n    }\n\n    function readParagraphProperties(element) {\n        return readParagraphStyle(element).map(function(style) {\n            return {\n                type: \"paragraphProperties\",\n                styleId: style.styleId,\n                styleName: style.name,\n                alignment: element.firstOrEmpty(\"w:jc\").attributes[\"w:val\"],\n                numbering: readNumberingProperties(style.styleId, element.firstOrEmpty(\"w:numPr\"), numbering),\n                indent: readParagraphIndent(element.firstOrEmpty(\"w:ind\"))\n            };\n        });\n    }\n\n    function readParagraphIndent(element) {\n        return {\n            start: element.attributes[\"w:start\"] || element.attributes[\"w:left\"],\n            end: element.attributes[\"w:end\"] || element.attributes[\"w:right\"],\n            firstLine: element.attributes[\"w:firstLine\"],\n            hanging: element.attributes[\"w:hanging\"]\n        };\n    }\n\n    function readRunProperties(element) {\n        return readRunStyle(element).map(function(style) {\n            var fontSizeString = element.firstOrEmpty(\"w:sz\").attributes[\"w:val\"];\n            // w:sz gives the font size in half points, so halve the value to get the size in points\n            var fontSize = /^[0-9]+$/.test(fontSizeString) ? parseInt(fontSizeString, 10) / 2 : null;\n\n            return {\n                type: \"runProperties\",\n                styleId: style.styleId,\n                styleName: style.name,\n                verticalAlignment: element.firstOrEmpty(\"w:vertAlign\").attributes[\"w:val\"],\n                font: element.firstOrEmpty(\"w:rFonts\").attributes[\"w:ascii\"],\n                fontSize: fontSize,\n                isBold: readBooleanElement(element.first(\"w:b\")),\n                isUnderline: readUnderline(element.first(\"w:u\")),\n                isItalic: readBooleanElement(element.first(\"w:i\")),\n                isStrikethrough: readBooleanElement(element.first(\"w:strike\")),\n                isAllCaps: readBooleanElement(element.first(\"w:caps\")),\n                isSmallCaps: readBooleanElement(element.first(\"w:smallCaps\")),\n                highlight: readHighlightValue(element.firstOrEmpty(\"w:highlight\").attributes[\"w:val\"])\n            };\n        });\n    }\n\n    function readUnderline(element) {\n        if (element) {\n            var value = element.attributes[\"w:val\"];\n            return value !== undefined && value !== \"false\" && value !== \"0\" && value !== \"none\";\n        } else {\n            return false;\n        }\n    }\n\n    function readBooleanElement(element) {\n        if (element) {\n            var value = element.attributes[\"w:val\"];\n            return value !== \"false\" && value !== \"0\";\n        } else {\n            return false;\n        }\n    }\n\n    function readBooleanAttributeValue(value) {\n        return value !== \"false\" && value !== \"0\";\n    }\n\n    function readHighlightValue(value) {\n        if (!value || value === \"none\") {\n            return null;\n        } else {\n            return value;\n        }\n    }\n\n    function readParagraphStyle(element) {\n        return readStyle(element, \"w:pStyle\", \"Paragraph\", styles.findParagraphStyleById);\n    }\n\n    function readRunStyle(element) {\n        return readStyle(element, \"w:rStyle\", \"Run\", styles.findCharacterStyleById);\n    }\n\n    function readTableStyle(element) {\n        return readStyle(element, \"w:tblStyle\", \"Table\", styles.findTableStyleById);\n    }\n\n    function readStyle(element, styleTagName, styleType, findStyleById) {\n        var messages = [];\n        var styleElement = element.first(styleTagName);\n        var styleId = null;\n        var name = null;\n        if (styleElement) {\n            styleId = styleElement.attributes[\"w:val\"];\n            if (styleId) {\n                var style = findStyleById(styleId);\n                if (style) {\n                    name = style.name;\n                } else {\n                    messages.push(undefinedStyleWarning(styleType, styleId));\n                }\n            }\n        }\n        return elementResultWithMessages({styleId: styleId, name: name}, messages);\n    }\n\n    function readFldChar(element) {\n        var type = element.attributes[\"w:fldCharType\"];\n        if (type === \"begin\") {\n            complexFieldStack.push({type: \"begin\", fldChar: element});\n            currentInstrText = [];\n        } else if (type === \"end\") {\n            var complexFieldEnd = complexFieldStack.pop();\n            if (complexFieldEnd.type === \"begin\") {\n                complexFieldEnd = parseCurrentInstrText(complexFieldEnd);\n            }\n            if (complexFieldEnd.type === \"checkbox\") {\n                return elementResult(documents.checkbox({\n                    checked: complexFieldEnd.checked\n                }));\n            }\n        } else if (type === \"separate\") {\n            var complexFieldSeparate = complexFieldStack.pop();\n            var complexField = parseCurrentInstrText(complexFieldSeparate);\n            complexFieldStack.push(complexField);\n        }\n        return emptyResult();\n    }\n\n    function currentHyperlinkOptions() {\n        var topHyperlink = _.last(complexFieldStack.filter(function(complexField) {\n            return complexField.type === \"hyperlink\";\n        }));\n        return topHyperlink ? topHyperlink.options : null;\n    }\n\n    function parseCurrentInstrText(complexField) {\n        return parseInstrText(\n            currentInstrText.join(''),\n            complexField.type === \"begin\"\n                ? complexField.fldChar\n                : xml.emptyElement\n        );\n    }\n\n    function parseInstrText(instrText, fldChar) {\n        var externalLinkResult = /\\s*HYPERLINK \"(.*)\"/.exec(instrText);\n        if (externalLinkResult) {\n            return {type: \"hyperlink\", options: {href: externalLinkResult[1]}};\n        }\n\n        var internalLinkResult = /\\s*HYPERLINK\\s+\\\\l\\s+\"(.*)\"/.exec(instrText);\n        if (internalLinkResult) {\n            return {type: \"hyperlink\", options: {anchor: internalLinkResult[1]}};\n        }\n\n        var checkboxResult = /\\s*FORMCHECKBOX\\s*/.exec(instrText);\n        if (checkboxResult) {\n            var checkboxElement = fldChar\n                .firstOrEmpty(\"w:ffData\")\n                .firstOrEmpty(\"w:checkBox\");\n            var checkedElement = checkboxElement.first(\"w:checked\");\n            var checked = checkedElement == null\n                ? readBooleanElement(checkboxElement.first(\"w:default\"))\n                : readBooleanElement(checkedElement);\n            return {type: \"checkbox\", checked: checked};\n        }\n\n        return {type: \"unknown\"};\n    }\n\n    function readInstrText(element) {\n        currentInstrText.push(element.text());\n        return emptyResult();\n    }\n\n    function readSymbol(element) {\n        // See 17.3.3.30 sym (Symbol Character) of ECMA-376 4th edition Part 1\n        var font = element.attributes[\"w:font\"];\n        var char = element.attributes[\"w:char\"];\n        var unicodeCharacter = dingbatToUnicode.hex(font, char);\n        if (unicodeCharacter == null && /^F0..$/.test(char)) {\n            unicodeCharacter = dingbatToUnicode.hex(font, char.substring(2));\n        }\n\n        if (unicodeCharacter == null) {\n            return emptyResultWithMessages([warning(\n                \"A w:sym element with an unsupported character was ignored: char \" +  char + \" in font \" + font\n            )]);\n        } else {\n            return elementResult(new documents.Text(unicodeCharacter.string));\n        }\n    }\n\n    function noteReferenceReader(noteType) {\n        return function(element) {\n            var noteId = element.attributes[\"w:id\"];\n            return elementResult(new documents.NoteReference({\n                noteType: noteType,\n                noteId: noteId\n            }));\n        };\n    }\n\n    function readCommentReference(element) {\n        return elementResult(documents.commentReference({\n            commentId: element.attributes[\"w:id\"]\n        }));\n    }\n\n    function readChildElements(element) {\n        return readXmlElements(element.children);\n    }\n\n    var xmlElementReaders = {\n        \"w:p\": function(element) {\n            var paragraphPropertiesElement = element.firstOrEmpty(\"w:pPr\");\n\n            var isDeleted = !!paragraphPropertiesElement\n                .firstOrEmpty(\"w:rPr\")\n                .first(\"w:del\");\n\n            if (isDeleted) {\n                element.children.forEach(function(child) {\n                    deletedParagraphContents.push(child);\n                });\n                return emptyResult();\n            } else {\n                var childrenXml = element.children;\n                if (deletedParagraphContents.length > 0) {\n                    childrenXml = deletedParagraphContents.concat(childrenXml);\n                    deletedParagraphContents = [];\n                }\n                return ReadResult.map(\n                    readParagraphProperties(paragraphPropertiesElement),\n                    readXmlElements(childrenXml),\n                    function(properties, children) {\n                        return new documents.Paragraph(children, properties);\n                    }\n                ).insertExtra();\n            }\n        },\n        \"w:r\": function(element) {\n            return ReadResult.map(\n                readRunProperties(element.firstOrEmpty(\"w:rPr\")),\n                readXmlElements(element.children),\n                function(properties, children) {\n                    var hyperlinkOptions = currentHyperlinkOptions();\n                    if (hyperlinkOptions !== null) {\n                        children = [new documents.Hyperlink(children, hyperlinkOptions)];\n                    }\n\n                    return new documents.Run(children, properties);\n                }\n            );\n        },\n        \"w:fldChar\": readFldChar,\n        \"w:instrText\": readInstrText,\n        \"w:t\": function(element) {\n            return elementResult(new documents.Text(element.text()));\n        },\n        \"w:tab\": function(element) {\n            return elementResult(new documents.Tab());\n        },\n        \"w:noBreakHyphen\": function() {\n            return elementResult(new documents.Text(\"\\u2011\"));\n        },\n        \"w:softHyphen\": function(element) {\n            return elementResult(new documents.Text(\"\\u00AD\"));\n        },\n        \"w:sym\": readSymbol,\n        \"w:hyperlink\": function(element) {\n            var relationshipId = element.attributes[\"r:id\"];\n            var anchor = element.attributes[\"w:anchor\"];\n            return readXmlElements(element.children).map(function(children) {\n                function create(options) {\n                    var targetFrame = element.attributes[\"w:tgtFrame\"] || null;\n\n                    return new documents.Hyperlink(\n                        children,\n                        _.extend({targetFrame: targetFrame}, options)\n                    );\n                }\n\n                if (relationshipId) {\n                    var href = relationships.findTargetByRelationshipId(relationshipId);\n                    if (anchor) {\n                        href = uris.replaceFragment(href, anchor);\n                    }\n                    return create({href: href});\n                } else if (anchor) {\n                    return create({anchor: anchor});\n                } else {\n                    return children;\n                }\n            });\n        },\n        \"w:tbl\": readTable,\n        \"w:tr\": readTableRow,\n        \"w:tc\": readTableCell,\n        \"w:footnoteReference\": noteReferenceReader(\"footnote\"),\n        \"w:endnoteReference\": noteReferenceReader(\"endnote\"),\n        \"w:commentReference\": readCommentReference,\n        \"w:br\": function(element) {\n            var breakType = element.attributes[\"w:type\"];\n            if (breakType == null || breakType === \"textWrapping\") {\n                return elementResult(documents.lineBreak);\n            } else if (breakType === \"page\") {\n                return elementResult(documents.pageBreak);\n            } else if (breakType === \"column\") {\n                return elementResult(documents.columnBreak);\n            } else {\n                return emptyResultWithMessages([warning(\"Unsupported break type: \" + breakType)]);\n            }\n        },\n        \"w:bookmarkStart\": function(element){\n            var name = element.attributes[\"w:name\"];\n            if (name === \"_GoBack\") {\n                return emptyResult();\n            } else {\n                return elementResult(new documents.BookmarkStart({name: name}));\n            }\n        },\n\n        \"mc:AlternateContent\": function(element) {\n            return readChildElements(element.firstOrEmpty(\"mc:Fallback\"));\n        },\n\n        \"w:sdt\": function(element) {\n            var checkbox = element\n                .firstOrEmpty(\"w:sdtPr\")\n                .first(\"wordml:checkbox\");\n\n            if (checkbox) {\n                var checkedElement = checkbox.first(\"wordml:checked\");\n                var isChecked = !!checkedElement && readBooleanAttributeValue(\n                    checkedElement.attributes[\"wordml:val\"]\n                );\n                return elementResult(documents.checkbox({\n                    checked: isChecked\n                }));\n            } else {\n                return readXmlElements(element.firstOrEmpty(\"w:sdtContent\").children);\n            }\n        },\n\n        \"w:ins\": readChildElements,\n        \"w:object\": readChildElements,\n        \"w:smartTag\": readChildElements,\n        \"w:drawing\": readChildElements,\n        \"w:pict\": function(element) {\n            return readChildElements(element).toExtra();\n        },\n        \"v:roundrect\": readChildElements,\n        \"v:shape\": readChildElements,\n        \"v:textbox\": readChildElements,\n        \"w:txbxContent\": readChildElements,\n        \"wp:inline\": readDrawingElement,\n        \"wp:anchor\": readDrawingElement,\n        \"v:imagedata\": readImageData,\n        \"v:group\": readChildElements,\n        \"v:rect\": readChildElements\n    };\n\n    return {\n        readXmlElement: readXmlElement,\n        readXmlElements: readXmlElements\n    };\n\n\n    function readTable(element) {\n        var propertiesResult = readTableProperties(element.firstOrEmpty(\"w:tblPr\"));\n        return readXmlElements(element.children)\n            .flatMap(calculateRowSpans)\n            .flatMap(function(children) {\n                return propertiesResult.map(function(properties) {\n                    return documents.Table(children, properties);\n                });\n            });\n    }\n\n    function readTableProperties(element) {\n        return readTableStyle(element).map(function(style) {\n            return {\n                styleId: style.styleId,\n                styleName: style.name\n            };\n        });\n    }\n\n    function readTableRow(element) {\n        var properties = element.firstOrEmpty(\"w:trPr\");\n        var isHeader = !!properties.first(\"w:tblHeader\");\n        return readXmlElements(element.children).map(function(children) {\n            return documents.TableRow(children, {isHeader: isHeader});\n        });\n    }\n\n    function readTableCell(element) {\n        return readXmlElements(element.children).map(function(children) {\n            var properties = element.firstOrEmpty(\"w:tcPr\");\n\n            var gridSpan = properties.firstOrEmpty(\"w:gridSpan\").attributes[\"w:val\"];\n            var colSpan = gridSpan ? parseInt(gridSpan, 10) : 1;\n\n            var cell = documents.TableCell(children, {colSpan: colSpan});\n            cell._vMerge = readVMerge(properties);\n            return cell;\n        });\n    }\n\n    function readVMerge(properties) {\n        var element = properties.first(\"w:vMerge\");\n        if (element) {\n            var val = element.attributes[\"w:val\"];\n            return val === \"continue\" || !val;\n        } else {\n            return null;\n        }\n    }\n\n    function calculateRowSpans(rows) {\n        var unexpectedNonRows = _.any(rows, function(row) {\n            return row.type !== documents.types.tableRow;\n        });\n        if (unexpectedNonRows) {\n            return elementResultWithMessages(rows, [warning(\n                \"unexpected non-row element in table, cell merging may be incorrect\"\n            )]);\n        }\n        var unexpectedNonCells = _.any(rows, function(row) {\n            return _.any(row.children, function(cell) {\n                return cell.type !== documents.types.tableCell;\n            });\n        });\n        if (unexpectedNonCells) {\n            return elementResultWithMessages(rows, [warning(\n                \"unexpected non-cell element in table row, cell merging may be incorrect\"\n            )]);\n        }\n\n        var columns = {};\n\n        rows.forEach(function(row) {\n            var cellIndex = 0;\n            row.children.forEach(function(cell) {\n                if (cell._vMerge && columns[cellIndex]) {\n                    columns[cellIndex].rowSpan++;\n                } else {\n                    columns[cellIndex] = cell;\n                    cell._vMerge = false;\n                }\n                cellIndex += cell.colSpan;\n            });\n        });\n\n        rows.forEach(function(row) {\n            row.children = row.children.filter(function(cell) {\n                return !cell._vMerge;\n            });\n            row.children.forEach(function(cell) {\n                delete cell._vMerge;\n            });\n        });\n\n        return elementResult(rows);\n    }\n\n    function readDrawingElement(element) {\n        var blips = element\n            .getElementsByTagName(\"a:graphic\")\n            .getElementsByTagName(\"a:graphicData\")\n            .getElementsByTagName(\"pic:pic\")\n            .getElementsByTagName(\"pic:blipFill\")\n            .getElementsByTagName(\"a:blip\");\n\n        return combineResults(blips.map(readBlip.bind(null, element)));\n    }\n\n    function readBlip(element, blip) {\n        var properties = element.first(\"wp:docPr\").attributes;\n        var altText = isBlank(properties.descr) ? properties.title : properties.descr;\n        var blipImageFile = findBlipImageFile(blip);\n        if (blipImageFile === null) {\n            return emptyResultWithMessages([warning(\"Could not find image file for a:blip element\")]);\n        } else {\n            return readImage(blipImageFile, altText);\n        }\n    }\n\n    function isBlank(value) {\n        return value == null || /^\\s*$/.test(value);\n    }\n\n    function findBlipImageFile(blip) {\n        var embedRelationshipId = blip.attributes[\"r:embed\"];\n        var linkRelationshipId = blip.attributes[\"r:link\"];\n        if (embedRelationshipId) {\n            return findEmbeddedImageFile(embedRelationshipId);\n        } else if (linkRelationshipId) {\n            var imagePath = relationships.findTargetByRelationshipId(linkRelationshipId);\n            return {\n                path: imagePath,\n                read: files.read.bind(files, imagePath)\n            };\n        } else {\n            return null;\n        }\n    }\n\n    function readImageData(element) {\n        var relationshipId = element.attributes['r:id'];\n\n        if (relationshipId) {\n            return readImage(\n                findEmbeddedImageFile(relationshipId),\n                element.attributes[\"o:title\"]);\n        } else {\n            return emptyResultWithMessages([warning(\"A v:imagedata element without a relationship ID was ignored\")]);\n        }\n    }\n\n    function findEmbeddedImageFile(relationshipId) {\n        var path = uris.uriToZipEntryName(\"word\", relationships.findTargetByRelationshipId(relationshipId));\n        return {\n            path: path,\n            read: docxFile.read.bind(docxFile, path)\n        };\n    }\n\n    function readImage(imageFile, altText) {\n        var contentType = contentTypes.findContentType(imageFile.path);\n\n        var image = documents.Image({\n            readImage: imageFile.read,\n            altText: altText,\n            contentType: contentType\n        });\n        var warnings = supportedImageTypes[contentType] ?\n            [] : warning(\"Image of type \" + contentType + \" is unlikely to display in web browsers\");\n        return elementResultWithMessages(image, warnings);\n    }\n\n    function undefinedStyleWarning(type, styleId) {\n        return warning(\n            type + \" style with ID \" + styleId + \" was referenced but not defined in the document\");\n    }\n}\n\n\nfunction readNumberingProperties(styleId, element, numbering) {\n    var level = element.firstOrEmpty(\"w:ilvl\").attributes[\"w:val\"];\n    var numId = element.firstOrEmpty(\"w:numId\").attributes[\"w:val\"];\n    if (level !== undefined && numId !== undefined) {\n        return numbering.findLevel(numId, level);\n    }\n\n    if (styleId != null) {\n        var levelByStyleId = numbering.findLevelByParagraphStyleId(styleId);\n        if (levelByStyleId != null) {\n            return levelByStyleId;\n        }\n    }\n\n    return null;\n}\n\nvar supportedImageTypes = {\n    \"image/png\": true,\n    \"image/gif\": true,\n    \"image/jpeg\": true,\n    \"image/svg+xml\": true,\n    \"image/tiff\": true\n};\n\nvar ignoreElements = {\n    \"office-word:wrap\": true,\n    \"v:shadow\": true,\n    \"v:shapetype\": true,\n    \"w:annotationRef\": true,\n    \"w:bookmarkEnd\": true,\n    \"w:sectPr\": true,\n    \"w:proofErr\": true,\n    \"w:lastRenderedPageBreak\": true,\n    \"w:commentRangeStart\": true,\n    \"w:commentRangeEnd\": true,\n    \"w:del\": true,\n    \"w:footnoteRef\": true,\n    \"w:endnoteRef\": true,\n    \"w:pPr\": true,\n    \"w:rPr\": true,\n    \"w:tblPr\": true,\n    \"w:tblGrid\": true,\n    \"w:trPr\": true,\n    \"w:tcPr\": true\n};\n\nfunction emptyResultWithMessages(messages) {\n    return new ReadResult(null, null, messages);\n}\n\nfunction emptyResult() {\n    return new ReadResult(null);\n}\n\nfunction elementResult(element) {\n    return new ReadResult(element);\n}\n\nfunction elementResultWithMessages(element, messages) {\n    return new ReadResult(element, null, messages);\n}\n\nfunction ReadResult(element, extra, messages) {\n    this.value = element || [];\n    this.extra = extra || [];\n    this._result = new Result({\n        element: this.value,\n        extra: extra\n    }, messages);\n    this.messages = this._result.messages;\n}\n\nReadResult.prototype.toExtra = function() {\n    return new ReadResult(null, joinElements(this.extra, this.value), this.messages);\n};\n\nReadResult.prototype.insertExtra = function() {\n    var extra = this.extra;\n    if (extra && extra.length) {\n        return new ReadResult(joinElements(this.value, extra), null, this.messages);\n    } else {\n        return this;\n    }\n};\n\nReadResult.prototype.map = function(func) {\n    var result = this._result.map(function(value) {\n        return func(value.element);\n    });\n    return new ReadResult(result.value, this.extra, result.messages);\n};\n\nReadResult.prototype.flatMap = function(func) {\n    var result = this._result.flatMap(function(value) {\n        return func(value.element)._result;\n    });\n    return new ReadResult(result.value.element, joinElements(this.extra, result.value.extra), result.messages);\n};\n\nReadResult.map = function(first, second, func) {\n    return new ReadResult(\n        func(first.value, second.value),\n        joinElements(first.extra, second.extra),\n        first.messages.concat(second.messages)\n    );\n};\n\nfunction combineResults(results) {\n    var result = Result.combine(_.pluck(results, \"_result\"));\n    return new ReadResult(\n        _.flatten(_.pluck(result.value, \"element\")),\n        _.filter(_.flatten(_.pluck(result.value, \"extra\")), identity),\n        result.messages\n    );\n}\n\nfunction joinElements(first, second) {\n    return _.flatten([first, second]);\n}\n\nfunction identity(value) {\n    return value;\n}\n"], "names": [], "mappings": "AAAA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,wBAAwB,GAAG;AAEnC,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI,SAAS,mGAAsB,MAAM;AACzC,IAAI,UAAU,mGAAsB,OAAO;AAC3C,IAAI;AACJ,IAAI;AAEJ,SAAS,iBAAiB,OAAO;IAC7B,OAAO;QACH,gBAAgB,SAAS,OAAO;YAC5B,OAAO,IAAI,WAAW,SAAS,cAAc,CAAC;QAClD;QACA,iBAAiB,SAAS,QAAQ;YAC9B,OAAO,IAAI,WAAW,SAAS,eAAe,CAAC;QACnD;IACJ;AACJ;AAEA,SAAS,WAAW,OAAO;IACvB,IAAI,oBAAoB,EAAE;IAC1B,IAAI,mBAAmB,EAAE;IAEzB,yEAAyE;IACzE,0EAA0E;IAC1E,+BAA+B;IAC/B,IAAI,2BAA2B,EAAE;IAEjC,IAAI,gBAAgB,QAAQ,aAAa;IACzC,IAAI,eAAe,QAAQ,YAAY;IACvC,IAAI,WAAW,QAAQ,QAAQ;IAC/B,IAAI,QAAQ,QAAQ,KAAK;IACzB,IAAI,YAAY,QAAQ,SAAS;IACjC,IAAI,SAAS,QAAQ,MAAM;IAE3B,SAAS,gBAAgB,QAAQ;QAC7B,IAAI,UAAU,SAAS,GAAG,CAAC;QAC3B,OAAO,eAAe;IAC1B;IAEA,SAAS,eAAe,OAAO;QAC3B,IAAI,QAAQ,IAAI,KAAK,WAAW;YAC5B,IAAI,UAAU,iBAAiB,CAAC,QAAQ,IAAI,CAAC;YAC7C,IAAI,SAAS;gBACT,OAAO,QAAQ;YACnB,OAAO,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,QAAQ,IAAI,GAAG;gBAC5E,IAAI,UAAU,QAAQ,0CAA0C,QAAQ,IAAI;gBAC5E,OAAO,wBAAwB;oBAAC;iBAAQ;YAC5C;QACJ;QACA,OAAO;IACX;IAEA,SAAS,wBAAwB,OAAO;QACpC,OAAO,mBAAmB,SAAS,GAAG,CAAC,SAAS,KAAK;YACjD,OAAO;gBACH,MAAM;gBACN,SAAS,MAAM,OAAO;gBACtB,WAAW,MAAM,IAAI;gBACrB,WAAW,QAAQ,YAAY,CAAC,QAAQ,UAAU,CAAC,QAAQ;gBAC3D,WAAW,wBAAwB,MAAM,OAAO,EAAE,QAAQ,YAAY,CAAC,YAAY;gBACnF,QAAQ,oBAAoB,QAAQ,YAAY,CAAC;YACrD;QACJ;IACJ;IAEA,SAAS,oBAAoB,OAAO;QAChC,OAAO;YACH,OAAO,QAAQ,UAAU,CAAC,UAAU,IAAI,QAAQ,UAAU,CAAC,SAAS;YACpE,KAAK,QAAQ,UAAU,CAAC,QAAQ,IAAI,QAAQ,UAAU,CAAC,UAAU;YACjE,WAAW,QAAQ,UAAU,CAAC,cAAc;YAC5C,SAAS,QAAQ,UAAU,CAAC,YAAY;QAC5C;IACJ;IAEA,SAAS,kBAAkB,OAAO;QAC9B,OAAO,aAAa,SAAS,GAAG,CAAC,SAAS,KAAK;YAC3C,IAAI,iBAAiB,QAAQ,YAAY,CAAC,QAAQ,UAAU,CAAC,QAAQ;YACrE,wFAAwF;YACxF,IAAI,WAAW,WAAW,IAAI,CAAC,kBAAkB,SAAS,gBAAgB,MAAM,IAAI;YAEpF,OAAO;gBACH,MAAM;gBACN,SAAS,MAAM,OAAO;gBACtB,WAAW,MAAM,IAAI;gBACrB,mBAAmB,QAAQ,YAAY,CAAC,eAAe,UAAU,CAAC,QAAQ;gBAC1E,MAAM,QAAQ,YAAY,CAAC,YAAY,UAAU,CAAC,UAAU;gBAC5D,UAAU;gBACV,QAAQ,mBAAmB,QAAQ,KAAK,CAAC;gBACzC,aAAa,cAAc,QAAQ,KAAK,CAAC;gBACzC,UAAU,mBAAmB,QAAQ,KAAK,CAAC;gBAC3C,iBAAiB,mBAAmB,QAAQ,KAAK,CAAC;gBAClD,WAAW,mBAAmB,QAAQ,KAAK,CAAC;gBAC5C,aAAa,mBAAmB,QAAQ,KAAK,CAAC;gBAC9C,WAAW,mBAAmB,QAAQ,YAAY,CAAC,eAAe,UAAU,CAAC,QAAQ;YACzF;QACJ;IACJ;IAEA,SAAS,cAAc,OAAO;QAC1B,IAAI,SAAS;YACT,IAAI,QAAQ,QAAQ,UAAU,CAAC,QAAQ;YACvC,OAAO,UAAU,aAAa,UAAU,WAAW,UAAU,OAAO,UAAU;QAClF,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,mBAAmB,OAAO;QAC/B,IAAI,SAAS;YACT,IAAI,QAAQ,QAAQ,UAAU,CAAC,QAAQ;YACvC,OAAO,UAAU,WAAW,UAAU;QAC1C,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,0BAA0B,KAAK;QACpC,OAAO,UAAU,WAAW,UAAU;IAC1C;IAEA,SAAS,mBAAmB,KAAK;QAC7B,IAAI,CAAC,SAAS,UAAU,QAAQ;YAC5B,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,mBAAmB,OAAO;QAC/B,OAAO,UAAU,SAAS,YAAY,aAAa,OAAO,sBAAsB;IACpF;IAEA,SAAS,aAAa,OAAO;QACzB,OAAO,UAAU,SAAS,YAAY,OAAO,OAAO,sBAAsB;IAC9E;IAEA,SAAS,eAAe,OAAO;QAC3B,OAAO,UAAU,SAAS,cAAc,SAAS,OAAO,kBAAkB;IAC9E;IAEA,SAAS,UAAU,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;QAC9D,IAAI,WAAW,EAAE;QACjB,IAAI,eAAe,QAAQ,KAAK,CAAC;QACjC,IAAI,UAAU;QACd,IAAI,OAAO;QACX,IAAI,cAAc;YACd,UAAU,aAAa,UAAU,CAAC,QAAQ;YAC1C,IAAI,SAAS;gBACT,IAAI,QAAQ,cAAc;gBAC1B,IAAI,OAAO;oBACP,OAAO,MAAM,IAAI;gBACrB,OAAO;oBACH,SAAS,IAAI,CAAC,sBAAsB,WAAW;gBACnD;YACJ;QACJ;QACA,OAAO,0BAA0B;YAAC,SAAS;YAAS,MAAM;QAAI,GAAG;IACrE;IAEA,SAAS,YAAY,OAAO;QACxB,IAAI,OAAO,QAAQ,UAAU,CAAC,gBAAgB;QAC9C,IAAI,SAAS,SAAS;YAClB,kBAAkB,IAAI,CAAC;gBAAC,MAAM;gBAAS,SAAS;YAAO;YACvD,mBAAmB,EAAE;QACzB,OAAO,IAAI,SAAS,OAAO;YACvB,IAAI,kBAAkB,kBAAkB,GAAG;YAC3C,IAAI,gBAAgB,IAAI,KAAK,SAAS;gBAClC,kBAAkB,sBAAsB;YAC5C;YACA,IAAI,gBAAgB,IAAI,KAAK,YAAY;gBACrC,OAAO,cAAc,UAAU,QAAQ,CAAC;oBACpC,SAAS,gBAAgB,OAAO;gBACpC;YACJ;QACJ,OAAO,IAAI,SAAS,YAAY;YAC5B,IAAI,uBAAuB,kBAAkB,GAAG;YAChD,IAAI,eAAe,sBAAsB;YACzC,kBAAkB,IAAI,CAAC;QAC3B;QACA,OAAO;IACX;IAEA,SAAS;QACL,IAAI,eAAe,EAAE,IAAI,CAAC,kBAAkB,MAAM,CAAC,SAAS,YAAY;YACpE,OAAO,aAAa,IAAI,KAAK;QACjC;QACA,OAAO,eAAe,aAAa,OAAO,GAAG;IACjD;IAEA,SAAS,sBAAsB,YAAY;QACvC,OAAO,eACH,iBAAiB,IAAI,CAAC,KACtB,aAAa,IAAI,KAAK,UAChB,aAAa,OAAO,GACpB,IAAI,YAAY;IAE9B;IAEA,SAAS,eAAe,SAAS,EAAE,OAAO;QACtC,IAAI,qBAAqB,sBAAsB,IAAI,CAAC;QACpD,IAAI,oBAAoB;YACpB,OAAO;gBAAC,MAAM;gBAAa,SAAS;oBAAC,MAAM,kBAAkB,CAAC,EAAE;gBAAA;YAAC;QACrE;QAEA,IAAI,qBAAqB,8BAA8B,IAAI,CAAC;QAC5D,IAAI,oBAAoB;YACpB,OAAO;gBAAC,MAAM;gBAAa,SAAS;oBAAC,QAAQ,kBAAkB,CAAC,EAAE;gBAAA;YAAC;QACvE;QAEA,IAAI,iBAAiB,qBAAqB,IAAI,CAAC;QAC/C,IAAI,gBAAgB;YAChB,IAAI,kBAAkB,QACjB,YAAY,CAAC,YACb,YAAY,CAAC;YAClB,IAAI,iBAAiB,gBAAgB,KAAK,CAAC;YAC3C,IAAI,UAAU,kBAAkB,OAC1B,mBAAmB,gBAAgB,KAAK,CAAC,gBACzC,mBAAmB;YACzB,OAAO;gBAAC,MAAM;gBAAY,SAAS;YAAO;QAC9C;QAEA,OAAO;YAAC,MAAM;QAAS;IAC3B;IAEA,SAAS,cAAc,OAAO;QAC1B,iBAAiB,IAAI,CAAC,QAAQ,IAAI;QAClC,OAAO;IACX;IAEA,SAAS,WAAW,OAAO;QACvB,sEAAsE;QACtE,IAAI,OAAO,QAAQ,UAAU,CAAC,SAAS;QACvC,IAAI,OAAO,QAAQ,UAAU,CAAC,SAAS;QACvC,IAAI,mBAAmB,iBAAiB,GAAG,CAAC,MAAM;QAClD,IAAI,oBAAoB,QAAQ,SAAS,IAAI,CAAC,OAAO;YACjD,mBAAmB,iBAAiB,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC;QACjE;QAEA,IAAI,oBAAoB,MAAM;YAC1B,OAAO,wBAAwB;gBAAC,QAC5B,qEAAsE,OAAO,cAAc;aAC7F;QACN,OAAO;YACH,OAAO,cAAc,IAAI,UAAU,IAAI,CAAC,iBAAiB,MAAM;QACnE;IACJ;IAEA,SAAS,oBAAoB,QAAQ;QACjC,OAAO,SAAS,OAAO;YACnB,IAAI,SAAS,QAAQ,UAAU,CAAC,OAAO;YACvC,OAAO,cAAc,IAAI,UAAU,aAAa,CAAC;gBAC7C,UAAU;gBACV,QAAQ;YACZ;QACJ;IACJ;IAEA,SAAS,qBAAqB,OAAO;QACjC,OAAO,cAAc,UAAU,gBAAgB,CAAC;YAC5C,WAAW,QAAQ,UAAU,CAAC,OAAO;QACzC;IACJ;IAEA,SAAS,kBAAkB,OAAO;QAC9B,OAAO,gBAAgB,QAAQ,QAAQ;IAC3C;IAEA,IAAI,oBAAoB;QACpB,OAAO,SAAS,OAAO;YACnB,IAAI,6BAA6B,QAAQ,YAAY,CAAC;YAEtD,IAAI,YAAY,CAAC,CAAC,2BACb,YAAY,CAAC,SACb,KAAK,CAAC;YAEX,IAAI,WAAW;gBACX,QAAQ,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACnC,yBAAyB,IAAI,CAAC;gBAClC;gBACA,OAAO;YACX,OAAO;gBACH,IAAI,cAAc,QAAQ,QAAQ;gBAClC,IAAI,yBAAyB,MAAM,GAAG,GAAG;oBACrC,cAAc,yBAAyB,MAAM,CAAC;oBAC9C,2BAA2B,EAAE;gBACjC;gBACA,OAAO,WAAW,GAAG,CACjB,wBAAwB,6BACxB,gBAAgB,cAChB,SAAS,UAAU,EAAE,QAAQ;oBACzB,OAAO,IAAI,UAAU,SAAS,CAAC,UAAU;gBAC7C,GACF,WAAW;YACjB;QACJ;QACA,OAAO,SAAS,OAAO;YACnB,OAAO,WAAW,GAAG,CACjB,kBAAkB,QAAQ,YAAY,CAAC,WACvC,gBAAgB,QAAQ,QAAQ,GAChC,SAAS,UAAU,EAAE,QAAQ;gBACzB,IAAI,mBAAmB;gBACvB,IAAI,qBAAqB,MAAM;oBAC3B,WAAW;wBAAC,IAAI,UAAU,SAAS,CAAC,UAAU;qBAAkB;gBACpE;gBAEA,OAAO,IAAI,UAAU,GAAG,CAAC,UAAU;YACvC;QAER;QACA,aAAa;QACb,eAAe;QACf,OAAO,SAAS,OAAO;YACnB,OAAO,cAAc,IAAI,UAAU,IAAI,CAAC,QAAQ,IAAI;QACxD;QACA,SAAS,SAAS,OAAO;YACrB,OAAO,cAAc,IAAI,UAAU,GAAG;QAC1C;QACA,mBAAmB;YACf,OAAO,cAAc,IAAI,UAAU,IAAI,CAAC;QAC5C;QACA,gBAAgB,SAAS,OAAO;YAC5B,OAAO,cAAc,IAAI,UAAU,IAAI,CAAC;QAC5C;QACA,SAAS;QACT,eAAe,SAAS,OAAO;YAC3B,IAAI,iBAAiB,QAAQ,UAAU,CAAC,OAAO;YAC/C,IAAI,SAAS,QAAQ,UAAU,CAAC,WAAW;YAC3C,OAAO,gBAAgB,QAAQ,QAAQ,EAAE,GAAG,CAAC,SAAS,QAAQ;gBAC1D,SAAS,OAAO,OAAO;oBACnB,IAAI,cAAc,QAAQ,UAAU,CAAC,aAAa,IAAI;oBAEtD,OAAO,IAAI,UAAU,SAAS,CAC1B,UACA,EAAE,MAAM,CAAC;wBAAC,aAAa;oBAAW,GAAG;gBAE7C;gBAEA,IAAI,gBAAgB;oBAChB,IAAI,OAAO,cAAc,0BAA0B,CAAC;oBACpD,IAAI,QAAQ;wBACR,OAAO,KAAK,eAAe,CAAC,MAAM;oBACtC;oBACA,OAAO,OAAO;wBAAC,MAAM;oBAAI;gBAC7B,OAAO,IAAI,QAAQ;oBACf,OAAO,OAAO;wBAAC,QAAQ;oBAAM;gBACjC,OAAO;oBACH,OAAO;gBACX;YACJ;QACJ;QACA,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,uBAAuB,oBAAoB;QAC3C,sBAAsB,oBAAoB;QAC1C,sBAAsB;QACtB,QAAQ,SAAS,OAAO;YACpB,IAAI,YAAY,QAAQ,UAAU,CAAC,SAAS;YAC5C,IAAI,aAAa,QAAQ,cAAc,gBAAgB;gBACnD,OAAO,cAAc,UAAU,SAAS;YAC5C,OAAO,IAAI,cAAc,QAAQ;gBAC7B,OAAO,cAAc,UAAU,SAAS;YAC5C,OAAO,IAAI,cAAc,UAAU;gBAC/B,OAAO,cAAc,UAAU,WAAW;YAC9C,OAAO;gBACH,OAAO,wBAAwB;oBAAC,QAAQ,6BAA6B;iBAAW;YACpF;QACJ;QACA,mBAAmB,SAAS,OAAO;YAC/B,IAAI,OAAO,QAAQ,UAAU,CAAC,SAAS;YACvC,IAAI,SAAS,WAAW;gBACpB,OAAO;YACX,OAAO;gBACH,OAAO,cAAc,IAAI,UAAU,aAAa,CAAC;oBAAC,MAAM;gBAAI;YAChE;QACJ;QAEA,uBAAuB,SAAS,OAAO;YACnC,OAAO,kBAAkB,QAAQ,YAAY,CAAC;QAClD;QAEA,SAAS,SAAS,OAAO;YACrB,IAAI,WAAW,QACV,YAAY,CAAC,WACb,KAAK,CAAC;YAEX,IAAI,UAAU;gBACV,IAAI,iBAAiB,SAAS,KAAK,CAAC;gBACpC,IAAI,YAAY,CAAC,CAAC,kBAAkB,0BAChC,eAAe,UAAU,CAAC,aAAa;gBAE3C,OAAO,cAAc,UAAU,QAAQ,CAAC;oBACpC,SAAS;gBACb;YACJ,OAAO;gBACH,OAAO,gBAAgB,QAAQ,YAAY,CAAC,gBAAgB,QAAQ;YACxE;QACJ;QAEA,SAAS;QACT,YAAY;QACZ,cAAc;QACd,aAAa;QACb,UAAU,SAAS,OAAO;YACtB,OAAO,kBAAkB,SAAS,OAAO;QAC7C;QACA,eAAe;QACf,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,eAAe;QACf,WAAW;QACX,UAAU;IACd;IAEA,OAAO;QACH,gBAAgB;QAChB,iBAAiB;IACrB;;IAGA,SAAS,UAAU,OAAO;QACtB,IAAI,mBAAmB,oBAAoB,QAAQ,YAAY,CAAC;QAChE,OAAO,gBAAgB,QAAQ,QAAQ,EAClC,OAAO,CAAC,mBACR,OAAO,CAAC,SAAS,QAAQ;YACtB,OAAO,iBAAiB,GAAG,CAAC,SAAS,UAAU;gBAC3C,OAAO,UAAU,KAAK,CAAC,UAAU;YACrC;QACJ;IACR;IAEA,SAAS,oBAAoB,OAAO;QAChC,OAAO,eAAe,SAAS,GAAG,CAAC,SAAS,KAAK;YAC7C,OAAO;gBACH,SAAS,MAAM,OAAO;gBACtB,WAAW,MAAM,IAAI;YACzB;QACJ;IACJ;IAEA,SAAS,aAAa,OAAO;QACzB,IAAI,aAAa,QAAQ,YAAY,CAAC;QACtC,IAAI,WAAW,CAAC,CAAC,WAAW,KAAK,CAAC;QAClC,OAAO,gBAAgB,QAAQ,QAAQ,EAAE,GAAG,CAAC,SAAS,QAAQ;YAC1D,OAAO,UAAU,QAAQ,CAAC,UAAU;gBAAC,UAAU;YAAQ;QAC3D;IACJ;IAEA,SAAS,cAAc,OAAO;QAC1B,OAAO,gBAAgB,QAAQ,QAAQ,EAAE,GAAG,CAAC,SAAS,QAAQ;YAC1D,IAAI,aAAa,QAAQ,YAAY,CAAC;YAEtC,IAAI,WAAW,WAAW,YAAY,CAAC,cAAc,UAAU,CAAC,QAAQ;YACxE,IAAI,UAAU,WAAW,SAAS,UAAU,MAAM;YAElD,IAAI,OAAO,UAAU,SAAS,CAAC,UAAU;gBAAC,SAAS;YAAO;YAC1D,KAAK,OAAO,GAAG,WAAW;YAC1B,OAAO;QACX;IACJ;IAEA,SAAS,WAAW,UAAU;QAC1B,IAAI,UAAU,WAAW,KAAK,CAAC;QAC/B,IAAI,SAAS;YACT,IAAI,MAAM,QAAQ,UAAU,CAAC,QAAQ;YACrC,OAAO,QAAQ,cAAc,CAAC;QAClC,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,kBAAkB,IAAI;QAC3B,IAAI,oBAAoB,EAAE,GAAG,CAAC,MAAM,SAAS,GAAG;YAC5C,OAAO,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC,QAAQ;QAChD;QACA,IAAI,mBAAmB;YACnB,OAAO,0BAA0B,MAAM;gBAAC,QACpC;aACF;QACN;QACA,IAAI,qBAAqB,EAAE,GAAG,CAAC,MAAM,SAAS,GAAG;YAC7C,OAAO,EAAE,GAAG,CAAC,IAAI,QAAQ,EAAE,SAAS,IAAI;gBACpC,OAAO,KAAK,IAAI,KAAK,UAAU,KAAK,CAAC,SAAS;YAClD;QACJ;QACA,IAAI,oBAAoB;YACpB,OAAO,0BAA0B,MAAM;gBAAC,QACpC;aACF;QACN;QAEA,IAAI,UAAU,CAAC;QAEf,KAAK,OAAO,CAAC,SAAS,GAAG;YACrB,IAAI,YAAY;YAChB,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI;gBAC9B,IAAI,KAAK,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpC,OAAO,CAAC,UAAU,CAAC,OAAO;gBAC9B,OAAO;oBACH,OAAO,CAAC,UAAU,GAAG;oBACrB,KAAK,OAAO,GAAG;gBACnB;gBACA,aAAa,KAAK,OAAO;YAC7B;QACJ;QAEA,KAAK,OAAO,CAAC,SAAS,GAAG;YACrB,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,IAAI;gBAC5C,OAAO,CAAC,KAAK,OAAO;YACxB;YACA,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,IAAI;gBAC9B,OAAO,KAAK,OAAO;YACvB;QACJ;QAEA,OAAO,cAAc;IACzB;IAEA,SAAS,mBAAmB,OAAO;QAC/B,IAAI,QAAQ,QACP,oBAAoB,CAAC,aACrB,oBAAoB,CAAC,iBACrB,oBAAoB,CAAC,WACrB,oBAAoB,CAAC,gBACrB,oBAAoB,CAAC;QAE1B,OAAO,eAAe,MAAM,GAAG,CAAC,SAAS,IAAI,CAAC,MAAM;IACxD;IAEA,SAAS,SAAS,OAAO,EAAE,IAAI;QAC3B,IAAI,aAAa,QAAQ,KAAK,CAAC,YAAY,UAAU;QACrD,IAAI,UAAU,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK,GAAG,WAAW,KAAK;QAC7E,IAAI,gBAAgB,kBAAkB;QACtC,IAAI,kBAAkB,MAAM;YACxB,OAAO,wBAAwB;gBAAC,QAAQ;aAAgD;QAC5F,OAAO;YACH,OAAO,UAAU,eAAe;QACpC;IACJ;IAEA,SAAS,QAAQ,KAAK;QAClB,OAAO,SAAS,QAAQ,QAAQ,IAAI,CAAC;IACzC;IAEA,SAAS,kBAAkB,IAAI;QAC3B,IAAI,sBAAsB,KAAK,UAAU,CAAC,UAAU;QACpD,IAAI,qBAAqB,KAAK,UAAU,CAAC,SAAS;QAClD,IAAI,qBAAqB;YACrB,OAAO,sBAAsB;QACjC,OAAO,IAAI,oBAAoB;YAC3B,IAAI,YAAY,cAAc,0BAA0B,CAAC;YACzD,OAAO;gBACH,MAAM;gBACN,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;YACjC;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,cAAc,OAAO;QAC1B,IAAI,iBAAiB,QAAQ,UAAU,CAAC,OAAO;QAE/C,IAAI,gBAAgB;YAChB,OAAO,UACH,sBAAsB,iBACtB,QAAQ,UAAU,CAAC,UAAU;QACrC,OAAO;YACH,OAAO,wBAAwB;gBAAC,QAAQ;aAA+D;QAC3G;IACJ;IAEA,SAAS,sBAAsB,cAAc;QACzC,IAAI,OAAO,KAAK,iBAAiB,CAAC,QAAQ,cAAc,0BAA0B,CAAC;QACnF,OAAO;YACH,MAAM;YACN,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU;QACvC;IACJ;IAEA,SAAS,UAAU,SAAS,EAAE,OAAO;QACjC,IAAI,cAAc,aAAa,eAAe,CAAC,UAAU,IAAI;QAE7D,IAAI,QAAQ,UAAU,KAAK,CAAC;YACxB,WAAW,UAAU,IAAI;YACzB,SAAS;YACT,aAAa;QACjB;QACA,IAAI,WAAW,mBAAmB,CAAC,YAAY,GAC3C,EAAE,GAAG,QAAQ,mBAAmB,cAAc;QAClD,OAAO,0BAA0B,OAAO;IAC5C;IAEA,SAAS,sBAAsB,IAAI,EAAE,OAAO;QACxC,OAAO,QACH,OAAO,oBAAoB,UAAU;IAC7C;AACJ;AAGA,SAAS,wBAAwB,OAAO,EAAE,OAAO,EAAE,SAAS;IACxD,IAAI,QAAQ,QAAQ,YAAY,CAAC,UAAU,UAAU,CAAC,QAAQ;IAC9D,IAAI,QAAQ,QAAQ,YAAY,CAAC,WAAW,UAAU,CAAC,QAAQ;IAC/D,IAAI,UAAU,aAAa,UAAU,WAAW;QAC5C,OAAO,UAAU,SAAS,CAAC,OAAO;IACtC;IAEA,IAAI,WAAW,MAAM;QACjB,IAAI,iBAAiB,UAAU,2BAA2B,CAAC;QAC3D,IAAI,kBAAkB,MAAM;YACxB,OAAO;QACX;IACJ;IAEA,OAAO;AACX;AAEA,IAAI,sBAAsB;IACtB,aAAa;IACb,aAAa;IACb,cAAc;IACd,iBAAiB;IACjB,cAAc;AAClB;AAEA,IAAI,iBAAiB;IACjB,oBAAoB;IACpB,YAAY;IACZ,eAAe;IACf,mBAAmB;IACnB,iBAAiB;IACjB,YAAY;IACZ,cAAc;IACd,2BAA2B;IAC3B,uBAAuB;IACvB,qBAAqB;IACrB,SAAS;IACT,iBAAiB;IACjB,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,WAAW;IACX,aAAa;IACb,UAAU;IACV,UAAU;AACd;AAEA,SAAS,wBAAwB,QAAQ;IACrC,OAAO,IAAI,WAAW,MAAM,MAAM;AACtC;AAEA,SAAS;IACL,OAAO,IAAI,WAAW;AAC1B;AAEA,SAAS,cAAc,OAAO;IAC1B,OAAO,IAAI,WAAW;AAC1B;AAEA,SAAS,0BAA0B,OAAO,EAAE,QAAQ;IAChD,OAAO,IAAI,WAAW,SAAS,MAAM;AACzC;AAEA,SAAS,WAAW,OAAO,EAAE,KAAK,EAAE,QAAQ;IACxC,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE;IAC1B,IAAI,CAAC,KAAK,GAAG,SAAS,EAAE;IACxB,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO;QACtB,SAAS,IAAI,CAAC,KAAK;QACnB,OAAO;IACX,GAAG;IACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;AACzC;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG;IAC3B,OAAO,IAAI,WAAW,MAAM,aAAa,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ;AACnF;AAEA,WAAW,SAAS,CAAC,WAAW,GAAG;IAC/B,IAAI,QAAQ,IAAI,CAAC,KAAK;IACtB,IAAI,SAAS,MAAM,MAAM,EAAE;QACvB,OAAO,IAAI,WAAW,aAAa,IAAI,CAAC,KAAK,EAAE,QAAQ,MAAM,IAAI,CAAC,QAAQ;IAC9E,OAAO;QACH,OAAO,IAAI;IACf;AACJ;AAEA,WAAW,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI;IACpC,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK;QACxC,OAAO,KAAK,MAAM,OAAO;IAC7B;IACA,OAAO,IAAI,WAAW,OAAO,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,QAAQ;AACnE;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,IAAI;IACxC,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK;QAC5C,OAAO,KAAK,MAAM,OAAO,EAAE,OAAO;IACtC;IACA,OAAO,IAAI,WAAW,OAAO,KAAK,CAAC,OAAO,EAAE,aAAa,IAAI,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,KAAK,GAAG,OAAO,QAAQ;AAC7G;AAEA,WAAW,GAAG,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE,IAAI;IACzC,OAAO,IAAI,WACP,KAAK,MAAM,KAAK,EAAE,OAAO,KAAK,GAC9B,aAAa,MAAM,KAAK,EAAE,OAAO,KAAK,GACtC,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,QAAQ;AAE7C;AAEA,SAAS,eAAe,OAAO;IAC3B,IAAI,SAAS,OAAO,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS;IAC7C,OAAO,IAAI,WACP,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,KAAK,EAAE,aAChC,EAAE,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,KAAK,EAAE,WAAW,WACpD,OAAO,QAAQ;AAEvB;AAEA,SAAS,aAAa,KAAK,EAAE,MAAM;IAC/B,OAAO,EAAE,OAAO,CAAC;QAAC;QAAO;KAAO;AACpC;AAEA,SAAS,SAAS,KAAK;IACnB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/document-xml-reader.js"], "sourcesContent": ["exports.DocumentXmlReader = DocumentXmlReader;\n\nvar documents = require(\"../documents\");\nvar Result = require(\"../results\").Result;\n\n\nfunction DocumentXmlReader(options) {\n    var bodyReader = options.bodyReader;\n\n    function convertXmlToDocument(element) {\n        var body = element.first(\"w:body\");\n\n        if (body == null) {\n            throw new Error(\"Could not find the body element: are you sure this is a docx file?\");\n        }\n\n        var result = bodyReader.readXmlElements(body.children)\n            .map(function(children) {\n                return new documents.Document(children, {\n                    notes: options.notes,\n                    comments: options.comments\n                });\n            });\n        return new Result(result.value, result.messages);\n    }\n\n    return {\n        convertXmlToDocument: convertXmlToDocument\n    };\n}\n"], "names": [], "mappings": "AAAA,QAAQ,iBAAiB,GAAG;AAE5B,IAAI;AACJ,IAAI,SAAS,mGAAsB,MAAM;AAGzC,SAAS,kBAAkB,OAAO;IAC9B,IAAI,aAAa,QAAQ,UAAU;IAEnC,SAAS,qBAAqB,OAAO;QACjC,IAAI,OAAO,QAAQ,KAAK,CAAC;QAEzB,IAAI,QAAQ,MAAM;YACd,MAAM,IAAI,MAAM;QACpB;QAEA,IAAI,SAAS,WAAW,eAAe,CAAC,KAAK,QAAQ,EAChD,GAAG,CAAC,SAAS,QAAQ;YAClB,OAAO,IAAI,UAAU,QAAQ,CAAC,UAAU;gBACpC,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;YAC9B;QACJ;QACJ,OAAO,IAAI,OAAO,OAAO,KAAK,EAAE,OAAO,QAAQ;IACnD;IAEA,OAAO;QACH,sBAAsB;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/relationships-reader.js"], "sourcesContent": ["exports.readRelationships = readRelationships;\nexports.defaultValue = new Relationships([]);\nexports.Relationships = Relationships;\n\n\nfunction readRelationships(element) {\n    var relationships = [];\n    element.children.forEach(function(child) {\n        if (child.name === \"relationships:Relationship\") {\n            var relationship = {\n                relationshipId: child.attributes.Id,\n                target: child.attributes.Target,\n                type: child.attributes.Type\n            };\n            relationships.push(relationship);\n        }\n    });\n    return new Relationships(relationships);\n}\n\nfunction Relationships(relationships) {\n    var targetsByRelationshipId = {};\n    relationships.forEach(function(relationship) {\n        targetsByRelationshipId[relationship.relationshipId] = relationship.target;\n    });\n\n    var targetsByType = {};\n    relationships.forEach(function(relationship) {\n        if (!targetsByType[relationship.type]) {\n            targetsByType[relationship.type] = [];\n        }\n        targetsByType[relationship.type].push(relationship.target);\n    });\n\n    return {\n        findTargetByRelationshipId: function(relationshipId) {\n            return targetsByRelationshipId[relationshipId];\n        },\n        findTargetsByType: function(type) {\n            return targetsByType[type] || [];\n        }\n    };\n}\n"], "names": [], "mappings": "AAAA,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,YAAY,GAAG,IAAI,cAAc,EAAE;AAC3C,QAAQ,aAAa,GAAG;AAGxB,SAAS,kBAAkB,OAAO;IAC9B,IAAI,gBAAgB,EAAE;IACtB,QAAQ,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;QACnC,IAAI,MAAM,IAAI,KAAK,8BAA8B;YAC7C,IAAI,eAAe;gBACf,gBAAgB,MAAM,UAAU,CAAC,EAAE;gBACnC,QAAQ,MAAM,UAAU,CAAC,MAAM;gBAC/B,MAAM,MAAM,UAAU,CAAC,IAAI;YAC/B;YACA,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,OAAO,IAAI,cAAc;AAC7B;AAEA,SAAS,cAAc,aAAa;IAChC,IAAI,0BAA0B,CAAC;IAC/B,cAAc,OAAO,CAAC,SAAS,YAAY;QACvC,uBAAuB,CAAC,aAAa,cAAc,CAAC,GAAG,aAAa,MAAM;IAC9E;IAEA,IAAI,gBAAgB,CAAC;IACrB,cAAc,OAAO,CAAC,SAAS,YAAY;QACvC,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI,CAAC,EAAE;YACnC,aAAa,CAAC,aAAa,IAAI,CAAC,GAAG,EAAE;QACzC;QACA,aAAa,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,MAAM;IAC7D;IAEA,OAAO;QACH,4BAA4B,SAAS,cAAc;YAC/C,OAAO,uBAAuB,CAAC,eAAe;QAClD;QACA,mBAAmB,SAAS,IAAI;YAC5B,OAAO,aAAa,CAAC,KAAK,IAAI,EAAE;QACpC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/content-types-reader.js"], "sourcesContent": ["exports.readContentTypesFromXml = readContentTypesFromXml;\n\nvar fallbackContentTypes = {\n    \"png\": \"png\",\n    \"gif\": \"gif\",\n    \"jpeg\": \"jpeg\",\n    \"jpg\": \"jpeg\",\n    \"tif\": \"tiff\",\n    \"tiff\": \"tiff\",\n    \"bmp\": \"bmp\"\n};\n\nexports.defaultContentTypes = contentTypes({}, {});\n\n\nfunction readContentTypesFromXml(element) {\n    var extensionDefaults = {};\n    var overrides = {};\n    \n    element.children.forEach(function(child) {\n        if (child.name === \"content-types:Default\") {\n            extensionDefaults[child.attributes.Extension] = child.attributes.ContentType;\n        }\n        if (child.name === \"content-types:Override\") {\n            var name = child.attributes.PartName;\n            if (name.charAt(0) === \"/\") {\n                name = name.substring(1);\n            }\n            overrides[name] = child.attributes.ContentType;\n        }\n    });\n    return contentTypes(overrides, extensionDefaults);\n}\n\nfunction contentTypes(overrides, extensionDefaults) {\n    return {\n        findContentType: function(path) {\n            var overrideContentType = overrides[path];\n            if (overrideContentType) {\n                return overrideContentType;\n            } else {\n                var pathParts = path.split(\".\");\n                var extension = pathParts[pathParts.length - 1];\n                if (extensionDefaults.hasOwnProperty(extension)) {\n                    return extensionDefaults[extension];\n                } else {\n                    var fallback = fallbackContentTypes[extension.toLowerCase()];\n                    if (fallback) {\n                        return \"image/\" + fallback;\n                    } else {\n                        return null;\n                    }\n                }\n            }\n        }\n    };\n    \n}\n"], "names": [], "mappings": "AAAA,QAAQ,uBAAuB,GAAG;AAElC,IAAI,uBAAuB;IACvB,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;AACX;AAEA,QAAQ,mBAAmB,GAAG,aAAa,CAAC,GAAG,CAAC;AAGhD,SAAS,wBAAwB,OAAO;IACpC,IAAI,oBAAoB,CAAC;IACzB,IAAI,YAAY,CAAC;IAEjB,QAAQ,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;QACnC,IAAI,MAAM,IAAI,KAAK,yBAAyB;YACxC,iBAAiB,CAAC,MAAM,UAAU,CAAC,SAAS,CAAC,GAAG,MAAM,UAAU,CAAC,WAAW;QAChF;QACA,IAAI,MAAM,IAAI,KAAK,0BAA0B;YACzC,IAAI,OAAO,MAAM,UAAU,CAAC,QAAQ;YACpC,IAAI,KAAK,MAAM,CAAC,OAAO,KAAK;gBACxB,OAAO,KAAK,SAAS,CAAC;YAC1B;YACA,SAAS,CAAC,KAAK,GAAG,MAAM,UAAU,CAAC,WAAW;QAClD;IACJ;IACA,OAAO,aAAa,WAAW;AACnC;AAEA,SAAS,aAAa,SAAS,EAAE,iBAAiB;IAC9C,OAAO;QACH,iBAAiB,SAAS,IAAI;YAC1B,IAAI,sBAAsB,SAAS,CAAC,KAAK;YACzC,IAAI,qBAAqB;gBACrB,OAAO;YACX,OAAO;gBACH,IAAI,YAAY,KAAK,KAAK,CAAC;gBAC3B,IAAI,YAAY,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;gBAC/C,IAAI,kBAAkB,cAAc,CAAC,YAAY;oBAC7C,OAAO,iBAAiB,CAAC,UAAU;gBACvC,OAAO;oBACH,IAAI,WAAW,oBAAoB,CAAC,UAAU,WAAW,GAAG;oBAC5D,IAAI,UAAU;wBACV,OAAO,WAAW;oBACtB,OAAO;wBACH,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1504, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/numbering-xml.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nexports.readNumberingXml = readNumberingXml;\nexports.Numbering = Numbering;\nexports.defaultNumbering = new Numbering({}, {});\n\nfunction Numbering(nums, abstractNums, styles) {\n    var allLevels = _.flatten(_.values(abstractNums).map(function(abstractNum) {\n        return _.values(abstractNum.levels);\n    }));\n\n    var levelsByParagraphStyleId = _.indexBy(\n        allLevels.filter(function(level) {\n            return level.paragraphStyleId != null;\n        }),\n        \"paragraphStyleId\"\n    );\n\n    function findLevel(numId, level) {\n        var num = nums[numId];\n        if (num) {\n            var abstractNum = abstractNums[num.abstractNumId];\n            if (!abstractNum) {\n                return null;\n            } else if (abstractNum.numStyleLink == null) {\n                return abstractNums[num.abstractNumId].levels[level];\n            } else {\n                var style = styles.findNumberingStyleById(abstractNum.numStyleLink);\n                return findLevel(style.numId, level);\n            }\n        } else {\n            return null;\n        }\n    }\n\n    function findLevelByParagraphStyleId(styleId) {\n        return levelsByParagraphStyleId[styleId] || null;\n    }\n\n    return {\n        findLevel: findLevel,\n        findLevelByParagraphStyleId: findLevelByParagraphStyleId\n    };\n}\n\nfunction readNumberingXml(root, options) {\n    if (!options || !options.styles) {\n        throw new Error(\"styles is missing\");\n    }\n\n    var abstractNums = readAbstractNums(root);\n    var nums = readNums(root, abstractNums);\n    return new Numbering(nums, abstractNums, options.styles);\n}\n\nfunction readAbstractNums(root) {\n    var abstractNums = {};\n    root.getElementsByTagName(\"w:abstractNum\").forEach(function(element) {\n        var id = element.attributes[\"w:abstractNumId\"];\n        abstractNums[id] = readAbstractNum(element);\n    });\n    return abstractNums;\n}\n\nfunction readAbstractNum(element) {\n    var levels = {};\n    element.getElementsByTagName(\"w:lvl\").forEach(function(levelElement) {\n        var levelIndex = levelElement.attributes[\"w:ilvl\"];\n        var numFmt = levelElement.firstOrEmpty(\"w:numFmt\").attributes[\"w:val\"];\n        var paragraphStyleId = levelElement.firstOrEmpty(\"w:pStyle\").attributes[\"w:val\"];\n\n        levels[levelIndex] = {\n            isOrdered: numFmt !== \"bullet\",\n            level: levelIndex,\n            paragraphStyleId: paragraphStyleId\n        };\n    });\n\n    var numStyleLink = element.firstOrEmpty(\"w:numStyleLink\").attributes[\"w:val\"];\n\n    return {levels: levels, numStyleLink: numStyleLink};\n}\n\nfunction readNums(root) {\n    var nums = {};\n    root.getElementsByTagName(\"w:num\").forEach(function(element) {\n        var numId = element.attributes[\"w:numId\"];\n        var abstractNumId = element.first(\"w:abstractNumId\").attributes[\"w:val\"];\n        nums[numId] = {abstractNumId: abstractNumId};\n    });\n    return nums;\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,SAAS,GAAG;AACpB,QAAQ,gBAAgB,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC;AAE9C,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM;IACzC,IAAI,YAAY,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,cAAc,GAAG,CAAC,SAAS,WAAW;QACrE,OAAO,EAAE,MAAM,CAAC,YAAY,MAAM;IACtC;IAEA,IAAI,2BAA2B,EAAE,OAAO,CACpC,UAAU,MAAM,CAAC,SAAS,KAAK;QAC3B,OAAO,MAAM,gBAAgB,IAAI;IACrC,IACA;IAGJ,SAAS,UAAU,KAAK,EAAE,KAAK;QAC3B,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,IAAI,KAAK;YACL,IAAI,cAAc,YAAY,CAAC,IAAI,aAAa,CAAC;YACjD,IAAI,CAAC,aAAa;gBACd,OAAO;YACX,OAAO,IAAI,YAAY,YAAY,IAAI,MAAM;gBACzC,OAAO,YAAY,CAAC,IAAI,aAAa,CAAC,CAAC,MAAM,CAAC,MAAM;YACxD,OAAO;gBACH,IAAI,QAAQ,OAAO,sBAAsB,CAAC,YAAY,YAAY;gBAClE,OAAO,UAAU,MAAM,KAAK,EAAE;YAClC;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS,4BAA4B,OAAO;QACxC,OAAO,wBAAwB,CAAC,QAAQ,IAAI;IAChD;IAEA,OAAO;QACH,WAAW;QACX,6BAA6B;IACjC;AACJ;AAEA,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;QAC7B,MAAM,IAAI,MAAM;IACpB;IAEA,IAAI,eAAe,iBAAiB;IACpC,IAAI,OAAO,SAAS,MAAM;IAC1B,OAAO,IAAI,UAAU,MAAM,cAAc,QAAQ,MAAM;AAC3D;AAEA,SAAS,iBAAiB,IAAI;IAC1B,IAAI,eAAe,CAAC;IACpB,KAAK,oBAAoB,CAAC,iBAAiB,OAAO,CAAC,SAAS,OAAO;QAC/D,IAAI,KAAK,QAAQ,UAAU,CAAC,kBAAkB;QAC9C,YAAY,CAAC,GAAG,GAAG,gBAAgB;IACvC;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,OAAO;IAC5B,IAAI,SAAS,CAAC;IACd,QAAQ,oBAAoB,CAAC,SAAS,OAAO,CAAC,SAAS,YAAY;QAC/D,IAAI,aAAa,aAAa,UAAU,CAAC,SAAS;QAClD,IAAI,SAAS,aAAa,YAAY,CAAC,YAAY,UAAU,CAAC,QAAQ;QACtE,IAAI,mBAAmB,aAAa,YAAY,CAAC,YAAY,UAAU,CAAC,QAAQ;QAEhF,MAAM,CAAC,WAAW,GAAG;YACjB,WAAW,WAAW;YACtB,OAAO;YACP,kBAAkB;QACtB;IACJ;IAEA,IAAI,eAAe,QAAQ,YAAY,CAAC,kBAAkB,UAAU,CAAC,QAAQ;IAE7E,OAAO;QAAC,QAAQ;QAAQ,cAAc;IAAY;AACtD;AAEA,SAAS,SAAS,IAAI;IAClB,IAAI,OAAO,CAAC;IACZ,KAAK,oBAAoB,CAAC,SAAS,OAAO,CAAC,SAAS,OAAO;QACvD,IAAI,QAAQ,QAAQ,UAAU,CAAC,UAAU;QACzC,IAAI,gBAAgB,QAAQ,KAAK,CAAC,mBAAmB,UAAU,CAAC,QAAQ;QACxE,IAAI,CAAC,MAAM,GAAG;YAAC,eAAe;QAAa;IAC/C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/styles-reader.js"], "sourcesContent": ["exports.readStylesXml = readStylesXml;\nexports.Styles = Styles;\nexports.defaultStyles = new Styles({}, {});\n\nfunction Styles(paragraphStyles, characterStyles, tableStyles, numberingStyles) {\n    return {\n        findParagraphStyleById: function(styleId) {\n            return paragraphStyles[styleId];\n        },\n        findCharacterStyleById: function(styleId) {\n            return characterStyles[styleId];\n        },\n        findTableStyleById: function(styleId) {\n            return tableStyles[styleId];\n        },\n        findNumberingStyleById: function(styleId) {\n            return numberingStyles[styleId];\n        }\n    };\n}\n\nStyles.EMPTY = new Styles({}, {}, {}, {});\n\nfunction readStylesXml(root) {\n    var paragraphStyles = {};\n    var characterStyles = {};\n    var tableStyles = {};\n    var numberingStyles = {};\n\n    var styles = {\n        \"paragraph\": paragraphStyles,\n        \"character\": characterStyles,\n        \"table\": tableStyles\n    };\n\n    root.getElementsByTagName(\"w:style\").forEach(function(styleElement) {\n        var style = readStyleElement(styleElement);\n        if (style.type === \"numbering\") {\n            numberingStyles[style.styleId] = readNumberingStyleElement(styleElement);\n        } else {\n            var styleSet = styles[style.type];\n            if (styleSet) {\n                styleSet[style.styleId] = style;\n            }\n        }\n    });\n\n    return new Styles(paragraphStyles, characterStyles, tableStyles, numberingStyles);\n}\n\nfunction readStyleElement(styleElement) {\n    var type = styleElement.attributes[\"w:type\"];\n    var styleId = styleElement.attributes[\"w:styleId\"];\n    var name = styleName(styleElement);\n    return {type: type, styleId: styleId, name: name};\n}\n\nfunction styleName(styleElement) {\n    var nameElement = styleElement.first(\"w:name\");\n    return nameElement ? nameElement.attributes[\"w:val\"] : null;\n}\n\nfunction readNumberingStyleElement(styleElement) {\n    var numId = styleElement\n        .firstOrEmpty(\"w:pPr\")\n        .firstOrEmpty(\"w:numPr\")\n        .firstOrEmpty(\"w:numId\")\n        .attributes[\"w:val\"];\n    return {numId: numId};\n}\n"], "names": [], "mappings": "AAAA,QAAQ,aAAa,GAAG;AACxB,QAAQ,MAAM,GAAG;AACjB,QAAQ,aAAa,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;AAExC,SAAS,OAAO,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe;IAC1E,OAAO;QACH,wBAAwB,SAAS,OAAO;YACpC,OAAO,eAAe,CAAC,QAAQ;QACnC;QACA,wBAAwB,SAAS,OAAO;YACpC,OAAO,eAAe,CAAC,QAAQ;QACnC;QACA,oBAAoB,SAAS,OAAO;YAChC,OAAO,WAAW,CAAC,QAAQ;QAC/B;QACA,wBAAwB,SAAS,OAAO;YACpC,OAAO,eAAe,CAAC,QAAQ;QACnC;IACJ;AACJ;AAEA,OAAO,KAAK,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAEvC,SAAS,cAAc,IAAI;IACvB,IAAI,kBAAkB,CAAC;IACvB,IAAI,kBAAkB,CAAC;IACvB,IAAI,cAAc,CAAC;IACnB,IAAI,kBAAkB,CAAC;IAEvB,IAAI,SAAS;QACT,aAAa;QACb,aAAa;QACb,SAAS;IACb;IAEA,KAAK,oBAAoB,CAAC,WAAW,OAAO,CAAC,SAAS,YAAY;QAC9D,IAAI,QAAQ,iBAAiB;QAC7B,IAAI,MAAM,IAAI,KAAK,aAAa;YAC5B,eAAe,CAAC,MAAM,OAAO,CAAC,GAAG,0BAA0B;QAC/D,OAAO;YACH,IAAI,WAAW,MAAM,CAAC,MAAM,IAAI,CAAC;YACjC,IAAI,UAAU;gBACV,QAAQ,CAAC,MAAM,OAAO,CAAC,GAAG;YAC9B;QACJ;IACJ;IAEA,OAAO,IAAI,OAAO,iBAAiB,iBAAiB,aAAa;AACrE;AAEA,SAAS,iBAAiB,YAAY;IAClC,IAAI,OAAO,aAAa,UAAU,CAAC,SAAS;IAC5C,IAAI,UAAU,aAAa,UAAU,CAAC,YAAY;IAClD,IAAI,OAAO,UAAU;IACrB,OAAO;QAAC,MAAM;QAAM,SAAS;QAAS,MAAM;IAAI;AACpD;AAEA,SAAS,UAAU,YAAY;IAC3B,IAAI,cAAc,aAAa,KAAK,CAAC;IACrC,OAAO,cAAc,YAAY,UAAU,CAAC,QAAQ,GAAG;AAC3D;AAEA,SAAS,0BAA0B,YAAY;IAC3C,IAAI,QAAQ,aACP,YAAY,CAAC,SACb,YAAY,CAAC,WACb,YAAY,CAAC,WACb,UAAU,CAAC,QAAQ;IACxB,OAAO;QAAC,OAAO;IAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/notes-reader.js"], "sourcesContent": ["var documents = require(\"../documents\");\nvar Result = require(\"../results\").Result;\n\nexports.createFootnotesReader = createReader.bind(this, \"footnote\");\nexports.createEndnotesReader = createReader.bind(this, \"endnote\");\n\nfunction createReader(noteType, bodyReader) {\n    function readNotesXml(element) {\n        return Result.combine(element.getElementsByTagName(\"w:\" + noteType)\n            .filter(isFootnoteElement)\n            .map(readFootnoteElement));\n    }\n\n    function isFootnoteElement(element) {\n        var type = element.attributes[\"w:type\"];\n        return type !== \"continuationSeparator\" && type !== \"separator\";\n    }\n\n    function readFootnoteElement(footnoteElement) {\n        var id = footnoteElement.attributes[\"w:id\"];\n        return bodyReader.readXmlElements(footnoteElement.children)\n            .map(function(body) {\n                return documents.Note({noteType: noteType, noteId: id, body: body});\n            });\n    }\n    \n    return readNotesXml;\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,SAAS,mGAAsB,MAAM;AAEzC,QAAQ,qBAAqB,GAAG,aAAa,IAAI,CAAC,IAAI,EAAE;AACxD,QAAQ,oBAAoB,GAAG,aAAa,IAAI,CAAC,IAAI,EAAE;AAEvD,SAAS,aAAa,QAAQ,EAAE,UAAU;IACtC,SAAS,aAAa,OAAO;QACzB,OAAO,OAAO,OAAO,CAAC,QAAQ,oBAAoB,CAAC,OAAO,UACrD,MAAM,CAAC,mBACP,GAAG,CAAC;IACb;IAEA,SAAS,kBAAkB,OAAO;QAC9B,IAAI,OAAO,QAAQ,UAAU,CAAC,SAAS;QACvC,OAAO,SAAS,2BAA2B,SAAS;IACxD;IAEA,SAAS,oBAAoB,eAAe;QACxC,IAAI,KAAK,gBAAgB,UAAU,CAAC,OAAO;QAC3C,OAAO,WAAW,eAAe,CAAC,gBAAgB,QAAQ,EACrD,GAAG,CAAC,SAAS,IAAI;YACd,OAAO,UAAU,IAAI,CAAC;gBAAC,UAAU;gBAAU,QAAQ;gBAAI,MAAM;YAAI;QACrE;IACR;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/comments-reader.js"], "sourcesContent": ["var documents = require(\"../documents\");\nvar Result = require(\"../results\").Result;\n\nfunction createCommentsReader(bodyReader) {\n    function readCommentsXml(element) {\n        return Result.combine(element.getElementsByTagName(\"w:comment\")\n            .map(readCommentElement));\n    }\n\n    function readCommentElement(element) {\n        var id = element.attributes[\"w:id\"];\n\n        function readOptionalAttribute(name) {\n            return (element.attributes[name] || \"\").trim() || null;\n        }\n\n        return bodyReader.readXmlElements(element.children)\n            .map(function(body) {\n                return documents.comment({\n                    commentId: id,\n                    body: body,\n                    authorName: readOptionalAttribute(\"w:author\"),\n                    authorInitials: readOptionalAttribute(\"w:initials\")\n                });\n            });\n    }\n    \n    return readCommentsXml;\n}\n\nexports.createCommentsReader = createCommentsReader;\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,SAAS,mGAAsB,MAAM;AAEzC,SAAS,qBAAqB,UAAU;IACpC,SAAS,gBAAgB,OAAO;QAC5B,OAAO,OAAO,OAAO,CAAC,QAAQ,oBAAoB,CAAC,aAC9C,GAAG,CAAC;IACb;IAEA,SAAS,mBAAmB,OAAO;QAC/B,IAAI,KAAK,QAAQ,UAAU,CAAC,OAAO;QAEnC,SAAS,sBAAsB,IAAI;YAC/B,OAAO,CAAC,QAAQ,UAAU,CAAC,KAAK,IAAI,EAAE,EAAE,IAAI,MAAM;QACtD;QAEA,OAAO,WAAW,eAAe,CAAC,QAAQ,QAAQ,EAC7C,GAAG,CAAC,SAAS,IAAI;YACd,OAAO,UAAU,OAAO,CAAC;gBACrB,WAAW;gBACX,MAAM;gBACN,YAAY,sBAAsB;gBAClC,gBAAgB,sBAAsB;YAC1C;QACJ;IACR;IAEA,OAAO;AACX;AAEA,QAAQ,oBAAoB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/browser/docx/files.js"], "sourcesContent": ["var promises = require(\"../../lib/promises\");\n\nexports.Files = Files;\n\n\nfunction Files() {\n    function read(uri) {\n        return promises.reject(new Error(\"could not open external image: '\" + uri + \"'\\ncannot open linked files from a web browser\"));\n    }\n    \n    return {\n        read: read\n    };\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,KAAK,GAAG;AAGhB,SAAS;IACL,SAAS,KAAK,GAAG;QACb,OAAO,SAAS,MAAM,CAAC,IAAI,MAAM,qCAAqC,MAAM;IAChF;IAEA,OAAO;QACH,MAAM;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1729, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/docx-reader.js"], "sourcesContent": ["exports.read = read;\nexports._findPartPaths = findPartPaths;\n\nvar promises = require(\"../promises\");\nvar documents = require(\"../documents\");\nvar Result = require(\"../results\").Result;\nvar zipfile = require(\"../zipfile\");\n\nvar readXmlFromZipFile = require(\"./office-xml-reader\").readXmlFromZipFile;\nvar createBodyReader = require(\"./body-reader\").createBodyReader;\nvar DocumentXmlReader = require(\"./document-xml-reader\").DocumentXmlReader;\nvar relationshipsReader = require(\"./relationships-reader\");\nvar contentTypesReader = require(\"./content-types-reader\");\nvar numberingXml = require(\"./numbering-xml\");\nvar stylesReader = require(\"./styles-reader\");\nvar notesReader = require(\"./notes-reader\");\nvar commentsReader = require(\"./comments-reader\");\nvar Files = require(\"./files\").Files;\n\n\nfunction read(docxFile, input) {\n    input = input || {};\n\n    return promises.props({\n        contentTypes: readContentTypesFromZipFile(docxFile),\n        partPaths: findPartPaths(docxFile),\n        docxFile: docxFile,\n        files: input.path ? Files.relativeToFile(input.path) : new Files(null)\n    }).also(function(result) {\n        return {\n            styles: readStylesFromZipFile(docxFile, result.partPaths.styles)\n        };\n    }).also(function(result) {\n        return {\n            numbering: readNumberingFromZipFile(docxFile, result.partPaths.numbering, result.styles)\n        };\n    }).also(function(result) {\n        return {\n            footnotes: readXmlFileWithBody(result.partPaths.footnotes, result, function(bodyReader, xml) {\n                if (xml) {\n                    return notesReader.createFootnotesReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            }),\n            endnotes: readXmlFileWithBody(result.partPaths.endnotes, result, function(bodyReader, xml) {\n                if (xml) {\n                    return notesReader.createEndnotesReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            }),\n            comments: readXmlFileWithBody(result.partPaths.comments, result, function(bodyReader, xml) {\n                if (xml) {\n                    return commentsReader.createCommentsReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            })\n        };\n    }).also(function(result) {\n        return {\n            notes: result.footnotes.flatMap(function(footnotes) {\n                return result.endnotes.map(function(endnotes) {\n                    return new documents.Notes(footnotes.concat(endnotes));\n                });\n            })\n        };\n    }).then(function(result) {\n        return readXmlFileWithBody(result.partPaths.mainDocument, result, function(bodyReader, xml) {\n            return result.notes.flatMap(function(notes) {\n                return result.comments.flatMap(function(comments) {\n                    var reader = new DocumentXmlReader({\n                        bodyReader: bodyReader,\n                        notes: notes,\n                        comments: comments\n                    });\n                    return reader.convertXmlToDocument(xml);\n                });\n            });\n        });\n    });\n}\n\nfunction findPartPaths(docxFile) {\n    return readPackageRelationships(docxFile).then(function(packageRelationships) {\n        var mainDocumentPath = findPartPath({\n            docxFile: docxFile,\n            relationships: packageRelationships,\n            relationshipType: \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\",\n            basePath: \"\",\n            fallbackPath: \"word/document.xml\"\n        });\n\n        if (!docxFile.exists(mainDocumentPath)) {\n            throw new Error(\"Could not find main document part. Are you sure this is a valid .docx file?\");\n        }\n\n        return xmlFileReader({\n            filename: relationshipsFilename(mainDocumentPath),\n            readElement: relationshipsReader.readRelationships,\n            defaultValue: relationshipsReader.defaultValue\n        })(docxFile).then(function(documentRelationships) {\n            function findPartRelatedToMainDocument(name) {\n                return findPartPath({\n                    docxFile: docxFile,\n                    relationships: documentRelationships,\n                    relationshipType: \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/\" + name,\n                    basePath: zipfile.splitPath(mainDocumentPath).dirname,\n                    fallbackPath: \"word/\" + name + \".xml\"\n                });\n            }\n\n            return {\n                mainDocument: mainDocumentPath,\n                comments: findPartRelatedToMainDocument(\"comments\"),\n                endnotes: findPartRelatedToMainDocument(\"endnotes\"),\n                footnotes: findPartRelatedToMainDocument(\"footnotes\"),\n                numbering: findPartRelatedToMainDocument(\"numbering\"),\n                styles: findPartRelatedToMainDocument(\"styles\")\n            };\n        });\n    });\n}\n\nfunction findPartPath(options) {\n    var docxFile = options.docxFile;\n    var relationships = options.relationships;\n    var relationshipType = options.relationshipType;\n    var basePath = options.basePath;\n    var fallbackPath = options.fallbackPath;\n\n    var targets = relationships.findTargetsByType(relationshipType);\n    var normalisedTargets = targets.map(function(target) {\n        return stripPrefix(zipfile.joinPath(basePath, target), \"/\");\n    });\n    var validTargets = normalisedTargets.filter(function(target) {\n        return docxFile.exists(target);\n    });\n    if (validTargets.length === 0) {\n        return fallbackPath;\n    } else {\n        return validTargets[0];\n    }\n}\n\nfunction stripPrefix(value, prefix) {\n    if (value.substring(0, prefix.length) === prefix) {\n        return value.substring(prefix.length);\n    } else {\n        return value;\n    }\n}\n\nfunction xmlFileReader(options) {\n    return function(zipFile) {\n        return readXmlFromZipFile(zipFile, options.filename)\n            .then(function(element) {\n                return element ? options.readElement(element) : options.defaultValue;\n            });\n    };\n}\n\nfunction readXmlFileWithBody(filename, options, func) {\n    var readRelationshipsFromZipFile = xmlFileReader({\n        filename: relationshipsFilename(filename),\n        readElement: relationshipsReader.readRelationships,\n        defaultValue: relationshipsReader.defaultValue\n    });\n\n    return readRelationshipsFromZipFile(options.docxFile).then(function(relationships) {\n        var bodyReader = new createBodyReader({\n            relationships: relationships,\n            contentTypes: options.contentTypes,\n            docxFile: options.docxFile,\n            numbering: options.numbering,\n            styles: options.styles,\n            files: options.files\n        });\n        return readXmlFromZipFile(options.docxFile, filename)\n            .then(function(xml) {\n                return func(bodyReader, xml);\n            });\n    });\n}\n\nfunction relationshipsFilename(filename) {\n    var split = zipfile.splitPath(filename);\n    return zipfile.joinPath(split.dirname, \"_rels\", split.basename + \".rels\");\n}\n\nvar readContentTypesFromZipFile = xmlFileReader({\n    filename: \"[Content_Types].xml\",\n    readElement: contentTypesReader.readContentTypesFromXml,\n    defaultValue: contentTypesReader.defaultContentTypes\n});\n\nfunction readNumberingFromZipFile(zipFile, path, styles) {\n    return xmlFileReader({\n        filename: path,\n        readElement: function(element) {\n            return numberingXml.readNumberingXml(element, {styles: styles});\n        },\n        defaultValue: numberingXml.defaultNumbering\n    })(zipFile);\n}\n\nfunction readStylesFromZipFile(zipFile, path) {\n    return xmlFileReader({\n        filename: path,\n        readElement: stylesReader.readStylesXml,\n        defaultValue: stylesReader.defaultStyles\n    })(zipFile);\n}\n\nvar readPackageRelationships = xmlFileReader({\n    filename: \"_rels/.rels\",\n    readElement: relationshipsReader.readRelationships,\n    defaultValue: relationshipsReader.defaultValue\n});\n"], "names": [], "mappings": "AAAA,QAAQ,IAAI,GAAG;AACf,QAAQ,cAAc,GAAG;AAEzB,IAAI;AACJ,IAAI;AACJ,IAAI,SAAS,mGAAsB,MAAM;AACzC,IAAI;AAEJ,IAAI,qBAAqB,kHAA+B,kBAAkB;AAC1E,IAAI,mBAAmB,4GAAyB,gBAAgB;AAChE,IAAI,oBAAoB,oHAAiC,iBAAiB;AAC1E,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,QAAQ,0GAAmB,KAAK;AAGpC,SAAS,KAAK,QAAQ,EAAE,KAAK;IACzB,QAAQ,SAAS,CAAC;IAElB,OAAO,SAAS,KAAK,CAAC;QAClB,cAAc,4BAA4B;QAC1C,WAAW,cAAc;QACzB,UAAU;QACV,OAAO,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM;IACrE,GAAG,IAAI,CAAC,SAAS,MAAM;QACnB,OAAO;YACH,QAAQ,sBAAsB,UAAU,OAAO,SAAS,CAAC,MAAM;QACnE;IACJ,GAAG,IAAI,CAAC,SAAS,MAAM;QACnB,OAAO;YACH,WAAW,yBAAyB,UAAU,OAAO,SAAS,CAAC,SAAS,EAAE,OAAO,MAAM;QAC3F;IACJ,GAAG,IAAI,CAAC,SAAS,MAAM;QACnB,OAAO;YACH,WAAW,oBAAoB,OAAO,SAAS,CAAC,SAAS,EAAE,QAAQ,SAAS,UAAU,EAAE,GAAG;gBACvF,IAAI,KAAK;oBACL,OAAO,YAAY,qBAAqB,CAAC,YAAY;gBACzD,OAAO;oBACH,OAAO,IAAI,OAAO,EAAE;gBACxB;YACJ;YACA,UAAU,oBAAoB,OAAO,SAAS,CAAC,QAAQ,EAAE,QAAQ,SAAS,UAAU,EAAE,GAAG;gBACrF,IAAI,KAAK;oBACL,OAAO,YAAY,oBAAoB,CAAC,YAAY;gBACxD,OAAO;oBACH,OAAO,IAAI,OAAO,EAAE;gBACxB;YACJ;YACA,UAAU,oBAAoB,OAAO,SAAS,CAAC,QAAQ,EAAE,QAAQ,SAAS,UAAU,EAAE,GAAG;gBACrF,IAAI,KAAK;oBACL,OAAO,eAAe,oBAAoB,CAAC,YAAY;gBAC3D,OAAO;oBACH,OAAO,IAAI,OAAO,EAAE;gBACxB;YACJ;QACJ;IACJ,GAAG,IAAI,CAAC,SAAS,MAAM;QACnB,OAAO;YACH,OAAO,OAAO,SAAS,CAAC,OAAO,CAAC,SAAS,SAAS;gBAC9C,OAAO,OAAO,QAAQ,CAAC,GAAG,CAAC,SAAS,QAAQ;oBACxC,OAAO,IAAI,UAAU,KAAK,CAAC,UAAU,MAAM,CAAC;gBAChD;YACJ;QACJ;IACJ,GAAG,IAAI,CAAC,SAAS,MAAM;QACnB,OAAO,oBAAoB,OAAO,SAAS,CAAC,YAAY,EAAE,QAAQ,SAAS,UAAU,EAAE,GAAG;YACtF,OAAO,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,KAAK;gBACtC,OAAO,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,QAAQ;oBAC5C,IAAI,SAAS,IAAI,kBAAkB;wBAC/B,YAAY;wBACZ,OAAO;wBACP,UAAU;oBACd;oBACA,OAAO,OAAO,oBAAoB,CAAC;gBACvC;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,QAAQ;IAC3B,OAAO,yBAAyB,UAAU,IAAI,CAAC,SAAS,oBAAoB;QACxE,IAAI,mBAAmB,aAAa;YAChC,UAAU;YACV,eAAe;YACf,kBAAkB;YAClB,UAAU;YACV,cAAc;QAClB;QAEA,IAAI,CAAC,SAAS,MAAM,CAAC,mBAAmB;YACpC,MAAM,IAAI,MAAM;QACpB;QAEA,OAAO,cAAc;YACjB,UAAU,sBAAsB;YAChC,aAAa,oBAAoB,iBAAiB;YAClD,cAAc,oBAAoB,YAAY;QAClD,GAAG,UAAU,IAAI,CAAC,SAAS,qBAAqB;YAC5C,SAAS,8BAA8B,IAAI;gBACvC,OAAO,aAAa;oBAChB,UAAU;oBACV,eAAe;oBACf,kBAAkB,yEAAyE;oBAC3F,UAAU,QAAQ,SAAS,CAAC,kBAAkB,OAAO;oBACrD,cAAc,UAAU,OAAO;gBACnC;YACJ;YAEA,OAAO;gBACH,cAAc;gBACd,UAAU,8BAA8B;gBACxC,UAAU,8BAA8B;gBACxC,WAAW,8BAA8B;gBACzC,WAAW,8BAA8B;gBACzC,QAAQ,8BAA8B;YAC1C;QACJ;IACJ;AACJ;AAEA,SAAS,aAAa,OAAO;IACzB,IAAI,WAAW,QAAQ,QAAQ;IAC/B,IAAI,gBAAgB,QAAQ,aAAa;IACzC,IAAI,mBAAmB,QAAQ,gBAAgB;IAC/C,IAAI,WAAW,QAAQ,QAAQ;IAC/B,IAAI,eAAe,QAAQ,YAAY;IAEvC,IAAI,UAAU,cAAc,iBAAiB,CAAC;IAC9C,IAAI,oBAAoB,QAAQ,GAAG,CAAC,SAAS,MAAM;QAC/C,OAAO,YAAY,QAAQ,QAAQ,CAAC,UAAU,SAAS;IAC3D;IACA,IAAI,eAAe,kBAAkB,MAAM,CAAC,SAAS,MAAM;QACvD,OAAO,SAAS,MAAM,CAAC;IAC3B;IACA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC3B,OAAO;IACX,OAAO;QACH,OAAO,YAAY,CAAC,EAAE;IAC1B;AACJ;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAC9B,IAAI,MAAM,SAAS,CAAC,GAAG,OAAO,MAAM,MAAM,QAAQ;QAC9C,OAAO,MAAM,SAAS,CAAC,OAAO,MAAM;IACxC,OAAO;QACH,OAAO;IACX;AACJ;AAEA,SAAS,cAAc,OAAO;IAC1B,OAAO,SAAS,OAAO;QACnB,OAAO,mBAAmB,SAAS,QAAQ,QAAQ,EAC9C,IAAI,CAAC,SAAS,OAAO;YAClB,OAAO,UAAU,QAAQ,WAAW,CAAC,WAAW,QAAQ,YAAY;QACxE;IACR;AACJ;AAEA,SAAS,oBAAoB,QAAQ,EAAE,OAAO,EAAE,IAAI;IAChD,IAAI,+BAA+B,cAAc;QAC7C,UAAU,sBAAsB;QAChC,aAAa,oBAAoB,iBAAiB;QAClD,cAAc,oBAAoB,YAAY;IAClD;IAEA,OAAO,6BAA6B,QAAQ,QAAQ,EAAE,IAAI,CAAC,SAAS,aAAa;QAC7E,IAAI,aAAa,IAAI,iBAAiB;YAClC,eAAe;YACf,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,WAAW,QAAQ,SAAS;YAC5B,QAAQ,QAAQ,MAAM;YACtB,OAAO,QAAQ,KAAK;QACxB;QACA,OAAO,mBAAmB,QAAQ,QAAQ,EAAE,UACvC,IAAI,CAAC,SAAS,GAAG;YACd,OAAO,KAAK,YAAY;QAC5B;IACR;AACJ;AAEA,SAAS,sBAAsB,QAAQ;IACnC,IAAI,QAAQ,QAAQ,SAAS,CAAC;IAC9B,OAAO,QAAQ,QAAQ,CAAC,MAAM,OAAO,EAAE,SAAS,MAAM,QAAQ,GAAG;AACrE;AAEA,IAAI,8BAA8B,cAAc;IAC5C,UAAU;IACV,aAAa,mBAAmB,uBAAuB;IACvD,cAAc,mBAAmB,mBAAmB;AACxD;AAEA,SAAS,yBAAyB,OAAO,EAAE,IAAI,EAAE,MAAM;IACnD,OAAO,cAAc;QACjB,UAAU;QACV,aAAa,SAAS,OAAO;YACzB,OAAO,aAAa,gBAAgB,CAAC,SAAS;gBAAC,QAAQ;YAAM;QACjE;QACA,cAAc,aAAa,gBAAgB;IAC/C,GAAG;AACP;AAEA,SAAS,sBAAsB,OAAO,EAAE,IAAI;IACxC,OAAO,cAAc;QACjB,UAAU;QACV,aAAa,aAAa,aAAa;QACvC,cAAc,aAAa,aAAa;IAC5C,GAAG;AACP;AAEA,IAAI,2BAA2B,cAAc;IACzC,UAAU;IACV,aAAa,oBAAoB,iBAAiB;IAClD,cAAc,oBAAoB,YAAY;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/docx/style-map.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar promises = require(\"../promises\");\nvar xml = require(\"../xml\");\n\nexports.writeStyleMap = writeStyleMap;\nexports.readStyleMap = readStyleMap;\n\n\nvar schema = \"http://schemas.zwobble.org/mammoth/style-map\";\nvar styleMapPath = \"mammoth/style-map\";\nvar styleMapAbsolutePath = \"/\" + styleMapPath;\n\nfunction writeStyleMap(docxFile, styleMap) {\n    docxFile.write(styleMapPath, styleMap);\n    return updateRelationships(docxFile).then(function() {\n        return updateContentTypes(docxFile);\n    });\n}\n\nfunction updateRelationships(docxFile) {\n    var path = \"word/_rels/document.xml.rels\";\n    var relationshipsUri = \"http://schemas.openxmlformats.org/package/2006/relationships\";\n    var relationshipElementName = \"{\" + relationshipsUri + \"}Relationship\";\n    return docxFile.read(path, \"utf8\")\n        .then(xml.readString)\n        .then(function(relationshipsContainer) {\n            var relationships = relationshipsContainer.children;\n            addOrUpdateElement(relationships, relationshipElementName, \"Id\", {\n                \"Id\": \"rMammothStyleMap\",\n                \"Type\": schema,\n                \"Target\": styleMapAbsolutePath\n            });\n            \n            var namespaces = {\"\": relationshipsUri};\n            return docxFile.write(path, xml.writeString(relationshipsContainer, namespaces));\n        });\n}\n\nfunction updateContentTypes(docxFile) {\n    var path = \"[Content_Types].xml\";\n    var contentTypesUri = \"http://schemas.openxmlformats.org/package/2006/content-types\";\n    var overrideName = \"{\" + contentTypesUri + \"}Override\";\n    return docxFile.read(path, \"utf8\")\n        .then(xml.readString)\n        .then(function(typesElement) {\n            var children = typesElement.children;\n            addOrUpdateElement(children, overrideName, \"PartName\", {\n                \"PartName\": styleMapAbsolutePath,\n                \"ContentType\": \"text/prs.mammoth.style-map\"\n            });\n            var namespaces = {\"\": contentTypesUri};\n            return docxFile.write(path, xml.writeString(typesElement, namespaces));\n        });\n}\n\nfunction addOrUpdateElement(elements, name, identifyingAttribute, attributes) {\n    var existingElement = _.find(elements, function(element) {\n        return element.name === name &&\n            element.attributes[identifyingAttribute] === attributes[identifyingAttribute];\n    });\n    if (existingElement) {\n        existingElement.attributes = attributes;\n    } else {\n        elements.push(xml.element(name, attributes));\n    }\n}\n\nfunction readStyleMap(docxFile) {\n    if (docxFile.exists(styleMapPath)) {\n        return docxFile.read(styleMapPath, \"utf8\");\n    } else {\n        return promises.resolve(null);\n    }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AACJ,IAAI;AAEJ,QAAQ,aAAa,GAAG;AACxB,QAAQ,YAAY,GAAG;AAGvB,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,uBAAuB,MAAM;AAEjC,SAAS,cAAc,QAAQ,EAAE,QAAQ;IACrC,SAAS,KAAK,CAAC,cAAc;IAC7B,OAAO,oBAAoB,UAAU,IAAI,CAAC;QACtC,OAAO,mBAAmB;IAC9B;AACJ;AAEA,SAAS,oBAAoB,QAAQ;IACjC,IAAI,OAAO;IACX,IAAI,mBAAmB;IACvB,IAAI,0BAA0B,MAAM,mBAAmB;IACvD,OAAO,SAAS,IAAI,CAAC,MAAM,QACtB,IAAI,CAAC,IAAI,UAAU,EACnB,IAAI,CAAC,SAAS,sBAAsB;QACjC,IAAI,gBAAgB,uBAAuB,QAAQ;QACnD,mBAAmB,eAAe,yBAAyB,MAAM;YAC7D,MAAM;YACN,QAAQ;YACR,UAAU;QACd;QAEA,IAAI,aAAa;YAAC,IAAI;QAAgB;QACtC,OAAO,SAAS,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,wBAAwB;IACxE;AACR;AAEA,SAAS,mBAAmB,QAAQ;IAChC,IAAI,OAAO;IACX,IAAI,kBAAkB;IACtB,IAAI,eAAe,MAAM,kBAAkB;IAC3C,OAAO,SAAS,IAAI,CAAC,MAAM,QACtB,IAAI,CAAC,IAAI,UAAU,EACnB,IAAI,CAAC,SAAS,YAAY;QACvB,IAAI,WAAW,aAAa,QAAQ;QACpC,mBAAmB,UAAU,cAAc,YAAY;YACnD,YAAY;YACZ,eAAe;QACnB;QACA,IAAI,aAAa;YAAC,IAAI;QAAe;QACrC,OAAO,SAAS,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,cAAc;IAC9D;AACR;AAEA,SAAS,mBAAmB,QAAQ,EAAE,IAAI,EAAE,oBAAoB,EAAE,UAAU;IACxE,IAAI,kBAAkB,EAAE,IAAI,CAAC,UAAU,SAAS,OAAO;QACnD,OAAO,QAAQ,IAAI,KAAK,QACpB,QAAQ,UAAU,CAAC,qBAAqB,KAAK,UAAU,CAAC,qBAAqB;IACrF;IACA,IAAI,iBAAiB;QACjB,gBAAgB,UAAU,GAAG;IACjC,OAAO;QACH,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM;IACpC;AACJ;AAEA,SAAS,aAAa,QAAQ;IAC1B,IAAI,SAAS,MAAM,CAAC,eAAe;QAC/B,OAAO,SAAS,IAAI,CAAC,cAAc;IACvC,OAAO;QACH,OAAO,SAAS,OAAO,CAAC;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/html/ast.js"], "sourcesContent": ["var htmlPaths = require(\"../styles/html-paths\");\n\n\nfunction nonFreshElement(tagName, attributes, children) {\n    return elementWithTag(\n        htmlPaths.element(tagName, attributes, {fresh: false}),\n        children);\n}\n\nfunction freshElement(tagName, attributes, children) {\n    var tag = htmlPaths.element(tagName, attributes, {fresh: true});\n    return elementWithTag(tag, children);\n}\n\nfunction elementWithTag(tag, children) {\n    return {\n        type: \"element\",\n        tag: tag,\n        children: children || []\n    };\n}\n\nfunction text(value) {\n    return {\n        type: \"text\",\n        value: value\n    };\n}\n\nvar forceWrite = {\n    type: \"forceWrite\"\n};\n\nexports.freshElement = freshElement;\nexports.nonFreshElement = nonFreshElement;\nexports.elementWithTag = elementWithTag;\nexports.text = text;\nexports.forceWrite = forceWrite;\n\nvar voidTagNames = {\n    \"br\": true,\n    \"hr\": true,\n    \"img\": true,\n    \"input\": true\n};\n\nfunction isVoidElement(node) {\n    return (node.children.length === 0) && voidTagNames[node.tag.tagName];\n}\n\nexports.isVoidElement = isVoidElement;\n"], "names": [], "mappings": "AAAA,IAAI;AAGJ,SAAS,gBAAgB,OAAO,EAAE,UAAU,EAAE,QAAQ;IAClD,OAAO,eACH,UAAU,OAAO,CAAC,SAAS,YAAY;QAAC,OAAO;IAAK,IACpD;AACR;AAEA,SAAS,aAAa,OAAO,EAAE,UAAU,EAAE,QAAQ;IAC/C,IAAI,MAAM,UAAU,OAAO,CAAC,SAAS,YAAY;QAAC,OAAO;IAAI;IAC7D,OAAO,eAAe,KAAK;AAC/B;AAEA,SAAS,eAAe,GAAG,EAAE,QAAQ;IACjC,OAAO;QACH,MAAM;QACN,KAAK;QACL,UAAU,YAAY,EAAE;IAC5B;AACJ;AAEA,SAAS,KAAK,KAAK;IACf,OAAO;QACH,MAAM;QACN,OAAO;IACX;AACJ;AAEA,IAAI,aAAa;IACb,MAAM;AACV;AAEA,QAAQ,YAAY,GAAG;AACvB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,cAAc,GAAG;AACzB,QAAQ,IAAI,GAAG;AACf,QAAQ,UAAU,GAAG;AAErB,IAAI,eAAe;IACf,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;AACb;AAEA,SAAS,cAAc,IAAI;IACvB,OAAO,AAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAM,YAAY,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC;AACzE;AAEA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/html/simplify.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar ast = require(\"./ast\");\n\nfunction simplify(nodes) {\n    return collapse(removeEmpty(nodes));\n}\n\nfunction collapse(nodes) {\n    var children = [];\n    \n    nodes.map(collapseNode).forEach(function(child) {\n        appendChild(children, child);\n    });\n    return children;\n}\n\nfunction collapseNode(node) {\n    return collapsers[node.type](node);\n}\n\nvar collapsers = {\n    element: collapseElement,\n    text: identity,\n    forceWrite: identity\n};\n\nfunction collapseElement(node) {\n    return ast.elementWithTag(node.tag, collapse(node.children));\n}\n\nfunction identity(value) {\n    return value;\n}\n\nfunction appendChild(children, child) {\n    var lastChild = children[children.length - 1];\n    if (child.type === \"element\" && !child.tag.fresh && lastChild && lastChild.type === \"element\" && child.tag.matchesElement(lastChild.tag)) {\n        if (child.tag.separator) {\n            appendChild(lastChild.children, ast.text(child.tag.separator));\n        }\n        child.children.forEach(function(grandChild) {\n            // Mutation is fine since simplifying elements create a copy of the children.\n            appendChild(lastChild.children, grandChild);\n        });\n    } else {\n        children.push(child);\n    }\n}\n\nfunction removeEmpty(nodes) {\n    return flatMap(nodes, function(node) {\n        return emptiers[node.type](node);\n    });\n}\n\nfunction flatMap(values, func) {\n    return _.flatten(_.map(values, func), true);\n}\n\nvar emptiers = {\n    element: elementEmptier,\n    text: textEmptier,\n    forceWrite: neverEmpty\n};\n\nfunction neverEmpty(node) {\n    return [node];\n}\n\nfunction elementEmptier(element) {\n    var children = removeEmpty(element.children);\n    if (children.length === 0 && !ast.isVoidElement(element)) {\n        return [];\n    } else {\n        return [ast.elementWithTag(element.tag, children)];\n    }\n}\n\nfunction textEmptier(node) {\n    if (node.value.length === 0) {\n        return [];\n    } else {\n        return [node];\n    }\n}\n\nmodule.exports = simplify;\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AAEJ,SAAS,SAAS,KAAK;IACnB,OAAO,SAAS,YAAY;AAChC;AAEA,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,EAAE;IAEjB,MAAM,GAAG,CAAC,cAAc,OAAO,CAAC,SAAS,KAAK;QAC1C,YAAY,UAAU;IAC1B;IACA,OAAO;AACX;AAEA,SAAS,aAAa,IAAI;IACtB,OAAO,UAAU,CAAC,KAAK,IAAI,CAAC,CAAC;AACjC;AAEA,IAAI,aAAa;IACb,SAAS;IACT,MAAM;IACN,YAAY;AAChB;AAEA,SAAS,gBAAgB,IAAI;IACzB,OAAO,IAAI,cAAc,CAAC,KAAK,GAAG,EAAE,SAAS,KAAK,QAAQ;AAC9D;AAEA,SAAS,SAAS,KAAK;IACnB,OAAO;AACX;AAEA,SAAS,YAAY,QAAQ,EAAE,KAAK;IAChC,IAAI,YAAY,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAC7C,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,aAAa,UAAU,IAAI,KAAK,aAAa,MAAM,GAAG,CAAC,cAAc,CAAC,UAAU,GAAG,GAAG;QACtI,IAAI,MAAM,GAAG,CAAC,SAAS,EAAE;YACrB,YAAY,UAAU,QAAQ,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,SAAS;QAChE;QACA,MAAM,QAAQ,CAAC,OAAO,CAAC,SAAS,UAAU;YACtC,6EAA6E;YAC7E,YAAY,UAAU,QAAQ,EAAE;QACpC;IACJ,OAAO;QACH,SAAS,IAAI,CAAC;IAClB;AACJ;AAEA,SAAS,YAAY,KAAK;IACtB,OAAO,QAAQ,OAAO,SAAS,IAAI;QAC/B,OAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;IAC/B;AACJ;AAEA,SAAS,QAAQ,MAAM,EAAE,IAAI;IACzB,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,QAAQ,OAAO;AAC1C;AAEA,IAAI,WAAW;IACX,SAAS;IACT,MAAM;IACN,YAAY;AAChB;AAEA,SAAS,WAAW,IAAI;IACpB,OAAO;QAAC;KAAK;AACjB;AAEA,SAAS,eAAe,OAAO;IAC3B,IAAI,WAAW,YAAY,QAAQ,QAAQ;IAC3C,IAAI,SAAS,MAAM,KAAK,KAAK,CAAC,IAAI,aAAa,CAAC,UAAU;QACtD,OAAO,EAAE;IACb,OAAO;QACH,OAAO;YAAC,IAAI,cAAc,CAAC,QAAQ,GAAG,EAAE;SAAU;IACtD;AACJ;AAEA,SAAS,YAAY,IAAI;IACrB,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;QACzB,OAAO,EAAE;IACb,OAAO;QACH,OAAO;YAAC;SAAK;IACjB;AACJ;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2134, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/html/index.js"], "sourcesContent": ["var ast = require(\"./ast\");\n\nexports.freshElement = ast.freshElement;\nexports.nonFreshElement = ast.nonFreshElement;\nexports.elementWithTag = ast.elementWithTag;\nexports.text = ast.text;\nexports.forceWrite = ast.forceWrite;\n\nexports.simplify = require(\"./simplify\");\n\nfunction write(writer, nodes) {\n    nodes.forEach(function(node) {\n        writeNode(writer, node);\n    });\n}\n\nfunction writeNode(writer, node) {\n    toStrings[node.type](writer, node);\n}\n\nvar toStrings = {\n    element: generateElementString,\n    text: generateTextString,\n    forceWrite: function() { }\n};\n\nfunction generateElementString(writer, node) {\n    if (ast.isVoidElement(node)) {\n        writer.selfClosing(node.tag.tagName, node.tag.attributes);\n    } else {\n        writer.open(node.tag.tagName, node.tag.attributes);\n        write(writer, node.children);\n        writer.close(node.tag.tagName);\n    }\n}\n\nfunction generateTextString(writer, node) {\n    writer.text(node.value);\n}\n\nexports.write = write;\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,YAAY,GAAG,IAAI,YAAY;AACvC,QAAQ,eAAe,GAAG,IAAI,eAAe;AAC7C,QAAQ,cAAc,GAAG,IAAI,cAAc;AAC3C,QAAQ,IAAI,GAAG,IAAI,IAAI;AACvB,QAAQ,UAAU,GAAG,IAAI,UAAU;AAEnC,QAAQ,QAAQ;AAEhB,SAAS,MAAM,MAAM,EAAE,KAAK;IACxB,MAAM,OAAO,CAAC,SAAS,IAAI;QACvB,UAAU,QAAQ;IACtB;AACJ;AAEA,SAAS,UAAU,MAAM,EAAE,IAAI;IAC3B,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC,QAAQ;AACjC;AAEA,IAAI,YAAY;IACZ,SAAS;IACT,MAAM;IACN,YAAY,YAAa;AAC7B;AAEA,SAAS,sBAAsB,MAAM,EAAE,IAAI;IACvC,IAAI,IAAI,aAAa,CAAC,OAAO;QACzB,OAAO,WAAW,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,UAAU;IAC5D,OAAO;QACH,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,UAAU;QACjD,MAAM,QAAQ,KAAK,QAAQ;QAC3B,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO;IACjC;AACJ;AAEA,SAAS,mBAAmB,MAAM,EAAE,IAAI;IACpC,OAAO,IAAI,CAAC,KAAK,KAAK;AAC1B;AAEA,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2172, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/styles/html-paths.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar html = require(\"../html\");\n\nexports.topLevelElement = topLevelElement;\nexports.elements = elements;\nexports.element = element;\n\nfunction topLevelElement(tagName, attributes) {\n    return elements([element(tagName, attributes, {fresh: true})]);\n}\n\nfunction elements(elementStyles) {\n    return new HtmlPath(elementStyles.map(function(elementStyle) {\n        if (_.isString(elementStyle)) {\n            return element(elementStyle);\n        } else {\n            return elementStyle;\n        }\n    }));\n}\n\nfunction HtmlPath(elements) {\n    this._elements = elements;\n}\n\nHtmlPath.prototype.wrap = function wrap(children) {\n    var result = children();\n    for (var index = this._elements.length - 1; index >= 0; index--) {\n        result = this._elements[index].wrapNodes(result);\n    }\n    return result;\n};\n\nfunction element(tagName, attributes, options) {\n    options = options || {};\n    return new Element(tagName, attributes, options);\n}\n\nfunction Element(tagName, attributes, options) {\n    var tagNames = {};\n    if (_.isArray(tagName)) {\n        tagName.forEach(function(tagName) {\n            tagNames[tagName] = true;\n        });\n        tagName = tagName[0];\n    } else {\n        tagNames[tagName] = true;\n    }\n    \n    this.tagName = tagName;\n    this.tagNames = tagNames;\n    this.attributes = attributes || {};\n    this.fresh = options.fresh;\n    this.separator = options.separator;\n}\n\nElement.prototype.matchesElement = function(element) {\n    return this.tagNames[element.tagName] && _.isEqual(this.attributes || {}, element.attributes || {});\n};\n\nElement.prototype.wrap = function wrap(generateNodes) {\n    return this.wrapNodes(generateNodes());\n};\n\nElement.prototype.wrapNodes = function wrapNodes(nodes) {\n    return [html.elementWithTag(this, nodes)];\n};\n\nexports.empty = elements([]);\nexports.ignore = {\n    wrap: function() {\n        return [];\n    }\n};\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AAEJ,QAAQ,eAAe,GAAG;AAC1B,QAAQ,QAAQ,GAAG;AACnB,QAAQ,OAAO,GAAG;AAElB,SAAS,gBAAgB,OAAO,EAAE,UAAU;IACxC,OAAO,SAAS;QAAC,QAAQ,SAAS,YAAY;YAAC,OAAO;QAAI;KAAG;AACjE;AAEA,SAAS,SAAS,aAAa;IAC3B,OAAO,IAAI,SAAS,cAAc,GAAG,CAAC,SAAS,YAAY;QACvD,IAAI,EAAE,QAAQ,CAAC,eAAe;YAC1B,OAAO,QAAQ;QACnB,OAAO;YACH,OAAO;QACX;IACJ;AACJ;AAEA,SAAS,SAAS,QAAQ;IACtB,IAAI,CAAC,SAAS,GAAG;AACrB;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,QAAQ;IAC5C,IAAI,SAAS;IACb,IAAK,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,SAAS,GAAG,QAAS;QAC7D,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;IAC7C;IACA,OAAO;AACX;AAEA,SAAS,QAAQ,OAAO,EAAE,UAAU,EAAE,OAAO;IACzC,UAAU,WAAW,CAAC;IACtB,OAAO,IAAI,QAAQ,SAAS,YAAY;AAC5C;AAEA,SAAS,QAAQ,OAAO,EAAE,UAAU,EAAE,OAAO;IACzC,IAAI,WAAW,CAAC;IAChB,IAAI,EAAE,OAAO,CAAC,UAAU;QACpB,QAAQ,OAAO,CAAC,SAAS,OAAO;YAC5B,QAAQ,CAAC,QAAQ,GAAG;QACxB;QACA,UAAU,OAAO,CAAC,EAAE;IACxB,OAAO;QACH,QAAQ,CAAC,QAAQ,GAAG;IACxB;IAEA,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;IACjC,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;IAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;AACtC;AAEA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAS,OAAO;IAC/C,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,QAAQ,UAAU,IAAI,CAAC;AACrG;AAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,aAAa;IAChD,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B;AAEA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,KAAK;IAClD,OAAO;QAAC,KAAK,cAAc,CAAC,IAAI,EAAE;KAAO;AAC7C;AAEA,QAAQ,KAAK,GAAG,SAAS,EAAE;AAC3B,QAAQ,MAAM,GAAG;IACb,MAAM;QACF,OAAO,EAAE;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/images.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar promises = require(\"./promises\");\nvar Html = require(\"./html\");\n\nexports.imgElement = imgElement;\n\nfunction imgElement(func) {\n    return function(element, messages) {\n        return promises.when(func(element)).then(function(result) {\n            var attributes = {};\n            if (element.altText) {\n                attributes.alt = element.altText;\n            }\n            _.extend(attributes, result);\n\n            return [Html.freshElement(\"img\", attributes)];\n        });\n    };\n}\n\n// Undocumented, but retained for backwards-compatibility with 0.3.x\nexports.inline = exports.imgElement;\n\nexports.dataUri = imgElement(function(element) {\n    return element.readAsBase64String().then(function(imageBuffer) {\n        return {\n            src: \"data:\" + element.contentType + \";base64,\" + imageBuffer\n        };\n    });\n});\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AACJ,IAAI;AAEJ,QAAQ,UAAU,GAAG;AAErB,SAAS,WAAW,IAAI;IACpB,OAAO,SAAS,OAAO,EAAE,QAAQ;QAC7B,OAAO,SAAS,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,SAAS,MAAM;YACpD,IAAI,aAAa,CAAC;YAClB,IAAI,QAAQ,OAAO,EAAE;gBACjB,WAAW,GAAG,GAAG,QAAQ,OAAO;YACpC;YACA,EAAE,MAAM,CAAC,YAAY;YAErB,OAAO;gBAAC,KAAK,YAAY,CAAC,OAAO;aAAY;QACjD;IACJ;AACJ;AAEA,oEAAoE;AACpE,QAAQ,MAAM,GAAG,QAAQ,UAAU;AAEnC,QAAQ,OAAO,GAAG,WAAW,SAAS,OAAO;IACzC,OAAO,QAAQ,kBAAkB,GAAG,IAAI,CAAC,SAAS,WAAW;QACzD,OAAO;YACH,KAAK,UAAU,QAAQ,WAAW,GAAG,aAAa;QACtD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/writers/html-writer.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nexports.writer = writer;\n\nfunction writer(options) {\n    options = options || {};\n    if (options.prettyPrint) {\n        return prettyWriter();\n    } else {\n        return simpleWriter();\n    }\n}\n\n\nvar indentedElements = {\n    div: true,\n    p: true,\n    ul: true,\n    li: true\n};\n\n\nfunction prettyWriter() {\n    var indentationLevel = 0;\n    var indentation = \"  \";\n    var stack = [];\n    var start = true;\n    var inText = false;\n\n    var writer = simpleWriter();\n\n    function open(tagName, attributes) {\n        if (indentedElements[tagName]) {\n            indent();\n        }\n        stack.push(tagName);\n        writer.open(tagName, attributes);\n        if (indentedElements[tagName]) {\n            indentationLevel++;\n        }\n        start = false;\n    }\n\n    function close(tagName) {\n        if (indentedElements[tagName]) {\n            indentationLevel--;\n            indent();\n        }\n        stack.pop();\n        writer.close(tagName);\n    }\n\n    function text(value) {\n        startText();\n        var text = isInPre() ? value : value.replace(\"\\n\", \"\\n\" + indentation);\n        writer.text(text);\n    }\n\n    function selfClosing(tagName, attributes) {\n        indent();\n        writer.selfClosing(tagName, attributes);\n    }\n\n    function insideIndentedElement() {\n        return stack.length === 0 || indentedElements[stack[stack.length - 1]];\n    }\n\n    function startText() {\n        if (!inText) {\n            indent();\n            inText = true;\n        }\n    }\n\n    function indent() {\n        inText = false;\n        if (!start && insideIndentedElement() && !isInPre()) {\n            writer._append(\"\\n\");\n            for (var i = 0; i < indentationLevel; i++) {\n                writer._append(indentation);\n            }\n        }\n    }\n\n    function isInPre() {\n        return _.some(stack, function(tagName) {\n            return tagName === \"pre\";\n        });\n    }\n\n    return {\n        asString: writer.asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing\n    };\n}\n\n\nfunction simpleWriter() {\n    var fragments = [];\n\n    function open(tagName, attributes) {\n        var attributeString = generateAttributeString(attributes);\n        fragments.push(\"<\" + tagName + attributeString + \">\");\n    }\n\n    function close(tagName) {\n        fragments.push(\"</\" + tagName + \">\");\n    }\n\n    function selfClosing(tagName, attributes) {\n        var attributeString = generateAttributeString(attributes);\n        fragments.push(\"<\" + tagName + attributeString + \" />\");\n    }\n\n    function generateAttributeString(attributes) {\n        return _.map(attributes, function(value, key) {\n            return \" \" + key + '=\"' + escapeHtmlAttribute(value) + '\"';\n        }).join(\"\");\n    }\n\n    function text(value) {\n        fragments.push(escapeHtmlText(value));\n    }\n\n    function append(html) {\n        fragments.push(html);\n    }\n\n    function asString() {\n        return fragments.join(\"\");\n    }\n\n    return {\n        asString: asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing,\n        _append: append\n    };\n}\n\nfunction escapeHtmlText(value) {\n    return value\n        .replace(/&/g, '&amp;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n\nfunction escapeHtmlAttribute(value) {\n    return value\n        .replace(/&/g, '&amp;')\n        .replace(/\"/g, '&quot;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,MAAM,GAAG;AAEjB,SAAS,OAAO,OAAO;IACnB,UAAU,WAAW,CAAC;IACtB,IAAI,QAAQ,WAAW,EAAE;QACrB,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAGA,IAAI,mBAAmB;IACnB,KAAK;IACL,GAAG;IACH,IAAI;IACJ,IAAI;AACR;AAGA,SAAS;IACL,IAAI,mBAAmB;IACvB,IAAI,cAAc;IAClB,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ;IACZ,IAAI,SAAS;IAEb,IAAI,SAAS;IAEb,SAAS,KAAK,OAAO,EAAE,UAAU;QAC7B,IAAI,gBAAgB,CAAC,QAAQ,EAAE;YAC3B;QACJ;QACA,MAAM,IAAI,CAAC;QACX,OAAO,IAAI,CAAC,SAAS;QACrB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;YAC3B;QACJ;QACA,QAAQ;IACZ;IAEA,SAAS,MAAM,OAAO;QAClB,IAAI,gBAAgB,CAAC,QAAQ,EAAE;YAC3B;YACA;QACJ;QACA,MAAM,GAAG;QACT,OAAO,KAAK,CAAC;IACjB;IAEA,SAAS,KAAK,KAAK;QACf;QACA,IAAI,OAAO,YAAY,QAAQ,MAAM,OAAO,CAAC,MAAM,OAAO;QAC1D,OAAO,IAAI,CAAC;IAChB;IAEA,SAAS,YAAY,OAAO,EAAE,UAAU;QACpC;QACA,OAAO,WAAW,CAAC,SAAS;IAChC;IAEA,SAAS;QACL,OAAO,MAAM,MAAM,KAAK,KAAK,gBAAgB,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;IAC1E;IAEA,SAAS;QACL,IAAI,CAAC,QAAQ;YACT;YACA,SAAS;QACb;IACJ;IAEA,SAAS;QACL,SAAS;QACT,IAAI,CAAC,SAAS,2BAA2B,CAAC,WAAW;YACjD,OAAO,OAAO,CAAC;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC,OAAO,OAAO,CAAC;YACnB;QACJ;IACJ;IAEA,SAAS;QACL,OAAO,EAAE,IAAI,CAAC,OAAO,SAAS,OAAO;YACjC,OAAO,YAAY;QACvB;IACJ;IAEA,OAAO;QACH,UAAU,OAAO,QAAQ;QACzB,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACjB;AACJ;AAGA,SAAS;IACL,IAAI,YAAY,EAAE;IAElB,SAAS,KAAK,OAAO,EAAE,UAAU;QAC7B,IAAI,kBAAkB,wBAAwB;QAC9C,UAAU,IAAI,CAAC,MAAM,UAAU,kBAAkB;IACrD;IAEA,SAAS,MAAM,OAAO;QAClB,UAAU,IAAI,CAAC,OAAO,UAAU;IACpC;IAEA,SAAS,YAAY,OAAO,EAAE,UAAU;QACpC,IAAI,kBAAkB,wBAAwB;QAC9C,UAAU,IAAI,CAAC,MAAM,UAAU,kBAAkB;IACrD;IAEA,SAAS,wBAAwB,UAAU;QACvC,OAAO,EAAE,GAAG,CAAC,YAAY,SAAS,KAAK,EAAE,GAAG;YACxC,OAAO,MAAM,MAAM,OAAO,oBAAoB,SAAS;QAC3D,GAAG,IAAI,CAAC;IACZ;IAEA,SAAS,KAAK,KAAK;QACf,UAAU,IAAI,CAAC,eAAe;IAClC;IAEA,SAAS,OAAO,IAAI;QAChB,UAAU,IAAI,CAAC;IACnB;IAEA,SAAS;QACL,OAAO,UAAU,IAAI,CAAC;IAC1B;IAEA,OAAO;QACH,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;QACb,SAAS;IACb;AACJ;AAEA,SAAS,eAAe,KAAK;IACzB,OAAO,MACF,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM;AACvB;AAEA,SAAS,oBAAoB,KAAK;IAC9B,OAAO,MACF,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/writers/markdown-writer.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\n\nfunction symmetricMarkdownElement(end) {\n    return markdownElement(end, end);\n}\n\nfunction markdownElement(start, end) {\n    return function() {\n        return {start: start, end: end};\n    };\n}\n\nfunction markdownLink(attributes) {\n    var href = attributes.href || \"\";\n    if (href) {\n        return {\n            start: \"[\",\n            end: \"](\" + href + \")\",\n            anchorPosition: \"before\"\n        };\n    } else {\n        return {};\n    }\n}\n\nfunction markdownImage(attributes) {\n    var src = attributes.src || \"\";\n    var altText = attributes.alt || \"\";\n    if (src || altText) {\n        return {start: \"![\" + altText + \"](\" + src + \")\"};\n    } else {\n        return {};\n    }\n}\n\nfunction markdownList(options) {\n    return function(attributes, list) {\n        return {\n            start: list ? \"\\n\" : \"\",\n            end: list ? \"\" : \"\\n\",\n            list: {\n                isOrdered: options.isOrdered,\n                indent: list ? list.indent + 1 : 0,\n                count: 0\n            }\n        };\n    };\n}\n\nfunction markdownListItem(attributes, list, listItem) {\n    list = list || {indent: 0, isOrdered: false, count: 0};\n    list.count++;\n    listItem.hasClosed = false;\n    \n    var bullet = list.isOrdered ? list.count + \".\" : \"-\";\n    var start = repeatString(\"\\t\", list.indent) + bullet + \" \";\n        \n    return {\n        start: start,\n        end: function() {\n            if (!listItem.hasClosed) {\n                listItem.hasClosed = true;\n                return \"\\n\";\n            }\n        }\n    };\n}\n\nvar htmlToMarkdown = {\n    \"p\": markdownElement(\"\", \"\\n\\n\"),\n    \"br\": markdownElement(\"\", \"  \\n\"),\n    \"ul\": markdownList({isOrdered: false}),\n    \"ol\": markdownList({isOrdered: true}),\n    \"li\": markdownListItem,\n    \"strong\": symmetricMarkdownElement(\"__\"),\n    \"em\": symmetricMarkdownElement(\"*\"),\n    \"a\": markdownLink,\n    \"img\": markdownImage\n};\n\n(function() {\n    for (var i = 1; i <= 6; i++) {\n        htmlToMarkdown[\"h\" + i] = markdownElement(repeatString(\"#\", i) + \" \", \"\\n\\n\");\n    }\n})();\n\nfunction repeatString(value, count) {\n    return new Array(count + 1).join(value);\n}\n\nfunction markdownWriter() {\n    var fragments = [];\n    var elementStack = [];\n    var list = null;\n    var listItem = {};\n    \n    function open(tagName, attributes) {\n        attributes = attributes || {};\n        \n        var createElement = htmlToMarkdown[tagName] || function() {\n            return {};\n        };\n        var element = createElement(attributes, list, listItem);\n        elementStack.push({end: element.end, list: list});\n        \n        if (element.list) {\n            list = element.list;\n        }\n        \n        var anchorBeforeStart = element.anchorPosition === \"before\";\n        if (anchorBeforeStart) {\n            writeAnchor(attributes);\n        }\n\n        fragments.push(element.start || \"\");\n        if (!anchorBeforeStart) {\n            writeAnchor(attributes);\n        }\n    }\n    \n    function writeAnchor(attributes) {\n        if (attributes.id) {\n            fragments.push('<a id=\"' + attributes.id + '\"></a>');\n        }\n    }\n    \n    function close(tagName) {\n        var element = elementStack.pop();\n        list = element.list;\n        var end = _.isFunction(element.end) ? element.end() : element.end;\n        fragments.push(end || \"\");\n    }\n    \n    function selfClosing(tagName, attributes) {\n        open(tagName, attributes);\n        close(tagName);\n    }\n    \n    function text(value) {\n        fragments.push(escapeMarkdown(value));\n    }\n    \n    function asString() {\n        return fragments.join(\"\");\n    }\n\n    return {\n        asString: asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing\n    };\n}\n\nexports.writer = markdownWriter;\n\nfunction escapeMarkdown(value) {\n    return value\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/([\\`\\*_\\{\\}\\[\\]\\(\\)\\#\\+\\-\\.\\!])/g, '\\\\$1');\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAGJ,SAAS,yBAAyB,GAAG;IACjC,OAAO,gBAAgB,KAAK;AAChC;AAEA,SAAS,gBAAgB,KAAK,EAAE,GAAG;IAC/B,OAAO;QACH,OAAO;YAAC,OAAO;YAAO,KAAK;QAAG;IAClC;AACJ;AAEA,SAAS,aAAa,UAAU;IAC5B,IAAI,OAAO,WAAW,IAAI,IAAI;IAC9B,IAAI,MAAM;QACN,OAAO;YACH,OAAO;YACP,KAAK,OAAO,OAAO;YACnB,gBAAgB;QACpB;IACJ,OAAO;QACH,OAAO,CAAC;IACZ;AACJ;AAEA,SAAS,cAAc,UAAU;IAC7B,IAAI,MAAM,WAAW,GAAG,IAAI;IAC5B,IAAI,UAAU,WAAW,GAAG,IAAI;IAChC,IAAI,OAAO,SAAS;QAChB,OAAO;YAAC,OAAO,OAAO,UAAU,OAAO,MAAM;QAAG;IACpD,OAAO;QACH,OAAO,CAAC;IACZ;AACJ;AAEA,SAAS,aAAa,OAAO;IACzB,OAAO,SAAS,UAAU,EAAE,IAAI;QAC5B,OAAO;YACH,OAAO,OAAO,OAAO;YACrB,KAAK,OAAO,KAAK;YACjB,MAAM;gBACF,WAAW,QAAQ,SAAS;gBAC5B,QAAQ,OAAO,KAAK,MAAM,GAAG,IAAI;gBACjC,OAAO;YACX;QACJ;IACJ;AACJ;AAEA,SAAS,iBAAiB,UAAU,EAAE,IAAI,EAAE,QAAQ;IAChD,OAAO,QAAQ;QAAC,QAAQ;QAAG,WAAW;QAAO,OAAO;IAAC;IACrD,KAAK,KAAK;IACV,SAAS,SAAS,GAAG;IAErB,IAAI,SAAS,KAAK,SAAS,GAAG,KAAK,KAAK,GAAG,MAAM;IACjD,IAAI,QAAQ,aAAa,MAAM,KAAK,MAAM,IAAI,SAAS;IAEvD,OAAO;QACH,OAAO;QACP,KAAK;YACD,IAAI,CAAC,SAAS,SAAS,EAAE;gBACrB,SAAS,SAAS,GAAG;gBACrB,OAAO;YACX;QACJ;IACJ;AACJ;AAEA,IAAI,iBAAiB;IACjB,KAAK,gBAAgB,IAAI;IACzB,MAAM,gBAAgB,IAAI;IAC1B,MAAM,aAAa;QAAC,WAAW;IAAK;IACpC,MAAM,aAAa;QAAC,WAAW;IAAI;IACnC,MAAM;IACN,UAAU,yBAAyB;IACnC,MAAM,yBAAyB;IAC/B,KAAK;IACL,OAAO;AACX;AAEA,CAAC;IACG,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;QACzB,cAAc,CAAC,MAAM,EAAE,GAAG,gBAAgB,aAAa,KAAK,KAAK,KAAK;IAC1E;AACJ,CAAC;AAED,SAAS,aAAa,KAAK,EAAE,KAAK;IAC9B,OAAO,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC;AACrC;AAEA,SAAS;IACL,IAAI,YAAY,EAAE;IAClB,IAAI,eAAe,EAAE;IACrB,IAAI,OAAO;IACX,IAAI,WAAW,CAAC;IAEhB,SAAS,KAAK,OAAO,EAAE,UAAU;QAC7B,aAAa,cAAc,CAAC;QAE5B,IAAI,gBAAgB,cAAc,CAAC,QAAQ,IAAI;YAC3C,OAAO,CAAC;QACZ;QACA,IAAI,UAAU,cAAc,YAAY,MAAM;QAC9C,aAAa,IAAI,CAAC;YAAC,KAAK,QAAQ,GAAG;YAAE,MAAM;QAAI;QAE/C,IAAI,QAAQ,IAAI,EAAE;YACd,OAAO,QAAQ,IAAI;QACvB;QAEA,IAAI,oBAAoB,QAAQ,cAAc,KAAK;QACnD,IAAI,mBAAmB;YACnB,YAAY;QAChB;QAEA,UAAU,IAAI,CAAC,QAAQ,KAAK,IAAI;QAChC,IAAI,CAAC,mBAAmB;YACpB,YAAY;QAChB;IACJ;IAEA,SAAS,YAAY,UAAU;QAC3B,IAAI,WAAW,EAAE,EAAE;YACf,UAAU,IAAI,CAAC,YAAY,WAAW,EAAE,GAAG;QAC/C;IACJ;IAEA,SAAS,MAAM,OAAO;QAClB,IAAI,UAAU,aAAa,GAAG;QAC9B,OAAO,QAAQ,IAAI;QACnB,IAAI,MAAM,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,QAAQ,GAAG,KAAK,QAAQ,GAAG;QACjE,UAAU,IAAI,CAAC,OAAO;IAC1B;IAEA,SAAS,YAAY,OAAO,EAAE,UAAU;QACpC,KAAK,SAAS;QACd,MAAM;IACV;IAEA,SAAS,KAAK,KAAK;QACf,UAAU,IAAI,CAAC,eAAe;IAClC;IAEA,SAAS;QACL,OAAO,UAAU,IAAI,CAAC;IAC1B;IAEA,OAAO;QACH,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACjB;AACJ;AAEA,QAAQ,MAAM,GAAG;AAEjB,SAAS,eAAe,KAAK;IACzB,OAAO,MACF,OAAO,CAAC,OAAO,QACf,OAAO,CAAC,oCAAoC;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2562, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/writers/index.js"], "sourcesContent": ["var htmlWriter = require(\"./html-writer\");\nvar markdownWriter = require(\"./markdown-writer\");\n\nexports.writer = writer;\n\n\nfunction writer(options) {\n    options = options || {};\n    if (options.outputFormat === \"markdown\") {\n        return markdownWriter.writer();\n    } else {\n        return htmlWriter.writer(options);\n    }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,QAAQ,MAAM,GAAG;AAGjB,SAAS,OAAO,OAAO;IACnB,UAAU,WAAW,CAAC;IACtB,IAAI,QAAQ,YAAY,KAAK,YAAY;QACrC,OAAO,eAAe,MAAM;IAChC,OAAO;QACH,OAAO,WAAW,MAAM,CAAC;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2578, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/document-to-html.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar promises = require(\"./promises\");\nvar documents = require(\"./documents\");\nvar htmlPaths = require(\"./styles/html-paths\");\nvar results = require(\"./results\");\nvar images = require(\"./images\");\nvar Html = require(\"./html\");\nvar writers = require(\"./writers\");\n\nexports.DocumentConverter = DocumentConverter;\n\n\nfunction DocumentConverter(options) {\n    return {\n        convertToHtml: function(element) {\n            var comments = _.indexBy(\n                element.type === documents.types.document ? element.comments : [],\n                \"commentId\"\n            );\n            var conversion = new DocumentConversion(options, comments);\n            return conversion.convertToHtml(element);\n        }\n    };\n}\n\nfunction DocumentConversion(options, comments) {\n    var noteNumber = 1;\n\n    var noteReferences = [];\n\n    var referencedComments = [];\n\n    options = _.extend({ignoreEmptyParagraphs: true}, options);\n    var idPrefix = options.idPrefix === undefined ? \"\" : options.idPrefix;\n    var ignoreEmptyParagraphs = options.ignoreEmptyParagraphs;\n\n    var defaultParagraphStyle = htmlPaths.topLevelElement(\"p\");\n\n    var styleMap = options.styleMap || [];\n\n    function convertToHtml(document) {\n        var messages = [];\n\n        var html = elementToHtml(document, messages, {});\n\n        var deferredNodes = [];\n        walkHtml(html, function(node) {\n            if (node.type === \"deferred\") {\n                deferredNodes.push(node);\n            }\n        });\n        var deferredValues = {};\n        return promises.mapSeries(deferredNodes, function(deferred) {\n            return deferred.value().then(function(value) {\n                deferredValues[deferred.id] = value;\n            });\n        }).then(function() {\n            function replaceDeferred(nodes) {\n                return flatMap(nodes, function(node) {\n                    if (node.type === \"deferred\") {\n                        return deferredValues[node.id];\n                    } else if (node.children) {\n                        return [\n                            _.extend({}, node, {\n                                children: replaceDeferred(node.children)\n                            })\n                        ];\n                    } else {\n                        return [node];\n                    }\n                });\n            }\n            var writer = writers.writer({\n                prettyPrint: options.prettyPrint,\n                outputFormat: options.outputFormat\n            });\n            Html.write(writer, Html.simplify(replaceDeferred(html)));\n            return new results.Result(writer.asString(), messages);\n        });\n    }\n\n    function convertElements(elements, messages, options) {\n        return flatMap(elements, function(element) {\n            return elementToHtml(element, messages, options);\n        });\n    }\n\n    function elementToHtml(element, messages, options) {\n        if (!options) {\n            throw new Error(\"options not set\");\n        }\n        var handler = elementConverters[element.type];\n        if (handler) {\n            return handler(element, messages, options);\n        } else {\n            return [];\n        }\n    }\n\n    function convertParagraph(element, messages, options) {\n        return htmlPathForParagraph(element, messages).wrap(function() {\n            var content = convertElements(element.children, messages, options);\n            if (ignoreEmptyParagraphs) {\n                return content;\n            } else {\n                return [Html.forceWrite].concat(content);\n            }\n        });\n    }\n\n    function htmlPathForParagraph(element, messages) {\n        var style = findStyle(element);\n\n        if (style) {\n            return style.to;\n        } else {\n            if (element.styleId) {\n                messages.push(unrecognisedStyleWarning(\"paragraph\", element));\n            }\n            return defaultParagraphStyle;\n        }\n    }\n\n    function convertRun(run, messages, options) {\n        var nodes = function() {\n            return convertElements(run.children, messages, options);\n        };\n        var paths = [];\n        if (run.highlight !== null) {\n            var path = findHtmlPath({type: \"highlight\", color: run.highlight});\n            if (path) {\n                paths.push(path);\n            }\n        }\n        if (run.isSmallCaps) {\n            paths.push(findHtmlPathForRunProperty(\"smallCaps\"));\n        }\n        if (run.isAllCaps) {\n            paths.push(findHtmlPathForRunProperty(\"allCaps\"));\n        }\n        if (run.isStrikethrough) {\n            paths.push(findHtmlPathForRunProperty(\"strikethrough\", \"s\"));\n        }\n        if (run.isUnderline) {\n            paths.push(findHtmlPathForRunProperty(\"underline\"));\n        }\n        if (run.verticalAlignment === documents.verticalAlignment.subscript) {\n            paths.push(htmlPaths.element(\"sub\", {}, {fresh: false}));\n        }\n        if (run.verticalAlignment === documents.verticalAlignment.superscript) {\n            paths.push(htmlPaths.element(\"sup\", {}, {fresh: false}));\n        }\n        if (run.isItalic) {\n            paths.push(findHtmlPathForRunProperty(\"italic\", \"em\"));\n        }\n        if (run.isBold) {\n            paths.push(findHtmlPathForRunProperty(\"bold\", \"strong\"));\n        }\n        var stylePath = htmlPaths.empty;\n        var style = findStyle(run);\n        if (style) {\n            stylePath = style.to;\n        } else if (run.styleId) {\n            messages.push(unrecognisedStyleWarning(\"run\", run));\n        }\n        paths.push(stylePath);\n\n        paths.forEach(function(path) {\n            nodes = path.wrap.bind(path, nodes);\n        });\n\n        return nodes();\n    }\n\n    function findHtmlPathForRunProperty(elementType, defaultTagName) {\n        var path = findHtmlPath({type: elementType});\n        if (path) {\n            return path;\n        } else if (defaultTagName) {\n            return htmlPaths.element(defaultTagName, {}, {fresh: false});\n        } else {\n            return htmlPaths.empty;\n        }\n    }\n\n    function findHtmlPath(element, defaultPath) {\n        var style = findStyle(element);\n        return style ? style.to : defaultPath;\n    }\n\n    function findStyle(element) {\n        for (var i = 0; i < styleMap.length; i++) {\n            if (styleMap[i].from.matches(element)) {\n                return styleMap[i];\n            }\n        }\n    }\n\n    function recoveringConvertImage(convertImage) {\n        return function(image, messages) {\n            return promises.attempt(function() {\n                return convertImage(image, messages);\n            }).caught(function(error) {\n                messages.push(results.error(error));\n                return [];\n            });\n        };\n    }\n\n    function noteHtmlId(note) {\n        return referentHtmlId(note.noteType, note.noteId);\n    }\n\n    function noteRefHtmlId(note) {\n        return referenceHtmlId(note.noteType, note.noteId);\n    }\n\n    function referentHtmlId(referenceType, referenceId) {\n        return htmlId(referenceType + \"-\" + referenceId);\n    }\n\n    function referenceHtmlId(referenceType, referenceId) {\n        return htmlId(referenceType + \"-ref-\" + referenceId);\n    }\n\n    function htmlId(suffix) {\n        return idPrefix + suffix;\n    }\n\n    var defaultTablePath = htmlPaths.elements([\n        htmlPaths.element(\"table\", {}, {fresh: true})\n    ]);\n\n    function convertTable(element, messages, options) {\n        return findHtmlPath(element, defaultTablePath).wrap(function() {\n            return convertTableChildren(element, messages, options);\n        });\n    }\n\n    function convertTableChildren(element, messages, options) {\n        var bodyIndex = _.findIndex(element.children, function(child) {\n            return !child.type === documents.types.tableRow || !child.isHeader;\n        });\n        if (bodyIndex === -1) {\n            bodyIndex = element.children.length;\n        }\n        var children;\n        if (bodyIndex === 0) {\n            children = convertElements(\n                element.children,\n                messages,\n                _.extend({}, options, {isTableHeader: false})\n            );\n        } else {\n            var headRows = convertElements(\n                element.children.slice(0, bodyIndex),\n                messages,\n                _.extend({}, options, {isTableHeader: true})\n            );\n            var bodyRows = convertElements(\n                element.children.slice(bodyIndex),\n                messages,\n                _.extend({}, options, {isTableHeader: false})\n            );\n            children = [\n                Html.freshElement(\"thead\", {}, headRows),\n                Html.freshElement(\"tbody\", {}, bodyRows)\n            ];\n        }\n        return [Html.forceWrite].concat(children);\n    }\n\n    function convertTableRow(element, messages, options) {\n        var children = convertElements(element.children, messages, options);\n        return [\n            Html.freshElement(\"tr\", {}, [Html.forceWrite].concat(children))\n        ];\n    }\n\n    function convertTableCell(element, messages, options) {\n        var tagName = options.isTableHeader ? \"th\" : \"td\";\n        var children = convertElements(element.children, messages, options);\n        var attributes = {};\n        if (element.colSpan !== 1) {\n            attributes.colspan = element.colSpan.toString();\n        }\n        if (element.rowSpan !== 1) {\n            attributes.rowspan = element.rowSpan.toString();\n        }\n\n        return [\n            Html.freshElement(tagName, attributes, [Html.forceWrite].concat(children))\n        ];\n    }\n\n    function convertCommentReference(reference, messages, options) {\n        return findHtmlPath(reference, htmlPaths.ignore).wrap(function() {\n            var comment = comments[reference.commentId];\n            var count = referencedComments.length + 1;\n            var label = \"[\" + commentAuthorLabel(comment) + count + \"]\";\n            referencedComments.push({label: label, comment: comment});\n            // TODO: remove duplication with note references\n            return [\n                Html.freshElement(\"a\", {\n                    href: \"#\" + referentHtmlId(\"comment\", reference.commentId),\n                    id: referenceHtmlId(\"comment\", reference.commentId)\n                }, [Html.text(label)])\n            ];\n        });\n    }\n\n    function convertComment(referencedComment, messages, options) {\n        // TODO: remove duplication with note references\n\n        var label = referencedComment.label;\n        var comment = referencedComment.comment;\n        var body = convertElements(comment.body, messages, options).concat([\n            Html.nonFreshElement(\"p\", {}, [\n                Html.text(\" \"),\n                Html.freshElement(\"a\", {\"href\": \"#\" + referenceHtmlId(\"comment\", comment.commentId)}, [\n                    Html.text(\"↑\")\n                ])\n            ])\n        ]);\n\n        return [\n            Html.freshElement(\n                \"dt\",\n                {\"id\": referentHtmlId(\"comment\", comment.commentId)},\n                [Html.text(\"Comment \" + label)]\n            ),\n            Html.freshElement(\"dd\", {}, body)\n        ];\n    }\n\n    function convertBreak(element, messages, options) {\n        return htmlPathForBreak(element).wrap(function() {\n            return [];\n        });\n    }\n\n    function htmlPathForBreak(element) {\n        var style = findStyle(element);\n        if (style) {\n            return style.to;\n        } else if (element.breakType === \"line\") {\n            return htmlPaths.topLevelElement(\"br\");\n        } else {\n            return htmlPaths.empty;\n        }\n    }\n\n    var elementConverters = {\n        \"document\": function(document, messages, options) {\n            var children = convertElements(document.children, messages, options);\n            var notes = noteReferences.map(function(noteReference) {\n                return document.notes.resolve(noteReference);\n            });\n            var notesNodes = convertElements(notes, messages, options);\n            return children.concat([\n                Html.freshElement(\"ol\", {}, notesNodes),\n                Html.freshElement(\"dl\", {}, flatMap(referencedComments, function(referencedComment) {\n                    return convertComment(referencedComment, messages, options);\n                }))\n            ]);\n        },\n        \"paragraph\": convertParagraph,\n        \"run\": convertRun,\n        \"text\": function(element, messages, options) {\n            return [Html.text(element.value)];\n        },\n        \"tab\": function(element, messages, options) {\n            return [Html.text(\"\\t\")];\n        },\n        \"hyperlink\": function(element, messages, options) {\n            var href = element.anchor ? \"#\" + htmlId(element.anchor) : element.href;\n            var attributes = {href: href};\n            if (element.targetFrame != null) {\n                attributes.target = element.targetFrame;\n            }\n\n            var children = convertElements(element.children, messages, options);\n            return [Html.nonFreshElement(\"a\", attributes, children)];\n        },\n        \"checkbox\": function(element) {\n            var attributes = {type: \"checkbox\"};\n            if (element.checked) {\n                attributes[\"checked\"] = \"checked\";\n            }\n            return [Html.freshElement(\"input\", attributes)];\n        },\n        \"bookmarkStart\": function(element, messages, options) {\n            var anchor = Html.freshElement(\"a\", {\n                id: htmlId(element.name)\n            }, [Html.forceWrite]);\n            return [anchor];\n        },\n        \"noteReference\": function(element, messages, options) {\n            noteReferences.push(element);\n            var anchor = Html.freshElement(\"a\", {\n                href: \"#\" + noteHtmlId(element),\n                id: noteRefHtmlId(element)\n            }, [Html.text(\"[\" + (noteNumber++) + \"]\")]);\n\n            return [Html.freshElement(\"sup\", {}, [anchor])];\n        },\n        \"note\": function(element, messages, options) {\n            var children = convertElements(element.body, messages, options);\n            var backLink = Html.elementWithTag(htmlPaths.element(\"p\", {}, {fresh: false}), [\n                Html.text(\" \"),\n                Html.freshElement(\"a\", {href: \"#\" + noteRefHtmlId(element)}, [Html.text(\"↑\")])\n            ]);\n            var body = children.concat([backLink]);\n\n            return Html.freshElement(\"li\", {id: noteHtmlId(element)}, body);\n        },\n        \"commentReference\": convertCommentReference,\n        \"comment\": convertComment,\n        \"image\": deferredConversion(recoveringConvertImage(options.convertImage || images.dataUri)),\n        \"table\": convertTable,\n        \"tableRow\": convertTableRow,\n        \"tableCell\": convertTableCell,\n        \"break\": convertBreak\n    };\n    return {\n        convertToHtml: convertToHtml\n    };\n}\n\nvar deferredId = 1;\n\nfunction deferredConversion(func) {\n    return function(element, messages, options) {\n        return [\n            {\n                type: \"deferred\",\n                id: deferredId++,\n                value: function() {\n                    return func(element, messages, options);\n                }\n            }\n        ];\n    };\n}\n\nfunction unrecognisedStyleWarning(type, element) {\n    return results.warning(\n        \"Unrecognised \" + type + \" style: '\" + element.styleName + \"'\" +\n        \" (Style ID: \" + element.styleId + \")\"\n    );\n}\n\nfunction flatMap(values, func) {\n    return _.flatten(values.map(func), true);\n}\n\nfunction walkHtml(nodes, callback) {\n    nodes.forEach(function(node) {\n        callback(node);\n        if (node.children) {\n            walkHtml(node.children, callback);\n        }\n    });\n}\n\nvar commentAuthorLabel = exports.commentAuthorLabel = function commentAuthorLabel(comment) {\n    return comment.authorInitials || \"\";\n};\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,QAAQ,iBAAiB,GAAG;AAG5B,SAAS,kBAAkB,OAAO;IAC9B,OAAO;QACH,eAAe,SAAS,OAAO;YAC3B,IAAI,WAAW,EAAE,OAAO,CACpB,QAAQ,IAAI,KAAK,UAAU,KAAK,CAAC,QAAQ,GAAG,QAAQ,QAAQ,GAAG,EAAE,EACjE;YAEJ,IAAI,aAAa,IAAI,mBAAmB,SAAS;YACjD,OAAO,WAAW,aAAa,CAAC;QACpC;IACJ;AACJ;AAEA,SAAS,mBAAmB,OAAO,EAAE,QAAQ;IACzC,IAAI,aAAa;IAEjB,IAAI,iBAAiB,EAAE;IAEvB,IAAI,qBAAqB,EAAE;IAE3B,UAAU,EAAE,MAAM,CAAC;QAAC,uBAAuB;IAAI,GAAG;IAClD,IAAI,WAAW,QAAQ,QAAQ,KAAK,YAAY,KAAK,QAAQ,QAAQ;IACrE,IAAI,wBAAwB,QAAQ,qBAAqB;IAEzD,IAAI,wBAAwB,UAAU,eAAe,CAAC;IAEtD,IAAI,WAAW,QAAQ,QAAQ,IAAI,EAAE;IAErC,SAAS,cAAc,QAAQ;QAC3B,IAAI,WAAW,EAAE;QAEjB,IAAI,OAAO,cAAc,UAAU,UAAU,CAAC;QAE9C,IAAI,gBAAgB,EAAE;QACtB,SAAS,MAAM,SAAS,IAAI;YACxB,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC1B,cAAc,IAAI,CAAC;YACvB;QACJ;QACA,IAAI,iBAAiB,CAAC;QACtB,OAAO,SAAS,SAAS,CAAC,eAAe,SAAS,QAAQ;YACtD,OAAO,SAAS,KAAK,GAAG,IAAI,CAAC,SAAS,KAAK;gBACvC,cAAc,CAAC,SAAS,EAAE,CAAC,GAAG;YAClC;QACJ,GAAG,IAAI,CAAC;YACJ,SAAS,gBAAgB,KAAK;gBAC1B,OAAO,QAAQ,OAAO,SAAS,IAAI;oBAC/B,IAAI,KAAK,IAAI,KAAK,YAAY;wBAC1B,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC;oBAClC,OAAO,IAAI,KAAK,QAAQ,EAAE;wBACtB,OAAO;4BACH,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM;gCACf,UAAU,gBAAgB,KAAK,QAAQ;4BAC3C;yBACH;oBACL,OAAO;wBACH,OAAO;4BAAC;yBAAK;oBACjB;gBACJ;YACJ;YACA,IAAI,SAAS,QAAQ,MAAM,CAAC;gBACxB,aAAa,QAAQ,WAAW;gBAChC,cAAc,QAAQ,YAAY;YACtC;YACA,KAAK,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,gBAAgB;YACjD,OAAO,IAAI,QAAQ,MAAM,CAAC,OAAO,QAAQ,IAAI;QACjD;IACJ;IAEA,SAAS,gBAAgB,QAAQ,EAAE,QAAQ,EAAE,OAAO;QAChD,OAAO,QAAQ,UAAU,SAAS,OAAO;YACrC,OAAO,cAAc,SAAS,UAAU;QAC5C;IACJ;IAEA,SAAS,cAAc,OAAO,EAAE,QAAQ,EAAE,OAAO;QAC7C,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,UAAU,iBAAiB,CAAC,QAAQ,IAAI,CAAC;QAC7C,IAAI,SAAS;YACT,OAAO,QAAQ,SAAS,UAAU;QACtC,OAAO;YACH,OAAO,EAAE;QACb;IACJ;IAEA,SAAS,iBAAiB,OAAO,EAAE,QAAQ,EAAE,OAAO;QAChD,OAAO,qBAAqB,SAAS,UAAU,IAAI,CAAC;YAChD,IAAI,UAAU,gBAAgB,QAAQ,QAAQ,EAAE,UAAU;YAC1D,IAAI,uBAAuB;gBACvB,OAAO;YACX,OAAO;gBACH,OAAO;oBAAC,KAAK,UAAU;iBAAC,CAAC,MAAM,CAAC;YACpC;QACJ;IACJ;IAEA,SAAS,qBAAqB,OAAO,EAAE,QAAQ;QAC3C,IAAI,QAAQ,UAAU;QAEtB,IAAI,OAAO;YACP,OAAO,MAAM,EAAE;QACnB,OAAO;YACH,IAAI,QAAQ,OAAO,EAAE;gBACjB,SAAS,IAAI,CAAC,yBAAyB,aAAa;YACxD;YACA,OAAO;QACX;IACJ;IAEA,SAAS,WAAW,GAAG,EAAE,QAAQ,EAAE,OAAO;QACtC,IAAI,QAAQ;YACR,OAAO,gBAAgB,IAAI,QAAQ,EAAE,UAAU;QACnD;QACA,IAAI,QAAQ,EAAE;QACd,IAAI,IAAI,SAAS,KAAK,MAAM;YACxB,IAAI,OAAO,aAAa;gBAAC,MAAM;gBAAa,OAAO,IAAI,SAAS;YAAA;YAChE,IAAI,MAAM;gBACN,MAAM,IAAI,CAAC;YACf;QACJ;QACA,IAAI,IAAI,WAAW,EAAE;YACjB,MAAM,IAAI,CAAC,2BAA2B;QAC1C;QACA,IAAI,IAAI,SAAS,EAAE;YACf,MAAM,IAAI,CAAC,2BAA2B;QAC1C;QACA,IAAI,IAAI,eAAe,EAAE;YACrB,MAAM,IAAI,CAAC,2BAA2B,iBAAiB;QAC3D;QACA,IAAI,IAAI,WAAW,EAAE;YACjB,MAAM,IAAI,CAAC,2BAA2B;QAC1C;QACA,IAAI,IAAI,iBAAiB,KAAK,UAAU,iBAAiB,CAAC,SAAS,EAAE;YACjE,MAAM,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,CAAC,GAAG;gBAAC,OAAO;YAAK;QACzD;QACA,IAAI,IAAI,iBAAiB,KAAK,UAAU,iBAAiB,CAAC,WAAW,EAAE;YACnE,MAAM,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,CAAC,GAAG;gBAAC,OAAO;YAAK;QACzD;QACA,IAAI,IAAI,QAAQ,EAAE;YACd,MAAM,IAAI,CAAC,2BAA2B,UAAU;QACpD;QACA,IAAI,IAAI,MAAM,EAAE;YACZ,MAAM,IAAI,CAAC,2BAA2B,QAAQ;QAClD;QACA,IAAI,YAAY,UAAU,KAAK;QAC/B,IAAI,QAAQ,UAAU;QACtB,IAAI,OAAO;YACP,YAAY,MAAM,EAAE;QACxB,OAAO,IAAI,IAAI,OAAO,EAAE;YACpB,SAAS,IAAI,CAAC,yBAAyB,OAAO;QAClD;QACA,MAAM,IAAI,CAAC;QAEX,MAAM,OAAO,CAAC,SAAS,IAAI;YACvB,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM;QACjC;QAEA,OAAO;IACX;IAEA,SAAS,2BAA2B,WAAW,EAAE,cAAc;QAC3D,IAAI,OAAO,aAAa;YAAC,MAAM;QAAW;QAC1C,IAAI,MAAM;YACN,OAAO;QACX,OAAO,IAAI,gBAAgB;YACvB,OAAO,UAAU,OAAO,CAAC,gBAAgB,CAAC,GAAG;gBAAC,OAAO;YAAK;QAC9D,OAAO;YACH,OAAO,UAAU,KAAK;QAC1B;IACJ;IAEA,SAAS,aAAa,OAAO,EAAE,WAAW;QACtC,IAAI,QAAQ,UAAU;QACtB,OAAO,QAAQ,MAAM,EAAE,GAAG;IAC9B;IAEA,SAAS,UAAU,OAAO;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnC,OAAO,QAAQ,CAAC,EAAE;YACtB;QACJ;IACJ;IAEA,SAAS,uBAAuB,YAAY;QACxC,OAAO,SAAS,KAAK,EAAE,QAAQ;YAC3B,OAAO,SAAS,OAAO,CAAC;gBACpB,OAAO,aAAa,OAAO;YAC/B,GAAG,MAAM,CAAC,SAAS,KAAK;gBACpB,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC;gBAC5B,OAAO,EAAE;YACb;QACJ;IACJ;IAEA,SAAS,WAAW,IAAI;QACpB,OAAO,eAAe,KAAK,QAAQ,EAAE,KAAK,MAAM;IACpD;IAEA,SAAS,cAAc,IAAI;QACvB,OAAO,gBAAgB,KAAK,QAAQ,EAAE,KAAK,MAAM;IACrD;IAEA,SAAS,eAAe,aAAa,EAAE,WAAW;QAC9C,OAAO,OAAO,gBAAgB,MAAM;IACxC;IAEA,SAAS,gBAAgB,aAAa,EAAE,WAAW;QAC/C,OAAO,OAAO,gBAAgB,UAAU;IAC5C;IAEA,SAAS,OAAO,MAAM;QAClB,OAAO,WAAW;IACtB;IAEA,IAAI,mBAAmB,UAAU,QAAQ,CAAC;QACtC,UAAU,OAAO,CAAC,SAAS,CAAC,GAAG;YAAC,OAAO;QAAI;KAC9C;IAED,SAAS,aAAa,OAAO,EAAE,QAAQ,EAAE,OAAO;QAC5C,OAAO,aAAa,SAAS,kBAAkB,IAAI,CAAC;YAChD,OAAO,qBAAqB,SAAS,UAAU;QACnD;IACJ;IAEA,SAAS,qBAAqB,OAAO,EAAE,QAAQ,EAAE,OAAO;QACpD,IAAI,YAAY,EAAE,SAAS,CAAC,QAAQ,QAAQ,EAAE,SAAS,KAAK;YACxD,OAAO,CAAC,MAAM,IAAI,KAAK,UAAU,KAAK,CAAC,QAAQ,IAAI,CAAC,MAAM,QAAQ;QACtE;QACA,IAAI,cAAc,CAAC,GAAG;YAClB,YAAY,QAAQ,QAAQ,CAAC,MAAM;QACvC;QACA,IAAI;QACJ,IAAI,cAAc,GAAG;YACjB,WAAW,gBACP,QAAQ,QAAQ,EAChB,UACA,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS;gBAAC,eAAe;YAAK;QAEnD,OAAO;YACH,IAAI,WAAW,gBACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,YAC1B,UACA,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS;gBAAC,eAAe;YAAI;YAE9C,IAAI,WAAW,gBACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,YACvB,UACA,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS;gBAAC,eAAe;YAAK;YAE/C,WAAW;gBACP,KAAK,YAAY,CAAC,SAAS,CAAC,GAAG;gBAC/B,KAAK,YAAY,CAAC,SAAS,CAAC,GAAG;aAClC;QACL;QACA,OAAO;YAAC,KAAK,UAAU;SAAC,CAAC,MAAM,CAAC;IACpC;IAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,OAAO;QAC/C,IAAI,WAAW,gBAAgB,QAAQ,QAAQ,EAAE,UAAU;QAC3D,OAAO;YACH,KAAK,YAAY,CAAC,MAAM,CAAC,GAAG;gBAAC,KAAK,UAAU;aAAC,CAAC,MAAM,CAAC;SACxD;IACL;IAEA,SAAS,iBAAiB,OAAO,EAAE,QAAQ,EAAE,OAAO;QAChD,IAAI,UAAU,QAAQ,aAAa,GAAG,OAAO;QAC7C,IAAI,WAAW,gBAAgB,QAAQ,QAAQ,EAAE,UAAU;QAC3D,IAAI,aAAa,CAAC;QAClB,IAAI,QAAQ,OAAO,KAAK,GAAG;YACvB,WAAW,OAAO,GAAG,QAAQ,OAAO,CAAC,QAAQ;QACjD;QACA,IAAI,QAAQ,OAAO,KAAK,GAAG;YACvB,WAAW,OAAO,GAAG,QAAQ,OAAO,CAAC,QAAQ;QACjD;QAEA,OAAO;YACH,KAAK,YAAY,CAAC,SAAS,YAAY;gBAAC,KAAK,UAAU;aAAC,CAAC,MAAM,CAAC;SACnE;IACL;IAEA,SAAS,wBAAwB,SAAS,EAAE,QAAQ,EAAE,OAAO;QACzD,OAAO,aAAa,WAAW,UAAU,MAAM,EAAE,IAAI,CAAC;YAClD,IAAI,UAAU,QAAQ,CAAC,UAAU,SAAS,CAAC;YAC3C,IAAI,QAAQ,mBAAmB,MAAM,GAAG;YACxC,IAAI,QAAQ,MAAM,mBAAmB,WAAW,QAAQ;YACxD,mBAAmB,IAAI,CAAC;gBAAC,OAAO;gBAAO,SAAS;YAAO;YACvD,gDAAgD;YAChD,OAAO;gBACH,KAAK,YAAY,CAAC,KAAK;oBACnB,MAAM,MAAM,eAAe,WAAW,UAAU,SAAS;oBACzD,IAAI,gBAAgB,WAAW,UAAU,SAAS;gBACtD,GAAG;oBAAC,KAAK,IAAI,CAAC;iBAAO;aACxB;QACL;IACJ;IAEA,SAAS,eAAe,iBAAiB,EAAE,QAAQ,EAAE,OAAO;QACxD,gDAAgD;QAEhD,IAAI,QAAQ,kBAAkB,KAAK;QACnC,IAAI,UAAU,kBAAkB,OAAO;QACvC,IAAI,OAAO,gBAAgB,QAAQ,IAAI,EAAE,UAAU,SAAS,MAAM,CAAC;YAC/D,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG;gBAC1B,KAAK,IAAI,CAAC;gBACV,KAAK,YAAY,CAAC,KAAK;oBAAC,QAAQ,MAAM,gBAAgB,WAAW,QAAQ,SAAS;gBAAC,GAAG;oBAClF,KAAK,IAAI,CAAC;iBACb;aACJ;SACJ;QAED,OAAO;YACH,KAAK,YAAY,CACb,MACA;gBAAC,MAAM,eAAe,WAAW,QAAQ,SAAS;YAAC,GACnD;gBAAC,KAAK,IAAI,CAAC,aAAa;aAAO;YAEnC,KAAK,YAAY,CAAC,MAAM,CAAC,GAAG;SAC/B;IACL;IAEA,SAAS,aAAa,OAAO,EAAE,QAAQ,EAAE,OAAO;QAC5C,OAAO,iBAAiB,SAAS,IAAI,CAAC;YAClC,OAAO,EAAE;QACb;IACJ;IAEA,SAAS,iBAAiB,OAAO;QAC7B,IAAI,QAAQ,UAAU;QACtB,IAAI,OAAO;YACP,OAAO,MAAM,EAAE;QACnB,OAAO,IAAI,QAAQ,SAAS,KAAK,QAAQ;YACrC,OAAO,UAAU,eAAe,CAAC;QACrC,OAAO;YACH,OAAO,UAAU,KAAK;QAC1B;IACJ;IAEA,IAAI,oBAAoB;QACpB,YAAY,SAAS,QAAQ,EAAE,QAAQ,EAAE,OAAO;YAC5C,IAAI,WAAW,gBAAgB,SAAS,QAAQ,EAAE,UAAU;YAC5D,IAAI,QAAQ,eAAe,GAAG,CAAC,SAAS,aAAa;gBACjD,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC;YAClC;YACA,IAAI,aAAa,gBAAgB,OAAO,UAAU;YAClD,OAAO,SAAS,MAAM,CAAC;gBACnB,KAAK,YAAY,CAAC,MAAM,CAAC,GAAG;gBAC5B,KAAK,YAAY,CAAC,MAAM,CAAC,GAAG,QAAQ,oBAAoB,SAAS,iBAAiB;oBAC9E,OAAO,eAAe,mBAAmB,UAAU;gBACvD;aACH;QACL;QACA,aAAa;QACb,OAAO;QACP,QAAQ,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YACvC,OAAO;gBAAC,KAAK,IAAI,CAAC,QAAQ,KAAK;aAAE;QACrC;QACA,OAAO,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YACtC,OAAO;gBAAC,KAAK,IAAI,CAAC;aAAM;QAC5B;QACA,aAAa,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YAC5C,IAAI,OAAO,QAAQ,MAAM,GAAG,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,IAAI;YACvE,IAAI,aAAa;gBAAC,MAAM;YAAI;YAC5B,IAAI,QAAQ,WAAW,IAAI,MAAM;gBAC7B,WAAW,MAAM,GAAG,QAAQ,WAAW;YAC3C;YAEA,IAAI,WAAW,gBAAgB,QAAQ,QAAQ,EAAE,UAAU;YAC3D,OAAO;gBAAC,KAAK,eAAe,CAAC,KAAK,YAAY;aAAU;QAC5D;QACA,YAAY,SAAS,OAAO;YACxB,IAAI,aAAa;gBAAC,MAAM;YAAU;YAClC,IAAI,QAAQ,OAAO,EAAE;gBACjB,UAAU,CAAC,UAAU,GAAG;YAC5B;YACA,OAAO;gBAAC,KAAK,YAAY,CAAC,SAAS;aAAY;QACnD;QACA,iBAAiB,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YAChD,IAAI,SAAS,KAAK,YAAY,CAAC,KAAK;gBAChC,IAAI,OAAO,QAAQ,IAAI;YAC3B,GAAG;gBAAC,KAAK,UAAU;aAAC;YACpB,OAAO;gBAAC;aAAO;QACnB;QACA,iBAAiB,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YAChD,eAAe,IAAI,CAAC;YACpB,IAAI,SAAS,KAAK,YAAY,CAAC,KAAK;gBAChC,MAAM,MAAM,WAAW;gBACvB,IAAI,cAAc;YACtB,GAAG;gBAAC,KAAK,IAAI,CAAC,MAAO,eAAgB;aAAK;YAE1C,OAAO;gBAAC,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG;oBAAC;iBAAO;aAAE;QACnD;QACA,QAAQ,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;YACvC,IAAI,WAAW,gBAAgB,QAAQ,IAAI,EAAE,UAAU;YACvD,IAAI,WAAW,KAAK,cAAc,CAAC,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG;gBAAC,OAAO;YAAK,IAAI;gBAC3E,KAAK,IAAI,CAAC;gBACV,KAAK,YAAY,CAAC,KAAK;oBAAC,MAAM,MAAM,cAAc;gBAAQ,GAAG;oBAAC,KAAK,IAAI,CAAC;iBAAK;aAChF;YACD,IAAI,OAAO,SAAS,MAAM,CAAC;gBAAC;aAAS;YAErC,OAAO,KAAK,YAAY,CAAC,MAAM;gBAAC,IAAI,WAAW;YAAQ,GAAG;QAC9D;QACA,oBAAoB;QACpB,WAAW;QACX,SAAS,mBAAmB,uBAAuB,QAAQ,YAAY,IAAI,OAAO,OAAO;QACzF,SAAS;QACT,YAAY;QACZ,aAAa;QACb,SAAS;IACb;IACA,OAAO;QACH,eAAe;IACnB;AACJ;AAEA,IAAI,aAAa;AAEjB,SAAS,mBAAmB,IAAI;IAC5B,OAAO,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO;QACtC,OAAO;YACH;gBACI,MAAM;gBACN,IAAI;gBACJ,OAAO;oBACH,OAAO,KAAK,SAAS,UAAU;gBACnC;YACJ;SACH;IACL;AACJ;AAEA,SAAS,yBAAyB,IAAI,EAAE,OAAO;IAC3C,OAAO,QAAQ,OAAO,CAClB,kBAAkB,OAAO,cAAc,QAAQ,SAAS,GAAG,MAC3D,iBAAiB,QAAQ,OAAO,GAAG;AAE3C;AAEA,SAAS,QAAQ,MAAM,EAAE,IAAI;IACzB,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,OAAO;AACvC;AAEA,SAAS,SAAS,KAAK,EAAE,QAAQ;IAC7B,MAAM,OAAO,CAAC,SAAS,IAAI;QACvB,SAAS;QACT,IAAI,KAAK,QAAQ,EAAE;YACf,SAAS,KAAK,QAAQ,EAAE;QAC5B;IACJ;AACJ;AAEA,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAS,mBAAmB,OAAO;IACrF,OAAO,QAAQ,cAAc,IAAI;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3052, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/raw-text.js"], "sourcesContent": ["var documents = require(\"./documents\");\n\nfunction convertElementToRawText(element) {\n    if (element.type === \"text\") {\n        return element.value;\n    } else if (element.type === documents.types.tab) {\n        return \"\\t\";\n    } else {\n        var tail = element.type === \"paragraph\" ? \"\\n\\n\" : \"\";\n        return (element.children || []).map(convertElementToRawText).join(\"\") + tail;\n    }\n}\n\nexports.convertElementToRawText = convertElementToRawText;\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,SAAS,wBAAwB,OAAO;IACpC,IAAI,QAAQ,IAAI,KAAK,QAAQ;QACzB,OAAO,QAAQ,KAAK;IACxB,OAAO,IAAI,QAAQ,IAAI,KAAK,UAAU,KAAK,CAAC,GAAG,EAAE;QAC7C,OAAO;IACX,OAAO;QACH,IAAI,OAAO,QAAQ,IAAI,KAAK,cAAc,SAAS;QACnD,OAAO,CAAC,QAAQ,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,yBAAyB,IAAI,CAAC,MAAM;IAC5E;AACJ;AAEA,QAAQ,uBAAuB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3069, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/styles/document-matchers.js"], "sourcesContent": ["exports.paragraph = paragraph;\nexports.run = run;\nexports.table = table;\nexports.bold = new Matcher(\"bold\");\nexports.italic = new Matcher(\"italic\");\nexports.underline = new Matcher(\"underline\");\nexports.strikethrough = new Matcher(\"strikethrough\");\nexports.allCaps = new Matcher(\"allCaps\");\nexports.smallCaps = new Matcher(\"smallCaps\");\nexports.highlight = highlight;\nexports.commentReference = new Matcher(\"commentReference\");\nexports.lineBreak = new BreakMatcher({breakType: \"line\"});\nexports.pageBreak = new BreakMatcher({breakType: \"page\"});\nexports.columnBreak = new BreakMatcher({breakType: \"column\"});\nexports.equalTo = equalTo;\nexports.startsWith = startsWith;\n\n\nfunction paragraph(options) {\n    return new Matcher(\"paragraph\", options);\n}\n\nfunction run(options) {\n    return new Matcher(\"run\", options);\n}\n\nfunction table(options) {\n    return new Matcher(\"table\", options);\n}\n\nfunction highlight(options) {\n    return new HighlightMatcher(options);\n}\n\nfunction Matcher(elementType, options) {\n    options = options || {};\n    this._elementType = elementType;\n    this._styleId = options.styleId;\n    this._styleName = options.styleName;\n    if (options.list) {\n        this._listIndex = options.list.levelIndex;\n        this._listIsOrdered = options.list.isOrdered;\n    }\n}\n\nMatcher.prototype.matches = function(element) {\n    return element.type === this._elementType &&\n        (this._styleId === undefined || element.styleId === this._styleId) &&\n        (this._styleName === undefined || (element.styleName && this._styleName.operator(this._styleName.operand, element.styleName))) &&\n        (this._listIndex === undefined || isList(element, this._listIndex, this._listIsOrdered)) &&\n        (this._breakType === undefined || this._breakType === element.breakType);\n};\n\nfunction HighlightMatcher(options) {\n    options = options || {};\n    this._color = options.color;\n}\n\nHighlightMatcher.prototype.matches = function(element) {\n    return element.type === \"highlight\" &&\n        (this._color === undefined || element.color === this._color);\n};\n\nfunction BreakMatcher(options) {\n    options = options || {};\n    this._breakType = options.breakType;\n}\n\nBreakMatcher.prototype.matches = function(element) {\n    return element.type === \"break\" &&\n        (this._breakType === undefined || element.breakType === this._breakType);\n};\n\nfunction isList(element, levelIndex, isOrdered) {\n    return element.numbering &&\n        element.numbering.level == levelIndex &&\n        element.numbering.isOrdered == isOrdered;\n}\n\nfunction equalTo(value) {\n    return {\n        operator: operatorEqualTo,\n        operand: value\n    };\n}\n\nfunction startsWith(value) {\n    return {\n        operator: operatorStartsWith,\n        operand: value\n    };\n}\n\nfunction operatorEqualTo(first, second) {\n    return first.toUpperCase() === second.toUpperCase();\n}\n\nfunction operatorStartsWith(first, second) {\n    return second.toUpperCase().indexOf(first.toUpperCase()) === 0;\n}\n"], "names": [], "mappings": "AAAA,QAAQ,SAAS,GAAG;AACpB,QAAQ,GAAG,GAAG;AACd,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG,IAAI,QAAQ;AAC3B,QAAQ,MAAM,GAAG,IAAI,QAAQ;AAC7B,QAAQ,SAAS,GAAG,IAAI,QAAQ;AAChC,QAAQ,aAAa,GAAG,IAAI,QAAQ;AACpC,QAAQ,OAAO,GAAG,IAAI,QAAQ;AAC9B,QAAQ,SAAS,GAAG,IAAI,QAAQ;AAChC,QAAQ,SAAS,GAAG;AACpB,QAAQ,gBAAgB,GAAG,IAAI,QAAQ;AACvC,QAAQ,SAAS,GAAG,IAAI,aAAa;IAAC,WAAW;AAAM;AACvD,QAAQ,SAAS,GAAG,IAAI,aAAa;IAAC,WAAW;AAAM;AACvD,QAAQ,WAAW,GAAG,IAAI,aAAa;IAAC,WAAW;AAAQ;AAC3D,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AAGrB,SAAS,UAAU,OAAO;IACtB,OAAO,IAAI,QAAQ,aAAa;AACpC;AAEA,SAAS,IAAI,OAAO;IAChB,OAAO,IAAI,QAAQ,OAAO;AAC9B;AAEA,SAAS,MAAM,OAAO;IAClB,OAAO,IAAI,QAAQ,SAAS;AAChC;AAEA,SAAS,UAAU,OAAO;IACtB,OAAO,IAAI,iBAAiB;AAChC;AAEA,SAAS,QAAQ,WAAW,EAAE,OAAO;IACjC,UAAU,WAAW,CAAC;IACtB,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;IAC/B,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;IACnC,IAAI,QAAQ,IAAI,EAAE;QACd,IAAI,CAAC,UAAU,GAAG,QAAQ,IAAI,CAAC,UAAU;QACzC,IAAI,CAAC,cAAc,GAAG,QAAQ,IAAI,CAAC,SAAS;IAChD;AACJ;AAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO;IACxC,OAAO,QAAQ,IAAI,KAAK,IAAI,CAAC,YAAY,IACrC,CAAC,IAAI,CAAC,QAAQ,KAAK,aAAa,QAAQ,OAAO,KAAK,IAAI,CAAC,QAAQ,KACjE,CAAC,IAAI,CAAC,UAAU,KAAK,aAAc,QAAQ,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,SAAS,CAAE,KAC7H,CAAC,IAAI,CAAC,UAAU,KAAK,aAAa,OAAO,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,KACvF,CAAC,IAAI,CAAC,UAAU,KAAK,aAAa,IAAI,CAAC,UAAU,KAAK,QAAQ,SAAS;AAC/E;AAEA,SAAS,iBAAiB,OAAO;IAC7B,UAAU,WAAW,CAAC;IACtB,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;AAC/B;AAEA,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO;IACjD,OAAO,QAAQ,IAAI,KAAK,eACpB,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,QAAQ,KAAK,KAAK,IAAI,CAAC,MAAM;AACnE;AAEA,SAAS,aAAa,OAAO;IACzB,UAAU,WAAW,CAAC;IACtB,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;AACvC;AAEA,aAAa,SAAS,CAAC,OAAO,GAAG,SAAS,OAAO;IAC7C,OAAO,QAAQ,IAAI,KAAK,WACpB,CAAC,IAAI,CAAC,UAAU,KAAK,aAAa,QAAQ,SAAS,KAAK,IAAI,CAAC,UAAU;AAC/E;AAEA,SAAS,OAAO,OAAO,EAAE,UAAU,EAAE,SAAS;IAC1C,OAAO,QAAQ,SAAS,IACpB,QAAQ,SAAS,CAAC,KAAK,IAAI,cAC3B,QAAQ,SAAS,CAAC,SAAS,IAAI;AACvC;AAEA,SAAS,QAAQ,KAAK;IAClB,OAAO;QACH,UAAU;QACV,SAAS;IACb;AACJ;AAEA,SAAS,WAAW,KAAK;IACrB,OAAO;QACH,UAAU;QACV,SAAS;IACb;AACJ;AAEA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IAClC,OAAO,MAAM,WAAW,OAAO,OAAO,WAAW;AACrD;AAEA,SAAS,mBAAmB,KAAK,EAAE,MAAM;IACrC,OAAO,OAAO,WAAW,GAAG,OAAO,CAAC,MAAM,WAAW,QAAQ;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/styles/parser/tokeniser.js"], "sourcesContent": ["var lop = require(\"lop\");\nvar RegexTokeniser = lop.RegexTokeniser;\n\nexports.tokenise = tokenise;\n\nvar stringPrefix = \"'((?:\\\\\\\\.|[^'])*)\";\n\nfunction tokenise(string) {\n    var identifierCharacter = \"(?:[a-zA-Z\\\\-_]|\\\\\\\\.)\";\n    var tokeniser = new RegexTokeniser([\n        {name: \"identifier\", regex: new RegExp(\"(\" + identifierCharacter + \"(?:\" + identifierCharacter + \"|[0-9])*)\")},\n        {name: \"dot\", regex: /\\./},\n        {name: \"colon\", regex: /:/},\n        {name: \"gt\", regex: />/},\n        {name: \"whitespace\", regex: /\\s+/},\n        {name: \"arrow\", regex: /=>/},\n        {name: \"equals\", regex: /=/},\n        {name: \"startsWith\", regex: /\\^=/},\n        {name: \"open-paren\", regex: /\\(/},\n        {name: \"close-paren\", regex: /\\)/},\n        {name: \"open-square-bracket\", regex: /\\[/},\n        {name: \"close-square-bracket\", regex: /\\]/},\n        {name: \"string\", regex: new RegExp(stringPrefix + \"'\")},\n        {name: \"unterminated-string\", regex: new RegExp(stringPrefix)},\n        {name: \"integer\", regex: /([0-9]+)/},\n        {name: \"choice\", regex: /\\|/},\n        {name: \"bang\", regex: /(!)/}\n    ]);\n    return tokeniser.tokenise(string);\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,iBAAiB,IAAI,cAAc;AAEvC,QAAQ,QAAQ,GAAG;AAEnB,IAAI,eAAe;AAEnB,SAAS,SAAS,MAAM;IACpB,IAAI,sBAAsB;IAC1B,IAAI,YAAY,IAAI,eAAe;QAC/B;YAAC,MAAM;YAAc,OAAO,IAAI,OAAO,MAAM,sBAAsB,QAAQ,sBAAsB;QAAY;QAC7G;YAAC,MAAM;YAAO,OAAO;QAAI;QACzB;YAAC,MAAM;YAAS,OAAO;QAAG;QAC1B;YAAC,MAAM;YAAM,OAAO;QAAG;QACvB;YAAC,MAAM;YAAc,OAAO;QAAK;QACjC;YAAC,MAAM;YAAS,OAAO;QAAI;QAC3B;YAAC,MAAM;YAAU,OAAO;QAAG;QAC3B;YAAC,MAAM;YAAc,OAAO;QAAK;QACjC;YAAC,MAAM;YAAc,OAAO;QAAI;QAChC;YAAC,MAAM;YAAe,OAAO;QAAI;QACjC;YAAC,MAAM;YAAuB,OAAO;QAAI;QACzC;YAAC,MAAM;YAAwB,OAAO;QAAI;QAC1C;YAAC,MAAM;YAAU,OAAO,IAAI,OAAO,eAAe;QAAI;QACtD;YAAC,MAAM;YAAuB,OAAO,IAAI,OAAO;QAAa;QAC7D;YAAC,MAAM;YAAW,OAAO;QAAU;QACnC;YAAC,MAAM;YAAU,OAAO;QAAI;QAC5B;YAAC,MAAM;YAAQ,OAAO;QAAK;KAC9B;IACD,OAAO,UAAU,QAAQ,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/style-reader.js"], "sourcesContent": ["var _ = require(\"underscore\");\nvar lop = require(\"lop\");\n\nvar documentMatchers = require(\"./styles/document-matchers\");\nvar htmlPaths = require(\"./styles/html-paths\");\nvar tokenise = require(\"./styles/parser/tokeniser\").tokenise;\nvar results = require(\"./results\");\n\nexports.readHtmlPath = readHtmlPath;\nexports.readDocumentMatcher = readDocumentMatcher;\nexports.readStyle = readStyle;\n\n\nfunction readStyle(string) {\n    return parseString(styleRule, string);\n}\n\nfunction createStyleRule() {\n    return lop.rules.sequence(\n        lop.rules.sequence.capture(documentMatcherRule()),\n        lop.rules.tokenOfType(\"whitespace\"),\n        lop.rules.tokenOfType(\"arrow\"),\n        lop.rules.sequence.capture(lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"whitespace\"),\n            lop.rules.sequence.capture(htmlPathRule())\n        ).head())),\n        lop.rules.tokenOfType(\"end\")\n    ).map(function(documentMatcher, htmlPath) {\n        return {\n            from: documentMatcher,\n            to: htmlPath.valueOrElse(htmlPaths.empty)\n        };\n    });\n}\n\nfunction readDocumentMatcher(string) {\n    return parseString(documentMatcherRule(), string);\n}\n\nfunction documentMatcherRule() {\n    var sequence = lop.rules.sequence;\n\n    var identifierToConstant = function(identifier, constant) {\n        return lop.rules.then(\n            lop.rules.token(\"identifier\", identifier),\n            function() {\n                return constant;\n            }\n        );\n    };\n\n    var paragraphRule = identifierToConstant(\"p\", documentMatchers.paragraph);\n    var runRule = identifierToConstant(\"r\", documentMatchers.run);\n\n    var elementTypeRule = lop.rules.firstOf(\"p or r or table\",\n        paragraphRule,\n        runRule\n    );\n\n    var styleIdRule = lop.rules.sequence(\n        lop.rules.tokenOfType(\"dot\"),\n        lop.rules.sequence.cut(),\n        lop.rules.sequence.capture(identifierRule)\n    ).map(function(styleId) {\n        return {styleId: styleId};\n    });\n\n    var styleNameMatcherRule = lop.rules.firstOf(\"style name matcher\",\n        lop.rules.then(\n            lop.rules.sequence(\n                lop.rules.tokenOfType(\"equals\"),\n                lop.rules.sequence.cut(),\n                lop.rules.sequence.capture(stringRule)\n            ).head(),\n            function(styleName) {\n                return {styleName: documentMatchers.equalTo(styleName)};\n            }\n        ),\n        lop.rules.then(\n            lop.rules.sequence(\n                lop.rules.tokenOfType(\"startsWith\"),\n                lop.rules.sequence.cut(),\n                lop.rules.sequence.capture(stringRule)\n            ).head(),\n            function(styleName) {\n                return {styleName: documentMatchers.startsWith(styleName)};\n            }\n        )\n    );\n\n    var styleNameRule = lop.rules.sequence(\n        lop.rules.tokenOfType(\"open-square-bracket\"),\n        lop.rules.sequence.cut(),\n        lop.rules.token(\"identifier\", \"style-name\"),\n        lop.rules.sequence.capture(styleNameMatcherRule),\n        lop.rules.tokenOfType(\"close-square-bracket\")\n    ).head();\n\n\n    var listTypeRule = lop.rules.firstOf(\"list type\",\n        identifierToConstant(\"ordered-list\", {isOrdered: true}),\n        identifierToConstant(\"unordered-list\", {isOrdered: false})\n    );\n    var listRule = sequence(\n        lop.rules.tokenOfType(\"colon\"),\n        sequence.capture(listTypeRule),\n        sequence.cut(),\n        lop.rules.tokenOfType(\"open-paren\"),\n        sequence.capture(integerRule),\n        lop.rules.tokenOfType(\"close-paren\")\n    ).map(function(listType, levelNumber) {\n        return {\n            list: {\n                isOrdered: listType.isOrdered,\n                levelIndex: levelNumber - 1\n            }\n        };\n    });\n\n    function createMatcherSuffixesRule(rules) {\n        var matcherSuffix = lop.rules.firstOf.apply(\n            lop.rules.firstOf,\n            [\"matcher suffix\"].concat(rules)\n        );\n        var matcherSuffixes = lop.rules.zeroOrMore(matcherSuffix);\n        return lop.rules.then(matcherSuffixes, function(suffixes) {\n            var matcherOptions = {};\n            suffixes.forEach(function(suffix) {\n                _.extend(matcherOptions, suffix);\n            });\n            return matcherOptions;\n        });\n    }\n\n    var paragraphOrRun = sequence(\n        sequence.capture(elementTypeRule),\n        sequence.capture(createMatcherSuffixesRule([\n            styleIdRule,\n            styleNameRule,\n            listRule\n        ]))\n    ).map(function(createMatcher, matcherOptions) {\n        return createMatcher(matcherOptions);\n    });\n\n    var table = sequence(\n        lop.rules.token(\"identifier\", \"table\"),\n        sequence.capture(createMatcherSuffixesRule([\n            styleIdRule,\n            styleNameRule\n        ]))\n    ).map(function(options) {\n        return documentMatchers.table(options);\n    });\n\n    var bold = identifierToConstant(\"b\", documentMatchers.bold);\n    var italic = identifierToConstant(\"i\", documentMatchers.italic);\n    var underline = identifierToConstant(\"u\", documentMatchers.underline);\n    var strikethrough = identifierToConstant(\"strike\", documentMatchers.strikethrough);\n    var allCaps = identifierToConstant(\"all-caps\", documentMatchers.allCaps);\n    var smallCaps = identifierToConstant(\"small-caps\", documentMatchers.smallCaps);\n\n    var highlight = sequence(\n        lop.rules.token(\"identifier\", \"highlight\"),\n        lop.rules.sequence.capture(lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"open-square-bracket\"),\n            lop.rules.sequence.cut(),\n            lop.rules.token(\"identifier\", \"color\"),\n            lop.rules.tokenOfType(\"equals\"),\n            lop.rules.sequence.capture(stringRule),\n            lop.rules.tokenOfType(\"close-square-bracket\")\n        ).head()))\n    ).map(function(color) {\n        return documentMatchers.highlight({\n            color: color.valueOrElse(undefined)\n        });\n    });\n\n    var commentReference = identifierToConstant(\"comment-reference\", documentMatchers.commentReference);\n\n    var breakMatcher = sequence(\n        lop.rules.token(\"identifier\", \"br\"),\n        sequence.cut(),\n        lop.rules.tokenOfType(\"open-square-bracket\"),\n        lop.rules.token(\"identifier\", \"type\"),\n        lop.rules.tokenOfType(\"equals\"),\n        sequence.capture(stringRule),\n        lop.rules.tokenOfType(\"close-square-bracket\")\n    ).map(function(breakType) {\n        switch (breakType) {\n        case \"line\":\n            return documentMatchers.lineBreak;\n        case \"page\":\n            return documentMatchers.pageBreak;\n        case \"column\":\n            return documentMatchers.columnBreak;\n        default:\n            // TODO: handle unknown document matchers\n        }\n    });\n\n    return lop.rules.firstOf(\"element type\",\n        paragraphOrRun,\n        table,\n        bold,\n        italic,\n        underline,\n        strikethrough,\n        allCaps,\n        smallCaps,\n        highlight,\n        commentReference,\n        breakMatcher\n    );\n}\n\nfunction readHtmlPath(string) {\n    return parseString(htmlPathRule(), string);\n}\n\nfunction htmlPathRule() {\n    var capture = lop.rules.sequence.capture;\n    var whitespaceRule = lop.rules.tokenOfType(\"whitespace\");\n    var freshRule = lop.rules.then(\n        lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"colon\"),\n            lop.rules.token(\"identifier\", \"fresh\")\n        )),\n        function(option) {\n            return option.map(function() {\n                return true;\n            }).valueOrElse(false);\n        }\n    );\n\n    var separatorRule = lop.rules.then(\n        lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"colon\"),\n            lop.rules.token(\"identifier\", \"separator\"),\n            lop.rules.tokenOfType(\"open-paren\"),\n            capture(stringRule),\n            lop.rules.tokenOfType(\"close-paren\")\n        ).head()),\n        function(option) {\n            return option.valueOrElse(\"\");\n        }\n    );\n\n    var tagNamesRule = lop.rules.oneOrMoreWithSeparator(\n        identifierRule,\n        lop.rules.tokenOfType(\"choice\")\n    );\n\n    var styleElementRule = lop.rules.sequence(\n        capture(tagNamesRule),\n        capture(lop.rules.zeroOrMore(attributeOrClassRule)),\n        capture(freshRule),\n        capture(separatorRule)\n    ).map(function(tagName, attributesList, fresh, separator) {\n        var attributes = {};\n        var options = {};\n        attributesList.forEach(function(attribute) {\n            if (attribute.append && attributes[attribute.name]) {\n                attributes[attribute.name] += \" \" + attribute.value;\n            } else {\n                attributes[attribute.name] = attribute.value;\n            }\n        });\n        if (fresh) {\n            options.fresh = true;\n        }\n        if (separator) {\n            options.separator = separator;\n        }\n        return htmlPaths.element(tagName, attributes, options);\n    });\n\n    return lop.rules.firstOf(\"html path\",\n        lop.rules.then(lop.rules.tokenOfType(\"bang\"), function() {\n            return htmlPaths.ignore;\n        }),\n        lop.rules.then(\n            lop.rules.zeroOrMoreWithSeparator(\n                styleElementRule,\n                lop.rules.sequence(\n                    whitespaceRule,\n                    lop.rules.tokenOfType(\"gt\"),\n                    whitespaceRule\n                )\n            ),\n            htmlPaths.elements\n        )\n    );\n}\n\nvar identifierRule = lop.rules.then(\n    lop.rules.tokenOfType(\"identifier\"),\n    decodeEscapeSequences\n);\nvar integerRule = lop.rules.tokenOfType(\"integer\");\n\nvar stringRule = lop.rules.then(\n    lop.rules.tokenOfType(\"string\"),\n    decodeEscapeSequences\n);\n\nvar escapeSequences = {\n    \"n\": \"\\n\",\n    \"r\": \"\\r\",\n    \"t\": \"\\t\"\n};\n\nfunction decodeEscapeSequences(value) {\n    return value.replace(/\\\\(.)/g, function(match, code) {\n        return escapeSequences[code] || code;\n    });\n}\n\nvar attributeRule = lop.rules.sequence(\n    lop.rules.tokenOfType(\"open-square-bracket\"),\n    lop.rules.sequence.cut(),\n    lop.rules.sequence.capture(identifierRule),\n    lop.rules.tokenOfType(\"equals\"),\n    lop.rules.sequence.capture(stringRule),\n    lop.rules.tokenOfType(\"close-square-bracket\")\n).map(function(name, value) {\n    return {name: name, value: value, append: false};\n});\n\nvar classRule = lop.rules.sequence(\n    lop.rules.tokenOfType(\"dot\"),\n    lop.rules.sequence.cut(),\n    lop.rules.sequence.capture(identifierRule)\n).map(function(className) {\n    return {name: \"class\", value: className, append: true};\n});\n\nvar attributeOrClassRule = lop.rules.firstOf(\n    \"attribute or class\",\n    attributeRule,\n    classRule\n);\n\nfunction parseString(rule, string) {\n    var tokens = tokenise(string);\n    var parser = lop.Parser();\n    var parseResult = parser.parseTokens(rule, tokens);\n    if (parseResult.isSuccess()) {\n        return results.success(parseResult.value());\n    } else {\n        return new results.Result(null, [results.warning(describeFailure(string, parseResult))]);\n    }\n}\n\nfunction describeFailure(input, parseResult) {\n    return \"Did not understand this style mapping, so ignored it: \" + input + \"\\n\" +\n        parseResult.errors().map(describeError).join(\"\\n\");\n}\n\nfunction describeError(error) {\n    return \"Error was at character number \" + error.characterNumber() + \": \" +\n        \"Expected \" + error.expected + \" but got \" + error.actual;\n}\n\nvar styleRule = createStyleRule();\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,mHAAqC,QAAQ;AAC5D,IAAI;AAEJ,QAAQ,YAAY,GAAG;AACvB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,SAAS,GAAG;AAGpB,SAAS,UAAU,MAAM;IACrB,OAAO,YAAY,WAAW;AAClC;AAEA,SAAS;IACL,OAAO,IAAI,KAAK,CAAC,QAAQ,CACrB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,wBAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB,IAAI,KAAK,CAAC,WAAW,CAAC,UACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAC5D,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAC7B,IAAI,MACN,IAAI,KAAK,CAAC,WAAW,CAAC,QACxB,GAAG,CAAC,SAAS,eAAe,EAAE,QAAQ;QACpC,OAAO;YACH,MAAM;YACN,IAAI,SAAS,WAAW,CAAC,UAAU,KAAK;QAC5C;IACJ;AACJ;AAEA,SAAS,oBAAoB,MAAM;IAC/B,OAAO,YAAY,uBAAuB;AAC9C;AAEA,SAAS;IACL,IAAI,WAAW,IAAI,KAAK,CAAC,QAAQ;IAEjC,IAAI,uBAAuB,SAAS,UAAU,EAAE,QAAQ;QACpD,OAAO,IAAI,KAAK,CAAC,IAAI,CACjB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,aAC9B;YACI,OAAO;QACX;IAER;IAEA,IAAI,gBAAgB,qBAAqB,KAAK,iBAAiB,SAAS;IACxE,IAAI,UAAU,qBAAqB,KAAK,iBAAiB,GAAG;IAE5D,IAAI,kBAAkB,IAAI,KAAK,CAAC,OAAO,CAAC,mBACpC,eACA;IAGJ,IAAI,cAAc,IAAI,KAAK,CAAC,QAAQ,CAChC,IAAI,KAAK,CAAC,WAAW,CAAC,QACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAC7B,GAAG,CAAC,SAAS,OAAO;QAClB,OAAO;YAAC,SAAS;QAAO;IAC5B;IAEA,IAAI,uBAAuB,IAAI,KAAK,CAAC,OAAO,CAAC,sBACzC,IAAI,KAAK,CAAC,IAAI,CACV,IAAI,KAAK,CAAC,QAAQ,CACd,IAAI,KAAK,CAAC,WAAW,CAAC,WACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAC7B,IAAI,IACN,SAAS,SAAS;QACd,OAAO;YAAC,WAAW,iBAAiB,OAAO,CAAC;QAAU;IAC1D,IAEJ,IAAI,KAAK,CAAC,IAAI,CACV,IAAI,KAAK,CAAC,QAAQ,CACd,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAC7B,IAAI,IACN,SAAS,SAAS;QACd,OAAO;YAAC,WAAW,iBAAiB,UAAU,CAAC;QAAU;IAC7D;IAIR,IAAI,gBAAgB,IAAI,KAAK,CAAC,QAAQ,CAClC,IAAI,KAAK,CAAC,WAAW,CAAC,wBACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,eAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,yBACxB,IAAI;IAGN,IAAI,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,aACjC,qBAAqB,gBAAgB;QAAC,WAAW;IAAI,IACrD,qBAAqB,kBAAkB;QAAC,WAAW;IAAK;IAE5D,IAAI,WAAW,SACX,IAAI,KAAK,CAAC,WAAW,CAAC,UACtB,SAAS,OAAO,CAAC,eACjB,SAAS,GAAG,IACZ,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB,SAAS,OAAO,CAAC,cACjB,IAAI,KAAK,CAAC,WAAW,CAAC,gBACxB,GAAG,CAAC,SAAS,QAAQ,EAAE,WAAW;QAChC,OAAO;YACH,MAAM;gBACF,WAAW,SAAS,SAAS;gBAC7B,YAAY,cAAc;YAC9B;QACJ;IACJ;IAEA,SAAS,0BAA0B,KAAK;QACpC,IAAI,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CACvC,IAAI,KAAK,CAAC,OAAO,EACjB;YAAC;SAAiB,CAAC,MAAM,CAAC;QAE9B,IAAI,kBAAkB,IAAI,KAAK,CAAC,UAAU,CAAC;QAC3C,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,iBAAiB,SAAS,QAAQ;YACpD,IAAI,iBAAiB,CAAC;YACtB,SAAS,OAAO,CAAC,SAAS,MAAM;gBAC5B,EAAE,MAAM,CAAC,gBAAgB;YAC7B;YACA,OAAO;QACX;IACJ;IAEA,IAAI,iBAAiB,SACjB,SAAS,OAAO,CAAC,kBACjB,SAAS,OAAO,CAAC,0BAA0B;QACvC;QACA;QACA;KACH,IACH,GAAG,CAAC,SAAS,aAAa,EAAE,cAAc;QACxC,OAAO,cAAc;IACzB;IAEA,IAAI,QAAQ,SACR,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,UAC9B,SAAS,OAAO,CAAC,0BAA0B;QACvC;QACA;KACH,IACH,GAAG,CAAC,SAAS,OAAO;QAClB,OAAO,iBAAiB,KAAK,CAAC;IAClC;IAEA,IAAI,OAAO,qBAAqB,KAAK,iBAAiB,IAAI;IAC1D,IAAI,SAAS,qBAAqB,KAAK,iBAAiB,MAAM;IAC9D,IAAI,YAAY,qBAAqB,KAAK,iBAAiB,SAAS;IACpE,IAAI,gBAAgB,qBAAqB,UAAU,iBAAiB,aAAa;IACjF,IAAI,UAAU,qBAAqB,YAAY,iBAAiB,OAAO;IACvE,IAAI,YAAY,qBAAqB,cAAc,iBAAiB,SAAS;IAE7E,IAAI,YAAY,SACZ,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,cAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CAC5D,IAAI,KAAK,CAAC,WAAW,CAAC,wBACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,UAC9B,IAAI,KAAK,CAAC,WAAW,CAAC,WACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,yBACxB,IAAI,MACR,GAAG,CAAC,SAAS,KAAK;QAChB,OAAO,iBAAiB,SAAS,CAAC;YAC9B,OAAO,MAAM,WAAW,CAAC;QAC7B;IACJ;IAEA,IAAI,mBAAmB,qBAAqB,qBAAqB,iBAAiB,gBAAgB;IAElG,IAAI,eAAe,SACf,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,OAC9B,SAAS,GAAG,IACZ,IAAI,KAAK,CAAC,WAAW,CAAC,wBACtB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,SAC9B,IAAI,KAAK,CAAC,WAAW,CAAC,WACtB,SAAS,OAAO,CAAC,aACjB,IAAI,KAAK,CAAC,WAAW,CAAC,yBACxB,GAAG,CAAC,SAAS,SAAS;QACpB,OAAQ;YACR,KAAK;gBACD,OAAO,iBAAiB,SAAS;YACrC,KAAK;gBACD,OAAO,iBAAiB,SAAS;YACrC,KAAK;gBACD,OAAO,iBAAiB,WAAW;YACvC;QAEA;IACJ;IAEA,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,gBACrB,gBACA,OACA,MACA,QACA,WACA,eACA,SACA,WACA,WACA,kBACA;AAER;AAEA,SAAS,aAAa,MAAM;IACxB,OAAO,YAAY,gBAAgB;AACvC;AAEA,SAAS;IACL,IAAI,UAAU,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO;IACxC,IAAI,iBAAiB,IAAI,KAAK,CAAC,WAAW,CAAC;IAC3C,IAAI,YAAY,IAAI,KAAK,CAAC,IAAI,CAC1B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CACjC,IAAI,KAAK,CAAC,WAAW,CAAC,UACtB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,YAElC,SAAS,MAAM;QACX,OAAO,OAAO,GAAG,CAAC;YACd,OAAO;QACX,GAAG,WAAW,CAAC;IACnB;IAGJ,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,QAAQ,CACjC,IAAI,KAAK,CAAC,WAAW,CAAC,UACtB,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,cAC9B,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB,QAAQ,aACR,IAAI,KAAK,CAAC,WAAW,CAAC,gBACxB,IAAI,KACN,SAAS,MAAM;QACX,OAAO,OAAO,WAAW,CAAC;IAC9B;IAGJ,IAAI,eAAe,IAAI,KAAK,CAAC,sBAAsB,CAC/C,gBACA,IAAI,KAAK,CAAC,WAAW,CAAC;IAG1B,IAAI,mBAAmB,IAAI,KAAK,CAAC,QAAQ,CACrC,QAAQ,eACR,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,wBAC7B,QAAQ,YACR,QAAQ,gBACV,GAAG,CAAC,SAAS,OAAO,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS;QACpD,IAAI,aAAa,CAAC;QAClB,IAAI,UAAU,CAAC;QACf,eAAe,OAAO,CAAC,SAAS,SAAS;YACrC,IAAI,UAAU,MAAM,IAAI,UAAU,CAAC,UAAU,IAAI,CAAC,EAAE;gBAChD,UAAU,CAAC,UAAU,IAAI,CAAC,IAAI,MAAM,UAAU,KAAK;YACvD,OAAO;gBACH,UAAU,CAAC,UAAU,IAAI,CAAC,GAAG,UAAU,KAAK;YAChD;QACJ;QACA,IAAI,OAAO;YACP,QAAQ,KAAK,GAAG;QACpB;QACA,IAAI,WAAW;YACX,QAAQ,SAAS,GAAG;QACxB;QACA,OAAO,UAAU,OAAO,CAAC,SAAS,YAAY;IAClD;IAEA,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,aACrB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,SAAS;QAC1C,OAAO,UAAU,MAAM;IAC3B,IACA,IAAI,KAAK,CAAC,IAAI,CACV,IAAI,KAAK,CAAC,uBAAuB,CAC7B,kBACA,IAAI,KAAK,CAAC,QAAQ,CACd,gBACA,IAAI,KAAK,CAAC,WAAW,CAAC,OACtB,kBAGR,UAAU,QAAQ;AAG9B;AAEA,IAAI,iBAAiB,IAAI,KAAK,CAAC,IAAI,CAC/B,IAAI,KAAK,CAAC,WAAW,CAAC,eACtB;AAEJ,IAAI,cAAc,IAAI,KAAK,CAAC,WAAW,CAAC;AAExC,IAAI,aAAa,IAAI,KAAK,CAAC,IAAI,CAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,WACtB;AAGJ,IAAI,kBAAkB;IAClB,KAAK;IACL,KAAK;IACL,KAAK;AACT;AAEA,SAAS,sBAAsB,KAAK;IAChC,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS,KAAK,EAAE,IAAI;QAC/C,OAAO,eAAe,CAAC,KAAK,IAAI;IACpC;AACJ;AAEA,IAAI,gBAAgB,IAAI,KAAK,CAAC,QAAQ,CAClC,IAAI,KAAK,CAAC,WAAW,CAAC,wBACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,WACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,yBACxB,GAAG,CAAC,SAAS,IAAI,EAAE,KAAK;IACtB,OAAO;QAAC,MAAM;QAAM,OAAO;QAAO,QAAQ;IAAK;AACnD;AAEA,IAAI,YAAY,IAAI,KAAK,CAAC,QAAQ,CAC9B,IAAI,KAAK,CAAC,WAAW,CAAC,QACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,IACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAC7B,GAAG,CAAC,SAAS,SAAS;IACpB,OAAO;QAAC,MAAM;QAAS,OAAO;QAAW,QAAQ;IAAI;AACzD;AAEA,IAAI,uBAAuB,IAAI,KAAK,CAAC,OAAO,CACxC,sBACA,eACA;AAGJ,SAAS,YAAY,IAAI,EAAE,MAAM;IAC7B,IAAI,SAAS,SAAS;IACtB,IAAI,SAAS,IAAI,MAAM;IACvB,IAAI,cAAc,OAAO,WAAW,CAAC,MAAM;IAC3C,IAAI,YAAY,SAAS,IAAI;QACzB,OAAO,QAAQ,OAAO,CAAC,YAAY,KAAK;IAC5C,OAAO;QACH,OAAO,IAAI,QAAQ,MAAM,CAAC,MAAM;YAAC,QAAQ,OAAO,CAAC,gBAAgB,QAAQ;SAAc;IAC3F;AACJ;AAEA,SAAS,gBAAgB,KAAK,EAAE,WAAW;IACvC,OAAO,2DAA2D,QAAQ,OACtE,YAAY,MAAM,GAAG,GAAG,CAAC,eAAe,IAAI,CAAC;AACrD;AAEA,SAAS,cAAc,KAAK;IACxB,OAAO,mCAAmC,MAAM,eAAe,KAAK,OAChE,cAAc,MAAM,QAAQ,GAAG,cAAc,MAAM,MAAM;AACjE;AAEA,IAAI,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/options-reader.js"], "sourcesContent": ["exports.readOptions = readOptions;\n\n\nvar _ = require(\"underscore\");\n\nvar defaultStyleMap = exports._defaultStyleMap = [\n    \"p.Heading1 => h1:fresh\",\n    \"p.Heading2 => h2:fresh\",\n    \"p.Heading3 => h3:fresh\",\n    \"p.Heading4 => h4:fresh\",\n    \"p.Heading5 => h5:fresh\",\n    \"p.Heading6 => h6:fresh\",\n    \"p[style-name='Heading 1'] => h1:fresh\",\n    \"p[style-name='Heading 2'] => h2:fresh\",\n    \"p[style-name='Heading 3'] => h3:fresh\",\n    \"p[style-name='Heading 4'] => h4:fresh\",\n    \"p[style-name='Heading 5'] => h5:fresh\",\n    \"p[style-name='Heading 6'] => h6:fresh\",\n    \"p[style-name='heading 1'] => h1:fresh\",\n    \"p[style-name='heading 2'] => h2:fresh\",\n    \"p[style-name='heading 3'] => h3:fresh\",\n    \"p[style-name='heading 4'] => h4:fresh\",\n    \"p[style-name='heading 5'] => h5:fresh\",\n    \"p[style-name='heading 6'] => h6:fresh\",\n    \n    \"r[style-name='Strong'] => strong\",\n    \n    \"p[style-name='footnote text'] => p:fresh\",\n    \"r[style-name='footnote reference'] =>\",\n    \"p[style-name='endnote text'] => p:fresh\",\n    \"r[style-name='endnote reference'] =>\",\n    \"p[style-name='annotation text'] => p:fresh\",\n    \"r[style-name='annotation reference'] =>\",\n    \n    // LibreOffice\n    \"p[style-name='Footnote'] => p:fresh\",\n    \"r[style-name='Footnote anchor'] =>\",\n    \"p[style-name='Endnote'] => p:fresh\",\n    \"r[style-name='Endnote anchor'] =>\",\n    \n    \"p:unordered-list(1) => ul > li:fresh\",\n    \"p:unordered-list(2) => ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(3) => ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:ordered-list(1) => ol > li:fresh\",\n    \"p:ordered-list(2) => ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(3) => ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \n    \"r[style-name='Hyperlink'] =>\",\n    \n    \"p[style-name='Normal'] => p:fresh\"\n];\n\nvar standardOptions = exports._standardOptions = {\n    transformDocument: identity,\n    includeDefaultStyleMap: true,\n    includeEmbeddedStyleMap: true\n};\n\nfunction readOptions(options) {\n    options = options || {};\n    return _.extend({}, standardOptions, options, {\n        customStyleMap: readStyleMap(options.styleMap),\n        readStyleMap: function() {\n            var styleMap = this.customStyleMap;\n            if (this.includeEmbeddedStyleMap) {\n                styleMap = styleMap.concat(readStyleMap(this.embeddedStyleMap));\n            }\n            if (this.includeDefaultStyleMap) {\n                styleMap = styleMap.concat(defaultStyleMap);\n            }\n            return styleMap;\n        }\n    });\n}\n\nfunction readStyleMap(styleMap) {\n    if (!styleMap) {\n        return [];\n    } else if (_.isString(styleMap)) {\n        return styleMap.split(\"\\n\")\n            .map(function(line) {\n                return line.trim();\n            })\n            .filter(function(line) {\n                return line !== \"\" && line.charAt(0) !== \"#\";\n            });\n    } else {\n        return styleMap;\n    }\n}\n\nfunction identity(value) {\n    return value;\n}\n"], "names": [], "mappings": "AAAA,QAAQ,WAAW,GAAG;AAGtB,IAAI;AAEJ,IAAI,kBAAkB,QAAQ,gBAAgB,GAAG;IAC7C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA,cAAc;IACd;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;CACH;AAED,IAAI,kBAAkB,QAAQ,gBAAgB,GAAG;IAC7C,mBAAmB;IACnB,wBAAwB;IACxB,yBAAyB;AAC7B;AAEA,SAAS,YAAY,OAAO;IACxB,UAAU,WAAW,CAAC;IACtB,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,iBAAiB,SAAS;QAC1C,gBAAgB,aAAa,QAAQ,QAAQ;QAC7C,cAAc;YACV,IAAI,WAAW,IAAI,CAAC,cAAc;YAClC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,WAAW,SAAS,MAAM,CAAC,aAAa,IAAI,CAAC,gBAAgB;YACjE;YACA,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,WAAW,SAAS,MAAM,CAAC;YAC/B;YACA,OAAO;QACX;IACJ;AACJ;AAEA,SAAS,aAAa,QAAQ;IAC1B,IAAI,CAAC,UAAU;QACX,OAAO,EAAE;IACb,OAAO,IAAI,EAAE,QAAQ,CAAC,WAAW;QAC7B,OAAO,SAAS,KAAK,CAAC,MACjB,GAAG,CAAC,SAAS,IAAI;YACd,OAAO,KAAK,IAAI;QACpB,GACC,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,SAAS,MAAM,KAAK,MAAM,CAAC,OAAO;QAC7C;IACR,OAAO;QACH,OAAO;IACX;AACJ;AAEA,SAAS,SAAS,KAAK;IACnB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3528, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/browser/unzip.js"], "sourcesContent": ["var promises = require(\"../lib/promises\");\nvar zipfile = require(\"../lib/zipfile\");\n\nexports.openZip = openZip;\n\nfunction openZip(options) {\n    if (options.arrayBuffer) {\n        return promises.resolve(zipfile.openArrayBuffer(options.arrayBuffer));\n    } else {\n        return promises.reject(new Error(\"Could not find file in options\"));\n    }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAEJ,QAAQ,OAAO,GAAG;AAElB,SAAS,QAAQ,OAAO;IACpB,IAAI,QAAQ,WAAW,EAAE;QACrB,OAAO,SAAS,OAAO,CAAC,QAAQ,eAAe,CAAC,QAAQ,WAAW;IACvE,OAAO;QACH,OAAO,SAAS,MAAM,CAAC,IAAI,MAAM;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3543, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/transforms.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nexports.paragraph = paragraph;\nexports.run = run;\nexports._elements = elements;\nexports.getDescendantsOfType = getDescendantsOfType;\nexports.getDescendants = getDescendants;\n\nfunction paragraph(transform) {\n    return elementsOfType(\"paragraph\", transform);\n}\n\nfunction run(transform) {\n    return elementsOfType(\"run\", transform);\n}\n\nfunction elementsOfType(elementType, transform) {\n    return elements(function(element) {\n        if (element.type === elementType) {\n            return transform(element);\n        } else {\n            return element;\n        }\n    });\n}\n\nfunction elements(transform) {\n    return function transformElement(element) {\n        if (element.children) {\n            var children = _.map(element.children, transformElement);\n            element = _.extend(element, {children: children});\n        }\n        return transform(element);\n    };\n}\n\n\nfunction getDescendantsOfType(element, type) {\n    return getDescendants(element).filter(function(descendant) {\n        return descendant.type === type;\n    });\n}\n\nfunction getDescendants(element) {\n    var descendants = [];\n\n    visitDescendants(element, function(descendant) {\n        descendants.push(descendant);\n    });\n\n    return descendants;\n}\n\nfunction visitDescendants(element, visit) {\n    if (element.children) {\n        element.children.forEach(function(child) {\n            visitDescendants(child, visit);\n            visit(child);\n        });\n    }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,QAAQ,SAAS,GAAG;AACpB,QAAQ,GAAG,GAAG;AACd,QAAQ,SAAS,GAAG;AACpB,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,cAAc,GAAG;AAEzB,SAAS,UAAU,SAAS;IACxB,OAAO,eAAe,aAAa;AACvC;AAEA,SAAS,IAAI,SAAS;IAClB,OAAO,eAAe,OAAO;AACjC;AAEA,SAAS,eAAe,WAAW,EAAE,SAAS;IAC1C,OAAO,SAAS,SAAS,OAAO;QAC5B,IAAI,QAAQ,IAAI,KAAK,aAAa;YAC9B,OAAO,UAAU;QACrB,OAAO;YACH,OAAO;QACX;IACJ;AACJ;AAEA,SAAS,SAAS,SAAS;IACvB,OAAO,SAAS,iBAAiB,OAAO;QACpC,IAAI,QAAQ,QAAQ,EAAE;YAClB,IAAI,WAAW,EAAE,GAAG,CAAC,QAAQ,QAAQ,EAAE;YACvC,UAAU,EAAE,MAAM,CAAC,SAAS;gBAAC,UAAU;YAAQ;QACnD;QACA,OAAO,UAAU;IACrB;AACJ;AAGA,SAAS,qBAAqB,OAAO,EAAE,IAAI;IACvC,OAAO,eAAe,SAAS,MAAM,CAAC,SAAS,UAAU;QACrD,OAAO,WAAW,IAAI,KAAK;IAC/B;AACJ;AAEA,SAAS,eAAe,OAAO;IAC3B,IAAI,cAAc,EAAE;IAEpB,iBAAiB,SAAS,SAAS,UAAU;QACzC,YAAY,IAAI,CAAC;IACrB;IAEA,OAAO;AACX;AAEA,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,IAAI,QAAQ,QAAQ,EAAE;QAClB,QAAQ,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;YACnC,iBAAiB,OAAO;YACxB,MAAM;QACV;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/underline.js"], "sourcesContent": ["var htmlPaths = require(\"./styles/html-paths\");\nvar Html = require(\"./html\");\n\n\nexports.element = element;\n\nfunction element(name) {\n    return function(html) {\n        return Html.elementWithTag(htmlPaths.element(name), [html]);\n    };\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AAGJ,QAAQ,OAAO,GAAG;AAElB,SAAS,QAAQ,IAAI;IACjB,OAAO,SAAS,IAAI;QAChB,OAAO,KAAK,cAAc,CAAC,UAAU,OAAO,CAAC,OAAO;YAAC;SAAK;IAC9D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3615, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/mammoth/lib/index.js"], "sourcesContent": ["var _ = require(\"underscore\");\n\nvar docxReader = require(\"./docx/docx-reader\");\nvar docxStyleMap = require(\"./docx/style-map\");\nvar DocumentConverter = require(\"./document-to-html\").DocumentConverter;\nvar convertElementToRawText = require(\"./raw-text\").convertElementToRawText;\nvar readStyle = require(\"./style-reader\").readStyle;\nvar readOptions = require(\"./options-reader\").readOptions;\nvar unzip = require(\"./unzip\");\nvar Result = require(\"./results\").Result;\n\nexports.convertToHtml = convertToHtml;\nexports.convertToMarkdown = convertToMarkdown;\nexports.convert = convert;\nexports.extractRawText = extractRawText;\nexports.images = require(\"./images\");\nexports.transforms = require(\"./transforms\");\nexports.underline = require(\"./underline\");\nexports.embedStyleMap = embedStyleMap;\nexports.readEmbeddedStyleMap = readEmbeddedStyleMap;\n\nfunction convertToHtml(input, options) {\n    return convert(input, options);\n}\n\nfunction convertToMarkdown(input, options) {\n    var markdownOptions = Object.create(options || {});\n    markdownOptions.outputFormat = \"markdown\";\n    return convert(input, markdownOptions);\n}\n\nfunction convert(input, options) {\n    options = readOptions(options);\n\n    return unzip.openZip(input)\n        .tap(function(docxFile) {\n            return docxStyleMap.readStyleMap(docxFile).then(function(styleMap) {\n                options.embeddedStyleMap = styleMap;\n            });\n        })\n        .then(function(docxFile) {\n            return docxReader.read(docxFile, input)\n                .then(function(documentResult) {\n                    return documentResult.map(options.transformDocument);\n                })\n                .then(function(documentResult) {\n                    return convertDocumentToHtml(documentResult, options);\n                });\n        });\n}\n\nfunction readEmbeddedStyleMap(input) {\n    return unzip.openZip(input)\n        .then(docxStyleMap.readStyleMap);\n}\n\nfunction convertDocumentToHtml(documentResult, options) {\n    var styleMapResult = parseStyleMap(options.readStyleMap());\n    var parsedOptions = _.extend({}, options, {\n        styleMap: styleMapResult.value\n    });\n    var documentConverter = new DocumentConverter(parsedOptions);\n\n    return documentResult.flatMapThen(function(document) {\n        return styleMapResult.flatMapThen(function(styleMap) {\n            return documentConverter.convertToHtml(document);\n        });\n    });\n}\n\nfunction parseStyleMap(styleMap) {\n    return Result.combine((styleMap || []).map(readStyle))\n        .map(function(styleMap) {\n            return styleMap.filter(function(styleMapping) {\n                return !!styleMapping;\n            });\n        });\n}\n\n\nfunction extractRawText(input) {\n    return unzip.openZip(input)\n        .then(docxReader.read)\n        .then(function(documentResult) {\n            return documentResult.map(convertElementToRawText);\n        });\n}\n\nfunction embedStyleMap(input, styleMap) {\n    return unzip.openZip(input)\n        .tap(function(docxFile) {\n            return docxStyleMap.writeStyleMap(docxFile, styleMap);\n        })\n        .then(function(docxFile) {\n            return docxFile.toArrayBuffer();\n        })\n        .then(function(arrayBuffer) {\n            return {\n                toArrayBuffer: function() {\n                    return arrayBuffer;\n                },\n                toBuffer: function() {\n                    return Buffer.from(arrayBuffer);\n                }\n            };\n        });\n}\n\nexports.styleMapping = function() {\n    throw new Error('Use a raw string instead of mammoth.styleMapping e.g. \"p[style-name=\\'Title\\'] => h1\" instead of mammoth.styleMapping(\"p[style-name=\\'Title\\'] => h1\")');\n};\n"], "names": [], "mappings": "AAsG2B;AAtG3B,IAAI;AAEJ,IAAI;AACJ,IAAI;AACJ,IAAI,oBAAoB,4GAA8B,iBAAiB;AACvE,IAAI,0BAA0B,oGAAsB,uBAAuB;AAC3E,IAAI,YAAY,wGAA0B,SAAS;AACnD,IAAI,cAAc,0GAA4B,WAAW;AACzD,IAAI;AACJ,IAAI,SAAS,mGAAqB,MAAM;AAExC,QAAQ,aAAa,GAAG;AACxB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,OAAO,GAAG;AAClB,QAAQ,cAAc,GAAG;AACzB,QAAQ,MAAM;AACd,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,aAAa,GAAG;AACxB,QAAQ,oBAAoB,GAAG;AAE/B,SAAS,cAAc,KAAK,EAAE,OAAO;IACjC,OAAO,QAAQ,OAAO;AAC1B;AAEA,SAAS,kBAAkB,KAAK,EAAE,OAAO;IACrC,IAAI,kBAAkB,OAAO,MAAM,CAAC,WAAW,CAAC;IAChD,gBAAgB,YAAY,GAAG;IAC/B,OAAO,QAAQ,OAAO;AAC1B;AAEA,SAAS,QAAQ,KAAK,EAAE,OAAO;IAC3B,UAAU,YAAY;IAEtB,OAAO,MAAM,OAAO,CAAC,OAChB,GAAG,CAAC,SAAS,QAAQ;QAClB,OAAO,aAAa,YAAY,CAAC,UAAU,IAAI,CAAC,SAAS,QAAQ;YAC7D,QAAQ,gBAAgB,GAAG;QAC/B;IACJ,GACC,IAAI,CAAC,SAAS,QAAQ;QACnB,OAAO,WAAW,IAAI,CAAC,UAAU,OAC5B,IAAI,CAAC,SAAS,cAAc;YACzB,OAAO,eAAe,GAAG,CAAC,QAAQ,iBAAiB;QACvD,GACC,IAAI,CAAC,SAAS,cAAc;YACzB,OAAO,sBAAsB,gBAAgB;QACjD;IACR;AACR;AAEA,SAAS,qBAAqB,KAAK;IAC/B,OAAO,MAAM,OAAO,CAAC,OAChB,IAAI,CAAC,aAAa,YAAY;AACvC;AAEA,SAAS,sBAAsB,cAAc,EAAE,OAAO;IAClD,IAAI,iBAAiB,cAAc,QAAQ,YAAY;IACvD,IAAI,gBAAgB,EAAE,MAAM,CAAC,CAAC,GAAG,SAAS;QACtC,UAAU,eAAe,KAAK;IAClC;IACA,IAAI,oBAAoB,IAAI,kBAAkB;IAE9C,OAAO,eAAe,WAAW,CAAC,SAAS,QAAQ;QAC/C,OAAO,eAAe,WAAW,CAAC,SAAS,QAAQ;YAC/C,OAAO,kBAAkB,aAAa,CAAC;QAC3C;IACJ;AACJ;AAEA,SAAS,cAAc,QAAQ;IAC3B,OAAO,OAAO,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,YACtC,GAAG,CAAC,SAAS,QAAQ;QAClB,OAAO,SAAS,MAAM,CAAC,SAAS,YAAY;YACxC,OAAO,CAAC,CAAC;QACb;IACJ;AACR;AAGA,SAAS,eAAe,KAAK;IACzB,OAAO,MAAM,OAAO,CAAC,OAChB,IAAI,CAAC,WAAW,IAAI,EACpB,IAAI,CAAC,SAAS,cAAc;QACzB,OAAO,eAAe,GAAG,CAAC;IAC9B;AACR;AAEA,SAAS,cAAc,KAAK,EAAE,QAAQ;IAClC,OAAO,MAAM,OAAO,CAAC,OAChB,GAAG,CAAC,SAAS,QAAQ;QAClB,OAAO,aAAa,aAAa,CAAC,UAAU;IAChD,GACC,IAAI,CAAC,SAAS,QAAQ;QACnB,OAAO,SAAS,aAAa;IACjC,GACC,IAAI,CAAC,SAAS,WAAW;QACtB,OAAO;YACH,eAAe;gBACX,OAAO;YACX;YACA,UAAU;gBACN,OAAO,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC;YACvB;QACJ;IACJ;AACR;AAEA,QAAQ,YAAY,GAAG;IACnB,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0], "debugId": null}}]}