const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixStuckSegment() {
  try {
    // 更新第一个片段的状态为失败
    const result = await prisma.videoSegment.update({
      where: { id: 'cmcd1zlh40002vmsor9hkgpr5' },
      data: {
        status: 'failed',
        updatedAt: new Date()
      }
    });
    
    console.log('✅ 已将卡住的片段状态更新为失败');
    console.log('片段ID:', result.id);
    console.log('新状态:', result.status);
    console.log('更新时间:', result.updatedAt);
  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixStuckSegment();
