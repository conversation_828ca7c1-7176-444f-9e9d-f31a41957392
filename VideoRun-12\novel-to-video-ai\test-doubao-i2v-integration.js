const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoI2VIntegration() {
  try {
    console.log('🎬 测试豆包图生视频模型集成...\n');
    
    // 1. 检查新模型是否已添加到数据库
    console.log('📋 检查豆包图生视频模型配置...');
    
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj'
      }
    });
    
    if (i2vModel) {
      console.log('✅ 找到豆包图生视频模型配置:');
      console.log(`   ID: ${i2vModel.id}`);
      console.log(`   名称: ${i2vModel.name}`);
      console.log(`   模型: ${i2vModel.model}`);
      console.log(`   启用状态: ${i2vModel.enabled}`);
      console.log(`   支持视频: ${i2vModel.supportsVideo}`);
      console.log(`   支持图生视频: ${i2vModel.supportsImageToVideo}`);
      console.log(`   API密钥: ${i2vModel.apiKey ? '已配置' : '未配置'}`);
    } else {
      console.log('❌ 未找到豆包图生视频模型配置');
      console.log('💡 请在AI配置页面添加该模型');
      return;
    }
    
    // 2. 检查是否有可用的角色参考图像
    console.log('\n🖼️ 检查角色参考图像...');
    
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 3
    });
    
    console.log(`📊 找到 ${charactersWithImages.length} 个有参考图像的角色:`);
    
    for (const character of charactersWithImages) {
      console.log(`   - ${character.name}: ${character.generatedImages ? '有图像' : '无图像'}`);
      
      if (character.generatedImages) {
        try {
          const images = JSON.parse(character.generatedImages);
          console.log(`     正面图: ${images.front ? '有' : '无'}`);
          console.log(`     侧面图: ${images.side ? '有' : '无'}`);
          console.log(`     背面图: ${images.back ? '有' : '无'}`);
        } catch (e) {
          console.log(`     图像数据解析失败: ${e.message}`);
        }
      }
    }
    
    if (charactersWithImages.length === 0) {
      console.log('⚠️ 没有找到有参考图像的角色');
      console.log('💡 图生视频需要角色参考图像，请先生成角色图像');
      return;
    }
    
    // 3. 检查现有剧集
    console.log('\n📖 检查可用剧集...');
    
    const episode = await prisma.episode.findFirst({
      where: { title: '雪夜初逢' }
    });
    
    if (!episode) {
      console.log('❌ 未找到"雪夜初逢"剧集');
      return;
    }
    
    console.log(`✅ 找到剧集: ${episode.title}`);
    
    // 4. 模拟图生视频API调用测试
    if (i2vModel.apiKey && i2vModel.enabled) {
      console.log('\n🧪 测试图生视频API调用...');
      
      const testData = {
        episodeId: episode.id,
        plotInfo: {
          generatedPrompt: '李四在雪夜中巡逻，发现血人，表情严肃警惕。'
        },
        forceModel: i2vModel.id // 强制使用图生视频模型
      };
      
      try {
        const response = await fetch('http://localhost:3000/api/ai/generate-story-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testData)
        });
        
        console.log(`API响应状态: ${response.status}`);
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ 图生视频API调用成功!');
          console.log(`📋 故事视频ID: ${result.storyVideoId}`);
          
          // 等待一段时间检查生成状态
          console.log('\n⏳ 等待片段生成...');
          await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
          
          const segments = await prisma.videoSegment.findMany({
            where: { storyVideoId: result.storyVideoId },
            orderBy: { createdAt: 'asc' }
          });
          
          console.log(`📊 生成的片段数: ${segments.length}`);
          
          for (const segment of segments) {
            console.log(`\n🎬 片段: ${segment.title.substring(0, 50)}...`);
            console.log(`   状态: ${segment.status}`);
            console.log(`   时长: ${segment.duration}秒`);
            
            if (segment.metadata) {
              try {
                const metadata = JSON.parse(segment.metadata);
                console.log(`   提供商: ${metadata.provider || '未知'}`);
                console.log(`   任务ID: ${metadata.taskId || '无'}`);
                console.log(`   输入图像: ${metadata.inputImage ? '有' : '无'}`);
              } catch (e) {
                console.log(`   元数据解析失败: ${e.message}`);
              }
            }
          }
          
        } else {
          const errorText = await response.text();
          console.error(`❌ API调用失败: ${errorText.substring(0, 500)}...`);
        }
        
      } catch (error) {
        console.error('❌ API调用异常:', error.message);
      }
      
    } else {
      console.log('⚠️ 模型未启用或API密钥未配置，跳过API测试');
    }
    
    // 5. 对比分析：文生视频 vs 图生视频
    console.log('\n📊 文生视频 vs 图生视频对比分析:');
    
    const t2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250622184757-q77k7'
      }
    });
    
    console.log('\n📋 模型对比:');
    console.log('文生视频 (Seedance 1.0 Pro):');
    console.log(`   模型ID: ${t2vModel?.model || '未配置'}`);
    console.log(`   启用状态: ${t2vModel?.enabled || false}`);
    console.log(`   输入要求: 文本提示词`);
    console.log(`   优势: 创意自由度高，无需预设图像`);
    console.log(`   劣势: 角色一致性难以保证`);
    
    console.log('\n图生视频 (Seedance 1.0 Lite I2V):');
    console.log(`   模型ID: ${i2vModel.model}`);
    console.log(`   启用状态: ${i2vModel.enabled}`);
    console.log(`   输入要求: 参考图像 + 文本提示词`);
    console.log(`   优势: 角色一致性更好，基于已有形象`);
    console.log(`   劣势: 需要预先生成角色图像`);
    
    // 6. 使用建议
    console.log('\n💡 使用建议:');
    console.log('🎯 推荐使用场景:');
    console.log('   - 图生视频: 角色特写、表情变化、已定型角色的动作');
    console.log('   - 文生视频: 环境建立、新场景探索、创意性内容');
    
    console.log('\n🔄 混合使用策略:');
    console.log('   1. 环境建立片段: 使用文生视频');
    console.log('   2. 角色登场片段: 使用图生视频');
    console.log('   3. 动作场景片段: 根据复杂度选择');
    console.log('   4. 情感表达片段: 优先使用图生视频');
    
    console.log('\n🚀 优化建议:');
    console.log('   - 为主要角色生成多角度参考图像');
    console.log('   - 根据片段类型智能选择模型');
    console.log('   - 建立角色图像库管理系统');
    console.log('   - 实现模型自动切换逻辑');
    
    // 7. 成本分析
    console.log('\n💰 成本对比分析:');
    console.log('文生视频成本: ¥3.67/5秒片段');
    console.log('图生视频成本: 待确认 (通常略高于文生视频)');
    console.log('混合使用策略: 可能增加10-20%成本，但显著提升质量');
    
    console.log('\n🎬 豆包图生视频模型集成测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoI2VIntegration();
