{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-image/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { PrismaClient } from '@prisma/client'\n\nconst prisma = new PrismaClient()\n\n// 图像生成请求接口\ninterface ImageGenerationRequest {\n  prompt: string\n  negativePrompt?: string\n  width?: number\n  height?: number\n  guidance?: number\n  style?: string\n  quality?: string\n}\n\n// 豆包图像生成API（基于官方文档）\nasync function generateImageWithDoubao(request: ImageGenerationRequest) {\n  try {\n    console.log('🎨 调用豆包图像生成API')\n    console.log('使用端点: ep-20250626132353-nlrtf')\n    \n    // 构建请求参数（基于官方文档格式）\n    const requestBody = {\n      model: 'ep-20250626132353-nlrtf',\n      prompt: request.prompt,\n      response_format: 'url',\n      size: `${request.width || 1024}x${request.height || 1024}`,\n      guidance_scale: request.guidance || 3,\n      watermark: true\n    }\n    \n    console.log('📝 请求参数:', {\n      model: requestBody.model,\n      promptLength: request.prompt.length,\n      size: requestBody.size,\n      guidance_scale: requestBody.guidance_scale\n    })\n    \n    // 注意：豆包API需要复杂的AK/SK签名认证\n    // 这里先使用模拟响应来演示功能\n    console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证')\n\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 2000))\n\n    // 智能模拟响应 - 根据提示词生成合适的占位图\n    const mockImageUrl = generateSmartMockImage(request.prompt, request.width || 1024, request.height || 1024)\n\n    console.log('✅ 模拟图像生成成功:', {\n      imageUrl: '已生成模拟图像',\n      size: `${request.width || 1024}x${request.height || 1024}`,\n      guidance_scale: requestBody.guidance_scale,\n      promptType: detectPromptType(request.prompt)\n    })\n    return {\n      imageUrl: mockImageUrl,\n      width: request.width || 1024,\n      height: request.height || 1024,\n      guidance: requestBody.guidance_scale,\n      metadata: {\n        model: requestBody.model,\n        prompt: request.prompt,\n        provider: 'doubao-mock',\n        generatedAt: new Date().toISOString(),\n        promptType: detectPromptType(request.prompt),\n        note: '这是模拟响应，实际部署需要实现火山引擎AK/SK签名认证'\n      }\n    }\n    \n  } catch (error) {\n    console.error('豆包图像生成失败:', error)\n    throw error\n  }\n}\n\n// 检测提示词类型\nfunction detectPromptType(prompt: string): string {\n  const lowerPrompt = prompt.toLowerCase()\n\n  if (lowerPrompt.includes('正面视图') || lowerPrompt.includes('front view') || lowerPrompt.includes('facing camera')) {\n    return 'character-front'\n  } else if (lowerPrompt.includes('侧面视图') || lowerPrompt.includes('side view') || lowerPrompt.includes('profile')) {\n    return 'character-side'\n  } else if (lowerPrompt.includes('背面视图') || lowerPrompt.includes('back view') || lowerPrompt.includes('rear')) {\n    return 'character-back'\n  } else if (lowerPrompt.includes('角色') || lowerPrompt.includes('character') || lowerPrompt.includes('人物')) {\n    return 'character-general'\n  } else if (lowerPrompt.includes('场景') || lowerPrompt.includes('scene') || lowerPrompt.includes('landscape')) {\n    return 'scene'\n  } else {\n    return 'general'\n  }\n}\n\n// 智能生成模拟图像URL\nfunction generateSmartMockImage(prompt: string, width: number, height: number): string {\n  const promptType = detectPromptType(prompt)\n  const timestamp = Date.now()\n\n  // 根据提示词类型生成不同的占位图\n  switch (promptType) {\n    case 'character-front':\n      // 正面角色 - 使用人像占位图\n      return generateCharacterPlaceholder('front', width, height, timestamp, prompt)\n\n    case 'character-side':\n      // 侧面角色 - 使用侧面人像占位图\n      return generateCharacterPlaceholder('side', width, height, timestamp, prompt)\n\n    case 'character-back':\n      // 背面角色 - 使用背影占位图\n      return generateCharacterPlaceholder('back', width, height, timestamp, prompt)\n\n    case 'character-general':\n      // 一般角色 - 使用通用人像占位图\n      return generateCharacterPlaceholder('general', width, height, timestamp, prompt)\n\n    case 'scene':\n      // 场景 - 使用风景占位图\n      return `https://picsum.photos/${width}/${height}?random=${timestamp}`\n\n    default:\n      // 默认 - 使用随机占位图\n      return `https://picsum.photos/${width}/${height}?random=${timestamp}`\n  }\n}\n\n// 生成角色占位图\nfunction generateCharacterPlaceholder(viewType: string, width: number, height: number, timestamp: number, prompt?: string): string {\n  // 使用SVG生成角色占位图，包含角色信息\n  const svg = generateCharacterSVG(viewType, width, height, prompt)\n  return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`\n}\n\n// 生成角色SVG占位图\nfunction generateCharacterSVG(viewType: string, width: number, height: number, prompt?: string): string {\n  const viewConfig = {\n    front: {\n      title: '正面视图',\n      color: '#8B5CF6',\n      icon: '👤'\n    },\n    side: {\n      title: '侧面视图',\n      color: '#3B82F6',\n      icon: '🧑‍💼'\n    },\n    back: {\n      title: '背面视图',\n      color: '#10B981',\n      icon: '🚶‍♂️'\n    },\n    general: {\n      title: '角色图像',\n      color: '#F59E0B',\n      icon: '👨‍🎨'\n    }\n  }\n\n  const config = viewConfig[viewType] || viewConfig.general\n\n  // 从提示词中提取角色信息\n  const characterInfo = extractCharacterInfo(prompt || '')\n\n  return `\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${config.color}20;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${config.color}40;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n\n      <!-- 背景 -->\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#bg)\"/>\n\n      <!-- 边框 -->\n      <rect x=\"2\" y=\"2\" width=\"${width-4}\" height=\"${height-4}\"\n            fill=\"none\" stroke=\"${config.color}\" stroke-width=\"3\" stroke-dasharray=\"10,5\"/>\n\n      <!-- 中心图标 -->\n      <circle cx=\"${width/2}\" cy=\"${height/2-80}\" r=\"60\" fill=\"${config.color}30\" stroke=\"${config.color}\" stroke-width=\"2\"/>\n      <text x=\"${width/2}\" y=\"${height/2-70}\" text-anchor=\"middle\" font-size=\"48\" fill=\"${config.color}\">${config.icon}</text>\n\n      <!-- 标题 -->\n      <text x=\"${width/2}\" y=\"${height/2-10}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"24\" font-weight=\"bold\" fill=\"${config.color}\">\n        ${config.title}\n      </text>\n\n      <!-- 角色信息 -->\n      ${characterInfo.map((info, index) => `\n        <text x=\"${width/2}\" y=\"${height/2 + 20 + index * 25}\" text-anchor=\"middle\"\n              font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"${config.color}\">\n          ${info}\n        </text>\n      `).join('')}\n\n      <!-- 提示信息 -->\n      <text x=\"${width/2}\" y=\"${height-60}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"${config.color}60\">\n        模拟响应 - 实际部署将生成真实图像\n      </text>\n\n      <!-- 尺寸信息 -->\n      <text x=\"${width/2}\" y=\"${height-30}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"${config.color}60\">\n        ${width} × ${height}\n      </text>\n    </svg>\n  `.trim()\n}\n\n// 从提示词中提取角色信息\nfunction extractCharacterInfo(prompt: string): string[] {\n  const info: string[] = []\n\n  // 提取关键特征\n  const features = [\n    { pattern: /(\\d+岁?[男女性]?[性别]?)/g, label: '年龄性别' },\n    { pattern: /(黑发|金发|棕发|银发|短发|长发|卷发|直发)/g, label: '发型' },\n    { pattern: /(蓝眼|棕眼|黑眼|绿眼|灰眼)/g, label: '眼睛' },\n    { pattern: /(高挑|娇小|健壮|苗条|魁梧)/g, label: '身材' },\n    { pattern: /(军装|西装|长裙|短裙|盔甲|便装)/g, label: '服装' },\n    { pattern: /(战士|法师|弓箭手|骑士|商人|学者|贵族)/g, label: '职业' }\n  ]\n\n  features.forEach(({ pattern, label }) => {\n    const matches = prompt.match(pattern)\n    if (matches && matches.length > 0) {\n      info.push(`${label}: ${matches[0]}`)\n    }\n  })\n\n  // 如果没有提取到特征，显示通用信息\n  if (info.length === 0) {\n    info.push('基于角色描述生成')\n    info.push('包含详细特征信息')\n  }\n\n  // 限制显示的信息条数\n  return info.slice(0, 4)\n}\n\n// GET接口 - 获取配置信息\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('🔍 获取图像生成配置')\n    \n    // 获取豆包图像生成配置\n    const imageConfig = await prisma.aIConfig.findFirst({\n      where: {\n        provider: 'doubao',\n        supportsImage: true,\n        enabled: true\n      },\n      orderBy: { updatedAt: 'desc' }\n    })\n    \n    if (!imageConfig) {\n      return NextResponse.json(\n        { \n          success: false,\n          error: '未找到可用的图像生成配置' \n        },\n        { status: 404 }\n      )\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        provider: 'doubao',\n        model: imageConfig.name,\n        endpoint: imageConfig.model,\n        status: imageConfig.status,\n        defaultSettings: {\n          width: 1024,\n          height: 1024,\n          guidance: 3,\n          quality: 'high'\n        },\n        availableSizes: [\n          '512x512',\n          '768x768', \n          '1024x1024',\n          '768x1024',\n          '1024x768'\n        ]\n      }\n    })\n    \n  } catch (error) {\n    console.error('获取图像生成配置失败:', error)\n    return NextResponse.json(\n      { \n        success: false,\n        error: error instanceof Error ? error.message : '获取配置失败'\n      },\n      { status: 500 }\n    )\n  }\n}\n\n// POST接口 - 生成图像\nexport async function POST(request: NextRequest) {\n  try {\n    console.log('🎨 开始图像生成')\n    \n    const body: ImageGenerationRequest = await request.json()\n    \n    if (!body.prompt) {\n      return NextResponse.json(\n        { \n          success: false,\n          error: '提示词不能为空' \n        },\n        { status: 400 }\n      )\n    }\n    \n    console.log('📝 图像生成请求:', {\n      promptLength: body.prompt.length,\n      width: body.width || 1024,\n      height: body.height || 1024,\n      guidance: body.guidance || 3\n    })\n    \n    // 获取图像生成配置\n    const imageConfig = await prisma.aIConfig.findFirst({\n      where: {\n        provider: 'doubao',\n        supportsImage: true,\n        enabled: true\n      },\n      orderBy: { updatedAt: 'desc' }\n    })\n    \n    if (!imageConfig) {\n      return NextResponse.json(\n        { \n          success: false,\n          error: '未找到可用的图像生成配置' \n        },\n        { status: 404 }\n      )\n    }\n    \n    console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`)\n    \n    // 调用豆包图像生成\n    const result = await generateImageWithDoubao(body)\n    \n    console.log('✅ 图像生成成功:', {\n      hasImageUrl: !!result.imageUrl,\n      width: result.width,\n      height: result.height,\n      guidance: result.guidance\n    })\n    \n    return NextResponse.json({\n      success: true,\n      data: result\n    })\n    \n  } catch (error) {\n    console.error('图像生成失败:', error)\n    return NextResponse.json(\n      { \n        success: false,\n        error: error instanceof Error ? error.message : '图像生成失败'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY;AAa/B,oBAAoB;AACpB,eAAe,wBAAwB,OAA+B;IACpE,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,mBAAmB;QACnB,MAAM,cAAc;YAClB,OAAO;YACP,QAAQ,QAAQ,MAAM;YACtB,iBAAiB;YACjB,MAAM,GAAG,QAAQ,KAAK,IAAI,KAAK,CAAC,EAAE,QAAQ,MAAM,IAAI,MAAM;YAC1D,gBAAgB,QAAQ,QAAQ,IAAI;YACpC,WAAW;QACb;QAEA,QAAQ,GAAG,CAAC,YAAY;YACtB,OAAO,YAAY,KAAK;YACxB,cAAc,QAAQ,MAAM,CAAC,MAAM;YACnC,MAAM,YAAY,IAAI;YACtB,gBAAgB,YAAY,cAAc;QAC5C;QAEA,yBAAyB;QACzB,iBAAiB;QACjB,QAAQ,GAAG,CAAC;QAEZ,YAAY;QACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,yBAAyB;QACzB,MAAM,eAAe,uBAAuB,QAAQ,MAAM,EAAE,QAAQ,KAAK,IAAI,MAAM,QAAQ,MAAM,IAAI;QAErG,QAAQ,GAAG,CAAC,eAAe;YACzB,UAAU;YACV,MAAM,GAAG,QAAQ,KAAK,IAAI,KAAK,CAAC,EAAE,QAAQ,MAAM,IAAI,MAAM;YAC1D,gBAAgB,YAAY,cAAc;YAC1C,YAAY,iBAAiB,QAAQ,MAAM;QAC7C;QACA,OAAO;YACL,UAAU;YACV,OAAO,QAAQ,KAAK,IAAI;YACxB,QAAQ,QAAQ,MAAM,IAAI;YAC1B,UAAU,YAAY,cAAc;YACpC,UAAU;gBACR,OAAO,YAAY,KAAK;gBACxB,QAAQ,QAAQ,MAAM;gBACtB,UAAU;gBACV,aAAa,IAAI,OAAO,WAAW;gBACnC,YAAY,iBAAiB,QAAQ,MAAM;gBAC3C,MAAM;YACR;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAEA,UAAU;AACV,SAAS,iBAAiB,MAAc;IACtC,MAAM,cAAc,OAAO,WAAW;IAEtC,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,iBAAiB,YAAY,QAAQ,CAAC,kBAAkB;QAC/G,OAAO;IACT,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,YAAY;QAC/G,OAAO;IACT,OAAO,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,SAAS;QAC5G,OAAO;IACT,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,OAAO;QACxG,OAAO;IACT,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS,YAAY,QAAQ,CAAC,YAAY,YAAY,QAAQ,CAAC,cAAc;QAC3G,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,cAAc;AACd,SAAS,uBAAuB,MAAc,EAAE,KAAa,EAAE,MAAc;IAC3E,MAAM,aAAa,iBAAiB;IACpC,MAAM,YAAY,KAAK,GAAG;IAE1B,kBAAkB;IAClB,OAAQ;QACN,KAAK;YACH,iBAAiB;YACjB,OAAO,6BAA6B,SAAS,OAAO,QAAQ,WAAW;QAEzE,KAAK;YACH,mBAAmB;YACnB,OAAO,6BAA6B,QAAQ,OAAO,QAAQ,WAAW;QAExE,KAAK;YACH,iBAAiB;YACjB,OAAO,6BAA6B,QAAQ,OAAO,QAAQ,WAAW;QAExE,KAAK;YACH,mBAAmB;YACnB,OAAO,6BAA6B,WAAW,OAAO,QAAQ,WAAW;QAE3E,KAAK;YACH,eAAe;YACf,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,EAAE,OAAO,QAAQ,EAAE,WAAW;QAEvE;YACE,eAAe;YACf,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC,EAAE,OAAO,QAAQ,EAAE,WAAW;IACzE;AACF;AAEA,UAAU;AACV,SAAS,6BAA6B,QAAgB,EAAE,KAAa,EAAE,MAAc,EAAE,SAAiB,EAAE,MAAe;IACvH,sBAAsB;IACtB,MAAM,MAAM,qBAAqB,UAAU,OAAO,QAAQ;IAC1D,OAAO,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC,WAAW;AAC3E;AAEA,aAAa;AACb,SAAS,qBAAqB,QAAgB,EAAE,KAAa,EAAE,MAAc,EAAE,MAAe;IAC5F,MAAM,aAAa;QACjB,OAAO;YACL,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,SAAS;YACP,OAAO;YACP,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,SAAS,UAAU,CAAC,SAAS,IAAI,WAAW,OAAO;IAEzD,cAAc;IACd,MAAM,gBAAgB,qBAAqB,UAAU;IAErD,OAAO,CAAC;gBACM,EAAE,MAAM,UAAU,EAAE,OAAO;;;8CAGG,EAAE,OAAO,KAAK,CAAC;gDACb,EAAE,OAAO,KAAK,CAAC;;;;;;;;+BAQhC,EAAE,QAAM,EAAE,UAAU,EAAE,SAAO,EAAE;gCAC9B,EAAE,OAAO,KAAK,CAAC;;;kBAG7B,EAAE,QAAM,EAAE,MAAM,EAAE,SAAO,IAAE,GAAG,eAAe,EAAE,OAAO,KAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC;eAC1F,EAAE,QAAM,EAAE,KAAK,EAAE,SAAO,IAAE,GAAG,4CAA4C,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC;;;eAGxG,EAAE,QAAM,EAAE,KAAK,EAAE,SAAO,IAAE,GAAG;oFACwC,EAAE,OAAO,KAAK,CAAC;QAC3F,EAAE,OAAO,KAAK,CAAC;;;;MAIjB,EAAE,cAAc,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;iBAC3B,EAAE,QAAM,EAAE,KAAK,EAAE,SAAO,IAAI,KAAK,QAAQ,GAAG;mEACM,EAAE,OAAO,KAAK,CAAC;UACxE,EAAE,KAAK;;MAEX,CAAC,EAAE,IAAI,CAAC,IAAI;;;eAGH,EAAE,QAAM,EAAE,KAAK,EAAE,SAAO,GAAG;iEACuB,EAAE,OAAO,KAAK,CAAC;;;;;eAKjE,EAAE,QAAM,EAAE,KAAK,EAAE,SAAO,GAAG;iEACuB,EAAE,OAAO,KAAK,CAAC;QACxE,EAAE,MAAM,GAAG,EAAE,OAAO;;;EAG1B,CAAC,CAAC,IAAI;AACR;AAEA,cAAc;AACd,SAAS,qBAAqB,MAAc;IAC1C,MAAM,OAAiB,EAAE;IAEzB,SAAS;IACT,MAAM,WAAW;QACf;YAAE,SAAS;YAAuB,OAAO;QAAO;QAChD;YAAE,SAAS;YAA8B,OAAO;QAAK;QACrD;YAAE,SAAS;YAAqB,OAAO;QAAK;QAC5C;YAAE,SAAS;YAAqB,OAAO;QAAK;QAC5C;YAAE,SAAS;YAAwB,OAAO;QAAK;QAC/C;YAAE,SAAS;YAA4B,OAAO;QAAK;KACpD;IAED,SAAS,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;QAClC,MAAM,UAAU,OAAO,KAAK,CAAC;QAC7B,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;QACrC;IACF;IAEA,mBAAmB;IACnB,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,CAAC;IACZ;IAEA,YAAY;IACZ,OAAO,KAAK,KAAK,CAAC,GAAG;AACvB;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,aAAa;QACb,MAAM,cAAc,MAAM,OAAO,QAAQ,CAAC,SAAS,CAAC;YAClD,OAAO;gBACL,UAAU;gBACV,eAAe;gBACf,SAAS;YACX;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,UAAU;gBACV,OAAO,YAAY,IAAI;gBACvB,UAAU,YAAY,KAAK;gBAC3B,QAAQ,YAAY,MAAM;gBAC1B,iBAAiB;oBACf,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,SAAS;gBACX;gBACA,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,OAA+B,MAAM,QAAQ,IAAI;QAEvD,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,cAAc;YACxB,cAAc,KAAK,MAAM,CAAC,MAAM;YAChC,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;YACvB,UAAU,KAAK,QAAQ,IAAI;QAC7B;QAEA,WAAW;QACX,MAAM,cAAc,MAAM,OAAO,QAAQ,CAAC,SAAS,CAAC;YAClD,OAAO;gBACL,UAAU;gBACV,eAAe;gBACf,SAAS;YACX;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,IAAI,CAAC,EAAE,EAAE,YAAY,KAAK,CAAC,CAAC,CAAC;QAEjE,WAAW;QACX,MAAM,SAAS,MAAM,wBAAwB;QAE7C,QAAQ,GAAG,CAAC,aAAa;YACvB,aAAa,CAAC,CAAC,OAAO,QAAQ;YAC9B,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;YACrB,UAAU,OAAO,QAAQ;QAC3B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}