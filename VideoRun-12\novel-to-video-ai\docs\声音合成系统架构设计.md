# 声音合成系统架构设计

## 📋 概述

本文档详细描述了小说剧情视频生成系统中声音合成功能的完整架构设计，包括TTS服务集成、角色声音配置、音频处理和视频音频同步等核心模块。

## 🎯 设计目标

1. **多模态TTS集成**：支持豆包、阿里云、微软Azure等多种TTS服务
2. **角色声音一致性**：为每个角色配置专属声音特征
3. **智能对话提取**：从剧情文本中自动识别对话和旁白
4. **音画同步**：确保生成的音频与视频片段完美同步
5. **情感表达**：根据剧情情感调整语音参数

## 🏗️ 系统架构

### 核心模块设计

```
声音合成系统
├── TTS服务管理层
│   ├── 豆包TTS集成
│   ├── 阿里云语音合成
│   ├── 微软Azure Speech
│   └── 本地TTS方案
├── 角色声音配置层
│   ├── 声音映射管理
│   ├── 情感参数配置
│   └── 声音一致性验证
├── 文本处理层
│   ├── 对话提取器
│   ├── 旁白识别器
│   └── 情感分析器
├── 音频处理层
│   ├── 语音合成引擎
│   ├── 音频后处理
│   └── 背景音乐集成
└── 同步合并层
    ├── 音视频同步
    ├── 时长匹配
    └── 质量优化
```

## 🔧 技术实现

### 1. TTS服务接口设计

```typescript
// TTS服务基础接口
interface TTSService {
  provider: 'doubao' | 'aliyun' | 'azure' | 'local'
  apiKey: string
  region?: string
  
  // 核心方法
  generateSpeech(request: SpeechRequest): Promise<AudioResult>
  listVoices(): Promise<Voice[]>
  validateConfig(): Promise<boolean>
}

// 语音生成请求
interface SpeechRequest {
  text: string
  voiceId: string
  pitch: number        // 音调 (-20 to +20)
  speed: number        // 语速 (0.5 to 2.0)
  emotion: EmotionType // 情感类型
  volume: number       // 音量 (0 to 100)
  format: 'mp3' | 'wav' | 'ogg'
}

// 语音生成结果
interface AudioResult {
  audioUrl: string
  duration: number
  format: string
  size: number
  metadata?: AudioMetadata
}
```

### 2. 角色声音配置系统

```typescript
// 角色声音配置
interface CharacterVoice {
  id: string
  characterId: string
  characterName: string
  
  // 基础声音配置
  voiceId: string      // TTS服务中的声音ID
  provider: string     // TTS服务提供商
  
  // 声音特征参数
  basePitch: number    // 基础音调
  baseSpeed: number    // 基础语速
  baseVolume: number   // 基础音量
  
  // 情感映射
  emotionMappings: {
    [emotion: string]: {
      pitchAdjust: number
      speedAdjust: number
      volumeAdjust: number
    }
  }
  
  // 示例音频
  sampleAudioUrl?: string
  
  createdAt: Date
  updatedAt: Date
}

// 情感类型定义
type EmotionType = 
  | 'neutral'    // 中性
  | 'happy'      // 高兴
  | 'sad'        // 悲伤
  | 'angry'      // 愤怒
  | 'excited'    // 兴奋
  | 'worried'    // 担心
  | 'surprised'  // 惊讶
  | 'gentle'     // 温柔
```

### 3. 文本处理与对话提取

```typescript
// 对话片段
interface DialogueSegment {
  id: string
  type: 'dialogue' | 'narration' | 'thought'
  text: string
  characterId?: string
  characterName?: string
  emotion: EmotionType
  startTime: number    // 在视频中的开始时间(秒)
  duration: number     // 持续时间(秒)
  segmentIndex: number // 对应的视频片段索引
}

// 文本处理器
interface TextProcessor {
  extractDialogues(plotText: string): DialogueSegment[]
  analyzeEmotion(text: string, context?: string): EmotionType
  splitByCharacter(dialogues: DialogueSegment[]): Map<string, DialogueSegment[]>
  calculateTiming(dialogues: DialogueSegment[], videoDuration: number): DialogueSegment[]
}
```

### 4. 音频处理管理器

```typescript
// 音频管理器
interface AudioManager {
  // 语音合成
  generateCharacterSpeech(
    dialogue: DialogueSegment, 
    voiceConfig: CharacterVoice
  ): Promise<AudioResult>
  
  // 批量生成
  generateBatchSpeech(
    dialogues: DialogueSegment[], 
    voiceConfigs: Map<string, CharacterVoice>
  ): Promise<AudioResult[]>
  
  // 音频后处理
  enhanceAudio(audioPath: string, options: AudioEnhanceOptions): Promise<string>
  
  // 背景音乐
  addBackgroundMusic(
    speechAudio: string, 
    mood: string, 
    duration: number
  ): Promise<string>
  
  // 音效添加
  addSoundEffects(
    audioPath: string, 
    effects: SoundEffect[]
  ): Promise<string>
}

// 音频增强选项
interface AudioEnhanceOptions {
  noiseReduction: boolean
  volumeNormalization: boolean
  fadeInOut: boolean
  compressionLevel: number
}
```

## 📊 数据库设计

### 新增表结构

```sql
-- TTS服务配置表
CREATE TABLE tts_configs (
  id VARCHAR(50) PRIMARY KEY,
  provider VARCHAR(20) NOT NULL,
  name VARCHAR(100) NOT NULL,
  api_key TEXT NOT NULL,
  region VARCHAR(50),
  endpoint_url TEXT,
  enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色声音配置表
CREATE TABLE character_voices (
  id VARCHAR(50) PRIMARY KEY,
  character_id VARCHAR(50) NOT NULL,
  tts_config_id VARCHAR(50) NOT NULL,
  voice_id VARCHAR(100) NOT NULL,
  voice_name VARCHAR(100),
  base_pitch INTEGER DEFAULT 0,
  base_speed DECIMAL(3,2) DEFAULT 1.0,
  base_volume INTEGER DEFAULT 80,
  emotion_mappings JSON,
  sample_audio_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (character_id) REFERENCES characters(id),
  FOREIGN KEY (tts_config_id) REFERENCES tts_configs(id)
);

-- 音频文件表
CREATE TABLE audio_files (
  id VARCHAR(50) PRIMARY KEY,
  episode_id VARCHAR(50) NOT NULL,
  segment_index INTEGER NOT NULL,
  dialogue_type VARCHAR(20) NOT NULL, -- 'dialogue', 'narration', 'thought'
  character_id VARCHAR(50),
  text TEXT NOT NULL,
  emotion VARCHAR(20) NOT NULL,
  audio_url TEXT,
  duration DECIMAL(5,2),
  file_size INTEGER,
  format VARCHAR(10),
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (episode_id) REFERENCES episodes(id),
  FOREIGN KEY (character_id) REFERENCES characters(id)
);
```

## 🔄 工作流程

### 1. 声音合成流程

```mermaid
graph TD
    A[剧情文本] --> B[对话提取]
    B --> C[角色识别]
    C --> D[情感分析]
    D --> E[声音配置匹配]
    E --> F[TTS服务调用]
    F --> G[音频后处理]
    G --> H[背景音乐添加]
    H --> I[与视频同步]
    I --> J[最终音频输出]
```

### 2. 角色声音配置流程

```mermaid
graph TD
    A[角色创建] --> B[选择TTS服务]
    B --> C[选择基础声音]
    C --> D[调整声音参数]
    D --> E[配置情感映射]
    E --> F[生成示例音频]
    F --> G[用户确认]
    G --> H[保存配置]
```

## 🎵 TTS服务集成方案

### 1. 豆包TTS集成

```typescript
class DoubaoTTSService implements TTSService {
  provider = 'doubao' as const
  
  async generateSpeech(request: SpeechRequest): Promise<AudioResult> {
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/tts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'doubao-tts-v1',
        input: {
          text: request.text
        },
        voice: request.voiceId,
        response_format: request.format,
        speed: request.speed,
        pitch: request.pitch
      })
    })
    
    return this.processResponse(response)
  }
}
```

### 2. 阿里云语音合成集成

```typescript
class AliyunTTSService implements TTSService {
  provider = 'aliyun' as const
  
  async generateSpeech(request: SpeechRequest): Promise<AudioResult> {
    // 使用阿里云NLS SDK
    const response = await this.nlsClient.synthesize({
      text: request.text,
      voice: request.voiceId,
      format: request.format,
      sample_rate: 16000,
      speech_rate: Math.round(request.speed * 100),
      pitch_rate: request.pitch
    })
    
    return this.processResponse(response)
  }
}
```

## 📈 性能优化策略

1. **音频缓存**：缓存常用对话的音频文件
2. **批量处理**：支持批量TTS请求减少API调用
3. **异步处理**：音频生成与视频生成并行进行
4. **格式优化**：根据用途选择最优音频格式
5. **压缩策略**：平衡音质与文件大小

## 🔍 质量控制

1. **音频质量检测**：自动检测音频质量问题
2. **时长匹配验证**：确保音频与视频时长匹配
3. **声音一致性检查**：验证角色声音的一致性
4. **情感表达评估**：检查情感表达的准确性

## 🚀 实施计划

详细的实施步骤将在后续任务中逐步完成，包括：
1. TTS服务配置管理实现
2. 角色声音映射功能开发
3. 文本到语音转换实现
4. 音频与视频同步功能
5. 背景音乐和音效集成

---

*本文档将随着开发进度持续更新和完善*
