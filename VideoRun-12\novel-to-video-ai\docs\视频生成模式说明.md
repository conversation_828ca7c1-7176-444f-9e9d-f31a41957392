# 视频生成模式说明

## 概述

系统现在支持两种视频生成模式：**自动模式** 和 **手动模式**（默认）。用户可以根据需要选择合适的生成方式。

## 模式说明

### 🔧 手动模式（推荐，默认）

**特点：**
- 创建视频片段后，所有片段状态为 `pending`
- 需要用户逐个点击生成按钮来生成视频
- 可以选择性地生成需要的片段
- 避免一次性生成所有片段导致的资源浪费

**适用场景：**
- 想要预览片段内容后再决定是否生成
- 只需要生成部分片段
- 避免因某个片段失败而影响整体进度
- 节省计算资源和成本

**操作流程：**
1. 在"详细剧情提取"页面选择"手动模式"
2. 点击"生成剧情视频"创建片段
3. 在"剧情视频片段"页面查看所有片段
4. 鼠标悬停在需要生成的片段上，点击生成按钮
5. 逐个生成所需的视频片段

### ⚡ 自动模式

**特点：**
- 创建视频片段后立即开始生成所有片段
- 串行生成，一个接一个
- 无需用户干预，全自动完成

**适用场景：**
- 确定需要生成所有片段
- 希望一次性完成所有视频生成
- 不介意等待较长时间

**操作流程：**
1. 在"详细剧情提取"页面选择"自动模式"
2. 点击"生成剧情视频"
3. 系统自动开始生成所有片段
4. 在"剧情视频片段"页面监控生成进度

## UI 界面说明

### 模式选择器

在"详细剧情提取"页面的"视频生成设置"部分：

```
生成模式
○ 手动模式 (推荐)
○ 自动模式

手动模式：创建片段后需要逐个点击生成，可选择性生成需要的片段
自动模式：创建片段后自动开始生成所有片段
```

### 片段生成按钮

在"剧情视频片段"页面：

**手动模式下：**
- 每个 `pending`、`failed`、`waiting` 状态的片段都显示生成按钮
- 鼠标悬停在片段上显示紫色的生成按钮
- 按钮提示文字：
  - `pending`: "开始生成"
  - `failed`: "重新生成"  
  - `waiting`: "立即生成"

**自动模式下：**
- 片段会自动开始生成，无需手动点击
- 仍可以对失败的片段进行重新生成

### 状态指示器

在"剧情视频片段"页面头部显示：
```
🎬 剧情视频片段 • 详细进度监控
[手动生成模式] 点击片段上的生成按钮来逐个生成视频
```

## 技术实现

### 前端变更
- 添加 `videoGenerationMode` 状态管理
- 在 `DetailedPlotExtraction` 组件中添加模式选择器
- 在 `VideoSegmentViewer` 中显示当前模式
- 优化生成按钮的显示逻辑

### 后端变更
- API 接受 `generationMode` 参数
- 根据模式决定是否调用 `generateSegmentsAsync`
- 保持原有的单片段生成 API 不变

### 数据流
1. 用户在前端选择生成模式
2. 模式参数传递给后端 API
3. 后端根据模式决定生成策略：
   - `auto`: 立即调用 `generateSegmentsAsync`
   - `manual`: 跳过自动生成，等待手动触发

## 使用建议

1. **首次使用**：建议使用手动模式，先生成1-2个片段查看效果
2. **批量生成**：确认效果满意后，可切换到自动模式批量生成
3. **调试阶段**：使用手动模式便于调试和优化提示词
4. **生产环境**：根据实际需求选择合适的模式

## 注意事项

- 默认模式为手动模式，更安全和节省资源
- 两种模式可以随时切换，不影响已创建的片段
- 失败的片段在任何模式下都可以重新生成
- 手动模式下的生成按钮只在鼠标悬停时显示，保持界面简洁
