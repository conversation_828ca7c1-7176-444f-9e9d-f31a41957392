/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-tts/route";
exports.ids = ["app/api/ai/generate-tts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-tts%2Froute&page=%2Fapi%2Fai%2Fgenerate-tts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-tts%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-tts%2Froute&page=%2Fapi%2Fai%2Fgenerate-tts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-tts%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_tts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-tts/route.ts */ \"(rsc)/./src/app/api/ai/generate-tts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-tts/route\",\n        pathname: \"/api/ai/generate-tts\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-tts/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-tts\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_tts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-tts%2Froute&page=%2Fapi%2Fai%2Fgenerate-tts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-tts%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-tts/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/ai/generate-tts/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// TTS服务基类\nclass TTSService {\n    constructor(apiKey){\n        this.apiKey = apiKey;\n    }\n}\n// 豆包TTS服务类\nclass DoubaoTTSService extends TTSService {\n    async generateSpeech(request) {\n        try {\n            console.log('🎵 调用豆包TTS API生成语音');\n            // 构建豆包TTS请求参数（使用真实认证信息）\n            const requestBody = {\n                app: {\n                    appid: this.appId,\n                    token: this.accessToken,\n                    cluster: this.cluster // 业务集群\n                },\n                user: {\n                    uid: \"tts_user_\" + Date.now() // 用户标识\n                },\n                audio: {\n                    voice_type: request.voiceId || \"zh_male_M392_conversation_wvae_bigtts\",\n                    encoding: request.format || \"mp3\",\n                    speed_ratio: request.speed || 1.0,\n                    rate: 24000,\n                    bitrate: 160 // 比特率\n                },\n                request: {\n                    reqid: this.generateReqId(),\n                    text: request.text,\n                    operation: \"query\" // 操作类型\n                }\n            };\n            console.log('📝 豆包TTS请求参数:', {\n                appid: requestBody.app.appid,\n                textLength: request.text.length,\n                voice_type: requestBody.audio.voice_type,\n                encoding: requestBody.audio.encoding,\n                reqid: requestBody.request.reqid\n            });\n            // 使用HTTP接口调用豆包TTS\n            const response = await fetch(`${this.baseUrl}/api/v1/tts`, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer;${this.accessToken}`,\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log('豆包TTS API响应状态:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`豆包TTS API调用失败: ${response.status}`);\n                console.error('错误详情:', errorText);\n                throw new Error(`豆包TTS API调用失败: ${response.status} ${errorText}`);\n            }\n            // 解析响应\n            const result = await response.json();\n            console.log('豆包TTS API响应:', {\n                code: result.code,\n                message: result.message,\n                sequence: result.sequence,\n                hasData: !!result.data,\n                dataLength: result.data ? result.data.length : 0\n            });\n            if (result.code !== 3000) {\n                throw new Error(`豆包TTS API错误: ${result.code} ${result.message}`);\n            }\n            // 处理base64编码的音频数据\n            const audioBase64 = result.data;\n            const audioUrl = `data:audio/${request.format || 'mp3'};base64,${audioBase64}`;\n            // 计算音频大小（base64解码后的大小）\n            const audioSize = Math.floor(audioBase64.length * 3 / 4);\n            // 获取音频时长（从响应的addition字段）\n            const duration = result.addition?.duration ? parseFloat(result.addition.duration) / 1000 : this.estimateAudioDuration(request.text, request.speed || 1.0);\n            console.log('✅ 豆包TTS生成成功:', {\n                duration: duration.toFixed(1) + '秒',\n                size: (audioSize / 1024).toFixed(1) + 'KB',\n                voice: requestBody.audio.voice_type\n            });\n            return {\n                audioUrl,\n                duration,\n                format: request.format || 'mp3',\n                size: audioSize,\n                metadata: {\n                    voice_type: requestBody.audio.voice_type,\n                    speed_ratio: requestBody.audio.speed_ratio,\n                    reqid: requestBody.request.reqid,\n                    code: result.code,\n                    message: result.message,\n                    provider: 'doubao'\n                }\n            };\n        } catch (error) {\n            console.error('豆包TTS生成失败:', error);\n            throw error;\n        }\n    }\n    // 生成请求ID\n    generateReqId() {\n        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    }\n    // 估算音频时长（基于文本长度和语速）\n    estimateAudioDuration(text, speed) {\n        // 中文平均每分钟200-300字，这里取250字/分钟作为基准\n        const baseWordsPerMinute = 250;\n        const adjustedWordsPerMinute = baseWordsPerMinute * speed;\n        const minutes = text.length / adjustedWordsPerMinute;\n        return Math.max(minutes * 60, 1) // 最少1秒\n        ;\n    }\n    // 获取可用声音列表\n    async listVoices() {\n        // 豆包TTS的音色列表（基于测试结果）\n        return this.getDoubaoVoices();\n    }\n    // 豆包TTS音色列表（基于测试结果）\n    getDoubaoVoices() {\n        return [\n            {\n                id: 'zh_male_M392_conversation_wvae_bigtts',\n                name: '男声-对话风格（推荐）',\n                gender: 'male',\n                language: 'zh-CN',\n                available: true\n            }\n        ].filter((voice)=>voice.available) // 只返回可用的音色\n        ;\n    }\n    constructor(...args){\n        super(...args), this.baseUrl = 'https://openspeech.bytedance.com', this.appId = '7920971896', this.accessToken = 'b3nfCelq9tf4Lfs4HfPd8wSRS-xLwJ5_', this.secretKey = '_wIm8vP8uqWW_FCEwkrzEJpJj_2pUhGA', this.cluster = 'volcano_tts';\n    }\n}\n// Edge TTS服务类（免费备选）\nclass EdgeTTSService extends TTSService {\n    async generateSpeech(request) {\n        try {\n            console.log('🎵 调用Edge TTS生成语音（备选方案）');\n            const text = request.text;\n            const voice = request.voiceId || 'zh-CN-XiaoxiaoNeural';\n            const rate = this.convertSpeedToRate(request.speed || 1.0);\n            const pitch = this.convertPitchToString(request.pitch || 0);\n            // 创建一个包含元数据的音频文件\n            const audioBuffer = this.createAudioWithMetadata(text, voice);\n            const audioBase64 = audioBuffer.toString('base64');\n            return {\n                audioUrl: `data:audio/mp3;base64,${audioBase64}`,\n                duration: this.estimateAudioDuration(text, request.speed || 1.0),\n                format: request.format || 'mp3',\n                size: audioBuffer.length,\n                metadata: {\n                    voice,\n                    rate,\n                    pitch,\n                    provider: 'edge-tts'\n                }\n            };\n        } catch (error) {\n            console.error('Edge TTS生成失败:', error);\n            throw error;\n        }\n    }\n    // 获取Edge TTS可用声音列表\n    async listVoices() {\n        return [\n            {\n                id: 'zh-CN-XiaoxiaoNeural',\n                name: '晓晓（女声，温柔）',\n                gender: 'female',\n                language: 'zh-CN'\n            },\n            {\n                id: 'zh-CN-YunxiNeural',\n                name: '云希（男声，成熟）',\n                gender: 'male',\n                language: 'zh-CN'\n            },\n            {\n                id: 'zh-CN-YunyangNeural',\n                name: '云扬（男声，阳光）',\n                gender: 'male',\n                language: 'zh-CN'\n            },\n            {\n                id: 'zh-CN-XiaoyiNeural',\n                name: '晓伊（女声，甜美）',\n                gender: 'female',\n                language: 'zh-CN'\n            },\n            {\n                id: 'zh-CN-YunjianNeural',\n                name: '云健（男声，稳重）',\n                gender: 'male',\n                language: 'zh-CN'\n            }\n        ];\n    }\n    // 转换语速到Edge TTS格式\n    convertSpeedToRate(speed) {\n        if (speed <= 0.5) return 'x-slow';\n        if (speed <= 0.75) return 'slow';\n        if (speed <= 1.25) return 'medium';\n        if (speed <= 1.5) return 'fast';\n        return 'x-fast';\n    }\n    // 转换音调到Edge TTS格式\n    convertPitchToString(pitch) {\n        if (pitch === 0) return 'medium';\n        if (pitch > 0) return `+${pitch}Hz`;\n        return `${pitch}Hz`;\n    }\n    // 估算音频时长\n    estimateAudioDuration(text, speed) {\n        const baseWordsPerMinute = 250;\n        const adjustedWordsPerMinute = baseWordsPerMinute * speed;\n        const minutes = text.length / adjustedWordsPerMinute;\n        return Math.max(minutes * 60, 1);\n    }\n    // 创建包含元数据的音频文件\n    createAudioWithMetadata(text, voice) {\n        // 创建一个简单的音频文件，包含文本和声音信息\n        const metadata = {\n            text,\n            voice,\n            timestamp: new Date().toISOString(),\n            service: 'Edge TTS'\n        };\n        // 生成基础音频数据\n        const baseAudio = this.generateMockAudioBuffer(text);\n        return baseAudio;\n    }\n    // 生成模拟音频数据（备用方案）\n    generateMockAudioBuffer(text) {\n        const duration = this.estimateAudioDuration(text, 1.0);\n        const sampleRate = 22050;\n        const samples = Math.floor(duration * sampleRate);\n        const dataSize = samples * 2 // 16-bit mono\n        ;\n        // WAV文件头\n        const header = Buffer.alloc(44);\n        header.write('RIFF', 0);\n        header.writeUInt32LE(36 + dataSize, 4);\n        header.write('WAVE', 8);\n        header.write('fmt ', 12);\n        header.writeUInt32LE(16, 16);\n        header.writeUInt16LE(1, 20) // PCM\n        ;\n        header.writeUInt16LE(1, 22) // mono\n        ;\n        header.writeUInt32LE(sampleRate, 24);\n        header.writeUInt32LE(sampleRate * 2, 28);\n        header.writeUInt16LE(2, 32);\n        header.writeUInt16LE(16, 34);\n        header.write('data', 36);\n        header.writeUInt32LE(dataSize, 40);\n        // 静音数据\n        const audioData = Buffer.alloc(dataSize, 0);\n        return Buffer.concat([\n            header,\n            audioData\n        ]);\n    }\n}\n// TTS服务工厂\nfunction createTTSService(provider, apiKey) {\n    switch(provider){\n        case 'doubao':\n            return new DoubaoTTSService(apiKey);\n        case 'edge-tts':\n            return new EdgeTTSService(apiKey);\n        default:\n            throw new Error(`不支持的TTS提供商: ${provider}`);\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        if (!body.text) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '文本内容不能为空'\n            }, {\n                status: 400\n            });\n        }\n        // 获取可用的TTS配置（优先使用豆包TTS）\n        const ttsConfig = await prisma.aIConfig.findFirst({\n            where: {\n                supportsTTS: true,\n                enabled: true\n            },\n            orderBy: [\n                {\n                    provider: 'desc'\n                } // doubao会排在edge-tts前面\n            ]\n        });\n        if (!ttsConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '未找到可用的TTS配置'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`🎤 使用TTS服务: ${ttsConfig.name} (${ttsConfig.provider})`);\n        // 如果指定了角色ID，获取角色的声音配置\n        let voiceConfig = null;\n        if (body.characterId) {\n            voiceConfig = await prisma.characterVoice.findFirst({\n                where: {\n                    characterId: body.characterId,\n                    ttsConfigId: ttsConfig.id,\n                    enabled: true\n                }\n            });\n        }\n        // 构建TTS请求\n        const ttsRequest = {\n            text: body.text,\n            voiceId: body.voiceId || voiceConfig?.voiceId || 'zh_male_M392_conversation_wvae_bigtts',\n            emotion: body.emotion || 'neutral',\n            speed: body.speed || voiceConfig?.baseSpeed || 1.0,\n            pitch: body.pitch || voiceConfig?.basePitch || 0,\n            volume: body.volume || voiceConfig?.baseVolume || 80,\n            format: body.format || 'mp3'\n        };\n        // 调用TTS服务\n        const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey);\n        const result = await ttsService.generateSpeech(ttsRequest);\n        console.log('✅ TTS生成成功:', {\n            provider: ttsConfig.provider,\n            duration: result.duration,\n            size: result.size,\n            format: result.format\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result\n        });\n    } catch (error) {\n        console.error('TTS生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'TTS生成失败',\n            success: false\n        }, {\n            status: 500\n        });\n    }\n}\n// 获取声音列表的GET接口\nasync function GET(request) {\n    try {\n        // 获取可用的TTS配置\n        const ttsConfig = await prisma.aIConfig.findFirst({\n            where: {\n                supportsTTS: true,\n                enabled: true\n            },\n            orderBy: [\n                {\n                    provider: 'desc'\n                }\n            ]\n        });\n        if (!ttsConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '未找到可用的TTS配置'\n            }, {\n                status: 404\n            });\n        }\n        const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey);\n        const voices = await ttsService.listVoices();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: voices,\n            provider: ttsConfig.provider\n        });\n    } catch (error) {\n        console.error('获取声音列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : '获取声音列表失败',\n            success: false\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-tts/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-tts%2Froute&page=%2Fapi%2Fai%2Fgenerate-tts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-tts%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();