const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugVideoDuration() {
  try {
    console.log('🔍 调试视频时长问题...\n');
    
    // 1. 检查最新的视频片段
    const recentSegments = await prisma.videoSegment.findMany({
      where: {
        status: 'completed',
        videoUrl: { not: null }
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: {
        storyVideo: true
      }
    });
    
    console.log(`📊 找到 ${recentSegments.length} 个最新完成的视频片段:\n`);
    
    for (const segment of recentSegments) {
      console.log(`🎬 片段: ${segment.title}`);
      console.log(`   ID: ${segment.id}`);
      console.log(`   故事视频ID: ${segment.storyVideo?.id || '未知'}`);
      console.log(`   数据库中的时长: ${segment.duration}秒`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   视频URL: ${segment.videoUrl ? '有' : '无'}`);
      
      // 解析metadata查看详细信息
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   元数据:`);
          console.log(`     - 任务ID: ${metadata.taskId || '无'}`);
          console.log(`     - 提供商: ${metadata.provider || '未知'}`);
          console.log(`     - 完成时间: ${metadata.completedAt || '未知'}`);
          console.log(`     - 视频大小: ${metadata.videoSize || '未知'}`);
          console.log(`     - 生成时长: ${metadata.generationTime ? Math.round(metadata.generationTime / 1000) + 's' : '未知'}`);
          console.log(`     - 原始URL: ${metadata.originalUrl ? '有' : '无'}`);
          console.log(`     - 本地路径: ${metadata.localPath || '无'}`);
          
          // 检查是否有duration信息
          if (metadata.duration !== undefined) {
            console.log(`     - 元数据中的时长: ${metadata.duration}秒`);
          }
          
          // 检查优化后的提示词
          if (metadata.optimizedPrompt) {
            console.log(`     - 优化提示词长度: ${metadata.optimizedPrompt.length}字符`);
            
            // 检查提示词中是否包含时长信息
            const prompt = metadata.optimizedPrompt.toLowerCase();
            if (prompt.includes('秒') || prompt.includes('second')) {
              console.log(`     - 提示词包含时长信息: ✅`);
            } else {
              console.log(`     - 提示词包含时长信息: ❌`);
            }
          }
        } catch (e) {
          console.log(`   元数据解析失败: ${e.message}`);
        }
      }
      
      console.log('');
    }
    
    // 2. 检查豆包API配置
    console.log('🔧 检查豆包API配置...');
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        enabled: true,
        supportsVideo: true
      }
    });
    
    if (doubaoConfig) {
      console.log(`✅ 豆包配置: ${doubaoConfig.model}`);
      console.log(`   API密钥: ${doubaoConfig.apiKey ? '已配置' : '未配置'}`);
    } else {
      console.log('❌ 未找到豆包配置');
    }
    
    // 3. 分析时长问题的可能原因
    console.log('\n🔍 时长问题分析:');
    
    const hasCorrectDuration = recentSegments.some(s => s.duration && s.duration > 10);
    const hasMetadataDuration = recentSegments.some(s => {
      try {
        const metadata = JSON.parse(s.metadata || '{}');
        return metadata.duration && metadata.duration > 10;
      } catch {
        return false;
      }
    });
    
    console.log(`   数据库时长正确: ${hasCorrectDuration ? '✅' : '❌'}`);
    console.log(`   元数据时长正确: ${hasMetadataDuration ? '✅' : '❌'}`);
    
    if (!hasCorrectDuration && !hasMetadataDuration) {
      console.log('\n💡 可能的问题:');
      console.log('   1. 豆包API不支持时长参数');
      console.log('   2. 提示词中的时长信息被忽略');
      console.log('   3. API响应中没有返回正确的时长');
      console.log('   4. 时长信息在处理过程中丢失');
    }
    
    // 4. 检查最新的API调用日志
    console.log('\n📋 建议的解决方案:');
    console.log('   1. 在提示词中明确指定时长');
    console.log('   2. 检查豆包API文档确认时长参数格式');
    console.log('   3. 在API调用中添加时长参数');
    console.log('   4. 验证API响应中的时长字段');
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugVideoDuration();
