const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupTestData() {
  try {
    console.log('🎬 设置完整影视生成测试数据...');
    
    // 1. 创建测试项目
    console.log('📚 创建测试项目...');
    
    // 先查找是否存在同名项目
    let testProject = await prisma.project.findFirst({
      where: { name: '完整影视生成测试项目' }
    });

    if (!testProject) {
      testProject = await prisma.project.create({
        data: {
          name: '完整影视生成测试项目',
          description: '用于测试完整影视作品生成功能的示例项目',
          content: `
第一章：神秘的开始

在一个阳光明媚的早晨，年轻的探险家李明走进了古老的图书馆。他正在寻找一本传说中的古籍，据说这本书记录着失落文明的秘密。

"请问，您知道《时光之书》在哪里吗？"李明向图书管理员询问。

图书管理员是一位慈祥的老人，他戴着厚厚的眼镜，仔细地看着李明。"年轻人，那本书已经失踪很多年了。但是，如果你真的想找到它，也许我可以给你一些线索。"

李明的眼睛亮了起来。"真的吗？请告诉我！"

老人神秘地笑了笑，从抽屉里拿出一张古老的地图。"这张地图会指引你找到答案，但是路途充满危险，你确定要继续吗？"

李明毫不犹豫地点了点头。"我已经准备好了。"

就这样，一场惊心动魄的冒险即将开始...
          `,
          status: 'uploaded'
        }
      });
    }
    
    console.log(`✅ 测试项目创建成功: ${testProject.name} (ID: ${testProject.id})`);
    
    // 2. 创建测试剧集
    console.log('📖 创建测试剧集...');
    
    // 先查找是否存在剧集
    let testEpisode = await prisma.episode.findFirst({
      where: {
        projectId: testProject.id,
        orderIndex: 1
      }
    });

    if (!testEpisode) {
      testEpisode = await prisma.episode.create({
        data: {
          projectId: testProject.id,
          title: '第一集：图书馆的秘密',
          content: `
李明走进古老的图书馆，阳光透过彩色玻璃窗洒在木质书架上。他环顾四周，寻找着传说中的《时光之书》。

图书管理员老人注意到了他，主动走过来询问。两人进行了一番对话，老人最终拿出了一张神秘的古地图。

李明接过地图，仔细研究着上面的标记。他意识到这将是一场危险的冒险，但内心的好奇心驱使他决定继续前进。

随着李明离开图书馆，一个全新的冒险故事即将展开...
          `,
          orderIndex: 1,
          status: 'analyzed'
        }
      });
    }
    
    console.log(`✅ 测试剧集创建成功: ${testEpisode.title} (ID: ${testEpisode.id})`);
    
    // 3. 创建剧情信息
    console.log('🎭 创建剧情信息...');
    
    // 先查找是否存在剧情信息
    let plotInfo = await prisma.plotInfo.findUnique({
      where: { episodeId: testEpisode.id }
    });

    if (!plotInfo) {
      plotInfo = await prisma.plotInfo.create({
        data: {
        episodeId: testEpisode.id,
        characters: JSON.stringify([
          {
            name: '李明',
            role: '主角',
            description: '年轻的探险家，充满好奇心和勇气'
          },
          {
            name: '图书管理员',
            role: '配角',
            description: '慈祥的老人，知晓古老秘密的守护者'
          }
        ]),
        scenes: JSON.stringify([
          {
            location: '古老图书馆',
            description: '充满历史感的图书馆，阳光透过彩色玻璃窗',
            mood: '神秘而温暖'
          }
        ]),
        events: JSON.stringify([
          {
            event: '李明寻找《时光之书》',
            cause: '对失落文明的好奇',
            effect: '遇到了知情的图书管理员'
          },
          {
            event: '获得神秘地图',
            cause: '图书管理员的信任',
            effect: '冒险之旅的开始'
          }
        ]),
        detailedCharacters: JSON.stringify([
          {
            name: '李明',
            appearance: '年轻男性，身材中等，穿着休闲装',
            personality: '好奇、勇敢、坚定',
            voice: '年轻男声，充满活力'
          },
          {
            name: '图书管理员',
            appearance: '慈祥老人，戴着厚眼镜，穿着朴素',
            personality: '智慧、神秘、善良',
            voice: '苍老但温和的声音'
          }
        ]),
        detailedScenes: JSON.stringify([
          {
            location: '古老图书馆内部',
            visualElements: '木质书架、彩色玻璃窗、古老书籍、温暖阳光',
            atmosphere: '神秘而宁静',
            lighting: '自然光线，温暖明亮'
          }
        ]),
        plotSequences: JSON.stringify([
          {
            sequenceId: 1,
            action: '李明进入图书馆',
            description: '年轻探险家走进古老的图书馆，环顾四周寻找目标',
            characters: ['李明'],
            location: '图书馆入口',
            emotion: 'curious',
            visualElements: '图书馆内景、书架、阳光',
            dialogue: [
              {
                speaker: '旁白',
                text: '李明走进了古老的图书馆，开始寻找传说中的《时光之书》。',
                emotion: 'neutral'
              }
            ]
          },
          {
            sequenceId: 2,
            action: '与图书管理员对话',
            description: '李明向图书管理员询问《时光之书》的下落',
            characters: ['李明', '图书管理员'],
            location: '图书馆服务台',
            emotion: 'hopeful',
            visualElements: '服务台、老人、对话场景',
            dialogue: [
              {
                speaker: '李明',
                text: '请问，您知道《时光之书》在哪里吗？',
                emotion: 'hopeful'
              },
              {
                speaker: '图书管理员',
                text: '年轻人，那本书已经失踪很多年了。但是，如果你真的想找到它，也许我可以给你一些线索。',
                emotion: 'mysterious'
              }
            ]
          },
          {
            sequenceId: 3,
            action: '获得神秘地图',
            description: '图书管理员拿出古老地图，李明决定开始冒险',
            characters: ['李明', '图书管理员'],
            location: '图书馆服务台',
            emotion: 'excited',
            visualElements: '古老地图、神秘氛围、决心',
            dialogue: [
              {
                speaker: '李明',
                text: '真的吗？请告诉我！',
                emotion: 'excited'
              },
              {
                speaker: '图书管理员',
                text: '这张地图会指引你找到答案，但是路途充满危险，你确定要继续吗？',
                emotion: 'serious'
              },
              {
                speaker: '李明',
                text: '我已经准备好了。',
                emotion: 'determined'
              }
            ]
          }
        ]),
          emotionalArc: '从好奇到希望，再到兴奋和决心',
          generatedPrompt: '古老图书馆场景，年轻探险家与慈祥管理员的神秘对话，温暖阳光透过彩色玻璃窗'
        }
      });
    }
    
    console.log(`✅ 剧情信息创建成功 (ID: ${plotInfo.id})`);
    
    // 4. 创建测试角色
    console.log('👥 创建测试角色...');
    
    // 先查找是否存在角色
    let character1 = await prisma.character.findFirst({
      where: {
        projectId: testProject.id,
        name: '李明'
      }
    });

    if (!character1) {
      character1 = await prisma.character.create({
        data: {
        projectId: testProject.id,
        name: '李明',
        identity: '年轻探险家',
        personality: '好奇心强、勇敢、坚定不移',
        physique: '身材中等，体格健壮',
        facial: '年轻面孔，眼神坚定',
        hairstyle: '短发，整洁',
        clothing: '休闲装，适合冒险',
          detailedDNA: JSON.stringify({
            age: '25岁',
            height: '175cm',
            build: '中等身材',
            eyeColor: '黑色',
            hairColor: '黑色',
            skinTone: '健康肤色',
            distinctiveFeatures: '坚定的眼神，自信的笑容'
          })
        }
      });
    }

    let character2 = await prisma.character.findFirst({
      where: {
        projectId: testProject.id,
        name: '图书管理员'
      }
    });

    if (!character2) {
      character2 = await prisma.character.create({
        data: {
        projectId: testProject.id,
        name: '图书管理员',
        identity: '古老图书馆的守护者',
        personality: '智慧、神秘、善良',
        physique: '年老体弱，但精神矍铄',
        facial: '慈祥面容，戴着厚眼镜',
        hairstyle: '花白头发，稀疏',
        clothing: '朴素的管理员制服',
          detailedDNA: JSON.stringify({
            age: '70岁',
            height: '165cm',
            build: '瘦弱',
            eyeColor: '灰色',
            hairColor: '花白',
            skinTone: '苍白',
            distinctiveFeatures: '厚厚的眼镜，神秘的笑容'
          })
        }
      });
    }
    
    console.log(`✅ 角色创建成功: ${character1.name}, ${character2.name}`);
    
    // 5. 显示测试数据摘要
    console.log('\n📊 测试数据摘要:');
    console.log(`项目ID: ${testProject.id}`);
    console.log(`剧集ID: ${testEpisode.id}`);
    console.log(`剧情信息ID: ${plotInfo.id}`);
    console.log(`角色数量: 2`);
    
    console.log('\n🎉 测试数据设置完成！');
    console.log('现在可以运行完整影视生成测试了。');
    
    return {
      project: testProject,
      episode: testEpisode,
      plotInfo: plotInfo,
      characters: [character1, character2]
    };
    
  } catch (error) {
    console.error('❌ 设置测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

setupTestData();
