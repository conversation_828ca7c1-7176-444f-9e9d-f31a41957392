{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Home,\n  FolderOpen,\n  Settings,\n  User,\n  Menu,\n  X,\n  Sparkles\n} from 'lucide-react'\n\ninterface LayoutProps {\n  children: React.ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navigation = [\n    {\n      name: '首页',\n      href: '/',\n      icon: Home,\n      current: pathname === '/'\n    },\n    {\n      name: '项目',\n      href: '/projects',\n      icon: FolderOpen,\n      current: pathname.startsWith('/projects')\n    },\n    {\n      name: '模型配置',\n      href: '/models',\n      icon: Settings,\n      current: pathname === '/models'\n    },\n    {\n      name: '账户',\n      href: '/account',\n      icon: User,\n      current: pathname === '/account',\n      disabled: true\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* 左侧 Logo */}\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <Sparkles className=\"text-purple-600 mr-2\" size={24} />\n                <span className=\"text-lg font-semibold text-gray-900 whitespace-nowrap\">\n                  灵犀Ai——小说转视频神器\n                </span>\n              </Link>\n            </div>\n\n            {/* 右侧导航菜单 */}\n            <div className=\"hidden md:flex md:items-center md:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    inline-flex items-center text-lg font-semibold\n                    ${item.current\n                      ? 'text-purple-600'\n                      : 'text-gray-900 hover:text-purple-600'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                >\n                  <item.icon className=\"mr-2\" size={20} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden flex items-center\">\n              <button\n                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <Menu size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端菜单 */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    flex items-center px-3 py-2 text-sm font-medium border-l-4\n                    ${item.current\n                      ? 'bg-purple-50 border-purple-500 text-purple-700'\n                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <item.icon className=\"mr-2\" size={16} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* 主内容区域 */}\n      <main className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAmBe,SAAS,OAAO,EAAE,QAAQ,EAAe;;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,SAAS,aAAa;YACtB,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,MAAM;;;;;;0DACjD,6LAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;;;;;;8CAO5E,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,oBACA,sCACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;;8DAED,6LAAC,KAAK,IAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDACjC,KAAK,IAAI;;2CAZL,KAAK,IAAI;;;;;;;;;;8CAkBpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,gCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,mDACA,wEACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;oCACD,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC,KAAK,IAAI;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCACjC,KAAK,IAAI;;mCAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BAsB1B,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GApHwB;;QAEL,qIAAA,CAAA,cAAW;;;KAFN", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/AIConfigPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Switch } from '@headlessui/react'\nimport { Settings, Check, X, AlertCircle, Loader, Sliders } from 'lucide-react'\n\ninterface ModelConfig {\n  id: string\n  provider: string\n  model: string\n  apiKey: string\n  enabled: boolean\n  name: string\n  description: string\n  status?: 'connected' | 'disconnected' | 'testing' | 'error'\n}\n\ninterface AIConfigPanelProps {\n  onConfigSaved?: () => void\n}\n\nexport default function AIConfigPanel({ onConfigSaved }: AIConfigPanelProps) {\n  const [models, setModels] = useState<ModelConfig[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [showParameterSettings, setShowParameterSettings] = useState<string | null>(null)\n\n  // 预定义的模型配置\n  const predefinedModels = [\n    // DeepSeek 系列\n    {\n      id: 'deepseek-reasoner',\n      provider: 'deepseek',\n      model: 'deepseek-reasoner',\n      name: 'DeepSeek Reasoner',\n      description: '专业推理模型，适合复杂逻辑分析',\n      apiKey: '',\n      enabled: false\n    },\n    // 豆包系列 - 使用正确的端点信息\n    {\n      id: 'doubao-seedream-3-0-t2i-latest',\n      provider: 'doubao',\n      model: 'ep-20250626132353-nlrtf',\n      name: 'Doubao-Seedream-3.0-t2i',\n      description: '豆包文本到图像生成模型 - 最新版本 (2025-06-26)',\n      apiKey: '',\n      enabled: false,\n      supportsVideo: false,\n      supportsImage: true,\n      supportsImageToVideo: false,\n      supportsTTS: false\n    },\n    {\n      id: 'doubao-seedance-1-0-pro',\n      provider: 'doubao',\n      model: 'ep-20250624192235-zttm6',\n      name: 'Doubao-Seedance-1.0-pro',\n      description: '豆包专业版视频生成模型',\n      apiKey: '',\n      enabled: false,\n      supportsVideo: true,\n      supportsImage: false,\n      supportsImageToVideo: false,\n      supportsTTS: false\n    },\n    {\n      id: 'doubao-seedance-1-0-lite-i2v',\n      provider: 'doubao',\n      model: 'ep-20250624195026-qjsmk',\n      name: 'Doubao-Seedance-1.0-lite-i2v',\n      description: '豆包图片到视频转换模型',\n      apiKey: '',\n      enabled: false,\n      supportsVideo: true,\n      supportsImage: false,\n      supportsImageToVideo: true,\n      supportsTTS: false\n    },\n    {\n      id: 'doubao-seedance-1-0-lite-t2v',\n      provider: 'doubao',\n      model: 'ep-20250624192345-5ccwj',\n      name: 'Doubao-Seedance-1.0-lite-t2v',\n      description: '豆包文本到视频生成模型',\n      apiKey: '',\n      enabled: false,\n      supportsVideo: true,\n      supportsImage: false,\n      supportsImageToVideo: false,\n      supportsTTS: false\n    },\n    // SkyReels-V2 本地模型\n    {\n      id: 'skyreels-v2-df-1-3b-540p',\n      provider: 'skyreels',\n      model: 'SkyReels-V2-DF-1.3B-540P',\n      name: 'SkyReels-V2 本地模型',\n      description: '本地部署的SkyReels-V2文生视频模型，支持无限长度视频生成，540P分辨率',\n      apiKey: 'http://localhost:8000',\n      enabled: false,\n      supportsVideo: true,\n      supportsImageToVideo: false\n    }\n  ]\n\n  // 加载模型配置\n  useEffect(() => {\n    loadModels()\n  }, [])\n\n  const loadModels = async () => {\n    try {\n      const response = await fetch('/api/models')\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          // 合并预定义模型和已保存的配置\n          const savedModels = data.data || []\n          const mergedModels = predefinedModels.map(predefined => {\n            // 通过 provider + model 匹配，而不是通过ID\n            const saved = savedModels.find((s: ModelConfig) =>\n              s.provider === predefined.provider && s.model === predefined.model\n            )\n            return saved ? {\n              ...predefined,\n              ...saved,\n              // 保持预定义的描述和名称，但使用数据库的ID和状态\n              name: predefined.name,\n              description: predefined.description\n            } : predefined\n          })\n          setModels(mergedModels)\n        } else {\n          setModels(predefinedModels)\n        }\n      } else {\n        setModels(predefinedModels)\n      }\n    } catch (error) {\n      console.error('加载模型配置失败:', error)\n      setModels(predefinedModels)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // 更新模型配置\n  const updateModel = (id: string, updates: Partial<ModelConfig>) => {\n    setModels(prev => prev.map(model => {\n      if (model.id === id) {\n        const updated = { ...model, ...updates }\n        // 如果更新包含新的ID，需要特殊处理\n        if (updates.id && updates.id !== id) {\n          // ID发生了变化，这通常发生在保存新模型时\n          return updated\n        }\n        return updated\n      }\n      return model\n    }))\n  }\n\n  // 保存模型配置\n  const saveModel = async (model: ModelConfig) => {\n    try {\n      // 检查是否为新模型\n      const isNewModel = !model.id.startsWith('cmc')\n\n      const response = await fetch('/api/models', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...model,\n          id: isNewModel ? undefined : model.id // 新模型不传ID\n        }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        if (isNewModel) {\n          // 新模型：更新ID并设置状态\n          const oldId = model.id\n          const dbId = data.data.id\n          setModels(prev => prev.map(m =>\n            m.id === oldId ? { ...m, id: dbId, status: 'connected' } : m\n          ))\n        } else {\n          // 现有模型：只更新状态\n          updateModel(model.id, { status: 'connected' })\n        }\n        onConfigSaved?.()\n      } else {\n        updateModel(model.id, { status: 'error' })\n        setError(data.error || '保存配置失败')\n      }\n    } catch (error) {\n      updateModel(model.id, { status: 'error' })\n      setError('保存配置失败，请重试')\n    }\n  }\n\n  // 测试模型连接\n  const testModel = async (model: ModelConfig) => {\n    if (!model.apiKey) {\n      setError('请先输入API密钥')\n      return\n    }\n\n    updateModel(model.id, { status: 'testing' })\n    setError(null)\n\n    try {\n      // 检查模型是否已保存到数据库（通过ID格式判断）\n      const isNewModel = !model.id.startsWith('cmc')\n\n      if (isNewModel) {\n        // 这是新模型，需要先保存\n        const saveResponse = await fetch('/api/models', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            ...model,\n            id: undefined // 不传ID让数据库生成\n          }),\n        })\n\n        const saveData = await saveResponse.json()\n        if (!saveData.success) {\n          updateModel(model.id, { status: 'error' })\n          setError(saveData.error || '保存配置失败')\n          return\n        }\n\n        // 更新本地模型，使用数据库返回的ID\n        const dbId = saveData.data.id\n        const oldId = model.id\n        setModels(prev => prev.map(m =>\n          m.id === oldId ? { ...m, id: dbId } : m\n        ))\n        model = { ...model, id: dbId }\n      }\n\n      const response = await fetch('/api/models/test', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(model),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        updateModel(model.id, { status: 'connected' })\n      } else {\n        updateModel(model.id, { status: 'error' })\n        setError(data.error || '连接测试失败')\n      }\n    } catch (error) {\n      updateModel(model.id, { status: 'error' })\n      setError('连接测试失败，请检查网络')\n    }\n  }\n\n  // 按提供商分组模型\n  const groupedModels = models.reduce((groups, model) => {\n    const provider = model.provider\n    if (!groups[provider]) {\n      groups[provider] = []\n    }\n    groups[provider].push(model)\n    return groups\n  }, {} as Record<string, ModelConfig[]>)\n\n  // 获取提供商显示名称\n  const getProviderName = (provider: string) => {\n    switch (provider) {\n      case 'deepseek': return 'DeepSeek'\n      case 'doubao': return '豆包 (火山引擎)'\n      case 'skyreels': return 'SkyReels-V2 (本地模型)'\n      default: return provider\n    }\n  }\n\n  // 获取状态图标\n  const getStatusIcon = (status?: string) => {\n    switch (status) {\n      case 'connected': return <Check className=\"text-green-500\" size={16} />\n      case 'testing': return <Loader className=\"text-blue-500 animate-spin\" size={16} />\n      case 'error': return <X className=\"text-red-500\" size={16} />\n      default: return <AlertCircle className=\"text-gray-400\" size={16} />\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n        <div className=\"flex items-center justify-center py-8\">\n          <Loader className=\"animate-spin mr-2\" size={20} />\n          <span>加载模型配置中...</span>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n      <h2 className=\"text-xl font-semibold mb-6 flex items-center\">\n        <Settings className=\"mr-2\" size={24} />\n        大模型配置\n      </h2>\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n          {error}\n        </div>\n      )}\n\n      {/* 按提供商分组显示模型 */}\n      <div className=\"space-y-6\">\n        {Object.entries(groupedModels).map(([provider, providerModels]) => (\n          <div key={provider} className=\"border border-gray-200 rounded-lg p-4\">\n            <h3 className=\"text-lg font-medium mb-4 text-gray-900\">\n              {getProviderName(provider)}\n            </h3>\n\n            <div className=\"space-y-4\">\n              {providerModels.map((model) => (\n                <div key={model.id} className=\"border border-gray-100 rounded-lg p-4 bg-gray-50\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"flex items-center space-x-2\">\n                        {getStatusIcon(model.status)}\n                        <h4 className=\"font-medium text-gray-900\">{model.name}</h4>\n                        <button\n                          onClick={() => setShowParameterSettings(showParameterSettings === model.id ? null : model.id)}\n                          className=\"p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors\"\n                          title=\"API参数设置\"\n                        >\n                          <Sliders size={16} />\n                        </button>\n                      </div>\n                      <Switch\n                        checked={model.enabled}\n                        onChange={(enabled) => {\n                          updateModel(model.id, { enabled })\n                          if (enabled && model.apiKey) {\n                            saveModel({ ...model, enabled })\n                          }\n                        }}\n                        className={`${\n                          model.enabled ? 'bg-purple-600' : 'bg-gray-200'\n                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}\n                      >\n                        <span\n                          className={`${\n                            model.enabled ? 'translate-x-6' : 'translate-x-1'\n                          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}\n                        />\n                      </Switch>\n                    </div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 mb-3\">{model.description}</p>\n\n                  <div className=\"flex space-x-2\">\n                    <input\n                      type={model.provider === 'skyreels' ? 'text' : 'password'}\n                      value={model.apiKey}\n                      onChange={(e) => updateModel(model.id, { apiKey: e.target.value })}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm\"\n                      placeholder={model.provider === 'skyreels' ? '输入API服务器地址 (如: http://localhost:8000)' : '输入API密钥...'}\n                    />\n                    <button\n                      onClick={() => testModel(model)}\n                      disabled={!model.apiKey || model.status === 'testing'}\n                      className=\"px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n                      title=\"测试API连接\"\n                    >\n                      {model.status === 'testing' ? '测试中' : '测试'}\n                    </button>\n                    <button\n                      onClick={() => saveModel(model)}\n                      disabled={!model.apiKey}\n                      className=\"px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n                      title=\"保存模型配置\"\n                    >\n                      保存\n                    </button>\n                  </div>\n\n                  {/* API参数设置面板 */}\n                  {showParameterSettings === model.id && (\n                    <div className=\"mt-4 p-4 bg-white border border-gray-200 rounded-lg\">\n                      <h5 className=\"text-md font-medium text-gray-900 mb-4 flex items-center\">\n                        <Sliders className=\"mr-2\" size={18} />\n                        API调用参数设置\n                      </h5>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        {/* Temperature 设置 */}\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Temperature (创造性)\n                          </label>\n                          <input\n                            type=\"range\"\n                            min=\"0\"\n                            max=\"2\"\n                            step=\"0.1\"\n                            value={model.temperature || 0.7}\n                            onChange={(e) => updateModel(model.id, { temperature: parseFloat(e.target.value) })}\n                            className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                          />\n                          <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                            <span>0 (保守)</span>\n                            <span className=\"font-medium\">{model.temperature || 0.7}</span>\n                            <span>2 (创新)</span>\n                          </div>\n                        </div>\n\n                        {/* Max Tokens 设置 */}\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Max Tokens (最大输出)\n                          </label>\n                          <input\n                            type=\"number\"\n                            min=\"100\"\n                            max=\"8000\"\n                            step=\"100\"\n                            value={model.maxTokens || 4000}\n                            onChange={(e) => updateModel(model.id, { maxTokens: parseInt(e.target.value) })}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm\"\n                          />\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            当前: {model.maxTokens || 4000} tokens\n                          </div>\n                        </div>\n\n                        {/* Top P 设置 */}\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Top P (核心采样)\n                          </label>\n                          <input\n                            type=\"range\"\n                            min=\"0.1\"\n                            max=\"1\"\n                            step=\"0.05\"\n                            value={model.topP || 0.9}\n                            onChange={(e) => updateModel(model.id, { topP: parseFloat(e.target.value) })}\n                            className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                          />\n                          <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                            <span>0.1 (聚焦)</span>\n                            <span className=\"font-medium\">{model.topP || 0.9}</span>\n                            <span>1.0 (多样)</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* 参数说明 */}\n                      <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                        <h6 className=\"text-sm font-medium text-blue-900 mb-2\">参数说明：</h6>\n                        <ul className=\"text-xs text-blue-800 space-y-1\">\n                          <li><strong>Temperature:</strong> 控制输出的随机性，值越高越有创造性</li>\n                          <li><strong>Max Tokens:</strong> 限制模型单次输出的最大长度</li>\n                          <li><strong>Top P:</strong> 控制词汇选择范围，值越小输出越聚焦</li>\n                        </ul>\n                      </div>\n\n                      {/* 保存参数按钮 */}\n                      <div className=\"mt-4 flex justify-end\">\n                        <button\n                          onClick={() => {\n                            saveModel(model)\n                            setShowParameterSettings(null)\n                          }}\n                          className=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm\"\n                          title=\"保存API参数设置\"\n                        >\n                          保存参数设置\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAqBe,SAAS,cAAc,EAAE,aAAa,EAAsB;;IACzE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElF,WAAW;IACX,MAAM,mBAAmB;QACvB,cAAc;QACd;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;QACX;QACA,mBAAmB;QACnB;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,eAAe;YACf,sBAAsB;YACtB,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,eAAe;YACf,sBAAsB;YACtB,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,eAAe;YACf,sBAAsB;YACtB,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,eAAe;YACf,sBAAsB;YACtB,aAAa;QACf;QACA,mBAAmB;QACnB;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,MAAM;YACN,aAAa;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,sBAAsB;QACxB;KACD;IAED,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,iBAAiB;oBACjB,MAAM,cAAc,KAAK,IAAI,IAAI,EAAE;oBACnC,MAAM,eAAe,iBAAiB,GAAG,CAAC,CAAA;wBACxC,iCAAiC;wBACjC,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAC,IAC9B,EAAE,QAAQ,KAAK,WAAW,QAAQ,IAAI,EAAE,KAAK,KAAK,WAAW,KAAK;wBAEpE,OAAO,QAAQ;4BACb,GAAG,UAAU;4BACb,GAAG,KAAK;4BACR,2BAA2B;4BAC3B,MAAM,WAAW,IAAI;4BACrB,aAAa,WAAW,WAAW;wBACrC,IAAI;oBACN;oBACA,UAAU;gBACZ,OAAO;oBACL,UAAU;gBACZ;YACF,OAAO;gBACL,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF;IAEA,SAAS;IACT,MAAM,cAAc,CAAC,IAAY;QAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBACzB,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,UAAU;wBAAE,GAAG,KAAK;wBAAE,GAAG,OAAO;oBAAC;oBACvC,oBAAoB;oBACpB,IAAI,QAAQ,EAAE,IAAI,QAAQ,EAAE,KAAK,IAAI;wBACnC,uBAAuB;wBACvB,OAAO;oBACT;oBACA,OAAO;gBACT;gBACA,OAAO;YACT;IACF;IAEA,SAAS;IACT,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC;YAExC,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,KAAK;oBACR,IAAI,aAAa,YAAY,MAAM,EAAE,CAAC,UAAU;gBAClD;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,YAAY;oBACd,gBAAgB;oBAChB,MAAM,QAAQ,MAAM,EAAE;oBACtB,MAAM,OAAO,KAAK,IAAI,CAAC,EAAE;oBACzB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,QAAQ;gCAAE,GAAG,CAAC;gCAAE,IAAI;gCAAM,QAAQ;4BAAY,IAAI;gBAE/D,OAAO;oBACL,aAAa;oBACb,YAAY,MAAM,EAAE,EAAE;wBAAE,QAAQ;oBAAY;gBAC9C;gBACA;YACF,OAAO;gBACL,YAAY,MAAM,EAAE,EAAE;oBAAE,QAAQ;gBAAQ;gBACxC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,MAAM,EAAE,EAAE;gBAAE,QAAQ;YAAQ;YACxC,SAAS;QACX;IACF;IAEA,SAAS;IACT,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,MAAM,MAAM,EAAE;YACjB,SAAS;YACT;QACF;QAEA,YAAY,MAAM,EAAE,EAAE;YAAE,QAAQ;QAAU;QAC1C,SAAS;QAET,IAAI;YACF,0BAA0B;YAC1B,MAAM,aAAa,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC;YAExC,IAAI,YAAY;gBACd,cAAc;gBACd,MAAM,eAAe,MAAM,MAAM,eAAe;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,GAAG,KAAK;wBACR,IAAI,UAAU,aAAa;oBAC7B;gBACF;gBAEA,MAAM,WAAW,MAAM,aAAa,IAAI;gBACxC,IAAI,CAAC,SAAS,OAAO,EAAE;oBACrB,YAAY,MAAM,EAAE,EAAE;wBAAE,QAAQ;oBAAQ;oBACxC,SAAS,SAAS,KAAK,IAAI;oBAC3B;gBACF;gBAEA,oBAAoB;gBACpB,MAAM,OAAO,SAAS,IAAI,CAAC,EAAE;gBAC7B,MAAM,QAAQ,MAAM,EAAE;gBACtB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,QAAQ;4BAAE,GAAG,CAAC;4BAAE,IAAI;wBAAK,IAAI;gBAExC,QAAQ;oBAAE,GAAG,KAAK;oBAAE,IAAI;gBAAK;YAC/B;YAEA,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,MAAM,EAAE,EAAE;oBAAE,QAAQ;gBAAY;YAC9C,OAAO;gBACL,YAAY,MAAM,EAAE,EAAE;oBAAE,QAAQ;gBAAQ;gBACxC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,MAAM,EAAE,EAAE;gBAAE,QAAQ;YAAQ;YACxC,SAAS;QACX;IACF;IAEA,WAAW;IACX,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,QAAQ;QAC3C,MAAM,WAAW,MAAM,QAAQ;QAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QACA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,YAAY;IACZ,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACjE,KAAK;gBAAW,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;oBAA6B,MAAM;;;;;;YAC5E,KAAK;gBAAS,qBAAO,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACvD;gBAAS,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QAC/D;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;wBAAoB,MAAM;;;;;;kCAC5C,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;oBAAM;;;;;;;YAKxC,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,eAAe,iBAC5D,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCAAG,WAAU;0CACX,gBAAgB;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,MAAM,MAAM;8EAC3B,6LAAC;oEAAG,WAAU;8EAA6B,MAAM,IAAI;;;;;;8EACrD,6LAAC;oEACC,SAAS,IAAM,yBAAyB,0BAA0B,MAAM,EAAE,GAAG,OAAO,MAAM,EAAE;oEAC5F,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,uNAAA,CAAA,UAAO;wEAAC,MAAM;;;;;;;;;;;;;;;;;sEAGnB,6LAAC,kLAAA,CAAA,SAAM;4DACL,SAAS,MAAM,OAAO;4DACtB,UAAU,CAAC;gEACT,YAAY,MAAM,EAAE,EAAE;oEAAE;gEAAQ;gEAChC,IAAI,WAAW,MAAM,MAAM,EAAE;oEAC3B,UAAU;wEAAE,GAAG,KAAK;wEAAE;oEAAQ;gEAChC;4DACF;4DACA,WAAW,GACT,MAAM,OAAO,GAAG,kBAAkB,cACnC,oJAAoJ,CAAC;sEAEtJ,cAAA,6LAAC;gEACC,WAAW,GACT,MAAM,OAAO,GAAG,kBAAkB,gBACnC,0EAA0E,CAAC;;;;;;;;;;;;;;;;;;;;;;0DAMpF,6LAAC;gDAAE,WAAU;0DAA8B,MAAM,WAAW;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,MAAM,QAAQ,KAAK,aAAa,SAAS;wDAC/C,OAAO,MAAM,MAAM;wDACnB,UAAU,CAAC,IAAM,YAAY,MAAM,EAAE,EAAE;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAChE,WAAU;wDACV,aAAa,MAAM,QAAQ,KAAK,aAAa,0CAA0C;;;;;;kEAEzF,6LAAC;wDACC,SAAS,IAAM,UAAU;wDACzB,UAAU,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK;wDAC5C,WAAU;wDACV,OAAM;kEAEL,MAAM,MAAM,KAAK,YAAY,QAAQ;;;;;;kEAExC,6LAAC;wDACC,SAAS,IAAM,UAAU;wDACzB,UAAU,CAAC,MAAM,MAAM;wDACvB,WAAU;wDACV,OAAM;kEACP;;;;;;;;;;;;4CAMF,0BAA0B,MAAM,EAAE,kBACjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,uNAAA,CAAA,UAAO;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;kEAIxC,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,MAAK;wEACL,OAAO,MAAM,WAAW,IAAI;wEAC5B,UAAU,CAAC,IAAM,YAAY,MAAM,EAAE,EAAE;gFAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;4EAAE;wEACjF,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;gFAAK,WAAU;0FAAe,MAAM,WAAW,IAAI;;;;;;0FACpD,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;0EAKV,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,MAAK;wEACL,OAAO,MAAM,SAAS,IAAI;wEAC1B,UAAU,CAAC,IAAM,YAAY,MAAM,EAAE,EAAE;gFAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;4EAAE;wEAC7E,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;4EAA6B;4EACrC,MAAM,SAAS,IAAI;4EAAK;;;;;;;;;;;;;0EAKjC,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACJ,MAAK;wEACL,OAAO,MAAM,IAAI,IAAI;wEACrB,UAAU,CAAC,IAAM,YAAY,MAAM,EAAE,EAAE;gFAAE,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4EAAE;wEAC1E,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;gFAAK,WAAU;0FAAe,MAAM,IAAI,IAAI;;;;;;0FAC7C,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;kEAMZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;;0FAAG,6LAAC;0FAAO;;;;;;4EAAqB;;;;;;;kFACjC,6LAAC;;0FAAG,6LAAC;0FAAO;;;;;;4EAAoB;;;;;;;kFAChC,6LAAC;;0FAAG,6LAAC;0FAAO;;;;;;4EAAe;;;;;;;;;;;;;;;;;;;kEAK/B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,SAAS;gEACP,UAAU;gEACV,yBAAyB;4DAC3B;4DACA,WAAU;4DACV,OAAM;sEACP;;;;;;;;;;;;;;;;;;uCAzJC,MAAM,EAAE;;;;;;;;;;;uBAPd;;;;;;;;;;;;;;;;AA8KpB;GAjewB;KAAA", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/models/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Settings, CheckCircle, AlertCircle } from 'lucide-react'\nimport Layout from '@/components/Layout'\nimport AIConfigPanel from '@/components/AIConfigPanel'\nimport { AIConfig } from '@/types'\n\nexport default function ModelsPage() {\n  const [savedConfig, setSavedConfig] = useState<AIConfig | null>(null)\n  const [showSuccess, setShowSuccess] = useState(false)\n\n  // 处理配置保存\n  const handleConfigSaved = (config: AIConfig) => {\n    setSavedConfig(config)\n    setShowSuccess(true)\n    \n    // 3秒后隐藏成功提示\n    setTimeout(() => {\n      setShowSuccess(false)\n    }, 3000)\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto space-y-8\">\n        {/* 页面头部 */}\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <Settings className=\"text-purple-600 mr-3\" size={32} />\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">模型配置</h1>\n              <p className=\"mt-1 text-sm text-gray-600\">\n                配置AI大模型，用于小说分析和视频脚本生成\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* 成功提示 */}\n        {showSuccess && (\n          <div className=\"rounded-md bg-green-50 p-4 border border-green-200\">\n            <div className=\"flex\">\n              <CheckCircle className=\"h-5 w-5 text-green-400\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-green-800\">\n                  配置保存成功！\n                </h3>\n                <div className=\"mt-2 text-sm text-green-700\">\n                  <p>AI模型配置已保存，现在可以开始创建项目并分析小说了。</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* AI配置面板 */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">\n              大模型配置\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              选择并配置您要使用的AI模型\n            </p>\n          </div>\n          \n          <div className=\"p-6\">\n            <AIConfigPanel onConfigSaved={handleConfigSaved} />\n          </div>\n        </div>\n\n        {/* 配置说明 */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-blue-400 mt-0.5\" />\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800 mb-2\">\n                配置说明\n              </h3>\n              <div className=\"text-sm text-blue-700 space-y-2\">\n                <p><strong>DeepSeek模型选择：</strong></p>\n                <ul className=\"list-disc list-inside ml-4 space-y-1\">\n                  <li><strong>DeepSeek Reasoner</strong> - 专业推理模型，适合小说分析、角色提取和剧情分析</li>\n                </ul>\n                <p className=\"mt-3\"><strong>豆包模型选择：</strong></p>\n                <ul className=\"list-disc list-inside ml-4 space-y-1\">\n                  <li><strong>豆包 Seedance 1.0 Pro</strong> - 专业视频生成模型，支持高质量文生视频</li>\n                </ul>\n                <p className=\"mt-3\"><strong>参数调整：</strong></p>\n                <ul className=\"list-disc list-inside ml-4 space-y-1\">\n                  <li><strong>温度(Temperature)</strong> - 控制输出的随机性，0.7为推荐值</li>\n                  <li><strong>最大Token数</strong> - 控制单次输出长度，4000为推荐值</li>\n                  <li><strong>Top P</strong> - 控制输出的多样性，0.9为推荐值</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 使用流程 */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">\n              使用流程\n            </h2>\n          </div>\n          \n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\">\n                    1\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">配置AI模型</h3>\n                  <p className=\"text-sm text-gray-600\">选择模型提供商，输入API密钥，测试连接</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\">\n                    2\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">创建项目</h3>\n                  <p className=\"text-sm text-gray-600\">在项目页面创建新项目，上传小说文件</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\">\n                    3\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">AI分析</h3>\n                  <p className=\"text-sm text-gray-600\">AI自动分析小说，提取角色和剧集信息</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\">\n                    4\n                  </div>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">生成视频脚本</h3>\n                  <p className=\"text-sm text-gray-600\">基于多维度信息生成专业视频脚本</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 当前配置状态 */}\n        {savedConfig && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">\n                当前配置\n              </h2>\n            </div>\n            \n            <div className=\"p-6\">\n              <dl className=\"grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2\">\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">提供商</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{savedConfig.provider}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">模型</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{savedConfig.model}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">温度</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{savedConfig.temperature}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">最大Token数</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">{savedConfig.maxTokens}</dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">状态</dt>\n                  <dd className=\"mt-1\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      savedConfig.status === 'connected' \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-yellow-100 text-yellow-800'\n                    }`}>\n                      {savedConfig.status === 'connected' ? '已连接' : '未测试'}\n                    </span>\n                  </dd>\n                </div>\n                <div>\n                  <dt className=\"text-sm font-medium text-gray-500\">最后更新</dt>\n                  <dd className=\"mt-1 text-sm text-gray-900\">\n                    {new Date(savedConfig.updatedAt).toLocaleString('zh-CN')}\n                  </dd>\n                </div>\n              </dl>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,eAAe;QACf,eAAe;QAEf,YAAY;QACZ,WAAW;YACT,eAAe;QACjB,GAAG;IACL;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;8BACC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAuB,MAAM;;;;;;0CACjD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;gBAQ/C,6BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,sIAAA,CAAA,UAAa;gCAAC,eAAe;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAE,cAAA,6LAAC;8DAAO;;;;;;;;;;;0DACX,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;;sEAAG,6LAAC;sEAAO;;;;;;wDAA0B;;;;;;;;;;;;0DAExC,6LAAC;gDAAE,WAAU;0DAAO,cAAA,6LAAC;8DAAO;;;;;;;;;;;0DAC5B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;;sEAAG,6LAAC;sEAAO;;;;;;wDAA4B;;;;;;;;;;;;0DAE1C,6LAAC;gDAAE,WAAU;0DAAO,cAAA,6LAAC;8DAAO;;;;;;;;;;;0DAC5B,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;;0EAAG,6LAAC;0EAAO;;;;;;4DAAwB;;;;;;;kEACpC,6LAAC;;0EAAG,6LAAC;0EAAO;;;;;;4DAAiB;;;;;;;kEAC7B,6LAAC;;0EAAG,6LAAC;0EAAO;;;;;;4DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;sCAKpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAA0G;;;;;;;;;;;0DAI3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAA0G;;;;;;;;;;;0DAI3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAA0G;;;;;;;;;;;0DAI3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAA0G;;;;;;;;;;;0DAI3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9C,6BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;sCAKpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DAA8B,YAAY,QAAQ;;;;;;;;;;;;kDAElE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DAA8B,YAAY,KAAK;;;;;;;;;;;;kDAE/D,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DAA8B,YAAY,WAAW;;;;;;;;;;;;kDAErE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DAA8B,YAAY,SAAS;;;;;;;;;;;;kDAEnE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,wEAAwE,EACxF,YAAY,MAAM,KAAK,cACnB,gCACA,iCACJ;8DACC,YAAY,MAAM,KAAK,cAAc,QAAQ;;;;;;;;;;;;;;;;;kDAIpD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAG,WAAU;0DACX,IAAI,KAAK,YAAY,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;GA7MwB;KAAA", "debugId": null}}]}