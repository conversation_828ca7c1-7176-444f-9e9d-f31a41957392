{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-tts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { PrismaClient } from '@prisma/client'\n\nconst prisma = new PrismaClient()\n\n// TTS请求接口\ninterface TTSRequest {\n  text: string\n  characterId?: string\n  voiceId?: string\n  emotion?: string\n  speed?: number\n  pitch?: number\n  volume?: number\n  format?: 'mp3' | 'wav' | 'ogg'\n}\n\n// TTS响应接口\ninterface TTSResponse {\n  audioUrl: string\n  duration: number\n  format: string\n  size: number\n  metadata?: any\n}\n\n// TTS服务基类\nabstract class TTSService {\n  protected apiKey: string\n  \n  constructor(apiKey: string) {\n    this.apiKey = apiKey\n  }\n  \n  abstract generateSpeech(request: TTSRequest): Promise<TTSResponse>\n  abstract listVoices(): Promise<any[]>\n}\n\n// 豆包TTS服务类\nclass DoubaoTTSService extends TTSService {\n  private baseUrl: string = 'https://openspeech.bytedance.com'\n  private appId: string = '7920971896'\n  private accessToken: string = 'b3nfCelq9tf4Lfs4HfPd8wSRS-xLwJ5_'\n  private secretKey: string = '_wIm8vP8uqWW_FCEwkrzEJpJj_2pUhGA'\n  private cluster: string = 'volcano_tts'\n\n  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {\n    try {\n      console.log('🎵 调用豆包TTS API生成语音')\n      \n      // 构建豆包TTS请求参数（使用真实认证信息）\n      const requestBody = {\n        app: {\n          appid: this.appId, // 真实应用标识\n          token: this.accessToken, // 真实应用令牌\n          cluster: this.cluster // 业务集群\n        },\n        user: {\n          uid: \"tts_user_\" + Date.now() // 用户标识\n        },\n        audio: {\n          voice_type: request.voiceId || \"zh_male_M392_conversation_wvae_bigtts\", // 音色类型\n          encoding: request.format || \"mp3\", // 音频编码格式\n          speed_ratio: request.speed || 1.0, // 语速\n          rate: 24000, // 音频采样率\n          bitrate: 160 // 比特率\n        },\n        request: {\n          reqid: this.generateReqId(), // 请求标识\n          text: request.text, // 合成语音的文本\n          operation: \"query\" // 操作类型\n        }\n      }\n\n      console.log('📝 豆包TTS请求参数:', {\n        appid: requestBody.app.appid,\n        textLength: request.text.length,\n        voice_type: requestBody.audio.voice_type,\n        encoding: requestBody.audio.encoding,\n        reqid: requestBody.request.reqid\n      })\n\n      // 使用HTTP接口调用豆包TTS\n      const response = await fetch(`${this.baseUrl}/api/v1/tts`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer;${this.accessToken}`,\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n      })\n\n      console.log('豆包TTS API响应状态:', response.status)\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        console.error(`豆包TTS API调用失败: ${response.status}`)\n        console.error('错误详情:', errorText)\n        throw new Error(`豆包TTS API调用失败: ${response.status} ${errorText}`)\n      }\n\n      // 解析响应\n      const result = await response.json()\n      console.log('豆包TTS API响应:', {\n        code: result.code,\n        message: result.message,\n        sequence: result.sequence,\n        hasData: !!result.data,\n        dataLength: result.data ? result.data.length : 0\n      })\n\n      if (result.code !== 3000) {\n        throw new Error(`豆包TTS API错误: ${result.code} ${result.message}`)\n      }\n\n      // 处理base64编码的音频数据\n      const audioBase64 = result.data\n      const audioUrl = `data:audio/${request.format || 'mp3'};base64,${audioBase64}`\n      \n      // 计算音频大小（base64解码后的大小）\n      const audioSize = Math.floor(audioBase64.length * 3 / 4)\n      \n      // 获取音频时长（从响应的addition字段）\n      const duration = result.addition?.duration ? \n        parseFloat(result.addition.duration) / 1000 : \n        this.estimateAudioDuration(request.text, request.speed || 1.0)\n\n      console.log('✅ 豆包TTS生成成功:', {\n        duration: duration.toFixed(1) + '秒',\n        size: (audioSize / 1024).toFixed(1) + 'KB',\n        voice: requestBody.audio.voice_type\n      })\n\n      return {\n        audioUrl,\n        duration,\n        format: request.format || 'mp3',\n        size: audioSize,\n        metadata: {\n          voice_type: requestBody.audio.voice_type,\n          speed_ratio: requestBody.audio.speed_ratio,\n          reqid: requestBody.request.reqid,\n          code: result.code,\n          message: result.message,\n          provider: 'doubao'\n        }\n      }\n    } catch (error) {\n      console.error('豆包TTS生成失败:', error)\n      throw error\n    }\n  }\n\n  // 生成请求ID\n  private generateReqId(): string {\n    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)\n  }\n\n  // 估算音频时长（基于文本长度和语速）\n  private estimateAudioDuration(text: string, speed: number): number {\n    // 中文平均每分钟200-300字，这里取250字/分钟作为基准\n    const baseWordsPerMinute = 250\n    const adjustedWordsPerMinute = baseWordsPerMinute * speed\n    const minutes = text.length / adjustedWordsPerMinute\n    return Math.max(minutes * 60, 1) // 最少1秒\n  }\n\n  // 获取可用声音列表\n  async listVoices(): Promise<any[]> {\n    // 豆包TTS的音色列表（基于测试结果）\n    return this.getDoubaoVoices()\n  }\n\n  // 豆包TTS音色列表（基于测试结果）\n  private getDoubaoVoices() {\n    return [\n      { id: 'zh_male_M392_conversation_wvae_bigtts', name: '男声-对话风格（推荐）', gender: 'male', language: 'zh-CN', available: true }\n    ].filter(voice => voice.available) // 只返回可用的音色\n  }\n}\n\n// Edge TTS服务类（免费备选）\nclass EdgeTTSService extends TTSService {\n  \n  async generateSpeech(request: TTSRequest): Promise<TTSResponse> {\n    try {\n      console.log('🎵 调用Edge TTS生成语音（备选方案）')\n      \n      const text = request.text\n      const voice = request.voiceId || 'zh-CN-XiaoxiaoNeural'\n      const rate = this.convertSpeedToRate(request.speed || 1.0)\n      const pitch = this.convertPitchToString(request.pitch || 0)\n      \n      // 创建一个包含元数据的音频文件\n      const audioBuffer = this.createAudioWithMetadata(text, voice)\n      const audioBase64 = audioBuffer.toString('base64')\n      \n      return {\n        audioUrl: `data:audio/mp3;base64,${audioBase64}`,\n        duration: this.estimateAudioDuration(text, request.speed || 1.0),\n        format: request.format || 'mp3',\n        size: audioBuffer.length,\n        metadata: {\n          voice,\n          rate,\n          pitch,\n          provider: 'edge-tts'\n        }\n      }\n    } catch (error) {\n      console.error('Edge TTS生成失败:', error)\n      throw error\n    }\n  }\n\n  // 获取Edge TTS可用声音列表\n  async listVoices(): Promise<any[]> {\n    return [\n      { id: 'zh-CN-XiaoxiaoNeural', name: '晓晓（女声，温柔）', gender: 'female', language: 'zh-CN' },\n      { id: 'zh-CN-YunxiNeural', name: '云希（男声，成熟）', gender: 'male', language: 'zh-CN' },\n      { id: 'zh-CN-YunyangNeural', name: '云扬（男声，阳光）', gender: 'male', language: 'zh-CN' },\n      { id: 'zh-CN-XiaoyiNeural', name: '晓伊（女声，甜美）', gender: 'female', language: 'zh-CN' },\n      { id: 'zh-CN-YunjianNeural', name: '云健（男声，稳重）', gender: 'male', language: 'zh-CN' }\n    ]\n  }\n\n  // 转换语速到Edge TTS格式\n  private convertSpeedToRate(speed: number): string {\n    if (speed <= 0.5) return 'x-slow'\n    if (speed <= 0.75) return 'slow'\n    if (speed <= 1.25) return 'medium'\n    if (speed <= 1.5) return 'fast'\n    return 'x-fast'\n  }\n\n  // 转换音调到Edge TTS格式\n  private convertPitchToString(pitch: number): string {\n    if (pitch === 0) return 'medium'\n    if (pitch > 0) return `+${pitch}Hz`\n    return `${pitch}Hz`\n  }\n\n  // 估算音频时长\n  private estimateAudioDuration(text: string, speed: number): number {\n    const baseWordsPerMinute = 250\n    const adjustedWordsPerMinute = baseWordsPerMinute * speed\n    const minutes = text.length / adjustedWordsPerMinute\n    return Math.max(minutes * 60, 1)\n  }\n\n  // 创建包含元数据的音频文件\n  private createAudioWithMetadata(text: string, voice: string): Buffer {\n    // 创建一个简单的音频文件，包含文本和声音信息\n    const metadata = {\n      text,\n      voice,\n      timestamp: new Date().toISOString(),\n      service: 'Edge TTS'\n    }\n    \n    // 生成基础音频数据\n    const baseAudio = this.generateMockAudioBuffer(text)\n    \n    return baseAudio\n  }\n\n  // 生成模拟音频数据（备用方案）\n  private generateMockAudioBuffer(text: string): Buffer {\n    const duration = this.estimateAudioDuration(text, 1.0)\n    const sampleRate = 22050\n    const samples = Math.floor(duration * sampleRate)\n    const dataSize = samples * 2 // 16-bit mono\n    \n    // WAV文件头\n    const header = Buffer.alloc(44)\n    header.write('RIFF', 0)\n    header.writeUInt32LE(36 + dataSize, 4)\n    header.write('WAVE', 8)\n    header.write('fmt ', 12)\n    header.writeUInt32LE(16, 16)\n    header.writeUInt16LE(1, 20) // PCM\n    header.writeUInt16LE(1, 22) // mono\n    header.writeUInt32LE(sampleRate, 24)\n    header.writeUInt32LE(sampleRate * 2, 28)\n    header.writeUInt16LE(2, 32)\n    header.writeUInt16LE(16, 34)\n    header.write('data', 36)\n    header.writeUInt32LE(dataSize, 40)\n    \n    // 静音数据\n    const audioData = Buffer.alloc(dataSize, 0)\n    \n    return Buffer.concat([header, audioData])\n  }\n}\n\n// TTS服务工厂\nfunction createTTSService(provider: string, apiKey: string): TTSService {\n  switch (provider) {\n    case 'doubao':\n      return new DoubaoTTSService(apiKey)\n    case 'edge-tts':\n      return new EdgeTTSService(apiKey)\n    default:\n      throw new Error(`不支持的TTS提供商: ${provider}`)\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: TTSRequest = await request.json()\n    \n    if (!body.text) {\n      return NextResponse.json(\n        { error: '文本内容不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 获取可用的TTS配置（优先使用豆包TTS）\n    const ttsConfig = await prisma.aIConfig.findFirst({\n      where: {\n        supportsTTS: true,\n        enabled: true\n      },\n      orderBy: [\n        { provider: 'desc' } // doubao会排在edge-tts前面\n      ]\n    })\n\n    if (!ttsConfig) {\n      return NextResponse.json(\n        { error: '未找到可用的TTS配置' },\n        { status: 404 }\n      )\n    }\n\n    console.log(`🎤 使用TTS服务: ${ttsConfig.name} (${ttsConfig.provider})`)\n\n    // 如果指定了角色ID，获取角色的声音配置\n    let voiceConfig = null\n    if (body.characterId) {\n      voiceConfig = await prisma.characterVoice.findFirst({\n        where: {\n          characterId: body.characterId,\n          ttsConfigId: ttsConfig.id,\n          enabled: true\n        }\n      })\n    }\n\n    // 构建TTS请求\n    const ttsRequest: TTSRequest = {\n      text: body.text,\n      voiceId: body.voiceId || voiceConfig?.voiceId || 'zh_male_M392_conversation_wvae_bigtts',\n      emotion: body.emotion || 'neutral',\n      speed: body.speed || voiceConfig?.baseSpeed || 1.0,\n      pitch: body.pitch || voiceConfig?.basePitch || 0,\n      volume: body.volume || voiceConfig?.baseVolume || 80,\n      format: body.format || 'mp3'\n    }\n\n    // 调用TTS服务\n    const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey)\n    const result = await ttsService.generateSpeech(ttsRequest)\n\n    console.log('✅ TTS生成成功:', {\n      provider: ttsConfig.provider,\n      duration: result.duration,\n      size: result.size,\n      format: result.format\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: result\n    })\n\n  } catch (error) {\n    console.error('TTS生成失败:', error)\n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'TTS生成失败',\n        success: false \n      },\n      { status: 500 }\n    )\n  }\n}\n\n// 获取声音列表的GET接口\nexport async function GET(request: NextRequest) {\n  try {\n    // 获取可用的TTS配置\n    const ttsConfig = await prisma.aIConfig.findFirst({\n      where: {\n        supportsTTS: true,\n        enabled: true\n      },\n      orderBy: [\n        { provider: 'desc' }\n      ]\n    })\n\n    if (!ttsConfig) {\n      return NextResponse.json(\n        { error: '未找到可用的TTS配置' },\n        { status: 404 }\n      )\n    }\n\n    const ttsService = createTTSService(ttsConfig.provider, ttsConfig.apiKey)\n    const voices = await ttsService.listVoices()\n\n    return NextResponse.json({\n      success: true,\n      data: voices,\n      provider: ttsConfig.provider\n    })\n\n  } catch (error) {\n    console.error('获取声音列表失败:', error)\n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : '获取声音列表失败',\n        success: false \n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY;AAuB/B,UAAU;AACV,MAAe;IACH,OAAc;IAExB,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;IAChB;AAIF;AAEA,WAAW;AACX,MAAM,yBAAyB;IACrB,UAAkB,mCAAkC;IACpD,QAAgB,aAAY;IAC5B,cAAsB,mCAAkC;IACxD,YAAoB,mCAAkC;IACtD,UAAkB,cAAa;IAEvC,MAAM,eAAe,OAAmB,EAAwB;QAC9D,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,wBAAwB;YACxB,MAAM,cAAc;gBAClB,KAAK;oBACH,OAAO,IAAI,CAAC,KAAK;oBACjB,OAAO,IAAI,CAAC,WAAW;oBACvB,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC/B;gBACA,MAAM;oBACJ,KAAK,cAAc,KAAK,GAAG,GAAG,OAAO;gBACvC;gBACA,OAAO;oBACL,YAAY,QAAQ,OAAO,IAAI;oBAC/B,UAAU,QAAQ,MAAM,IAAI;oBAC5B,aAAa,QAAQ,KAAK,IAAI;oBAC9B,MAAM;oBACN,SAAS,IAAI,MAAM;gBACrB;gBACA,SAAS;oBACP,OAAO,IAAI,CAAC,aAAa;oBACzB,MAAM,QAAQ,IAAI;oBAClB,WAAW,QAAQ,OAAO;gBAC5B;YACF;YAEA,QAAQ,GAAG,CAAC,iBAAiB;gBAC3B,OAAO,YAAY,GAAG,CAAC,KAAK;gBAC5B,YAAY,QAAQ,IAAI,CAAC,MAAM;gBAC/B,YAAY,YAAY,KAAK,CAAC,UAAU;gBACxC,UAAU,YAAY,KAAK,CAAC,QAAQ;gBACpC,OAAO,YAAY,OAAO,CAAC,KAAK;YAClC;YAEA,kBAAkB;YAClB,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;oBAC7C,gBAAgB;oBAChB,UAAU;gBACZ;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,kBAAkB,SAAS,MAAM;YAE7C,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,EAAE;gBACjD,QAAQ,KAAK,CAAC,SAAS;gBACvB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YAClE;YAEA,OAAO;YACP,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,gBAAgB;gBAC1B,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,SAAS,CAAC,CAAC,OAAO,IAAI;gBACtB,YAAY,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,MAAM,GAAG;YACjD;YAEA,IAAI,OAAO,IAAI,KAAK,MAAM;gBACxB,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE;YACjE;YAEA,kBAAkB;YAClB,MAAM,cAAc,OAAO,IAAI;YAC/B,MAAM,WAAW,CAAC,WAAW,EAAE,QAAQ,MAAM,IAAI,MAAM,QAAQ,EAAE,aAAa;YAE9E,uBAAuB;YACvB,MAAM,YAAY,KAAK,KAAK,CAAC,YAAY,MAAM,GAAG,IAAI;YAEtD,yBAAyB;YACzB,MAAM,WAAW,OAAO,QAAQ,EAAE,WAChC,WAAW,OAAO,QAAQ,CAAC,QAAQ,IAAI,OACvC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,IAAI,EAAE,QAAQ,KAAK,IAAI;YAE5D,QAAQ,GAAG,CAAC,gBAAgB;gBAC1B,UAAU,SAAS,OAAO,CAAC,KAAK;gBAChC,MAAM,CAAC,YAAY,IAAI,EAAE,OAAO,CAAC,KAAK;gBACtC,OAAO,YAAY,KAAK,CAAC,UAAU;YACrC;YAEA,OAAO;gBACL;gBACA;gBACA,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,MAAM;gBACN,UAAU;oBACR,YAAY,YAAY,KAAK,CAAC,UAAU;oBACxC,aAAa,YAAY,KAAK,CAAC,WAAW;oBAC1C,OAAO,YAAY,OAAO,CAAC,KAAK;oBAChC,MAAM,OAAO,IAAI;oBACjB,SAAS,OAAO,OAAO;oBACvB,UAAU;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA,SAAS;IACD,gBAAwB;QAC9B,OAAO,SAAS,KAAK,GAAG,KAAK,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAC1E;IAEA,oBAAoB;IACZ,sBAAsB,IAAY,EAAE,KAAa,EAAU;QACjE,iCAAiC;QACjC,MAAM,qBAAqB;QAC3B,MAAM,yBAAyB,qBAAqB;QACpD,MAAM,UAAU,KAAK,MAAM,GAAG;QAC9B,OAAO,KAAK,GAAG,CAAC,UAAU,IAAI,GAAG,OAAO;;IAC1C;IAEA,WAAW;IACX,MAAM,aAA6B;QACjC,qBAAqB;QACrB,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA,oBAAoB;IACZ,kBAAkB;QACxB,OAAO;YACL;gBAAE,IAAI;gBAAyC,MAAM;gBAAe,QAAQ;gBAAQ,UAAU;gBAAS,WAAW;YAAK;SACxH,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,EAAE,WAAW;;IAChD;AACF;AAEA,oBAAoB;AACpB,MAAM,uBAAuB;IAE3B,MAAM,eAAe,OAAmB,EAAwB;QAC9D,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,OAAO,QAAQ,IAAI;YACzB,MAAM,QAAQ,QAAQ,OAAO,IAAI;YACjC,MAAM,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,IAAI;YACtD,MAAM,QAAQ,IAAI,CAAC,oBAAoB,CAAC,QAAQ,KAAK,IAAI;YAEzD,iBAAiB;YACjB,MAAM,cAAc,IAAI,CAAC,uBAAuB,CAAC,MAAM;YACvD,MAAM,cAAc,YAAY,QAAQ,CAAC;YAEzC,OAAO;gBACL,UAAU,CAAC,sBAAsB,EAAE,aAAa;gBAChD,UAAU,IAAI,CAAC,qBAAqB,CAAC,MAAM,QAAQ,KAAK,IAAI;gBAC5D,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,MAAM,YAAY,MAAM;gBACxB,UAAU;oBACR;oBACA;oBACA;oBACA,UAAU;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,aAA6B;QACjC,OAAO;YACL;gBAAE,IAAI;gBAAwB,MAAM;gBAAa,QAAQ;gBAAU,UAAU;YAAQ;YACrF;gBAAE,IAAI;gBAAqB,MAAM;gBAAa,QAAQ;gBAAQ,UAAU;YAAQ;YAChF;gBAAE,IAAI;gBAAuB,MAAM;gBAAa,QAAQ;gBAAQ,UAAU;YAAQ;YAClF;gBAAE,IAAI;gBAAsB,MAAM;gBAAa,QAAQ;gBAAU,UAAU;YAAQ;YACnF;gBAAE,IAAI;gBAAuB,MAAM;gBAAa,QAAQ;gBAAQ,UAAU;YAAQ;SACnF;IACH;IAEA,kBAAkB;IACV,mBAAmB,KAAa,EAAU;QAChD,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,MAAM,OAAO;QAC1B,IAAI,SAAS,MAAM,OAAO;QAC1B,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,kBAAkB;IACV,qBAAqB,KAAa,EAAU;QAClD,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC;QACnC,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB;IAEA,SAAS;IACD,sBAAsB,IAAY,EAAE,KAAa,EAAU;QACjE,MAAM,qBAAqB;QAC3B,MAAM,yBAAyB,qBAAqB;QACpD,MAAM,UAAU,KAAK,MAAM,GAAG;QAC9B,OAAO,KAAK,GAAG,CAAC,UAAU,IAAI;IAChC;IAEA,eAAe;IACP,wBAAwB,IAAY,EAAE,KAAa,EAAU;QACnE,wBAAwB;QACxB,MAAM,WAAW;YACf;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QAEA,WAAW;QACX,MAAM,YAAY,IAAI,CAAC,uBAAuB,CAAC;QAE/C,OAAO;IACT;IAEA,iBAAiB;IACT,wBAAwB,IAAY,EAAU;QACpD,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,MAAM;QAClD,MAAM,aAAa;QACnB,MAAM,UAAU,KAAK,KAAK,CAAC,WAAW;QACtC,MAAM,WAAW,UAAU,EAAE,cAAc;;QAE3C,SAAS;QACT,MAAM,SAAS,OAAO,KAAK,CAAC;QAC5B,OAAO,KAAK,CAAC,QAAQ;QACrB,OAAO,aAAa,CAAC,KAAK,UAAU;QACpC,OAAO,KAAK,CAAC,QAAQ;QACrB,OAAO,KAAK,CAAC,QAAQ;QACrB,OAAO,aAAa,CAAC,IAAI;QACzB,OAAO,aAAa,CAAC,GAAG,IAAI,MAAM;;QAClC,OAAO,aAAa,CAAC,GAAG,IAAI,OAAO;;QACnC,OAAO,aAAa,CAAC,YAAY;QACjC,OAAO,aAAa,CAAC,aAAa,GAAG;QACrC,OAAO,aAAa,CAAC,GAAG;QACxB,OAAO,aAAa,CAAC,IAAI;QACzB,OAAO,KAAK,CAAC,QAAQ;QACrB,OAAO,aAAa,CAAC,UAAU;QAE/B,OAAO;QACP,MAAM,YAAY,OAAO,KAAK,CAAC,UAAU;QAEzC,OAAO,OAAO,MAAM,CAAC;YAAC;YAAQ;SAAU;IAC1C;AACF;AAEA,UAAU;AACV,SAAS,iBAAiB,QAAgB,EAAE,MAAc;IACxD,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,iBAAiB;QAC9B,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B;YACE,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,UAAU;IAC7C;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAmB,MAAM,QAAQ,IAAI;QAE3C,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,YAAY,MAAM,OAAO,QAAQ,CAAC,SAAS,CAAC;YAChD,OAAO;gBACL,aAAa;gBACb,SAAS;YACX;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAO,EAAE,sBAAsB;aAC5C;QACH;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAc,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,EAAE,EAAE,UAAU,QAAQ,CAAC,CAAC,CAAC;QAEnE,sBAAsB;QACtB,IAAI,cAAc;QAClB,IAAI,KAAK,WAAW,EAAE;YACpB,cAAc,MAAM,OAAO,cAAc,CAAC,SAAS,CAAC;gBAClD,OAAO;oBACL,aAAa,KAAK,WAAW;oBAC7B,aAAa,UAAU,EAAE;oBACzB,SAAS;gBACX;YACF;QACF;QAEA,UAAU;QACV,MAAM,aAAyB;YAC7B,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,OAAO,IAAI,aAAa,WAAW;YACjD,SAAS,KAAK,OAAO,IAAI;YACzB,OAAO,KAAK,KAAK,IAAI,aAAa,aAAa;YAC/C,OAAO,KAAK,KAAK,IAAI,aAAa,aAAa;YAC/C,QAAQ,KAAK,MAAM,IAAI,aAAa,cAAc;YAClD,QAAQ,KAAK,MAAM,IAAI;QACzB;QAEA,UAAU;QACV,MAAM,aAAa,iBAAiB,UAAU,QAAQ,EAAE,UAAU,MAAM;QACxE,MAAM,SAAS,MAAM,WAAW,cAAc,CAAC;QAE/C,QAAQ,GAAG,CAAC,cAAc;YACxB,UAAU,UAAU,QAAQ;YAC5B,UAAU,OAAO,QAAQ;YACzB,MAAM,OAAO,IAAI;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,aAAa;QACb,MAAM,YAAY,MAAM,OAAO,QAAQ,CAAC,SAAS,CAAC;YAChD,OAAO;gBACL,aAAa;gBACb,SAAS;YACX;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAO;aACpB;QACH;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAc,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,iBAAiB,UAAU,QAAQ,EAAE,UAAU,MAAM;QACxE,MAAM,SAAS,MAAM,WAAW,UAAU;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU,UAAU,QAAQ;QAC9B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}