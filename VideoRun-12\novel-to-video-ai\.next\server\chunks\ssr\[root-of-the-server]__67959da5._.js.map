{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Home,\n  FolderOpen,\n  Settings,\n  User,\n  Menu,\n  X,\n  Sparkles\n} from 'lucide-react'\n\ninterface LayoutProps {\n  children: React.ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navigation = [\n    {\n      name: '首页',\n      href: '/',\n      icon: Home,\n      current: pathname === '/'\n    },\n    {\n      name: '项目',\n      href: '/projects',\n      icon: FolderOpen,\n      current: pathname.startsWith('/projects')\n    },\n    {\n      name: '模型配置',\n      href: '/models',\n      icon: Settings,\n      current: pathname === '/models'\n    },\n    {\n      name: '账户',\n      href: '/account',\n      icon: User,\n      current: pathname === '/account',\n      disabled: true\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* 左侧 Logo */}\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <Sparkles className=\"text-purple-600 mr-2\" size={24} />\n                <span className=\"text-lg font-semibold text-gray-900 whitespace-nowrap\">\n                  灵犀Ai——小说转视频神器\n                </span>\n              </Link>\n            </div>\n\n            {/* 右侧导航菜单 */}\n            <div className=\"hidden md:flex md:items-center md:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    inline-flex items-center text-lg font-semibold\n                    ${item.current\n                      ? 'text-purple-600'\n                      : 'text-gray-900 hover:text-purple-600'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                >\n                  <item.icon className=\"mr-2\" size={20} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden flex items-center\">\n              <button\n                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <Menu size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端菜单 */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    flex items-center px-3 py-2 text-sm font-medium border-l-4\n                    ${item.current\n                      ? 'bg-purple-50 border-purple-500 text-purple-700'\n                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <item.icon className=\"mr-2\" size={16} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* 主内容区域 */}\n      <main className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAmBe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;YACV,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,SAAS,aAAa;YACtB,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,MAAM;;;;;;0DACjD,8OAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;;;;;;8CAO5E,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,oBACA,sCACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;;8DAED,8OAAC,KAAK,IAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDACjC,KAAK,IAAI;;2CAZL,KAAK,IAAI;;;;;;;;;;8CAkBpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,mDACA,wEACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;oCACD,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC,KAAK,IAAI;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCACjC,KAAK,IAAI;;mCAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BAsB1B,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/projects/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport {\n  Plus,\n  FolderOpen,\n  Calendar,\n  FileText,\n  Users,\n  Film,\n  Video,\n  MoreVertical,\n  Trash2,\n  Edit,\n  X\n} from 'lucide-react'\nimport Layout from '@/components/Layout'\nimport { Project } from '@/types'\n\nexport default function ProjectsPage() {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // 加载项目列表\n  useEffect(() => {\n    loadProjects()\n  }, [])\n\n  const loadProjects = async () => {\n    try {\n      const response = await fetch('/api/projects')\n      const data = await response.json()\n      \n      if (data.success) {\n        setProjects(data.data)\n      } else {\n        throw new Error(data.error || '加载项目失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '加载项目失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 删除项目\n  const handleDeleteProject = async (projectId: string) => {\n    if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {\n      return\n    }\n\n    try {\n      const response = await fetch(`/api/projects/${projectId}`, {\n        method: 'DELETE'\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setProjects(prev => prev.filter(p => p.id !== projectId))\n      } else {\n        throw new Error(data.error || '删除项目失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '删除项目失败')\n    }\n  }\n\n  // 获取状态显示\n  const getStatusDisplay = (status: string) => {\n    const statusMap = {\n      created: { text: '已创建', color: 'bg-gray-100 text-gray-800' },\n      uploaded: { text: '已上传', color: 'bg-blue-100 text-blue-800' },\n      analyzing: { text: '分析中', color: 'bg-yellow-100 text-yellow-800' },\n      completed: { text: '已完成', color: 'bg-green-100 text-green-800' }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.created\n  }\n\n  // 格式化日期\n  const formatDate = (date: Date | string) => {\n    return new Date(date).toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">加载项目中...</p>\n          </div>\n        </div>\n      </Layout>\n    )\n  }\n\n  return (\n    <Layout>\n      <div className=\"space-y-6\">\n        {/* 页面头部 */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">项目管理</h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              管理您的小说转视频项目\n            </p>\n          </div>\n          <Link\n            href=\"/projects/new\"\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\"\n          >\n            <Plus className=\"mr-2\" size={16} />\n            新建项目\n          </Link>\n        </div>\n\n        {/* 错误信息 */}\n        {error && (\n          <div className=\"rounded-md bg-red-50 p-4\">\n            <div className=\"text-sm text-red-700\">\n              {error}\n              <button\n                onClick={() => setError(null)}\n                className=\"ml-2 p-1 text-red-800 hover:text-red-900 rounded-full hover:bg-red-100 transition-colors\"\n                title=\"关闭\"\n              >\n                <X size={16} />\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* 项目列表 */}\n        {projects.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <FolderOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无项目</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              开始创建您的第一个小说转视频项目\n            </p>\n            <div className=\"mt-6\">\n              <Link\n                href=\"/projects/new\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\"\n              >\n                <Plus className=\"mr-2\" size={16} />\n                新建项目\n              </Link>\n            </div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {projects.map((project) => {\n              const statusDisplay = getStatusDisplay(project.status)\n              \n              return (\n                <Link\n                  key={project.id}\n                  href={`/projects/${project.id}`}\n                  className=\"relative group bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow block\"\n                >\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <FolderOpen className=\"text-purple-600 mr-3\" size={20} />\n                        <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                          {project.name}\n                        </h3>\n                      </div>\n                      <div className=\"relative\">\n                        <button\n                          onClick={(e) => {\n                            e.preventDefault()\n                            e.stopPropagation()\n                          }}\n                          className=\"p-1 text-gray-400 hover:text-gray-600\"\n                        >\n                          <MoreVertical size={16} />\n                        </button>\n                      </div>\n                    </div>\n\n                    {project.description && (\n                      <p className=\"mt-2 text-sm text-gray-600 line-clamp-2\">\n                        {project.description}\n                      </p>\n                    )}\n\n                    <div className=\"mt-4 flex items-center justify-between\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>\n                        {statusDisplay.text}\n                      </span>\n                      <div className=\"flex items-center text-sm text-gray-500\">\n                        <Calendar size={14} className=\"mr-1\" />\n                        {formatDate(project.createdAt)}\n                      </div>\n                    </div>\n\n                    <div className=\"mt-4 flex justify-between\">\n                      <span className=\"text-sm font-medium text-purple-600\">\n                        查看详情 →\n                      </span>\n                      <button\n                        onClick={(e) => {\n                          e.preventDefault()\n                          e.stopPropagation()\n                          handleDeleteProject(project.id)\n                        }}\n                        className=\"text-sm text-red-600 hover:text-red-500\"\n                      >\n                        <Trash2 size={14} />\n                      </button>\n                    </div>\n                  </div>\n                </Link>\n              )\n            })}\n          </div>\n        )}\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAjBA;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;YACvB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,wBAAwB;YACnC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY;YAChB,SAAS;gBAAE,MAAM;gBAAO,OAAO;YAA4B;YAC3D,UAAU;gBAAE,MAAM;gBAAO,OAAO;YAA4B;YAC5D,WAAW;gBAAE,MAAM;gBAAO,OAAO;YAAgC;YACjE,WAAW;gBAAE,MAAM;gBAAO,OAAO;YAA8B;QACjE;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;;;;;;;gBAMtC,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCACC,SAAS,IAAM,SAAS;gCACxB,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gBAOhB,SAAS,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;yCAMzC,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,gBAAgB,iBAAiB,QAAQ,MAAM;wBAErD,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;4BAC/B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;wDAAuB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEACX,QAAQ,IAAI;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,cAAc;wDAChB,EAAE,eAAe;oDACnB;oDACA,WAAU;8DAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;oCAKzB,QAAQ,WAAW,kBAClB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EAAE,cAAc,KAAK,EAAE;0DAC9G,cAAc,IAAI;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAC7B,WAAW,QAAQ,SAAS;;;;;;;;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;0DAGtD,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,oBAAoB,QAAQ,EAAE;gDAChC;gDACA,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;2BArDf,QAAQ,EAAE;;;;;oBA2DrB;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}