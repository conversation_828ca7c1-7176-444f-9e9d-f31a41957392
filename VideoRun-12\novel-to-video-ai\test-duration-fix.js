const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDurationFix() {
  try {
    console.log('🧪 测试时长修复功能...\n');
    
    // 1. 检查雪夜初逢剧集
    const episode = await prisma.episode.findFirst({
      where: { title: '雪夜初逢' }
    });
    
    if (!episode) {
      console.log('❌ 未找到"雪夜初逢"剧集');
      return;
    }
    
    console.log(`✅ 找到剧集: ${episode.title}`);
    console.log(`   剧集ID: ${episode.id}`);
    
    // 2. 模拟API调用生成视频
    console.log('\n📡 模拟视频生成API调用...');
    
    const testData = {
      episodeId: episode.id,
      plotInfo: {
        generatedPrompt: '李四在雪夜中巡逻，发现血人，报告张三。张三检查虎符，确认身份。两人讨论狼主威胁和雁门关防务。'
      }
    };
    
    const response = await fetch('http://localhost:3000/api/ai/generate-story-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${errorText.substring(0, 500)}...`);
      return;
    }
    
    const result = await response.json();
    console.log('✅ API调用成功!');
    console.log('📋 响应数据:', JSON.stringify(result, null, 2));
    
    // 3. 等待片段生成完成
    console.log('\n⏳ 等待视频片段生成完成...');
    
    const storyVideoId = result.storyVideoId;
    if (!storyVideoId) {
      console.log('❌ 未获取到故事视频ID');
      return;
    }
    
    // 轮询检查片段状态
    let allCompleted = false;
    let attempts = 0;
    const maxAttempts = 60; // 最多等待5分钟
    
    while (!allCompleted && attempts < maxAttempts) {
      attempts++;
      
      const segments = await prisma.videoSegment.findMany({
        where: { storyVideoId },
        orderBy: { createdAt: 'asc' }
      });
      
      const completedSegments = segments.filter(s => s.status === 'completed');
      const failedSegments = segments.filter(s => s.status === 'failed');
      const generatingSegments = segments.filter(s => s.status === 'generating');
      
      console.log(`📊 片段状态 (${attempts}/${maxAttempts}): 完成${completedSegments.length}, 失败${failedSegments.length}, 生成中${generatingSegments.length}, 总计${segments.length}`);
      
      if (completedSegments.length === segments.length) {
        allCompleted = true;
        break;
      }
      
      if (failedSegments.length > 0) {
        console.log('⚠️ 有片段生成失败，但继续等待其他片段...');
      }
      
      await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
    }
    
    // 4. 分析最终结果
    console.log('\n📊 最终结果分析:');
    
    const finalSegments = await prisma.videoSegment.findMany({
      where: { storyVideoId },
      orderBy: { createdAt: 'asc' }
    });
    
    console.log(`总片段数: ${finalSegments.length}`);
    
    for (const segment of finalSegments) {
      console.log(`\n🎬 片段: ${segment.title.substring(0, 50)}...`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   数据库时长: ${segment.duration}秒`);
      console.log(`   视频URL: ${segment.videoUrl ? '有' : '无'}`);
      
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   预期时长: ${metadata.expectedDuration || '未知'}秒`);
          console.log(`   实际时长: ${metadata.actualDuration || '未知'}秒`);
          console.log(`   时长匹配: ${metadata.durationMatch ? '✅' : '❌'}`);
          console.log(`   提供商: ${metadata.provider || '未知'}`);
          
          if (metadata.actualDuration && metadata.expectedDuration) {
            const diff = Math.abs(metadata.actualDuration - metadata.expectedDuration);
            console.log(`   时长差异: ${diff}秒`);
          }
        } catch (e) {
          console.log(`   元数据解析失败: ${e.message}`);
        }
      }
    }
    
    // 5. 总结修复效果
    console.log('\n🎯 时长修复效果总结:');
    
    const completedSegments = finalSegments.filter(s => s.status === 'completed');
    const segmentsWithCorrectDuration = completedSegments.filter(s => s.duration > 10);
    
    console.log(`   完成的片段: ${completedSegments.length}/${finalSegments.length}`);
    console.log(`   时长正确的片段: ${segmentsWithCorrectDuration.length}/${completedSegments.length}`);
    
    if (segmentsWithCorrectDuration.length === completedSegments.length && completedSegments.length > 0) {
      console.log('🎉 时长修复成功！所有片段都有正确的时长');
    } else if (segmentsWithCorrectDuration.length > 0) {
      console.log('⚠️ 时长修复部分成功，仍有改进空间');
    } else {
      console.log('❌ 时长修复失败，需要进一步调试');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDurationFix();
