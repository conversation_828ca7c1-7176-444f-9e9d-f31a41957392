// 创建测试角色数据
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestCharacters() {
  try {
    console.log('创建测试角色数据...');
    
    // 首先创建一个测试项目
    const project = await prisma.project.create({
      data: {
        name: '测试小说项目',
        description: '用于测试角色一致性功能的项目'
      }
    });
    
    console.log('✅ 测试项目创建成功:', project.id);
    
    // 创建测试角色数据
    const testCharacters = [
      {
        name: '张小雅',
        identity: '高中生',
        personality: '活泼开朗，善良勇敢，喜欢帮助他人',
        physique: '身材娇小，体态轻盈',
        facial: '圆脸，大眼睛，樱桃小嘴',
        hairstyle: '黑色长发，马尾辫',
        clothing: '校服，白色衬衫配蓝色裙子',
        consistencyScore: 0.92,
        detailedDNA: JSON.stringify({
          facial: {
            faceShape: '圆脸',
            eyeShape: '杏眼',
            eyeColor: '深邃的黑色',
            noseShape: '小巧的鼻子',
            mouthShape: '樱桃小嘴',
            skinTone: '白皙透亮',
            facialFeatures: '左脸颊有一颗小痣'
          },
          uniqueIdentifiers: ['圆脸', '大眼睛', '马尾辫', '左脸颊小痣']
        }),
        referenceImages: JSON.stringify({
          front: 'data:image/svg+xml;base64,***************************************************************************************************************************************************************************************************************************************************+5byg5bCP6ZuFPC90ZXh0Pjwvc3ZnPg=='
        })
      },
      {
        name: '李明轩',
        identity: '大学教授',
        personality: '博学睿智，温文尔雅，严谨治学',
        physique: '身材高大，体态挺拔',
        facial: '方脸，深邃的眼睛，浓眉',
        hairstyle: '短发，略显花白',
        clothing: '正装，深色西装',
        consistencyScore: 0.75,
        detailedDNA: JSON.stringify({
          facial: {
            faceShape: '方脸',
            eyeShape: '深邃的眼睛',
            eyeColor: '深棕色',
            noseShape: '高挺的鼻梁',
            mouthShape: '薄唇',
            skinTone: '健康的肤色',
            facialFeatures: '眉间有细纹'
          },
          uniqueIdentifiers: ['方脸', '浓眉', '花白短发', '眉间细纹']
        })
      },
      {
        name: '王美丽',
        identity: '咖啡店老板',
        personality: '热情友善，细心体贴，有艺术气质',
        physique: '中等身材，优雅的体态',
        facial: '瓜子脸，温柔的眼神',
        hairstyle: '波浪卷发，棕色',
        clothing: '休闲装，围裙',
        consistencyScore: 0.58,
        detailedDNA: JSON.stringify({
          facial: {
            faceShape: '瓜子脸',
            eyeShape: '温柔的眼神',
            eyeColor: '浅棕色',
            noseShape: '精致的鼻子',
            mouthShape: '微笑的嘴角',
            skinTone: '白皙的肌肤',
            facialFeatures: '右眼角有笑纹'
          },
          uniqueIdentifiers: ['瓜子脸', '波浪卷发', '温柔眼神', '右眼角笑纹']
        })
      },
      {
        name: '陈浩然',
        identity: '程序员',
        personality: '内向专注，逻辑思维强，有点宅',
        physique: '瘦高身材，略显驼背',
        facial: '长脸，戴眼镜',
        hairstyle: '凌乱的短发',
        clothing: 'T恤，牛仔裤',
        // 没有设置一致性评分，测试未设置一致性的情况
      }
    ];
    
    // 批量创建角色
    for (const characterData of testCharacters) {
      const character = await prisma.character.create({
        data: {
          ...characterData,
          projectId: project.id
        }
      });
      console.log(`✅ 角色创建成功: ${character.name} (一致性: ${character.consistencyScore || '未设置'})`);
    }
    
    console.log('\n🎉 测试角色数据创建完成！');
    console.log(`项目ID: ${project.id}`);
    
  } catch (error) {
    console.error('❌ 创建测试角色数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestCharacters();
