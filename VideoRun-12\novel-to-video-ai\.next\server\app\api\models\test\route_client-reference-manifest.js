globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/models/test/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/page.tsx":{"*":{"id":"(ssr)/./src/app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/new/page.tsx":{"*":{"id":"(ssr)/./src/app/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/models/page.tsx":{"*":{"id":"(ssr)/./src/app/models/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\projects\\page.tsx":{"id":"(app-pages-browser)/./src/app/projects/page.tsx","name":"*","chunks":[],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\projects\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/projects/[id]/page.tsx","name":"*","chunks":[],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\projects\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/projects/new/page.tsx","name":"*","chunks":[],"async":false},"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\models\\page.tsx":{"id":"(app-pages-browser)/./src/app/models/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\":[],"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\page":[],"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\api\\models\\route":[],"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\api\\models\\test\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/page.tsx":{"*":{"id":"(rsc)/./src/app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/new/page.tsx":{"*":{"id":"(rsc)/./src/app/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/models/page.tsx":{"*":{"id":"(rsc)/./src/app/models/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}