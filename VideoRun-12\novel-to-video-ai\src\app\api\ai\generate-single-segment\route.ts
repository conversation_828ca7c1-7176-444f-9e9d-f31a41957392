import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// POST - 生成单个视频片段
export async function POST(request: NextRequest) {
  try {
    const { segmentId, modelId } = await request.json()

    console.log('🎬 开始生成单个视频片段')
    console.log('📋 参数:', { segmentId, modelId })

    // 获取当前请求的host信息
    const host = request.headers.get('host') || 'localhost:3000'
    const protocol = request.headers.get('x-forwarded-proto') || 'http'

    if (!segmentId) {
      return NextResponse.json(
        { success: false, error: '缺少片段ID' },
        { status: 400 }
      )
    }

    // 获取片段信息
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId },
      include: {
        storyVideo: true
      }
    })

    if (!segment) {
      return NextResponse.json(
        { success: false, error: '片段不存在' },
        { status: 404 }
      )
    }

    // 获取项目ID - 直接从segment中获取
    const projectId = segment.projectId

    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '片段缺少项目ID' },
        { status: 400 }
      )
    }

    // 🔒 先决条件检查：主要角色必须有参考图像
    console.log('🔍 检查角色参考图像先决条件...')
    const characterCheck = await checkCharacterImagePrerequisites(projectId)
    
    if (!characterCheck.success) {
      console.log('❌ 角色图像先决条件检查失败:', characterCheck.error)
      return NextResponse.json(
        { 
          success: false, 
          error: characterCheck.error,
          missingCharacters: characterCheck.missingCharacters,
          requirementType: 'character_images'
        },
        { status: 400 }
      )
    }
    
    console.log('✅ 角色图像先决条件检查通过')

    // 检查片段状态
    if (segment.status === 'generating') {
      return NextResponse.json(
        { success: false, error: '该片段正在生成中，请稍后重试' },
        { status: 400 }
      )
    }

    if (segment.status === 'completed' && segment.videoUrl) {
      return NextResponse.json(
        { success: false, error: '该片段已经生成完成' },
        { status: 400 }
      )
    }

    // 更新片段状态为生成中
    await prisma.videoSegment.update({
      where: { id: segmentId },
      data: { 
        status: 'generating',
        updatedAt: new Date()
      }
    })

    console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`)

    // 异步调用内部生成API（不等待完成）
    // 使用当前请求的host信息构建内部API URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${protocol}://${host}`
    const internalApiUrl = `${baseUrl}/api/ai/generate-story-video/segment`
    console.log(`🔗 调用内部生成API: ${internalApiUrl}`)

    fetch(internalApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        segmentId: segment.id,
        modelId
      })
    })
    .then(response => {
      console.log(`📡 内部API响应状态: ${response.status}`)
      if (!response.ok) {
        return response.text().then(text => {
          throw new Error(`内部API调用失败: ${response.status} ${text}`)
        })
      }
      return response.json()
    })
    .then(data => {
      console.log(`✅ 内部API调用成功:`, data)
    })
    .catch(error => {
      console.error(`❌ 片段 ${segment.segmentIndex} 内部API调用失败:`, error)

      // 更新片段状态为失败
      prisma.videoSegment.update({
        where: { id: segment.id },
        data: {
          status: 'failed',
          updatedAt: new Date()
        }
      }).catch(dbError => {
        console.error('更新片段状态失败:', dbError)
      })
    })

    return NextResponse.json({
      success: true,
      data: {
        message: `片段 ${segment.segmentIndex} 开始生成`,
        segmentId: segment.id,
        segmentIndex: segment.segmentIndex,
        title: segment.title
      }
    })

  } catch (error) {
    console.error('生成单个视频片段失败:', error)
    return NextResponse.json(
      { success: false, error: '生成视频片段失败' },
      { status: 500 }
    )
  }
}

// 检查角色图像先决条件（复用主API的逻辑）
async function checkCharacterImagePrerequisites(projectId: string): Promise<{
  success: boolean
  error?: string
  missingCharacters?: string[]
}> {
  try {
    console.log(`🔍 检查项目 ${projectId} 的角色图像先决条件`)

    // 获取项目的所有角色
    const characters = await prisma.character.findMany({
      where: { projectId },
      select: { id: true, name: true, generatedImages: true }
    })

    if (characters.length === 0) {
      return {
        success: false,
        error: '项目中没有角色信息，请先创建角色',
        missingCharacters: []
      }
    }

    console.log(`📋 找到 ${characters.length} 个角色`)

    // 检查每个角色是否有参考图像
    const missingCharacters: string[] = []
    
    for (const character of characters) {
      console.log(`🎭 检查角色: ${character.name}`)
      
      if (!character.generatedImages) {
        console.log(`❌ 角色 ${character.name} 没有生成图像`)
        missingCharacters.push(character.name)
        continue
      }

      try {
        const images = JSON.parse(character.generatedImages)
        const hasAllImages = images.front && images.side && images.back
        
        if (!hasAllImages) {
          console.log(`❌ 角色 ${character.name} 的图像不完整`)
          missingCharacters.push(character.name)
        } else {
          console.log(`✅ 角色 ${character.name} 有完整的参考图像`)
        }
      } catch (e) {
        console.log(`❌ 角色 ${character.name} 的图像数据解析失败`)
        missingCharacters.push(character.name)
      }
    }

    if (missingCharacters.length > 0) {
      const errorMessage = `以下角色缺少参考图像，请先生成角色形象：${missingCharacters.join('、')}`
      console.log(`❌ 先决条件检查失败: ${errorMessage}`)
      
      return {
        success: false,
        error: errorMessage,
        missingCharacters
      }
    }

    console.log('✅ 所有角色都有完整的参考图像')
    return { success: true }

  } catch (error) {
    console.error('检查角色图像先决条件失败:', error)
    return {
      success: false,
      error: '检查角色图像时发生错误，请稍后重试'
    }
  }
}


