// 测试AI配置
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAIConfig() {
  try {
    console.log('🔍 检查AI配置...');
    
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    });
    
    if (!aiConfig) {
      console.log('❌ 未找到启用的AI配置');
      return;
    }
    
    console.log('✅ AI配置找到:');
    console.log('- 提供商:', aiConfig.provider);
    console.log('- 模型:', aiConfig.model);
    console.log('- API密钥:', aiConfig.apiKey ? '已设置' : '未设置');
    console.log('- 启用状态:', aiConfig.enabled);
    
  } catch (error) {
    console.error('❌ 检查AI配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAIConfig();
