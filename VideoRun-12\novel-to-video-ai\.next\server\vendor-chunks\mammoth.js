/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mammoth";
exports.ids = ["vendor-chunks/mammoth"];
exports.modules = {

/***/ "(ssr)/./node_modules/mammoth/lib/document-to-html.js":
/*!******************************************************!*\
  !*** ./node_modules/mammoth/lib/document-to-html.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar promises = __webpack_require__(/*! ./promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar documents = __webpack_require__(/*! ./documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar htmlPaths = __webpack_require__(/*! ./styles/html-paths */ \"(ssr)/./node_modules/mammoth/lib/styles/html-paths.js\");\nvar results = __webpack_require__(/*! ./results */ \"(ssr)/./node_modules/mammoth/lib/results.js\");\nvar images = __webpack_require__(/*! ./images */ \"(ssr)/./node_modules/mammoth/lib/images.js\");\nvar Html = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/mammoth/lib/html/index.js\");\nvar writers = __webpack_require__(/*! ./writers */ \"(ssr)/./node_modules/mammoth/lib/writers/index.js\");\n\nexports.DocumentConverter = DocumentConverter;\n\n\nfunction DocumentConverter(options) {\n    return {\n        convertToHtml: function(element) {\n            var comments = _.indexBy(\n                element.type === documents.types.document ? element.comments : [],\n                \"commentId\"\n            );\n            var conversion = new DocumentConversion(options, comments);\n            return conversion.convertToHtml(element);\n        }\n    };\n}\n\nfunction DocumentConversion(options, comments) {\n    var noteNumber = 1;\n\n    var noteReferences = [];\n\n    var referencedComments = [];\n\n    options = _.extend({ignoreEmptyParagraphs: true}, options);\n    var idPrefix = options.idPrefix === undefined ? \"\" : options.idPrefix;\n    var ignoreEmptyParagraphs = options.ignoreEmptyParagraphs;\n\n    var defaultParagraphStyle = htmlPaths.topLevelElement(\"p\");\n\n    var styleMap = options.styleMap || [];\n\n    function convertToHtml(document) {\n        var messages = [];\n\n        var html = elementToHtml(document, messages, {});\n\n        var deferredNodes = [];\n        walkHtml(html, function(node) {\n            if (node.type === \"deferred\") {\n                deferredNodes.push(node);\n            }\n        });\n        var deferredValues = {};\n        return promises.mapSeries(deferredNodes, function(deferred) {\n            return deferred.value().then(function(value) {\n                deferredValues[deferred.id] = value;\n            });\n        }).then(function() {\n            function replaceDeferred(nodes) {\n                return flatMap(nodes, function(node) {\n                    if (node.type === \"deferred\") {\n                        return deferredValues[node.id];\n                    } else if (node.children) {\n                        return [\n                            _.extend({}, node, {\n                                children: replaceDeferred(node.children)\n                            })\n                        ];\n                    } else {\n                        return [node];\n                    }\n                });\n            }\n            var writer = writers.writer({\n                prettyPrint: options.prettyPrint,\n                outputFormat: options.outputFormat\n            });\n            Html.write(writer, Html.simplify(replaceDeferred(html)));\n            return new results.Result(writer.asString(), messages);\n        });\n    }\n\n    function convertElements(elements, messages, options) {\n        return flatMap(elements, function(element) {\n            return elementToHtml(element, messages, options);\n        });\n    }\n\n    function elementToHtml(element, messages, options) {\n        if (!options) {\n            throw new Error(\"options not set\");\n        }\n        var handler = elementConverters[element.type];\n        if (handler) {\n            return handler(element, messages, options);\n        } else {\n            return [];\n        }\n    }\n\n    function convertParagraph(element, messages, options) {\n        return htmlPathForParagraph(element, messages).wrap(function() {\n            var content = convertElements(element.children, messages, options);\n            if (ignoreEmptyParagraphs) {\n                return content;\n            } else {\n                return [Html.forceWrite].concat(content);\n            }\n        });\n    }\n\n    function htmlPathForParagraph(element, messages) {\n        var style = findStyle(element);\n\n        if (style) {\n            return style.to;\n        } else {\n            if (element.styleId) {\n                messages.push(unrecognisedStyleWarning(\"paragraph\", element));\n            }\n            return defaultParagraphStyle;\n        }\n    }\n\n    function convertRun(run, messages, options) {\n        var nodes = function() {\n            return convertElements(run.children, messages, options);\n        };\n        var paths = [];\n        if (run.highlight !== null) {\n            var path = findHtmlPath({type: \"highlight\", color: run.highlight});\n            if (path) {\n                paths.push(path);\n            }\n        }\n        if (run.isSmallCaps) {\n            paths.push(findHtmlPathForRunProperty(\"smallCaps\"));\n        }\n        if (run.isAllCaps) {\n            paths.push(findHtmlPathForRunProperty(\"allCaps\"));\n        }\n        if (run.isStrikethrough) {\n            paths.push(findHtmlPathForRunProperty(\"strikethrough\", \"s\"));\n        }\n        if (run.isUnderline) {\n            paths.push(findHtmlPathForRunProperty(\"underline\"));\n        }\n        if (run.verticalAlignment === documents.verticalAlignment.subscript) {\n            paths.push(htmlPaths.element(\"sub\", {}, {fresh: false}));\n        }\n        if (run.verticalAlignment === documents.verticalAlignment.superscript) {\n            paths.push(htmlPaths.element(\"sup\", {}, {fresh: false}));\n        }\n        if (run.isItalic) {\n            paths.push(findHtmlPathForRunProperty(\"italic\", \"em\"));\n        }\n        if (run.isBold) {\n            paths.push(findHtmlPathForRunProperty(\"bold\", \"strong\"));\n        }\n        var stylePath = htmlPaths.empty;\n        var style = findStyle(run);\n        if (style) {\n            stylePath = style.to;\n        } else if (run.styleId) {\n            messages.push(unrecognisedStyleWarning(\"run\", run));\n        }\n        paths.push(stylePath);\n\n        paths.forEach(function(path) {\n            nodes = path.wrap.bind(path, nodes);\n        });\n\n        return nodes();\n    }\n\n    function findHtmlPathForRunProperty(elementType, defaultTagName) {\n        var path = findHtmlPath({type: elementType});\n        if (path) {\n            return path;\n        } else if (defaultTagName) {\n            return htmlPaths.element(defaultTagName, {}, {fresh: false});\n        } else {\n            return htmlPaths.empty;\n        }\n    }\n\n    function findHtmlPath(element, defaultPath) {\n        var style = findStyle(element);\n        return style ? style.to : defaultPath;\n    }\n\n    function findStyle(element) {\n        for (var i = 0; i < styleMap.length; i++) {\n            if (styleMap[i].from.matches(element)) {\n                return styleMap[i];\n            }\n        }\n    }\n\n    function recoveringConvertImage(convertImage) {\n        return function(image, messages) {\n            return promises.attempt(function() {\n                return convertImage(image, messages);\n            }).caught(function(error) {\n                messages.push(results.error(error));\n                return [];\n            });\n        };\n    }\n\n    function noteHtmlId(note) {\n        return referentHtmlId(note.noteType, note.noteId);\n    }\n\n    function noteRefHtmlId(note) {\n        return referenceHtmlId(note.noteType, note.noteId);\n    }\n\n    function referentHtmlId(referenceType, referenceId) {\n        return htmlId(referenceType + \"-\" + referenceId);\n    }\n\n    function referenceHtmlId(referenceType, referenceId) {\n        return htmlId(referenceType + \"-ref-\" + referenceId);\n    }\n\n    function htmlId(suffix) {\n        return idPrefix + suffix;\n    }\n\n    var defaultTablePath = htmlPaths.elements([\n        htmlPaths.element(\"table\", {}, {fresh: true})\n    ]);\n\n    function convertTable(element, messages, options) {\n        return findHtmlPath(element, defaultTablePath).wrap(function() {\n            return convertTableChildren(element, messages, options);\n        });\n    }\n\n    function convertTableChildren(element, messages, options) {\n        var bodyIndex = _.findIndex(element.children, function(child) {\n            return !child.type === documents.types.tableRow || !child.isHeader;\n        });\n        if (bodyIndex === -1) {\n            bodyIndex = element.children.length;\n        }\n        var children;\n        if (bodyIndex === 0) {\n            children = convertElements(\n                element.children,\n                messages,\n                _.extend({}, options, {isTableHeader: false})\n            );\n        } else {\n            var headRows = convertElements(\n                element.children.slice(0, bodyIndex),\n                messages,\n                _.extend({}, options, {isTableHeader: true})\n            );\n            var bodyRows = convertElements(\n                element.children.slice(bodyIndex),\n                messages,\n                _.extend({}, options, {isTableHeader: false})\n            );\n            children = [\n                Html.freshElement(\"thead\", {}, headRows),\n                Html.freshElement(\"tbody\", {}, bodyRows)\n            ];\n        }\n        return [Html.forceWrite].concat(children);\n    }\n\n    function convertTableRow(element, messages, options) {\n        var children = convertElements(element.children, messages, options);\n        return [\n            Html.freshElement(\"tr\", {}, [Html.forceWrite].concat(children))\n        ];\n    }\n\n    function convertTableCell(element, messages, options) {\n        var tagName = options.isTableHeader ? \"th\" : \"td\";\n        var children = convertElements(element.children, messages, options);\n        var attributes = {};\n        if (element.colSpan !== 1) {\n            attributes.colspan = element.colSpan.toString();\n        }\n        if (element.rowSpan !== 1) {\n            attributes.rowspan = element.rowSpan.toString();\n        }\n\n        return [\n            Html.freshElement(tagName, attributes, [Html.forceWrite].concat(children))\n        ];\n    }\n\n    function convertCommentReference(reference, messages, options) {\n        return findHtmlPath(reference, htmlPaths.ignore).wrap(function() {\n            var comment = comments[reference.commentId];\n            var count = referencedComments.length + 1;\n            var label = \"[\" + commentAuthorLabel(comment) + count + \"]\";\n            referencedComments.push({label: label, comment: comment});\n            // TODO: remove duplication with note references\n            return [\n                Html.freshElement(\"a\", {\n                    href: \"#\" + referentHtmlId(\"comment\", reference.commentId),\n                    id: referenceHtmlId(\"comment\", reference.commentId)\n                }, [Html.text(label)])\n            ];\n        });\n    }\n\n    function convertComment(referencedComment, messages, options) {\n        // TODO: remove duplication with note references\n\n        var label = referencedComment.label;\n        var comment = referencedComment.comment;\n        var body = convertElements(comment.body, messages, options).concat([\n            Html.nonFreshElement(\"p\", {}, [\n                Html.text(\" \"),\n                Html.freshElement(\"a\", {\"href\": \"#\" + referenceHtmlId(\"comment\", comment.commentId)}, [\n                    Html.text(\"↑\")\n                ])\n            ])\n        ]);\n\n        return [\n            Html.freshElement(\n                \"dt\",\n                {\"id\": referentHtmlId(\"comment\", comment.commentId)},\n                [Html.text(\"Comment \" + label)]\n            ),\n            Html.freshElement(\"dd\", {}, body)\n        ];\n    }\n\n    function convertBreak(element, messages, options) {\n        return htmlPathForBreak(element).wrap(function() {\n            return [];\n        });\n    }\n\n    function htmlPathForBreak(element) {\n        var style = findStyle(element);\n        if (style) {\n            return style.to;\n        } else if (element.breakType === \"line\") {\n            return htmlPaths.topLevelElement(\"br\");\n        } else {\n            return htmlPaths.empty;\n        }\n    }\n\n    var elementConverters = {\n        \"document\": function(document, messages, options) {\n            var children = convertElements(document.children, messages, options);\n            var notes = noteReferences.map(function(noteReference) {\n                return document.notes.resolve(noteReference);\n            });\n            var notesNodes = convertElements(notes, messages, options);\n            return children.concat([\n                Html.freshElement(\"ol\", {}, notesNodes),\n                Html.freshElement(\"dl\", {}, flatMap(referencedComments, function(referencedComment) {\n                    return convertComment(referencedComment, messages, options);\n                }))\n            ]);\n        },\n        \"paragraph\": convertParagraph,\n        \"run\": convertRun,\n        \"text\": function(element, messages, options) {\n            return [Html.text(element.value)];\n        },\n        \"tab\": function(element, messages, options) {\n            return [Html.text(\"\\t\")];\n        },\n        \"hyperlink\": function(element, messages, options) {\n            var href = element.anchor ? \"#\" + htmlId(element.anchor) : element.href;\n            var attributes = {href: href};\n            if (element.targetFrame != null) {\n                attributes.target = element.targetFrame;\n            }\n\n            var children = convertElements(element.children, messages, options);\n            return [Html.nonFreshElement(\"a\", attributes, children)];\n        },\n        \"checkbox\": function(element) {\n            var attributes = {type: \"checkbox\"};\n            if (element.checked) {\n                attributes[\"checked\"] = \"checked\";\n            }\n            return [Html.freshElement(\"input\", attributes)];\n        },\n        \"bookmarkStart\": function(element, messages, options) {\n            var anchor = Html.freshElement(\"a\", {\n                id: htmlId(element.name)\n            }, [Html.forceWrite]);\n            return [anchor];\n        },\n        \"noteReference\": function(element, messages, options) {\n            noteReferences.push(element);\n            var anchor = Html.freshElement(\"a\", {\n                href: \"#\" + noteHtmlId(element),\n                id: noteRefHtmlId(element)\n            }, [Html.text(\"[\" + (noteNumber++) + \"]\")]);\n\n            return [Html.freshElement(\"sup\", {}, [anchor])];\n        },\n        \"note\": function(element, messages, options) {\n            var children = convertElements(element.body, messages, options);\n            var backLink = Html.elementWithTag(htmlPaths.element(\"p\", {}, {fresh: false}), [\n                Html.text(\" \"),\n                Html.freshElement(\"a\", {href: \"#\" + noteRefHtmlId(element)}, [Html.text(\"↑\")])\n            ]);\n            var body = children.concat([backLink]);\n\n            return Html.freshElement(\"li\", {id: noteHtmlId(element)}, body);\n        },\n        \"commentReference\": convertCommentReference,\n        \"comment\": convertComment,\n        \"image\": deferredConversion(recoveringConvertImage(options.convertImage || images.dataUri)),\n        \"table\": convertTable,\n        \"tableRow\": convertTableRow,\n        \"tableCell\": convertTableCell,\n        \"break\": convertBreak\n    };\n    return {\n        convertToHtml: convertToHtml\n    };\n}\n\nvar deferredId = 1;\n\nfunction deferredConversion(func) {\n    return function(element, messages, options) {\n        return [\n            {\n                type: \"deferred\",\n                id: deferredId++,\n                value: function() {\n                    return func(element, messages, options);\n                }\n            }\n        ];\n    };\n}\n\nfunction unrecognisedStyleWarning(type, element) {\n    return results.warning(\n        \"Unrecognised \" + type + \" style: '\" + element.styleName + \"'\" +\n        \" (Style ID: \" + element.styleId + \")\"\n    );\n}\n\nfunction flatMap(values, func) {\n    return _.flatten(values.map(func), true);\n}\n\nfunction walkHtml(nodes, callback) {\n    nodes.forEach(function(node) {\n        callback(node);\n        if (node.children) {\n            walkHtml(node.children, callback);\n        }\n    });\n}\n\nvar commentAuthorLabel = exports.commentAuthorLabel = function commentAuthorLabel(comment) {\n    return comment.authorInitials || \"\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvZG9jdW1lbnQtdG8taHRtbC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxRQUFRLG1CQUFPLENBQUMsd0VBQVk7O0FBRTVCLGVBQWUsbUJBQU8sQ0FBQyxnRUFBWTtBQUNuQyxnQkFBZ0IsbUJBQU8sQ0FBQyxrRUFBYTtBQUNyQyxnQkFBZ0IsbUJBQU8sQ0FBQyxrRkFBcUI7QUFDN0MsY0FBYyxtQkFBTyxDQUFDLDhEQUFXO0FBQ2pDLGFBQWEsbUJBQU8sQ0FBQyw0REFBVTtBQUMvQixXQUFXLG1CQUFPLENBQUMsOERBQVE7QUFDM0IsY0FBYyxtQkFBTyxDQUFDLG9FQUFXOztBQUVqQyx5QkFBeUI7OztBQUd6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQSx3QkFBd0IsNEJBQTRCO0FBQ3BEO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQSx1REFBdUQ7O0FBRXZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLFNBQVM7QUFDVDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLFNBQVM7QUFDVDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyx3Q0FBd0M7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxHQUFHLGFBQWE7QUFDbEU7QUFDQTtBQUNBLGtEQUFrRCxHQUFHLGFBQWE7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTs7QUFFQTtBQUNBLGlDQUFpQyxrQkFBa0I7QUFDbkQ7QUFDQTtBQUNBLFVBQVU7QUFDVix1REFBdUQsR0FBRyxhQUFhO0FBQ3ZFLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx3QkFBd0IscUJBQXFCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxxQ0FBcUMsR0FBRyxZQUFZO0FBQ3BEOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixZQUFZLHFCQUFxQjtBQUM1RDtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsWUFBWSxvQkFBb0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsWUFBWSxxQkFBcUI7QUFDNUQ7QUFDQTtBQUNBLDZDQUE2QztBQUM3Qyw2Q0FBNkM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQywrQkFBK0I7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QztBQUN4QztBQUNBLHdDQUF3Qyw0REFBNEQ7QUFDcEc7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLG1EQUFtRDtBQUNwRTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSwwQ0FBMEM7QUFDMUMsMENBQTBDO0FBQzFDO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7O0FBRWIsK0NBQStDO0FBQy9DLFNBQVM7QUFDVDtBQUNBO0FBQ0Esd0VBQXdFLEdBQUcsYUFBYTtBQUN4RjtBQUNBLHdDQUF3QyxtQ0FBbUM7QUFDM0U7QUFDQTs7QUFFQSw0Q0FBNEMsd0JBQXdCO0FBQ3BFLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUEseUJBQXlCLDBCQUEwQjtBQUNuRDtBQUNBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxub2RlX21vZHVsZXNcXG1hbW1vdGhcXGxpYlxcZG9jdW1lbnQtdG8taHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgXyA9IHJlcXVpcmUoXCJ1bmRlcnNjb3JlXCIpO1xuXG52YXIgcHJvbWlzZXMgPSByZXF1aXJlKFwiLi9wcm9taXNlc1wiKTtcbnZhciBkb2N1bWVudHMgPSByZXF1aXJlKFwiLi9kb2N1bWVudHNcIik7XG52YXIgaHRtbFBhdGhzID0gcmVxdWlyZShcIi4vc3R5bGVzL2h0bWwtcGF0aHNcIik7XG52YXIgcmVzdWx0cyA9IHJlcXVpcmUoXCIuL3Jlc3VsdHNcIik7XG52YXIgaW1hZ2VzID0gcmVxdWlyZShcIi4vaW1hZ2VzXCIpO1xudmFyIEh0bWwgPSByZXF1aXJlKFwiLi9odG1sXCIpO1xudmFyIHdyaXRlcnMgPSByZXF1aXJlKFwiLi93cml0ZXJzXCIpO1xuXG5leHBvcnRzLkRvY3VtZW50Q29udmVydGVyID0gRG9jdW1lbnRDb252ZXJ0ZXI7XG5cblxuZnVuY3Rpb24gRG9jdW1lbnRDb252ZXJ0ZXIob3B0aW9ucykge1xuICAgIHJldHVybiB7XG4gICAgICAgIGNvbnZlcnRUb0h0bWw6IGZ1bmN0aW9uKGVsZW1lbnQpIHtcbiAgICAgICAgICAgIHZhciBjb21tZW50cyA9IF8uaW5kZXhCeShcbiAgICAgICAgICAgICAgICBlbGVtZW50LnR5cGUgPT09IGRvY3VtZW50cy50eXBlcy5kb2N1bWVudCA/IGVsZW1lbnQuY29tbWVudHMgOiBbXSxcbiAgICAgICAgICAgICAgICBcImNvbW1lbnRJZFwiXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgdmFyIGNvbnZlcnNpb24gPSBuZXcgRG9jdW1lbnRDb252ZXJzaW9uKG9wdGlvbnMsIGNvbW1lbnRzKTtcbiAgICAgICAgICAgIHJldHVybiBjb252ZXJzaW9uLmNvbnZlcnRUb0h0bWwoZWxlbWVudCk7XG4gICAgICAgIH1cbiAgICB9O1xufVxuXG5mdW5jdGlvbiBEb2N1bWVudENvbnZlcnNpb24ob3B0aW9ucywgY29tbWVudHMpIHtcbiAgICB2YXIgbm90ZU51bWJlciA9IDE7XG5cbiAgICB2YXIgbm90ZVJlZmVyZW5jZXMgPSBbXTtcblxuICAgIHZhciByZWZlcmVuY2VkQ29tbWVudHMgPSBbXTtcblxuICAgIG9wdGlvbnMgPSBfLmV4dGVuZCh7aWdub3JlRW1wdHlQYXJhZ3JhcGhzOiB0cnVlfSwgb3B0aW9ucyk7XG4gICAgdmFyIGlkUHJlZml4ID0gb3B0aW9ucy5pZFByZWZpeCA9PT0gdW5kZWZpbmVkID8gXCJcIiA6IG9wdGlvbnMuaWRQcmVmaXg7XG4gICAgdmFyIGlnbm9yZUVtcHR5UGFyYWdyYXBocyA9IG9wdGlvbnMuaWdub3JlRW1wdHlQYXJhZ3JhcGhzO1xuXG4gICAgdmFyIGRlZmF1bHRQYXJhZ3JhcGhTdHlsZSA9IGh0bWxQYXRocy50b3BMZXZlbEVsZW1lbnQoXCJwXCIpO1xuXG4gICAgdmFyIHN0eWxlTWFwID0gb3B0aW9ucy5zdHlsZU1hcCB8fCBbXTtcblxuICAgIGZ1bmN0aW9uIGNvbnZlcnRUb0h0bWwoZG9jdW1lbnQpIHtcbiAgICAgICAgdmFyIG1lc3NhZ2VzID0gW107XG5cbiAgICAgICAgdmFyIGh0bWwgPSBlbGVtZW50VG9IdG1sKGRvY3VtZW50LCBtZXNzYWdlcywge30pO1xuXG4gICAgICAgIHZhciBkZWZlcnJlZE5vZGVzID0gW107XG4gICAgICAgIHdhbGtIdG1sKGh0bWwsIGZ1bmN0aW9uKG5vZGUpIHtcbiAgICAgICAgICAgIGlmIChub2RlLnR5cGUgPT09IFwiZGVmZXJyZWRcIikge1xuICAgICAgICAgICAgICAgIGRlZmVycmVkTm9kZXMucHVzaChub2RlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHZhciBkZWZlcnJlZFZhbHVlcyA9IHt9O1xuICAgICAgICByZXR1cm4gcHJvbWlzZXMubWFwU2VyaWVzKGRlZmVycmVkTm9kZXMsIGZ1bmN0aW9uKGRlZmVycmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gZGVmZXJyZWQudmFsdWUoKS50aGVuKGZ1bmN0aW9uKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgZGVmZXJyZWRWYWx1ZXNbZGVmZXJyZWQuaWRdID0gdmFsdWU7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHtcbiAgICAgICAgICAgIGZ1bmN0aW9uIHJlcGxhY2VEZWZlcnJlZChub2Rlcykge1xuICAgICAgICAgICAgICAgIHJldHVybiBmbGF0TWFwKG5vZGVzLCBmdW5jdGlvbihub2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChub2RlLnR5cGUgPT09IFwiZGVmZXJyZWRcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlZmVycmVkVmFsdWVzW25vZGUuaWRdO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUuY2hpbGRyZW4pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgXy5leHRlbmQoe30sIG5vZGUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IHJlcGxhY2VEZWZlcnJlZChub2RlLmNoaWxkcmVuKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFtub2RlXTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFyIHdyaXRlciA9IHdyaXRlcnMud3JpdGVyKHtcbiAgICAgICAgICAgICAgICBwcmV0dHlQcmludDogb3B0aW9ucy5wcmV0dHlQcmludCxcbiAgICAgICAgICAgICAgICBvdXRwdXRGb3JtYXQ6IG9wdGlvbnMub3V0cHV0Rm9ybWF0XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIEh0bWwud3JpdGUod3JpdGVyLCBIdG1sLnNpbXBsaWZ5KHJlcGxhY2VEZWZlcnJlZChodG1sKSkpO1xuICAgICAgICAgICAgcmV0dXJuIG5ldyByZXN1bHRzLlJlc3VsdCh3cml0ZXIuYXNTdHJpbmcoKSwgbWVzc2FnZXMpO1xuICAgICAgICB9KTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBjb252ZXJ0RWxlbWVudHMoZWxlbWVudHMsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBmbGF0TWFwKGVsZW1lbnRzLCBmdW5jdGlvbihlbGVtZW50KSB7XG4gICAgICAgICAgICByZXR1cm4gZWxlbWVudFRvSHRtbChlbGVtZW50LCBtZXNzYWdlcywgb3B0aW9ucyk7XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGVsZW1lbnRUb0h0bWwoZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKCFvcHRpb25zKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJvcHRpb25zIG5vdCBzZXRcIik7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGhhbmRsZXIgPSBlbGVtZW50Q29udmVydGVyc1tlbGVtZW50LnR5cGVdO1xuICAgICAgICBpZiAoaGFuZGxlcikge1xuICAgICAgICAgICAgcmV0dXJuIGhhbmRsZXIoZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gY29udmVydFBhcmFncmFwaChlbGVtZW50LCBtZXNzYWdlcywgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gaHRtbFBhdGhGb3JQYXJhZ3JhcGgoZWxlbWVudCwgbWVzc2FnZXMpLndyYXAoZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICB2YXIgY29udGVudCA9IGNvbnZlcnRFbGVtZW50cyhlbGVtZW50LmNoaWxkcmVuLCBtZXNzYWdlcywgb3B0aW9ucyk7XG4gICAgICAgICAgICBpZiAoaWdub3JlRW1wdHlQYXJhZ3JhcGhzKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvbnRlbnQ7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHJldHVybiBbSHRtbC5mb3JjZVdyaXRlXS5jb25jYXQoY29udGVudCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGh0bWxQYXRoRm9yUGFyYWdyYXBoKGVsZW1lbnQsIG1lc3NhZ2VzKSB7XG4gICAgICAgIHZhciBzdHlsZSA9IGZpbmRTdHlsZShlbGVtZW50KTtcblxuICAgICAgICBpZiAoc3R5bGUpIHtcbiAgICAgICAgICAgIHJldHVybiBzdHlsZS50bztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGlmIChlbGVtZW50LnN0eWxlSWQpIHtcbiAgICAgICAgICAgICAgICBtZXNzYWdlcy5wdXNoKHVucmVjb2duaXNlZFN0eWxlV2FybmluZyhcInBhcmFncmFwaFwiLCBlbGVtZW50KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGVmYXVsdFBhcmFncmFwaFN0eWxlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gY29udmVydFJ1bihydW4sIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHZhciBub2RlcyA9IGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRFbGVtZW50cyhydW4uY2hpbGRyZW4sIG1lc3NhZ2VzLCBvcHRpb25zKTtcbiAgICAgICAgfTtcbiAgICAgICAgdmFyIHBhdGhzID0gW107XG4gICAgICAgIGlmIChydW4uaGlnaGxpZ2h0ICE9PSBudWxsKSB7XG4gICAgICAgICAgICB2YXIgcGF0aCA9IGZpbmRIdG1sUGF0aCh7dHlwZTogXCJoaWdobGlnaHRcIiwgY29sb3I6IHJ1bi5oaWdobGlnaHR9KTtcbiAgICAgICAgICAgIGlmIChwYXRoKSB7XG4gICAgICAgICAgICAgICAgcGF0aHMucHVzaChwYXRoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAocnVuLmlzU21hbGxDYXBzKSB7XG4gICAgICAgICAgICBwYXRocy5wdXNoKGZpbmRIdG1sUGF0aEZvclJ1blByb3BlcnR5KFwic21hbGxDYXBzXCIpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocnVuLmlzQWxsQ2Fwcykge1xuICAgICAgICAgICAgcGF0aHMucHVzaChmaW5kSHRtbFBhdGhGb3JSdW5Qcm9wZXJ0eShcImFsbENhcHNcIikpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChydW4uaXNTdHJpa2V0aHJvdWdoKSB7XG4gICAgICAgICAgICBwYXRocy5wdXNoKGZpbmRIdG1sUGF0aEZvclJ1blByb3BlcnR5KFwic3RyaWtldGhyb3VnaFwiLCBcInNcIikpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChydW4uaXNVbmRlcmxpbmUpIHtcbiAgICAgICAgICAgIHBhdGhzLnB1c2goZmluZEh0bWxQYXRoRm9yUnVuUHJvcGVydHkoXCJ1bmRlcmxpbmVcIikpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChydW4udmVydGljYWxBbGlnbm1lbnQgPT09IGRvY3VtZW50cy52ZXJ0aWNhbEFsaWdubWVudC5zdWJzY3JpcHQpIHtcbiAgICAgICAgICAgIHBhdGhzLnB1c2goaHRtbFBhdGhzLmVsZW1lbnQoXCJzdWJcIiwge30sIHtmcmVzaDogZmFsc2V9KSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHJ1bi52ZXJ0aWNhbEFsaWdubWVudCA9PT0gZG9jdW1lbnRzLnZlcnRpY2FsQWxpZ25tZW50LnN1cGVyc2NyaXB0KSB7XG4gICAgICAgICAgICBwYXRocy5wdXNoKGh0bWxQYXRocy5lbGVtZW50KFwic3VwXCIsIHt9LCB7ZnJlc2g6IGZhbHNlfSkpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChydW4uaXNJdGFsaWMpIHtcbiAgICAgICAgICAgIHBhdGhzLnB1c2goZmluZEh0bWxQYXRoRm9yUnVuUHJvcGVydHkoXCJpdGFsaWNcIiwgXCJlbVwiKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHJ1bi5pc0JvbGQpIHtcbiAgICAgICAgICAgIHBhdGhzLnB1c2goZmluZEh0bWxQYXRoRm9yUnVuUHJvcGVydHkoXCJib2xkXCIsIFwic3Ryb25nXCIpKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgc3R5bGVQYXRoID0gaHRtbFBhdGhzLmVtcHR5O1xuICAgICAgICB2YXIgc3R5bGUgPSBmaW5kU3R5bGUocnVuKTtcbiAgICAgICAgaWYgKHN0eWxlKSB7XG4gICAgICAgICAgICBzdHlsZVBhdGggPSBzdHlsZS50bztcbiAgICAgICAgfSBlbHNlIGlmIChydW4uc3R5bGVJZCkge1xuICAgICAgICAgICAgbWVzc2FnZXMucHVzaCh1bnJlY29nbmlzZWRTdHlsZVdhcm5pbmcoXCJydW5cIiwgcnVuKSk7XG4gICAgICAgIH1cbiAgICAgICAgcGF0aHMucHVzaChzdHlsZVBhdGgpO1xuXG4gICAgICAgIHBhdGhzLmZvckVhY2goZnVuY3Rpb24ocGF0aCkge1xuICAgICAgICAgICAgbm9kZXMgPSBwYXRoLndyYXAuYmluZChwYXRoLCBub2Rlcyk7XG4gICAgICAgIH0pO1xuXG4gICAgICAgIHJldHVybiBub2RlcygpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGZpbmRIdG1sUGF0aEZvclJ1blByb3BlcnR5KGVsZW1lbnRUeXBlLCBkZWZhdWx0VGFnTmFtZSkge1xuICAgICAgICB2YXIgcGF0aCA9IGZpbmRIdG1sUGF0aCh7dHlwZTogZWxlbWVudFR5cGV9KTtcbiAgICAgICAgaWYgKHBhdGgpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXRoO1xuICAgICAgICB9IGVsc2UgaWYgKGRlZmF1bHRUYWdOYW1lKSB7XG4gICAgICAgICAgICByZXR1cm4gaHRtbFBhdGhzLmVsZW1lbnQoZGVmYXVsdFRhZ05hbWUsIHt9LCB7ZnJlc2g6IGZhbHNlfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gaHRtbFBhdGhzLmVtcHR5O1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gZmluZEh0bWxQYXRoKGVsZW1lbnQsIGRlZmF1bHRQYXRoKSB7XG4gICAgICAgIHZhciBzdHlsZSA9IGZpbmRTdHlsZShlbGVtZW50KTtcbiAgICAgICAgcmV0dXJuIHN0eWxlID8gc3R5bGUudG8gOiBkZWZhdWx0UGF0aDtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBmaW5kU3R5bGUoZWxlbWVudCkge1xuICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHN0eWxlTWFwLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAoc3R5bGVNYXBbaV0uZnJvbS5tYXRjaGVzKGVsZW1lbnQpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0eWxlTWFwW2ldO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gcmVjb3ZlcmluZ0NvbnZlcnRJbWFnZShjb252ZXJ0SW1hZ2UpIHtcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uKGltYWdlLCBtZXNzYWdlcykge1xuICAgICAgICAgICAgcmV0dXJuIHByb21pc2VzLmF0dGVtcHQoZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRJbWFnZShpbWFnZSwgbWVzc2FnZXMpO1xuICAgICAgICAgICAgfSkuY2F1Z2h0KGZ1bmN0aW9uKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgbWVzc2FnZXMucHVzaChyZXN1bHRzLmVycm9yKGVycm9yKSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gbm90ZUh0bWxJZChub3RlKSB7XG4gICAgICAgIHJldHVybiByZWZlcmVudEh0bWxJZChub3RlLm5vdGVUeXBlLCBub3RlLm5vdGVJZCk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gbm90ZVJlZkh0bWxJZChub3RlKSB7XG4gICAgICAgIHJldHVybiByZWZlcmVuY2VIdG1sSWQobm90ZS5ub3RlVHlwZSwgbm90ZS5ub3RlSWQpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIHJlZmVyZW50SHRtbElkKHJlZmVyZW5jZVR5cGUsIHJlZmVyZW5jZUlkKSB7XG4gICAgICAgIHJldHVybiBodG1sSWQocmVmZXJlbmNlVHlwZSArIFwiLVwiICsgcmVmZXJlbmNlSWQpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIHJlZmVyZW5jZUh0bWxJZChyZWZlcmVuY2VUeXBlLCByZWZlcmVuY2VJZCkge1xuICAgICAgICByZXR1cm4gaHRtbElkKHJlZmVyZW5jZVR5cGUgKyBcIi1yZWYtXCIgKyByZWZlcmVuY2VJZCk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gaHRtbElkKHN1ZmZpeCkge1xuICAgICAgICByZXR1cm4gaWRQcmVmaXggKyBzdWZmaXg7XG4gICAgfVxuXG4gICAgdmFyIGRlZmF1bHRUYWJsZVBhdGggPSBodG1sUGF0aHMuZWxlbWVudHMoW1xuICAgICAgICBodG1sUGF0aHMuZWxlbWVudChcInRhYmxlXCIsIHt9LCB7ZnJlc2g6IHRydWV9KVxuICAgIF0pO1xuXG4gICAgZnVuY3Rpb24gY29udmVydFRhYmxlKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBmaW5kSHRtbFBhdGgoZWxlbWVudCwgZGVmYXVsdFRhYmxlUGF0aCkud3JhcChmdW5jdGlvbigpIHtcbiAgICAgICAgICAgIHJldHVybiBjb252ZXJ0VGFibGVDaGlsZHJlbihlbGVtZW50LCBtZXNzYWdlcywgb3B0aW9ucyk7XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGNvbnZlcnRUYWJsZUNoaWxkcmVuKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHZhciBib2R5SW5kZXggPSBfLmZpbmRJbmRleChlbGVtZW50LmNoaWxkcmVuLCBmdW5jdGlvbihjaGlsZCkge1xuICAgICAgICAgICAgcmV0dXJuICFjaGlsZC50eXBlID09PSBkb2N1bWVudHMudHlwZXMudGFibGVSb3cgfHwgIWNoaWxkLmlzSGVhZGVyO1xuICAgICAgICB9KTtcbiAgICAgICAgaWYgKGJvZHlJbmRleCA9PT0gLTEpIHtcbiAgICAgICAgICAgIGJvZHlJbmRleCA9IGVsZW1lbnQuY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIHZhciBjaGlsZHJlbjtcbiAgICAgICAgaWYgKGJvZHlJbmRleCA9PT0gMCkge1xuICAgICAgICAgICAgY2hpbGRyZW4gPSBjb252ZXJ0RWxlbWVudHMoXG4gICAgICAgICAgICAgICAgZWxlbWVudC5jaGlsZHJlbixcbiAgICAgICAgICAgICAgICBtZXNzYWdlcyxcbiAgICAgICAgICAgICAgICBfLmV4dGVuZCh7fSwgb3B0aW9ucywge2lzVGFibGVIZWFkZXI6IGZhbHNlfSlcbiAgICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB2YXIgaGVhZFJvd3MgPSBjb252ZXJ0RWxlbWVudHMoXG4gICAgICAgICAgICAgICAgZWxlbWVudC5jaGlsZHJlbi5zbGljZSgwLCBib2R5SW5kZXgpLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VzLFxuICAgICAgICAgICAgICAgIF8uZXh0ZW5kKHt9LCBvcHRpb25zLCB7aXNUYWJsZUhlYWRlcjogdHJ1ZX0pXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgdmFyIGJvZHlSb3dzID0gY29udmVydEVsZW1lbnRzKFxuICAgICAgICAgICAgICAgIGVsZW1lbnQuY2hpbGRyZW4uc2xpY2UoYm9keUluZGV4KSxcbiAgICAgICAgICAgICAgICBtZXNzYWdlcyxcbiAgICAgICAgICAgICAgICBfLmV4dGVuZCh7fSwgb3B0aW9ucywge2lzVGFibGVIZWFkZXI6IGZhbHNlfSlcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICBjaGlsZHJlbiA9IFtcbiAgICAgICAgICAgICAgICBIdG1sLmZyZXNoRWxlbWVudChcInRoZWFkXCIsIHt9LCBoZWFkUm93cyksXG4gICAgICAgICAgICAgICAgSHRtbC5mcmVzaEVsZW1lbnQoXCJ0Ym9keVwiLCB7fSwgYm9keVJvd3MpXG4gICAgICAgICAgICBdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBbSHRtbC5mb3JjZVdyaXRlXS5jb25jYXQoY2hpbGRyZW4pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGNvbnZlcnRUYWJsZVJvdyhlbGVtZW50LCBtZXNzYWdlcywgb3B0aW9ucykge1xuICAgICAgICB2YXIgY2hpbGRyZW4gPSBjb252ZXJ0RWxlbWVudHMoZWxlbWVudC5jaGlsZHJlbiwgbWVzc2FnZXMsIG9wdGlvbnMpO1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgSHRtbC5mcmVzaEVsZW1lbnQoXCJ0clwiLCB7fSwgW0h0bWwuZm9yY2VXcml0ZV0uY29uY2F0KGNoaWxkcmVuKSlcbiAgICAgICAgXTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiBjb252ZXJ0VGFibGVDZWxsKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHZhciB0YWdOYW1lID0gb3B0aW9ucy5pc1RhYmxlSGVhZGVyID8gXCJ0aFwiIDogXCJ0ZFwiO1xuICAgICAgICB2YXIgY2hpbGRyZW4gPSBjb252ZXJ0RWxlbWVudHMoZWxlbWVudC5jaGlsZHJlbiwgbWVzc2FnZXMsIG9wdGlvbnMpO1xuICAgICAgICB2YXIgYXR0cmlidXRlcyA9IHt9O1xuICAgICAgICBpZiAoZWxlbWVudC5jb2xTcGFuICE9PSAxKSB7XG4gICAgICAgICAgICBhdHRyaWJ1dGVzLmNvbHNwYW4gPSBlbGVtZW50LmNvbFNwYW4udG9TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZWxlbWVudC5yb3dTcGFuICE9PSAxKSB7XG4gICAgICAgICAgICBhdHRyaWJ1dGVzLnJvd3NwYW4gPSBlbGVtZW50LnJvd1NwYW4udG9TdHJpbmcoKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBIdG1sLmZyZXNoRWxlbWVudCh0YWdOYW1lLCBhdHRyaWJ1dGVzLCBbSHRtbC5mb3JjZVdyaXRlXS5jb25jYXQoY2hpbGRyZW4pKVxuICAgICAgICBdO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGNvbnZlcnRDb21tZW50UmVmZXJlbmNlKHJlZmVyZW5jZSwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIGZpbmRIdG1sUGF0aChyZWZlcmVuY2UsIGh0bWxQYXRocy5pZ25vcmUpLndyYXAoZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICB2YXIgY29tbWVudCA9IGNvbW1lbnRzW3JlZmVyZW5jZS5jb21tZW50SWRdO1xuICAgICAgICAgICAgdmFyIGNvdW50ID0gcmVmZXJlbmNlZENvbW1lbnRzLmxlbmd0aCArIDE7XG4gICAgICAgICAgICB2YXIgbGFiZWwgPSBcIltcIiArIGNvbW1lbnRBdXRob3JMYWJlbChjb21tZW50KSArIGNvdW50ICsgXCJdXCI7XG4gICAgICAgICAgICByZWZlcmVuY2VkQ29tbWVudHMucHVzaCh7bGFiZWw6IGxhYmVsLCBjb21tZW50OiBjb21tZW50fSk7XG4gICAgICAgICAgICAvLyBUT0RPOiByZW1vdmUgZHVwbGljYXRpb24gd2l0aCBub3RlIHJlZmVyZW5jZXNcbiAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgICAgSHRtbC5mcmVzaEVsZW1lbnQoXCJhXCIsIHtcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogXCIjXCIgKyByZWZlcmVudEh0bWxJZChcImNvbW1lbnRcIiwgcmVmZXJlbmNlLmNvbW1lbnRJZCksXG4gICAgICAgICAgICAgICAgICAgIGlkOiByZWZlcmVuY2VIdG1sSWQoXCJjb21tZW50XCIsIHJlZmVyZW5jZS5jb21tZW50SWQpXG4gICAgICAgICAgICAgICAgfSwgW0h0bWwudGV4dChsYWJlbCldKVxuICAgICAgICAgICAgXTtcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gY29udmVydENvbW1lbnQocmVmZXJlbmNlZENvbW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIC8vIFRPRE86IHJlbW92ZSBkdXBsaWNhdGlvbiB3aXRoIG5vdGUgcmVmZXJlbmNlc1xuXG4gICAgICAgIHZhciBsYWJlbCA9IHJlZmVyZW5jZWRDb21tZW50LmxhYmVsO1xuICAgICAgICB2YXIgY29tbWVudCA9IHJlZmVyZW5jZWRDb21tZW50LmNvbW1lbnQ7XG4gICAgICAgIHZhciBib2R5ID0gY29udmVydEVsZW1lbnRzKGNvbW1lbnQuYm9keSwgbWVzc2FnZXMsIG9wdGlvbnMpLmNvbmNhdChbXG4gICAgICAgICAgICBIdG1sLm5vbkZyZXNoRWxlbWVudChcInBcIiwge30sIFtcbiAgICAgICAgICAgICAgICBIdG1sLnRleHQoXCIgXCIpLFxuICAgICAgICAgICAgICAgIEh0bWwuZnJlc2hFbGVtZW50KFwiYVwiLCB7XCJocmVmXCI6IFwiI1wiICsgcmVmZXJlbmNlSHRtbElkKFwiY29tbWVudFwiLCBjb21tZW50LmNvbW1lbnRJZCl9LCBbXG4gICAgICAgICAgICAgICAgICAgIEh0bWwudGV4dChcIuKGkVwiKVxuICAgICAgICAgICAgICAgIF0pXG4gICAgICAgICAgICBdKVxuICAgICAgICBdKTtcblxuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgSHRtbC5mcmVzaEVsZW1lbnQoXG4gICAgICAgICAgICAgICAgXCJkdFwiLFxuICAgICAgICAgICAgICAgIHtcImlkXCI6IHJlZmVyZW50SHRtbElkKFwiY29tbWVudFwiLCBjb21tZW50LmNvbW1lbnRJZCl9LFxuICAgICAgICAgICAgICAgIFtIdG1sLnRleHQoXCJDb21tZW50IFwiICsgbGFiZWwpXVxuICAgICAgICAgICAgKSxcbiAgICAgICAgICAgIEh0bWwuZnJlc2hFbGVtZW50KFwiZGRcIiwge30sIGJvZHkpXG4gICAgICAgIF07XG4gICAgfVxuXG4gICAgZnVuY3Rpb24gY29udmVydEJyZWFrKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBodG1sUGF0aEZvckJyZWFrKGVsZW1lbnQpLndyYXAoZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGh0bWxQYXRoRm9yQnJlYWsoZWxlbWVudCkge1xuICAgICAgICB2YXIgc3R5bGUgPSBmaW5kU3R5bGUoZWxlbWVudCk7XG4gICAgICAgIGlmIChzdHlsZSkge1xuICAgICAgICAgICAgcmV0dXJuIHN0eWxlLnRvO1xuICAgICAgICB9IGVsc2UgaWYgKGVsZW1lbnQuYnJlYWtUeXBlID09PSBcImxpbmVcIikge1xuICAgICAgICAgICAgcmV0dXJuIGh0bWxQYXRocy50b3BMZXZlbEVsZW1lbnQoXCJiclwiKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBodG1sUGF0aHMuZW1wdHk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICB2YXIgZWxlbWVudENvbnZlcnRlcnMgPSB7XG4gICAgICAgIFwiZG9jdW1lbnRcIjogZnVuY3Rpb24oZG9jdW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgICAgICB2YXIgY2hpbGRyZW4gPSBjb252ZXJ0RWxlbWVudHMoZG9jdW1lbnQuY2hpbGRyZW4sIG1lc3NhZ2VzLCBvcHRpb25zKTtcbiAgICAgICAgICAgIHZhciBub3RlcyA9IG5vdGVSZWZlcmVuY2VzLm1hcChmdW5jdGlvbihub3RlUmVmZXJlbmNlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50Lm5vdGVzLnJlc29sdmUobm90ZVJlZmVyZW5jZSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHZhciBub3Rlc05vZGVzID0gY29udmVydEVsZW1lbnRzKG5vdGVzLCBtZXNzYWdlcywgb3B0aW9ucyk7XG4gICAgICAgICAgICByZXR1cm4gY2hpbGRyZW4uY29uY2F0KFtcbiAgICAgICAgICAgICAgICBIdG1sLmZyZXNoRWxlbWVudChcIm9sXCIsIHt9LCBub3Rlc05vZGVzKSxcbiAgICAgICAgICAgICAgICBIdG1sLmZyZXNoRWxlbWVudChcImRsXCIsIHt9LCBmbGF0TWFwKHJlZmVyZW5jZWRDb21tZW50cywgZnVuY3Rpb24ocmVmZXJlbmNlZENvbW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRDb21tZW50KHJlZmVyZW5jZWRDb21tZW50LCBtZXNzYWdlcywgb3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICBdKTtcbiAgICAgICAgfSxcbiAgICAgICAgXCJwYXJhZ3JhcGhcIjogY29udmVydFBhcmFncmFwaCxcbiAgICAgICAgXCJydW5cIjogY29udmVydFJ1bixcbiAgICAgICAgXCJ0ZXh0XCI6IGZ1bmN0aW9uKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgICAgICByZXR1cm4gW0h0bWwudGV4dChlbGVtZW50LnZhbHVlKV07XG4gICAgICAgIH0sXG4gICAgICAgIFwidGFiXCI6IGZ1bmN0aW9uKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgICAgICByZXR1cm4gW0h0bWwudGV4dChcIlxcdFwiKV07XG4gICAgICAgIH0sXG4gICAgICAgIFwiaHlwZXJsaW5rXCI6IGZ1bmN0aW9uKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKSB7XG4gICAgICAgICAgICB2YXIgaHJlZiA9IGVsZW1lbnQuYW5jaG9yID8gXCIjXCIgKyBodG1sSWQoZWxlbWVudC5hbmNob3IpIDogZWxlbWVudC5ocmVmO1xuICAgICAgICAgICAgdmFyIGF0dHJpYnV0ZXMgPSB7aHJlZjogaHJlZn07XG4gICAgICAgICAgICBpZiAoZWxlbWVudC50YXJnZXRGcmFtZSAhPSBudWxsKSB7XG4gICAgICAgICAgICAgICAgYXR0cmlidXRlcy50YXJnZXQgPSBlbGVtZW50LnRhcmdldEZyYW1lO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICB2YXIgY2hpbGRyZW4gPSBjb252ZXJ0RWxlbWVudHMoZWxlbWVudC5jaGlsZHJlbiwgbWVzc2FnZXMsIG9wdGlvbnMpO1xuICAgICAgICAgICAgcmV0dXJuIFtIdG1sLm5vbkZyZXNoRWxlbWVudChcImFcIiwgYXR0cmlidXRlcywgY2hpbGRyZW4pXTtcbiAgICAgICAgfSxcbiAgICAgICAgXCJjaGVja2JveFwiOiBmdW5jdGlvbihlbGVtZW50KSB7XG4gICAgICAgICAgICB2YXIgYXR0cmlidXRlcyA9IHt0eXBlOiBcImNoZWNrYm94XCJ9O1xuICAgICAgICAgICAgaWYgKGVsZW1lbnQuY2hlY2tlZCkge1xuICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXNbXCJjaGVja2VkXCJdID0gXCJjaGVja2VkXCI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gW0h0bWwuZnJlc2hFbGVtZW50KFwiaW5wdXRcIiwgYXR0cmlidXRlcyldO1xuICAgICAgICB9LFxuICAgICAgICBcImJvb2ttYXJrU3RhcnRcIjogZnVuY3Rpb24oZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIHZhciBhbmNob3IgPSBIdG1sLmZyZXNoRWxlbWVudChcImFcIiwge1xuICAgICAgICAgICAgICAgIGlkOiBodG1sSWQoZWxlbWVudC5uYW1lKVxuICAgICAgICAgICAgfSwgW0h0bWwuZm9yY2VXcml0ZV0pO1xuICAgICAgICAgICAgcmV0dXJuIFthbmNob3JdO1xuICAgICAgICB9LFxuICAgICAgICBcIm5vdGVSZWZlcmVuY2VcIjogZnVuY3Rpb24oZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIG5vdGVSZWZlcmVuY2VzLnB1c2goZWxlbWVudCk7XG4gICAgICAgICAgICB2YXIgYW5jaG9yID0gSHRtbC5mcmVzaEVsZW1lbnQoXCJhXCIsIHtcbiAgICAgICAgICAgICAgICBocmVmOiBcIiNcIiArIG5vdGVIdG1sSWQoZWxlbWVudCksXG4gICAgICAgICAgICAgICAgaWQ6IG5vdGVSZWZIdG1sSWQoZWxlbWVudClcbiAgICAgICAgICAgIH0sIFtIdG1sLnRleHQoXCJbXCIgKyAobm90ZU51bWJlcisrKSArIFwiXVwiKV0pO1xuXG4gICAgICAgICAgICByZXR1cm4gW0h0bWwuZnJlc2hFbGVtZW50KFwic3VwXCIsIHt9LCBbYW5jaG9yXSldO1xuICAgICAgICB9LFxuICAgICAgICBcIm5vdGVcIjogZnVuY3Rpb24oZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIHZhciBjaGlsZHJlbiA9IGNvbnZlcnRFbGVtZW50cyhlbGVtZW50LmJvZHksIG1lc3NhZ2VzLCBvcHRpb25zKTtcbiAgICAgICAgICAgIHZhciBiYWNrTGluayA9IEh0bWwuZWxlbWVudFdpdGhUYWcoaHRtbFBhdGhzLmVsZW1lbnQoXCJwXCIsIHt9LCB7ZnJlc2g6IGZhbHNlfSksIFtcbiAgICAgICAgICAgICAgICBIdG1sLnRleHQoXCIgXCIpLFxuICAgICAgICAgICAgICAgIEh0bWwuZnJlc2hFbGVtZW50KFwiYVwiLCB7aHJlZjogXCIjXCIgKyBub3RlUmVmSHRtbElkKGVsZW1lbnQpfSwgW0h0bWwudGV4dChcIuKGkVwiKV0pXG4gICAgICAgICAgICBdKTtcbiAgICAgICAgICAgIHZhciBib2R5ID0gY2hpbGRyZW4uY29uY2F0KFtiYWNrTGlua10pO1xuXG4gICAgICAgICAgICByZXR1cm4gSHRtbC5mcmVzaEVsZW1lbnQoXCJsaVwiLCB7aWQ6IG5vdGVIdG1sSWQoZWxlbWVudCl9LCBib2R5KTtcbiAgICAgICAgfSxcbiAgICAgICAgXCJjb21tZW50UmVmZXJlbmNlXCI6IGNvbnZlcnRDb21tZW50UmVmZXJlbmNlLFxuICAgICAgICBcImNvbW1lbnRcIjogY29udmVydENvbW1lbnQsXG4gICAgICAgIFwiaW1hZ2VcIjogZGVmZXJyZWRDb252ZXJzaW9uKHJlY292ZXJpbmdDb252ZXJ0SW1hZ2Uob3B0aW9ucy5jb252ZXJ0SW1hZ2UgfHwgaW1hZ2VzLmRhdGFVcmkpKSxcbiAgICAgICAgXCJ0YWJsZVwiOiBjb252ZXJ0VGFibGUsXG4gICAgICAgIFwidGFibGVSb3dcIjogY29udmVydFRhYmxlUm93LFxuICAgICAgICBcInRhYmxlQ2VsbFwiOiBjb252ZXJ0VGFibGVDZWxsLFxuICAgICAgICBcImJyZWFrXCI6IGNvbnZlcnRCcmVha1xuICAgIH07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY29udmVydFRvSHRtbDogY29udmVydFRvSHRtbFxuICAgIH07XG59XG5cbnZhciBkZWZlcnJlZElkID0gMTtcblxuZnVuY3Rpb24gZGVmZXJyZWRDb252ZXJzaW9uKGZ1bmMpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24oZWxlbWVudCwgbWVzc2FnZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0eXBlOiBcImRlZmVycmVkXCIsXG4gICAgICAgICAgICAgICAgaWQ6IGRlZmVycmVkSWQrKyxcbiAgICAgICAgICAgICAgICB2YWx1ZTogZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmdW5jKGVsZW1lbnQsIG1lc3NhZ2VzLCBvcHRpb25zKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIF07XG4gICAgfTtcbn1cblxuZnVuY3Rpb24gdW5yZWNvZ25pc2VkU3R5bGVXYXJuaW5nKHR5cGUsIGVsZW1lbnQpIHtcbiAgICByZXR1cm4gcmVzdWx0cy53YXJuaW5nKFxuICAgICAgICBcIlVucmVjb2duaXNlZCBcIiArIHR5cGUgKyBcIiBzdHlsZTogJ1wiICsgZWxlbWVudC5zdHlsZU5hbWUgKyBcIidcIiArXG4gICAgICAgIFwiIChTdHlsZSBJRDogXCIgKyBlbGVtZW50LnN0eWxlSWQgKyBcIilcIlxuICAgICk7XG59XG5cbmZ1bmN0aW9uIGZsYXRNYXAodmFsdWVzLCBmdW5jKSB7XG4gICAgcmV0dXJuIF8uZmxhdHRlbih2YWx1ZXMubWFwKGZ1bmMpLCB0cnVlKTtcbn1cblxuZnVuY3Rpb24gd2Fsa0h0bWwobm9kZXMsIGNhbGxiYWNrKSB7XG4gICAgbm9kZXMuZm9yRWFjaChmdW5jdGlvbihub2RlKSB7XG4gICAgICAgIGNhbGxiYWNrKG5vZGUpO1xuICAgICAgICBpZiAobm9kZS5jaGlsZHJlbikge1xuICAgICAgICAgICAgd2Fsa0h0bWwobm9kZS5jaGlsZHJlbiwgY2FsbGJhY2spO1xuICAgICAgICB9XG4gICAgfSk7XG59XG5cbnZhciBjb21tZW50QXV0aG9yTGFiZWwgPSBleHBvcnRzLmNvbW1lbnRBdXRob3JMYWJlbCA9IGZ1bmN0aW9uIGNvbW1lbnRBdXRob3JMYWJlbChjb21tZW50KSB7XG4gICAgcmV0dXJuIGNvbW1lbnQuYXV0aG9ySW5pdGlhbHMgfHwgXCJcIjtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/document-to-html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/documents.js":
/*!***********************************************!*\
  !*** ./node_modules/mammoth/lib/documents.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar types = exports.types = {\n    document: \"document\",\n    paragraph: \"paragraph\",\n    run: \"run\",\n    text: \"text\",\n    tab: \"tab\",\n    checkbox: \"checkbox\",\n    hyperlink: \"hyperlink\",\n    noteReference: \"noteReference\",\n    image: \"image\",\n    note: \"note\",\n    commentReference: \"commentReference\",\n    comment: \"comment\",\n    table: \"table\",\n    tableRow: \"tableRow\",\n    tableCell: \"tableCell\",\n    \"break\": \"break\",\n    bookmarkStart: \"bookmarkStart\"\n};\n\nfunction Document(children, options) {\n    options = options || {};\n    return {\n        type: types.document,\n        children: children,\n        notes: options.notes || new Notes({}),\n        comments: options.comments || []\n    };\n}\n\nfunction Paragraph(children, properties) {\n    properties = properties || {};\n    var indent = properties.indent || {};\n    return {\n        type: types.paragraph,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null,\n        numbering: properties.numbering || null,\n        alignment: properties.alignment || null,\n        indent: {\n            start: indent.start || null,\n            end: indent.end || null,\n            firstLine: indent.firstLine || null,\n            hanging: indent.hanging || null\n        }\n    };\n}\n\nfunction Run(children, properties) {\n    properties = properties || {};\n    return {\n        type: types.run,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null,\n        isBold: !!properties.isBold,\n        isUnderline: !!properties.isUnderline,\n        isItalic: !!properties.isItalic,\n        isStrikethrough: !!properties.isStrikethrough,\n        isAllCaps: !!properties.isAllCaps,\n        isSmallCaps: !!properties.isSmallCaps,\n        verticalAlignment: properties.verticalAlignment || verticalAlignment.baseline,\n        font: properties.font || null,\n        fontSize: properties.fontSize || null,\n        highlight: properties.highlight || null\n    };\n}\n\nvar verticalAlignment = {\n    baseline: \"baseline\",\n    superscript: \"superscript\",\n    subscript: \"subscript\"\n};\n\nfunction Text(value) {\n    return {\n        type: types.text,\n        value: value\n    };\n}\n\nfunction Tab() {\n    return {\n        type: types.tab\n    };\n}\n\nfunction Checkbox(options) {\n    return {\n        type: types.checkbox,\n        checked: options.checked\n    };\n}\n\nfunction Hyperlink(children, options) {\n    return {\n        type: types.hyperlink,\n        children: children,\n        href: options.href,\n        anchor: options.anchor,\n        targetFrame: options.targetFrame\n    };\n}\n\nfunction NoteReference(options) {\n    return {\n        type: types.noteReference,\n        noteType: options.noteType,\n        noteId: options.noteId\n    };\n}\n\nfunction Notes(notes) {\n    this._notes = _.indexBy(notes, function(note) {\n        return noteKey(note.noteType, note.noteId);\n    });\n}\n\nNotes.prototype.resolve = function(reference) {\n    return this.findNoteByKey(noteKey(reference.noteType, reference.noteId));\n};\n\nNotes.prototype.findNoteByKey = function(key) {\n    return this._notes[key] || null;\n};\n\nfunction Note(options) {\n    return {\n        type: types.note,\n        noteType: options.noteType,\n        noteId: options.noteId,\n        body: options.body\n    };\n}\n\nfunction commentReference(options) {\n    return {\n        type: types.commentReference,\n        commentId: options.commentId\n    };\n}\n\nfunction comment(options) {\n    return {\n        type: types.comment,\n        commentId: options.commentId,\n        body: options.body,\n        authorName: options.authorName,\n        authorInitials: options.authorInitials\n    };\n}\n\nfunction noteKey(noteType, id) {\n    return noteType + \"-\" + id;\n}\n\nfunction Image(options) {\n    return {\n        type: types.image,\n        // `read` is retained for backwards compatibility, but other read\n        // methods should be preferred.\n        read: function(encoding) {\n            if (encoding) {\n                return options.readImage(encoding);\n            } else {\n                return options.readImage().then(function(arrayBuffer) {\n                    return Buffer.from(arrayBuffer);\n                });\n            }\n        },\n        readAsArrayBuffer: function() {\n            return options.readImage();\n        },\n        readAsBase64String: function() {\n            return options.readImage(\"base64\");\n        },\n        readAsBuffer: function() {\n            return options.readImage().then(function(arrayBuffer) {\n                return Buffer.from(arrayBuffer);\n            });\n        },\n        altText: options.altText,\n        contentType: options.contentType\n    };\n}\n\nfunction Table(children, properties) {\n    properties = properties || {};\n    return {\n        type: types.table,\n        children: children,\n        styleId: properties.styleId || null,\n        styleName: properties.styleName || null\n    };\n}\n\nfunction TableRow(children, options) {\n    options = options || {};\n    return {\n        type: types.tableRow,\n        children: children,\n        isHeader: options.isHeader || false\n    };\n}\n\nfunction TableCell(children, options) {\n    options = options || {};\n    return {\n        type: types.tableCell,\n        children: children,\n        colSpan: options.colSpan == null ? 1 : options.colSpan,\n        rowSpan: options.rowSpan == null ? 1 : options.rowSpan\n    };\n}\n\nfunction Break(breakType) {\n    return {\n        type: types[\"break\"],\n        breakType: breakType\n    };\n}\n\nfunction BookmarkStart(options) {\n    return {\n        type: types.bookmarkStart,\n        name: options.name\n    };\n}\n\nexports.document = exports.Document = Document;\nexports.paragraph = exports.Paragraph = Paragraph;\nexports.run = exports.Run = Run;\nexports.text = exports.Text = Text;\nexports.tab = exports.Tab = Tab;\nexports.checkbox = exports.Checkbox = Checkbox;\nexports.Hyperlink = Hyperlink;\nexports.noteReference = exports.NoteReference = NoteReference;\nexports.Notes = Notes;\nexports.Note = Note;\nexports.commentReference = commentReference;\nexports.comment = comment;\nexports.Image = Image;\nexports.Table = Table;\nexports.TableRow = TableRow;\nexports.TableCell = TableCell;\nexports.lineBreak = Break(\"line\");\nexports.pageBreak = Break(\"page\");\nexports.columnBreak = Break(\"column\");\nexports.BookmarkStart = BookmarkStart;\n\nexports.verticalAlignment = verticalAlignment;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvZG9jdW1lbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsbUJBQU8sQ0FBQyx3RUFBWTs7QUFFNUIsWUFBWSxhQUFhO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDO0FBQzVDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdCQUFnQixHQUFHLGdCQUFnQjtBQUNuQyxpQkFBaUIsR0FBRyxpQkFBaUI7QUFDckMsV0FBVyxHQUFHLFdBQVc7QUFDekIsWUFBWSxHQUFHLFlBQVk7QUFDM0IsV0FBVyxHQUFHLFdBQVc7QUFDekIsZ0JBQWdCLEdBQUcsZ0JBQWdCO0FBQ25DLGlCQUFpQjtBQUNqQixxQkFBcUIsR0FBRyxxQkFBcUI7QUFDN0MsYUFBYTtBQUNiLFlBQVk7QUFDWix3QkFBd0I7QUFDeEIsZUFBZTtBQUNmLGFBQWE7QUFDYixhQUFhO0FBQ2IsZ0JBQWdCO0FBQ2hCLGlCQUFpQjtBQUNqQixpQkFBaUI7QUFDakIsaUJBQWlCO0FBQ2pCLG1CQUFtQjtBQUNuQixxQkFBcUI7O0FBRXJCLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXGRvY3VtZW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgXyA9IHJlcXVpcmUoXCJ1bmRlcnNjb3JlXCIpO1xuXG52YXIgdHlwZXMgPSBleHBvcnRzLnR5cGVzID0ge1xuICAgIGRvY3VtZW50OiBcImRvY3VtZW50XCIsXG4gICAgcGFyYWdyYXBoOiBcInBhcmFncmFwaFwiLFxuICAgIHJ1bjogXCJydW5cIixcbiAgICB0ZXh0OiBcInRleHRcIixcbiAgICB0YWI6IFwidGFiXCIsXG4gICAgY2hlY2tib3g6IFwiY2hlY2tib3hcIixcbiAgICBoeXBlcmxpbms6IFwiaHlwZXJsaW5rXCIsXG4gICAgbm90ZVJlZmVyZW5jZTogXCJub3RlUmVmZXJlbmNlXCIsXG4gICAgaW1hZ2U6IFwiaW1hZ2VcIixcbiAgICBub3RlOiBcIm5vdGVcIixcbiAgICBjb21tZW50UmVmZXJlbmNlOiBcImNvbW1lbnRSZWZlcmVuY2VcIixcbiAgICBjb21tZW50OiBcImNvbW1lbnRcIixcbiAgICB0YWJsZTogXCJ0YWJsZVwiLFxuICAgIHRhYmxlUm93OiBcInRhYmxlUm93XCIsXG4gICAgdGFibGVDZWxsOiBcInRhYmxlQ2VsbFwiLFxuICAgIFwiYnJlYWtcIjogXCJicmVha1wiLFxuICAgIGJvb2ttYXJrU3RhcnQ6IFwiYm9va21hcmtTdGFydFwiXG59O1xuXG5mdW5jdGlvbiBEb2N1bWVudChjaGlsZHJlbiwgb3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLmRvY3VtZW50LFxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4sXG4gICAgICAgIG5vdGVzOiBvcHRpb25zLm5vdGVzIHx8IG5ldyBOb3Rlcyh7fSksXG4gICAgICAgIGNvbW1lbnRzOiBvcHRpb25zLmNvbW1lbnRzIHx8IFtdXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gUGFyYWdyYXBoKGNoaWxkcmVuLCBwcm9wZXJ0aWVzKSB7XG4gICAgcHJvcGVydGllcyA9IHByb3BlcnRpZXMgfHwge307XG4gICAgdmFyIGluZGVudCA9IHByb3BlcnRpZXMuaW5kZW50IHx8IHt9O1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLnBhcmFncmFwaCxcbiAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuLFxuICAgICAgICBzdHlsZUlkOiBwcm9wZXJ0aWVzLnN0eWxlSWQgfHwgbnVsbCxcbiAgICAgICAgc3R5bGVOYW1lOiBwcm9wZXJ0aWVzLnN0eWxlTmFtZSB8fCBudWxsLFxuICAgICAgICBudW1iZXJpbmc6IHByb3BlcnRpZXMubnVtYmVyaW5nIHx8IG51bGwsXG4gICAgICAgIGFsaWdubWVudDogcHJvcGVydGllcy5hbGlnbm1lbnQgfHwgbnVsbCxcbiAgICAgICAgaW5kZW50OiB7XG4gICAgICAgICAgICBzdGFydDogaW5kZW50LnN0YXJ0IHx8IG51bGwsXG4gICAgICAgICAgICBlbmQ6IGluZGVudC5lbmQgfHwgbnVsbCxcbiAgICAgICAgICAgIGZpcnN0TGluZTogaW5kZW50LmZpcnN0TGluZSB8fCBudWxsLFxuICAgICAgICAgICAgaGFuZ2luZzogaW5kZW50LmhhbmdpbmcgfHwgbnVsbFxuICAgICAgICB9XG4gICAgfTtcbn1cblxuZnVuY3Rpb24gUnVuKGNoaWxkcmVuLCBwcm9wZXJ0aWVzKSB7XG4gICAgcHJvcGVydGllcyA9IHByb3BlcnRpZXMgfHwge307XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogdHlwZXMucnVuLFxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4sXG4gICAgICAgIHN0eWxlSWQ6IHByb3BlcnRpZXMuc3R5bGVJZCB8fCBudWxsLFxuICAgICAgICBzdHlsZU5hbWU6IHByb3BlcnRpZXMuc3R5bGVOYW1lIHx8IG51bGwsXG4gICAgICAgIGlzQm9sZDogISFwcm9wZXJ0aWVzLmlzQm9sZCxcbiAgICAgICAgaXNVbmRlcmxpbmU6ICEhcHJvcGVydGllcy5pc1VuZGVybGluZSxcbiAgICAgICAgaXNJdGFsaWM6ICEhcHJvcGVydGllcy5pc0l0YWxpYyxcbiAgICAgICAgaXNTdHJpa2V0aHJvdWdoOiAhIXByb3BlcnRpZXMuaXNTdHJpa2V0aHJvdWdoLFxuICAgICAgICBpc0FsbENhcHM6ICEhcHJvcGVydGllcy5pc0FsbENhcHMsXG4gICAgICAgIGlzU21hbGxDYXBzOiAhIXByb3BlcnRpZXMuaXNTbWFsbENhcHMsXG4gICAgICAgIHZlcnRpY2FsQWxpZ25tZW50OiBwcm9wZXJ0aWVzLnZlcnRpY2FsQWxpZ25tZW50IHx8IHZlcnRpY2FsQWxpZ25tZW50LmJhc2VsaW5lLFxuICAgICAgICBmb250OiBwcm9wZXJ0aWVzLmZvbnQgfHwgbnVsbCxcbiAgICAgICAgZm9udFNpemU6IHByb3BlcnRpZXMuZm9udFNpemUgfHwgbnVsbCxcbiAgICAgICAgaGlnaGxpZ2h0OiBwcm9wZXJ0aWVzLmhpZ2hsaWdodCB8fCBudWxsXG4gICAgfTtcbn1cblxudmFyIHZlcnRpY2FsQWxpZ25tZW50ID0ge1xuICAgIGJhc2VsaW5lOiBcImJhc2VsaW5lXCIsXG4gICAgc3VwZXJzY3JpcHQ6IFwic3VwZXJzY3JpcHRcIixcbiAgICBzdWJzY3JpcHQ6IFwic3Vic2NyaXB0XCJcbn07XG5cbmZ1bmN0aW9uIFRleHQodmFsdWUpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiB0eXBlcy50ZXh0LFxuICAgICAgICB2YWx1ZTogdmFsdWVcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBUYWIoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogdHlwZXMudGFiXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gQ2hlY2tib3gob3B0aW9ucykge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLmNoZWNrYm94LFxuICAgICAgICBjaGVja2VkOiBvcHRpb25zLmNoZWNrZWRcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBIeXBlcmxpbmsoY2hpbGRyZW4sIG9wdGlvbnMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiB0eXBlcy5oeXBlcmxpbmssXG4gICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlbixcbiAgICAgICAgaHJlZjogb3B0aW9ucy5ocmVmLFxuICAgICAgICBhbmNob3I6IG9wdGlvbnMuYW5jaG9yLFxuICAgICAgICB0YXJnZXRGcmFtZTogb3B0aW9ucy50YXJnZXRGcmFtZVxuICAgIH07XG59XG5cbmZ1bmN0aW9uIE5vdGVSZWZlcmVuY2Uob3B0aW9ucykge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLm5vdGVSZWZlcmVuY2UsXG4gICAgICAgIG5vdGVUeXBlOiBvcHRpb25zLm5vdGVUeXBlLFxuICAgICAgICBub3RlSWQ6IG9wdGlvbnMubm90ZUlkXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gTm90ZXMobm90ZXMpIHtcbiAgICB0aGlzLl9ub3RlcyA9IF8uaW5kZXhCeShub3RlcywgZnVuY3Rpb24obm90ZSkge1xuICAgICAgICByZXR1cm4gbm90ZUtleShub3RlLm5vdGVUeXBlLCBub3RlLm5vdGVJZCk7XG4gICAgfSk7XG59XG5cbk5vdGVzLnByb3RvdHlwZS5yZXNvbHZlID0gZnVuY3Rpb24ocmVmZXJlbmNlKSB7XG4gICAgcmV0dXJuIHRoaXMuZmluZE5vdGVCeUtleShub3RlS2V5KHJlZmVyZW5jZS5ub3RlVHlwZSwgcmVmZXJlbmNlLm5vdGVJZCkpO1xufTtcblxuTm90ZXMucHJvdG90eXBlLmZpbmROb3RlQnlLZXkgPSBmdW5jdGlvbihrZXkpIHtcbiAgICByZXR1cm4gdGhpcy5fbm90ZXNba2V5XSB8fCBudWxsO1xufTtcblxuZnVuY3Rpb24gTm90ZShvcHRpb25zKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogdHlwZXMubm90ZSxcbiAgICAgICAgbm90ZVR5cGU6IG9wdGlvbnMubm90ZVR5cGUsXG4gICAgICAgIG5vdGVJZDogb3B0aW9ucy5ub3RlSWQsXG4gICAgICAgIGJvZHk6IG9wdGlvbnMuYm9keVxuICAgIH07XG59XG5cbmZ1bmN0aW9uIGNvbW1lbnRSZWZlcmVuY2Uob3B0aW9ucykge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLmNvbW1lbnRSZWZlcmVuY2UsXG4gICAgICAgIGNvbW1lbnRJZDogb3B0aW9ucy5jb21tZW50SWRcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBjb21tZW50KG9wdGlvbnMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiB0eXBlcy5jb21tZW50LFxuICAgICAgICBjb21tZW50SWQ6IG9wdGlvbnMuY29tbWVudElkLFxuICAgICAgICBib2R5OiBvcHRpb25zLmJvZHksXG4gICAgICAgIGF1dGhvck5hbWU6IG9wdGlvbnMuYXV0aG9yTmFtZSxcbiAgICAgICAgYXV0aG9ySW5pdGlhbHM6IG9wdGlvbnMuYXV0aG9ySW5pdGlhbHNcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBub3RlS2V5KG5vdGVUeXBlLCBpZCkge1xuICAgIHJldHVybiBub3RlVHlwZSArIFwiLVwiICsgaWQ7XG59XG5cbmZ1bmN0aW9uIEltYWdlKG9wdGlvbnMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiB0eXBlcy5pbWFnZSxcbiAgICAgICAgLy8gYHJlYWRgIGlzIHJldGFpbmVkIGZvciBiYWNrd2FyZHMgY29tcGF0aWJpbGl0eSwgYnV0IG90aGVyIHJlYWRcbiAgICAgICAgLy8gbWV0aG9kcyBzaG91bGQgYmUgcHJlZmVycmVkLlxuICAgICAgICByZWFkOiBmdW5jdGlvbihlbmNvZGluZykge1xuICAgICAgICAgICAgaWYgKGVuY29kaW5nKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9wdGlvbnMucmVhZEltYWdlKGVuY29kaW5nKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9wdGlvbnMucmVhZEltYWdlKCkudGhlbihmdW5jdGlvbihhcnJheUJ1ZmZlcikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oYXJyYXlCdWZmZXIpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICByZWFkQXNBcnJheUJ1ZmZlcjogZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICByZXR1cm4gb3B0aW9ucy5yZWFkSW1hZ2UoKTtcbiAgICAgICAgfSxcbiAgICAgICAgcmVhZEFzQmFzZTY0U3RyaW5nOiBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgIHJldHVybiBvcHRpb25zLnJlYWRJbWFnZShcImJhc2U2NFwiKTtcbiAgICAgICAgfSxcbiAgICAgICAgcmVhZEFzQnVmZmVyOiBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgIHJldHVybiBvcHRpb25zLnJlYWRJbWFnZSgpLnRoZW4oZnVuY3Rpb24oYXJyYXlCdWZmZXIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oYXJyYXlCdWZmZXIpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIGFsdFRleHQ6IG9wdGlvbnMuYWx0VGV4dCxcbiAgICAgICAgY29udGVudFR5cGU6IG9wdGlvbnMuY29udGVudFR5cGVcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBUYWJsZShjaGlsZHJlbiwgcHJvcGVydGllcykge1xuICAgIHByb3BlcnRpZXMgPSBwcm9wZXJ0aWVzIHx8IHt9O1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLnRhYmxlLFxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4sXG4gICAgICAgIHN0eWxlSWQ6IHByb3BlcnRpZXMuc3R5bGVJZCB8fCBudWxsLFxuICAgICAgICBzdHlsZU5hbWU6IHByb3BlcnRpZXMuc3R5bGVOYW1lIHx8IG51bGxcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBUYWJsZVJvdyhjaGlsZHJlbiwgb3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IHR5cGVzLnRhYmxlUm93LFxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4sXG4gICAgICAgIGlzSGVhZGVyOiBvcHRpb25zLmlzSGVhZGVyIHx8IGZhbHNlXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gVGFibGVDZWxsKGNoaWxkcmVuLCBvcHRpb25zKSB7XG4gICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogdHlwZXMudGFibGVDZWxsLFxuICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW4sXG4gICAgICAgIGNvbFNwYW46IG9wdGlvbnMuY29sU3BhbiA9PSBudWxsID8gMSA6IG9wdGlvbnMuY29sU3BhbixcbiAgICAgICAgcm93U3Bhbjogb3B0aW9ucy5yb3dTcGFuID09IG51bGwgPyAxIDogb3B0aW9ucy5yb3dTcGFuXG4gICAgfTtcbn1cblxuZnVuY3Rpb24gQnJlYWsoYnJlYWtUeXBlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogdHlwZXNbXCJicmVha1wiXSxcbiAgICAgICAgYnJlYWtUeXBlOiBicmVha1R5cGVcbiAgICB9O1xufVxuXG5mdW5jdGlvbiBCb29rbWFya1N0YXJ0KG9wdGlvbnMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiB0eXBlcy5ib29rbWFya1N0YXJ0LFxuICAgICAgICBuYW1lOiBvcHRpb25zLm5hbWVcbiAgICB9O1xufVxuXG5leHBvcnRzLmRvY3VtZW50ID0gZXhwb3J0cy5Eb2N1bWVudCA9IERvY3VtZW50O1xuZXhwb3J0cy5wYXJhZ3JhcGggPSBleHBvcnRzLlBhcmFncmFwaCA9IFBhcmFncmFwaDtcbmV4cG9ydHMucnVuID0gZXhwb3J0cy5SdW4gPSBSdW47XG5leHBvcnRzLnRleHQgPSBleHBvcnRzLlRleHQgPSBUZXh0O1xuZXhwb3J0cy50YWIgPSBleHBvcnRzLlRhYiA9IFRhYjtcbmV4cG9ydHMuY2hlY2tib3ggPSBleHBvcnRzLkNoZWNrYm94ID0gQ2hlY2tib3g7XG5leHBvcnRzLkh5cGVybGluayA9IEh5cGVybGluaztcbmV4cG9ydHMubm90ZVJlZmVyZW5jZSA9IGV4cG9ydHMuTm90ZVJlZmVyZW5jZSA9IE5vdGVSZWZlcmVuY2U7XG5leHBvcnRzLk5vdGVzID0gTm90ZXM7XG5leHBvcnRzLk5vdGUgPSBOb3RlO1xuZXhwb3J0cy5jb21tZW50UmVmZXJlbmNlID0gY29tbWVudFJlZmVyZW5jZTtcbmV4cG9ydHMuY29tbWVudCA9IGNvbW1lbnQ7XG5leHBvcnRzLkltYWdlID0gSW1hZ2U7XG5leHBvcnRzLlRhYmxlID0gVGFibGU7XG5leHBvcnRzLlRhYmxlUm93ID0gVGFibGVSb3c7XG5leHBvcnRzLlRhYmxlQ2VsbCA9IFRhYmxlQ2VsbDtcbmV4cG9ydHMubGluZUJyZWFrID0gQnJlYWsoXCJsaW5lXCIpO1xuZXhwb3J0cy5wYWdlQnJlYWsgPSBCcmVhayhcInBhZ2VcIik7XG5leHBvcnRzLmNvbHVtbkJyZWFrID0gQnJlYWsoXCJjb2x1bW5cIik7XG5leHBvcnRzLkJvb2ttYXJrU3RhcnQgPSBCb29rbWFya1N0YXJ0O1xuXG5leHBvcnRzLnZlcnRpY2FsQWxpZ25tZW50ID0gdmVydGljYWxBbGlnbm1lbnQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/documents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/body-reader.js":
/*!******************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/body-reader.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.createBodyReader = createBodyReader;\nexports._readNumberingProperties = readNumberingProperties;\n\nvar dingbatToUnicode = __webpack_require__(/*! dingbat-to-unicode */ \"(ssr)/./node_modules/dingbat-to-unicode/dist/index.js\");\nvar _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar documents = __webpack_require__(/*! ../documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar Result = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\nvar warning = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").warning);\nvar xml = __webpack_require__(/*! ../xml */ \"(ssr)/./node_modules/mammoth/lib/xml/index.js\");\nvar uris = __webpack_require__(/*! ./uris */ \"(ssr)/./node_modules/mammoth/lib/docx/uris.js\");\n\nfunction createBodyReader(options) {\n    return {\n        readXmlElement: function(element) {\n            return new BodyReader(options).readXmlElement(element);\n        },\n        readXmlElements: function(elements) {\n            return new BodyReader(options).readXmlElements(elements);\n        }\n    };\n}\n\nfunction BodyReader(options) {\n    var complexFieldStack = [];\n    var currentInstrText = [];\n\n    // When a paragraph is marked as deleted, its contents should be combined\n    // with the following paragraph. See ********** del (Deleted Paragraph) of\n    // ECMA-376 4th edition Part 1.\n    var deletedParagraphContents = [];\n\n    var relationships = options.relationships;\n    var contentTypes = options.contentTypes;\n    var docxFile = options.docxFile;\n    var files = options.files;\n    var numbering = options.numbering;\n    var styles = options.styles;\n\n    function readXmlElements(elements) {\n        var results = elements.map(readXmlElement);\n        return combineResults(results);\n    }\n\n    function readXmlElement(element) {\n        if (element.type === \"element\") {\n            var handler = xmlElementReaders[element.name];\n            if (handler) {\n                return handler(element);\n            } else if (!Object.prototype.hasOwnProperty.call(ignoreElements, element.name)) {\n                var message = warning(\"An unrecognised element was ignored: \" + element.name);\n                return emptyResultWithMessages([message]);\n            }\n        }\n        return emptyResult();\n    }\n\n    function readParagraphProperties(element) {\n        return readParagraphStyle(element).map(function(style) {\n            return {\n                type: \"paragraphProperties\",\n                styleId: style.styleId,\n                styleName: style.name,\n                alignment: element.firstOrEmpty(\"w:jc\").attributes[\"w:val\"],\n                numbering: readNumberingProperties(style.styleId, element.firstOrEmpty(\"w:numPr\"), numbering),\n                indent: readParagraphIndent(element.firstOrEmpty(\"w:ind\"))\n            };\n        });\n    }\n\n    function readParagraphIndent(element) {\n        return {\n            start: element.attributes[\"w:start\"] || element.attributes[\"w:left\"],\n            end: element.attributes[\"w:end\"] || element.attributes[\"w:right\"],\n            firstLine: element.attributes[\"w:firstLine\"],\n            hanging: element.attributes[\"w:hanging\"]\n        };\n    }\n\n    function readRunProperties(element) {\n        return readRunStyle(element).map(function(style) {\n            var fontSizeString = element.firstOrEmpty(\"w:sz\").attributes[\"w:val\"];\n            // w:sz gives the font size in half points, so halve the value to get the size in points\n            var fontSize = /^[0-9]+$/.test(fontSizeString) ? parseInt(fontSizeString, 10) / 2 : null;\n\n            return {\n                type: \"runProperties\",\n                styleId: style.styleId,\n                styleName: style.name,\n                verticalAlignment: element.firstOrEmpty(\"w:vertAlign\").attributes[\"w:val\"],\n                font: element.firstOrEmpty(\"w:rFonts\").attributes[\"w:ascii\"],\n                fontSize: fontSize,\n                isBold: readBooleanElement(element.first(\"w:b\")),\n                isUnderline: readUnderline(element.first(\"w:u\")),\n                isItalic: readBooleanElement(element.first(\"w:i\")),\n                isStrikethrough: readBooleanElement(element.first(\"w:strike\")),\n                isAllCaps: readBooleanElement(element.first(\"w:caps\")),\n                isSmallCaps: readBooleanElement(element.first(\"w:smallCaps\")),\n                highlight: readHighlightValue(element.firstOrEmpty(\"w:highlight\").attributes[\"w:val\"])\n            };\n        });\n    }\n\n    function readUnderline(element) {\n        if (element) {\n            var value = element.attributes[\"w:val\"];\n            return value !== undefined && value !== \"false\" && value !== \"0\" && value !== \"none\";\n        } else {\n            return false;\n        }\n    }\n\n    function readBooleanElement(element) {\n        if (element) {\n            var value = element.attributes[\"w:val\"];\n            return value !== \"false\" && value !== \"0\";\n        } else {\n            return false;\n        }\n    }\n\n    function readBooleanAttributeValue(value) {\n        return value !== \"false\" && value !== \"0\";\n    }\n\n    function readHighlightValue(value) {\n        if (!value || value === \"none\") {\n            return null;\n        } else {\n            return value;\n        }\n    }\n\n    function readParagraphStyle(element) {\n        return readStyle(element, \"w:pStyle\", \"Paragraph\", styles.findParagraphStyleById);\n    }\n\n    function readRunStyle(element) {\n        return readStyle(element, \"w:rStyle\", \"Run\", styles.findCharacterStyleById);\n    }\n\n    function readTableStyle(element) {\n        return readStyle(element, \"w:tblStyle\", \"Table\", styles.findTableStyleById);\n    }\n\n    function readStyle(element, styleTagName, styleType, findStyleById) {\n        var messages = [];\n        var styleElement = element.first(styleTagName);\n        var styleId = null;\n        var name = null;\n        if (styleElement) {\n            styleId = styleElement.attributes[\"w:val\"];\n            if (styleId) {\n                var style = findStyleById(styleId);\n                if (style) {\n                    name = style.name;\n                } else {\n                    messages.push(undefinedStyleWarning(styleType, styleId));\n                }\n            }\n        }\n        return elementResultWithMessages({styleId: styleId, name: name}, messages);\n    }\n\n    function readFldChar(element) {\n        var type = element.attributes[\"w:fldCharType\"];\n        if (type === \"begin\") {\n            complexFieldStack.push({type: \"begin\", fldChar: element});\n            currentInstrText = [];\n        } else if (type === \"end\") {\n            var complexFieldEnd = complexFieldStack.pop();\n            if (complexFieldEnd.type === \"begin\") {\n                complexFieldEnd = parseCurrentInstrText(complexFieldEnd);\n            }\n            if (complexFieldEnd.type === \"checkbox\") {\n                return elementResult(documents.checkbox({\n                    checked: complexFieldEnd.checked\n                }));\n            }\n        } else if (type === \"separate\") {\n            var complexFieldSeparate = complexFieldStack.pop();\n            var complexField = parseCurrentInstrText(complexFieldSeparate);\n            complexFieldStack.push(complexField);\n        }\n        return emptyResult();\n    }\n\n    function currentHyperlinkOptions() {\n        var topHyperlink = _.last(complexFieldStack.filter(function(complexField) {\n            return complexField.type === \"hyperlink\";\n        }));\n        return topHyperlink ? topHyperlink.options : null;\n    }\n\n    function parseCurrentInstrText(complexField) {\n        return parseInstrText(\n            currentInstrText.join(''),\n            complexField.type === \"begin\"\n                ? complexField.fldChar\n                : xml.emptyElement\n        );\n    }\n\n    function parseInstrText(instrText, fldChar) {\n        var externalLinkResult = /\\s*HYPERLINK \"(.*)\"/.exec(instrText);\n        if (externalLinkResult) {\n            return {type: \"hyperlink\", options: {href: externalLinkResult[1]}};\n        }\n\n        var internalLinkResult = /\\s*HYPERLINK\\s+\\\\l\\s+\"(.*)\"/.exec(instrText);\n        if (internalLinkResult) {\n            return {type: \"hyperlink\", options: {anchor: internalLinkResult[1]}};\n        }\n\n        var checkboxResult = /\\s*FORMCHECKBOX\\s*/.exec(instrText);\n        if (checkboxResult) {\n            var checkboxElement = fldChar\n                .firstOrEmpty(\"w:ffData\")\n                .firstOrEmpty(\"w:checkBox\");\n            var checkedElement = checkboxElement.first(\"w:checked\");\n            var checked = checkedElement == null\n                ? readBooleanElement(checkboxElement.first(\"w:default\"))\n                : readBooleanElement(checkedElement);\n            return {type: \"checkbox\", checked: checked};\n        }\n\n        return {type: \"unknown\"};\n    }\n\n    function readInstrText(element) {\n        currentInstrText.push(element.text());\n        return emptyResult();\n    }\n\n    function readSymbol(element) {\n        // See 17.3.3.30 sym (Symbol Character) of ECMA-376 4th edition Part 1\n        var font = element.attributes[\"w:font\"];\n        var char = element.attributes[\"w:char\"];\n        var unicodeCharacter = dingbatToUnicode.hex(font, char);\n        if (unicodeCharacter == null && /^F0..$/.test(char)) {\n            unicodeCharacter = dingbatToUnicode.hex(font, char.substring(2));\n        }\n\n        if (unicodeCharacter == null) {\n            return emptyResultWithMessages([warning(\n                \"A w:sym element with an unsupported character was ignored: char \" +  char + \" in font \" + font\n            )]);\n        } else {\n            return elementResult(new documents.Text(unicodeCharacter.string));\n        }\n    }\n\n    function noteReferenceReader(noteType) {\n        return function(element) {\n            var noteId = element.attributes[\"w:id\"];\n            return elementResult(new documents.NoteReference({\n                noteType: noteType,\n                noteId: noteId\n            }));\n        };\n    }\n\n    function readCommentReference(element) {\n        return elementResult(documents.commentReference({\n            commentId: element.attributes[\"w:id\"]\n        }));\n    }\n\n    function readChildElements(element) {\n        return readXmlElements(element.children);\n    }\n\n    var xmlElementReaders = {\n        \"w:p\": function(element) {\n            var paragraphPropertiesElement = element.firstOrEmpty(\"w:pPr\");\n\n            var isDeleted = !!paragraphPropertiesElement\n                .firstOrEmpty(\"w:rPr\")\n                .first(\"w:del\");\n\n            if (isDeleted) {\n                element.children.forEach(function(child) {\n                    deletedParagraphContents.push(child);\n                });\n                return emptyResult();\n            } else {\n                var childrenXml = element.children;\n                if (deletedParagraphContents.length > 0) {\n                    childrenXml = deletedParagraphContents.concat(childrenXml);\n                    deletedParagraphContents = [];\n                }\n                return ReadResult.map(\n                    readParagraphProperties(paragraphPropertiesElement),\n                    readXmlElements(childrenXml),\n                    function(properties, children) {\n                        return new documents.Paragraph(children, properties);\n                    }\n                ).insertExtra();\n            }\n        },\n        \"w:r\": function(element) {\n            return ReadResult.map(\n                readRunProperties(element.firstOrEmpty(\"w:rPr\")),\n                readXmlElements(element.children),\n                function(properties, children) {\n                    var hyperlinkOptions = currentHyperlinkOptions();\n                    if (hyperlinkOptions !== null) {\n                        children = [new documents.Hyperlink(children, hyperlinkOptions)];\n                    }\n\n                    return new documents.Run(children, properties);\n                }\n            );\n        },\n        \"w:fldChar\": readFldChar,\n        \"w:instrText\": readInstrText,\n        \"w:t\": function(element) {\n            return elementResult(new documents.Text(element.text()));\n        },\n        \"w:tab\": function(element) {\n            return elementResult(new documents.Tab());\n        },\n        \"w:noBreakHyphen\": function() {\n            return elementResult(new documents.Text(\"\\u2011\"));\n        },\n        \"w:softHyphen\": function(element) {\n            return elementResult(new documents.Text(\"\\u00AD\"));\n        },\n        \"w:sym\": readSymbol,\n        \"w:hyperlink\": function(element) {\n            var relationshipId = element.attributes[\"r:id\"];\n            var anchor = element.attributes[\"w:anchor\"];\n            return readXmlElements(element.children).map(function(children) {\n                function create(options) {\n                    var targetFrame = element.attributes[\"w:tgtFrame\"] || null;\n\n                    return new documents.Hyperlink(\n                        children,\n                        _.extend({targetFrame: targetFrame}, options)\n                    );\n                }\n\n                if (relationshipId) {\n                    var href = relationships.findTargetByRelationshipId(relationshipId);\n                    if (anchor) {\n                        href = uris.replaceFragment(href, anchor);\n                    }\n                    return create({href: href});\n                } else if (anchor) {\n                    return create({anchor: anchor});\n                } else {\n                    return children;\n                }\n            });\n        },\n        \"w:tbl\": readTable,\n        \"w:tr\": readTableRow,\n        \"w:tc\": readTableCell,\n        \"w:footnoteReference\": noteReferenceReader(\"footnote\"),\n        \"w:endnoteReference\": noteReferenceReader(\"endnote\"),\n        \"w:commentReference\": readCommentReference,\n        \"w:br\": function(element) {\n            var breakType = element.attributes[\"w:type\"];\n            if (breakType == null || breakType === \"textWrapping\") {\n                return elementResult(documents.lineBreak);\n            } else if (breakType === \"page\") {\n                return elementResult(documents.pageBreak);\n            } else if (breakType === \"column\") {\n                return elementResult(documents.columnBreak);\n            } else {\n                return emptyResultWithMessages([warning(\"Unsupported break type: \" + breakType)]);\n            }\n        },\n        \"w:bookmarkStart\": function(element){\n            var name = element.attributes[\"w:name\"];\n            if (name === \"_GoBack\") {\n                return emptyResult();\n            } else {\n                return elementResult(new documents.BookmarkStart({name: name}));\n            }\n        },\n\n        \"mc:AlternateContent\": function(element) {\n            return readChildElements(element.firstOrEmpty(\"mc:Fallback\"));\n        },\n\n        \"w:sdt\": function(element) {\n            var checkbox = element\n                .firstOrEmpty(\"w:sdtPr\")\n                .first(\"wordml:checkbox\");\n\n            if (checkbox) {\n                var checkedElement = checkbox.first(\"wordml:checked\");\n                var isChecked = !!checkedElement && readBooleanAttributeValue(\n                    checkedElement.attributes[\"wordml:val\"]\n                );\n                return elementResult(documents.checkbox({\n                    checked: isChecked\n                }));\n            } else {\n                return readXmlElements(element.firstOrEmpty(\"w:sdtContent\").children);\n            }\n        },\n\n        \"w:ins\": readChildElements,\n        \"w:object\": readChildElements,\n        \"w:smartTag\": readChildElements,\n        \"w:drawing\": readChildElements,\n        \"w:pict\": function(element) {\n            return readChildElements(element).toExtra();\n        },\n        \"v:roundrect\": readChildElements,\n        \"v:shape\": readChildElements,\n        \"v:textbox\": readChildElements,\n        \"w:txbxContent\": readChildElements,\n        \"wp:inline\": readDrawingElement,\n        \"wp:anchor\": readDrawingElement,\n        \"v:imagedata\": readImageData,\n        \"v:group\": readChildElements,\n        \"v:rect\": readChildElements\n    };\n\n    return {\n        readXmlElement: readXmlElement,\n        readXmlElements: readXmlElements\n    };\n\n\n    function readTable(element) {\n        var propertiesResult = readTableProperties(element.firstOrEmpty(\"w:tblPr\"));\n        return readXmlElements(element.children)\n            .flatMap(calculateRowSpans)\n            .flatMap(function(children) {\n                return propertiesResult.map(function(properties) {\n                    return documents.Table(children, properties);\n                });\n            });\n    }\n\n    function readTableProperties(element) {\n        return readTableStyle(element).map(function(style) {\n            return {\n                styleId: style.styleId,\n                styleName: style.name\n            };\n        });\n    }\n\n    function readTableRow(element) {\n        var properties = element.firstOrEmpty(\"w:trPr\");\n        var isHeader = !!properties.first(\"w:tblHeader\");\n        return readXmlElements(element.children).map(function(children) {\n            return documents.TableRow(children, {isHeader: isHeader});\n        });\n    }\n\n    function readTableCell(element) {\n        return readXmlElements(element.children).map(function(children) {\n            var properties = element.firstOrEmpty(\"w:tcPr\");\n\n            var gridSpan = properties.firstOrEmpty(\"w:gridSpan\").attributes[\"w:val\"];\n            var colSpan = gridSpan ? parseInt(gridSpan, 10) : 1;\n\n            var cell = documents.TableCell(children, {colSpan: colSpan});\n            cell._vMerge = readVMerge(properties);\n            return cell;\n        });\n    }\n\n    function readVMerge(properties) {\n        var element = properties.first(\"w:vMerge\");\n        if (element) {\n            var val = element.attributes[\"w:val\"];\n            return val === \"continue\" || !val;\n        } else {\n            return null;\n        }\n    }\n\n    function calculateRowSpans(rows) {\n        var unexpectedNonRows = _.any(rows, function(row) {\n            return row.type !== documents.types.tableRow;\n        });\n        if (unexpectedNonRows) {\n            return elementResultWithMessages(rows, [warning(\n                \"unexpected non-row element in table, cell merging may be incorrect\"\n            )]);\n        }\n        var unexpectedNonCells = _.any(rows, function(row) {\n            return _.any(row.children, function(cell) {\n                return cell.type !== documents.types.tableCell;\n            });\n        });\n        if (unexpectedNonCells) {\n            return elementResultWithMessages(rows, [warning(\n                \"unexpected non-cell element in table row, cell merging may be incorrect\"\n            )]);\n        }\n\n        var columns = {};\n\n        rows.forEach(function(row) {\n            var cellIndex = 0;\n            row.children.forEach(function(cell) {\n                if (cell._vMerge && columns[cellIndex]) {\n                    columns[cellIndex].rowSpan++;\n                } else {\n                    columns[cellIndex] = cell;\n                    cell._vMerge = false;\n                }\n                cellIndex += cell.colSpan;\n            });\n        });\n\n        rows.forEach(function(row) {\n            row.children = row.children.filter(function(cell) {\n                return !cell._vMerge;\n            });\n            row.children.forEach(function(cell) {\n                delete cell._vMerge;\n            });\n        });\n\n        return elementResult(rows);\n    }\n\n    function readDrawingElement(element) {\n        var blips = element\n            .getElementsByTagName(\"a:graphic\")\n            .getElementsByTagName(\"a:graphicData\")\n            .getElementsByTagName(\"pic:pic\")\n            .getElementsByTagName(\"pic:blipFill\")\n            .getElementsByTagName(\"a:blip\");\n\n        return combineResults(blips.map(readBlip.bind(null, element)));\n    }\n\n    function readBlip(element, blip) {\n        var properties = element.first(\"wp:docPr\").attributes;\n        var altText = isBlank(properties.descr) ? properties.title : properties.descr;\n        var blipImageFile = findBlipImageFile(blip);\n        if (blipImageFile === null) {\n            return emptyResultWithMessages([warning(\"Could not find image file for a:blip element\")]);\n        } else {\n            return readImage(blipImageFile, altText);\n        }\n    }\n\n    function isBlank(value) {\n        return value == null || /^\\s*$/.test(value);\n    }\n\n    function findBlipImageFile(blip) {\n        var embedRelationshipId = blip.attributes[\"r:embed\"];\n        var linkRelationshipId = blip.attributes[\"r:link\"];\n        if (embedRelationshipId) {\n            return findEmbeddedImageFile(embedRelationshipId);\n        } else if (linkRelationshipId) {\n            var imagePath = relationships.findTargetByRelationshipId(linkRelationshipId);\n            return {\n                path: imagePath,\n                read: files.read.bind(files, imagePath)\n            };\n        } else {\n            return null;\n        }\n    }\n\n    function readImageData(element) {\n        var relationshipId = element.attributes['r:id'];\n\n        if (relationshipId) {\n            return readImage(\n                findEmbeddedImageFile(relationshipId),\n                element.attributes[\"o:title\"]);\n        } else {\n            return emptyResultWithMessages([warning(\"A v:imagedata element without a relationship ID was ignored\")]);\n        }\n    }\n\n    function findEmbeddedImageFile(relationshipId) {\n        var path = uris.uriToZipEntryName(\"word\", relationships.findTargetByRelationshipId(relationshipId));\n        return {\n            path: path,\n            read: docxFile.read.bind(docxFile, path)\n        };\n    }\n\n    function readImage(imageFile, altText) {\n        var contentType = contentTypes.findContentType(imageFile.path);\n\n        var image = documents.Image({\n            readImage: imageFile.read,\n            altText: altText,\n            contentType: contentType\n        });\n        var warnings = supportedImageTypes[contentType] ?\n            [] : warning(\"Image of type \" + contentType + \" is unlikely to display in web browsers\");\n        return elementResultWithMessages(image, warnings);\n    }\n\n    function undefinedStyleWarning(type, styleId) {\n        return warning(\n            type + \" style with ID \" + styleId + \" was referenced but not defined in the document\");\n    }\n}\n\n\nfunction readNumberingProperties(styleId, element, numbering) {\n    var level = element.firstOrEmpty(\"w:ilvl\").attributes[\"w:val\"];\n    var numId = element.firstOrEmpty(\"w:numId\").attributes[\"w:val\"];\n    if (level !== undefined && numId !== undefined) {\n        return numbering.findLevel(numId, level);\n    }\n\n    if (styleId != null) {\n        var levelByStyleId = numbering.findLevelByParagraphStyleId(styleId);\n        if (levelByStyleId != null) {\n            return levelByStyleId;\n        }\n    }\n\n    return null;\n}\n\nvar supportedImageTypes = {\n    \"image/png\": true,\n    \"image/gif\": true,\n    \"image/jpeg\": true,\n    \"image/svg+xml\": true,\n    \"image/tiff\": true\n};\n\nvar ignoreElements = {\n    \"office-word:wrap\": true,\n    \"v:shadow\": true,\n    \"v:shapetype\": true,\n    \"w:annotationRef\": true,\n    \"w:bookmarkEnd\": true,\n    \"w:sectPr\": true,\n    \"w:proofErr\": true,\n    \"w:lastRenderedPageBreak\": true,\n    \"w:commentRangeStart\": true,\n    \"w:commentRangeEnd\": true,\n    \"w:del\": true,\n    \"w:footnoteRef\": true,\n    \"w:endnoteRef\": true,\n    \"w:pPr\": true,\n    \"w:rPr\": true,\n    \"w:tblPr\": true,\n    \"w:tblGrid\": true,\n    \"w:trPr\": true,\n    \"w:tcPr\": true\n};\n\nfunction emptyResultWithMessages(messages) {\n    return new ReadResult(null, null, messages);\n}\n\nfunction emptyResult() {\n    return new ReadResult(null);\n}\n\nfunction elementResult(element) {\n    return new ReadResult(element);\n}\n\nfunction elementResultWithMessages(element, messages) {\n    return new ReadResult(element, null, messages);\n}\n\nfunction ReadResult(element, extra, messages) {\n    this.value = element || [];\n    this.extra = extra || [];\n    this._result = new Result({\n        element: this.value,\n        extra: extra\n    }, messages);\n    this.messages = this._result.messages;\n}\n\nReadResult.prototype.toExtra = function() {\n    return new ReadResult(null, joinElements(this.extra, this.value), this.messages);\n};\n\nReadResult.prototype.insertExtra = function() {\n    var extra = this.extra;\n    if (extra && extra.length) {\n        return new ReadResult(joinElements(this.value, extra), null, this.messages);\n    } else {\n        return this;\n    }\n};\n\nReadResult.prototype.map = function(func) {\n    var result = this._result.map(function(value) {\n        return func(value.element);\n    });\n    return new ReadResult(result.value, this.extra, result.messages);\n};\n\nReadResult.prototype.flatMap = function(func) {\n    var result = this._result.flatMap(function(value) {\n        return func(value.element)._result;\n    });\n    return new ReadResult(result.value.element, joinElements(this.extra, result.value.extra), result.messages);\n};\n\nReadResult.map = function(first, second, func) {\n    return new ReadResult(\n        func(first.value, second.value),\n        joinElements(first.extra, second.extra),\n        first.messages.concat(second.messages)\n    );\n};\n\nfunction combineResults(results) {\n    var result = Result.combine(_.pluck(results, \"_result\"));\n    return new ReadResult(\n        _.flatten(_.pluck(result.value, \"element\")),\n        _.filter(_.flatten(_.pluck(result.value, \"extra\")), identity),\n        result.messages\n    );\n}\n\nfunction joinElements(first, second) {\n    return _.flatten([first, second]);\n}\n\nfunction identity(value) {\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/body-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/comments-reader.js":
/*!**********************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/comments-reader.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var documents = __webpack_require__(/*! ../documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar Result = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\n\nfunction createCommentsReader(bodyReader) {\n    function readCommentsXml(element) {\n        return Result.combine(element.getElementsByTagName(\"w:comment\")\n            .map(readCommentElement));\n    }\n\n    function readCommentElement(element) {\n        var id = element.attributes[\"w:id\"];\n\n        function readOptionalAttribute(name) {\n            return (element.attributes[name] || \"\").trim() || null;\n        }\n\n        return bodyReader.readXmlElements(element.children)\n            .map(function(body) {\n                return documents.comment({\n                    commentId: id,\n                    body: body,\n                    authorName: readOptionalAttribute(\"w:author\"),\n                    authorInitials: readOptionalAttribute(\"w:initials\")\n                });\n            });\n    }\n    \n    return readCommentsXml;\n}\n\nexports.createCommentsReader = createCommentsReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvZG9jeC9jb21tZW50cy1yZWFkZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsZ0JBQWdCLG1CQUFPLENBQUMsbUVBQWM7QUFDdEMsYUFBYSw2RkFBNEI7O0FBRXpDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDRCQUE0QiIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXGRvY3hcXGNvbW1lbnRzLXJlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZG9jdW1lbnRzID0gcmVxdWlyZShcIi4uL2RvY3VtZW50c1wiKTtcbnZhciBSZXN1bHQgPSByZXF1aXJlKFwiLi4vcmVzdWx0c1wiKS5SZXN1bHQ7XG5cbmZ1bmN0aW9uIGNyZWF0ZUNvbW1lbnRzUmVhZGVyKGJvZHlSZWFkZXIpIHtcbiAgICBmdW5jdGlvbiByZWFkQ29tbWVudHNYbWwoZWxlbWVudCkge1xuICAgICAgICByZXR1cm4gUmVzdWx0LmNvbWJpbmUoZWxlbWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZShcInc6Y29tbWVudFwiKVxuICAgICAgICAgICAgLm1hcChyZWFkQ29tbWVudEVsZW1lbnQpKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiByZWFkQ29tbWVudEVsZW1lbnQoZWxlbWVudCkge1xuICAgICAgICB2YXIgaWQgPSBlbGVtZW50LmF0dHJpYnV0ZXNbXCJ3OmlkXCJdO1xuXG4gICAgICAgIGZ1bmN0aW9uIHJlYWRPcHRpb25hbEF0dHJpYnV0ZShuYW1lKSB7XG4gICAgICAgICAgICByZXR1cm4gKGVsZW1lbnQuYXR0cmlidXRlc1tuYW1lXSB8fCBcIlwiKS50cmltKCkgfHwgbnVsbDtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBib2R5UmVhZGVyLnJlYWRYbWxFbGVtZW50cyhlbGVtZW50LmNoaWxkcmVuKVxuICAgICAgICAgICAgLm1hcChmdW5jdGlvbihib2R5KSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50cy5jb21tZW50KHtcbiAgICAgICAgICAgICAgICAgICAgY29tbWVudElkOiBpZCxcbiAgICAgICAgICAgICAgICAgICAgYm9keTogYm9keSxcbiAgICAgICAgICAgICAgICAgICAgYXV0aG9yTmFtZTogcmVhZE9wdGlvbmFsQXR0cmlidXRlKFwidzphdXRob3JcIiksXG4gICAgICAgICAgICAgICAgICAgIGF1dGhvckluaXRpYWxzOiByZWFkT3B0aW9uYWxBdHRyaWJ1dGUoXCJ3OmluaXRpYWxzXCIpXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHJlYWRDb21tZW50c1htbDtcbn1cblxuZXhwb3J0cy5jcmVhdGVDb21tZW50c1JlYWRlciA9IGNyZWF0ZUNvbW1lbnRzUmVhZGVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/comments-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/content-types-reader.js":
/*!***************************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/content-types-reader.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.readContentTypesFromXml = readContentTypesFromXml;\n\nvar fallbackContentTypes = {\n    \"png\": \"png\",\n    \"gif\": \"gif\",\n    \"jpeg\": \"jpeg\",\n    \"jpg\": \"jpeg\",\n    \"tif\": \"tiff\",\n    \"tiff\": \"tiff\",\n    \"bmp\": \"bmp\"\n};\n\nexports.defaultContentTypes = contentTypes({}, {});\n\n\nfunction readContentTypesFromXml(element) {\n    var extensionDefaults = {};\n    var overrides = {};\n    \n    element.children.forEach(function(child) {\n        if (child.name === \"content-types:Default\") {\n            extensionDefaults[child.attributes.Extension] = child.attributes.ContentType;\n        }\n        if (child.name === \"content-types:Override\") {\n            var name = child.attributes.PartName;\n            if (name.charAt(0) === \"/\") {\n                name = name.substring(1);\n            }\n            overrides[name] = child.attributes.ContentType;\n        }\n    });\n    return contentTypes(overrides, extensionDefaults);\n}\n\nfunction contentTypes(overrides, extensionDefaults) {\n    return {\n        findContentType: function(path) {\n            var overrideContentType = overrides[path];\n            if (overrideContentType) {\n                return overrideContentType;\n            } else {\n                var pathParts = path.split(\".\");\n                var extension = pathParts[pathParts.length - 1];\n                if (extensionDefaults.hasOwnProperty(extension)) {\n                    return extensionDefaults[extension];\n                } else {\n                    var fallback = fallbackContentTypes[extension.toLowerCase()];\n                    if (fallback) {\n                        return \"image/\" + fallback;\n                    } else {\n                        return null;\n                    }\n                }\n            }\n        }\n    };\n    \n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/content-types-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/document-xml-reader.js":
/*!**************************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/document-xml-reader.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.DocumentXmlReader = DocumentXmlReader;\n\nvar documents = __webpack_require__(/*! ../documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar Result = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\n\n\nfunction DocumentXmlReader(options) {\n    var bodyReader = options.bodyReader;\n\n    function convertXmlToDocument(element) {\n        var body = element.first(\"w:body\");\n\n        if (body == null) {\n            throw new Error(\"Could not find the body element: are you sure this is a docx file?\");\n        }\n\n        var result = bodyReader.readXmlElements(body.children)\n            .map(function(children) {\n                return new documents.Document(children, {\n                    notes: options.notes,\n                    comments: options.comments\n                });\n            });\n        return new Result(result.value, result.messages);\n    }\n\n    return {\n        convertXmlToDocument: convertXmlToDocument\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvZG9jeC9kb2N1bWVudC14bWwtcmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlCQUF5Qjs7QUFFekIsZ0JBQWdCLG1CQUFPLENBQUMsbUVBQWM7QUFDdEMsYUFBYSw2RkFBNEI7OztBQUd6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFxkb2N4XFxkb2N1bWVudC14bWwtcmVhZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydHMuRG9jdW1lbnRYbWxSZWFkZXIgPSBEb2N1bWVudFhtbFJlYWRlcjtcblxudmFyIGRvY3VtZW50cyA9IHJlcXVpcmUoXCIuLi9kb2N1bWVudHNcIik7XG52YXIgUmVzdWx0ID0gcmVxdWlyZShcIi4uL3Jlc3VsdHNcIikuUmVzdWx0O1xuXG5cbmZ1bmN0aW9uIERvY3VtZW50WG1sUmVhZGVyKG9wdGlvbnMpIHtcbiAgICB2YXIgYm9keVJlYWRlciA9IG9wdGlvbnMuYm9keVJlYWRlcjtcblxuICAgIGZ1bmN0aW9uIGNvbnZlcnRYbWxUb0RvY3VtZW50KGVsZW1lbnQpIHtcbiAgICAgICAgdmFyIGJvZHkgPSBlbGVtZW50LmZpcnN0KFwidzpib2R5XCIpO1xuXG4gICAgICAgIGlmIChib2R5ID09IG51bGwpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNvdWxkIG5vdCBmaW5kIHRoZSBib2R5IGVsZW1lbnQ6IGFyZSB5b3Ugc3VyZSB0aGlzIGlzIGEgZG9jeCBmaWxlP1wiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHZhciByZXN1bHQgPSBib2R5UmVhZGVyLnJlYWRYbWxFbGVtZW50cyhib2R5LmNoaWxkcmVuKVxuICAgICAgICAgICAgLm1hcChmdW5jdGlvbihjaGlsZHJlbikge1xuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgZG9jdW1lbnRzLkRvY3VtZW50KGNoaWxkcmVuLCB7XG4gICAgICAgICAgICAgICAgICAgIG5vdGVzOiBvcHRpb25zLm5vdGVzLFxuICAgICAgICAgICAgICAgICAgICBjb21tZW50czogb3B0aW9ucy5jb21tZW50c1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBuZXcgUmVzdWx0KHJlc3VsdC52YWx1ZSwgcmVzdWx0Lm1lc3NhZ2VzKTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgICBjb252ZXJ0WG1sVG9Eb2N1bWVudDogY29udmVydFhtbFRvRG9jdW1lbnRcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/document-xml-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/docx-reader.js":
/*!******************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/docx-reader.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.read = read;\nexports._findPartPaths = findPartPaths;\n\nvar promises = __webpack_require__(/*! ../promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar documents = __webpack_require__(/*! ../documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar Result = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\nvar zipfile = __webpack_require__(/*! ../zipfile */ \"(ssr)/./node_modules/mammoth/lib/zipfile.js\");\n\nvar readXmlFromZipFile = (__webpack_require__(/*! ./office-xml-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/office-xml-reader.js\").readXmlFromZipFile);\nvar createBodyReader = (__webpack_require__(/*! ./body-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/body-reader.js\").createBodyReader);\nvar DocumentXmlReader = (__webpack_require__(/*! ./document-xml-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/document-xml-reader.js\").DocumentXmlReader);\nvar relationshipsReader = __webpack_require__(/*! ./relationships-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/relationships-reader.js\");\nvar contentTypesReader = __webpack_require__(/*! ./content-types-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/content-types-reader.js\");\nvar numberingXml = __webpack_require__(/*! ./numbering-xml */ \"(ssr)/./node_modules/mammoth/lib/docx/numbering-xml.js\");\nvar stylesReader = __webpack_require__(/*! ./styles-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/styles-reader.js\");\nvar notesReader = __webpack_require__(/*! ./notes-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/notes-reader.js\");\nvar commentsReader = __webpack_require__(/*! ./comments-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/comments-reader.js\");\nvar Files = (__webpack_require__(/*! ./files */ \"(ssr)/./node_modules/mammoth/lib/docx/files.js\").Files);\n\n\nfunction read(docxFile, input) {\n    input = input || {};\n\n    return promises.props({\n        contentTypes: readContentTypesFromZipFile(docxFile),\n        partPaths: findPartPaths(docxFile),\n        docxFile: docxFile,\n        files: input.path ? Files.relativeToFile(input.path) : new Files(null)\n    }).also(function(result) {\n        return {\n            styles: readStylesFromZipFile(docxFile, result.partPaths.styles)\n        };\n    }).also(function(result) {\n        return {\n            numbering: readNumberingFromZipFile(docxFile, result.partPaths.numbering, result.styles)\n        };\n    }).also(function(result) {\n        return {\n            footnotes: readXmlFileWithBody(result.partPaths.footnotes, result, function(bodyReader, xml) {\n                if (xml) {\n                    return notesReader.createFootnotesReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            }),\n            endnotes: readXmlFileWithBody(result.partPaths.endnotes, result, function(bodyReader, xml) {\n                if (xml) {\n                    return notesReader.createEndnotesReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            }),\n            comments: readXmlFileWithBody(result.partPaths.comments, result, function(bodyReader, xml) {\n                if (xml) {\n                    return commentsReader.createCommentsReader(bodyReader)(xml);\n                } else {\n                    return new Result([]);\n                }\n            })\n        };\n    }).also(function(result) {\n        return {\n            notes: result.footnotes.flatMap(function(footnotes) {\n                return result.endnotes.map(function(endnotes) {\n                    return new documents.Notes(footnotes.concat(endnotes));\n                });\n            })\n        };\n    }).then(function(result) {\n        return readXmlFileWithBody(result.partPaths.mainDocument, result, function(bodyReader, xml) {\n            return result.notes.flatMap(function(notes) {\n                return result.comments.flatMap(function(comments) {\n                    var reader = new DocumentXmlReader({\n                        bodyReader: bodyReader,\n                        notes: notes,\n                        comments: comments\n                    });\n                    return reader.convertXmlToDocument(xml);\n                });\n            });\n        });\n    });\n}\n\nfunction findPartPaths(docxFile) {\n    return readPackageRelationships(docxFile).then(function(packageRelationships) {\n        var mainDocumentPath = findPartPath({\n            docxFile: docxFile,\n            relationships: packageRelationships,\n            relationshipType: \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument\",\n            basePath: \"\",\n            fallbackPath: \"word/document.xml\"\n        });\n\n        if (!docxFile.exists(mainDocumentPath)) {\n            throw new Error(\"Could not find main document part. Are you sure this is a valid .docx file?\");\n        }\n\n        return xmlFileReader({\n            filename: relationshipsFilename(mainDocumentPath),\n            readElement: relationshipsReader.readRelationships,\n            defaultValue: relationshipsReader.defaultValue\n        })(docxFile).then(function(documentRelationships) {\n            function findPartRelatedToMainDocument(name) {\n                return findPartPath({\n                    docxFile: docxFile,\n                    relationships: documentRelationships,\n                    relationshipType: \"http://schemas.openxmlformats.org/officeDocument/2006/relationships/\" + name,\n                    basePath: zipfile.splitPath(mainDocumentPath).dirname,\n                    fallbackPath: \"word/\" + name + \".xml\"\n                });\n            }\n\n            return {\n                mainDocument: mainDocumentPath,\n                comments: findPartRelatedToMainDocument(\"comments\"),\n                endnotes: findPartRelatedToMainDocument(\"endnotes\"),\n                footnotes: findPartRelatedToMainDocument(\"footnotes\"),\n                numbering: findPartRelatedToMainDocument(\"numbering\"),\n                styles: findPartRelatedToMainDocument(\"styles\")\n            };\n        });\n    });\n}\n\nfunction findPartPath(options) {\n    var docxFile = options.docxFile;\n    var relationships = options.relationships;\n    var relationshipType = options.relationshipType;\n    var basePath = options.basePath;\n    var fallbackPath = options.fallbackPath;\n\n    var targets = relationships.findTargetsByType(relationshipType);\n    var normalisedTargets = targets.map(function(target) {\n        return stripPrefix(zipfile.joinPath(basePath, target), \"/\");\n    });\n    var validTargets = normalisedTargets.filter(function(target) {\n        return docxFile.exists(target);\n    });\n    if (validTargets.length === 0) {\n        return fallbackPath;\n    } else {\n        return validTargets[0];\n    }\n}\n\nfunction stripPrefix(value, prefix) {\n    if (value.substring(0, prefix.length) === prefix) {\n        return value.substring(prefix.length);\n    } else {\n        return value;\n    }\n}\n\nfunction xmlFileReader(options) {\n    return function(zipFile) {\n        return readXmlFromZipFile(zipFile, options.filename)\n            .then(function(element) {\n                return element ? options.readElement(element) : options.defaultValue;\n            });\n    };\n}\n\nfunction readXmlFileWithBody(filename, options, func) {\n    var readRelationshipsFromZipFile = xmlFileReader({\n        filename: relationshipsFilename(filename),\n        readElement: relationshipsReader.readRelationships,\n        defaultValue: relationshipsReader.defaultValue\n    });\n\n    return readRelationshipsFromZipFile(options.docxFile).then(function(relationships) {\n        var bodyReader = new createBodyReader({\n            relationships: relationships,\n            contentTypes: options.contentTypes,\n            docxFile: options.docxFile,\n            numbering: options.numbering,\n            styles: options.styles,\n            files: options.files\n        });\n        return readXmlFromZipFile(options.docxFile, filename)\n            .then(function(xml) {\n                return func(bodyReader, xml);\n            });\n    });\n}\n\nfunction relationshipsFilename(filename) {\n    var split = zipfile.splitPath(filename);\n    return zipfile.joinPath(split.dirname, \"_rels\", split.basename + \".rels\");\n}\n\nvar readContentTypesFromZipFile = xmlFileReader({\n    filename: \"[Content_Types].xml\",\n    readElement: contentTypesReader.readContentTypesFromXml,\n    defaultValue: contentTypesReader.defaultContentTypes\n});\n\nfunction readNumberingFromZipFile(zipFile, path, styles) {\n    return xmlFileReader({\n        filename: path,\n        readElement: function(element) {\n            return numberingXml.readNumberingXml(element, {styles: styles});\n        },\n        defaultValue: numberingXml.defaultNumbering\n    })(zipFile);\n}\n\nfunction readStylesFromZipFile(zipFile, path) {\n    return xmlFileReader({\n        filename: path,\n        readElement: stylesReader.readStylesXml,\n        defaultValue: stylesReader.defaultStyles\n    })(zipFile);\n}\n\nvar readPackageRelationships = xmlFileReader({\n    filename: \"_rels/.rels\",\n    readElement: relationshipsReader.readRelationships,\n    defaultValue: relationshipsReader.defaultValue\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/docx-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/files.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/files.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! fs */ \"fs\");\nvar url = __webpack_require__(/*! url */ \"url\");\nvar os = __webpack_require__(/*! os */ \"os\");\nvar dirname = (__webpack_require__(/*! path */ \"path\").dirname);\nvar resolvePath = (__webpack_require__(/*! path */ \"path\").resolve);\nvar isAbsolutePath = __webpack_require__(/*! path-is-absolute */ \"(ssr)/./node_modules/path-is-absolute/index.js\");\n\nvar promises = __webpack_require__(/*! ../promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\n\n\nexports.Files = Files;\nexports.uriToPath = uriToPath;\n\n\nfunction Files(base) {\n    function read(uri, encoding) {\n        return resolveUri(uri).then(function(path) {\n            return readFile(path, encoding).caught(function(error) {\n                var message = \"could not open external image: '\" + uri + \"' (document directory: '\" + base + \"')\\n\" + error.message;\n                return promises.reject(new Error(message));\n            });\n        });\n    }\n\n    function resolveUri(uri) {\n        var path = uriToPath(uri);\n        if (isAbsolutePath(path)) {\n            return promises.resolve(path);\n        } else if (base) {\n            return promises.resolve(resolvePath(base, path));\n        } else {\n            return promises.reject(new Error(\"could not find external image '\" + uri + \"', path of input document is unknown\"));\n        }\n    }\n\n    return {\n        read: read\n    };\n}\n\n\nfunction relativeToFile(filePath) {\n    return new Files(dirname(filePath));\n}\n\nFiles.relativeToFile = relativeToFile;\n\n\nvar readFile = promises.promisify(fs.readFile.bind(fs));\n\n\nfunction uriToPath(uriString, platform) {\n    if (!platform) {\n        platform = os.platform();\n    }\n\n    var uri = url.parse(uriString);\n    if (isLocalFileUri(uri) || isRelativeUri(uri)) {\n        var path = decodeURIComponent(uri.path);\n        if (platform === \"win32\" && /^\\/[a-z]:/i.test(path)) {\n            return path.slice(1);\n        } else {\n            return path;\n        }\n    } else {\n        throw new Error(\"Could not convert URI to path: \" + uriString);\n    }\n}\n\nfunction isLocalFileUri(uri) {\n    return uri.protocol === \"file:\" && (!uri.host || uri.host === \"localhost\");\n}\n\nfunction isRelativeUri(uri) {\n    return !uri.protocol && !uri.host;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/files.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/notes-reader.js":
/*!*******************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/notes-reader.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("var documents = __webpack_require__(/*! ../documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\nvar Result = (__webpack_require__(/*! ../results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\n\nexports.createFootnotesReader = createReader.bind(this, \"footnote\");\nexports.createEndnotesReader = createReader.bind(this, \"endnote\");\n\nfunction createReader(noteType, bodyReader) {\n    function readNotesXml(element) {\n        return Result.combine(element.getElementsByTagName(\"w:\" + noteType)\n            .filter(isFootnoteElement)\n            .map(readFootnoteElement));\n    }\n\n    function isFootnoteElement(element) {\n        var type = element.attributes[\"w:type\"];\n        return type !== \"continuationSeparator\" && type !== \"separator\";\n    }\n\n    function readFootnoteElement(footnoteElement) {\n        var id = footnoteElement.attributes[\"w:id\"];\n        return bodyReader.readXmlElements(footnoteElement.children)\n            .map(function(body) {\n                return documents.Note({noteType: noteType, noteId: id, body: body});\n            });\n    }\n    \n    return readNotesXml;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/notes-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/numbering-xml.js":
/*!********************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/numbering-xml.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nexports.readNumberingXml = readNumberingXml;\nexports.Numbering = Numbering;\nexports.defaultNumbering = new Numbering({}, {});\n\nfunction Numbering(nums, abstractNums, styles) {\n    var allLevels = _.flatten(_.values(abstractNums).map(function(abstractNum) {\n        return _.values(abstractNum.levels);\n    }));\n\n    var levelsByParagraphStyleId = _.indexBy(\n        allLevels.filter(function(level) {\n            return level.paragraphStyleId != null;\n        }),\n        \"paragraphStyleId\"\n    );\n\n    function findLevel(numId, level) {\n        var num = nums[numId];\n        if (num) {\n            var abstractNum = abstractNums[num.abstractNumId];\n            if (!abstractNum) {\n                return null;\n            } else if (abstractNum.numStyleLink == null) {\n                return abstractNums[num.abstractNumId].levels[level];\n            } else {\n                var style = styles.findNumberingStyleById(abstractNum.numStyleLink);\n                return findLevel(style.numId, level);\n            }\n        } else {\n            return null;\n        }\n    }\n\n    function findLevelByParagraphStyleId(styleId) {\n        return levelsByParagraphStyleId[styleId] || null;\n    }\n\n    return {\n        findLevel: findLevel,\n        findLevelByParagraphStyleId: findLevelByParagraphStyleId\n    };\n}\n\nfunction readNumberingXml(root, options) {\n    if (!options || !options.styles) {\n        throw new Error(\"styles is missing\");\n    }\n\n    var abstractNums = readAbstractNums(root);\n    var nums = readNums(root, abstractNums);\n    return new Numbering(nums, abstractNums, options.styles);\n}\n\nfunction readAbstractNums(root) {\n    var abstractNums = {};\n    root.getElementsByTagName(\"w:abstractNum\").forEach(function(element) {\n        var id = element.attributes[\"w:abstractNumId\"];\n        abstractNums[id] = readAbstractNum(element);\n    });\n    return abstractNums;\n}\n\nfunction readAbstractNum(element) {\n    var levels = {};\n    element.getElementsByTagName(\"w:lvl\").forEach(function(levelElement) {\n        var levelIndex = levelElement.attributes[\"w:ilvl\"];\n        var numFmt = levelElement.firstOrEmpty(\"w:numFmt\").attributes[\"w:val\"];\n        var paragraphStyleId = levelElement.firstOrEmpty(\"w:pStyle\").attributes[\"w:val\"];\n\n        levels[levelIndex] = {\n            isOrdered: numFmt !== \"bullet\",\n            level: levelIndex,\n            paragraphStyleId: paragraphStyleId\n        };\n    });\n\n    var numStyleLink = element.firstOrEmpty(\"w:numStyleLink\").attributes[\"w:val\"];\n\n    return {levels: levels, numStyleLink: numStyleLink};\n}\n\nfunction readNums(root) {\n    var nums = {};\n    root.getElementsByTagName(\"w:num\").forEach(function(element) {\n        var numId = element.attributes[\"w:numId\"];\n        var abstractNumId = element.first(\"w:abstractNumId\").attributes[\"w:val\"];\n        nums[numId] = {abstractNumId: abstractNumId};\n    });\n    return nums;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/numbering-xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/office-xml-reader.js":
/*!************************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/office-xml-reader.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar promises = __webpack_require__(/*! ../promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar xml = __webpack_require__(/*! ../xml */ \"(ssr)/./node_modules/mammoth/lib/xml/index.js\");\n\n\nexports.read = read;\nexports.readXmlFromZipFile = readXmlFromZipFile;\n\nvar xmlNamespaceMap = {\n    // Transitional format\n    \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\": \"w\",\n    \"http://schemas.openxmlformats.org/officeDocument/2006/relationships\": \"r\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\": \"wp\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/main\": \"a\",\n    \"http://schemas.openxmlformats.org/drawingml/2006/picture\": \"pic\",\n\n    // Strict format\n    \"http://purl.oclc.org/ooxml/wordprocessingml/main\": \"w\",\n    \"http://purl.oclc.org/ooxml/officeDocument/relationships\": \"r\",\n    \"http://purl.oclc.org/ooxml/drawingml/wordprocessingDrawing\": \"wp\",\n    \"http://purl.oclc.org/ooxml/drawingml/main\": \"a\",\n    \"http://purl.oclc.org/ooxml/drawingml/picture\": \"pic\",\n\n    // Common\n    \"http://schemas.openxmlformats.org/package/2006/content-types\": \"content-types\",\n    \"http://schemas.openxmlformats.org/package/2006/relationships\": \"relationships\",\n    \"http://schemas.openxmlformats.org/markup-compatibility/2006\": \"mc\",\n    \"urn:schemas-microsoft-com:vml\": \"v\",\n    \"urn:schemas-microsoft-com:office:word\": \"office-word\",\n\n    // [MS-DOCX]: Word Extensions to the Office Open XML (.docx) File Format\n    // https://learn.microsoft.com/en-us/openspecs/office_standards/ms-docx/b839fe1f-e1ca-4fa6-8c26-5954d0abbccd\n    \"http://schemas.microsoft.com/office/word/2010/wordml\": \"wordml\"\n};\n\n\nfunction read(xmlString) {\n    return xml.readString(xmlString, xmlNamespaceMap)\n        .then(function(document) {\n            return collapseAlternateContent(document)[0];\n        });\n}\n\n\nfunction readXmlFromZipFile(docxFile, path) {\n    if (docxFile.exists(path)) {\n        return docxFile.read(path, \"utf-8\")\n            .then(stripUtf8Bom)\n            .then(read);\n    } else {\n        return promises.resolve(null);\n    }\n}\n\n\nfunction stripUtf8Bom(xmlString) {\n    return xmlString.replace(/^\\uFEFF/g, '');\n}\n\n\nfunction collapseAlternateContent(node) {\n    if (node.type === \"element\") {\n        if (node.name === \"mc:AlternateContent\") {\n            return node.firstOrEmpty(\"mc:Fallback\").children;\n        } else {\n            node.children = _.flatten(node.children.map(collapseAlternateContent, true));\n            return [node];\n        }\n    } else {\n        return [node];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/office-xml-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/relationships-reader.js":
/*!***************************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/relationships-reader.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.readRelationships = readRelationships;\nexports.defaultValue = new Relationships([]);\nexports.Relationships = Relationships;\n\n\nfunction readRelationships(element) {\n    var relationships = [];\n    element.children.forEach(function(child) {\n        if (child.name === \"relationships:Relationship\") {\n            var relationship = {\n                relationshipId: child.attributes.Id,\n                target: child.attributes.Target,\n                type: child.attributes.Type\n            };\n            relationships.push(relationship);\n        }\n    });\n    return new Relationships(relationships);\n}\n\nfunction Relationships(relationships) {\n    var targetsByRelationshipId = {};\n    relationships.forEach(function(relationship) {\n        targetsByRelationshipId[relationship.relationshipId] = relationship.target;\n    });\n\n    var targetsByType = {};\n    relationships.forEach(function(relationship) {\n        if (!targetsByType[relationship.type]) {\n            targetsByType[relationship.type] = [];\n        }\n        targetsByType[relationship.type].push(relationship.target);\n    });\n\n    return {\n        findTargetByRelationshipId: function(relationshipId) {\n            return targetsByRelationshipId[relationshipId];\n        },\n        findTargetsByType: function(type) {\n            return targetsByType[type] || [];\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/relationships-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/style-map.js":
/*!****************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/style-map.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar promises = __webpack_require__(/*! ../promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar xml = __webpack_require__(/*! ../xml */ \"(ssr)/./node_modules/mammoth/lib/xml/index.js\");\n\nexports.writeStyleMap = writeStyleMap;\nexports.readStyleMap = readStyleMap;\n\n\nvar schema = \"http://schemas.zwobble.org/mammoth/style-map\";\nvar styleMapPath = \"mammoth/style-map\";\nvar styleMapAbsolutePath = \"/\" + styleMapPath;\n\nfunction writeStyleMap(docxFile, styleMap) {\n    docxFile.write(styleMapPath, styleMap);\n    return updateRelationships(docxFile).then(function() {\n        return updateContentTypes(docxFile);\n    });\n}\n\nfunction updateRelationships(docxFile) {\n    var path = \"word/_rels/document.xml.rels\";\n    var relationshipsUri = \"http://schemas.openxmlformats.org/package/2006/relationships\";\n    var relationshipElementName = \"{\" + relationshipsUri + \"}Relationship\";\n    return docxFile.read(path, \"utf8\")\n        .then(xml.readString)\n        .then(function(relationshipsContainer) {\n            var relationships = relationshipsContainer.children;\n            addOrUpdateElement(relationships, relationshipElementName, \"Id\", {\n                \"Id\": \"rMammothStyleMap\",\n                \"Type\": schema,\n                \"Target\": styleMapAbsolutePath\n            });\n            \n            var namespaces = {\"\": relationshipsUri};\n            return docxFile.write(path, xml.writeString(relationshipsContainer, namespaces));\n        });\n}\n\nfunction updateContentTypes(docxFile) {\n    var path = \"[Content_Types].xml\";\n    var contentTypesUri = \"http://schemas.openxmlformats.org/package/2006/content-types\";\n    var overrideName = \"{\" + contentTypesUri + \"}Override\";\n    return docxFile.read(path, \"utf8\")\n        .then(xml.readString)\n        .then(function(typesElement) {\n            var children = typesElement.children;\n            addOrUpdateElement(children, overrideName, \"PartName\", {\n                \"PartName\": styleMapAbsolutePath,\n                \"ContentType\": \"text/prs.mammoth.style-map\"\n            });\n            var namespaces = {\"\": contentTypesUri};\n            return docxFile.write(path, xml.writeString(typesElement, namespaces));\n        });\n}\n\nfunction addOrUpdateElement(elements, name, identifyingAttribute, attributes) {\n    var existingElement = _.find(elements, function(element) {\n        return element.name === name &&\n            element.attributes[identifyingAttribute] === attributes[identifyingAttribute];\n    });\n    if (existingElement) {\n        existingElement.attributes = attributes;\n    } else {\n        elements.push(xml.element(name, attributes));\n    }\n}\n\nfunction readStyleMap(docxFile) {\n    if (docxFile.exists(styleMapPath)) {\n        return docxFile.read(styleMapPath, \"utf8\");\n    } else {\n        return promises.resolve(null);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/style-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/styles-reader.js":
/*!********************************************************!*\
  !*** ./node_modules/mammoth/lib/docx/styles-reader.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.readStylesXml = readStylesXml;\nexports.Styles = Styles;\nexports.defaultStyles = new Styles({}, {});\n\nfunction Styles(paragraphStyles, characterStyles, tableStyles, numberingStyles) {\n    return {\n        findParagraphStyleById: function(styleId) {\n            return paragraphStyles[styleId];\n        },\n        findCharacterStyleById: function(styleId) {\n            return characterStyles[styleId];\n        },\n        findTableStyleById: function(styleId) {\n            return tableStyles[styleId];\n        },\n        findNumberingStyleById: function(styleId) {\n            return numberingStyles[styleId];\n        }\n    };\n}\n\nStyles.EMPTY = new Styles({}, {}, {}, {});\n\nfunction readStylesXml(root) {\n    var paragraphStyles = {};\n    var characterStyles = {};\n    var tableStyles = {};\n    var numberingStyles = {};\n\n    var styles = {\n        \"paragraph\": paragraphStyles,\n        \"character\": characterStyles,\n        \"table\": tableStyles\n    };\n\n    root.getElementsByTagName(\"w:style\").forEach(function(styleElement) {\n        var style = readStyleElement(styleElement);\n        if (style.type === \"numbering\") {\n            numberingStyles[style.styleId] = readNumberingStyleElement(styleElement);\n        } else {\n            var styleSet = styles[style.type];\n            if (styleSet) {\n                styleSet[style.styleId] = style;\n            }\n        }\n    });\n\n    return new Styles(paragraphStyles, characterStyles, tableStyles, numberingStyles);\n}\n\nfunction readStyleElement(styleElement) {\n    var type = styleElement.attributes[\"w:type\"];\n    var styleId = styleElement.attributes[\"w:styleId\"];\n    var name = styleName(styleElement);\n    return {type: type, styleId: styleId, name: name};\n}\n\nfunction styleName(styleElement) {\n    var nameElement = styleElement.first(\"w:name\");\n    return nameElement ? nameElement.attributes[\"w:val\"] : null;\n}\n\nfunction readNumberingStyleElement(styleElement) {\n    var numId = styleElement\n        .firstOrEmpty(\"w:pPr\")\n        .firstOrEmpty(\"w:numPr\")\n        .firstOrEmpty(\"w:numId\")\n        .attributes[\"w:val\"];\n    return {numId: numId};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/styles-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/docx/uris.js":
/*!***********************************************!*\
  !*** ./node_modules/mammoth/lib/docx/uris.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.uriToZipEntryName = uriToZipEntryName;\nexports.replaceFragment = replaceFragment;\n\nfunction uriToZipEntryName(base, uri) {\n    if (uri.charAt(0) === \"/\") {\n        return uri.substr(1);\n    } else {\n        // In general, we should check first and second for trailing and leading slashes,\n        // but in our specific case this seems to be sufficient\n        return base + \"/\" + uri;\n    }\n}\n\n\nfunction replaceFragment(uri, fragment) {\n    var hashIndex = uri.indexOf(\"#\");\n    if (hashIndex !== -1) {\n        uri = uri.substring(0, hashIndex);\n    }\n    return uri + \"#\" + fragment;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvZG9jeC91cmlzLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlCQUF5QjtBQUN6Qix1QkFBdUI7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXGRvY3hcXHVyaXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy51cmlUb1ppcEVudHJ5TmFtZSA9IHVyaVRvWmlwRW50cnlOYW1lO1xuZXhwb3J0cy5yZXBsYWNlRnJhZ21lbnQgPSByZXBsYWNlRnJhZ21lbnQ7XG5cbmZ1bmN0aW9uIHVyaVRvWmlwRW50cnlOYW1lKGJhc2UsIHVyaSkge1xuICAgIGlmICh1cmkuY2hhckF0KDApID09PSBcIi9cIikge1xuICAgICAgICByZXR1cm4gdXJpLnN1YnN0cigxKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICAvLyBJbiBnZW5lcmFsLCB3ZSBzaG91bGQgY2hlY2sgZmlyc3QgYW5kIHNlY29uZCBmb3IgdHJhaWxpbmcgYW5kIGxlYWRpbmcgc2xhc2hlcyxcbiAgICAgICAgLy8gYnV0IGluIG91ciBzcGVjaWZpYyBjYXNlIHRoaXMgc2VlbXMgdG8gYmUgc3VmZmljaWVudFxuICAgICAgICByZXR1cm4gYmFzZSArIFwiL1wiICsgdXJpO1xuICAgIH1cbn1cblxuXG5mdW5jdGlvbiByZXBsYWNlRnJhZ21lbnQodXJpLCBmcmFnbWVudCkge1xuICAgIHZhciBoYXNoSW5kZXggPSB1cmkuaW5kZXhPZihcIiNcIik7XG4gICAgaWYgKGhhc2hJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgdXJpID0gdXJpLnN1YnN0cmluZygwLCBoYXNoSW5kZXgpO1xuICAgIH1cbiAgICByZXR1cm4gdXJpICsgXCIjXCIgKyBmcmFnbWVudDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/docx/uris.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/html/ast.js":
/*!**********************************************!*\
  !*** ./node_modules/mammoth/lib/html/ast.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var htmlPaths = __webpack_require__(/*! ../styles/html-paths */ \"(ssr)/./node_modules/mammoth/lib/styles/html-paths.js\");\n\n\nfunction nonFreshElement(tagName, attributes, children) {\n    return elementWithTag(\n        htmlPaths.element(tagName, attributes, {fresh: false}),\n        children);\n}\n\nfunction freshElement(tagName, attributes, children) {\n    var tag = htmlPaths.element(tagName, attributes, {fresh: true});\n    return elementWithTag(tag, children);\n}\n\nfunction elementWithTag(tag, children) {\n    return {\n        type: \"element\",\n        tag: tag,\n        children: children || []\n    };\n}\n\nfunction text(value) {\n    return {\n        type: \"text\",\n        value: value\n    };\n}\n\nvar forceWrite = {\n    type: \"forceWrite\"\n};\n\nexports.freshElement = freshElement;\nexports.nonFreshElement = nonFreshElement;\nexports.elementWithTag = elementWithTag;\nexports.text = text;\nexports.forceWrite = forceWrite;\n\nvar voidTagNames = {\n    \"br\": true,\n    \"hr\": true,\n    \"img\": true,\n    \"input\": true\n};\n\nfunction isVoidElement(node) {\n    return (node.children.length === 0) && voidTagNames[node.tag.tagName];\n}\n\nexports.isVoidElement = isVoidElement;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/html/ast.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/html/index.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/html/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var ast = __webpack_require__(/*! ./ast */ \"(ssr)/./node_modules/mammoth/lib/html/ast.js\");\n\nexports.freshElement = ast.freshElement;\nexports.nonFreshElement = ast.nonFreshElement;\nexports.elementWithTag = ast.elementWithTag;\nexports.text = ast.text;\nexports.forceWrite = ast.forceWrite;\n\nexports.simplify = __webpack_require__(/*! ./simplify */ \"(ssr)/./node_modules/mammoth/lib/html/simplify.js\");\n\nfunction write(writer, nodes) {\n    nodes.forEach(function(node) {\n        writeNode(writer, node);\n    });\n}\n\nfunction writeNode(writer, node) {\n    toStrings[node.type](writer, node);\n}\n\nvar toStrings = {\n    element: generateElementString,\n    text: generateTextString,\n    forceWrite: function() { }\n};\n\nfunction generateElementString(writer, node) {\n    if (ast.isVoidElement(node)) {\n        writer.selfClosing(node.tag.tagName, node.tag.attributes);\n    } else {\n        writer.open(node.tag.tagName, node.tag.attributes);\n        write(writer, node.children);\n        writer.close(node.tag.tagName);\n    }\n}\n\nfunction generateTextString(writer, node) {\n    writer.text(node.value);\n}\n\nexports.write = write;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/html/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/html/simplify.js":
/*!***************************************************!*\
  !*** ./node_modules/mammoth/lib/html/simplify.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar ast = __webpack_require__(/*! ./ast */ \"(ssr)/./node_modules/mammoth/lib/html/ast.js\");\n\nfunction simplify(nodes) {\n    return collapse(removeEmpty(nodes));\n}\n\nfunction collapse(nodes) {\n    var children = [];\n    \n    nodes.map(collapseNode).forEach(function(child) {\n        appendChild(children, child);\n    });\n    return children;\n}\n\nfunction collapseNode(node) {\n    return collapsers[node.type](node);\n}\n\nvar collapsers = {\n    element: collapseElement,\n    text: identity,\n    forceWrite: identity\n};\n\nfunction collapseElement(node) {\n    return ast.elementWithTag(node.tag, collapse(node.children));\n}\n\nfunction identity(value) {\n    return value;\n}\n\nfunction appendChild(children, child) {\n    var lastChild = children[children.length - 1];\n    if (child.type === \"element\" && !child.tag.fresh && lastChild && lastChild.type === \"element\" && child.tag.matchesElement(lastChild.tag)) {\n        if (child.tag.separator) {\n            appendChild(lastChild.children, ast.text(child.tag.separator));\n        }\n        child.children.forEach(function(grandChild) {\n            // Mutation is fine since simplifying elements create a copy of the children.\n            appendChild(lastChild.children, grandChild);\n        });\n    } else {\n        children.push(child);\n    }\n}\n\nfunction removeEmpty(nodes) {\n    return flatMap(nodes, function(node) {\n        return emptiers[node.type](node);\n    });\n}\n\nfunction flatMap(values, func) {\n    return _.flatten(_.map(values, func), true);\n}\n\nvar emptiers = {\n    element: elementEmptier,\n    text: textEmptier,\n    forceWrite: neverEmpty\n};\n\nfunction neverEmpty(node) {\n    return [node];\n}\n\nfunction elementEmptier(element) {\n    var children = removeEmpty(element.children);\n    if (children.length === 0 && !ast.isVoidElement(element)) {\n        return [];\n    } else {\n        return [ast.elementWithTag(element.tag, children)];\n    }\n}\n\nfunction textEmptier(node) {\n    if (node.value.length === 0) {\n        return [];\n    } else {\n        return [node];\n    }\n}\n\nmodule.exports = simplify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/html/simplify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/images.js":
/*!********************************************!*\
  !*** ./node_modules/mammoth/lib/images.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar promises = __webpack_require__(/*! ./promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar Html = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/mammoth/lib/html/index.js\");\n\nexports.imgElement = imgElement;\n\nfunction imgElement(func) {\n    return function(element, messages) {\n        return promises.when(func(element)).then(function(result) {\n            var attributes = {};\n            if (element.altText) {\n                attributes.alt = element.altText;\n            }\n            _.extend(attributes, result);\n\n            return [Html.freshElement(\"img\", attributes)];\n        });\n    };\n}\n\n// Undocumented, but retained for backwards-compatibility with 0.3.x\nexports.inline = exports.imgElement;\n\nexports.dataUri = imgElement(function(element) {\n    return element.readAsBase64String().then(function(imageBuffer) {\n        return {\n            src: \"data:\" + element.contentType + \";base64,\" + imageBuffer\n        };\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvaW1hZ2VzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsbUJBQU8sQ0FBQyx3RUFBWTs7QUFFNUIsZUFBZSxtQkFBTyxDQUFDLGdFQUFZO0FBQ25DLFdBQVcsbUJBQU8sQ0FBQyw4REFBUTs7QUFFM0Isa0JBQWtCOztBQUVsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQSxjQUFjOztBQUVkLGVBQWU7QUFDZjtBQUNBO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0EsS0FBSztBQUNMLENBQUMiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFxpbWFnZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF8gPSByZXF1aXJlKFwidW5kZXJzY29yZVwiKTtcblxudmFyIHByb21pc2VzID0gcmVxdWlyZShcIi4vcHJvbWlzZXNcIik7XG52YXIgSHRtbCA9IHJlcXVpcmUoXCIuL2h0bWxcIik7XG5cbmV4cG9ydHMuaW1nRWxlbWVudCA9IGltZ0VsZW1lbnQ7XG5cbmZ1bmN0aW9uIGltZ0VsZW1lbnQoZnVuYykge1xuICAgIHJldHVybiBmdW5jdGlvbihlbGVtZW50LCBtZXNzYWdlcykge1xuICAgICAgICByZXR1cm4gcHJvbWlzZXMud2hlbihmdW5jKGVsZW1lbnQpKS50aGVuKGZ1bmN0aW9uKHJlc3VsdCkge1xuICAgICAgICAgICAgdmFyIGF0dHJpYnV0ZXMgPSB7fTtcbiAgICAgICAgICAgIGlmIChlbGVtZW50LmFsdFRleHQpIHtcbiAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzLmFsdCA9IGVsZW1lbnQuYWx0VGV4dDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF8uZXh0ZW5kKGF0dHJpYnV0ZXMsIHJlc3VsdCk7XG5cbiAgICAgICAgICAgIHJldHVybiBbSHRtbC5mcmVzaEVsZW1lbnQoXCJpbWdcIiwgYXR0cmlidXRlcyldO1xuICAgICAgICB9KTtcbiAgICB9O1xufVxuXG4vLyBVbmRvY3VtZW50ZWQsIGJ1dCByZXRhaW5lZCBmb3IgYmFja3dhcmRzLWNvbXBhdGliaWxpdHkgd2l0aCAwLjMueFxuZXhwb3J0cy5pbmxpbmUgPSBleHBvcnRzLmltZ0VsZW1lbnQ7XG5cbmV4cG9ydHMuZGF0YVVyaSA9IGltZ0VsZW1lbnQoZnVuY3Rpb24oZWxlbWVudCkge1xuICAgIHJldHVybiBlbGVtZW50LnJlYWRBc0Jhc2U2NFN0cmluZygpLnRoZW4oZnVuY3Rpb24oaW1hZ2VCdWZmZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNyYzogXCJkYXRhOlwiICsgZWxlbWVudC5jb250ZW50VHlwZSArIFwiO2Jhc2U2NCxcIiArIGltYWdlQnVmZmVyXG4gICAgICAgIH07XG4gICAgfSk7XG59KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/images.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/mammoth/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar docxReader = __webpack_require__(/*! ./docx/docx-reader */ \"(ssr)/./node_modules/mammoth/lib/docx/docx-reader.js\");\nvar docxStyleMap = __webpack_require__(/*! ./docx/style-map */ \"(ssr)/./node_modules/mammoth/lib/docx/style-map.js\");\nvar DocumentConverter = (__webpack_require__(/*! ./document-to-html */ \"(ssr)/./node_modules/mammoth/lib/document-to-html.js\").DocumentConverter);\nvar convertElementToRawText = (__webpack_require__(/*! ./raw-text */ \"(ssr)/./node_modules/mammoth/lib/raw-text.js\").convertElementToRawText);\nvar readStyle = (__webpack_require__(/*! ./style-reader */ \"(ssr)/./node_modules/mammoth/lib/style-reader.js\").readStyle);\nvar readOptions = (__webpack_require__(/*! ./options-reader */ \"(ssr)/./node_modules/mammoth/lib/options-reader.js\").readOptions);\nvar unzip = __webpack_require__(/*! ./unzip */ \"(ssr)/./node_modules/mammoth/lib/unzip.js\");\nvar Result = (__webpack_require__(/*! ./results */ \"(ssr)/./node_modules/mammoth/lib/results.js\").Result);\n\nexports.convertToHtml = convertToHtml;\nexports.convertToMarkdown = convertToMarkdown;\nexports.convert = convert;\nexports.extractRawText = extractRawText;\nexports.images = __webpack_require__(/*! ./images */ \"(ssr)/./node_modules/mammoth/lib/images.js\");\nexports.transforms = __webpack_require__(/*! ./transforms */ \"(ssr)/./node_modules/mammoth/lib/transforms.js\");\nexports.underline = __webpack_require__(/*! ./underline */ \"(ssr)/./node_modules/mammoth/lib/underline.js\");\nexports.embedStyleMap = embedStyleMap;\nexports.readEmbeddedStyleMap = readEmbeddedStyleMap;\n\nfunction convertToHtml(input, options) {\n    return convert(input, options);\n}\n\nfunction convertToMarkdown(input, options) {\n    var markdownOptions = Object.create(options || {});\n    markdownOptions.outputFormat = \"markdown\";\n    return convert(input, markdownOptions);\n}\n\nfunction convert(input, options) {\n    options = readOptions(options);\n\n    return unzip.openZip(input)\n        .tap(function(docxFile) {\n            return docxStyleMap.readStyleMap(docxFile).then(function(styleMap) {\n                options.embeddedStyleMap = styleMap;\n            });\n        })\n        .then(function(docxFile) {\n            return docxReader.read(docxFile, input)\n                .then(function(documentResult) {\n                    return documentResult.map(options.transformDocument);\n                })\n                .then(function(documentResult) {\n                    return convertDocumentToHtml(documentResult, options);\n                });\n        });\n}\n\nfunction readEmbeddedStyleMap(input) {\n    return unzip.openZip(input)\n        .then(docxStyleMap.readStyleMap);\n}\n\nfunction convertDocumentToHtml(documentResult, options) {\n    var styleMapResult = parseStyleMap(options.readStyleMap());\n    var parsedOptions = _.extend({}, options, {\n        styleMap: styleMapResult.value\n    });\n    var documentConverter = new DocumentConverter(parsedOptions);\n\n    return documentResult.flatMapThen(function(document) {\n        return styleMapResult.flatMapThen(function(styleMap) {\n            return documentConverter.convertToHtml(document);\n        });\n    });\n}\n\nfunction parseStyleMap(styleMap) {\n    return Result.combine((styleMap || []).map(readStyle))\n        .map(function(styleMap) {\n            return styleMap.filter(function(styleMapping) {\n                return !!styleMapping;\n            });\n        });\n}\n\n\nfunction extractRawText(input) {\n    return unzip.openZip(input)\n        .then(docxReader.read)\n        .then(function(documentResult) {\n            return documentResult.map(convertElementToRawText);\n        });\n}\n\nfunction embedStyleMap(input, styleMap) {\n    return unzip.openZip(input)\n        .tap(function(docxFile) {\n            return docxStyleMap.writeStyleMap(docxFile, styleMap);\n        })\n        .then(function(docxFile) {\n            return docxFile.toArrayBuffer();\n        })\n        .then(function(arrayBuffer) {\n            return {\n                toArrayBuffer: function() {\n                    return arrayBuffer;\n                },\n                toBuffer: function() {\n                    return Buffer.from(arrayBuffer);\n                }\n            };\n        });\n}\n\nexports.styleMapping = function() {\n    throw new Error('Use a raw string instead of mammoth.styleMapping e.g. \"p[style-name=\\'Title\\'] => h1\" instead of mammoth.styleMapping(\"p[style-name=\\'Title\\'] => h1\")');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsUUFBUSxtQkFBTyxDQUFDLHdFQUFZOztBQUU1QixpQkFBaUIsbUJBQU8sQ0FBQyxnRkFBb0I7QUFDN0MsbUJBQW1CLG1CQUFPLENBQUMsNEVBQWtCO0FBQzdDLHdCQUF3Qix5SEFBK0M7QUFDdkUsOEJBQThCLCtHQUE2QztBQUMzRSxnQkFBZ0IseUdBQW1DO0FBQ25ELGtCQUFrQiwrR0FBdUM7QUFDekQsWUFBWSxtQkFBTyxDQUFDLDBEQUFTO0FBQzdCLGFBQWEsNEZBQTJCOztBQUV4QyxxQkFBcUI7QUFDckIseUJBQXlCO0FBQ3pCLGVBQWU7QUFDZixzQkFBc0I7QUFDdEIsa0dBQW9DO0FBQ3BDLDhHQUE0QztBQUM1QywyR0FBMEM7QUFDMUMscUJBQXFCO0FBQ3JCLDRCQUE0Qjs7QUFFNUI7QUFDQTtBQUNBOztBQUVBO0FBQ0EscURBQXFEO0FBQ3JEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLFNBQVM7QUFDVDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDs7QUFFQSxvQkFBb0I7QUFDcEI7QUFDQSIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBfID0gcmVxdWlyZShcInVuZGVyc2NvcmVcIik7XG5cbnZhciBkb2N4UmVhZGVyID0gcmVxdWlyZShcIi4vZG9jeC9kb2N4LXJlYWRlclwiKTtcbnZhciBkb2N4U3R5bGVNYXAgPSByZXF1aXJlKFwiLi9kb2N4L3N0eWxlLW1hcFwiKTtcbnZhciBEb2N1bWVudENvbnZlcnRlciA9IHJlcXVpcmUoXCIuL2RvY3VtZW50LXRvLWh0bWxcIikuRG9jdW1lbnRDb252ZXJ0ZXI7XG52YXIgY29udmVydEVsZW1lbnRUb1Jhd1RleHQgPSByZXF1aXJlKFwiLi9yYXctdGV4dFwiKS5jb252ZXJ0RWxlbWVudFRvUmF3VGV4dDtcbnZhciByZWFkU3R5bGUgPSByZXF1aXJlKFwiLi9zdHlsZS1yZWFkZXJcIikucmVhZFN0eWxlO1xudmFyIHJlYWRPcHRpb25zID0gcmVxdWlyZShcIi4vb3B0aW9ucy1yZWFkZXJcIikucmVhZE9wdGlvbnM7XG52YXIgdW56aXAgPSByZXF1aXJlKFwiLi91bnppcFwiKTtcbnZhciBSZXN1bHQgPSByZXF1aXJlKFwiLi9yZXN1bHRzXCIpLlJlc3VsdDtcblxuZXhwb3J0cy5jb252ZXJ0VG9IdG1sID0gY29udmVydFRvSHRtbDtcbmV4cG9ydHMuY29udmVydFRvTWFya2Rvd24gPSBjb252ZXJ0VG9NYXJrZG93bjtcbmV4cG9ydHMuY29udmVydCA9IGNvbnZlcnQ7XG5leHBvcnRzLmV4dHJhY3RSYXdUZXh0ID0gZXh0cmFjdFJhd1RleHQ7XG5leHBvcnRzLmltYWdlcyA9IHJlcXVpcmUoXCIuL2ltYWdlc1wiKTtcbmV4cG9ydHMudHJhbnNmb3JtcyA9IHJlcXVpcmUoXCIuL3RyYW5zZm9ybXNcIik7XG5leHBvcnRzLnVuZGVybGluZSA9IHJlcXVpcmUoXCIuL3VuZGVybGluZVwiKTtcbmV4cG9ydHMuZW1iZWRTdHlsZU1hcCA9IGVtYmVkU3R5bGVNYXA7XG5leHBvcnRzLnJlYWRFbWJlZGRlZFN0eWxlTWFwID0gcmVhZEVtYmVkZGVkU3R5bGVNYXA7XG5cbmZ1bmN0aW9uIGNvbnZlcnRUb0h0bWwoaW5wdXQsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gY29udmVydChpbnB1dCwgb3B0aW9ucyk7XG59XG5cbmZ1bmN0aW9uIGNvbnZlcnRUb01hcmtkb3duKGlucHV0LCBvcHRpb25zKSB7XG4gICAgdmFyIG1hcmtkb3duT3B0aW9ucyA9IE9iamVjdC5jcmVhdGUob3B0aW9ucyB8fCB7fSk7XG4gICAgbWFya2Rvd25PcHRpb25zLm91dHB1dEZvcm1hdCA9IFwibWFya2Rvd25cIjtcbiAgICByZXR1cm4gY29udmVydChpbnB1dCwgbWFya2Rvd25PcHRpb25zKTtcbn1cblxuZnVuY3Rpb24gY29udmVydChpbnB1dCwgb3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSByZWFkT3B0aW9ucyhvcHRpb25zKTtcblxuICAgIHJldHVybiB1bnppcC5vcGVuWmlwKGlucHV0KVxuICAgICAgICAudGFwKGZ1bmN0aW9uKGRvY3hGaWxlKSB7XG4gICAgICAgICAgICByZXR1cm4gZG9jeFN0eWxlTWFwLnJlYWRTdHlsZU1hcChkb2N4RmlsZSkudGhlbihmdW5jdGlvbihzdHlsZU1hcCkge1xuICAgICAgICAgICAgICAgIG9wdGlvbnMuZW1iZWRkZWRTdHlsZU1hcCA9IHN0eWxlTWFwO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pXG4gICAgICAgIC50aGVuKGZ1bmN0aW9uKGRvY3hGaWxlKSB7XG4gICAgICAgICAgICByZXR1cm4gZG9jeFJlYWRlci5yZWFkKGRvY3hGaWxlLCBpbnB1dClcbiAgICAgICAgICAgICAgICAudGhlbihmdW5jdGlvbihkb2N1bWVudFJlc3VsdCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZG9jdW1lbnRSZXN1bHQubWFwKG9wdGlvbnMudHJhbnNmb3JtRG9jdW1lbnQpO1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24oZG9jdW1lbnRSZXN1bHQpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnREb2N1bWVudFRvSHRtbChkb2N1bWVudFJlc3VsdCwgb3B0aW9ucyk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xufVxuXG5mdW5jdGlvbiByZWFkRW1iZWRkZWRTdHlsZU1hcChpbnB1dCkge1xuICAgIHJldHVybiB1bnppcC5vcGVuWmlwKGlucHV0KVxuICAgICAgICAudGhlbihkb2N4U3R5bGVNYXAucmVhZFN0eWxlTWFwKTtcbn1cblxuZnVuY3Rpb24gY29udmVydERvY3VtZW50VG9IdG1sKGRvY3VtZW50UmVzdWx0LCBvcHRpb25zKSB7XG4gICAgdmFyIHN0eWxlTWFwUmVzdWx0ID0gcGFyc2VTdHlsZU1hcChvcHRpb25zLnJlYWRTdHlsZU1hcCgpKTtcbiAgICB2YXIgcGFyc2VkT3B0aW9ucyA9IF8uZXh0ZW5kKHt9LCBvcHRpb25zLCB7XG4gICAgICAgIHN0eWxlTWFwOiBzdHlsZU1hcFJlc3VsdC52YWx1ZVxuICAgIH0pO1xuICAgIHZhciBkb2N1bWVudENvbnZlcnRlciA9IG5ldyBEb2N1bWVudENvbnZlcnRlcihwYXJzZWRPcHRpb25zKTtcblxuICAgIHJldHVybiBkb2N1bWVudFJlc3VsdC5mbGF0TWFwVGhlbihmdW5jdGlvbihkb2N1bWVudCkge1xuICAgICAgICByZXR1cm4gc3R5bGVNYXBSZXN1bHQuZmxhdE1hcFRoZW4oZnVuY3Rpb24oc3R5bGVNYXApIHtcbiAgICAgICAgICAgIHJldHVybiBkb2N1bWVudENvbnZlcnRlci5jb252ZXJ0VG9IdG1sKGRvY3VtZW50KTtcbiAgICAgICAgfSk7XG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIHBhcnNlU3R5bGVNYXAoc3R5bGVNYXApIHtcbiAgICByZXR1cm4gUmVzdWx0LmNvbWJpbmUoKHN0eWxlTWFwIHx8IFtdKS5tYXAocmVhZFN0eWxlKSlcbiAgICAgICAgLm1hcChmdW5jdGlvbihzdHlsZU1hcCkge1xuICAgICAgICAgICAgcmV0dXJuIHN0eWxlTWFwLmZpbHRlcihmdW5jdGlvbihzdHlsZU1hcHBpbmcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gISFzdHlsZU1hcHBpbmc7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG59XG5cblxuZnVuY3Rpb24gZXh0cmFjdFJhd1RleHQoaW5wdXQpIHtcbiAgICByZXR1cm4gdW56aXAub3BlblppcChpbnB1dClcbiAgICAgICAgLnRoZW4oZG9jeFJlYWRlci5yZWFkKVxuICAgICAgICAudGhlbihmdW5jdGlvbihkb2N1bWVudFJlc3VsdCkge1xuICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50UmVzdWx0Lm1hcChjb252ZXJ0RWxlbWVudFRvUmF3VGV4dCk7XG4gICAgICAgIH0pO1xufVxuXG5mdW5jdGlvbiBlbWJlZFN0eWxlTWFwKGlucHV0LCBzdHlsZU1hcCkge1xuICAgIHJldHVybiB1bnppcC5vcGVuWmlwKGlucHV0KVxuICAgICAgICAudGFwKGZ1bmN0aW9uKGRvY3hGaWxlKSB7XG4gICAgICAgICAgICByZXR1cm4gZG9jeFN0eWxlTWFwLndyaXRlU3R5bGVNYXAoZG9jeEZpbGUsIHN0eWxlTWFwKTtcbiAgICAgICAgfSlcbiAgICAgICAgLnRoZW4oZnVuY3Rpb24oZG9jeEZpbGUpIHtcbiAgICAgICAgICAgIHJldHVybiBkb2N4RmlsZS50b0FycmF5QnVmZmVyKCk7XG4gICAgICAgIH0pXG4gICAgICAgIC50aGVuKGZ1bmN0aW9uKGFycmF5QnVmZmVyKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHRvQXJyYXlCdWZmZXI6IGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYXJyYXlCdWZmZXI7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB0b0J1ZmZlcjogZnVuY3Rpb24oKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBCdWZmZXIuZnJvbShhcnJheUJ1ZmZlcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSk7XG59XG5cbmV4cG9ydHMuc3R5bGVNYXBwaW5nID0gZnVuY3Rpb24oKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdVc2UgYSByYXcgc3RyaW5nIGluc3RlYWQgb2YgbWFtbW90aC5zdHlsZU1hcHBpbmcgZS5nLiBcInBbc3R5bGUtbmFtZT1cXCdUaXRsZVxcJ10gPT4gaDFcIiBpbnN0ZWFkIG9mIG1hbW1vdGguc3R5bGVNYXBwaW5nKFwicFtzdHlsZS1uYW1lPVxcJ1RpdGxlXFwnXSA9PiBoMVwiKScpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/options-reader.js":
/*!****************************************************!*\
  !*** ./node_modules/mammoth/lib/options-reader.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.readOptions = readOptions;\n\n\nvar _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar defaultStyleMap = exports._defaultStyleMap = [\n    \"p.Heading1 => h1:fresh\",\n    \"p.Heading2 => h2:fresh\",\n    \"p.Heading3 => h3:fresh\",\n    \"p.Heading4 => h4:fresh\",\n    \"p.Heading5 => h5:fresh\",\n    \"p.Heading6 => h6:fresh\",\n    \"p[style-name='Heading 1'] => h1:fresh\",\n    \"p[style-name='Heading 2'] => h2:fresh\",\n    \"p[style-name='Heading 3'] => h3:fresh\",\n    \"p[style-name='Heading 4'] => h4:fresh\",\n    \"p[style-name='Heading 5'] => h5:fresh\",\n    \"p[style-name='Heading 6'] => h6:fresh\",\n    \"p[style-name='heading 1'] => h1:fresh\",\n    \"p[style-name='heading 2'] => h2:fresh\",\n    \"p[style-name='heading 3'] => h3:fresh\",\n    \"p[style-name='heading 4'] => h4:fresh\",\n    \"p[style-name='heading 5'] => h5:fresh\",\n    \"p[style-name='heading 6'] => h6:fresh\",\n    \n    \"r[style-name='Strong'] => strong\",\n    \n    \"p[style-name='footnote text'] => p:fresh\",\n    \"r[style-name='footnote reference'] =>\",\n    \"p[style-name='endnote text'] => p:fresh\",\n    \"r[style-name='endnote reference'] =>\",\n    \"p[style-name='annotation text'] => p:fresh\",\n    \"r[style-name='annotation reference'] =>\",\n    \n    // LibreOffice\n    \"p[style-name='Footnote'] => p:fresh\",\n    \"r[style-name='Footnote anchor'] =>\",\n    \"p[style-name='Endnote'] => p:fresh\",\n    \"r[style-name='Endnote anchor'] =>\",\n    \n    \"p:unordered-list(1) => ul > li:fresh\",\n    \"p:unordered-list(2) => ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(3) => ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:unordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh\",\n    \"p:ordered-list(1) => ol > li:fresh\",\n    \"p:ordered-list(2) => ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(3) => ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \"p:ordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh\",\n    \n    \"r[style-name='Hyperlink'] =>\",\n    \n    \"p[style-name='Normal'] => p:fresh\"\n];\n\nvar standardOptions = exports._standardOptions = {\n    transformDocument: identity,\n    includeDefaultStyleMap: true,\n    includeEmbeddedStyleMap: true\n};\n\nfunction readOptions(options) {\n    options = options || {};\n    return _.extend({}, standardOptions, options, {\n        customStyleMap: readStyleMap(options.styleMap),\n        readStyleMap: function() {\n            var styleMap = this.customStyleMap;\n            if (this.includeEmbeddedStyleMap) {\n                styleMap = styleMap.concat(readStyleMap(this.embeddedStyleMap));\n            }\n            if (this.includeDefaultStyleMap) {\n                styleMap = styleMap.concat(defaultStyleMap);\n            }\n            return styleMap;\n        }\n    });\n}\n\nfunction readStyleMap(styleMap) {\n    if (!styleMap) {\n        return [];\n    } else if (_.isString(styleMap)) {\n        return styleMap.split(\"\\n\")\n            .map(function(line) {\n                return line.trim();\n            })\n            .filter(function(line) {\n                return line !== \"\" && line.charAt(0) !== \"#\";\n            });\n    } else {\n        return styleMap;\n    }\n}\n\nfunction identity(value) {\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/options-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/promises.js":
/*!**********************************************!*\
  !*** ./node_modules/mammoth/lib/promises.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\nvar bluebird = __webpack_require__(/*! bluebird/js/release/promise */ \"(ssr)/./node_modules/bluebird/js/release/promise.js\")();\n\nexports.defer = defer;\nexports.when = bluebird.resolve;\nexports.resolve = bluebird.resolve;\nexports.all = bluebird.all;\nexports.props = bluebird.props;\nexports.reject = bluebird.reject;\nexports.promisify = bluebird.promisify;\nexports.mapSeries = bluebird.mapSeries;\nexports.attempt = bluebird.attempt;\n\nexports.nfcall = function(func) {\n    var args = Array.prototype.slice.call(arguments, 1);\n    var promisedFunc = bluebird.promisify(func);\n    return promisedFunc.apply(null, args);\n};\n\nbluebird.prototype.fail = bluebird.prototype.caught;\n\nbluebird.prototype.also = function(func) {\n    return this.then(function(value) {\n        var returnValue = _.extend({}, value, func(value));\n        return bluebird.props(returnValue);\n    });\n};\n\nfunction defer() {\n    var resolve;\n    var reject;\n    var promise = new bluebird.Promise(function(resolveArg, rejectArg) {\n        resolve = resolveArg;\n        reject = rejectArg;\n    });\n\n    return {\n        resolve: resolve,\n        reject: reject,\n        promise: promise\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/promises.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/raw-text.js":
/*!**********************************************!*\
  !*** ./node_modules/mammoth/lib/raw-text.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var documents = __webpack_require__(/*! ./documents */ \"(ssr)/./node_modules/mammoth/lib/documents.js\");\n\nfunction convertElementToRawText(element) {\n    if (element.type === \"text\") {\n        return element.value;\n    } else if (element.type === documents.types.tab) {\n        return \"\\t\";\n    } else {\n        var tail = element.type === \"paragraph\" ? \"\\n\\n\" : \"\";\n        return (element.children || []).map(convertElementToRawText).join(\"\") + tail;\n    }\n}\n\nexports.convertElementToRawText = convertElementToRawText;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvcmF3LXRleHQuanMiLCJtYXBwaW5ncyI6IkFBQUEsZ0JBQWdCLG1CQUFPLENBQUMsa0VBQWE7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwrQkFBK0IiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFxyYXctdGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZG9jdW1lbnRzID0gcmVxdWlyZShcIi4vZG9jdW1lbnRzXCIpO1xuXG5mdW5jdGlvbiBjb252ZXJ0RWxlbWVudFRvUmF3VGV4dChlbGVtZW50KSB7XG4gICAgaWYgKGVsZW1lbnQudHlwZSA9PT0gXCJ0ZXh0XCIpIHtcbiAgICAgICAgcmV0dXJuIGVsZW1lbnQudmFsdWU7XG4gICAgfSBlbHNlIGlmIChlbGVtZW50LnR5cGUgPT09IGRvY3VtZW50cy50eXBlcy50YWIpIHtcbiAgICAgICAgcmV0dXJuIFwiXFx0XCI7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgdmFyIHRhaWwgPSBlbGVtZW50LnR5cGUgPT09IFwicGFyYWdyYXBoXCIgPyBcIlxcblxcblwiIDogXCJcIjtcbiAgICAgICAgcmV0dXJuIChlbGVtZW50LmNoaWxkcmVuIHx8IFtdKS5tYXAoY29udmVydEVsZW1lbnRUb1Jhd1RleHQpLmpvaW4oXCJcIikgKyB0YWlsO1xuICAgIH1cbn1cblxuZXhwb3J0cy5jb252ZXJ0RWxlbWVudFRvUmF3VGV4dCA9IGNvbnZlcnRFbGVtZW50VG9SYXdUZXh0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/raw-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/results.js":
/*!*********************************************!*\
  !*** ./node_modules/mammoth/lib/results.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\n\nexports.Result = Result;\nexports.success = success;\nexports.warning = warning;\nexports.error = error;\n\n\nfunction Result(value, messages) {\n    this.value = value;\n    this.messages = messages || [];\n}\n\nResult.prototype.map = function(func) {\n    return new Result(func(this.value), this.messages);\n};\n\nResult.prototype.flatMap = function(func) {\n    var funcResult = func(this.value);\n    return new Result(funcResult.value, combineMessages([this, funcResult]));\n};\n\nResult.prototype.flatMapThen = function(func) {\n    var that = this;\n    return func(this.value).then(function(otherResult) {\n        return new Result(otherResult.value, combineMessages([that, otherResult]));\n    });\n};\n\nResult.combine = function(results) {\n    var values = _.flatten(_.pluck(results, \"value\"));\n    var messages = combineMessages(results);\n    return new Result(values, messages);\n};\n\nfunction success(value) {\n    return new Result(value, []);\n}\n\nfunction warning(message) {\n    return {\n        type: \"warning\",\n        message: message\n    };\n}\n\nfunction error(exception) {\n    return {\n        type: \"error\",\n        message: exception.message,\n        error: exception\n    };\n}\n\nfunction combineMessages(results) {\n    var messages = [];\n    _.flatten(_.pluck(results, \"messages\"), true).forEach(function(message) {\n        if (!containsMessage(messages, message)) {\n            messages.push(message);\n        }\n    });\n    return messages;\n}\n\nfunction containsMessage(messages, message) {\n    return _.find(messages, isSameMessage.bind(null, message)) !== undefined;\n}\n\nfunction isSameMessage(first, second) {\n    return first.type === second.type && first.message === second.message;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/results.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/style-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/mammoth/lib/style-reader.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\nvar lop = __webpack_require__(/*! lop */ \"(ssr)/./node_modules/lop/index.js\");\n\nvar documentMatchers = __webpack_require__(/*! ./styles/document-matchers */ \"(ssr)/./node_modules/mammoth/lib/styles/document-matchers.js\");\nvar htmlPaths = __webpack_require__(/*! ./styles/html-paths */ \"(ssr)/./node_modules/mammoth/lib/styles/html-paths.js\");\nvar tokenise = (__webpack_require__(/*! ./styles/parser/tokeniser */ \"(ssr)/./node_modules/mammoth/lib/styles/parser/tokeniser.js\").tokenise);\nvar results = __webpack_require__(/*! ./results */ \"(ssr)/./node_modules/mammoth/lib/results.js\");\n\nexports.readHtmlPath = readHtmlPath;\nexports.readDocumentMatcher = readDocumentMatcher;\nexports.readStyle = readStyle;\n\n\nfunction readStyle(string) {\n    return parseString(styleRule, string);\n}\n\nfunction createStyleRule() {\n    return lop.rules.sequence(\n        lop.rules.sequence.capture(documentMatcherRule()),\n        lop.rules.tokenOfType(\"whitespace\"),\n        lop.rules.tokenOfType(\"arrow\"),\n        lop.rules.sequence.capture(lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"whitespace\"),\n            lop.rules.sequence.capture(htmlPathRule())\n        ).head())),\n        lop.rules.tokenOfType(\"end\")\n    ).map(function(documentMatcher, htmlPath) {\n        return {\n            from: documentMatcher,\n            to: htmlPath.valueOrElse(htmlPaths.empty)\n        };\n    });\n}\n\nfunction readDocumentMatcher(string) {\n    return parseString(documentMatcherRule(), string);\n}\n\nfunction documentMatcherRule() {\n    var sequence = lop.rules.sequence;\n\n    var identifierToConstant = function(identifier, constant) {\n        return lop.rules.then(\n            lop.rules.token(\"identifier\", identifier),\n            function() {\n                return constant;\n            }\n        );\n    };\n\n    var paragraphRule = identifierToConstant(\"p\", documentMatchers.paragraph);\n    var runRule = identifierToConstant(\"r\", documentMatchers.run);\n\n    var elementTypeRule = lop.rules.firstOf(\"p or r or table\",\n        paragraphRule,\n        runRule\n    );\n\n    var styleIdRule = lop.rules.sequence(\n        lop.rules.tokenOfType(\"dot\"),\n        lop.rules.sequence.cut(),\n        lop.rules.sequence.capture(identifierRule)\n    ).map(function(styleId) {\n        return {styleId: styleId};\n    });\n\n    var styleNameMatcherRule = lop.rules.firstOf(\"style name matcher\",\n        lop.rules.then(\n            lop.rules.sequence(\n                lop.rules.tokenOfType(\"equals\"),\n                lop.rules.sequence.cut(),\n                lop.rules.sequence.capture(stringRule)\n            ).head(),\n            function(styleName) {\n                return {styleName: documentMatchers.equalTo(styleName)};\n            }\n        ),\n        lop.rules.then(\n            lop.rules.sequence(\n                lop.rules.tokenOfType(\"startsWith\"),\n                lop.rules.sequence.cut(),\n                lop.rules.sequence.capture(stringRule)\n            ).head(),\n            function(styleName) {\n                return {styleName: documentMatchers.startsWith(styleName)};\n            }\n        )\n    );\n\n    var styleNameRule = lop.rules.sequence(\n        lop.rules.tokenOfType(\"open-square-bracket\"),\n        lop.rules.sequence.cut(),\n        lop.rules.token(\"identifier\", \"style-name\"),\n        lop.rules.sequence.capture(styleNameMatcherRule),\n        lop.rules.tokenOfType(\"close-square-bracket\")\n    ).head();\n\n\n    var listTypeRule = lop.rules.firstOf(\"list type\",\n        identifierToConstant(\"ordered-list\", {isOrdered: true}),\n        identifierToConstant(\"unordered-list\", {isOrdered: false})\n    );\n    var listRule = sequence(\n        lop.rules.tokenOfType(\"colon\"),\n        sequence.capture(listTypeRule),\n        sequence.cut(),\n        lop.rules.tokenOfType(\"open-paren\"),\n        sequence.capture(integerRule),\n        lop.rules.tokenOfType(\"close-paren\")\n    ).map(function(listType, levelNumber) {\n        return {\n            list: {\n                isOrdered: listType.isOrdered,\n                levelIndex: levelNumber - 1\n            }\n        };\n    });\n\n    function createMatcherSuffixesRule(rules) {\n        var matcherSuffix = lop.rules.firstOf.apply(\n            lop.rules.firstOf,\n            [\"matcher suffix\"].concat(rules)\n        );\n        var matcherSuffixes = lop.rules.zeroOrMore(matcherSuffix);\n        return lop.rules.then(matcherSuffixes, function(suffixes) {\n            var matcherOptions = {};\n            suffixes.forEach(function(suffix) {\n                _.extend(matcherOptions, suffix);\n            });\n            return matcherOptions;\n        });\n    }\n\n    var paragraphOrRun = sequence(\n        sequence.capture(elementTypeRule),\n        sequence.capture(createMatcherSuffixesRule([\n            styleIdRule,\n            styleNameRule,\n            listRule\n        ]))\n    ).map(function(createMatcher, matcherOptions) {\n        return createMatcher(matcherOptions);\n    });\n\n    var table = sequence(\n        lop.rules.token(\"identifier\", \"table\"),\n        sequence.capture(createMatcherSuffixesRule([\n            styleIdRule,\n            styleNameRule\n        ]))\n    ).map(function(options) {\n        return documentMatchers.table(options);\n    });\n\n    var bold = identifierToConstant(\"b\", documentMatchers.bold);\n    var italic = identifierToConstant(\"i\", documentMatchers.italic);\n    var underline = identifierToConstant(\"u\", documentMatchers.underline);\n    var strikethrough = identifierToConstant(\"strike\", documentMatchers.strikethrough);\n    var allCaps = identifierToConstant(\"all-caps\", documentMatchers.allCaps);\n    var smallCaps = identifierToConstant(\"small-caps\", documentMatchers.smallCaps);\n\n    var highlight = sequence(\n        lop.rules.token(\"identifier\", \"highlight\"),\n        lop.rules.sequence.capture(lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"open-square-bracket\"),\n            lop.rules.sequence.cut(),\n            lop.rules.token(\"identifier\", \"color\"),\n            lop.rules.tokenOfType(\"equals\"),\n            lop.rules.sequence.capture(stringRule),\n            lop.rules.tokenOfType(\"close-square-bracket\")\n        ).head()))\n    ).map(function(color) {\n        return documentMatchers.highlight({\n            color: color.valueOrElse(undefined)\n        });\n    });\n\n    var commentReference = identifierToConstant(\"comment-reference\", documentMatchers.commentReference);\n\n    var breakMatcher = sequence(\n        lop.rules.token(\"identifier\", \"br\"),\n        sequence.cut(),\n        lop.rules.tokenOfType(\"open-square-bracket\"),\n        lop.rules.token(\"identifier\", \"type\"),\n        lop.rules.tokenOfType(\"equals\"),\n        sequence.capture(stringRule),\n        lop.rules.tokenOfType(\"close-square-bracket\")\n    ).map(function(breakType) {\n        switch (breakType) {\n        case \"line\":\n            return documentMatchers.lineBreak;\n        case \"page\":\n            return documentMatchers.pageBreak;\n        case \"column\":\n            return documentMatchers.columnBreak;\n        default:\n            // TODO: handle unknown document matchers\n        }\n    });\n\n    return lop.rules.firstOf(\"element type\",\n        paragraphOrRun,\n        table,\n        bold,\n        italic,\n        underline,\n        strikethrough,\n        allCaps,\n        smallCaps,\n        highlight,\n        commentReference,\n        breakMatcher\n    );\n}\n\nfunction readHtmlPath(string) {\n    return parseString(htmlPathRule(), string);\n}\n\nfunction htmlPathRule() {\n    var capture = lop.rules.sequence.capture;\n    var whitespaceRule = lop.rules.tokenOfType(\"whitespace\");\n    var freshRule = lop.rules.then(\n        lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"colon\"),\n            lop.rules.token(\"identifier\", \"fresh\")\n        )),\n        function(option) {\n            return option.map(function() {\n                return true;\n            }).valueOrElse(false);\n        }\n    );\n\n    var separatorRule = lop.rules.then(\n        lop.rules.optional(lop.rules.sequence(\n            lop.rules.tokenOfType(\"colon\"),\n            lop.rules.token(\"identifier\", \"separator\"),\n            lop.rules.tokenOfType(\"open-paren\"),\n            capture(stringRule),\n            lop.rules.tokenOfType(\"close-paren\")\n        ).head()),\n        function(option) {\n            return option.valueOrElse(\"\");\n        }\n    );\n\n    var tagNamesRule = lop.rules.oneOrMoreWithSeparator(\n        identifierRule,\n        lop.rules.tokenOfType(\"choice\")\n    );\n\n    var styleElementRule = lop.rules.sequence(\n        capture(tagNamesRule),\n        capture(lop.rules.zeroOrMore(attributeOrClassRule)),\n        capture(freshRule),\n        capture(separatorRule)\n    ).map(function(tagName, attributesList, fresh, separator) {\n        var attributes = {};\n        var options = {};\n        attributesList.forEach(function(attribute) {\n            if (attribute.append && attributes[attribute.name]) {\n                attributes[attribute.name] += \" \" + attribute.value;\n            } else {\n                attributes[attribute.name] = attribute.value;\n            }\n        });\n        if (fresh) {\n            options.fresh = true;\n        }\n        if (separator) {\n            options.separator = separator;\n        }\n        return htmlPaths.element(tagName, attributes, options);\n    });\n\n    return lop.rules.firstOf(\"html path\",\n        lop.rules.then(lop.rules.tokenOfType(\"bang\"), function() {\n            return htmlPaths.ignore;\n        }),\n        lop.rules.then(\n            lop.rules.zeroOrMoreWithSeparator(\n                styleElementRule,\n                lop.rules.sequence(\n                    whitespaceRule,\n                    lop.rules.tokenOfType(\"gt\"),\n                    whitespaceRule\n                )\n            ),\n            htmlPaths.elements\n        )\n    );\n}\n\nvar identifierRule = lop.rules.then(\n    lop.rules.tokenOfType(\"identifier\"),\n    decodeEscapeSequences\n);\nvar integerRule = lop.rules.tokenOfType(\"integer\");\n\nvar stringRule = lop.rules.then(\n    lop.rules.tokenOfType(\"string\"),\n    decodeEscapeSequences\n);\n\nvar escapeSequences = {\n    \"n\": \"\\n\",\n    \"r\": \"\\r\",\n    \"t\": \"\\t\"\n};\n\nfunction decodeEscapeSequences(value) {\n    return value.replace(/\\\\(.)/g, function(match, code) {\n        return escapeSequences[code] || code;\n    });\n}\n\nvar attributeRule = lop.rules.sequence(\n    lop.rules.tokenOfType(\"open-square-bracket\"),\n    lop.rules.sequence.cut(),\n    lop.rules.sequence.capture(identifierRule),\n    lop.rules.tokenOfType(\"equals\"),\n    lop.rules.sequence.capture(stringRule),\n    lop.rules.tokenOfType(\"close-square-bracket\")\n).map(function(name, value) {\n    return {name: name, value: value, append: false};\n});\n\nvar classRule = lop.rules.sequence(\n    lop.rules.tokenOfType(\"dot\"),\n    lop.rules.sequence.cut(),\n    lop.rules.sequence.capture(identifierRule)\n).map(function(className) {\n    return {name: \"class\", value: className, append: true};\n});\n\nvar attributeOrClassRule = lop.rules.firstOf(\n    \"attribute or class\",\n    attributeRule,\n    classRule\n);\n\nfunction parseString(rule, string) {\n    var tokens = tokenise(string);\n    var parser = lop.Parser();\n    var parseResult = parser.parseTokens(rule, tokens);\n    if (parseResult.isSuccess()) {\n        return results.success(parseResult.value());\n    } else {\n        return new results.Result(null, [results.warning(describeFailure(string, parseResult))]);\n    }\n}\n\nfunction describeFailure(input, parseResult) {\n    return \"Did not understand this style mapping, so ignored it: \" + input + \"\\n\" +\n        parseResult.errors().map(describeError).join(\"\\n\");\n}\n\nfunction describeError(error) {\n    return \"Error was at character number \" + error.characterNumber() + \": \" +\n        \"Expected \" + error.expected + \" but got \" + error.actual;\n}\n\nvar styleRule = createStyleRule();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/style-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/styles/document-matchers.js":
/*!**************************************************************!*\
  !*** ./node_modules/mammoth/lib/styles/document-matchers.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.paragraph = paragraph;\nexports.run = run;\nexports.table = table;\nexports.bold = new Matcher(\"bold\");\nexports.italic = new Matcher(\"italic\");\nexports.underline = new Matcher(\"underline\");\nexports.strikethrough = new Matcher(\"strikethrough\");\nexports.allCaps = new Matcher(\"allCaps\");\nexports.smallCaps = new Matcher(\"smallCaps\");\nexports.highlight = highlight;\nexports.commentReference = new Matcher(\"commentReference\");\nexports.lineBreak = new BreakMatcher({breakType: \"line\"});\nexports.pageBreak = new BreakMatcher({breakType: \"page\"});\nexports.columnBreak = new BreakMatcher({breakType: \"column\"});\nexports.equalTo = equalTo;\nexports.startsWith = startsWith;\n\n\nfunction paragraph(options) {\n    return new Matcher(\"paragraph\", options);\n}\n\nfunction run(options) {\n    return new Matcher(\"run\", options);\n}\n\nfunction table(options) {\n    return new Matcher(\"table\", options);\n}\n\nfunction highlight(options) {\n    return new HighlightMatcher(options);\n}\n\nfunction Matcher(elementType, options) {\n    options = options || {};\n    this._elementType = elementType;\n    this._styleId = options.styleId;\n    this._styleName = options.styleName;\n    if (options.list) {\n        this._listIndex = options.list.levelIndex;\n        this._listIsOrdered = options.list.isOrdered;\n    }\n}\n\nMatcher.prototype.matches = function(element) {\n    return element.type === this._elementType &&\n        (this._styleId === undefined || element.styleId === this._styleId) &&\n        (this._styleName === undefined || (element.styleName && this._styleName.operator(this._styleName.operand, element.styleName))) &&\n        (this._listIndex === undefined || isList(element, this._listIndex, this._listIsOrdered)) &&\n        (this._breakType === undefined || this._breakType === element.breakType);\n};\n\nfunction HighlightMatcher(options) {\n    options = options || {};\n    this._color = options.color;\n}\n\nHighlightMatcher.prototype.matches = function(element) {\n    return element.type === \"highlight\" &&\n        (this._color === undefined || element.color === this._color);\n};\n\nfunction BreakMatcher(options) {\n    options = options || {};\n    this._breakType = options.breakType;\n}\n\nBreakMatcher.prototype.matches = function(element) {\n    return element.type === \"break\" &&\n        (this._breakType === undefined || element.breakType === this._breakType);\n};\n\nfunction isList(element, levelIndex, isOrdered) {\n    return element.numbering &&\n        element.numbering.level == levelIndex &&\n        element.numbering.isOrdered == isOrdered;\n}\n\nfunction equalTo(value) {\n    return {\n        operator: operatorEqualTo,\n        operand: value\n    };\n}\n\nfunction startsWith(value) {\n    return {\n        operator: operatorStartsWith,\n        operand: value\n    };\n}\n\nfunction operatorEqualTo(first, second) {\n    return first.toUpperCase() === second.toUpperCase();\n}\n\nfunction operatorStartsWith(first, second) {\n    return second.toUpperCase().indexOf(first.toUpperCase()) === 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/styles/document-matchers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/styles/html-paths.js":
/*!*******************************************************!*\
  !*** ./node_modules/mammoth/lib/styles/html-paths.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar html = __webpack_require__(/*! ../html */ \"(ssr)/./node_modules/mammoth/lib/html/index.js\");\n\nexports.topLevelElement = topLevelElement;\nexports.elements = elements;\nexports.element = element;\n\nfunction topLevelElement(tagName, attributes) {\n    return elements([element(tagName, attributes, {fresh: true})]);\n}\n\nfunction elements(elementStyles) {\n    return new HtmlPath(elementStyles.map(function(elementStyle) {\n        if (_.isString(elementStyle)) {\n            return element(elementStyle);\n        } else {\n            return elementStyle;\n        }\n    }));\n}\n\nfunction HtmlPath(elements) {\n    this._elements = elements;\n}\n\nHtmlPath.prototype.wrap = function wrap(children) {\n    var result = children();\n    for (var index = this._elements.length - 1; index >= 0; index--) {\n        result = this._elements[index].wrapNodes(result);\n    }\n    return result;\n};\n\nfunction element(tagName, attributes, options) {\n    options = options || {};\n    return new Element(tagName, attributes, options);\n}\n\nfunction Element(tagName, attributes, options) {\n    var tagNames = {};\n    if (_.isArray(tagName)) {\n        tagName.forEach(function(tagName) {\n            tagNames[tagName] = true;\n        });\n        tagName = tagName[0];\n    } else {\n        tagNames[tagName] = true;\n    }\n    \n    this.tagName = tagName;\n    this.tagNames = tagNames;\n    this.attributes = attributes || {};\n    this.fresh = options.fresh;\n    this.separator = options.separator;\n}\n\nElement.prototype.matchesElement = function(element) {\n    return this.tagNames[element.tagName] && _.isEqual(this.attributes || {}, element.attributes || {});\n};\n\nElement.prototype.wrap = function wrap(generateNodes) {\n    return this.wrapNodes(generateNodes());\n};\n\nElement.prototype.wrapNodes = function wrapNodes(nodes) {\n    return [html.elementWithTag(this, nodes)];\n};\n\nexports.empty = elements([]);\nexports.ignore = {\n    wrap: function() {\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/styles/html-paths.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/styles/parser/tokeniser.js":
/*!*************************************************************!*\
  !*** ./node_modules/mammoth/lib/styles/parser/tokeniser.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var lop = __webpack_require__(/*! lop */ \"(ssr)/./node_modules/lop/index.js\");\nvar RegexTokeniser = lop.RegexTokeniser;\n\nexports.tokenise = tokenise;\n\nvar stringPrefix = \"'((?:\\\\\\\\.|[^'])*)\";\n\nfunction tokenise(string) {\n    var identifierCharacter = \"(?:[a-zA-Z\\\\-_]|\\\\\\\\.)\";\n    var tokeniser = new RegexTokeniser([\n        {name: \"identifier\", regex: new RegExp(\"(\" + identifierCharacter + \"(?:\" + identifierCharacter + \"|[0-9])*)\")},\n        {name: \"dot\", regex: /\\./},\n        {name: \"colon\", regex: /:/},\n        {name: \"gt\", regex: />/},\n        {name: \"whitespace\", regex: /\\s+/},\n        {name: \"arrow\", regex: /=>/},\n        {name: \"equals\", regex: /=/},\n        {name: \"startsWith\", regex: /\\^=/},\n        {name: \"open-paren\", regex: /\\(/},\n        {name: \"close-paren\", regex: /\\)/},\n        {name: \"open-square-bracket\", regex: /\\[/},\n        {name: \"close-square-bracket\", regex: /\\]/},\n        {name: \"string\", regex: new RegExp(stringPrefix + \"'\")},\n        {name: \"unterminated-string\", regex: new RegExp(stringPrefix)},\n        {name: \"integer\", regex: /([0-9]+)/},\n        {name: \"choice\", regex: /\\|/},\n        {name: \"bang\", regex: /(!)/}\n    ]);\n    return tokeniser.tokenise(string);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/styles/parser/tokeniser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/transforms.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/transforms.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nexports.paragraph = paragraph;\nexports.run = run;\nexports._elements = elements;\nexports.getDescendantsOfType = getDescendantsOfType;\nexports.getDescendants = getDescendants;\n\nfunction paragraph(transform) {\n    return elementsOfType(\"paragraph\", transform);\n}\n\nfunction run(transform) {\n    return elementsOfType(\"run\", transform);\n}\n\nfunction elementsOfType(elementType, transform) {\n    return elements(function(element) {\n        if (element.type === elementType) {\n            return transform(element);\n        } else {\n            return element;\n        }\n    });\n}\n\nfunction elements(transform) {\n    return function transformElement(element) {\n        if (element.children) {\n            var children = _.map(element.children, transformElement);\n            element = _.extend(element, {children: children});\n        }\n        return transform(element);\n    };\n}\n\n\nfunction getDescendantsOfType(element, type) {\n    return getDescendants(element).filter(function(descendant) {\n        return descendant.type === type;\n    });\n}\n\nfunction getDescendants(element) {\n    var descendants = [];\n\n    visitDescendants(element, function(descendant) {\n        descendants.push(descendant);\n    });\n\n    return descendants;\n}\n\nfunction visitDescendants(element, visit) {\n    if (element.children) {\n        element.children.forEach(function(child) {\n            visitDescendants(child, visit);\n            visit(child);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/transforms.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/underline.js":
/*!***********************************************!*\
  !*** ./node_modules/mammoth/lib/underline.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var htmlPaths = __webpack_require__(/*! ./styles/html-paths */ \"(ssr)/./node_modules/mammoth/lib/styles/html-paths.js\");\nvar Html = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/mammoth/lib/html/index.js\");\n\n\nexports.element = element;\n\nfunction element(name) {\n    return function(html) {\n        return Html.elementWithTag(htmlPaths.element(name), [html]);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvdW5kZXJsaW5lLmpzIiwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQixtQkFBTyxDQUFDLGtGQUFxQjtBQUM3QyxXQUFXLG1CQUFPLENBQUMsOERBQVE7OztBQUczQixlQUFlOztBQUVmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFx1bmRlcmxpbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGh0bWxQYXRocyA9IHJlcXVpcmUoXCIuL3N0eWxlcy9odG1sLXBhdGhzXCIpO1xudmFyIEh0bWwgPSByZXF1aXJlKFwiLi9odG1sXCIpO1xuXG5cbmV4cG9ydHMuZWxlbWVudCA9IGVsZW1lbnQ7XG5cbmZ1bmN0aW9uIGVsZW1lbnQobmFtZSkge1xuICAgIHJldHVybiBmdW5jdGlvbihodG1sKSB7XG4gICAgICAgIHJldHVybiBIdG1sLmVsZW1lbnRXaXRoVGFnKGh0bWxQYXRocy5lbGVtZW50KG5hbWUpLCBbaHRtbF0pO1xuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/underline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/unzip.js":
/*!*******************************************!*\
  !*** ./node_modules/mammoth/lib/unzip.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! fs */ \"fs\");\n\nvar promises = __webpack_require__(/*! ./promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar zipfile = __webpack_require__(/*! ./zipfile */ \"(ssr)/./node_modules/mammoth/lib/zipfile.js\");\n\nexports.openZip = openZip;\n\nvar readFile = promises.promisify(fs.readFile);\n\nfunction openZip(options) {\n    if (options.path) {\n        return readFile(options.path).then(zipfile.openArrayBuffer);\n    } else if (options.buffer) {\n        return promises.resolve(zipfile.openArrayBuffer(options.buffer));\n    } else if (options.file) {\n        return promises.resolve(options.file);\n    } else {\n        return promises.reject(new Error(\"Could not find file in options\"));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvdW56aXAuanMiLCJtYXBwaW5ncyI6IkFBQUEsU0FBUyxtQkFBTyxDQUFDLGNBQUk7O0FBRXJCLGVBQWUsbUJBQU8sQ0FBQyxnRUFBWTtBQUNuQyxjQUFjLG1CQUFPLENBQUMsOERBQVc7O0FBRWpDLGVBQWU7O0FBRWY7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXHVuemlwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBmcyA9IHJlcXVpcmUoXCJmc1wiKTtcblxudmFyIHByb21pc2VzID0gcmVxdWlyZShcIi4vcHJvbWlzZXNcIik7XG52YXIgemlwZmlsZSA9IHJlcXVpcmUoXCIuL3ppcGZpbGVcIik7XG5cbmV4cG9ydHMub3BlblppcCA9IG9wZW5aaXA7XG5cbnZhciByZWFkRmlsZSA9IHByb21pc2VzLnByb21pc2lmeShmcy5yZWFkRmlsZSk7XG5cbmZ1bmN0aW9uIG9wZW5aaXAob3B0aW9ucykge1xuICAgIGlmIChvcHRpb25zLnBhdGgpIHtcbiAgICAgICAgcmV0dXJuIHJlYWRGaWxlKG9wdGlvbnMucGF0aCkudGhlbih6aXBmaWxlLm9wZW5BcnJheUJ1ZmZlcik7XG4gICAgfSBlbHNlIGlmIChvcHRpb25zLmJ1ZmZlcikge1xuICAgICAgICByZXR1cm4gcHJvbWlzZXMucmVzb2x2ZSh6aXBmaWxlLm9wZW5BcnJheUJ1ZmZlcihvcHRpb25zLmJ1ZmZlcikpO1xuICAgIH0gZWxzZSBpZiAob3B0aW9ucy5maWxlKSB7XG4gICAgICAgIHJldHVybiBwcm9taXNlcy5yZXNvbHZlKG9wdGlvbnMuZmlsZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHByb21pc2VzLnJlamVjdChuZXcgRXJyb3IoXCJDb3VsZCBub3QgZmluZCBmaWxlIGluIG9wdGlvbnNcIikpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/unzip.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/writers/html-writer.js":
/*!*********************************************************!*\
  !*** ./node_modules/mammoth/lib/writers/html-writer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nexports.writer = writer;\n\nfunction writer(options) {\n    options = options || {};\n    if (options.prettyPrint) {\n        return prettyWriter();\n    } else {\n        return simpleWriter();\n    }\n}\n\n\nvar indentedElements = {\n    div: true,\n    p: true,\n    ul: true,\n    li: true\n};\n\n\nfunction prettyWriter() {\n    var indentationLevel = 0;\n    var indentation = \"  \";\n    var stack = [];\n    var start = true;\n    var inText = false;\n\n    var writer = simpleWriter();\n\n    function open(tagName, attributes) {\n        if (indentedElements[tagName]) {\n            indent();\n        }\n        stack.push(tagName);\n        writer.open(tagName, attributes);\n        if (indentedElements[tagName]) {\n            indentationLevel++;\n        }\n        start = false;\n    }\n\n    function close(tagName) {\n        if (indentedElements[tagName]) {\n            indentationLevel--;\n            indent();\n        }\n        stack.pop();\n        writer.close(tagName);\n    }\n\n    function text(value) {\n        startText();\n        var text = isInPre() ? value : value.replace(\"\\n\", \"\\n\" + indentation);\n        writer.text(text);\n    }\n\n    function selfClosing(tagName, attributes) {\n        indent();\n        writer.selfClosing(tagName, attributes);\n    }\n\n    function insideIndentedElement() {\n        return stack.length === 0 || indentedElements[stack[stack.length - 1]];\n    }\n\n    function startText() {\n        if (!inText) {\n            indent();\n            inText = true;\n        }\n    }\n\n    function indent() {\n        inText = false;\n        if (!start && insideIndentedElement() && !isInPre()) {\n            writer._append(\"\\n\");\n            for (var i = 0; i < indentationLevel; i++) {\n                writer._append(indentation);\n            }\n        }\n    }\n\n    function isInPre() {\n        return _.some(stack, function(tagName) {\n            return tagName === \"pre\";\n        });\n    }\n\n    return {\n        asString: writer.asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing\n    };\n}\n\n\nfunction simpleWriter() {\n    var fragments = [];\n\n    function open(tagName, attributes) {\n        var attributeString = generateAttributeString(attributes);\n        fragments.push(\"<\" + tagName + attributeString + \">\");\n    }\n\n    function close(tagName) {\n        fragments.push(\"</\" + tagName + \">\");\n    }\n\n    function selfClosing(tagName, attributes) {\n        var attributeString = generateAttributeString(attributes);\n        fragments.push(\"<\" + tagName + attributeString + \" />\");\n    }\n\n    function generateAttributeString(attributes) {\n        return _.map(attributes, function(value, key) {\n            return \" \" + key + '=\"' + escapeHtmlAttribute(value) + '\"';\n        }).join(\"\");\n    }\n\n    function text(value) {\n        fragments.push(escapeHtmlText(value));\n    }\n\n    function append(html) {\n        fragments.push(html);\n    }\n\n    function asString() {\n        return fragments.join(\"\");\n    }\n\n    return {\n        asString: asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing,\n        _append: append\n    };\n}\n\nfunction escapeHtmlText(value) {\n    return value\n        .replace(/&/g, '&amp;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n\nfunction escapeHtmlAttribute(value) {\n    return value\n        .replace(/&/g, '&amp;')\n        .replace(/\"/g, '&quot;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/writers/html-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/writers/index.js":
/*!***************************************************!*\
  !*** ./node_modules/mammoth/lib/writers/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var htmlWriter = __webpack_require__(/*! ./html-writer */ \"(ssr)/./node_modules/mammoth/lib/writers/html-writer.js\");\nvar markdownWriter = __webpack_require__(/*! ./markdown-writer */ \"(ssr)/./node_modules/mammoth/lib/writers/markdown-writer.js\");\n\nexports.writer = writer;\n\n\nfunction writer(options) {\n    options = options || {};\n    if (options.outputFormat === \"markdown\") {\n        return markdownWriter.writer();\n    } else {\n        return htmlWriter.writer(options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIvd3JpdGVycy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpQkFBaUIsbUJBQU8sQ0FBQyw4RUFBZTtBQUN4QyxxQkFBcUIsbUJBQU8sQ0FBQyxzRkFBbUI7O0FBRWhELGNBQWM7OztBQUdkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXOmhueebrlxcVmlkZW9SdW4tMTJcXFZpZGVvUnVuLTEyXFxub3ZlbC10by12aWRlby1haVxcbm9kZV9tb2R1bGVzXFxtYW1tb3RoXFxsaWJcXHdyaXRlcnNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBodG1sV3JpdGVyID0gcmVxdWlyZShcIi4vaHRtbC13cml0ZXJcIik7XG52YXIgbWFya2Rvd25Xcml0ZXIgPSByZXF1aXJlKFwiLi9tYXJrZG93bi13cml0ZXJcIik7XG5cbmV4cG9ydHMud3JpdGVyID0gd3JpdGVyO1xuXG5cbmZ1bmN0aW9uIHdyaXRlcihvcHRpb25zKSB7XG4gICAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gICAgaWYgKG9wdGlvbnMub3V0cHV0Rm9ybWF0ID09PSBcIm1hcmtkb3duXCIpIHtcbiAgICAgICAgcmV0dXJuIG1hcmtkb3duV3JpdGVyLndyaXRlcigpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBodG1sV3JpdGVyLndyaXRlcihvcHRpb25zKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/writers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/writers/markdown-writer.js":
/*!*************************************************************!*\
  !*** ./node_modules/mammoth/lib/writers/markdown-writer.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\n\nfunction symmetricMarkdownElement(end) {\n    return markdownElement(end, end);\n}\n\nfunction markdownElement(start, end) {\n    return function() {\n        return {start: start, end: end};\n    };\n}\n\nfunction markdownLink(attributes) {\n    var href = attributes.href || \"\";\n    if (href) {\n        return {\n            start: \"[\",\n            end: \"](\" + href + \")\",\n            anchorPosition: \"before\"\n        };\n    } else {\n        return {};\n    }\n}\n\nfunction markdownImage(attributes) {\n    var src = attributes.src || \"\";\n    var altText = attributes.alt || \"\";\n    if (src || altText) {\n        return {start: \"![\" + altText + \"](\" + src + \")\"};\n    } else {\n        return {};\n    }\n}\n\nfunction markdownList(options) {\n    return function(attributes, list) {\n        return {\n            start: list ? \"\\n\" : \"\",\n            end: list ? \"\" : \"\\n\",\n            list: {\n                isOrdered: options.isOrdered,\n                indent: list ? list.indent + 1 : 0,\n                count: 0\n            }\n        };\n    };\n}\n\nfunction markdownListItem(attributes, list, listItem) {\n    list = list || {indent: 0, isOrdered: false, count: 0};\n    list.count++;\n    listItem.hasClosed = false;\n    \n    var bullet = list.isOrdered ? list.count + \".\" : \"-\";\n    var start = repeatString(\"\\t\", list.indent) + bullet + \" \";\n        \n    return {\n        start: start,\n        end: function() {\n            if (!listItem.hasClosed) {\n                listItem.hasClosed = true;\n                return \"\\n\";\n            }\n        }\n    };\n}\n\nvar htmlToMarkdown = {\n    \"p\": markdownElement(\"\", \"\\n\\n\"),\n    \"br\": markdownElement(\"\", \"  \\n\"),\n    \"ul\": markdownList({isOrdered: false}),\n    \"ol\": markdownList({isOrdered: true}),\n    \"li\": markdownListItem,\n    \"strong\": symmetricMarkdownElement(\"__\"),\n    \"em\": symmetricMarkdownElement(\"*\"),\n    \"a\": markdownLink,\n    \"img\": markdownImage\n};\n\n(function() {\n    for (var i = 1; i <= 6; i++) {\n        htmlToMarkdown[\"h\" + i] = markdownElement(repeatString(\"#\", i) + \" \", \"\\n\\n\");\n    }\n})();\n\nfunction repeatString(value, count) {\n    return new Array(count + 1).join(value);\n}\n\nfunction markdownWriter() {\n    var fragments = [];\n    var elementStack = [];\n    var list = null;\n    var listItem = {};\n    \n    function open(tagName, attributes) {\n        attributes = attributes || {};\n        \n        var createElement = htmlToMarkdown[tagName] || function() {\n            return {};\n        };\n        var element = createElement(attributes, list, listItem);\n        elementStack.push({end: element.end, list: list});\n        \n        if (element.list) {\n            list = element.list;\n        }\n        \n        var anchorBeforeStart = element.anchorPosition === \"before\";\n        if (anchorBeforeStart) {\n            writeAnchor(attributes);\n        }\n\n        fragments.push(element.start || \"\");\n        if (!anchorBeforeStart) {\n            writeAnchor(attributes);\n        }\n    }\n    \n    function writeAnchor(attributes) {\n        if (attributes.id) {\n            fragments.push('<a id=\"' + attributes.id + '\"></a>');\n        }\n    }\n    \n    function close(tagName) {\n        var element = elementStack.pop();\n        list = element.list;\n        var end = _.isFunction(element.end) ? element.end() : element.end;\n        fragments.push(end || \"\");\n    }\n    \n    function selfClosing(tagName, attributes) {\n        open(tagName, attributes);\n        close(tagName);\n    }\n    \n    function text(value) {\n        fragments.push(escapeMarkdown(value));\n    }\n    \n    function asString() {\n        return fragments.join(\"\");\n    }\n\n    return {\n        asString: asString,\n        open: open,\n        close: close,\n        text: text,\n        selfClosing: selfClosing\n    };\n}\n\nexports.writer = markdownWriter;\n\nfunction escapeMarkdown(value) {\n    return value\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/([\\`\\*_\\{\\}\\[\\]\\(\\)\\#\\+\\-\\.\\!])/g, '\\\\$1');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/writers/markdown-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/xml/index.js":
/*!***********************************************!*\
  !*** ./node_modules/mammoth/lib/xml/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var nodes = __webpack_require__(/*! ./nodes */ \"(ssr)/./node_modules/mammoth/lib/xml/nodes.js\");\n\nexports.Element = nodes.Element;\nexports.element = nodes.element;\nexports.emptyElement = nodes.emptyElement;\nexports.text = nodes.text;\nexports.readString = __webpack_require__(/*! ./reader */ \"(ssr)/./node_modules/mammoth/lib/xml/reader.js\").readString;\nexports.writeString = __webpack_require__(/*! ./writer */ \"(ssr)/./node_modules/mammoth/lib/xml/writer.js\").writeString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIveG1sL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQyw4REFBUzs7QUFFN0IsZUFBZTtBQUNmLGVBQWU7QUFDZixvQkFBb0I7QUFDcEIsWUFBWTtBQUNaLHFIQUFtRDtBQUNuRCx1SEFBcUQiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFx4bWxcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBub2RlcyA9IHJlcXVpcmUoXCIuL25vZGVzXCIpO1xuXG5leHBvcnRzLkVsZW1lbnQgPSBub2Rlcy5FbGVtZW50O1xuZXhwb3J0cy5lbGVtZW50ID0gbm9kZXMuZWxlbWVudDtcbmV4cG9ydHMuZW1wdHlFbGVtZW50ID0gbm9kZXMuZW1wdHlFbGVtZW50O1xuZXhwb3J0cy50ZXh0ID0gbm9kZXMudGV4dDtcbmV4cG9ydHMucmVhZFN0cmluZyA9IHJlcXVpcmUoXCIuL3JlYWRlclwiKS5yZWFkU3RyaW5nO1xuZXhwb3J0cy53cml0ZVN0cmluZyA9IHJlcXVpcmUoXCIuL3dyaXRlclwiKS53cml0ZVN0cmluZztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/xml/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/xml/nodes.js":
/*!***********************************************!*\
  !*** ./node_modules/mammoth/lib/xml/nodes.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\n\nexports.Element = Element;\nexports.element = function(name, attributes, children) {\n    return new Element(name, attributes, children);\n};\nexports.text = function(value) {\n    return {\n        type: \"text\",\n        value: value\n    };\n};\n\n\nvar emptyElement = exports.emptyElement = {\n    first: function() {\n        return null;\n    },\n    firstOrEmpty: function() {\n        return emptyElement;\n    },\n    attributes: {},\n    children: []\n};\n\nfunction Element(name, attributes, children) {\n    this.type = \"element\";\n    this.name = name;\n    this.attributes = attributes || {};\n    this.children = children || [];\n}\n\nElement.prototype.first = function(name) {\n    return _.find(this.children, function(child) {\n        return child.name === name;\n    });\n};\n\nElement.prototype.firstOrEmpty = function(name) {\n    return this.first(name) || emptyElement;\n};\n\nElement.prototype.getElementsByTagName = function(name) {\n    var elements = _.filter(this.children, function(child) {\n        return child.name === name;\n    });\n    return toElementList(elements);\n};\n\nElement.prototype.text = function() {\n    if (this.children.length === 0) {\n        return \"\";\n    } else if (this.children.length !== 1 || this.children[0].type !== \"text\") {\n        throw new Error(\"Not implemented\");\n    }\n    return this.children[0].value;\n};\n\nvar elementListPrototype = {\n    getElementsByTagName: function(name) {\n        return toElementList(_.flatten(this.map(function(element) {\n            return element.getElementsByTagName(name);\n        }, true)));\n    }\n};\n\nfunction toElementList(array) {\n    return _.extend(array, elementListPrototype);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/xml/nodes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/xml/reader.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/xml/reader.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var promises = __webpack_require__(/*! ../promises */ \"(ssr)/./node_modules/mammoth/lib/promises.js\");\nvar _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\n\nvar xmldom = __webpack_require__(/*! ./xmldom */ \"(ssr)/./node_modules/mammoth/lib/xml/xmldom.js\");\nvar nodes = __webpack_require__(/*! ./nodes */ \"(ssr)/./node_modules/mammoth/lib/xml/nodes.js\");\nvar Element = nodes.Element;\n\nexports.readString = readString;\n\nvar Node = xmldom.Node;\n\nfunction readString(xmlString, namespaceMap) {\n    namespaceMap = namespaceMap || {};\n\n    try {\n        var document = xmldom.parseFromString(xmlString, \"text/xml\");\n    } catch (error) {\n        return promises.reject(error);\n    }\n\n    if (document.documentElement.tagName === \"parsererror\") {\n        return promises.resolve(new Error(document.documentElement.textContent));\n    }\n\n    function convertNode(node) {\n        switch (node.nodeType) {\n        case Node.ELEMENT_NODE:\n            return convertElement(node);\n        case Node.TEXT_NODE:\n            return nodes.text(node.nodeValue);\n        }\n    }\n\n    function convertElement(element) {\n        var convertedName = convertName(element);\n\n        var convertedChildren = [];\n        _.forEach(element.childNodes, function(childNode) {\n            var convertedNode = convertNode(childNode);\n            if (convertedNode) {\n                convertedChildren.push(convertedNode);\n            }\n        });\n\n        var convertedAttributes = {};\n        _.forEach(element.attributes, function(attribute) {\n            convertedAttributes[convertName(attribute)] = attribute.value;\n        });\n\n        return new Element(convertedName, convertedAttributes, convertedChildren);\n    }\n\n    function convertName(node) {\n        if (node.namespaceURI) {\n            var mappedPrefix = namespaceMap[node.namespaceURI];\n            var prefix;\n            if (mappedPrefix) {\n                prefix = mappedPrefix + \":\";\n            } else {\n                prefix = \"{\" + node.namespaceURI + \"}\";\n            }\n            return prefix + node.localName;\n        } else {\n            return node.localName;\n        }\n    }\n\n    return promises.resolve(convertNode(document.documentElement));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/xml/reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/xml/writer.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/xml/writer.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var _ = __webpack_require__(/*! underscore */ \"(ssr)/./node_modules/underscore/modules/index-all.js\");\nvar xmlbuilder = __webpack_require__(/*! xmlbuilder */ \"(ssr)/./node_modules/xmlbuilder/lib/index.js\");\n\n\nexports.writeString = writeString;\n\n\nfunction writeString(root, namespaces) {\n    var uriToPrefix = _.invert(namespaces);\n    \n    var nodeWriters = {\n        element: writeElement,\n        text: writeTextNode\n    };\n\n    function writeNode(builder, node) {\n        return nodeWriters[node.type](builder, node);\n    }\n\n    function writeElement(builder, element) {\n        var elementBuilder = builder.element(mapElementName(element.name), element.attributes);\n        element.children.forEach(function(child) {\n            writeNode(elementBuilder, child);\n        });\n    }\n    \n    function mapElementName(name) {\n        var longFormMatch = /^\\{(.*)\\}(.*)$/.exec(name);\n        if (longFormMatch) {\n            var prefix = uriToPrefix[longFormMatch[1]];\n            return prefix + (prefix === \"\" ? \"\" : \":\") + longFormMatch[2];\n        } else {\n            return name;\n        }\n    }\n    \n    function writeDocument(root) {\n        var builder = xmlbuilder\n            .create(mapElementName(root.name), {\n                version: '1.0',\n                encoding: 'UTF-8',\n                standalone: true\n            });\n        \n        _.forEach(namespaces, function(uri, prefix) {\n            var key = \"xmlns\" + (prefix === \"\" ? \"\" : \":\" + prefix);\n            builder.attribute(key, uri);\n        });\n        \n        root.children.forEach(function(child) {\n            writeNode(builder, child);\n        });\n        return builder.end();\n    }\n\n    return writeDocument(root);\n}\n\nfunction writeTextNode(builder, node) {\n    builder.text(node.value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIveG1sL3dyaXRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxRQUFRLG1CQUFPLENBQUMsd0VBQVk7QUFDNUIsaUJBQWlCLG1CQUFPLENBQUMsZ0VBQVk7OztBQUdyQyxtQkFBbUI7OztBQUduQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLE1BQU07QUFDdEM7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxub2RlX21vZHVsZXNcXG1hbW1vdGhcXGxpYlxceG1sXFx3cml0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF8gPSByZXF1aXJlKFwidW5kZXJzY29yZVwiKTtcbnZhciB4bWxidWlsZGVyID0gcmVxdWlyZShcInhtbGJ1aWxkZXJcIik7XG5cblxuZXhwb3J0cy53cml0ZVN0cmluZyA9IHdyaXRlU3RyaW5nO1xuXG5cbmZ1bmN0aW9uIHdyaXRlU3RyaW5nKHJvb3QsIG5hbWVzcGFjZXMpIHtcbiAgICB2YXIgdXJpVG9QcmVmaXggPSBfLmludmVydChuYW1lc3BhY2VzKTtcbiAgICBcbiAgICB2YXIgbm9kZVdyaXRlcnMgPSB7XG4gICAgICAgIGVsZW1lbnQ6IHdyaXRlRWxlbWVudCxcbiAgICAgICAgdGV4dDogd3JpdGVUZXh0Tm9kZVxuICAgIH07XG5cbiAgICBmdW5jdGlvbiB3cml0ZU5vZGUoYnVpbGRlciwgbm9kZSkge1xuICAgICAgICByZXR1cm4gbm9kZVdyaXRlcnNbbm9kZS50eXBlXShidWlsZGVyLCBub2RlKTtcbiAgICB9XG5cbiAgICBmdW5jdGlvbiB3cml0ZUVsZW1lbnQoYnVpbGRlciwgZWxlbWVudCkge1xuICAgICAgICB2YXIgZWxlbWVudEJ1aWxkZXIgPSBidWlsZGVyLmVsZW1lbnQobWFwRWxlbWVudE5hbWUoZWxlbWVudC5uYW1lKSwgZWxlbWVudC5hdHRyaWJ1dGVzKTtcbiAgICAgICAgZWxlbWVudC5jaGlsZHJlbi5mb3JFYWNoKGZ1bmN0aW9uKGNoaWxkKSB7XG4gICAgICAgICAgICB3cml0ZU5vZGUoZWxlbWVudEJ1aWxkZXIsIGNoaWxkKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIGZ1bmN0aW9uIG1hcEVsZW1lbnROYW1lKG5hbWUpIHtcbiAgICAgICAgdmFyIGxvbmdGb3JtTWF0Y2ggPSAvXlxceyguKilcXH0oLiopJC8uZXhlYyhuYW1lKTtcbiAgICAgICAgaWYgKGxvbmdGb3JtTWF0Y2gpIHtcbiAgICAgICAgICAgIHZhciBwcmVmaXggPSB1cmlUb1ByZWZpeFtsb25nRm9ybU1hdGNoWzFdXTtcbiAgICAgICAgICAgIHJldHVybiBwcmVmaXggKyAocHJlZml4ID09PSBcIlwiID8gXCJcIiA6IFwiOlwiKSArIGxvbmdGb3JtTWF0Y2hbMl07XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gbmFtZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBcbiAgICBmdW5jdGlvbiB3cml0ZURvY3VtZW50KHJvb3QpIHtcbiAgICAgICAgdmFyIGJ1aWxkZXIgPSB4bWxidWlsZGVyXG4gICAgICAgICAgICAuY3JlYXRlKG1hcEVsZW1lbnROYW1lKHJvb3QubmFtZSksIHtcbiAgICAgICAgICAgICAgICB2ZXJzaW9uOiAnMS4wJyxcbiAgICAgICAgICAgICAgICBlbmNvZGluZzogJ1VURi04JyxcbiAgICAgICAgICAgICAgICBzdGFuZGFsb25lOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgXG4gICAgICAgIF8uZm9yRWFjaChuYW1lc3BhY2VzLCBmdW5jdGlvbih1cmksIHByZWZpeCkge1xuICAgICAgICAgICAgdmFyIGtleSA9IFwieG1sbnNcIiArIChwcmVmaXggPT09IFwiXCIgPyBcIlwiIDogXCI6XCIgKyBwcmVmaXgpO1xuICAgICAgICAgICAgYnVpbGRlci5hdHRyaWJ1dGUoa2V5LCB1cmkpO1xuICAgICAgICB9KTtcbiAgICAgICAgXG4gICAgICAgIHJvb3QuY2hpbGRyZW4uZm9yRWFjaChmdW5jdGlvbihjaGlsZCkge1xuICAgICAgICAgICAgd3JpdGVOb2RlKGJ1aWxkZXIsIGNoaWxkKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBidWlsZGVyLmVuZCgpO1xuICAgIH1cblxuICAgIHJldHVybiB3cml0ZURvY3VtZW50KHJvb3QpO1xufVxuXG5mdW5jdGlvbiB3cml0ZVRleHROb2RlKGJ1aWxkZXIsIG5vZGUpIHtcbiAgICBidWlsZGVyLnRleHQobm9kZS52YWx1ZSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/xml/writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/xml/xmldom.js":
/*!************************************************!*\
  !*** ./node_modules/mammoth/lib/xml/xmldom.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var xmldom = __webpack_require__(/*! @xmldom/xmldom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/index.js\");\nvar dom = __webpack_require__(/*! @xmldom/xmldom/lib/dom */ \"(ssr)/./node_modules/@xmldom/xmldom/lib/dom.js\");\n\nfunction parseFromString(string) {\n    var error = null;\n\n    var domParser = new xmldom.DOMParser({\n        errorHandler: function(level, message) {\n            error = {level: level, message: message};\n        }\n    });\n\n    var document = domParser.parseFromString(string);\n\n    if (error === null) {\n        return document;\n    } else {\n        throw new Error(error.level + \": \" + error.message);\n    }\n}\n\nexports.parseFromString = parseFromString;\nexports.Node = dom.Node;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFtbW90aC9saWIveG1sL3htbGRvbS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsd0VBQWdCO0FBQ3JDLFVBQVUsbUJBQU8sQ0FBQyw4RUFBd0I7O0FBRTFDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLEtBQUs7O0FBRUw7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUEsdUJBQXVCO0FBQ3ZCLFlBQVkiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXG5vZGVfbW9kdWxlc1xcbWFtbW90aFxcbGliXFx4bWxcXHhtbGRvbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgeG1sZG9tID0gcmVxdWlyZShcIkB4bWxkb20veG1sZG9tXCIpO1xudmFyIGRvbSA9IHJlcXVpcmUoXCJAeG1sZG9tL3htbGRvbS9saWIvZG9tXCIpO1xuXG5mdW5jdGlvbiBwYXJzZUZyb21TdHJpbmcoc3RyaW5nKSB7XG4gICAgdmFyIGVycm9yID0gbnVsbDtcblxuICAgIHZhciBkb21QYXJzZXIgPSBuZXcgeG1sZG9tLkRPTVBhcnNlcih7XG4gICAgICAgIGVycm9ySGFuZGxlcjogZnVuY3Rpb24obGV2ZWwsIG1lc3NhZ2UpIHtcbiAgICAgICAgICAgIGVycm9yID0ge2xldmVsOiBsZXZlbCwgbWVzc2FnZTogbWVzc2FnZX07XG4gICAgICAgIH1cbiAgICB9KTtcblxuICAgIHZhciBkb2N1bWVudCA9IGRvbVBhcnNlci5wYXJzZUZyb21TdHJpbmcoc3RyaW5nKTtcblxuICAgIGlmIChlcnJvciA9PT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gZG9jdW1lbnQ7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLmxldmVsICsgXCI6IFwiICsgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxufVxuXG5leHBvcnRzLnBhcnNlRnJvbVN0cmluZyA9IHBhcnNlRnJvbVN0cmluZztcbmV4cG9ydHMuTm9kZSA9IGRvbS5Ob2RlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/xml/xmldom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mammoth/lib/zipfile.js":
/*!*********************************************!*\
  !*** ./node_modules/mammoth/lib/zipfile.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var base64js = __webpack_require__(/*! base64-js */ \"(ssr)/./node_modules/base64-js/index.js\");\nvar JSZip = __webpack_require__(/*! jszip */ \"(ssr)/./node_modules/jszip/lib/index.js\");\n\nexports.openArrayBuffer = openArrayBuffer;\nexports.splitPath = splitPath;\nexports.joinPath = joinPath;\n\nfunction openArrayBuffer(arrayBuffer) {\n    return JSZip.loadAsync(arrayBuffer).then(function(zipFile) {\n        function exists(name) {\n            return zipFile.file(name) !== null;\n        }\n\n        function read(name, encoding) {\n            return zipFile.file(name).async(\"uint8array\").then(function(array) {\n                if (encoding === \"base64\") {\n                    return base64js.fromByteArray(array);\n                } else if (encoding) {\n                    var decoder = new TextDecoder(encoding);\n                    return decoder.decode(array);\n                } else {\n                    return array;\n                }\n            });\n        }\n\n        function write(name, contents) {\n            zipFile.file(name, contents);\n        }\n\n        function toArrayBuffer() {\n            return zipFile.generateAsync({type: \"arraybuffer\"});\n        }\n\n        return {\n            exists: exists,\n            read: read,\n            write: write,\n            toArrayBuffer: toArrayBuffer\n        };\n    });\n}\n\nfunction splitPath(path) {\n    var lastIndex = path.lastIndexOf(\"/\");\n    if (lastIndex === -1) {\n        return {dirname: \"\", basename: path};\n    } else {\n        return {\n            dirname: path.substring(0, lastIndex),\n            basename: path.substring(lastIndex + 1)\n        };\n    }\n}\n\nfunction joinPath() {\n    var nonEmptyPaths = Array.prototype.filter.call(arguments, function(path) {\n        return path;\n    });\n\n    var relevantPaths = [];\n\n    nonEmptyPaths.forEach(function(path) {\n        if (/^\\//.test(path)) {\n            relevantPaths = [path];\n        } else {\n            relevantPaths.push(path);\n        }\n    });\n\n    return relevantPaths.join(\"/\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mammoth/lib/zipfile.js\n");

/***/ })

};
;