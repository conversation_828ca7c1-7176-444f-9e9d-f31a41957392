const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoPolling() {
  try {
    console.log('🧪 测试豆包轮询API响应格式...');
    
    // 获取豆包配置
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: { 
        provider: 'doubao',
        supportsVideo: true,
        enabled: true
      }
    });
    
    if (!doubaoConfig) {
      console.log('❌ 未找到豆包视频配置');
      return;
    }
    
    // 使用一个已知的任务ID进行测试（从之前的错误日志中获取）
    const testTaskId = 'cgt-20250623185952-k5wqj'; // 从之前的测试中获取
    
    console.log(`📡 查询豆包任务状态: ${testTaskId}`);
    const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${testTaskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${doubaoConfig.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ 轮询请求失败: ${response.status} ${errorText}`);
      return;
    }
    
    const result = await response.json();
    console.log('📋 豆包轮询响应格式:');
    console.log(JSON.stringify(result, null, 2));
    
    // 分析响应格式
    console.log('\n🔍 轮询响应分析:');
    console.log('- result.data:', !!result.data);
    console.log('- result.data?.status:', result.data?.status);
    console.log('- result.data?.video_url:', result.data?.video_url);
    console.log('- result.data?.output_url:', result.data?.output_url);
    console.log('- result.data?.message:', result.data?.message);
    console.log('- result.data?.error:', result.data?.error);
    
    // 检查状态处理逻辑
    if (result.data) {
      const status = result.data.status;
      console.log(`✅ 任务状态: ${status}`);
      
      if (status === 'SUCCEEDED' || status === 'SUCCESS') {
        const videoUrl = result.data.video_url || result.data.output_url;
        console.log(`🎬 视频URL: ${videoUrl || '未找到'}`);
      } else if (status === 'FAILED' || status === 'ERROR') {
        const errorMessage = result.data.message || result.data.error || '任务失败';
        console.log(`❌ 失败原因: ${errorMessage}`);
      } else {
        console.log(`⏳ 任务进行中: ${status}`);
      }
    } else {
      console.log('❌ 响应中没有data字段');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoPolling();
