// 直接测试API端点

async function testAPI() {
  try {
    console.log('🔍 直接测试API端点...');
    
    const response = await fetch('http://localhost:3001/api/ai/analyze-detailed-plot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        episodeId: 'cmc60kmf30001vm3cw6lvt6ki',
        episodeContent: '测试内容',
        customPrompt: '测试提示'
      })
    });

    console.log('📊 响应状态:', response.status);

    const text = await response.text();
    console.log('📊 响应内容:', text);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testAPI();
