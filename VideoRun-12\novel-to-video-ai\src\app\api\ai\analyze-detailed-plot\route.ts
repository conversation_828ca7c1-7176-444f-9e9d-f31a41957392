import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { createAIClient, AIServiceError, handleAIRequest } from '@/lib/ai'
import { generateStoryVideoPrompt, createCharacterDNA, createSceneDNA } from '@/utils/storyVideoPromptGenerator'

// GET - 获取已保存的详细剧情信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId参数' },
        { status: 400 }
      )
    }

    // 查找已保存的详细剧情信息
    const plotInfo = await prisma.plotInfo.findUnique({
      where: { episodeId }
    })

    if (!plotInfo) {
      return NextResponse.json({
        success: false,
        error: '未找到详细剧情信息'
      })
    }

    // 检查是否有任何详细信息
    const hasDetailedInfo = plotInfo.detailedCharacters ||
                           plotInfo.detailedScenes ||
                           plotInfo.plotSequences ||
                           plotInfo.emotionalArc ||
                           plotInfo.generatedPrompt

    if (!hasDetailedInfo) {
      return NextResponse.json({
        success: false,
        error: '未找到详细剧情信息'
      })
    }

    // 解析保存的数据
    let characters = []
    let scenes = []
    let plotSequences = []

    try {
      characters = plotInfo.detailedCharacters ? JSON.parse(plotInfo.detailedCharacters) : []
    } catch (error) {
      console.error('解析detailedCharacters失败:', error)
      characters = []
    }

    try {
      scenes = plotInfo.detailedScenes ? JSON.parse(plotInfo.detailedScenes) : []
    } catch (error) {
      console.error('解析detailedScenes失败:', error)
      scenes = []
    }

    try {
      plotSequences = plotInfo.plotSequences ? JSON.parse(plotInfo.plotSequences) : []
    } catch (error) {
      console.error('解析plotSequences失败:', error)
      plotSequences = []
    }

    const analysisResult = {
      characters,
      scenes,
      plotSequences,
      emotionalArc: plotInfo.emotionalArc || '',
      generatedPrompt: plotInfo.generatedPrompt || ''
    }


    return NextResponse.json({
      success: true,
      data: analysisResult,
      message: '详细剧情信息获取成功'
    })
  } catch (error) {
    console.error('获取详细剧情信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取失败，请重试' },
      { status: 500 }
    )
  }
}

// POST - 分析详细剧情信息
export async function POST(request: NextRequest) {
  try {
    console.log('📥 收到POST请求')
    const body = await request.json()
    console.log('📥 请求体:', { episodeId: body.episodeId, hasContent: !!body.episodeContent })
    const { episodeId, episodeContent, customPrompt } = body

    if (!episodeId || !episodeContent) {
      console.log('❌ 缺少必要参数')
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型' },
        { status: 400 }
      )
    }

    // 分析详细剧情信息（传入episodeId用于一致性约束）
    let analysisResult
    try {
      console.log('开始调用analyzeDetailedPlotInfo...')
      analysisResult = await analyzeDetailedPlotInfo(aiConfig, episodeContent, customPrompt, episodeId)
      console.log('分析完成，结果:', analysisResult)
    } catch (error) {
      console.error('分析过程出错:', error)
      console.error('错误详情:', error.stack)
      throw error
    }

    // 保存详细剧情信息到数据库
    try {
      await saveDetailedPlotInfo(episodeId, analysisResult)
      console.log('保存完成')
    } catch (error) {
      console.error('保存过程出错:', error)
      // 继续执行，不影响返回结果
    }

    return NextResponse.json({
      success: true,
      data: analysisResult,
      message: '剧情信息分析完成'
    })
  } catch (error) {
    console.error('分析详细剧情失败:', error)
    // 返回更详细的错误信息用于调试
    const errorMessage = error instanceof Error ? error.message : '分析失败，请重试'
    return NextResponse.json(
      { success: false, error: errorMessage, details: error },
      { status: 500 }
    )
  }
}

// 分析详细剧情信息（增强版：集成角色一致性约束）
async function analyzeDetailedPlotInfo(aiConfig: any, episodeContent: string, customPrompt?: string, episodeId?: string) {
  try {
    console.log('开始详细剧情分析（一致性增强版）...')
    console.log('AI配置:', { provider: aiConfig.provider, model: aiConfig.model, hasApiKey: !!aiConfig.apiKey })

    const aiClient = createAIClient(aiConfig)

    // 1. 获取项目角色库（用于一致性约束）
    let projectCharacters: any[] = []
    if (episodeId) {
      try {
        const episode = await prisma.episode.findUnique({
          where: { id: episodeId },
          include: {
            project: {
              include: {
                characters: true
              }
            }
          }
        })
        projectCharacters = episode?.project?.characters || []
        console.log('获取项目角色库，角色数量:', projectCharacters.length)
      } catch (error) {
        console.error('获取项目角色库失败:', error)
      }
    }

    // 强制进行真实的AI分析，不使用任何测试数据
    console.log('正在调用真实AI API进行分析...')

    // 2. 分析角色信息（增强版：包含一致性约束）
    console.log('开始分析角色信息（一致性增强）...')
    const charactersResult = await handleAIRequest(() =>
      analyzeCharactersWithConsistency(aiClient, episodeContent, projectCharacters)
    )
    console.log('角色分析完成，结果数量:', charactersResult.length)

    // 2. 分析场景信息
    console.log('开始分析场景信息...')
    const scenesResult = await handleAIRequest(() =>
      analyzeScenes(aiClient, episodeContent)
    )
    console.log('场景分析完成，结果数量:', scenesResult.length)

    // 3. 分析情节序列
    console.log('开始分析情节序列...')
    const plotSequencesResult = await handleAIRequest(() =>
      analyzePlotSequences(aiClient, episodeContent)
    )
    console.log('情节序列分析完成，结果数量:', plotSequencesResult.length)

    // 4. 分析情感弧线
    console.log('开始分析情感弧线...')
    const emotionalArcResult = await handleAIRequest(() =>
      analyzeEmotionalArc(aiClient, episodeContent)
    )
    console.log('情感弧线分析完成，长度:', emotionalArcResult.length)

    // 5. 创建角色DNA档案（增强版：包含一致性信息）
    const characterDNAs = charactersResult.map((char: any) => {
      const dna = createCharacterDNA({
        id: char.name?.toLowerCase().replace(/\s+/g, '_') || 'character',
        name: char.name || '角色',
        identity: char.identity,
        facial: char.appearance,
        personality: char.personality,
        clothing: char.clothing
      })

      // 添加一致性信息
      if (char.consistencyInfo) {
        dna.consistencyInfo = char.consistencyInfo
      }

      return dna
    })

    // 6. 创建场景DNA档案
    const sceneDNAs = scenesResult.map((scene: any) =>
      createSceneDNA({
        id: scene.location?.toLowerCase().replace(/\s+/g, '_') || 'scene',
        name: scene.location || '场景',
        location: scene.location,
        description: scene.description,
        atmosphere: scene.atmosphere
      })
    )

    // 7. 生成专业视频提示词
    const generatedPrompt = generateStoryVideoPrompt({
      characters: characterDNAs,
      scene: sceneDNAs[0] || createSceneDNA({ id: 'default', name: '默认场景' }),
      timeOfDay: 'midday',
      plotSequences: plotSequencesResult,
      emotionalArc: emotionalArcResult,
      customEnhancement: customPrompt,
      style: 'cinematic',
      quality: 'high'
    })

    return {
      characters: charactersResult,
      scenes: scenesResult,
      plotSequences: plotSequencesResult,
      emotionalArc: emotionalArcResult,
      characterDNAs,
      sceneDNAs,
      generatedPrompt
    }
  } catch (error) {
    console.error('详细剧情分析失败:', error)
    throw error
  }
}

// 分析角色信息
async function analyzeCharacters(aiClient: any, episodeContent: string) {
  const prompt = `你是一个专业的小说分析师。请仔细分析以下剧集内容，提取其中出现的所有角色信息。

剧集内容：
${episodeContent}

请按照以下JSON格式返回角色信息：
{
  "characters": [
    {
      "name": "角色姓名",
      "identity": "身份职业和基本信息",
      "appearance": "外貌特征描述",
      "personality": "性格特点",
      "clothing": "服装描述",
      "role": "在本集中的作用"
    }
  ]
}

要求：
1. 只提取在本集中实际出现的角色
2. 外貌描述要具体详细
3. 性格分析要基于文本中的行为表现
4. 如果文中没有明确描述某项信息，可以合理推断但要标注
5. 返回标准JSON格式`

  const response = await aiClient.callAPI(prompt, 4000)
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }
    const result = JSON.parse(jsonMatch[0])
    return result.characters || []
  } catch (error) {
    console.error('解析角色分析响应失败:', error)
    return []
  }
}

// 分析场景信息
async function analyzeScenes(aiClient: any, episodeContent: string) {
  const prompt = `你是一个专业的场景设计师。请仔细分析以下剧集内容，提取其中的场景信息。

剧集内容：
${episodeContent}

请按照以下JSON格式返回场景信息：
{
  "scenes": [
    {
      "location": "场景地点名称",
      "description": "场景详细描述",
      "atmosphere": "氛围和环境特点",
      "timeOfDay": "时间段",
      "lighting": "光线条件",
      "keyElements": "关键环境元素"
    }
  ]
}

要求：
1. 提取所有在本集中出现的场景
2. 描述要具体详细，包含空间布局
3. 氛围描述要体现情感色彩
4. 注意光线和时间的描述
5. 返回标准JSON格式`

  const response = await aiClient.callAPI(prompt, 3000)
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }
    const result = JSON.parse(jsonMatch[0])
    return result.scenes || []
  } catch (error) {
    console.error('解析场景分析响应失败:', error)
    return []
  }
}

// 分析情节序列
async function analyzePlotSequences(aiClient: any, episodeContent: string) {
  const prompt = `你是一个专业的剧本分析师。请仔细分析以下剧集内容，将情节分解为可视化的动作序列。

剧集内容：
${episodeContent}

请按照以下JSON格式返回情节序列：
{
  "plotSequences": [
    {
      "sequenceId": "序列编号",
      "action": "具体动作描述",
      "emotion": "情感状态",
      "duration": "预估时长",
      "keyMoments": ["关键时刻1", "关键时刻2"],
      "visualElements": "视觉表现要点"
    }
  ]
}

要求：
1. 将剧情分解为3-5个主要序列
2. 每个序列要有明确的动作和情感
3. 关键时刻要具体可视化
4. 考虑视频制作的可行性
5. 返回标准JSON格式`

  const response = await aiClient.callAPI(prompt, 3000)
  
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }
    const result = JSON.parse(jsonMatch[0])
    return result.plotSequences || []
  } catch (error) {
    console.error('解析情节序列响应失败:', error)
    return []
  }
}

// 分析情感弧线
async function analyzeEmotionalArc(aiClient: any, episodeContent: string) {
  const prompt = `你是一个专业的情感分析师。请仔细分析以下剧集内容，总结整体的情感发展弧线。

剧集内容：
${episodeContent}

请分析并返回情感弧线描述，格式如下：
开始情感 → 发展过程 → 转折点 → 高潮情感 → 结束情感

要求：
1. 分析主要角色的情感变化
2. 识别关键的情感转折点
3. 描述要简洁明了
4. 适合视频表现的情感节奏
5. 用箭头连接不同阶段

示例：平静专注 → 遇到困难 → 灵感突现 → 兴奋创作 → 满足成就`

  const response = await aiClient.callAPI(prompt, 2000)
  
  // 直接返回文本，不需要JSON解析
  return response.trim()
}

// 分析角色信息（增强版：包含一致性约束）
async function analyzeCharactersWithConsistency(aiClient: any, episodeContent: string, projectCharacters: any[]) {
  // 构建包含项目角色库信息的提示词
  let characterConstraints = ''
  if (projectCharacters.length > 0) {
    characterConstraints = `\n\n已知项目角色库：\n${projectCharacters.map(char => {
      const details = []
      if (char.identity) details.push(`身份: ${char.identity}`)
      if (char.facial) details.push(`外貌: ${char.facial}`)
      if (char.personality) details.push(`性格: ${char.personality}`)
      if (char.consistencyScore) details.push(`一致性评分: ${char.consistencyScore}`)

      return `- ${char.name}: ${details.join(', ')}`
    }).join('\n')}\n\n请在分析时：
1. 如果剧集中的角色与已知角色匹配，请保持一致性约束
2. 标注角色是否为已知角色（isKnownCharacter: true/false）
3. 如果是已知角色，提供一致性匹配度评分（consistencyMatch: 0.0-1.0）
4. 指出与已知角色的差异（如有）`
  }

  const prompt = `你是一个专业的小说分析师和角色一致性专家。请仔细分析以下剧集内容，提取其中出现的所有角色信息，并进行一致性约束分析。

剧集内容：
${episodeContent}${characterConstraints}

请按照以下JSON格式返回角色信息：
{
  "characters": [
    {
      "name": "角色姓名",
      "identity": "身份职业和基本信息",
      "appearance": "外貌特征描述",
      "personality": "性格特点",
      "clothing": "服装描述",
      "role": "在本集中的作用",
      "isKnownCharacter": true/false,
      "consistencyInfo": {
        "matchedCharacterId": "匹配的已知角色ID（如果有）",
        "consistencyMatch": 0.95,
        "differences": ["与已知角色的差异列表"],
        "consistencyConstraints": "一致性约束建议"
      }
    }
  ]
}

要求：
1. 只提取在本集中实际出现的角色
2. 外貌描述要具体详细，符合一致性要求
3. 性格分析要基于文本中的行为表现
4. 如果是已知角色，必须保持与项目角色库的一致性
5. 提供详细的一致性分析和约束建议
6. 返回标准JSON格式`

  const response = await aiClient.callAPI(prompt, 5000)

  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }
    const result = JSON.parse(jsonMatch[0])
    return result.characters || []
  } catch (error) {
    console.error('解析角色分析响应失败:', error)
    // 降级到原始分析方法
    return await analyzeCharacters(aiClient, episodeContent)
  }
}

// 保存详细剧情信息到数据库 - 使用与基础剧情信息相同的存储方式
async function saveDetailedPlotInfo(episodeId: string, analysisResult: any) {
  try {
    // 使用upsert操作，与基础剧情分析保持一致
    await prisma.plotInfo.upsert({
      where: { episodeId },
      update: {
        // 更新详细信息字段
        detailedCharacters: JSON.stringify(analysisResult.characters || []),
        detailedScenes: JSON.stringify(analysisResult.scenes || []),
        plotSequences: JSON.stringify(analysisResult.plotSequences || []),
        emotionalArc: analysisResult.emotionalArc || '',
        generatedPrompt: analysisResult.generatedPrompt || '',
        updatedAt: new Date()
      },
      create: {
        episodeId,
        // 基础字段，保持与基础剧情分析的兼容性
        characters: '[]',
        scenes: '[]',
        events: '[]',
        // 详细信息字段
        detailedCharacters: JSON.stringify(analysisResult.characters || []),
        detailedScenes: JSON.stringify(analysisResult.scenes || []),
        plotSequences: JSON.stringify(analysisResult.plotSequences || []),
        emotionalArc: analysisResult.emotionalArc || '',
        generatedPrompt: analysisResult.generatedPrompt || ''
      }
    })
  } catch (error) {
    console.error('保存详细剧情信息失败:', error)
    // 不抛出错误，避免影响主流程
  }
}
