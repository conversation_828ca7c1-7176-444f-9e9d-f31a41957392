const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 最新的豆包模型配置
const LATEST_DOUBAO_CONFIG = {
  // 最新图像生成模型
  image: {
    name: 'Doubao-Seedream-3.0-t2i',
    endpoint: 'ep-20250626132353-nlrtf',
    description: '豆包文本到图像生成模型 - 最新版本 (2025-06-26)',
    updateDate: '2025-06-26'
  },
  
  // 视频生成模型保持不变
  video: [
    {
      name: 'Doubao-Seedance-1.0-pro',
      endpoint: 'ep-20250624192235-zttm6',
      description: '豆包专业版视频生成模型'
    },
    {
      name: 'Doubao-Seedance-1.0-lite-i2v',
      endpoint: 'ep-20250624195026-qjsmk',
      description: '豆包图片到视频转换模型'
    },
    {
      name: 'Doubao-Seedance-1.0-lite-t2v',
      endpoint: 'ep-20250624192345-5ccwj',
      description: '豆包文本到视频生成模型'
    }
  ]
};

// Volcengine API配置
const VOLCENGINE_CONFIG = {
  accessKeyId: 'AKLTOTgwMzIxY2VlNDIxNDNiMWFlZjAzOWY1OTU3ZDIwOWE',
  secretAccessKey: 'WXpBMU9ETmtNamxoTmpZMk5EQTNZV0psWVdZelpqRXlOREkxT1dJM01ETQ==',
  baseUrl: 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'
};

async function updateLatestDoubaoConfig() {
  try {
    console.log('🎨 更新豆包最新模型配置');
    console.log('='.repeat(50));
    console.log(`📅 更新日期: ${new Date().toLocaleString()}`);
    console.log(`🆕 最新端点: ${LATEST_DOUBAO_CONFIG.image.endpoint}`);
    console.log('');
    
    // 1. 更新图像生成模型配置
    console.log('🔄 更新图像生成模型配置...');
    
    const imageModel = LATEST_DOUBAO_CONFIG.image;
    
    // 查找现有的图像生成配置
    let imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true
      }
    });
    
    if (imageConfig) {
      // 更新现有配置
      imageConfig = await prisma.aIConfig.update({
        where: { id: imageConfig.id },
        data: {
          model: imageModel.endpoint,
          name: imageModel.name,
          description: imageModel.description,
          apiKey: VOLCENGINE_CONFIG.accessKeyId,
          enabled: true,
          status: 'connected',
          updatedAt: new Date()
        }
      });
      console.log(`✅ 已更新图像模型: ${imageModel.name}`);
      console.log(`   旧端点: ${imageConfig.model !== imageModel.endpoint ? '已更新' : '无变化'}`);
      console.log(`   新端点: ${imageModel.endpoint}`);
    } else {
      // 创建新的图像生成配置
      imageConfig = await prisma.aIConfig.create({
        data: {
          provider: 'doubao',
          apiKey: VOLCENGINE_CONFIG.accessKeyId,
          model: imageModel.endpoint,
          name: imageModel.name,
          description: imageModel.description,
          enabled: true,
          supportsVideo: false,
          supportsImage: true,
          supportsImageToVideo: false,
          supportsTTS: false,
          temperature: 0.7,
          maxTokens: 4000,
          topP: 0.9,
          status: 'connected'
        }
      });
      console.log(`✅ 已创建图像模型: ${imageModel.name}`);
      console.log(`   端点: ${imageModel.endpoint}`);
    }
    
    // 2. 验证视频模型配置
    console.log('\n🎬 验证视频模型配置...');
    
    for (const videoModel of LATEST_DOUBAO_CONFIG.video) {
      const videoConfig = await prisma.aIConfig.findFirst({
        where: {
          provider: 'doubao',
          model: videoModel.endpoint
        }
      });
      
      if (videoConfig) {
        console.log(`✅ 视频模型已存在: ${videoModel.name} (${videoModel.endpoint})`);
      } else {
        console.log(`⚠️ 视频模型不存在: ${videoModel.name} (${videoModel.endpoint})`);
      }
    }
    
    // 3. 显示当前所有豆包配置
    console.log('\n📊 当前豆包模型配置:');
    console.log('-'.repeat(50));
    
    const allConfigs = await prisma.aIConfig.findMany({
      where: { provider: 'doubao' },
      orderBy: { updatedAt: 'desc' }
    });
    
    allConfigs.forEach((config, index) => {
      const capabilities = [];
      if (config.supportsImage) capabilities.push('图像生成');
      if (config.supportsVideo) capabilities.push('视频生成');
      if (config.supportsImageToVideo) capabilities.push('图生视频');
      if (config.supportsTTS) capabilities.push('TTS语音');
      
      console.log(`${index + 1}. ${config.name}`);
      console.log(`   端点: ${config.model}`);
      console.log(`   功能: ${capabilities.join(', ')}`);
      console.log(`   状态: ${config.enabled ? '✅ 启用' : '❌ 禁用'}`);
      console.log(`   更新: ${config.updatedAt.toLocaleString()}`);
      console.log('');
    });
    
    // 4. 生成配置摘要
    console.log('📋 配置更新摘要:');
    console.log('='.repeat(50));
    console.log(`🎨 最新图像模型: ${imageModel.name}`);
    console.log(`   端点ID: ${imageModel.endpoint}`);
    console.log(`   更新时间: ${imageModel.updateDate}`);
    console.log(`   配置状态: ✅ 已更新并启用`);
    
    console.log('\n🎬 视频模型状态:');
    LATEST_DOUBAO_CONFIG.video.forEach(model => {
      console.log(`   - ${model.name}: ${model.endpoint}`);
    });
    
    console.log('\n🎉 豆包模型配置更新完成！');
    console.log('💡 现在可以使用最新的Doubao-Seedream-3.0-t2i模型进行图像生成。');
    
    return {
      imageConfig,
      allConfigs,
      latestEndpoint: imageModel.endpoint
    };
    
  } catch (error) {
    console.error('❌ 更新配置失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 测试最新配置
async function testLatestConfig() {
  try {
    console.log('\n🧪 测试最新配置...');
    
    // 验证数据库中的配置
    const latestImageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    });
    
    if (latestImageConfig) {
      console.log('✅ 最新图像配置验证成功');
      console.log(`   模型: ${latestImageConfig.name}`);
      console.log(`   端点: ${latestImageConfig.model}`);
      console.log(`   状态: ${latestImageConfig.status}`);
      
      // 检查是否是最新端点
      if (latestImageConfig.model === LATEST_DOUBAO_CONFIG.image.endpoint) {
        console.log('✅ 端点配置正确，使用最新版本');
      } else {
        console.log('⚠️ 端点配置可能不是最新版本');
      }
    } else {
      console.log('❌ 未找到可用的图像生成配置');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  console.log('🎨 豆包最新模型配置更新工具');
  console.log(`🆕 目标端点: ${LATEST_DOUBAO_CONFIG.image.endpoint}`);
  console.log('开始时间:', new Date().toLocaleString());
  console.log('');
  
  try {
    const result = await updateLatestDoubaoConfig();
    await testLatestConfig();
    
    console.log('\n✅ 所有操作完成！');
    console.log(`🎯 最新端点已配置: ${result.latestEndpoint}`);
    
  } catch (error) {
    console.error('\n❌ 操作失败:', error.message);
  }
}

main();
