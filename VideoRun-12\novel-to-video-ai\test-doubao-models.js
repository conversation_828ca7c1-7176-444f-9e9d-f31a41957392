// 测试正确的豆包模型名称
async function testDoubaoModels() {
  try {
    console.log('🧪 测试正确的豆包模型名称...');
    
    // 正确的豆包模型列表
    const correctModels = [
      {
        model: 'doubao-pro-4k',
        name: '豆包 Pro 4K',
        description: '专业版4K上下文模型，适合复杂推理任务'
      },
      {
        model: 'doubao-pro-32k', 
        name: '豆包 Pro 32K',
        description: '专业版32K长文本模型，适合长文档处理'
      },
      {
        model: 'doubao-lite-4k',
        name: '豆包 Lite 4K',
        description: '轻量版4K模型，高性价比选择'
      },
      {
        model: 'doubao-lite-32k',
        name: '豆包 Lite 32K',
        description: '轻量版32K模型，适合长文本处理'
      },
      {
        model: 'doubao-lite-128k',
        name: '豆包 Lite 128K',
        description: '轻量版128K超长文本模型，适合超长文档'
      }
    ];
    
    console.log('\n📋 正确的豆包模型列表:');
    correctModels.forEach((model, index) => {
      console.log(`   ${index + 1}. ${model.name} (${model.model})`);
      console.log(`      ${model.description}`);
    });
    
    // 测试每个模型的配置保存
    console.log('\n💾 测试模型配置保存...');
    
    for (const model of correctModels) {
      console.log(`\n🔧 配置模型: ${model.name}`);
      
      try {
        const response = await fetch('http://localhost:3000/api/models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            provider: 'doubao',
            model: model.model,
            name: model.name,
            description: model.description,
            apiKey: 'test-placeholder-key',
            enabled: false // 先不启用，避免测试时出错
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log(`✅ ${model.name} 配置保存成功`);
        } else {
          const errorText = await response.text();
          console.log(`❌ ${model.name} 配置保存失败:`, errorText);
        }
      } catch (error) {
        console.log(`❌ ${model.name} 配置过程中发生错误:`, error.message);
      }
    }
    
    // 验证模型列表
    console.log('\n📋 验证模型列表...');
    
    const listResponse = await fetch('http://localhost:3000/api/models');
    if (listResponse.ok) {
      const listResult = await listResponse.json();
      const doubaoModels = listResult.data.filter(model => model.provider === 'doubao');
      
      console.log(`✅ 找到豆包模型: ${doubaoModels.length} 个`);
      
      // 检查是否包含所有正确的模型
      const foundModels = doubaoModels.map(m => m.model);
      const expectedModels = correctModels.map(m => m.model);
      
      expectedModels.forEach(expectedModel => {
        if (foundModels.includes(expectedModel)) {
          console.log(`   ✅ ${expectedModel}: 已配置`);
        } else {
          console.log(`   ❌ ${expectedModel}: 未找到`);
        }
      });
      
      // 检查是否有错误的模型名称
      const incorrectModels = foundModels.filter(model => 
        !expectedModels.includes(model) && 
        model.startsWith('doubao-') &&
        !model.includes('seedance') // 忽略之前的测试模型
      );
      
      if (incorrectModels.length > 0) {
        console.log('\n⚠️ 发现可能不正确的模型名称:');
        incorrectModels.forEach(model => {
          console.log(`   - ${model}`);
        });
      }
    }
    
    // 显示使用建议
    console.log('\n💡 使用建议:');
    console.log('   📝 基础文本处理: doubao-lite-4k');
    console.log('   📚 长文本分析: doubao-lite-32k 或 doubao-pro-32k');
    console.log('   📖 超长文档: doubao-lite-128k');
    console.log('   🧠 复杂推理: doubao-pro-4k 或 doubao-pro-32k');
    console.log('   💰 成本考虑: lite系列更经济，pro系列质量更高');
    
    // 显示API密钥配置提示
    console.log('\n🔑 API密钥配置提示:');
    console.log('   1. 访问火山引擎控制台: https://console.volcengine.com/');
    console.log('   2. 进入火山方舟大模型服务平台');
    console.log('   3. 在模型广场中选择豆包模型');
    console.log('   4. 创建接入点并获取API密钥');
    console.log('   5. 将API密钥配置到系统中');
    console.log('   6. 测试连接确保配置正确');
    
    console.log('\n🎉 豆包模型名称测试完成！');
    console.log('\n📝 总结:');
    console.log('   ✅ 已更新为正确的豆包模型名称');
    console.log('   ✅ 支持5种不同规格的豆包模型');
    console.log('   ✅ 涵盖从轻量到专业的全系列模型');
    console.log('   ✅ 支持从4K到128K的不同上下文长度');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoModels();
