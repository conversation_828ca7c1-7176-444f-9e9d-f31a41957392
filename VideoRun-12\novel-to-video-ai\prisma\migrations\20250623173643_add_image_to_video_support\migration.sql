-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_ai_configs" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "provider" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "supportsVideo" BOOLEAN NOT NULL DEFAULT false,
    "supportsImage" BOOLEAN NOT NULL DEFAULT false,
    "supportsImageToVideo" BOOLEAN NOT NULL DEFAULT false,
    "temperature" REAL NOT NULL DEFAULT 0.7,
    "maxTokens" INTEGER NOT NULL DEFAULT 4000,
    "topP" REAL NOT NULL DEFAULT 0.9,
    "status" TEXT NOT NULL DEFAULT 'disconnected',
    "lastTest" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_ai_configs" ("apiKey", "createdAt", "description", "enabled", "id", "lastTest", "maxTokens", "model", "name", "provider", "status", "supportsVideo", "temperature", "topP", "updatedAt") SELECT "apiKey", "createdAt", "description", "enabled", "id", "lastTest", "maxTokens", "model", "name", "provider", "status", "supportsVideo", "temperature", "topP", "updatedAt" FROM "ai_configs";
DROP TABLE "ai_configs";
ALTER TABLE "new_ai_configs" RENAME TO "ai_configs";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
