# 🎯 大模型提示词规范指南

## 1. 基本原则

### A. 清晰性原则
```
❌ 不好的例子：画个人
✅ 好的例子：生成一个25岁亚洲女性的正面肖像，现代服装，白色背景
```

### B. 具体性原则
```
❌ 模糊：漂亮的女孩
✅ 具体：oval face, large brown eyes, straight black hair, gentle smile, wearing white dress
```

### C. 结构化原则
```
✅ 推荐结构：
[主体描述] + [风格要求] + [技术参数] + [质量要求]
```

## 2. 文生图提示词规范

### A. 主体描述（Subject）
```
格式：[数量] + [年龄] + [性别] + [种族] + [职业/身份]

示例：
- one 25-year-old Asian woman, office worker
- a middle-aged European man, doctor
- young Chinese girl, student
```

### B. 外貌特征（Appearance）
```
脸部：face shape, eyes, nose, mouth, skin
身材：height, build, posture
发型：hair color, hair style, hair length

示例：
- oval face, large brown eyes, straight nose, gentle smile
- tall and slim build, confident posture
- long black hair, side-parted, flowing
```

### C. 服装风格（Clothing）
```
格式：[颜色] + [材质] + [款式] + [配饰]

示例：
- wearing white cotton shirt, dark blue jeans, black leather shoes
- elegant red silk dress, pearl necklace, high heels
- casual hoodie, denim jacket, sneakers
```

### D. 姿势和动作（Pose & Action）
```
静态姿势：
- standing straight, arms at sides
- sitting cross-legged
- three-quarter view, looking at camera

动态动作：
- walking forward
- raising hand
- turning head slightly
```

### E. 环境背景（Background）
```
简单背景：
- white background
- solid color background
- gradient background

复杂场景：
- modern office interior
- outdoor park setting
- studio lighting setup
```

### F. 艺术风格（Style）
```
写实风格：
- photorealistic, hyperrealistic
- professional photography
- studio portrait

动漫风格：
- anime style, manga style
- cel shading, clean lines
- Japanese animation style

艺术风格：
- oil painting style
- watercolor style
- digital art, concept art
```

### G. 技术参数（Technical）
```
画质：
- high resolution, 4K, 8K
- sharp focus, detailed
- masterpiece, best quality

光照：
- soft lighting, studio lighting
- natural lighting, golden hour
- dramatic lighting, rim lighting

相机参数：
- shot on Canon EOS R5
- 85mm lens, f/1.4
- shallow depth of field
```

## 3. 文本生成提示词规范

### A. 角色设定
```
格式：你是一个[专业身份]，具有[专业技能]，擅长[具体领域]

示例：
你是一个资深的小说分析专家，具有20年的文学研究经验，擅长角色心理分析和情节结构解析。
```

### B. 任务描述
```
格式：请[动作动词] + [具体对象] + [详细要求]

示例：
请分析以下小说文本中的主要角色，提取每个角色的姓名、外貌特征、性格特点和在故事中的作用。
```

### C. 输出格式
```
明确指定：
- 返回JSON格式
- 使用表格形式
- 分点列举
- 包含特定字段

示例：
请按照以下JSON格式返回：
{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": "外貌描述",
      "personality": "性格特点"
    }
  ]
}
```

### D. 约束条件
```
长度限制：
- 每个描述不超过100字
- 总结控制在500字以内

内容要求：
- 必须基于原文内容
- 不能添加原文没有的信息
- 保持客观中性的语调
```

## 4. 提示词优化技巧

### A. 权重控制
```
强调重要元素：
- (important element:1.2)  # 轻微强调
- ((very important:1.5))   # 中等强调
- (((critical element:2.0))) # 强烈强调

降低权重：
- [less important:0.8]     # 降低重要性
```

### B. 负面提示词（Negative Prompts）
```
常用负面词：
- blurry, low quality, pixelated
- deformed, distorted, ugly
- extra limbs, missing parts
- watermark, signature, text
```

### C. 分步骤描述
```
复杂任务分解：
第一步：分析文本结构
第二步：识别主要角色
第三步：提取角色特征
第四步：整理输出格式
```

## 5. 不同模型的特点

### A. 通义万相（图像生成）
```
偏好：
- 中英文混合提示词
- 详细的视觉描述
- 明确的风格指定

示例：
高质量角色设计图，anime style，一个20岁的中国女孩，long black hair，wearing white dress，clean white background，masterpiece，best quality
```

### B. 通义千问（文本生成）
```
偏好：
- 中文提示词
- 结构化的任务描述
- 明确的输出要求

示例：
你是一个专业的小说分析师。请仔细分析以下文本，按照指定的JSON格式提取角色信息。要求：1）准确提取角色姓名 2）详细描述外貌特征 3）分析性格特点。
```

### C. DeepSeek（推理分析）
```
偏好：
- 逻辑清晰的问题描述
- 分步骤的思考过程
- 具体的分析要求

示例：
请按照以下步骤分析小说角色：
1. 首先识别文本中出现的所有人物
2. 然后分析每个人物的描述信息
3. 最后整理成结构化的数据格式
```

## 6. 实用提示词模板

### A. 角色分析模板
```
你是一个{专业身份}，请分析以下{内容类型}。

任务要求：
1. {具体要求1}
2. {具体要求2}
3. {具体要求3}

输出格式：{指定格式}

注意事项：
- {约束条件1}
- {约束条件2}

分析内容：{实际内容}
```

### B. 图像生成模板
```
{画质要求}, {风格描述}, {主体描述}, {外貌特征}, {服装描述}, {姿势动作}, {背景环境}, {光照效果}, {技术参数}

负面提示词：{不想要的元素}
```

## 7. 常见错误避免

### ❌ 避免的表达
```
- 模糊词汇：好看的、漂亮的、帅气的
- 主观判断：最好的、完美的、理想的
- 矛盾描述：同时要求写实和卡通风格
- 过于复杂：一个提示词包含太多要求
```

### ✅ 推荐的表达
```
- 具体描述：oval face, gentle smile, confident posture
- 客观特征：25 years old, 170cm tall, black hair
- 一致风格：anime style, clean lines, bright colors
- 分层描述：主体 + 风格 + 技术 + 质量
```

## 8. 测试和优化

### A. 迭代优化流程
```
1. 基础提示词 → 测试效果
2. 添加细节描述 → 再次测试
3. 调整权重和顺序 → 优化结果
4. 添加负面提示词 → 最终调优
```

### B. 效果评估标准
```
- 准确性：是否符合描述要求
- 一致性：多次生成是否稳定
- 质量：输出质量是否达标
- 效率：是否能快速得到满意结果
```

## 💡 总结要点

1. **结构化**：使用清晰的层次结构组织提示词
2. **具体化**：避免模糊词汇，使用具体的描述
3. **专业化**：使用领域专业术语提高准确性
4. **标准化**：遵循模型偏好的格式和风格
5. **迭代化**：通过测试和调整不断优化效果

掌握这些规范，您就能更好地与AI模型"对话"，获得更准确、更满意的结果！🎯

---

## 附录：专业术语词典

### 图像生成常用英文术语
```
人物特征：
- facial features: 面部特征
- body proportions: 身体比例
- character design: 角色设计
- three-quarter view: 四分之三视角
- profile view: 侧面视角

艺术风格：
- photorealistic: 照片级写实
- hyperrealistic: 超写实
- cel shading: 卡通渲染
- concept art: 概念艺术
- character sheet: 角色设定图

技术参数：
- high resolution: 高分辨率
- sharp focus: 清晰对焦
- depth of field: 景深
- studio lighting: 棚拍光照
- soft lighting: 柔和光照
```

### 中文描述转英文对照表
```
年龄相关：
- 少女 → young girl, teenage girl
- 青年女性 → young woman
- 中年男性 → middle-aged man
- 老人 → elderly person

外貌特征：
- 瓜子脸 → oval face
- 圆脸 → round face
- 方脸 → square face
- 大眼睛 → large eyes
- 小眼睛 → small eyes
- 高鼻梁 → high nose bridge
- 薄嘴唇 → thin lips
- 厚嘴唇 → full lips

发型：
- 长发 → long hair
- 短发 → short hair
- 卷发 → curly hair
- 直发 → straight hair
- 马尾辫 → ponytail
- 双马尾 → twin tails

服装：
- 校服 → school uniform
- 西装 → business suit
- 连衣裙 → dress
- 牛仔裤 → jeans
- 运动装 → sportswear
```
