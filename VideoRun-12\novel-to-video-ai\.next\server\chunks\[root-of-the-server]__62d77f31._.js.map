{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/video-segments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\n// GET - 获取视频片段状态\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const episodeId = searchParams.get('episodeId')\n    const storyVideoId = searchParams.get('storyVideoId')\n\n    if (!episodeId && !storyVideoId) {\n      return NextResponse.json(\n        { success: false, error: '缺少episodeId或storyVideoId参数' },\n        { status: 400 }\n      )\n    }\n\n    let whereClause: any = {}\n    if (storyVideoId) {\n      whereClause.storyVideoId = storyVideoId\n    } else if (episodeId) {\n      whereClause.episodeId = episodeId\n    }\n\n    // 获取视频片段信息\n    const segments = await prisma.videoSegment.findMany({\n      where: whereClause,\n      orderBy: { segmentIndex: 'asc' },\n      include: {\n        storyVideo: {\n          select: {\n            id: true,\n            status: true,\n            metadata: true\n          }\n        }\n      }\n    })\n\n    if (segments.length === 0) {\n      return NextResponse.json({\n        success: true,\n        data: {\n          segments: [],\n          summary: {\n            totalSegments: 0,\n            completedSegments: 0,\n            generatingSegments: 0,\n            failedSegments: 0,\n            pendingSegments: 0,\n            progress: 0,\n            overallStatus: 'none'\n          }\n        }\n      })\n    }\n\n    // 统计生成状态\n    const statusCounts = segments.reduce((acc, segment) => {\n      acc[segment.status] = (acc[segment.status] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    const totalSegments = segments.length\n    const completedSegments = statusCounts.completed || 0\n    const generatingSegments = statusCounts.generating || 0\n    const failedSegments = statusCounts.failed || 0\n    const pendingSegments = statusCounts.pending || 0\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        segments: segments.map(segment => ({\n          id: segment.id,\n          segmentIndex: segment.segmentIndex,\n          title: segment.title,\n          description: segment.description,\n          videoUrl: segment.videoUrl || '',\n          thumbnailUrl: segment.thumbnailUrl || '',\n          duration: segment.duration || 0,\n          status: segment.status,\n          segmentType: segment.segmentType,\n          createdAt: segment.createdAt.toISOString(),\n          updatedAt: segment.updatedAt.toISOString(),\n          metadata: segment.metadata\n        })),\n        summary: {\n          totalSegments,\n          completedSegments,\n          generatingSegments,\n          failedSegments,\n          pendingSegments,\n          progress: Math.round((completedSegments / totalSegments) * 100),\n          overallStatus: segments[0].storyVideo?.status || 'unknown'\n        }\n      }\n    })\n  } catch (error) {\n    console.error('获取视频片段失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取失败，请重试' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,eAAe,aAAa,GAAG,CAAC;QAEtC,IAAI,CAAC,aAAa,CAAC,cAAc;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA6B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,cAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,YAAY,YAAY,GAAG;QAC7B,OAAO,IAAI,WAAW;YACpB,YAAY,SAAS,GAAG;QAC1B;QAEA,WAAW;QACX,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAClD,OAAO;YACP,SAAS;gBAAE,cAAc;YAAM;YAC/B,SAAS;gBACP,YAAY;oBACV,QAAQ;wBACN,IAAI;wBACJ,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,UAAU,EAAE;oBACZ,SAAS;wBACP,eAAe;wBACf,mBAAmB;wBACnB,oBAAoB;wBACpB,gBAAgB;wBAChB,iBAAiB;wBACjB,UAAU;wBACV,eAAe;oBACjB;gBACF;YACF;QACF;QAEA,SAAS;QACT,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,KAAK;YACzC,GAAG,CAAC,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI;YACnD,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,oBAAoB,aAAa,SAAS,IAAI;QACpD,MAAM,qBAAqB,aAAa,UAAU,IAAI;QACtD,MAAM,iBAAiB,aAAa,MAAM,IAAI;QAC9C,MAAM,kBAAkB,aAAa,OAAO,IAAI;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;wBACjC,IAAI,QAAQ,EAAE;wBACd,cAAc,QAAQ,YAAY;wBAClC,OAAO,QAAQ,KAAK;wBACpB,aAAa,QAAQ,WAAW;wBAChC,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,cAAc,QAAQ,YAAY,IAAI;wBACtC,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,QAAQ,QAAQ,MAAM;wBACtB,aAAa,QAAQ,WAAW;wBAChC,WAAW,QAAQ,SAAS,CAAC,WAAW;wBACxC,WAAW,QAAQ,SAAS,CAAC,WAAW;wBACxC,UAAU,QAAQ,QAAQ;oBAC5B,CAAC;gBACD,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA,UAAU,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;oBAC3D,eAAe,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU;gBACnD;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}