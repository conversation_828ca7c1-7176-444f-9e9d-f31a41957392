# 🎬 "NovelVideo AI - 小说剧情视频生成系统" 使用指南

## 📛 系统命名

### **正式名称**
**"NovelVideo AI - 小说剧情视频生成系统"**

### **核心子系统命名**
- **角色DNA系统** (Character DNA System)
- **场景模板库** (Scene Template Library) 
- **剧情视频提示词生成器** (Story Video Prompt Generator)
- **视频一致性管理器** (Video Consistency Manager)

### **简称**
- **中文简称**：小说视频AI、剧情视频生成器
- **英文简称**：NovelVideo AI、NVAI

---

## 🚀 系统使用指南

### **第一步：项目初始化**

#### **1.1 上传小说文件**
```
操作路径：首页 → 文件上传区域
支持格式：.txt, .doc, .docx
操作方式：拖拽上传或点击选择文件
```

#### **1.2 AI自动分析**
```
触发按钮：「自动分析角色剧情」
处理内容：
- 提取角色信息（姓名、外貌、性格、身份）
- 分析剧情结构（章节、情节、冲突）
- 识别场景环境（地点、氛围、时间）
```

### **第二步：角色形象生成**

#### **2.1 查看角色信息**
```
位置：角色栏 → 点击具体角色
内容：角色详细信息页面
功能：查看AI提取的角色特征
```

#### **2.2 生成角色形象**
```
操作：点击「AI一键生成角色形象」按钮
选项：
- 选择AI模型（通义万相/智谱AI CogView）
- 输入增强提示词（可选）
- 预览提示词（新功能）
生成：正面、侧面、背面三视图
```

### **第三步：剧情分析处理**

#### **3.1 查看剧集列表**
```
位置：剧情栏
内容：按原小说章节顺序排列的剧集
操作：点击剧集查看详细内容
```

#### **3.2 提取剧情信息**
```
触发：剧集详情页 → 「查看剧情信息」按钮
分析维度：
- 出现角色（关联角色库）
- 场景信息（地点、环境、氛围）
- 事件要素（正常→冲突→升级结构）
```

#### **3.3 提取具体剧情信息（新功能）**
```
触发：剧集详情页 → 「提取具体剧情信息」按钮
功能：
- 打开剧情信息提取页面
- 显示角色DNA信息
- 显示场景模板信息
- 显示情节分解结果
- 提供「生成剧情视频」按钮
```

### **第四步：视频生成（核心功能）**

#### **4.1 剧情视频生成（新流程）**
```
操作路径：
剧集页面 → 「提取具体剧情信息」→ 剧情信息页面 → 「生成剧情视频」

功能特色：
1. 基于角色DNA系统确保人物一致性
2. 基于场景模板库确保环境一致性
3. 智能分析剧情情感弧线
4. 生成专业的视频提示词
5. 支持多种AI模型选择
```

#### **4.2 分段视频生成（推荐）**
```
操作：剧集页面 → 「提取具体剧情信息」→ 「生成剧情视频」
特色功能：
1. AI智能分段：自动将剧情分解为3-8个短视频片段
2. 并行生成：多个片段同时生成，减轻大模型压力
3. 实时监控：可查看每个片段的生成状态和进度
4. 独立管理：每个片段可单独播放、下载和管理
5. 快速体验：短片段生成更快，用户体验更佳
```

#### **4.3 标准视频生成**
```
操作：剧集页面 → 「生成视频脚本」按钮
流程：
1. 系统自动整合多维度信息
2. 智能选择最适合的AI模型
3. 生成视频脚本内容
4. 返回脚本文件
```

---

## 🎯 详细操作流程

### **新的分段剧情视频生成流程**
```
📚 上传小说
    ↓
🤖 AI自动分析角色剧情
    ↓
👥 查看角色信息 + 生成角色形象
    ↓
📖 查看剧集列表
    ↓
🔍 点击「提取具体剧情信息」
    ↓
📋 查看剧情信息提取页面
    - 角色DNA信息
    - 场景模板信息
    - 情节分解结果
    - 情感弧线分析
    ↓
🎬 点击「生成剧情视频」
    ↓
⚙️ 选择AI模型和参数
    ↓
🎞️ AI智能分段（3-8个短片段）
    ↓
🔄 并行生成多个视频片段
    ↓
📺 自动跳转到视频标签页
    ↓
👀 实时查看生成进度和结果
    - 📊 点击详细进度图标查看完整监控信息
    - ⏱️ 查看预计剩余时间和完成百分比
    - 🔍 监控每个片段的实时状态
    - 📈 查看生成统计和性能指标
    ↓
✅ 逐个播放和下载完成的片段
```

### **剧情信息提取页面内容**
```
页面结构：
┌─ 剧集标题和内容概览
├─ 角色DNA信息展示
│  ├─ 参与角色列表
│  ├─ 角色外貌特征
│  ├─ 角色一致性要素
│  └─ 角色行为特征
├─ 场景模板信息展示
│  ├─ 场景基础描述
│  ├─ 固定环境元素
│  ├─ 时间状态变化
│  └─ 氛围营造要素
├─ 情节分解结果展示
│  ├─ 关键情节点
│  ├─ 情感变化轨迹
│  ├─ 冲突发展过程
│  └─ 视觉表现要点
└─ 生成剧情视频按钮
```

### **视频片段管理页面**
```
页面结构：
┌─ 生成进度概览
│  ├─ 进度条显示（实时百分比）
│  ├─ 完成/生成中/失败统计
│  ├─ 预计剩余时间（智能估算）
│  └─ 总体状态监控
├─ 详细进度监控（点击📊图标查看）
│  ├─ 当前处理片段实时状态
│  ├─ 任务队列状态（总数/完成/处理中/等待/失败）
│  ├─ 生成统计（总文件大小/总时长/平均时长）
│  ├─ 片段详情列表（状态/时长/文件大小）
│  └─ API响应时间和轮询次数
├─ 视频片段网格展示
│  ├─ 片段缩略图
│  ├─ 生成状态标识（已完成✅/生成中🔄/等待中⏳/失败❌）
│  ├─ 片段信息（标题、描述、时长）
│  ├─ 片段类型（场景/动作/对话/转场）
│  └─ 操作按钮（播放/下载）
└─ 实时状态更新（每3秒自动刷新，智能轮询策略）
```

---

## 📊 如何查看详细的视频生成进度

### **快速查看方式**
```
1. 进入项目页面
2. 点击任意剧集的「查看视频」按钮
3. 在视频片段页面查看基础进度信息
```

### **详细进度监控**
```
操作步骤：
1. 在视频片段页面，点击右上角的「📊」（详细进度）图标
2. 系统会展开完整的进度监控面板

显示内容：
┌─ 总体进度
│  ├─ 进度百分比（0-100%）
│  ├─ 完成片段数 / 总片段数
│  ├─ 预计剩余时间（基于实际生成速度智能估算）
│  └─ 当前处理状态
├─ 正在处理的片段
│  ├─ 片段编号和标题
│  ├─ 开始处理时间
│  └─ 实时状态更新
├─ 任务队列状态
│  ├─ 总数：4个片段
│  ├─ 已完成：✅ 绿色数字
│  ├─ 生成中：🔄 蓝色数字
│  ├─ 等待中：⏳ 灰色数字
│  └─ 失败：❌ 红色数字
├─ 片段详情列表
│  ├─ 每个片段的详细状态
│  ├─ 生成时长和文件大小
│  ├─ API响应时间
│  └─ 错误信息（如有）
└─ 生成统计
   ├─ 总文件大小
   ├─ 总视频时长
   └─ 平均片段时长
```

### **实时更新机制**
```
更新频率：
- 初期：每3秒检查一次（快速响应）
- 后期：逐渐增加到每10秒（节省资源）

状态指示：
✅ 已完成 - 绿色，可播放和下载
🔄 生成中 - 蓝色，正在AI处理
⏳ 等待中 - 灰色，排队等待处理
❌ 失败 - 红色，生成失败需重试

自动刷新：
- 页面会自动更新，无需手动刷新
- 完成后会自动显示视频预览
- 支持后台生成，可以关闭页面稍后查看
```

### **性能优化说明**
```
串行生成策略（推荐）：
- 一个接一个生成片段，避免API压力
- 更快看到第一个结果（2-3分钟）
- 更稳定的生成质量和成功率
- 避免API限流和超时问题

生成顺序：
- 片段1 → 完成 → 片段2 → 完成 → 片段3 → 完成 → 片段4
- 每个片段完成后立即可以观看
- 总时间相同，但体验更好

智能轮询：
- 前10次轮询：3秒间隔（快速响应）
- 后续轮询：逐渐增加到10秒（节省资源）
- 自动重试：网络错误时自动重试

预计时间：
- 基于已完成片段的实际生成时间
- 动态调整预估算法
- 通常每个5秒片段需要2-5分钟生成时间
- 串行生成：第一个片段最快2分钟可看
```

---

## 🛠️ 核心功能特色

### **角色DNA系统**
```
目的：确保角色在所有视频中保持一致的外貌
特色：
- 自动提取角色核心特征
- 建立不变特征库（脸型、眼睛、发型等）
- 定义可变特征（表情、姿态、服装）
- 在视频生成时自动应用一致性约束
```

### **场景模板库**
```
目的：确保同一场景在不同时间保持环境一致
特色：
- 建立场景标准模板
- 定义固定元素（布局、家具、色彩等）
- 设置时间状态（不同时间的光线变化）
- 保持空间关系的逻辑一致性
```

### **剧情视频提示词生成器**
```
目的：生成高度符合小说剧情的专业视频提示词
特色：
- 整合角色DNA和场景模板信息
- 分析情感弧线和情节发展
- 构建分层次的提示词结构
- 强调一致性和连贯性要求
```

---

## 📋 使用建议

### **最佳实践**
```
1. 首次使用建议：
   - 选择较短的小说片段进行测试
   - 先生成角色形象确认效果
   - 使用「提取具体剧情信息」功能查看分析结果
   - 逐步熟悉各个功能模块

2. 质量优化技巧：
   - 确保角色信息提取准确完整
   - 检查场景描述的详细程度
   - 关注情感弧线的合理性
   - 选择合适的AI模型进行生成

3. 一致性保证：
   - 重点关注角色DNA信息的准确性
   - 确认场景模板的完整性
   - 在生成前预览提示词内容
   - 必要时使用增强提示词功能
```

### **系统优势**
```
✅ 人物一致性：同一角色在不同场景中保持外貌一致
✅ 场景一致性：同一场景在不同时间线上保持环境一致
✅ 剧情符合度：生成的视频高度符合小说剧情描述
✅ 制作效率：大幅提升小说改编视频的制作效率
✅ 专业质量：基于专业提示词规范生成高质量视频
✅ 分段生成：智能分解为短片段，减轻大模型压力
✅ 串行处理：一个接一个生成，更快看到结果，更稳定
✅ 实时监控：可视化进度跟踪，用户体验优秀
✅ 灵活管理：每个片段独立播放和下载
✅ 渐进体验：第一个片段2-3分钟即可观看，无需等待全部完成
```

这套"NovelVideo AI"系统为小说改编视频制作提供了完整的解决方案，让您能够轻松创造出高质量、高一致性的视频内容！🚀✨
