'use client'

import Link from 'next/link'
import {
  Sparkles,
  Upload,
  Users,
  Film,
  Video,
  ArrowRight,
  CheckCircle,
  Settings
} from 'lucide-react'
import Layout from '@/components/Layout'

export default function Home() {
  const steps = [
    {
      icon: Settings,
      title: '配置AI模型',
      description: '选择DeepSeek等大模型，配置API密钥',
      href: '/models'
    },
    {
      icon: Upload,
      title: '上传小说文件',
      description: '支持.txt、.docx格式，创建新项目并上传',
      href: '/projects/new'
    },
    {
      icon: Users,
      title: '自动提取角色',
      description: 'AI分析小说，提取角色的五官、身份、性格等信息',
      href: '/projects'
    },
    {
      icon: Film,
      title: '拆分剧集',
      description: '按章节自动拆分成独立剧集，提取剧情信息',
      href: '/projects'
    },
    {
      icon: Video,
      title: '生成视频脚本',
      description: '基于多维度信息，为每集生成视频脚本',
      href: '/projects'
    }
  ]

  return (
    <Layout>
      {/* 英雄区域 */}
      <div className="text-center">
        <div className="mx-auto max-w-2xl">
          <div className="flex justify-center mb-6">
            <Sparkles className="text-purple-600" size={64} />
          </div>
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl whitespace-nowrap">
            灵犀Ai——小说转视频神器
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            基于DeepSeek大模型的智能视频内容创作平台，将小说自动转换为结构化的视频脚本，
            提取角色信息、拆分剧集、生成多维度视频内容。
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Link
              href="/projects/new"
              className="rounded-md bg-purple-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600"
            >
              开始创作
            </Link>
            <Link
              href="/models"
              className="text-sm font-semibold leading-6 text-gray-900"
            >
              配置模型 <span aria-hidden="true">→</span>
            </Link>
          </div>
        </div>
      </div>

      {/* 生成流程 */}
      <div className="mt-24">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            AI视频生成流程
          </h2>
          <p className="mt-4 text-lg leading-8 text-gray-600">
            简单5步，将您的小说转换为专业的视频脚本
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-5xl">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-5">
            {steps.map((step, index) => (
              <div key={step.title} className="relative">
                <div className="flex flex-col items-center text-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-purple-100 text-purple-600">
                    <step.icon size={24} />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold text-gray-900">
                    {step.title}
                  </h3>
                  <p className="mt-2 text-sm text-gray-600">
                    {step.description}
                  </p>
                  <Link
                    href={step.href}
                    className="mt-3 text-sm font-medium text-purple-600 hover:text-purple-500"
                  >
                    开始 →
                  </Link>
                </div>

                {/* 连接线 */}
                {index < steps.length - 1 && (
                  <div className="absolute top-8 left-full hidden w-full lg:block">
                    <ArrowRight className="mx-auto text-gray-300" size={20} />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 项目特性 */}
      <div className="mt-24">
        <div className="mx-auto max-w-7xl">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              项目特性
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              基于先进的AI技术，为您提供专业的小说转视频解决方案
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-5xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    智能角色提取
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  自动识别小说中的所有角色，提取五官、身份、外貌、性格、隐线等多维度信息
                </p>
              </div>

              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    章节智能拆分
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  按原小说章节自动拆分成独立剧集，保持故事的完整性和连贯性
                </p>
              </div>

              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    剧情信息提取
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  提取每集的本集人物、场景信息、事件三要素（正常-矛盾冲突-升级事件）
                </p>
              </div>

              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    多格式支持
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  支持.txt、.docx等多种文件格式，最大支持50MB文件上传
                </p>
              </div>

              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    DeepSeek模型
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  集成DeepSeek等先进大模型，提供Chat、R1、V3、Code多种模型选择
                </p>
              </div>

              <div className="relative p-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center">
                  <CheckCircle className="text-green-500 mr-3" size={20} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    项目管理
                  </h3>
                </div>
                <p className="mt-3 text-gray-600">
                  完整的项目管理系统，支持多项目并行处理，数据安全可靠
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
