const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkFailures() {
  try {
    const segments = await prisma.videoSegment.findMany({
      where: { status: 'failed' },
      orderBy: { updatedAt: 'desc' },
      take: 1
    });

    if (segments.length > 0) {
      const segment = segments[0];
      console.log('最新失败片段:');
      console.log('ID:', segment.id);
      console.log('状态:', segment.status);
      console.log('更新时间:', segment.updatedAt);

      if (segment.metadata) {
        try {
          const meta = JSON.parse(segment.metadata);
          console.log('错误:', meta.error);
          console.log('提供商:', meta.provider);
        } catch (e) {
          console.log('原始元数据:', segment.metadata);
        }
      }
    } else {
      console.log('没有失败的片段');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkFailures();
