import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { DeepSeekClient, AIServiceError, handleAIRequest } from '@/lib/ai'

// 自动分析人物剧情
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, customPrompt } = body

    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '项目ID不能为空' },
        { status: 400 }
      )
    }

    // 获取项目信息
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 获取全局AI配置
    const aiConfig = await prisma.aIConfig.findUnique({
      where: { id: 'global' },
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型' },
        { status: 400 }
      )
    }

    // 更新项目状态为分析中
    await prisma.project.update({
      where: { id: projectId },
      data: { status: 'analyzing' },
    })

    try {
      // 创建AI客户端
      const aiClient = new DeepSeekClient(aiConfig)

      // 调用AI分析
      const analysisResult = await handleAIRequest(() =>
        aiClient.analyzeNovel(project.content, customPrompt)
      )

      // 保存角色信息
      const characters = []
      if (analysisResult.characters && analysisResult.characters.length > 0) {
        for (const char of analysisResult.characters) {
          const character = await prisma.character.create({
            data: {
              projectId: projectId,
              name: char.name,
              appearance: char.appearance,
              identity: char.identity || '',
              personality: char.personality || '',
              hiddenLines: char.hiddenLines || '',
            },
          })
          characters.push(character)
        }
      }

      // 保存剧集信息
      const episodes = []
      if (analysisResult.episodes && analysisResult.episodes.length > 0) {
        for (const ep of analysisResult.episodes) {
          const episode = await prisma.episode.create({
            data: {
              projectId: projectId,
              title: ep.title,
              content: ep.content,
              orderIndex: ep.orderIndex,
              status: 'created',
            },
          })
          episodes.push(episode)
        }
      }

      // 更新项目状态为完成
      await prisma.project.update({
        where: { id: projectId },
        data: { status: 'completed' },
      })

      return NextResponse.json({
        success: true,
        data: {
          characters,
          episodes,
          summary: {
            charactersCount: characters.length,
            episodesCount: episodes.length,
          },
        },
        message: '分析完成',
      })
    } catch (error) {
      // 分析失败，恢复项目状态
      await prisma.project.update({
        where: { id: projectId },
        data: { status: 'uploaded' },
      })

      if (error instanceof AIServiceError) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: error.statusCode }
        )
      }

      throw error
    }
  } catch (error) {
    console.error('分析失败:', error)
    return NextResponse.json(
      { success: false, error: '分析失败，请重试' },
      { status: 500 }
    )
  }
}

// 获取分析状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '项目ID不能为空' },
        { status: 400 }
      )
    }

    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        status: true,
        updatedAt: true,
      },
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        status: project.status,
        updatedAt: project.updatedAt,
      },
    })
  } catch (error) {
    console.error('获取分析状态失败:', error)
    return NextResponse.json(
      { success: false, error: '获取分析状态失败' },
      { status: 500 }
    )
  }
}
