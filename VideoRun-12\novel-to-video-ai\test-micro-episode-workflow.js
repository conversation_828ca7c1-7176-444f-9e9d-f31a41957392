const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMicroEpisodeWorkflow() {
  try {
    console.log('🎬 测试微剧集制作完整流程...\n');
    
    // 1. 检查现有剧集
    const episode = await prisma.episode.findFirst({
      where: { title: '雪夜初逢' }
    });
    
    if (!episode) {
      console.log('❌ 未找到"雪夜初逢"剧集');
      return;
    }
    
    console.log(`✅ 找到剧集: ${episode.title}`);
    
    // 2. 生成微剧集片段
    console.log('\n📡 开始生成微剧集片段...');
    
    const testData = {
      episodeId: episode.id,
      plotInfo: {
        generatedPrompt: '李四在雪夜中巡逻，发现血人，报告张三。张三检查虎符，确认身份。两人讨论狼主威胁和雁门关防务。'
      }
    };
    
    const response = await fetch('http://localhost:3000/api/ai/generate-story-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ 生成API调用失败: ${errorText.substring(0, 500)}...`);
      return;
    }
    
    const result = await response.json();
    console.log('✅ 微剧集生成请求成功!');
    console.log(`📋 故事视频ID: ${result.storyVideoId}`);
    
    const storyVideoId = result.storyVideoId;
    
    // 3. 等待所有片段生成完成
    console.log('\n⏳ 等待所有片段生成完成...');
    
    let allCompleted = false;
    let attempts = 0;
    const maxAttempts = 60; // 最多等待5分钟
    
    while (!allCompleted && attempts < maxAttempts) {
      attempts++;
      
      const segments = await prisma.videoSegment.findMany({
        where: { storyVideoId },
        orderBy: { createdAt: 'asc' }
      });
      
      const completedSegments = segments.filter(s => s.status === 'completed');
      const failedSegments = segments.filter(s => s.status === 'failed');
      const generatingSegments = segments.filter(s => s.status === 'generating');
      
      console.log(`📊 片段状态 (${attempts}/${maxAttempts}): 完成${completedSegments.length}, 失败${failedSegments.length}, 生成中${generatingSegments.length}, 总计${segments.length}`);
      
      if (completedSegments.length === segments.length && segments.length > 0) {
        allCompleted = true;
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
    }
    
    if (!allCompleted) {
      console.log('⏰ 等待超时，继续测试合并功能...');
    }
    
    // 4. 分析生成的片段
    console.log('\n📊 分析生成的微剧集片段:');
    
    const finalSegments = await prisma.videoSegment.findMany({
      where: { storyVideoId },
      orderBy: { createdAt: 'asc' }
    });
    
    let totalDuration = 0;
    let completedCount = 0;
    
    for (const segment of finalSegments) {
      console.log(`\n🎬 片段 ${segment.segmentIndex}: ${segment.title.substring(0, 50)}...`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   时长: ${segment.duration}秒`);
      console.log(`   类型: ${segment.segmentType}`);
      console.log(`   视频URL: ${segment.videoUrl ? '有' : '无'}`);
      
      if (segment.status === 'completed') {
        completedCount++;
        totalDuration += segment.duration || 0;
      }
    }
    
    console.log(`\n📈 微剧集统计:`);
    console.log(`   总片段数: ${finalSegments.length}`);
    console.log(`   完成片段: ${completedCount}`);
    console.log(`   总时长: ${totalDuration}秒`);
    console.log(`   平均时长: ${completedCount > 0 ? (totalDuration / completedCount).toFixed(1) : 0}秒/片段`);
    
    // 5. 测试视频合并功能
    if (completedCount > 0) {
      console.log('\n🔧 测试视频合并功能...');
      
      const mergeResponse = await fetch('http://localhost:3000/api/video/merge-segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          storyVideoId,
          outputFormat: 'mp4',
          quality: 'high'
        })
      });
      
      if (mergeResponse.ok) {
        const mergeResult = await mergeResponse.json();
        console.log('✅ 视频合并测试成功!');
        console.log(`📋 合并结果:`, JSON.stringify(mergeResult.data, null, 2));
      } else {
        const mergeError = await mergeResponse.text();
        console.log('⚠️ 视频合并测试失败:', mergeError.substring(0, 200));
      }
    }
    
    // 6. 微剧集质量评估
    console.log('\n🎯 微剧集质量评估:');
    
    const qualityMetrics = {
      技术可行性: completedCount > 0 ? '✅ 通过' : '❌ 失败',
      时长控制: totalDuration > 0 && totalDuration <= 60 ? '✅ 合理' : '⚠️ 需调整',
      片段数量: finalSegments.length >= 3 && finalSegments.length <= 8 ? '✅ 适中' : '⚠️ 需调整',
      生成成功率: `${Math.round((completedCount / finalSegments.length) * 100)}%`,
      平均片段时长: completedCount > 0 ? `${(totalDuration / completedCount).toFixed(1)}秒` : '0秒'
    };
    
    Object.entries(qualityMetrics).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    
    // 7. 提供改进建议
    console.log('\n💡 微剧集制作建议:');
    
    if (completedCount === 0) {
      console.log('   🔧 检查AI配置和网络连接');
      console.log('   🔧 确认豆包API密钥有效');
      console.log('   🔧 检查提示词格式');
    } else if (completedCount < finalSegments.length) {
      console.log('   🔧 优化失败片段的提示词');
      console.log('   🔧 检查网络稳定性');
      console.log('   🔧 考虑重试机制');
    } else {
      console.log('   🎉 微剧集生成成功！');
      console.log('   🎬 可以开始制作更多剧集');
      console.log('   🔧 考虑添加音效和字幕');
    }
    
    // 8. 用户体验建议
    console.log('\n👥 用户体验优化建议:');
    console.log('   📱 添加移动端适配');
    console.log('   ⚡ 优化加载速度');
    console.log('   🎨 改进播放界面');
    console.log('   📊 添加进度显示');
    console.log('   🔄 支持片段重新排序');
    
    // 9. 成本分析
    if (completedCount > 0) {
      console.log('\n💰 成本分析:');
      const costPerSegment = 3.67; // 豆包每5秒视频成本
      const totalCost = completedCount * costPerSegment;
      console.log(`   单片段成本: ¥${costPerSegment}`);
      console.log(`   总制作成本: ¥${totalCost.toFixed(2)}`);
      console.log(`   每秒成本: ¥${(totalCost / totalDuration).toFixed(2)}`);
    }
    
    console.log('\n🎬 微剧集制作流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMicroEpisodeWorkflow();
