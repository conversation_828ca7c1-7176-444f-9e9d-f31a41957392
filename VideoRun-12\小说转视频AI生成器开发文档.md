# 灵犀Ai——小说转视频神器开发文档

## 项目概述

基于React + Next.js构建的小说转视频AI生成器，使用DeepSeek作为默认大语言模型驱动，实现小说文本的智能分析和视频内容生成。项目包含完整的角色管理系统，支持AI角色信息提取和形象一致性编辑。

## 技术栈选择

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **UI库**: Tailwind CSS + shadcn/ui
- **状态管理**: Zustand
- **文件上传**: react-dropzone
- **图标**: Lucide React
- **类型检查**: TypeScript

### 后端技术栈
- **API**: Next.js API Routes
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **ORM**: Prisma
- **文件存储**: 本地存储 / 云存储
- **AI集成**: DeepSeek API

### 开发工具
- **包管理**: pnpm
- **代码规范**: ESLint + Prettier
- **Git钩子**: Husky
- **部署**: Vercel

## 项目结构

```
novel-to-video-ai/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API路由
│   │   │   ├── ai/           # AI相关API
│   │   │   ├── upload/       # 文件上传API
│   │   │   └── episodes/     # 剧集管理API
│   │   ├── components/        # React组件
│   │   │   ├── ui/           # 基础UI组件
│   │   │   ├── upload/       # 上传相关组件
│   │   │   ├── characters/   # 角色管理组件
│   │   │   ├── episodes/     # 剧集管理组件
│   │   │   └── ai-config/    # AI配置组件
│   │   ├── lib/              # 工具库
│   │   │   ├── ai/           # AI集成
│   │   │   ├── db/           # 数据库
│   │   │   └── utils/        # 通用工具
│   │   ├── types/            # TypeScript类型定义
│   │   └── stores/           # Zustand状态管理
├── prisma/                   # 数据库模式
├── public/                   # 静态资源
└── docs/                     # 文档
```

## 数据库设计

### 表结构设计

```sql
-- 项目表
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  file_path TEXT,
  file_name TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI配置表
CREATE TABLE ai_configs (
  id TEXT PRIMARY KEY,
  project_id TEXT REFERENCES projects(id),
  provider TEXT DEFAULT 'deepseek',
  api_key TEXT,
  model TEXT DEFAULT 'deepseek-chat',
  parameters JSON,
  prompts JSON,
  status TEXT DEFAULT 'disconnected',
  last_test DATETIME
);

-- 角色表
CREATE TABLE characters (
  id TEXT PRIMARY KEY,
  project_id TEXT REFERENCES projects(id),
  name TEXT NOT NULL,
  appearance JSON,
  identity TEXT,
  personality TEXT,
  hidden_lines TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 剧集表
CREATE TABLE episodes (
  id TEXT PRIMARY KEY,
  project_id TEXT REFERENCES projects(id),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  order_index INTEGER,
  plot_info JSON,
  video_status TEXT DEFAULT 'not_generated',
  video_path TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 剧情信息表
CREATE TABLE plot_infos (
  id TEXT PRIMARY KEY,
  episode_id TEXT REFERENCES episodes(id),
  characters JSON,
  scenes JSON,
  events JSON,
  extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 大模型配置模块

#### AI配置组件 (`src/components/ai-config/AIConfigPanel.tsx`)
```typescript
interface AIConfig {
  provider: 'deepseek' | 'openai' | 'claude';
  apiKey: string;
  model: string;
  parameters: {
    temperature: number;
    maxTokens: number;
    topP: number;
  };
}

export function AIConfigPanel() {
  // 配置表单
  // API连接测试
  // 状态显示
}
```

#### DeepSeek集成 (`src/lib/ai/deepseek.ts`)
```typescript
export class DeepSeekClient {
  constructor(apiKey: string) {}
  
  async analyzeNovel(text: string): Promise<AnalysisResult> {
    // 调用DeepSeek API进行小说分析
  }
  
  async extractCharacters(text: string): Promise<Character[]> {
    // 提取角色信息
  }
  
  async splitEpisodes(text: string): Promise<Episode[]> {
    // 拆分剧集
  }
  
  async analyzePlot(episodeText: string): Promise<PlotInfo> {
    // 分析剧情信息
  }
}
```

### 2. 文件上传模块

#### 上传组件 (`src/components/upload/FileUpload.tsx`)
```typescript
export function FileUpload() {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    // 处理文件上传
    // 文件验证
    // 进度显示
  }, []);

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      {/* 拖拽区域UI */}
    </div>
  );
}
```

### 3. 角色管理模块（升级版）

#### 角色管理组件 (`src/components/CharacterManager.tsx`)
```typescript
interface Character {
  id: string;
  name: string;
  facial?: string;      // 五官描述
  identity?: string;    // 身份信息
  appearance?: string;  // 外貌描述
  personality?: string; // 性格特点
  hiddenLines?: string; // 隐线伏笔
}

export function CharacterManager({ characters }: { characters: Character[] }) {
  // 卡片式角色列表展示
  // 头像+名称显示
  // 点击打开详情弹窗
}
```

#### 角色详情弹窗 (`src/components/CharacterDetail.tsx`)
```typescript
interface CharacterDetailProps {
  character: Character;
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (character: Character) => void;
}

export function CharacterDetail({ character, isOpen, onClose, onUpdate }: CharacterDetailProps) {
  // 双标签页设计：
  // 1. 角色信息页 - AI提取的五大角色信息
  // 2. 形象编辑页 - 连续剧一致性设置
  // 支持在线编辑和保存
}
```

#### 功能特点
- **卡片式显示**: 头像+名称的响应式网格布局
- **详情弹窗**: 点击角色打开全屏详情弹窗
- **双标签页**:
  - 角色信息: 五官、身份、外貌、性格、隐线
  - 形象编辑: 五官、发型、身材、服装（为连续剧一致性准备）
- **在线编辑**: 支持角色信息的实时编辑和保存
- **响应式设计**: 自适应1-4列布局

### 4. 剧集管理模块

#### 剧集列表组件 (`src/components/episodes/EpisodeList.tsx`)
```typescript
interface Episode {
  id: string;
  title: string;
  content: string;
  orderIndex: number;
  plotInfo?: PlotInfo;
  videoStatus: 'not_generated' | 'generating' | 'completed';
}

export function EpisodeList({ episodes }: { episodes: Episode[] }) {
  // 剧集列表展示
  // 剧集详情查看
  // 剧情信息提取
  // 视频生成
}
```

## API设计

### AI分析API (`/api/ai/analyze`)
```typescript
// POST /api/ai/analyze
interface AnalyzeRequest {
  projectId: string;
  text: string;
  type: 'full' | 'characters' | 'episodes' | 'plot';
}

interface AnalyzeResponse {
  success: boolean;
  data: {
    characters?: Character[];
    episodes?: Episode[];
    plotInfo?: PlotInfo;
  };
  error?: string;
}
```

### 文件上传API (`/api/upload`)
```typescript
// POST /api/upload
interface UploadResponse {
  success: boolean;
  data: {
    projectId: string;
    fileName: string;
    filePath: string;
    content: string;
  };
  error?: string;
}
```

## 状态管理

### 使用Zustand管理全局状态

```typescript
interface AppState {
  // AI配置状态
  aiConfig: AIConfig | null;
  setAIConfig: (config: AIConfig) => void;
  
  // 项目状态
  currentProject: Project | null;
  setCurrentProject: (project: Project) => void;
  
  // 角色状态
  characters: Character[];
  setCharacters: (characters: Character[]) => void;
  
  // 剧集状态
  episodes: Episode[];
  setEpisodes: (episodes: Episode[]) => void;
  
  // UI状态
  isAnalyzing: boolean;
  setIsAnalyzing: (analyzing: boolean) => void;
}
```

## 开发流程

### 第一阶段：基础架构搭建
1. **项目初始化**
   ```bash
   npx create-next-app@latest novel-to-video-ai --typescript --tailwind --app
   cd novel-to-video-ai
   pnpm install
   ```

2. **安装依赖**
   ```bash
   pnpm add zustand prisma @prisma/client react-dropzone lucide-react
   pnpm add -D @types/node
   ```

3. **配置数据库**
   ```bash
   npx prisma init
   npx prisma generate
   npx prisma db push
   ```

4. **创建基础组件结构**
   - AI配置面板
   - 文件上传组件
   - 基础布局组件

### 第二阶段：AI集成
1. **DeepSeek API集成**
2. **提示词工程**
3. **错误处理和重试机制**
4. **API限流处理**

### 第三阶段：核心功能实现
1. **角色信息提取**
2. **剧集自动拆分**
3. **剧情信息分析**
4. **数据持久化**

### 第四阶段：视频生成
1. **视频生成API集成**
2. **多维度信息整合**
3. **生成状态管理**
4. **结果展示**

## 部署配置

### 环境变量配置
```env
# 数据库
DATABASE_URL="postgresql://..."

# AI配置
DEEPSEEK_API_KEY="sk-..."
OPENAI_API_KEY="sk-..."

# 文件存储
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="52428800" # 50MB

# 应用配置
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="http://localhost:3000"
```

### Vercel部署
```json
{
  "name": "novel-to-video-ai",
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ]
}
```

## 测试策略

### 单元测试
- AI API调用测试
- 数据处理逻辑测试
- 组件渲染测试

### 集成测试
- 完整工作流程测试
- API端到端测试
- 文件上传流程测试

### 性能测试
- 大文件处理性能
- AI API响应时间
- 并发请求处理

## 安全考虑

1. **API密钥安全**：加密存储，不在前端暴露
2. **文件上传安全**：文件类型验证，大小限制
3. **输入验证**：所有用户输入进行验证和清理
4. **错误处理**：不暴露敏感信息的错误消息

## 性能优化

1. **代码分割**：按路由和功能进行代码分割
2. **缓存策略**：API响应缓存，静态资源缓存
3. **懒加载**：大组件和非关键功能懒加载
4. **数据库优化**：索引优化，查询优化

## 角色管理升级实现

### 设计理念
角色管理升级旨在为连续剧制作提供人物形象一致性支持，通过AI提取的角色信息和手动编辑的形象设置，确保同一角色在不同剧集中保持视觉统一。

### 核心功能

#### 1. 卡片式角色展示
- **视觉设计**: 头像+名称的卡片布局
- **响应式网格**: 1-4列自适应布局
- **渐变头像**: 紫色渐变圆形头像设计
- **信息预览**: 显示身份标签和性格简介

#### 2. 角色详情弹窗
- **全屏模态**: 居中显示，支持点击外部关闭
- **双标签页**: 角色信息 + 形象编辑
- **编辑模式**: 支持在线编辑和实时保存
- **数据同步**: 编辑后数据实时同步到数据库

#### 3. 形象一致性编辑
- **四维度设置**: 五官、发型、身材、服装
- **详细描述**: 为AI视频生成提供精确参考
- **标准化格式**: 统一的描述格式和字段

### 技术实现

#### 组件架构
```typescript
// 主要组件结构
CharacterManager (角色管理主组件)
├── 角色卡片网格布局
├── 点击事件处理
└── CharacterDetail (角色详情弹窗)
    ├── 弹窗容器和遮罩
    ├── 标签页导航
    ├── 角色信息标签页
    ├── 形象编辑标签页
    └── 编辑功能和保存逻辑
```

#### 状态管理
```typescript
// 角色详情状态
const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
const [isDetailOpen, setIsDetailOpen] = useState(false);
const [activeTab, setActiveTab] = useState<'info' | 'appearance'>('info');
const [isEditing, setIsEditing] = useState(false);
```

## 关键实现细节

### DeepSeek提示词模板

#### 角色提取提示词
```
你是一个专业的小说分析师。请分析以下小说文本，提取所有角色信息。

要求：
1. 识别所有出现的人物角色
2. 为每个角色提取以下信息：
   - 姓名
   - 五官描述（眼睛、鼻子、嘴巴等特征）
   - 身份背景（职业、社会地位等）
   - 外貌特征（身高、体型、服装等）
   - 性格特点（内向/外向、温和/暴躁等）
   - 隐线伏笔（暗示的背景故事、未来发展等）

请以JSON格式返回结果：
{
  "characters": [
    {
      "name": "角色名",
      "appearance": {
        "face": "五官描述",
        "body": "外貌特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ]
}

小说文本：
{novel_text}
```

#### 剧集拆分提示词
```
你是一个专业的剧本编辑。请将以下小说按照原有章节结构拆分成独立的剧集。

要求：
1. 保持原有章节划分
2. 每个剧集包含完整的故事情节
3. 提取故事标题和详细剧情内容
4. 按原文顺序排列

请以JSON格式返回结果：
{
  "episodes": [
    {
      "title": "故事标题",
      "content": "详细剧情内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
{novel_text}
```

#### 剧情信息提取提示词
```
你是一个专业的剧情分析师。请分析以下剧集内容，提取三大核心信息。

要求：
1. 本集人物：识别当前剧集中出场的所有角色
2. 场景信息：分析故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

请以JSON格式返回结果：
{
  "plotInfo": {
    "characters": ["角色名列表"],
    "scenes": [
      {
        "location": "场景地点",
        "description": "环境描述",
        "atmosphere": "氛围设定"
      }
    ],
    "events": [
      {
        "normal": "正常状态描述",
        "conflict": "矛盾冲突描述",
        "escalation": "升级事件描述",
        "participants": ["参与角色"],
        "location": "发生地点",
        "actions": ["具体行为"]
      }
    ]
  }
}

剧集内容：
{episode_content}
```

### 错误处理策略

#### API错误处理
```typescript
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

export async function handleAIRequest<T>(
  request: () => Promise<T>
): Promise<T> {
  try {
    return await request();
  } catch (error) {
    if (error instanceof AIServiceError) {
      throw error;
    }

    // 网络错误
    if (error.code === 'ECONNREFUSED') {
      throw new AIServiceError(
        'AI服务连接失败，请检查网络连接',
        'CONNECTION_ERROR',
        503
      );
    }

    // API限流
    if (error.status === 429) {
      throw new AIServiceError(
        'API调用频率过高，请稍后重试',
        'RATE_LIMIT',
        429
      );
    }

    // 通用错误
    throw new AIServiceError(
      'AI服务处理失败，请重试',
      'UNKNOWN_ERROR',
      500
    );
  }
}
```

### 文件处理工具

#### 文本文件解析
```typescript
export class TextFileParser {
  static async parseFile(file: File): Promise<string> {
    const extension = file.name.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'txt':
        return await this.parseTxtFile(file);
      case 'doc':
      case 'docx':
        return await this.parseDocFile(file);
      default:
        throw new Error(`不支持的文件格式: ${extension}`);
    }
  }

  private static async parseTxtFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'utf-8');
    });
  }

  private static async parseDocFile(file: File): Promise<string> {
    // 使用mammoth.js或其他库解析Word文档
    // 这里需要额外的依赖
    throw new Error('Word文档解析功能待实现');
  }
}
```

## 开发最佳实践

### 1. 组件设计原则
- 单一职责：每个组件只负责一个功能
- 可复用性：通用组件抽象到ui目录
- 类型安全：所有props和状态都有TypeScript类型

### 2. 状态管理原则
- 最小化状态：只存储必要的状态
- 状态归一化：避免重复数据
- 副作用隔离：异步操作单独处理

### 3. API设计原则
- RESTful设计：遵循REST规范
- 错误统一：统一的错误响应格式
- 版本控制：API版本管理

### 4. 安全最佳实践
- 输入验证：所有用户输入都要验证
- 权限控制：API访问权限控制
- 数据加密：敏感数据加密存储

## 监控和日志

### 应用监控
```typescript
// 性能监控
export function trackPerformance(name: string, fn: () => Promise<any>) {
  const start = performance.now();
  return fn().finally(() => {
    const duration = performance.now() - start;
    console.log(`${name} took ${duration}ms`);
  });
}

// 错误监控
export function trackError(error: Error, context: any) {
  console.error('Application Error:', {
    message: error.message,
    stack: error.stack,
    context
  });

  // 发送到监控服务
  // sendToMonitoring(error, context);
}
```

### 用户行为分析
```typescript
export function trackUserAction(action: string, data?: any) {
  console.log('User Action:', { action, data, timestamp: new Date() });

  // 发送到分析服务
  // sendToAnalytics(action, data);
}
```

## 部署和运维

### Docker配置
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 环境配置管理
```typescript
export const config = {
  database: {
    url: process.env.DATABASE_URL!,
  },
  ai: {
    deepseek: {
      apiKey: process.env.DEEPSEEK_API_KEY!,
      baseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
    },
  },
  upload: {
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'),
    allowedTypes: ['text/plain', 'application/msword'],
  },
};
```

这个开发文档提供了完整的技术实现指南，包括：

1. **技术栈选择**：现代化的React生态
2. **项目结构**：清晰的目录组织
3. **数据库设计**：完整的表结构
4. **核心功能实现**：详细的代码示例
5. **API设计**：RESTful接口规范
6. **DeepSeek集成**：专门的提示词模板
7. **错误处理**：完善的异常处理机制
8. **性能优化**：多方面的优化策略
9. **安全考虑**：全面的安全措施
10. **部署运维**：生产环境配置

你觉得这个开发文档是否满足项目需求？有什么需要补充或修改的地方吗？
