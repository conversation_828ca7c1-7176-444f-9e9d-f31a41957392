import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// POST - 清理假的视频数据
export async function POST(request: NextRequest) {
  try {
    console.log('开始清理假的视频数据...')

    // 1. 查找所有状态为"completed"但videoUrl为空或为假URL的视频片段
    const fakeSegments = await prisma.videoSegment.findMany({
      where: {
        status: 'completed',
        OR: [
          { videoUrl: null },
          { videoUrl: '' },
          { videoUrl: { contains: 'example.com' } },
          { videoUrl: { contains: 'placeholder' } }
        ]
      }
    })

    console.log(`找到 ${fakeSegments.length} 个假的视频片段`)

    // 2. 将这些片段的状态重置为pending
    if (fakeSegments.length > 0) {
      await prisma.videoSegment.updateMany({
        where: {
          id: { in: fakeSegments.map(seg => seg.id) }
        },
        data: {
          status: 'pending',
          videoUrl: null,
          thumbnailUrl: null,
          metadata: JSON.stringify({
            cleanedAt: new Date().toISOString(),
            note: '清理假数据，重置为待生成状态'
          })
        }
      })
    }

    // 3. 查找相关的主视频记录并更新状态
    const storyVideoIds = [...new Set(fakeSegments.map(seg => seg.storyVideoId))]
    
    for (const storyVideoId of storyVideoIds) {
      // 检查该主视频下是否还有已完成的片段
      const completedSegments = await prisma.videoSegment.findMany({
        where: {
          storyVideoId,
          status: 'completed'
        }
      })

      const totalSegments = await prisma.videoSegment.count({
        where: { storyVideoId }
      })

      // 更新主视频状态
      await prisma.storyVideo.update({
        where: { id: storyVideoId },
        data: {
          status: completedSegments.length === totalSegments ? 'completed' : 
                  completedSegments.length > 0 ? 'generating' : 'pending',
          metadata: JSON.stringify({
            totalSegments,
            completedSegments: completedSegments.length,
            cleanedAt: new Date().toISOString()
          })
        }
      })
    }

    // 4. 统计清理结果
    const result = {
      cleanedSegments: fakeSegments.length,
      affectedStoryVideos: storyVideoIds.length,
      cleanedAt: new Date().toISOString()
    }

    console.log('清理完成:', result)

    return NextResponse.json({
      success: true,
      message: '假视频数据清理完成',
      data: result
    })

  } catch (error) {
    console.error('清理假视频数据失败:', error)
    return NextResponse.json(
      { success: false, error: '清理失败' },
      { status: 500 }
    )
  }
}

// GET - 检查假视频数据统计
export async function GET(request: NextRequest) {
  try {
    // 统计假视频数据
    const fakeSegments = await prisma.videoSegment.findMany({
      where: {
        status: 'completed',
        OR: [
          { videoUrl: null },
          { videoUrl: '' },
          { videoUrl: { contains: 'example.com' } },
          { videoUrl: { contains: 'placeholder' } }
        ]
      },
      include: {
        storyVideo: {
          include: {
            episode: {
              select: { title: true }
            }
          }
        }
      }
    })

    const stats = {
      totalFakeSegments: fakeSegments.length,
      affectedStoryVideos: [...new Set(fakeSegments.map(seg => seg.storyVideoId))].length,
      segmentsByEpisode: fakeSegments.reduce((acc, seg) => {
        const episodeTitle = seg.storyVideo?.episode?.title || '未知剧集'
        acc[episodeTitle] = (acc[episodeTitle] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }

    return NextResponse.json({
      success: true,
      data: stats,
      segments: fakeSegments.map(seg => ({
        id: seg.id,
        title: seg.title,
        status: seg.status,
        videoUrl: seg.videoUrl,
        episodeTitle: seg.storyVideo?.episode?.title || '未知剧集'
      }))
    })

  } catch (error) {
    console.error('检查假视频数据失败:', error)
    return NextResponse.json(
      { success: false, error: '检查失败' },
      { status: 500 }
    )
  }
}
