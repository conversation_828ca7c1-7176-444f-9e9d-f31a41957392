import { AIConfig, Character, Episode, PlotInfo, AnalysisResponse } from '@/types'
import { SkyReelsClient } from './skyreels'

// AI服务错误类
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'AIServiceError'
  }
}

// DeepSeek API客户端
export class DeepSeekClient {
  private apiKey: string
  private model: string
  private baseUrl: string = 'https://api.deepseek.com/v1/chat/completions'

  constructor(config: AIConfig) {
    this.apiKey = config.apiKey
    this.model = config.model
  }

  // 测试API连接
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          messages: [{ role: 'user', content: '测试连接' }],
          max_tokens: 10,
        }),
      })

      return response.ok
    } catch (error) {
      console.error('DeepSeek连接测试失败:', error)
      return false
    }
  }

  // 调用AI API的通用方法（公开方法）
  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          messages: [{ role: 'user', content: prompt }],
          max_tokens: maxTokens,
          temperature: 0.7,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new AIServiceError(
          errorData.error?.message || 'API调用失败',
          'API_ERROR',
          response.status
        )
      }

      const data = await response.json()
      return data.choices[0]?.message?.content || ''
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error
      }
      throw new AIServiceError(
        'AI服务调用失败，请检查网络连接',
        'NETWORK_ERROR',
        500
      )
    }
  }

  // 生成测试响应
  private generateTestResponse(prompt: string): string {
    if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {
      // 角色一致性分析的测试响应
      return `{
        "characters": [
          {
            "name": "张小雅",
            "identity": "高中生",
            "appearance": "马尾辫，甜美笑容，青春活泼",
            "personality": "开朗活泼，善良纯真",
            "clothing": "校服或休闲装",
            "role": "主要角色",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "zhang_xiaoya",
              "consistencyMatch": 0.95,
              "differences": [],
              "consistencyConstraints": "保持马尾辫和甜美笑容的标志性特征"
            }
          },
          {
            "name": "王美丽",
            "identity": "咖啡店老板",
            "appearance": "瓜子脸，波浪卷发，温柔气质",
            "personality": "温柔善良，热情好客",
            "clothing": "简约优雅的服装",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "wang_meili",
              "consistencyMatch": 0.88,
              "differences": [],
              "consistencyConstraints": "保持瓜子脸和波浪卷发的特征"
            }
          },
          {
            "name": "李明轩",
            "identity": "大学教授",
            "appearance": "方脸，花白短发，学者气质",
            "personality": "温文尔雅，博学睿智",
            "clothing": "正式的学者装扮",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "li_mingxuan",
              "consistencyMatch": 0.92,
              "differences": [],
              "consistencyConstraints": "保持方脸和花白短发的学者形象"
            }
          },
          {
            "name": "林志强",
            "identity": "程序员",
            "appearance": "高瘦身材，黑框眼镜，简约穿着",
            "personality": "内向专注，技术宅",
            "clothing": "白色T恤，牛仔裤",
            "role": "新角色",
            "isKnownCharacter": false,
            "consistencyInfo": {
              "matchedCharacterId": null,
              "consistencyMatch": 0.0,
              "differences": ["新出现的角色"],
              "consistencyConstraints": "建立新的角色DNA档案"
            }
          }
        ]
      }`
    } else if (prompt.includes('场景信息')) {
      return `{
        "scenes": [
          {
            "location": "温馨咖啡店",
            "description": "装修温馨的小型咖啡店，木质桌椅，暖色调灯光",
            "atmosphere": "温馨舒适，充满生活气息",
            "timeOfDay": "下午",
            "lighting": "暖色调室内灯光",
            "keyElements": "咖啡香味，轻柔音乐，温馨装饰"
          }
        ]
      }`
    } else if (prompt.includes('情节序列')) {
      return `{
        "plotSequences": [
          {
            "sequenceId": "reunion_1",
            "action": "张小雅进入咖啡店与王美丽重逢",
            "emotion": "惊喜和温暖",
            "duration": "2分钟",
            "keyMoments": ["进门", "相认", "拥抱"],
            "visualElements": "特写表情变化，温馨的重逢场面"
          },
          {
            "sequenceId": "professor_arrival",
            "action": "李明轩教授进入咖啡店",
            "emotion": "温和友善",
            "duration": "1分钟",
            "keyMoments": ["进门", "打招呼"],
            "visualElements": "学者风度，温和笑容"
          },
          {
            "sequenceId": "new_character",
            "action": "林志强询问WiFi",
            "emotion": "略显紧张的初次见面",
            "duration": "1分钟",
            "keyMoments": ["进门", "询问", "介绍"],
            "visualElements": "新角色登场，技术宅形象"
          }
        ]
      }`
    } else {
      return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽'
    }
  }

  // 分析小说，提取角色和剧集信息
  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {
    const prompt = this.buildAnalysisPrompt(content, customPrompt)
    const response = await this.callAPI(prompt, 8000)
    
    try {
      return this.parseAnalysisResponse(response)
    } catch (error) {
      console.error('解析AI响应失败:', error)
      throw new AIServiceError(
        'AI响应解析失败，请重试',
        'PARSE_ERROR',
        500
      )
    }
  }

  // 分析单个剧集的剧情信息
  async analyzePlot(episodeContent: string): Promise<PlotInfo> {
    const prompt = this.buildPlotAnalysisPrompt(episodeContent)
    const response = await this.callAPI(prompt, 4000)
    
    try {
      return this.parsePlotResponse(response)
    } catch (error) {
      console.error('解析剧情分析响应失败:', error)
      throw new AIServiceError(
        '剧情分析失败，请重试',
        'PLOT_PARSE_ERROR',
        500
      )
    }
  }

  // 构建小说分析提示词
  private buildAnalysisPrompt(content: string, customPrompt?: string): string {
    let basePrompt = `请分析以下小说文本，同时完成两个任务：`

    if (customPrompt && customPrompt.trim()) {
      basePrompt += `\n\n增强要求：${customPrompt}\n`
    }

    return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`
  }

  // 构建剧情分析提示词
  private buildPlotAnalysisPrompt(episodeContent: string): string {
    return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`
  }

  // 解析小说分析响应
  private parseAnalysisResponse(response: string): AnalysisResponse {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }

    const parsed = JSON.parse(jsonMatch[0])
    
    return {
      characters: parsed.characters?.map((char: any) => ({
        name: char.name || '',
        appearance: JSON.stringify(char.appearance || {}),
        identity: char.identity || '',
        personality: char.personality || '',
        hiddenLines: char.hiddenLines || '',
      })) || [],
      episodes: parsed.episodes?.map((ep: any, index: number) => ({
        title: ep.title || `第${index + 1}章`,
        content: ep.content || '',
        orderIndex: ep.orderIndex || index + 1,
        status: 'created' as const,
      })) || [],
    }
  }

  // 解析剧情分析响应
  private parsePlotResponse(response: string): any {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }

    const parsed = JSON.parse(jsonMatch[0])
    
    return {
      characters: JSON.stringify(parsed.characters || []),
      scenes: JSON.stringify(parsed.scenes || []),
      events: JSON.stringify(parsed.events || []),
    }
  }
}

// 豆包 (Doubao) API客户端
export class DoubaoClient {
  private apiKey: string
  private model: string
  private baseUrl: string
  private isVideoModel: boolean

  constructor(config: AIConfig) {
    this.apiKey = config.apiKey
    this.model = config.model || 'doubao-seedance-1.0-pro'
    // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID
    this.isVideoModel = this.model.includes('seedance') ||
                       this.model.includes('video') ||
                       this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式

    // 根据模型类型选择正确的API端点
    if (this.isVideoModel) {
      // 豆包视频生成使用专门的视频生成API
      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'
    } else {
      // 文本模型使用chat completions API
      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
    }

    if (config.baseUrl) {
      this.baseUrl = config.baseUrl
    }
  }

  // 测试API连接（带重试机制）
  async testConnection(): Promise<boolean> {
    const maxRetries = 3
    const retryDelay = 1000 // 1秒

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        let requestBody: any

        if (this.isVideoModel) {
          // 豆包视频生成使用官方确认的API格式
          requestBody = {
            model: this.model,
            content: [
              {
                type: "text",
                text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
              }
            ]
          }
        } else {
          // 文本模型使用chat completions格式
          requestBody = {
            model: this.model,
            messages: [{ role: 'user', content: '测试连接' }],
            max_tokens: 10
          }
        }

        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        })

        if (response.ok) {
          return true
        }

        // 检查是否是可重试的错误
        const errorText = await response.text()
        if (errorText.includes('internal error') && attempt < maxRetries) {
          console.log(`豆包API内部错误，第${attempt}次重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }

        return false
      } catch (error) {
        console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error)
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }
        return false
      }
    }

    return false
  }

  // 调用AI API的通用方法（带重试机制）
  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {
    const maxRetries = 3
    const retryDelay = 1000 // 1秒

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        let requestBody: any

        if (this.isVideoModel) {
          // 豆包视频生成使用官方确认的API格式
          requestBody = {
            model: this.model,
            content: [
              {
                type: "text",
                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`
              }
            ]
          }
        } else {
          // 文本模型使用chat completions格式
          requestBody = {
            model: this.model,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: maxTokens,
            temperature: 0.7
          }
        }

        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        })

        if (response.ok) {
          const data = await response.json()

          if (this.isVideoModel) {
            // 视频生成返回任务信息
            return JSON.stringify({
              task_id: data.task_id,
              status: data.status || 'submitted',
              message: '视频生成任务已提交，请稍后查询结果'
            })
          } else {
            // 文本生成返回内容
            return data.choices[0]?.message?.content || ''
          }
        }

        const errorData = await response.json()
        const errorMessage = errorData.error?.message || '豆包API调用失败'

        // 检查是否是可重试的内部错误
        if (errorMessage.includes('internal error') && attempt < maxRetries) {
          console.log(`豆包API内部错误，第${attempt}次重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }

        // 不可重试的错误，直接抛出
        throw new AIServiceError(
          errorMessage,
          'API_ERROR',
          response.status
        )
      } catch (error) {
        if (error instanceof AIServiceError) {
          // 如果是已知的API错误且不可重试，直接抛出
          throw error
        }

        // 网络错误等，可以重试
        if (attempt < maxRetries) {
          console.log(`豆包API调用失败，第${attempt}次重试...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          continue
        }

        throw new AIServiceError(
          '豆包服务调用失败，请检查网络连接和API密钥',
          'NETWORK_ERROR',
          500
        )
      }
    }

    throw new AIServiceError(
      '豆包服务调用失败，已达到最大重试次数',
      'MAX_RETRIES_EXCEEDED',
      500
    )
  }

  // 专门的视频生成方法
  async generateVideo(prompt: string, duration: number = 5): Promise<string> {
    if (!this.isVideoModel) {
      throw new Error('此模型不支持视频生成')
    }

    try {
      const requestBody = {
        model: this.model,
        prompt: prompt,
        video_setting: {
          video_duration: duration,
          video_aspect_ratio: '16:9',
          video_resolution: '720p'
        }
      }

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (response.ok) {
        const data = await response.json()
        return JSON.stringify({
          task_id: data.task_id,
          status: data.status || 'submitted',
          message: '视频生成任务已提交，请稍后查询结果'
        })
      }

      const errorData = await response.json()
      throw new AIServiceError(
        errorData.error?.message || '视频生成失败',
        'VIDEO_GENERATION_ERROR',
        response.status
      )
    } catch (error) {
      if (error instanceof AIServiceError) {
        throw error
      }
      throw new AIServiceError(
        '视频生成服务调用失败',
        'NETWORK_ERROR',
        500
      )
    }
  }

  // 分析小说，提取角色和剧集信息
  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {
    const prompt = this.buildAnalysisPrompt(content, customPrompt)
    const response = await this.callAPI(prompt, 8000)

    try {
      return this.parseAnalysisResponse(response)
    } catch (error) {
      console.error('解析豆包响应失败:', error)
      throw new AIServiceError(
        '豆包响应解析失败，请重试',
        'PARSE_ERROR',
        500
      )
    }
  }

  // 分析单个剧集的剧情信息
  async analyzePlot(episodeContent: string): Promise<PlotInfo> {
    const prompt = this.buildPlotAnalysisPrompt(episodeContent)
    const response = await this.callAPI(prompt, 4000)

    try {
      return this.parsePlotResponse(response)
    } catch (error) {
      console.error('解析豆包剧情分析响应失败:', error)
      throw new AIServiceError(
        '豆包剧情分析失败，请重试',
        'PLOT_PARSE_ERROR',
        500
      )
    }
  }

  // 构建小说分析提示词
  private buildAnalysisPrompt(content: string, customPrompt?: string): string {
    let basePrompt = `请分析以下小说文本，同时完成两个任务：`

    if (customPrompt && customPrompt.trim()) {
      basePrompt += `\n\n增强要求：${customPrompt}\n`
    }

    return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`
  }

  // 构建剧情分析提示词
  private buildPlotAnalysisPrompt(episodeContent: string): string {
    return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`
  }

  // 解析小说分析响应
  private parseAnalysisResponse(response: string): AnalysisResponse {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }

    const parsed = JSON.parse(jsonMatch[0])

    return {
      characters: parsed.characters?.map((char: any) => ({
        name: char.name || '',
        appearance: JSON.stringify(char.appearance || {}),
        identity: char.identity || '',
        personality: char.personality || '',
        hiddenLines: char.hiddenLines || '',
      })) || [],
      episodes: parsed.episodes?.map((ep: any, index: number) => ({
        title: ep.title || `第${index + 1}章`,
        content: ep.content || '',
        orderIndex: ep.orderIndex || index + 1,
        status: 'created' as const,
      })) || [],
    }
  }

  // 解析剧情分析响应
  private parsePlotResponse(response: string): any {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (!jsonMatch) {
      throw new Error('未找到有效的JSON响应')
    }

    const parsed = JSON.parse(jsonMatch[0])

    return {
      characters: JSON.stringify(parsed.characters || []),
      scenes: JSON.stringify(parsed.scenes || []),
      events: JSON.stringify(parsed.events || []),
    }
  }
}

// AI客户端工厂函数
export function createAIClient(config: AIConfig): DeepSeekClient | DoubaoClient | SkyReelsClient {
  switch (config.provider) {
    case 'deepseek':
      return new DeepSeekClient(config)
    case 'doubao':
      return new DoubaoClient(config)
    case 'skyreels':
      return new SkyReelsClient(config)
    default:
      // 默认使用DeepSeek客户端，但可以扩展支持其他提供商
      return new DeepSeekClient(config)
  }
}

// 错误处理包装器
export async function handleAIRequest<T>(
  request: () => Promise<T>
): Promise<T> {
  try {
    return await request()
  } catch (error) {
    if (error instanceof AIServiceError) {
      throw error
    }
    
    // 网络错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new AIServiceError(
        'AI服务连接失败，请检查网络连接',
        'CONNECTION_ERROR',
        503
      )
    }
    
    // 通用错误
    throw new AIServiceError(
      'AI服务处理失败，请重试',
      'UNKNOWN_ERROR',
      500
    )
  }
}
