'use client'

import { useState, useEffect } from 'react'
import { Eye, Copy, Edit, Wand2, RefreshCw, X } from 'lucide-react'
import { generateCharacterPrompt, generateNegativePrompt, generateConsistencyPrompt } from '@/utils/promptGenerator'

interface Character {
  id: string
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
}

interface PromptPreviewProps {
  character: Character
  customPrompt?: string
  onPromptChange?: (prompt: string) => void
  isOpen: boolean
  onClose: () => void
}

export default function PromptPreview({ 
  character, 
  customPrompt = '', 
  onPromptChange,
  isOpen,
  onClose 
}: PromptPreviewProps) {
  const [artStyle, setArtStyle] = useState<'anime' | 'realistic' | 'semi-realistic' | 'concept-art'>('anime')
  const [quality, setQuality] = useState<'standard' | 'high' | 'masterpiece'>('masterpiece')
  const [background, setBackground] = useState<'white' | 'transparent' | 'simple'>('white')
  const [activeTab, setActiveTab] = useState<'front' | 'side' | 'back'>('front')
  const [generatedPrompts, setGeneratedPrompts] = useState<{
    front: string
    side: string
    back: string
  }>({ front: '', side: '', back: '' })
  const [negativePrompt, setNegativePrompt] = useState('')

  // 生成提示词
  useEffect(() => {
    if (character) {
      const basePrompt = generateCharacterPrompt(character, {
        artStyle,
        quality,
        background,
        customEnhancement: customPrompt
      })

      const consistencyPrompts = generateConsistencyPrompt(character, basePrompt)
      setGeneratedPrompts(consistencyPrompts)
      setNegativePrompt(generateNegativePrompt())
    }
  }, [character, artStyle, quality, background, customPrompt])

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // 可以添加提示消息
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Eye className="text-purple-600 mr-2" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">
              提示词预览 - {character.name}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
            title="关闭"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* 左侧配置面板 */}
          <div className="w-1/3 p-6 border-r border-gray-200 overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">生成配置</h3>
            
            {/* 艺术风格 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                艺术风格
              </label>
              <select
                value={artStyle}
                onChange={(e) => setArtStyle(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="anime">动漫风格</option>
                <option value="realistic">写实风格</option>
                <option value="semi-realistic">半写实风格</option>
                <option value="concept-art">概念设计</option>
              </select>
            </div>

            {/* 质量等级 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                质量等级
              </label>
              <select
                value={quality}
                onChange={(e) => setQuality(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="standard">标准质量</option>
                <option value="high">高质量</option>
                <option value="masterpiece">大师级</option>
              </select>
            </div>

            {/* 背景设置 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                背景设置
              </label>
              <select
                value={background}
                onChange={(e) => setBackground(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="white">白色背景</option>
                <option value="transparent">透明背景</option>
                <option value="simple">简单背景</option>
              </select>
            </div>

            {/* 角色信息预览 */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-2">角色信息</h4>
              <div className="space-y-2 text-sm">
                {character.identity && (
                  <div>
                    <span className="font-medium text-gray-600">身份：</span>
                    <span className="text-gray-800">{character.identity.substring(0, 50)}...</span>
                  </div>
                )}
                {character.facial && (
                  <div>
                    <span className="font-medium text-gray-600">五官：</span>
                    <span className="text-gray-800">{character.facial.substring(0, 50)}...</span>
                  </div>
                )}
                {character.hairstyle && (
                  <div>
                    <span className="font-medium text-gray-600">发型：</span>
                    <span className="text-gray-800">{character.hairstyle.substring(0, 50)}...</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧提示词预览 */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">生成的提示词</h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => copyToClipboard(generatedPrompts[activeTab])}
                  className="inline-flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  <Copy size={14} className="mr-1" />
                  复制
                </button>
              </div>
            </div>

            {/* 视图切换标签 */}
            <div className="flex space-x-1 mb-4">
              {(['front', 'side', 'back'] as const).map((view) => (
                <button
                  key={view}
                  onClick={() => setActiveTab(view)}
                  className={`px-4 py-2 text-sm font-medium rounded-md ${
                    activeTab === view
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {view === 'front' ? '正面' : view === 'side' ? '侧面' : '背面'}
                </button>
              ))}
            </div>

            {/* 正面提示词 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                正面提示词 (Positive Prompt)
              </label>
              <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
                  {generatedPrompts[activeTab]}
                </pre>
              </div>
            </div>

            {/* 负面提示词 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                负面提示词 (Negative Prompt)
              </label>
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <pre className="text-sm text-red-800 whitespace-pre-wrap font-mono">
                  {negativePrompt}
                </pre>
              </div>
            </div>

            {/* 提示词分析 */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-2">提示词分析</h4>
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-900">总长度：</span>
                    <span className="text-blue-800">{generatedPrompts[activeTab].length} 字符</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-900">关键词数：</span>
                    <span className="text-blue-800">{generatedPrompts[activeTab].split(',').length} 个</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-900">艺术风格：</span>
                    <span className="text-blue-800">{artStyle}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-900">质量等级：</span>
                    <span className="text-blue-800">{quality}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 优化建议 */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-2">优化建议</h4>
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• 确保角色特征描述足够详细</li>
                  <li>• 三视图使用相同的核心特征保持一致性</li>
                  <li>• 可以在增强提示词中添加特定要求</li>
                  <li>• 建议使用高质量或大师级设置获得最佳效果</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            提示词已根据角色信息自动生成并优化
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              关闭
            </button>
            <button
              onClick={() => {
                if (onPromptChange) {
                  onPromptChange(generatedPrompts[activeTab])
                }
                onClose()
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              使用此提示词
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
