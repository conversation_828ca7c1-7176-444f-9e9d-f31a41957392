{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/video-progress/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const episodeId = searchParams.get('episodeId')\n    \n    if (!episodeId) {\n      return NextResponse.json(\n        { success: false, error: '缺少episodeId参数' },\n        { status: 400 }\n      )\n    }\n\n    // 获取视频片段详细信息\n    const segments = await prisma.videoSegment.findMany({\n      where: { episodeId },\n      orderBy: { segmentIndex: 'asc' },\n      select: {\n        id: true,\n        segmentIndex: true,\n        title: true,\n        status: true,\n        duration: true,\n        videoUrl: true,\n        metadata: true,\n        createdAt: true,\n        updatedAt: true\n      }\n    })\n\n    // 计算详细进度信息\n    const totalSegments = segments.length\n    const completedSegments = segments.filter(s => s.status === 'completed').length\n    const generatingSegments = segments.filter(s => s.status === 'generating').length\n    const failedSegments = segments.filter(s => s.status === 'failed').length\n    const pendingSegments = segments.filter(s => s.status === 'pending').length\n\n    // 计算进度百分比\n    const progress = totalSegments > 0 ? Math.round((completedSegments / totalSegments) * 100) : 0\n\n    // 估算剩余时间（基于已完成片段的平均生成时间）\n    let estimatedRemainingTime = null\n    const completedWithMetadata = segments.filter(s => {\n      if (s.status !== 'completed' || !s.metadata) return false\n      try {\n        const metadata = JSON.parse(s.metadata as string)\n        return metadata.generationTime && metadata.completedAt\n      } catch {\n        return false\n      }\n    })\n\n    if (completedWithMetadata.length > 0) {\n      const avgGenerationTime = completedWithMetadata.reduce((sum, segment) => {\n        try {\n          const metadata = JSON.parse(segment.metadata as string)\n          return sum + (metadata.generationTime || 0)\n        } catch {\n          return sum\n        }\n      }, 0) / completedWithMetadata.length\n\n      const remainingSegments = totalSegments - completedSegments\n      estimatedRemainingTime = Math.round((avgGenerationTime * remainingSegments) / 1000) // 转换为秒\n    }\n\n    // 获取当前处理的片段信息\n    const currentProcessingSegments = segments\n      .filter(s => s.status === 'generating')\n      .map(s => ({\n        index: s.segmentIndex,\n        title: s.title,\n        startTime: s.updatedAt,\n        metadata: s.metadata ? JSON.parse(s.metadata as string) : null\n      }))\n\n    // 计算总视频大小\n    let totalVideoSize = 0\n    let totalVideoDuration = 0\n    segments.forEach(segment => {\n      if (segment.status === 'completed' && segment.metadata) {\n        try {\n          const metadata = JSON.parse(segment.metadata as string)\n          if (metadata.videoSize && typeof metadata.videoSize === 'number') {\n            totalVideoSize += metadata.videoSize\n          }\n        } catch {}\n      }\n      if (segment.duration) {\n        totalVideoDuration += segment.duration\n      }\n    })\n\n    // 获取队列状态\n    const queueStatus = {\n      total: totalSegments,\n      completed: completedSegments,\n      processing: generatingSegments,\n      pending: pendingSegments,\n      failed: failedSegments\n    }\n\n    // 详细的片段信息\n    const detailedSegments = segments.map(segment => {\n      let metadata = null\n      try {\n        metadata = segment.metadata ? JSON.parse(segment.metadata as string) : null\n      } catch {}\n\n      return {\n        id: segment.id,\n        index: segment.segmentIndex,\n        title: segment.title,\n        status: segment.status,\n        duration: segment.duration,\n        hasVideo: !!segment.videoUrl,\n        videoUrl: segment.videoUrl,\n        createdAt: segment.createdAt,\n        updatedAt: segment.updatedAt,\n        metadata: {\n          taskId: metadata?.taskId,\n          provider: metadata?.provider,\n          videoSize: metadata?.videoSize,\n          responseTime: metadata?.responseTime,\n          totalAttempts: metadata?.totalAttempts,\n          generationTime: metadata?.generationTime,\n          completedAt: metadata?.completedAt,\n          error: metadata?.error\n        }\n      }\n    })\n\n    const response = {\n      success: true,\n      episodeId,\n      progress: {\n        percentage: progress,\n        completed: completedSegments,\n        total: totalSegments,\n        estimatedRemainingTime,\n        currentProcessing: currentProcessingSegments,\n        queueStatus\n      },\n      statistics: {\n        totalVideoSize,\n        totalVideoDuration,\n        averageSegmentDuration: totalSegments > 0 ? totalVideoDuration / totalSegments : 0\n      },\n      segments: detailedSegments,\n      lastUpdated: new Date().toISOString()\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('获取视频生成进度失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取进度信息失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgB,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAClD,OAAO;gBAAE;YAAU;YACnB,SAAS;gBAAE,cAAc;YAAM;YAC/B,QAAQ;gBACN,IAAI;gBACJ,cAAc;gBACd,OAAO;gBACP,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,WAAW;YACb;QACF;QAEA,WAAW;QACX,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QAC/E,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,cAAc,MAAM;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACzE,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAE3E,UAAU;QACV,MAAM,WAAW,gBAAgB,IAAI,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,OAAO;QAE7F,yBAAyB;QACzB,IAAI,yBAAyB;QAC7B,MAAM,wBAAwB,SAAS,MAAM,CAAC,CAAA;YAC5C,IAAI,EAAE,MAAM,KAAK,eAAe,CAAC,EAAE,QAAQ,EAAE,OAAO;YACpD,IAAI;gBACF,MAAM,WAAW,KAAK,KAAK,CAAC,EAAE,QAAQ;gBACtC,OAAO,SAAS,cAAc,IAAI,SAAS,WAAW;YACxD,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IAAI,sBAAsB,MAAM,GAAG,GAAG;YACpC,MAAM,oBAAoB,sBAAsB,MAAM,CAAC,CAAC,KAAK;gBAC3D,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ;oBAC5C,OAAO,MAAM,CAAC,SAAS,cAAc,IAAI,CAAC;gBAC5C,EAAE,OAAM;oBACN,OAAO;gBACT;YACF,GAAG,KAAK,sBAAsB,MAAM;YAEpC,MAAM,oBAAoB,gBAAgB;YAC1C,yBAAyB,KAAK,KAAK,CAAC,AAAC,oBAAoB,oBAAqB,MAAM,OAAO;;QAC7F;QAEA,cAAc;QACd,MAAM,4BAA4B,SAC/B,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,cACzB,GAAG,CAAC,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,SAAS;gBACtB,UAAU,EAAE,QAAQ,GAAG,KAAK,KAAK,CAAC,EAAE,QAAQ,IAAc;YAC5D,CAAC;QAEH,UAAU;QACV,IAAI,iBAAiB;QACrB,IAAI,qBAAqB;QACzB,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,QAAQ,EAAE;gBACtD,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,QAAQ;oBAC5C,IAAI,SAAS,SAAS,IAAI,OAAO,SAAS,SAAS,KAAK,UAAU;wBAChE,kBAAkB,SAAS,SAAS;oBACtC;gBACF,EAAE,OAAM,CAAC;YACX;YACA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,sBAAsB,QAAQ,QAAQ;YACxC;QACF;QAEA,SAAS;QACT,MAAM,cAAc;YAClB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,SAAS;YACT,QAAQ;QACV;QAEA,UAAU;QACV,MAAM,mBAAmB,SAAS,GAAG,CAAC,CAAA;YACpC,IAAI,WAAW;YACf,IAAI;gBACF,WAAW,QAAQ,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAc;YACzE,EAAE,OAAM,CAAC;YAET,OAAO;gBACL,IAAI,QAAQ,EAAE;gBACd,OAAO,QAAQ,YAAY;gBAC3B,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,MAAM;gBACtB,UAAU,QAAQ,QAAQ;gBAC1B,UAAU,CAAC,CAAC,QAAQ,QAAQ;gBAC5B,UAAU,QAAQ,QAAQ;gBAC1B,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,UAAU;oBACR,QAAQ,UAAU;oBAClB,UAAU,UAAU;oBACpB,WAAW,UAAU;oBACrB,cAAc,UAAU;oBACxB,eAAe,UAAU;oBACzB,gBAAgB,UAAU;oBAC1B,aAAa,UAAU;oBACvB,OAAO,UAAU;gBACnB;YACF;QACF;QAEA,MAAM,WAAW;YACf,SAAS;YACT;YACA,UAAU;gBACR,YAAY;gBACZ,WAAW;gBACX,OAAO;gBACP;gBACA,mBAAmB;gBACnB;YACF;YACA,YAAY;gBACV;gBACA;gBACA,wBAAwB,gBAAgB,IAAI,qBAAqB,gBAAgB;YACnF;YACA,UAAU;YACV,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}