import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 获取项目列表
export async function GET(request: NextRequest) {
  try {
    const projects = await prisma.project.findMany({
      orderBy: { createdAt: 'desc' },
      take: 20
    })

    return NextResponse.json({
      success: true,
      data: projects
    })

  } catch (error) {
    console.error('获取项目列表失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取项目列表失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 创建新项目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, content } = body

    if (!name) {
      return NextResponse.json(
        { error: '项目名称不能为空' },
        { status: 400 }
      )
    }

    const project = await prisma.project.create({
      data: {
        name,
        description,
        content,
        status: 'created'
      }
    })

    return NextResponse.json({
      success: true,
      data: project
    })

  } catch (error) {
    console.error('创建项目失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '创建项目失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
