import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 获取项目的剧集列表
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id

    // 验证项目存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 获取剧集列表
    const episodes = await prisma.episode.findMany({
      where: { projectId },
      include: {
        plotInfo: true
      },
      orderBy: { orderIndex: 'asc' }
    })

    return NextResponse.json({
      success: true,
      data: episodes
    })

  } catch (error) {
    console.error('获取剧集列表失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取剧集列表失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 创建新剧集
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id
    const body = await request.json()
    const { title, content, orderIndex } = body

    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容不能为空' },
        { status: 400 }
      )
    }

    // 验证项目存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      )
    }

    // 如果没有指定orderIndex，自动计算
    let finalOrderIndex = orderIndex
    if (finalOrderIndex === undefined) {
      const lastEpisode = await prisma.episode.findFirst({
        where: { projectId },
        orderBy: { orderIndex: 'desc' }
      })
      finalOrderIndex = (lastEpisode?.orderIndex || 0) + 1
    }

    const episode = await prisma.episode.create({
      data: {
        projectId,
        title,
        content,
        orderIndex: finalOrderIndex,
        status: 'created'
      }
    })

    return NextResponse.json({
      success: true,
      data: episode
    })

  } catch (error) {
    console.error('创建剧集失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '创建剧集失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
