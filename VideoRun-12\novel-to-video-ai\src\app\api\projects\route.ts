import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取项目列表
export async function GET(request: NextRequest) {
  try {
    const projects = await prisma.project.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        _count: {
          select: {
            characters: true,
            episodes: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: projects,
    })
  } catch (error) {
    console.error('获取项目列表失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目列表失败' },
      { status: 500 }
    )
  }
}

// 创建新项目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description } = body

    if (!name || !name.trim()) {
      return NextResponse.json(
        { success: false, error: '项目名称不能为空' },
        { status: 400 }
      )
    }

    // 检查项目名称是否已存在
    const existingProject = await prisma.project.findFirst({
      where: {
        name: name.trim()
      }
    })

    if (existingProject) {
      return NextResponse.json(
        { success: false, error: '项目名称已存在，请使用其他名称' },
        { status: 400 }
      )
    }

    // 创建项目
    const project = await prisma.project.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
        status: 'created'
      }
    })

    return NextResponse.json({
      success: true,
      data: project,
      message: '项目创建成功'
    })
  } catch (error) {
    console.error('创建项目失败:', error)
    return NextResponse.json(
      { success: false, error: '创建项目失败' },
      { status: 500 }
    )
  }
}
