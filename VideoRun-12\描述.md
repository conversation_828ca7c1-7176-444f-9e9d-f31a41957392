# 项目描述

## 原始需求描述
我想做一个网页版小说转视频ai生成器。上传小说，上传完成后，出现一个 自动分析人物剧情 按钮，点击这个按钮，配置的大模型会干两件事，1，根据剧情内容将所有人物的名称及其角色信息（五官、身份、外貌、性格、隐线）提取出来，放在角色一栏里。2，按原小说章节的划分将完整小说拆分成一个个小故事，并按原章节顺序，分别单独存储在剧集一栏里。存储的格式：故事标题为一级目录，详细剧情为二级。
在剧集一栏 所有的故事按原顺序排列，点击每一个故事即可查看 详情剧情。在故事里面有个 提取剧情信息 按钮，点击 提取剧情信息 按钮 配置的大模型会将故事的三大信息（本集人物，场景信息，事件三要素）提取出来，
特别说一下事件，一个事件都是三要素构成：正常——矛盾冲突——升级事件。因为事件的意思就是：哪几个人在什么地方做什么ai会按原小说章节自动拆分成一个个剧集，并将每个剧集的三大信息提取出来：本集人物，场景信息，事件三要素。三大信息跟角色信息一样，都是为从多维度让ai听懂。另外ai在生成视频的时候是一集一集的生成的。以一集为一个单位。在第一集的后面加入 生成视频按钮，ai将根据多个维度的信息开在创作。

## 角色管理升级需求
角色里的人物用一个头像和名称组成来显示，点击人物头像或名称，打开该人物的人物信息详情页面，里面有两个子项：
1. **角色信息** - 五大角色信息（五官、身份、外貌、性格、隐线），是大模型阅读完小说后，从中提炼总结的，要准确、详细。
2. **形象** - 该人物形象编辑窗口，为后面的连续剧人物一致性准备的。有四块组成：五官、发型、身材、服装。

## AI形象生成功能
在形象标签页中新增AI自动生成功能：
1. **AI一键生成角色形象按钮** - 基于角色信息自动生成详细的形象描述
2. **选择模型按钮** - 为AI生成功能配置驱动模型，支持已配置的DeepSeek、OpenAI、Claude等模型
3. **预览和保存机制** - 生成结果先在预览弹窗中展示，用户确认后可保存到形象编辑区域
4. **四维度生成** - 自动生成五官特征、发型样式、身材特征、服装风格四个维度的详细描述

## 新架构设计
🏠 1. 首页
生成流程介绍
项目简介
📁 2. 项目
项目列表 - 显示所有项目
新建项目 - 创建新项目
项目详情（每个项目下的4个子项）：
  - 上传文件
  - 角色（升级版）
  - 剧集
  - 视频
🤖 3. 模型配置
将当前的AI配置功能移到这里
全局模型设置
👤 4. 账户
暂不开发，预留

🔄 工作流程
用户在"新建项目"中创建项目
在项目的"上传文件"中上传小说
自动识别的结构化数据保存到该项目的"角色"和"剧集"中
在"角色"中查看和编辑角色信息，设置形象一致性
在"视频"中生成最终视频脚本

## 角色管理功能详细设计

### 🎭 角色列表显示
- **卡片式布局** - 每个角色显示为独立卡片
- **头像+名称** - 渐变紫色圆形头像 + 角色名称
- **身份标签** - 显示角色的身份信息
- **简要预览** - 显示性格特点的前两行
- **响应式设计** - 自适应1-4列布局

### 📋 角色详情弹窗
点击角色头像或名称打开详情弹窗，包含两个标签页：

#### 1. 角色信息标签页
AI提取的六大角色信息（已更新）：
- **身份** - 职业、社会地位、背景等身份信息（至少80字）
- **性格** - 性格特征、行为习惯、心理特点等内在特质（至少120字）
- **身材特征** - 身高、体型、体态、肌肉线条等身体特征（至少100字）
- **五官特征** - 脸型、眼睛、鼻子、嘴唇、眉毛等面部特征（至少100字）
- **发型样式** - 发型样式、发色、发长、发质等头发特征（至少80字）
- **服饰风格** - 服装风格、颜色搭配、材质、配饰等穿着特征（至少100字）

#### 2. 形象标签页（连续剧一致性）
为视频生成中的人物形象一致性准备：
- **五官特征** - 眼睛形状、鼻子特征、嘴唇样式、脸型等
- **发型样式** - 发型样式、发色、发长等
- **身材特征** - 身高、体型、体态特征等
- **服装风格** - 服装风格、颜色搭配、配饰等

#### 3. AI形象生成功能（已更新）
- **AI一键生成角色形象** - 基于角色信息生成正、侧、背三视图（不是文字描述）
- **模型选择器** - 支持DeepSeek的所有模型变体（chat、reasoner、v3、coder）
- **三视图展示** - 生成专业的角色设计三视图，用于连续剧人物一致性
- **预览和保存** - 生成结果在预览弹窗中展示，确认后保存

### ⚙️ 编辑功能
- **编辑模式** - 支持角色信息的在线编辑
- **实时保存** - 编辑后可保存更改
- **数据同步** - 编辑的数据实时同步到数据库

## 结构化数据设计
📊 结构化数据设计
👥 角色数据结构
名称 - 角色姓名
五官 - 面部特征描述
外貌 - 整体外貌描述
性格 - 性格特点
身份 - 社会身份/职业
服装 - 服装描述 

📺 剧集数据结构
故事标题 - 一级目录（章节标题）
详细剧情 - 二级目录（完整剧情内容）
剧情信息（点击"提取剧情信息"后获得）：
本集人物 - 该集出现的角色
场景信息 - 故事发生的地点和环境
事件三要素：
正常 - 初始状态
矛盾冲突 - 问题出现
升级事件 - 事件发展

🎬 视频生成
以一集为单位生成
基于多维度信息：角色信息 + 剧情信息
每集有独立的"生成视频"按钮

🔄 工作流程
上传小说 → 自动分析
角色提取 → 填入角色一栏
剧集拆分 → 按章节存储到剧集一栏
剧情分析 → 点击单集"提取剧情信息"
视频生成 → 基于多维度数据生成视频脚本


## 最新更新记录

### 2025-06-17 角色信息结构优化
1. **角色信息字段调整**：
   - 从5项调整为6项：身份、性格、身材特征、五官特征、发型样式、服饰风格
   - 删除了"隐线伏笔"字段，新增"身材特征"、"发型样式"、"服饰风格"字段
   - 每个字段都有最低字数要求，确保生成详细准确的角色信息

2. **AI分析提示词优化**：
   - 大幅增强角色分析提示词，要求生成详细准确的角色信息
   - 明确每个字段的具体要求和最低字数限制
   - 强调基于小说内容准确提取，为后续形象生成提供充分基础

3. **AI形象生成功能升级**：
   - 从生成文字描述改为生成正、侧、背三视图
   - 集成图像生成服务（Stable Diffusion + DALL-E）
   - 支持DeepSeek所有模型变体的自由选择

4. **数据库结构更新**：
   - 更新Character模型字段结构
   - 修复API和前端组件以适应新的字段结构
   - 确保数据一致性和类型安全