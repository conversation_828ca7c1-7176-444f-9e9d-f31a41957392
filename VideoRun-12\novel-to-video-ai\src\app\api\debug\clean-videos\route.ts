import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 调试API：清理指定剧集的所有视频记录
export async function POST(request: NextRequest) {
  try {
    const { episodeId } = await request.json()
    
    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId参数' },
        { status: 400 }
      )
    }

    console.log(`🧹 开始清理剧集 ${episodeId} 的所有视频记录...`)

    // 1. 查找所有相关的视频记录
    const storyVideos = await prisma.storyVideo.findMany({
      where: { episodeId },
      include: {
        segments: true
      }
    })

    console.log(`📊 找到 ${storyVideos.length} 个主视频记录`)

    let totalDeletedSegments = 0
    let totalDeletedVideos = 0

    // 2. 删除所有视频片段和主视频记录
    for (const storyVideo of storyVideos) {
      console.log(`🗑️ 删除视频 ${storyVideo.id}，包含 ${storyVideo.segments.length} 个片段`)
      
      // 删除片段
      const deletedSegments = await prisma.videoSegment.deleteMany({
        where: { storyVideoId: storyVideo.id }
      })
      totalDeletedSegments += deletedSegments.count
      console.log(`   - 删除了 ${deletedSegments.count} 个片段`)
      
      // 删除主视频记录
      await prisma.storyVideo.delete({
        where: { id: storyVideo.id }
      })
      totalDeletedVideos++
      console.log(`   - 删除了主视频记录`)
    }

    // 3. 验证清理结果
    const remainingVideos = await prisma.storyVideo.findMany({
      where: { episodeId }
    })
    
    const remainingSegments = await prisma.videoSegment.findMany({
      where: { episodeId }
    })

    console.log(`✅ 清理完成：`)
    console.log(`   - 删除了 ${totalDeletedVideos} 个主视频记录`)
    console.log(`   - 删除了 ${totalDeletedSegments} 个视频片段`)
    console.log(`   - 剩余主视频记录：${remainingVideos.length}`)
    console.log(`   - 剩余视频片段：${remainingSegments.length}`)

    return NextResponse.json({
      success: true,
      data: {
        deletedVideos: totalDeletedVideos,
        deletedSegments: totalDeletedSegments,
        remainingVideos: remainingVideos.length,
        remainingSegments: remainingSegments.length
      },
      message: `成功清理 ${totalDeletedVideos} 个视频记录和 ${totalDeletedSegments} 个片段`
    })

  } catch (error) {
    console.error('清理视频记录失败:', error)
    return NextResponse.json(
      { success: false, error: '清理失败，请重试' },
      { status: 500 }
    )
  }
}

// GET - 查看指定剧集的视频记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')
    
    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId参数' },
        { status: 400 }
      )
    }

    // 查找所有相关记录
    const storyVideos = await prisma.storyVideo.findMany({
      where: { episodeId },
      include: {
        segments: {
          orderBy: { segmentIndex: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const allSegments = await prisma.videoSegment.findMany({
      where: { episodeId },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: {
        storyVideos: storyVideos.map(video => ({
          id: video.id,
          status: video.status,
          createdAt: video.createdAt,
          segmentCount: video.segments.length,
          segments: video.segments.map(seg => ({
            id: seg.id,
            title: seg.title,
            status: seg.status,
            segmentIndex: seg.segmentIndex,
            createdAt: seg.createdAt
          }))
        })),
        allSegments: allSegments.map(seg => ({
          id: seg.id,
          storyVideoId: seg.storyVideoId,
          title: seg.title,
          status: seg.status,
          segmentIndex: seg.segmentIndex,
          createdAt: seg.createdAt
        })),
        summary: {
          totalStoryVideos: storyVideos.length,
          totalSegments: allSegments.length
        }
      }
    })

  } catch (error) {
    console.error('查看视频记录失败:', error)
    return NextResponse.json(
      { success: false, error: '查看失败，请重试' },
      { status: 500 }
    )
  }
}
