/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/models/route";
exports.ids = ["app/api/models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/models/route.ts */ \"(rsc)/./src/app/api/models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/models/route\",\n        pathname: \"/api/models\",\n        filename: \"route\",\n        bundlePath: \"app/api/models/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\models\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/models/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/models/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n// GET - 获取所有模型配置\nasync function GET() {\n    try {\n        const aiConfigs = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.findMany({\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        console.log('获取到的AI配置:', aiConfigs);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: aiConfigs\n        });\n    } catch (error) {\n        console.error('获取模型列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '获取模型列表失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - 保存或更新模型配置\nasync function POST(request) {\n    try {\n        const modelConfig = await request.json();\n        // 验证必需字段\n        if (!modelConfig.provider || !modelConfig.model || !modelConfig.name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少必需字段'\n            }, {\n                status: 400\n            });\n        }\n        let savedConfig;\n        if (modelConfig.id) {\n            // 更新现有配置\n            savedConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.update({\n                where: {\n                    id: modelConfig.id\n                },\n                data: {\n                    provider: modelConfig.provider,\n                    model: modelConfig.model,\n                    name: modelConfig.name,\n                    description: modelConfig.description,\n                    apiKey: modelConfig.apiKey,\n                    enabled: modelConfig.enabled,\n                    supportsVideo: modelConfig.supportsVideo ?? false,\n                    supportsImage: modelConfig.supportsImage ?? false,\n                    supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,\n                    temperature: modelConfig.temperature ?? 0.7,\n                    status: 'disconnected' // 重置状态，需要重新测试\n                }\n            });\n        } else {\n            // 创建新配置\n            savedConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.create({\n                data: {\n                    provider: modelConfig.provider,\n                    model: modelConfig.model,\n                    name: modelConfig.name,\n                    description: modelConfig.description || '',\n                    apiKey: modelConfig.apiKey,\n                    enabled: modelConfig.enabled ?? true,\n                    supportsVideo: modelConfig.supportsVideo ?? false,\n                    supportsImage: modelConfig.supportsImage ?? false,\n                    supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,\n                    temperature: modelConfig.temperature ?? 0.7,\n                    status: 'disconnected'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: savedConfig\n        });\n    } catch (error) {\n        console.error('保存模型配置失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '保存模型配置失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/models/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPVxuICBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Froute&page=%2Fapi%2Fmodels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();