const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzeCurrentMicroEpisodeStatus() {
  try {
    console.log('🎬 分析当前微剧集制作现状...\n');
    
    // 1. 检查现有剧集和视频
    console.log('📊 当前系统状态分析:');
    
    const episodes = await prisma.episode.findMany({
      include: {
        plotInfo: true,
        videoGenerations: true
      }
    });

    // 获取所有故事视频
    const storyVideos = await prisma.storyVideo.findMany({
      include: {
        segments: true
      }
    });
    
    console.log(`📖 总剧集数: ${episodes.length}`);
    
    for (const episode of episodes) {
      console.log(`\n🎭 剧集: ${episode.title}`);

      // 查找该剧集的故事视频
      const episodeStoryVideos = storyVideos.filter(sv => sv.episodeId === episode.id);
      console.log(`   故事视频数: ${episodeStoryVideos.length}`);
      console.log(`   视频生成记录数: ${episode.videoGenerations.length}`);
      console.log(`   剧情信息: ${episode.plotInfo ? '有' : '无'}`);

      for (const storyVideo of episodeStoryVideos) {
        console.log(`\n   📹 故事视频 ID: ${storyVideo.id}`);
        console.log(`      状态: ${storyVideo.status}`);
        console.log(`      片段数: ${storyVideo.segments.length}`);
        console.log(`      合并视频URL: ${storyVideo.mergedVideoUrl ? '有' : '无'}`);
        console.log(`      总时长: ${storyVideo.totalDuration || '未设置'}秒`);

        // 分析片段详情
        if (storyVideo.segments.length > 0) {
          console.log(`\n      📋 片段详情:`);
          let totalActualDuration = 0;
          let completedSegments = 0;

          for (const segment of storyVideo.segments) {
            console.log(`         片段${segment.segmentIndex}: ${segment.title}`);
            console.log(`           状态: ${segment.status}`);
            console.log(`           时长: ${segment.duration}秒`);
            console.log(`           类型: ${segment.segmentType || '未分类'}`);
            console.log(`           视频URL: ${segment.videoUrl ? '有' : '无'}`);

            if (segment.status === 'completed' && segment.duration) {
              totalActualDuration += segment.duration;
              completedSegments++;
            }
          }

          console.log(`\n      📊 统计信息:`);
          console.log(`         已完成片段: ${completedSegments}/${storyVideo.segments.length}`);
          console.log(`         实际总时长: ${totalActualDuration}秒`);
          console.log(`         平均片段时长: ${completedSegments > 0 ? (totalActualDuration / completedSegments).toFixed(1) : 0}秒`);
        }
      }
    }
    
    // 2. 分析微剧集方案实施情况
    console.log('\n\n🎯 微剧集方案实施分析:');
    
    // 检查片段时长分布
    const allSegments = await prisma.videoSegment.findMany({
      where: { status: 'completed' }
    });
    
    console.log(`\n📏 片段时长分析 (基于${allSegments.length}个已完成片段):`);
    
    const durationStats = {
      '1-5秒': 0,
      '6-8秒': 0,
      '9-15秒': 0,
      '16-30秒': 0,
      '30秒+': 0
    };
    
    let totalDuration = 0;
    
    for (const segment of allSegments) {
      const duration = segment.duration || 0;
      totalDuration += duration;
      
      if (duration <= 5) durationStats['1-5秒']++;
      else if (duration <= 8) durationStats['6-8秒']++;
      else if (duration <= 15) durationStats['9-15秒']++;
      else if (duration <= 30) durationStats['16-30秒']++;
      else durationStats['30秒+']++;
    }
    
    for (const [range, count] of Object.entries(durationStats)) {
      const percentage = allSegments.length > 0 ? ((count / allSegments.length) * 100).toFixed(1) : 0;
      console.log(`   ${range}: ${count}个 (${percentage}%)`);
    }
    
    const avgDuration = allSegments.length > 0 ? (totalDuration / allSegments.length).toFixed(1) : 0;
    console.log(`   平均时长: ${avgDuration}秒`);
    
    // 3. 微剧集方案对比分析
    console.log('\n\n📋 微剧集方案对比分析:');
    
    console.log('\n🎯 理想微剧集方案:');
    console.log('   片段时长: 5-8秒');
    console.log('   片段数量: 6-8个');
    console.log('   总时长: 42-56秒');
    console.log('   片段类型: 环境建立→角色登场→冲突引入→动作高潮→情感转折→对话互动→悬念结尾');
    
    console.log('\n📊 当前实际情况:');
    console.log(`   平均片段时长: ${avgDuration}秒`);
    console.log(`   符合5-8秒标准的片段: ${durationStats['6-8秒']}个 (${((durationStats['6-8秒'] / allSegments.length) * 100).toFixed(1)}%)`);
    
    // 检查片段类型分布
    const segmentTypes = await prisma.videoSegment.groupBy({
      by: ['segmentType'],
      _count: { segmentType: true }
    });
    
    console.log('\n🎭 片段类型分布:');
    for (const type of segmentTypes) {
      console.log(`   ${type.segmentType || '未分类'}: ${type._count.segmentType}个`);
    }
    
    // 4. 视频合并功能分析
    console.log('\n\n🔗 视频合并功能分析:');
    
    const mergedVideos = await prisma.storyVideo.findMany({
      where: { mergedVideoUrl: { not: null } }
    });
    
    console.log(`📹 已合并的完整视频: ${mergedVideos.length}个`);
    
    for (const video of mergedVideos) {
      console.log(`   视频ID: ${video.id}`);
      console.log(`   总时长: ${video.totalDuration}秒`);
      console.log(`   合并URL: ${video.mergedVideoUrl ? '有' : '无'}`);
    }
    
    // 5. 与传统方案的差异分析
    console.log('\n\n🔄 与传统方案的差异分析:');
    
    console.log('\n❌ 传统方案问题:');
    console.log('   - 技术限制: AI模型无法生成30秒+长视频');
    console.log('   - 质量问题: 长视频质量难以保证');
    console.log('   - 一致性: 长视频中角色一致性难以维持');
    console.log('   - 成本高: 失败重试成本高');
    
    console.log('\n✅ 微剧集方案优势:');
    console.log('   - 技术可行: 5-8秒片段完全支持');
    console.log('   - 质量保证: 每个片段都是精品');
    console.log('   - 一致性好: 短片段更易控制角色一致性');
    console.log('   - 成本可控: 单片段失败影响小');
    console.log('   - 并行生成: 多片段同时生成，效率高');
    console.log('   - 灵活编辑: 可单独替换或调整片段');
    
    // 6. 当前实施状态评估
    console.log('\n\n📈 当前实施状态评估:');
    
    const microEpisodeCompliance = {
      duration: durationStats['6-8秒'] / Math.max(allSegments.length, 1) * 100,
      segmentation: allSegments.length > 0 ? 100 : 0,
      merging: mergedVideos.length > 0 ? 100 : 0
    };
    
    console.log(`🎯 微剧集方案实施度:`);
    console.log(`   片段时长合规率: ${microEpisodeCompliance.duration.toFixed(1)}%`);
    console.log(`   片段化实现: ${microEpisodeCompliance.segmentation.toFixed(1)}%`);
    console.log(`   视频合并功能: ${microEpisodeCompliance.merging.toFixed(1)}%`);
    
    const overallCompliance = (microEpisodeCompliance.duration + microEpisodeCompliance.segmentation + microEpisodeCompliance.merging) / 3;
    console.log(`   总体实施度: ${overallCompliance.toFixed(1)}%`);
    
    // 7. 改进建议
    console.log('\n\n💡 改进建议:');
    
    if (microEpisodeCompliance.duration < 80) {
      console.log('🔧 片段时长优化:');
      console.log('   - 调整AI模型参数，确保生成5-8秒视频');
      console.log('   - 优化提示词模板，明确时长要求');
      console.log('   - 实施时长验证机制');
    }
    
    if (microEpisodeCompliance.merging < 100) {
      console.log('🔧 视频合并功能:');
      console.log('   - 完善FFmpeg集成');
      console.log('   - 实现智能转场效果');
      console.log('   - 添加音效和字幕支持');
    }
    
    console.log('\n🚀 下一步发展方向:');
    console.log('   1. 完善视频合并功能 (Phase 2)');
    console.log('   2. 优化用户界面体验 (Phase 3)');
    console.log('   3. 实现智能剧情分析 (Phase 4)');
    console.log('   4. 添加音效和字幕系统');
    
    console.log('\n🎬 微剧集制作现状分析完成！');
    
  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeCurrentMicroEpisodeStatus();
