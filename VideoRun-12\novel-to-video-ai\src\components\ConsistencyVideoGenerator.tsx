'use client'

import { useState } from 'react'
import { <PERSON>, Settings, Shield, Image, Sparkles, Clock, CheckCircle, AlertTriangle, Loader2, BarChart3 } from 'lucide-react'
import ConsistencyValidator from './ConsistencyValidator'

interface ConsistencyVideoGeneratorProps {
  episodeId: string
  episodeTitle: string
  onVideoGenerated?: (videoData: any) => void
}

export default function ConsistencyVideoGenerator({ 
  episodeId, 
  episodeTitle, 
  onVideoGenerated 
}: ConsistencyVideoGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationResult, setGenerationResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // 一致性验证相关状态
  const [showValidator, setShowValidator] = useState(false)
  const [validationResults, setValidationResults] = useState<any>(null)
  
  // 一致性配置
  const [consistencyMode, setConsistencyMode] = useState<'strict' | 'balanced' | 'creative'>('balanced')
  const [includeReferenceImages, setIncludeReferenceImages] = useState(true)
  const [style, setStyle] = useState<'cinematic' | 'documentary' | 'artistic'>('cinematic')
  const [quality, setQuality] = useState<'standard' | 'high' | 'cinematic'>('high')
  const [customEnhancement, setCustomEnhancement] = useState('')

  // 生成一致性视频
  const handleGenerateVideo = async () => {
    try {
      setIsGenerating(true)
      setError(null)
      setGenerationResult(null)

      console.log('🎬 开始生成一致性视频...')

      const response = await fetch('/api/ai/generate-video-with-consistency', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          consistencyMode,
          includeReferenceImages,
          style,
          quality,
          customEnhancement: customEnhancement.trim() || undefined
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '生成失败')
      }

      if (result.success) {
        setGenerationResult(result.data)
        onVideoGenerated?.(result.data)
        setShowValidator(true) // 自动显示验证器
        console.log('✅ 一致性视频生成成功:', result.data)
      } else {
        throw new Error(result.error || '生成失败')
      }

    } catch (error) {
      console.error('❌ 一致性视频生成失败:', error)
      setError(error instanceof Error ? error.message : '生成失败，请重试')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      {/* 标题 */}
      <div className="flex items-center mb-6">
        <Shield className="text-purple-600 mr-3" size={24} />
        <div>
          <h3 className="text-lg font-medium text-gray-900">一致性视频生成</h3>
          <p className="text-sm text-gray-600">为《{episodeTitle}》生成具有角色一致性约束的视频</p>
        </div>
      </div>

      {/* 一致性配置 */}
      <div className="space-y-6 mb-6">
        {/* 一致性模式 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Shield className="inline mr-1" size={14} />
            一致性模式
          </label>
          <div className="grid grid-cols-3 gap-3">
            {[
              { value: 'strict', label: '严格模式', desc: '零容忍偏差', color: 'red' },
              { value: 'balanced', label: '平衡模式', desc: '高保真度', color: 'blue' },
              { value: 'creative', label: '创意模式', desc: '艺术自由', color: 'green' }
            ].map(mode => (
              <button
                key={mode.value}
                onClick={() => setConsistencyMode(mode.value as any)}
                className={`p-3 rounded-lg border text-left transition-colors ${
                  consistencyMode === mode.value
                    ? `border-${mode.color}-500 bg-${mode.color}-50 text-${mode.color}-700`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-sm">{mode.label}</div>
                <div className="text-xs text-gray-500">{mode.desc}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 参考图像 */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={includeReferenceImages}
              onChange={(e) => setIncludeReferenceImages(e.target.checked)}
              className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
            />
            <Image className="ml-2 mr-1" size={14} />
            <span className="text-sm font-medium text-gray-700">使用参考图像约束</span>
          </label>
          <p className="text-xs text-gray-500 ml-6">
            启用后将使用项目角色库中的参考图像进行视觉一致性约束
          </p>
        </div>

        {/* 视频风格 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">视频风格</label>
            <select
              value={style}
              onChange={(e) => setStyle(e.target.value as any)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="cinematic">电影级</option>
              <option value="documentary">纪录片</option>
              <option value="artistic">艺术风格</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">视频质量</label>
            <select
              value={quality}
              onChange={(e) => setQuality(e.target.value as any)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="standard">标准质量</option>
              <option value="high">高质量</option>
              <option value="cinematic">电影级质量</option>
            </select>
          </div>
        </div>

        {/* 自定义增强 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Sparkles className="inline mr-1" size={14} />
            自定义增强要求
          </label>
          <textarea
            value={customEnhancement}
            onChange={(e) => setCustomEnhancement(e.target.value)}
            placeholder="例如：强调角色表情变化，增加特写镜头..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500"
            rows={3}
          />
        </div>
      </div>

      {/* 生成按钮 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          <Clock className="inline mr-1" size={14} />
          预计生成时间：2-5分钟
        </div>
        <button
          onClick={handleGenerateVideo}
          disabled={isGenerating}
          className={`inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors ${
            isGenerating
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-purple-600 text-white hover:bg-purple-700'
          }`}
        >
          {isGenerating ? (
            <>
              <Loader2 className="animate-spin mr-2" size={16} />
              生成中...
            </>
          ) : (
            <>
              <Play className="mr-2" size={16} />
              生成一致性视频
            </>
          )}
        </button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <AlertTriangle className="text-red-500 mr-2" size={16} />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* 生成结果 */}
      {generationResult && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center mb-3">
            <CheckCircle className="text-green-600 mr-2" size={20} />
            <h4 className="font-medium text-green-800">一致性视频生成成功</h4>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-gray-600">生成ID：</span>
                <span className="font-mono text-xs">{generationResult.generationId}</span>
              </div>
              <div>
                <span className="text-gray-600">一致性模式：</span>
                <span className="font-medium">{generationResult.consistencyInfo.mode}</span>
              </div>
              <div>
                <span className="text-gray-600">角色数量：</span>
                <span className="font-medium">{generationResult.consistencyInfo.characterCount}</span>
              </div>
              <div>
                <span className="text-gray-600">参考图像：</span>
                <span className="font-medium">{generationResult.consistencyInfo.referenceImageCount} 个</span>
              </div>
            </div>
            
            <div className="mt-3">
              <span className="text-gray-600">一致性特性：</span>
              <div className="flex flex-wrap gap-2 mt-1">
                {Object.entries(generationResult.consistencyInfo.consistencyFeatures).map(([key, enabled]) => (
                  enabled && (
                    <span key={key} className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      <CheckCircle className="mr-1" size={10} />
                      {key === 'characterDNA' ? '角色DNA' :
                       key === 'sceneDNA' ? '场景DNA' :
                       key === 'dualConstraints' ? '双重约束' :
                       key === 'validationProtocol' ? '验证协议' : key}
                    </span>
                  )
                ))}
              </div>
            </div>

            <div className="mt-3 p-2 bg-white rounded border">
              <span className="text-gray-600 text-xs">生成的提示词长度：</span>
              <span className="font-medium text-xs">{generationResult.prompt.length} 字符</span>
              <details className="mt-1">
                <summary className="text-xs text-blue-600 cursor-pointer">查看提示词</summary>
                <pre className="text-xs text-gray-600 mt-1 whitespace-pre-wrap max-h-32 overflow-y-auto">
                  {generationResult.prompt}
                </pre>
              </details>
            </div>

            {/* 一致性验证控制 */}
            <div className="mt-3 flex items-center justify-between">
              <span className="text-xs text-gray-600">一致性验证：</span>
              <button
                onClick={() => setShowValidator(!showValidator)}
                className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  showValidator
                    ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <BarChart3 className="mr-1" size={12} />
                {showValidator ? '隐藏验证器' : '显示验证器'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 一致性验证器 */}
      {generationResult && showValidator && (
        <div className="mt-6">
          <ConsistencyValidator
            generationId={generationResult.generationId}
            onValidationComplete={(results) => {
              setValidationResults(results)
              console.log('✅ 一致性验证完成:', results)
            }}
          />
        </div>
      )}
    </div>
  )
}
