const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 真实的豆包TTS认证信息
const APP_ID = '**********';
const ACCESS_TOKEN = 'b3nfCelq9tf4Lfs4HfPd8wSRS-xLwJ5_';
const SECRET_KEY = '_wIm8vP8uqWW_FCEwkrzEJpJj_2pUhGA';

async function updateDoubaoTTSConfig() {
  try {
    console.log('🔄 更新豆包TTS配置...');
    
    // 查找现有的豆包TTS配置
    const existingConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true
      }
    });
    
    if (existingConfig) {
      console.log('✅ 找到现有豆包TTS配置:', existingConfig.name);
      
      // 更新配置为真实的认证信息
      const updatedConfig = await prisma.aIConfig.update({
        where: { id: existingConfig.id },
        data: {
          apiKey: ACCESS_TOKEN, // 使用真实的Access Token
          model: APP_ID, // 使用真实的AppID
          name: '豆包语音合成 (已验证)',
          description: '豆包大模型语音合成服务，使用真实认证信息，已验证可用',
          enabled: true,
          supportsTTS: true,
          status: 'connected'
        }
      });
      
      console.log('✅ 豆包TTS配置已更新:');
      console.log(`   ID: ${updatedConfig.id}`);
      console.log(`   名称: ${updatedConfig.name}`);
      console.log(`   AppID (model): ${updatedConfig.model}`);
      console.log(`   Access Token (apiKey): ${updatedConfig.apiKey.substring(0, 20)}...`);
      console.log(`   启用状态: ${updatedConfig.enabled}`);
      console.log(`   支持TTS: ${updatedConfig.supportsTTS}`);
      
    } else {
      console.log('❌ 未找到现有豆包TTS配置，创建新配置...');
      
      // 创建新的豆包TTS配置
      const newConfig = await prisma.aIConfig.create({
        data: {
          provider: 'doubao',
          apiKey: ACCESS_TOKEN,
          model: APP_ID,
          name: '豆包语音合成 (已验证)',
          description: '豆包大模型语音合成服务，使用真实认证信息，已验证可用',
          enabled: true,
          supportsVideo: false,
          supportsImage: false,
          supportsImageToVideo: false,
          supportsTTS: true,
          temperature: 0.7,
          maxTokens: 4000,
          topP: 0.9,
          status: 'connected'
        }
      });
      
      console.log('✅ 已创建新的豆包TTS配置:', newConfig.name);
    }
    
    // 暂时禁用Edge TTS，确保优先使用豆包TTS
    const edgeConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'edge-tts',
        supportsTTS: true
      }
    });
    
    if (edgeConfig) {
      await prisma.aIConfig.update({
        where: { id: edgeConfig.id },
        data: {
          enabled: false // 暂时禁用Edge TTS
        }
      });
      console.log('⚠️ 已暂时禁用Edge TTS，优先使用豆包TTS');
    }
    
    // 验证更新后的配置
    console.log('\n🔍 验证更新后的TTS配置...');
    
    const currentTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'desc' }
      ]
    });
    
    if (currentTTSConfig) {
      console.log('✅ 当前活跃的TTS配置:');
      console.log(`   名称: ${currentTTSConfig.name}`);
      console.log(`   提供商: ${currentTTSConfig.provider}`);
      console.log(`   AppID: ${currentTTSConfig.model}`);
      console.log(`   Access Token: ${currentTTSConfig.apiKey.substring(0, 20)}...`);
    }
    
    console.log('\n🎉 豆包TTS配置更新完成！');
    console.log('现在系统将优先使用豆包TTS进行语音合成。');
    
  } catch (error) {
    console.error('❌ 更新豆包TTS配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateDoubaoTTSConfig();
