{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\n// 获取单个项目详情\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n\n    const project = await prisma.project.findUnique({\n      where: { id },\n      include: {\n        characters: {\n          orderBy: { createdAt: 'asc' }\n        },\n        episodes: {\n          orderBy: { createdAt: 'asc' },\n          include: {\n            plotInfo: true\n          }\n        }\n      }\n    })\n\n    if (!project) {\n      return NextResponse.json(\n        { success: false, error: '项目不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 解析角色的生成图像数据\n    const processedProject = {\n      ...project,\n      characters: project.characters.map(character => ({\n        ...character,\n        generatedImages: character.generatedImages\n          ? JSON.parse(character.generatedImages)\n          : null\n      }))\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: processedProject,\n    })\n  } catch (error) {\n    console.error('获取项目详情失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取项目详情失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 更新项目\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const body = await request.json()\n    const { name, description, status } = body\n\n    // 检查项目是否存在\n    const existingProject = await prisma.project.findUnique({\n      where: { id }\n    })\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { success: false, error: '项目不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 如果更新名称，检查是否与其他项目重名\n    if (name && name.trim() !== existingProject.name) {\n      const duplicateProject = await prisma.project.findFirst({\n        where: {\n          name: name.trim(),\n          id: { not: id }\n        }\n      })\n\n      if (duplicateProject) {\n        return NextResponse.json(\n          { success: false, error: '项目名称已存在，请使用其他名称' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // 更新项目\n    const updatedProject = await prisma.project.update({\n      where: { id },\n      data: {\n        ...(name && { name: name.trim() }),\n        ...(description !== undefined && { description: description?.trim() || null }),\n        ...(status && { status }),\n        updatedAt: new Date()\n      }\n    })\n\n    return NextResponse.json({\n      success: true,\n      data: updatedProject,\n      message: '项目更新成功'\n    })\n  } catch (error) {\n    console.error('更新项目失败:', error)\n    return NextResponse.json(\n      { success: false, error: '更新项目失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 删除项目\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n\n    // 检查项目是否存在\n    const existingProject = await prisma.project.findUnique({\n      where: { id }\n    })\n\n    if (!existingProject) {\n      return NextResponse.json(\n        { success: false, error: '项目不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 删除项目（级联删除相关数据）\n    await prisma.project.delete({\n      where: { id }\n    })\n\n    return NextResponse.json({\n      success: true,\n      message: '项目删除成功'\n    })\n  } catch (error) {\n    console.error('删除项目失败:', error)\n    return NextResponse.json(\n      { success: false, error: '删除项目失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,YAAY;oBACV,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;gBACA,UAAU;oBACR,SAAS;wBAAE,WAAW;oBAAM;oBAC5B,SAAS;wBACP,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,MAAM,mBAAmB;YACvB,GAAG,OAAO;YACV,YAAY,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAA,YAAa,CAAC;oBAC/C,GAAG,SAAS;oBACZ,iBAAiB,UAAU,eAAe,GACtC,KAAK,KAAK,CAAC,UAAU,eAAe,IACpC;gBACN,CAAC;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG;QAEtC,WAAW;QACX,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,QAAQ,KAAK,IAAI,OAAO,gBAAgB,IAAI,EAAE;YAChD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACtD,OAAO;oBACL,MAAM,KAAK,IAAI;oBACf,IAAI;wBAAE,KAAK;oBAAG;gBAChB;YACF;YAEA,IAAI,kBAAkB;gBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAkB,GAC3C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO;QACP,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ,GAAI,QAAQ;oBAAE,MAAM,KAAK,IAAI;gBAAG,CAAC;gBACjC,GAAI,gBAAgB,aAAa;oBAAE,aAAa,aAAa,UAAU;gBAAK,CAAC;gBAC7E,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,WAAW,IAAI;YACjB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,WAAW;QACX,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE;YAAG;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}