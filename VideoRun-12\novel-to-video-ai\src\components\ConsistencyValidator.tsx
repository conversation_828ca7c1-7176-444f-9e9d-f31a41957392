'use client'

import { useState } from 'react'
import { Shield, CheckCircle, AlertTriangle, XCircle, Play, BarChart3, Eye, RefreshCw } from 'lucide-react'

interface ConsistencyValidatorProps {
  generationId: string
  videoUrl?: string
  onValidationComplete?: (results: any) => void
}

export default function ConsistencyValidator({ 
  generationId, 
  videoUrl, 
  onValidationComplete 
}: ConsistencyValidatorProps) {
  const [isValidating, setIsValidating] = useState(false)
  const [validationResults, setValidationResults] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  
  // 验证配置
  const [validationType, setValidationType] = useState<'full' | 'character' | 'scene'>('full')
  const [strictMode, setStrictMode] = useState(false)

  // 执行一致性验证
  const handleValidateConsistency = async () => {
    try {
      setIsValidating(true)
      setError(null)
      setValidationResults(null)

      console.log('🔍 开始一致性验证...')

      const response = await fetch('/api/ai/validate-consistency', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationId,
          videoUrl,
          validationType,
          strictMode
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '验证失败')
      }

      if (result.success) {
        setValidationResults(result.data)
        onValidationComplete?.(result.data)
        console.log('✅ 一致性验证成功:', result.data)
      } else {
        throw new Error(result.error || '验证失败')
      }

    } catch (error) {
      console.error('❌ 一致性验证失败:', error)
      setError(error instanceof Error ? error.message : '验证失败，请重试')
    } finally {
      setIsValidating(false)
    }
  }

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 bg-green-100'
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  // 获取评分文本
  const getScoreText = (score: number) => {
    if (score >= 0.9) return '优秀'
    if (score >= 0.7) return '良好'
    if (score >= 0.5) return '一般'
    return '需改进'
  }

  // 获取问题严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      {/* 标题 */}
      <div className="flex items-center mb-6">
        <Shield className="text-purple-600 mr-3" size={24} />
        <div>
          <h3 className="text-lg font-medium text-gray-900">一致性验证</h3>
          <p className="text-sm text-gray-600">验证生成的视频是否符合角色和场景一致性约束</p>
        </div>
      </div>

      {/* 验证配置 */}
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-2 gap-4">
          {/* 验证类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">验证类型</label>
            <select
              value={validationType}
              onChange={(e) => setValidationType(e.target.value as any)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="full">完整验证</option>
              <option value="character">仅角色验证</option>
              <option value="scene">仅场景验证</option>
            </select>
          </div>

          {/* 严格模式 */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={strictMode}
                onChange={(e) => setStrictMode(e.target.checked)}
                className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span className="ml-2 text-sm font-medium text-gray-700">严格模式</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">
              启用后将使用更严格的一致性标准
            </p>
          </div>
        </div>

        {/* 验证按钮 */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            生成ID: <span className="font-mono text-xs">{generationId}</span>
          </div>
          <button
            onClick={handleValidateConsistency}
            disabled={isValidating}
            className={`inline-flex items-center px-4 py-2 rounded-md font-medium transition-colors ${
              isValidating
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
          >
            {isValidating ? (
              <>
                <RefreshCw className="animate-spin mr-2" size={16} />
                验证中...
              </>
            ) : (
              <>
                <Shield className="mr-2" size={16} />
                开始验证
              </>
            )}
          </button>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <XCircle className="text-red-500 mr-2" size={16} />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* 验证结果 */}
      {validationResults && (
        <div className="space-y-6">
          {/* 总体评分 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">总体一致性评分</h4>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreColor(validationResults.summary.overallScore)}`}>
                {(validationResults.summary.overallScore * 100).toFixed(1)}% - {getScoreText(validationResults.summary.overallScore)}
              </span>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {(validationResults.summary.characterConsistency * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">角色一致性</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {(validationResults.summary.sceneConsistency * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">场景一致性</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {validationResults.summary.issuesFound}
                </div>
                <div className="text-xs text-gray-500">发现问题</div>
              </div>
            </div>
          </div>

          {/* 详细结果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 角色验证结果 */}
            {validationResults.results.characterValidation && (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <CheckCircle className="text-green-600 mr-2" size={16} />
                  <h5 className="font-medium text-gray-900">角色一致性</h5>
                </div>
                
                <div className="space-y-2">
                  {validationResults.results.characterValidation.characterScores.map((char: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-700">{char.characterName}</span>
                      <div className="flex items-center space-x-2">
                        {char.hasProjectReference && (
                          <span className="text-xs text-blue-600">已关联</span>
                        )}
                        <span className={`text-xs px-2 py-1 rounded ${getScoreColor(char.score)}`}>
                          {(char.score * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 场景验证结果 */}
            {validationResults.results.sceneValidation && (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Eye className="text-blue-600 mr-2" size={16} />
                  <h5 className="font-medium text-gray-900">场景一致性</h5>
                </div>
                
                <div className="space-y-2">
                  {validationResults.results.sceneValidation.sceneScores.map((scene: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-700">{scene.sceneName}</span>
                      <span className={`text-xs px-2 py-1 rounded ${getScoreColor(scene.score)}`}>
                        {(scene.score * 100).toFixed(0)}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 发现的问题 */}
          {validationResults.results.issuesFound && validationResults.results.issuesFound.length > 0 && (
            <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
              <div className="flex items-center mb-3">
                <AlertTriangle className="text-orange-600 mr-2" size={16} />
                <h5 className="font-medium text-orange-800">发现的问题</h5>
              </div>
              
              <div className="space-y-2">
                {validationResults.results.issuesFound.map((issue: any, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(issue.severity)}`}>
                      {issue.severity}
                    </span>
                    <span className="text-sm text-gray-700 flex-1">{issue.description}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 改进建议 */}
          {validationResults.results.recommendations && validationResults.results.recommendations.length > 0 && (
            <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
              <div className="flex items-center mb-3">
                <BarChart3 className="text-blue-600 mr-2" size={16} />
                <h5 className="font-medium text-blue-800">改进建议</h5>
              </div>
              
              <div className="space-y-2">
                {validationResults.results.recommendations.map((rec: any, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                      rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {rec.priority}
                    </span>
                    <span className="text-sm text-gray-700 flex-1">{rec.description}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 验证详情 */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>验证ID: {validationResults.validationId}</p>
            <p>验证类型: {validationResults.validationType}</p>
            <p>严格模式: {validationResults.strictMode ? '是' : '否'}</p>
          </div>
        </div>
      )}
    </div>
  )
}
