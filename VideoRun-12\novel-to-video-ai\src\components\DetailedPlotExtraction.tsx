'use client'

import { useState, useEffect } from 'react'
import { X, Users, MapPin, Zap, Play, Settings, Eye, RefreshCw, Sparkles, ChevronDown, Save, Shield, AlertTriangle, CheckCircle } from 'lucide-react'
import ModelSelector from './ModelSelector'

interface Character {
  id: string
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
  // 新增：一致性相关字段
  isKnownCharacter?: boolean
  consistencyInfo?: {
    matchedCharacterId?: string
    consistencyMatch?: number
    differences?: string[]
    consistencyConstraints?: string
  }
}

interface Scene {
  id: string
  location: string
  description: string
  atmosphere: string
}

interface PlotSequence {
  sequenceId: string
  action: string
  emotion: string
  keyMoments: string[]
}

interface DetailedPlotExtractionProps {
  episodeId: string
  episodeTitle: string
  episodeContent: string
  isOpen: boolean
  onClose: () => void
  onGenerateStoryVideo?: (episodeId: string, prompt: string, modelId?: string) => void
  videoGenerationMode?: 'auto' | 'manual'
  onVideoGenerationModeChange?: (mode: 'auto' | 'manual') => void
}

export default function DetailedPlotExtraction({
  episodeId,
  episodeTitle,
  episodeContent,
  isOpen,
  onClose,
  onGenerateStoryVideo,
  videoGenerationMode = 'manual',
  onVideoGenerationModeChange
}: DetailedPlotExtractionProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [selectedModel, setSelectedModel] = useState('')
  const [customPrompt, setCustomPrompt] = useState('')
  const [isCustomPromptExpanded, setIsCustomPromptExpanded] = useState(false)
  
  // 提取的剧情信息
  const [characters, setCharacters] = useState<Character[]>([])
  const [scenes, setScenes] = useState<Scene[]>([])
  const [plotSequences, setPlotSequences] = useState<PlotSequence[]>([])
  const [emotionalArc, setEmotionalArc] = useState('')
  const [generatedPrompt, setGeneratedPrompt] = useState('')

  // 分析剧情信息
  const analyzeDetailedPlot = async () => {
    setIsAnalyzing(true)
    try {
      const response = await fetch('/api/ai/analyze-detailed-plot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          episodeContent,
          customPrompt
        })
      })

      if (!response.ok) {
        throw new Error('分析失败')
      }

      const result = await response.json()
      
      if (result.success) {
        setCharacters(result.data.characters || [])
        setScenes(result.data.scenes || [])
        setPlotSequences(result.data.plotSequences || [])
        setEmotionalArc(result.data.emotionalArc || '')
        setGeneratedPrompt(result.data.generatedPrompt || '')
      } else {
        alert('分析失败：' + result.error)
      }
    } catch (error) {
      console.error('分析剧情失败:', error)
      alert('分析剧情失败，请重试')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 保存增强提示词
  const saveCustomPrompt = async () => {
    try {
      const response = await fetch('/api/ai/save-custom-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          customPrompt
        })
      })

      if (!response.ok) {
        throw new Error('保存失败')
      }

      const result = await response.json()
      if (result.success) {
        alert('增强提示词保存成功！')
      } else {
        alert('保存失败：' + result.error)
      }
    } catch (error) {
      console.error('保存增强提示词失败:', error)
      alert('保存增强提示词失败，请重试')
    }
  }

  // 生成剧情视频
  const handleGenerateStoryVideo = async () => {
    if (!selectedModel) {
      alert('请先选择AI模型')
      return
    }

    if (!generatedPrompt) {
      alert('请先分析剧情信息')
      return
    }

    setIsAnalyzing(true)
    try {
      if (onGenerateStoryVideo) {
        await onGenerateStoryVideo(episodeId, generatedPrompt, selectedModel)
      }
    } catch (error) {
      console.error('生成剧情视频失败:', error)
      alert('生成剧情视频失败，请重试')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 加载已保存的自定义提示词
  const loadSavedCustomPrompt = async () => {
    try {
      const response = await fetch(`/api/ai/save-custom-prompt?episodeId=${episodeId}`)
      const data = await response.json()

      if (data.success) {
        setCustomPrompt(data.data.customPrompt || '')
      }
    } catch (error) {
      console.error('加载保存的自定义提示词失败:', error)
    }
  }

  // 加载已保存的详细剧情信息
  const loadSavedPlotInfo = async () => {
    try {
      const response = await fetch(`/api/ai/analyze-detailed-plot?episodeId=${episodeId}`)
      const data = await response.json()

      if (data.success) {
        setCharacters(data.data.characters || [])
        setScenes(data.data.scenes || [])
        setPlotSequences(data.data.plotSequences || [])
        setEmotionalArc(data.data.emotionalArc || '')
        setGeneratedPrompt(data.data.generatedPrompt || '')
        return true // 表示成功加载了保存的数据
      }
      return false // 表示没有保存的数据
    } catch (error) {
      console.error('加载保存的剧情信息失败:', error)
      return false
    }
  }

  // 初始化时先尝试加载保存的信息，如果没有则自动分析
  useEffect(() => {
    if (isOpen && episodeId) {
      // 加载保存的自定义提示词
      loadSavedCustomPrompt()

      // 加载保存的剧情信息
      loadSavedPlotInfo().then(hasData => {
        if (!hasData && episodeContent) {
          // 如果没有保存的数据，则进行新的分析
          analyzeDetailedPlot()
        }
      })
    }
  }, [isOpen, episodeId])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Zap className="text-purple-600 mr-2" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">
              剧情信息提取 - {episodeTitle}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* 左侧：剧情分析结果 */}
          <div className="w-2/3 p-6 overflow-y-auto border-r border-gray-200">
            {isAnalyzing ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">正在分析剧情信息...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* 剧集内容概览 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">剧集内容</h3>
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-32 overflow-y-auto">
                    <p className="text-sm text-gray-700">{episodeContent}</p>
                  </div>
                </div>

                {/* 角色DNA信息（增强版：包含一致性指示器） */}
                <div>
                  <div className="flex items-center mb-3">
                    <Users className="text-blue-600 mr-2" size={20} />
                    <h3 className="text-lg font-medium text-gray-900">角色DNA信息</h3>
                    <Shield className="text-purple-600 ml-2" size={16} title="一致性约束" />
                  </div>
                  {characters.length > 0 ? (
                    <div className="space-y-3">
                      {characters.map((character, index) => (
                        <div key={index} className={`border rounded-md p-4 relative ${
                          character.isKnownCharacter
                            ? 'bg-green-50 border-green-200'
                            : 'bg-blue-50 border-blue-200'
                        }`}>
                          {/* 一致性状态指示器 */}
                          <div className="absolute top-3 right-3 flex items-center space-x-2">
                            {character.isKnownCharacter ? (
                              <>
                                <CheckCircle className="text-green-600" size={16} title="已知角色" />
                                {character.consistencyInfo?.consistencyMatch && (
                                  <span className={`text-xs px-2 py-1 rounded-full ${
                                    character.consistencyInfo.consistencyMatch >= 0.9
                                      ? 'bg-green-100 text-green-800'
                                      : character.consistencyInfo.consistencyMatch >= 0.7
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {(character.consistencyInfo.consistencyMatch * 100).toFixed(0)}%
                                  </span>
                                )}
                              </>
                            ) : (
                              <AlertTriangle className="text-orange-500" size={16} title="新角色" />
                            )}
                          </div>

                          <h4 className={`font-medium pr-16 ${
                            character.isKnownCharacter ? 'text-green-900' : 'text-blue-900'
                          }`}>
                            {character.name}
                            {character.isKnownCharacter && (
                              <span className="ml-2 text-xs text-green-600">(已知角色)</span>
                            )}
                          </h4>

                          <div className={`mt-2 space-y-1 text-sm ${
                            character.isKnownCharacter ? 'text-green-800' : 'text-blue-800'
                          }`}>
                            {character.identity && <p><strong>身份：</strong>{character.identity}</p>}
                            {(character.facial || character.appearance) && <p><strong>外貌：</strong>{character.facial || character.appearance}</p>}
                            {character.personality && <p><strong>性格：</strong>{character.personality}</p>}
                            {character.clothing && <p><strong>服装：</strong>{character.clothing}</p>}
                            {character.role && <p><strong>作用：</strong>{character.role}</p>}

                            {/* 一致性信息显示 */}
                            {character.consistencyInfo && (
                              <div className="mt-3 p-2 bg-white bg-opacity-50 rounded border">
                                <p className="text-xs font-medium text-gray-700 mb-1">一致性分析：</p>
                                {character.consistencyInfo.consistencyConstraints && (
                                  <p className="text-xs text-gray-600 mb-1">
                                    <strong>约束建议：</strong>{character.consistencyInfo.consistencyConstraints}
                                  </p>
                                )}
                                {character.consistencyInfo.differences && character.consistencyInfo.differences.length > 0 && (
                                  <p className="text-xs text-orange-600">
                                    <strong>差异：</strong>{character.consistencyInfo.differences.join(', ')}
                                  </p>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">暂无角色信息</p>
                  )}
                </div>

                {/* 场景模板信息 */}
                <div>
                  <div className="flex items-center mb-3">
                    <MapPin className="text-green-600 mr-2" size={20} />
                    <h3 className="text-lg font-medium text-gray-900">场景模板信息</h3>
                  </div>
                  {scenes.length > 0 ? (
                    <div className="space-y-3">
                      {scenes.map((scene, index) => (
                        <div key={index} className="bg-green-50 border border-green-200 rounded-md p-4">
                          <h4 className="font-medium text-green-900">{scene.location}</h4>
                          <div className="mt-2 space-y-1 text-sm text-green-800">
                            {scene.description && <p><strong>描述：</strong>{scene.description}</p>}
                            {scene.atmosphere && <p><strong>氛围：</strong>{scene.atmosphere}</p>}
                            {scene.timeOfDay && <p><strong>时间：</strong>{scene.timeOfDay}</p>}
                            {scene.lighting && <p><strong>光线：</strong>{scene.lighting}</p>}
                            {scene.keyElements && <p><strong>关键元素：</strong>{scene.keyElements}</p>}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">暂无场景信息</p>
                  )}
                </div>

                {/* 情节分解结果 */}
                <div>
                  <div className="flex items-center mb-3">
                    <Settings className="text-orange-600 mr-2" size={20} />
                    <h3 className="text-lg font-medium text-gray-900">情节分解结果</h3>
                  </div>
                  {plotSequences.length > 0 ? (
                    <div className="space-y-3">
                      {plotSequences.map((sequence, index) => (
                        <div key={index} className="bg-orange-50 border border-orange-200 rounded-md p-4">
                          <h4 className="font-medium text-orange-900">序列 {index + 1}</h4>
                          <div className="mt-2 space-y-1 text-sm text-orange-800">
                            {sequence.action && <p><strong>动作：</strong>{sequence.action}</p>}
                            {sequence.emotion && <p><strong>情感：</strong>{sequence.emotion}</p>}
                            {sequence.duration && <p><strong>时长：</strong>{sequence.duration}</p>}
                            {sequence.keyMoments && sequence.keyMoments.length > 0 && (
                              <p><strong>关键时刻：</strong>{Array.isArray(sequence.keyMoments) ? sequence.keyMoments.join(', ') : sequence.keyMoments}</p>
                            )}
                            {sequence.visualElements && <p><strong>视觉要点：</strong>{sequence.visualElements}</p>}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">暂无情节信息</p>
                  )}
                </div>

                {/* 情感弧线 */}
                {emotionalArc && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">情感弧线</h3>
                    <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
                      <p className="text-sm text-purple-800">{emotionalArc}</p>
                    </div>
                  </div>
                )}

                {/* 生成的提示词 */}
                {generatedPrompt && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">生成的视频提示词</h3>
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                        {generatedPrompt}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 右侧：操作面板 */}
          <div className="w-1/3 p-6 bg-gray-50">
            <div className="space-y-6">
              {/* 重新分析按钮 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">分析控制</h3>
                <div className="flex">
                  <button
                    onClick={analyzeDetailedPlot}
                    disabled={isAnalyzing}
                    className="flex-1 flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-l-md hover:bg-purple-700 disabled:opacity-50"
                  >
                    <RefreshCw className="mr-2" size={16} />
                    {isAnalyzing ? '分析中...' : '重新分析'}
                  </button>
                  <button
                    onClick={() => setIsCustomPromptExpanded(!isCustomPromptExpanded)}
                    className="px-3 py-2 bg-purple-600 text-white rounded-r-md hover:bg-purple-700 border-l border-purple-500"
                  >
                    <ChevronDown
                      size={16}
                      className={`transform transition-transform ${isCustomPromptExpanded ? 'rotate-180' : ''}`}
                    />
                  </button>
                </div>
              </div>

              {/* 自定义增强 */}
              {isCustomPromptExpanded && (
                <div className="mb-8">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    自定义增强要求
                  </label>
                  <div className="relative">
                    <textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="添加特殊要求，如特定镜头角度、特效等..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <button
                      onClick={saveCustomPrompt}
                      className="absolute -bottom-7 right-0 flex items-center justify-center px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs shadow-sm"
                    >
                      <Save className="mr-1" size={12} />
                      保存
                    </button>
                  </div>
                </div>
              )}

              {/* 模型选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">视频生成设置</h3>
                <ModelSelector
                  selectedModel={selectedModel}
                  onModelSelect={setSelectedModel}
                />

                {/* 生成模式选择 */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    生成模式
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="generationMode"
                        value="manual"
                        checked={videoGenerationMode === 'manual'}
                        onChange={(e) => onVideoGenerationModeChange?.(e.target.value as 'manual')}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">手动模式</span>
                      <span className="ml-1 text-xs text-gray-500">(推荐)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="generationMode"
                        value="auto"
                        checked={videoGenerationMode === 'auto'}
                        onChange={(e) => onVideoGenerationModeChange?.(e.target.value as 'auto')}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">自动模式</span>
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {videoGenerationMode === 'manual'
                      ? '手动模式：创建片段后需要逐个点击生成，可选择性生成需要的片段'
                      : '自动模式：创建片段后自动开始生成所有片段'}
                  </p>
                </div>
              </div>

              {/* 生成剧情视频按钮 */}
              <button
                onClick={handleGenerateStoryVideo}
                disabled={!selectedModel || !generatedPrompt || isAnalyzing}
                className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Sparkles className="mr-2" size={16} />
                生成剧情视频
              </button>

              {/* 说明信息（增强版：包含一致性功能） */}
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-2">一致性增强功能</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• <strong>智能角色匹配：</strong>自动识别已知角色，保持一致性约束</li>
                  <li>• <strong>一致性评分：</strong>实时评估角色与项目库的匹配度</li>
                  <li>• <strong>差异检测：</strong>识别并标注角色描述的不一致之处</li>
                  <li>• <strong>约束建议：</strong>提供专业的一致性改进建议</li>
                  <li>• <strong>双重约束：</strong>结合文本和视觉约束生成视频</li>
                  <li>• <strong>DNA档案：</strong>建立详细的角色和场景DNA库</li>
                </ul>
                <div className="mt-3 flex items-center space-x-4 text-xs">
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="text-green-600" size={12} />
                    <span>已知角色</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <AlertTriangle className="text-orange-500" size={12} />
                    <span>新角色</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Shield className="text-purple-600" size={12} />
                    <span>一致性约束</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
