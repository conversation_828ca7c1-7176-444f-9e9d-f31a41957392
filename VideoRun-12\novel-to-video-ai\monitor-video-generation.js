const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function monitorVideoGeneration() {
  console.log('📺 开始监控视频生成状态...');
  console.log('按 Ctrl+C 停止监控\n');
  
  let lastSegmentCount = 0;
  let lastCompletedCount = 0;
  let lastFailedCount = 0;
  
  const monitor = async () => {
    try {
      // 获取所有视频片段状态
      const segments = await prisma.videoSegment.findMany({
        orderBy: { createdAt: 'desc' },
        take: 20,
        include: {
          storyVideo: true
        }
      });
      
      const statusCounts = {
        pending: 0,
        generating: 0,
        completed: 0,
        failed: 0
      };
      
      segments.forEach(segment => {
        statusCounts[segment.status] = (statusCounts[segment.status] || 0) + 1;
      });
      
      // 只在状态变化时显示
      if (segments.length !== lastSegmentCount || 
          statusCounts.completed !== lastCompletedCount || 
          statusCounts.failed !== lastFailedCount) {
        
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] 📊 视频片段状态:`);
        console.log(`   ⏳ 等待中: ${statusCounts.pending || 0}`);
        console.log(`   🎬 生成中: ${statusCounts.generating || 0}`);
        console.log(`   ✅ 已完成: ${statusCounts.completed || 0}`);
        console.log(`   ❌ 失败: ${statusCounts.failed || 0}`);
        console.log(`   📈 总计: ${segments.length}`);
        
        // 显示最近的活动
        if (segments.length > 0) {
          console.log('\n📋 最近活动:');
          segments.slice(0, 5).forEach((segment, index) => {
            const statusIcon = {
              pending: '⏳',
              generating: '🎬',
              completed: '✅',
              failed: '❌'
            }[segment.status] || '❓';
            
            const duration = segment.duration ? `${segment.duration}s` : '未知';
            console.log(`   ${statusIcon} ${segment.title.substring(0, 50)}... (${duration})`);
            
            // 如果是失败的，显示错误信息
            if (segment.status === 'failed' && segment.metadata) {
              try {
                const metadata = JSON.parse(segment.metadata);
                if (metadata.error) {
                  console.log(`      💬 错误: ${metadata.error}`);
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          });
        }
        
        console.log('\n' + '='.repeat(60) + '\n');
        
        lastSegmentCount = segments.length;
        lastCompletedCount = statusCounts.completed;
        lastFailedCount = statusCounts.failed;
      }
      
    } catch (error) {
      console.error('❌ 监控错误:', error.message);
    }
  };
  
  // 立即执行一次
  await monitor();
  
  // 每5秒检查一次
  const interval = setInterval(monitor, 5000);
  
  // 优雅退出
  process.on('SIGINT', async () => {
    console.log('\n👋 停止监控...');
    clearInterval(interval);
    await prisma.$disconnect();
    process.exit(0);
  });
}

monitorVideoGeneration().catch(console.error);
