// 测试模型选择功能
// 使用Node.js 18+的内置fetch

async function testModelSelection() {
  console.log('🧪 测试模型选择功能...\n');

  // 1. 获取可用模型列表
  console.log('1️⃣ 获取可用模型列表...');
  try {
    const response = await fetch('http://localhost:3002/api/models');
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ 模型列表获取成功:');
      data.data.forEach(model => {
        console.log(`   - ${model.name} (${model.provider}) - ${model.enabled ? '已启用' : '未启用'}`);
        console.log(`     支持视频: ${model.supportsVideo}, 支持图生视频: ${model.supportsImageToVideo}`);
      });
      
      // 找到支持视频生成的豆包模型
      const doubaoModels = data.data.filter(model =>
        model.provider === 'doubao' &&
        model.enabled &&
        (model.supportsVideo || model.supportsImageToVideo)
      );
      
      if (doubaoModels.length > 0) {
        console.log(`\n✅ 找到 ${doubaoModels.length} 个可用的豆包模型`);
        
        // 测试使用特定模型生成片段
        const testModelId = doubaoModels[0].id;
        console.log(`\n2️⃣ 测试使用模型 ${doubaoModels[0].name} (ID: ${testModelId}) 生成片段...`);
        
        // 模拟调用单个片段生成API - 使用pending状态的片段3
        const testResponse = await fetch('http://localhost:3002/api/ai/generate-single-segment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            segmentId: 'cmcd1zlh40007vmso4o90u1rb', // 使用片段3的ID
            modelId: testModelId
          })
        });
        
        const testData = await testResponse.json();
        
        if (testData.success) {
          console.log('✅ 模型选择测试成功!');
          console.log(`   消息: ${testData.data.message}`);
          console.log(`   片段: ${testData.data.title}`);
        } else {
          console.log('❌ 模型选择测试失败:');
          console.log(`   错误: ${testData.error}`);
        }
      } else {
        console.log('❌ 没有找到可用的豆包模型');
      }
    } else {
      console.log('❌ 获取模型列表失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testModelSelection();
