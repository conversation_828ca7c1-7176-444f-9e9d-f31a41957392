const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugDoubaoI2VActual() {
  try {
    console.log('🔍 调试豆包图生视频实际API调用...\n');
    
    // 1. 检查I2V模型配置
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    if (!i2vModel || !i2vModel.enabled || !i2vModel.apiKey) {
      console.log('❌ I2V模型未正确配置');
      return;
    }
    
    console.log('✅ I2V模型配置正确');
    console.log(`   模型ID: ${i2vModel.model}`);
    console.log(`   API密钥: ${i2vModel.apiKey.substring(0, 10)}...`);
    
    // 2. 获取角色参考图像
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 1
    });
    
    if (charactersWithImages.length === 0) {
      console.log('❌ 没有找到有参考图像的角色');
      return;
    }
    
    const character = charactersWithImages[0];
    let referenceImageUrl = null;
    
    try {
      const images = JSON.parse(character.generatedImages);
      referenceImageUrl = images.front || images.side || images.back;
    } catch (e) {
      console.log('❌ 解析角色图像失败');
      return;
    }
    
    if (!referenceImageUrl) {
      console.log('❌ 角色没有可用的参考图像');
      return;
    }
    
    console.log(`✅ 找到角色: ${character.name}`);
    console.log(`📸 参考图像URL: ${referenceImageUrl.substring(0, 100)}...`);
    
    // 3. 测试多种可能的格式
    const testPrompt = '角色在雪夜中缓缓转身，表情严肃警惕，眼神坚定';
    const duration = 5;
    
    const formats = [
      {
        name: '格式1: text + image_url (当前使用)',
        content: [
          {
            type: "text",
            text: `${testPrompt} --ratio adaptive --dur ${duration}`
          },
          {
            type: "image_url",
            image_url: {
              url: referenceImageUrl
            }
          }
        ]
      },
      {
        name: '格式2: image_url + text',
        content: [
          {
            type: "image_url",
            image_url: {
              url: referenceImageUrl
            }
          },
          {
            type: "text",
            text: `${testPrompt} --ratio adaptive --dur ${duration}`
          }
        ]
      },
      {
        name: '格式3: 只有image_url',
        content: [
          {
            type: "image_url",
            image_url: {
              url: referenceImageUrl
            }
          }
        ]
      },
      {
        name: '格式4: text不带参数 + image_url',
        content: [
          {
            type: "text",
            text: testPrompt
          },
          {
            type: "image_url",
            image_url: {
              url: referenceImageUrl
            }
          }
        ]
      }
    ];
    
    // 4. 逐一测试每种格式
    for (const format of formats) {
      console.log(`\n🧪 测试 ${format.name}:`);
      console.log('📋 请求内容:', JSON.stringify(format.content, null, 2));
      
      try {
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${i2vModel.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: i2vModel.model,
            content: format.content
          })
        });
        
        console.log(`📊 响应状态: ${response.status}`);
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ 成功!');
          console.log('📋 响应:', JSON.stringify(result, null, 2));
          
          const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
          if (taskId) {
            console.log(`🎯 任务ID: ${taskId}`);
            console.log(`🎉 ${format.name} 格式正确！`);
            break; // 找到正确格式就停止
          }
        } else {
          const errorText = await response.text();
          console.log('❌ 失败');
          console.log(`📋 错误: ${errorText}`);
        }
        
      } catch (error) {
        console.log('❌ 异常:', error.message);
      }
      
      // 等待一下避免请求过快
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 5. 检查参考图像是否有效
    console.log('\n🔍 检查参考图像有效性...');
    
    try {
      const imageResponse = await fetch(referenceImageUrl, { method: 'HEAD' });
      console.log(`📸 图像URL状态: ${imageResponse.status}`);
      console.log(`📸 图像类型: ${imageResponse.headers.get('content-type')}`);
      console.log(`📸 图像大小: ${imageResponse.headers.get('content-length')} bytes`);
      
      if (!imageResponse.ok) {
        console.log('❌ 参考图像URL无效，这可能是问题所在！');
      } else {
        console.log('✅ 参考图像URL有效');
      }
    } catch (error) {
      console.log('❌ 检查图像URL失败:', error.message);
    }
    
    // 6. 检查模型ID是否正确
    console.log('\n🔍 验证模型配置...');
    console.log(`模型ID: ${i2vModel.model}`);
    console.log('预期的I2V模型ID: ep-20250624013749-dbrbn');
    
    if (i2vModel.model !== 'ep-20250624013749-dbrbn') {
      console.log('❌ 模型ID不匹配！这可能是问题所在');
    } else {
      console.log('✅ 模型ID正确');
    }
    
    // 7. 总结
    console.log('\n📊 调试总结:');
    console.log('可能的问题原因:');
    console.log('1. 参考图像URL无效或无法访问');
    console.log('2. 模型ID配置错误');
    console.log('3. API格式仍然不正确');
    console.log('4. API密钥权限问题');
    console.log('5. 豆包API的特殊要求');
    
    console.log('\n🔧 建议解决方案:');
    console.log('1. 检查并重新生成角色参考图像');
    console.log('2. 确认模型ID配置正确');
    console.log('3. 联系豆包技术支持确认I2V API格式');
    console.log('4. 暂时使用T2V模型作为备选方案');
    
    console.log('\n🎬 豆包图生视频实际调试完成！');
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugDoubaoI2VActual();
