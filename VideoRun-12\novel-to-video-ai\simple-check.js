const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simpleCheck() {
  console.log('🔍 开始检查...');
  
  try {
    // 1. 检查AI配置
    const aiConfigs = await prisma.aIConfig.findMany();
    console.log(`AI配置数量: ${aiConfigs.length}`);
    
    // 2. 检查视频模型
    const videoModels = await prisma.aIConfig.findMany({
      where: { enabled: true, supportsVideo: true }
    });
    console.log(`视频模型数量: ${videoModels.length}`);
    
    // 3. 检查豆包配置
    const doubao = videoModels.find(m => m.provider === 'doubao');
    console.log(`豆包配置: ${doubao ? '✅ 有' : '❌ 无'}`);
    if (doubao) {
      console.log(`豆包模型: ${doubao.model}`);
    }
    
    // 4. 检查角色
    const characters = await prisma.character.findMany();
    console.log(`角色数量: ${characters.length}`);
    
    // 5. 检查视频片段
    const segments = await prisma.videoSegment.findMany();
    console.log(`视频片段数量: ${segments.length}`);
    
    console.log('✅ 检查完成');
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

simpleCheck();
