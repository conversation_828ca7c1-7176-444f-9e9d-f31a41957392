const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoAPI() {
  try {
    console.log('🧪 测试豆包API响应格式...');
    
    // 获取豆包配置
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsVideo: true,
        enabled: true
      }
    });
    
    if (!doubaoConfig) {
      console.log('❌ 未找到豆包视频配置');
      return;
    }
    
    console.log('✅ 找到豆包配置:', doubaoConfig.model);
    
    // 测试API调用
    const testPrompt = '一个简单的测试视频：蓝天白云，阳光明媚的风景';
    
    console.log('📡 调用豆包API...');
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${doubaoConfig.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: doubaoConfig.model,
        content: [
          {
            type: "text",
            text: `${testPrompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`
          }
        ]
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status} ${errorText}`);
      return;
    }
    
    const result = await response.json();
    console.log('📋 豆包API响应格式:');
    console.log(JSON.stringify(result, null, 2));
    
    // 分析响应格式
    console.log('\n🔍 响应分析:');
    console.log('- result.id:', result.id);
    console.log('- result.task_id:', result.task_id);
    console.log('- result.data?.id:', result.data?.id);
    console.log('- result.data?.task_id:', result.data?.task_id);
    
    // 确定任务ID
    const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
    console.log('✅ 提取的任务ID:', taskId);
    
    if (taskId) {
      console.log('🎯 任务ID提取成功，可以进行轮询');
    } else {
      console.log('❌ 无法提取任务ID');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoAPI();
