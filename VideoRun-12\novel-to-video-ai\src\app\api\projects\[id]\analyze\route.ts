import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// AI分析提示词
const CHARACTER_ANALYSIS_PROMPT = `
你是一个专业的小说分析专家和角色设计师。请仔细分析以下小说内容，提取所有重要角色的详细信息。

重要要求：
1. 每个字段都必须填写详细内容，不能为空或过于简单
2. 描述要准确反映小说中的角色特征
3. 为后续的角色形象生成提供充分的细节支撑
4. 必须返回有效的JSON格式，不能包含任何其他文字说明
5. JSON中的字符串不能包含未转义的换行符或引号

请按照以下JSON格式返回角色信息，每个角色包含以下字段：

- name: 角色姓名（从小说中准确提取）
- identity: 身份信息（至少80字）
  * 必须包含：职业/身份、社会地位、家庭背景、年龄段、所属势力/组织等
  * 要体现角色在故事中的重要性和作用
- personality: 性格特点（至少120字）
  * 必须包含：核心性格特征、行为习惯、说话方式、处事风格、内心特点、情感倾向等
  * 要深入分析角色的心理层面和行为动机
- physique: 身材特征（至少100字）
  * 必须包含：身高、体型、体态、肌肉线条、整体身材比例、体重感觉、身体姿态等
  * 要符合角色的身份地位和性格特点，体现角色的身体特征
- facial: 五官特征（至少100字）
  * 必须包含：脸型、眼睛形状/颜色/大小、鼻子特征、嘴唇样式、眉毛特征、肤色、面部轮廓等
  * 基于小说描述，如无明确描述则根据角色身份和性格合理推断
- hairstyle: 发型样式（至少80字）
  * 必须包含：发型样式、发色、发长、发质、刘海、发量、发型与身份的匹配度等
  * 要符合角色的时代背景、身份地位和个人喜好
- clothing: 服饰风格（至少100字）
  * 必须包含：服装风格、颜色搭配、材质、配饰、鞋子、整体穿着品味等
  * 要体现角色的身份地位、经济状况、个人喜好和时代特色

重要：只返回纯JSON，不要任何解释文字。所有字符串内容不能包含换行符、引号等特殊字符。格式如下：
{"characters":[{"name":"角色名称","identity":"详细身份信息80字以上","personality":"详细性格特点120字以上","physique":"详细身材特征100字以上","facial":"详细五官特征100字以上","hairstyle":"详细发型样式80字以上","clothing":"详细服饰风格100字以上"}]}

小说内容：
`

const EPISODE_ANALYSIS_PROMPT = `
你是一个专业的小说分析专家。请仔细分析以下小说内容，按章节拆分成独立的剧集。

重要要求：
1. 必须返回有效的JSON格式，不能包含任何其他文字说明
2. JSON中的字符串不能包含未转义的换行符或引号
3. 每个章节应该是一个完整的故事单元
4. 标题要简洁明了，体现该章节的核心内容
5. 内容要保持原文的完整性和连贯性
6. 按照原文的章节顺序进行拆分

请按照以下JSON格式返回剧集信息：
- title: 故事标题（一级目录，章节标题）
- content: 详细剧情（二级目录，完整的章节内容，用\\n表示换行）

重要：只返回纯JSON，不要任何解释文字。内容中不能包含引号、换行符等特殊字符，用句号分段。格式如下：
{"episodes":[{"title":"章节标题","content":"完整章节内容用句号分段不含特殊字符"}]}

小说内容：
`

// 项目AI分析
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params

    // 获取请求体中的模型ID
    let requestBody = {}
    try {
      requestBody = await request.json()
    } catch (e) {
      // 如果没有请求体，使用默认值
    }

    const { modelId, customPrompt } = requestBody as { modelId?: string; customPrompt?: string }

    // 获取项目信息
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    if (!project.content) {
      return NextResponse.json(
        { success: false, error: '项目中没有小说内容' },
        { status: 400 }
      )
    }

    // 获取AI配置
    let aiConfig
    if (modelId) {
      // 如果指定了模型ID，使用该模型配置
      aiConfig = await prisma.aIConfig.findUnique({
        where: { id: modelId }
      })

      if (!aiConfig) {
        return NextResponse.json(
          { success: false, error: '指定的模型配置不存在' },
          { status: 400 }
        )
      }

      if (!aiConfig.enabled) {
        return NextResponse.json(
          { success: false, error: '指定的模型未启用' },
          { status: 400 }
        )
      }
    } else {
      // 如果没有指定模型ID，使用第一个启用的模型
      aiConfig = await prisma.aIConfig.findFirst({
        where: { enabled: true }
      })

      if (!aiConfig) {
        return NextResponse.json(
          { success: false, error: '请先配置并启用AI模型' },
          { status: 400 }
        )
      }
    }

    // 更新项目状态为分析中
    await prisma.project.update({
      where: { id: projectId },
      data: { status: 'analyzing' }
    })

    // 如果是重新分析，先删除旧的角色和剧集数据
    await prisma.character.deleteMany({
      where: { projectId }
    })
    await prisma.episode.deleteMany({
      where: { projectId }
    })

    try {
      // 构建角色分析提示词（包含增强提示词）
      let characterPrompt = CHARACTER_ANALYSIS_PROMPT
      if (customPrompt && customPrompt.trim()) {
        characterPrompt = `${CHARACTER_ANALYSIS_PROMPT}\n\n增强要求：${customPrompt}\n\n`
      }
      characterPrompt += project.content

      // 1. 分析角色信息
      const characterResponse = await callAIModel(
        aiConfig,
        characterPrompt
      )

      let characters = []
      try {
        const cleanedResponse = cleanJsonResponse(characterResponse)
        const characterData = JSON.parse(cleanedResponse)
        characters = characterData.characters || []
      } catch (parseError) {
        console.error('角色信息解析失败:', parseError)
        console.error('原始响应:', characterResponse)
        // 如果解析失败，尝试提取部分信息
        characters = []
      }

      // 构建剧集分析提示词（包含增强提示词）
      let episodePrompt = EPISODE_ANALYSIS_PROMPT
      if (customPrompt && customPrompt.trim()) {
        episodePrompt = `${EPISODE_ANALYSIS_PROMPT}\n\n增强要求：${customPrompt}\n\n`
      }
      episodePrompt += project.content

      // 2. 分析剧集信息
      const episodeResponse = await callAIModel(
        aiConfig,
        episodePrompt
      )

      let episodes = []
      try {
        const cleanedResponse = cleanJsonResponse(episodeResponse)
        const episodeData = JSON.parse(cleanedResponse)
        episodes = episodeData.episodes || []
      } catch (parseError) {
        console.error('剧集信息解析失败:', parseError)
        console.error('原始响应:', episodeResponse)
        // 如果解析失败，尝试简单拆分
        episodes = []
      }

      // 3. 保存角色信息到数据库
      const savedCharacters = []
      for (const character of characters) {
        if (character.name) {
          const savedCharacter = await prisma.character.create({
            data: {
              projectId,
              name: character.name,
              identity: character.identity || null,
              personality: character.personality || null,
              physique: character.physique || null,
              facial: character.facial || null,
              hairstyle: character.hairstyle || null,
              clothing: character.clothing || null
            }
          })
          savedCharacters.push(savedCharacter)
        }
      }

      // 4. 保存剧集信息到数据库
      const savedEpisodes = []
      for (let i = 0; i < episodes.length; i++) {
        const episode = episodes[i]
        if (episode.title && episode.content) {
          const savedEpisode = await prisma.episode.create({
            data: {
              projectId,
              title: episode.title,
              content: episode.content,
              orderIndex: i + 1,
              status: 'created'
            }
          })
          savedEpisodes.push(savedEpisode)
        }
      }

      // 5. 更新项目状态为完成
      const updatedProject = await prisma.project.update({
        where: { id: projectId },
        data: { 
          status: 'completed',
          updatedAt: new Date()
        },
        include: {
          characters: true,
          episodes: true
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          project: updatedProject,
          characters: savedCharacters,
          episodes: savedEpisodes,
          analysis: {
            characterCount: savedCharacters.length,
            episodeCount: savedEpisodes.length
          }
        },
        message: 'AI分析完成'
      })

    } catch (aiError) {
      // AI分析失败，恢复项目状态
      await prisma.project.update({
        where: { id: projectId },
        data: { status: 'uploaded' }
      })

      console.error('AI分析失败:', aiError)
      return NextResponse.json(
        { success: false, error: 'AI分析失败，请检查模型配置或稍后重试' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('项目分析失败:', error)
    return NextResponse.json(
      { success: false, error: '项目分析失败' },
      { status: 500 }
    )
  }
}

// 清理JSON响应，移除可能导致解析失败的字符
function cleanJsonResponse(response: string): string {
  // 移除可能的前后缀文字，只保留JSON部分
  let cleaned = response.trim()

  // 查找JSON开始和结束位置
  const jsonStart = cleaned.indexOf('{')
  const jsonEnd = cleaned.lastIndexOf('}')

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    cleaned = cleaned.substring(jsonStart, jsonEnd + 1)
  }

  // 更安全的JSON清理方法
  try {
    // 尝试直接解析，如果成功就返回
    JSON.parse(cleaned)
    return cleaned
  } catch (error) {
    // 如果解析失败，进行更激进的清理
    console.log('JSON解析失败，进行清理:', error.message)

    // 使用更简单的方法：逐字符处理，正确处理字符串边界
    let result = ''
    let inString = false
    let escapeNext = false

    for (let i = 0; i < cleaned.length; i++) {
      const char = cleaned[i]
      const prevChar = i > 0 ? cleaned[i - 1] : ''

      if (escapeNext) {
        result += char
        escapeNext = false
        continue
      }

      if (char === '\\') {
        result += char
        escapeNext = true
        continue
      }

      if (char === '"' && prevChar !== '\\') {
        inString = !inString
        result += char
        continue
      }

      if (inString) {
        // 在字符串内部，转义特殊字符
        if (char === '\n') {
          result += '\\n'
        } else if (char === '\r') {
          result += '\\r'
        } else if (char === '\t') {
          result += '\\t'
        } else {
          result += char
        }
      } else {
        // 在字符串外部，正常处理
        result += char
      }
    }

    // 移除末尾逗号
    result = result
      .replace(/,\s*}/g, '}')
      .replace(/,\s*]/g, ']')

    return result
  }
}

// 调用AI模型生成内容
async function callAIModel(aiConfig: any, prompt: string) {
  const { provider, apiKey, model } = aiConfig

  switch (provider) {
    case 'deepseek':
      return await callDeepSeek(apiKey, model, prompt)
    case 'openai':
      return await callOpenAI(apiKey, model, prompt)
    case 'claude':
      return await callClaude(apiKey, model, prompt)
    case 'tongyi':
      return await callTongyi(apiKey, model, prompt)
    case 'doubao':
      return await callDoubao(apiKey, model, prompt)
    default:
      throw new Error(`不支持的AI提供商: ${provider}`)
  }
}

// DeepSeek API调用
async function callDeepSeek(apiKey: string, model: string, prompt: string) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 300000) // 5分钟超时

  try {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 8000
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`DeepSeek API调用失败: ${response.statusText}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('AI返回内容为空')
    }

    return content
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('AI调用超时，请稍后重试')
    }
    throw error
  }
}

// OpenAI API调用
async function callOpenAI(apiKey: string, model: string, prompt: string) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 4000
    })
  })

  if (!response.ok) {
    throw new Error(`OpenAI API调用失败: ${response.statusText}`)
  }

  const data = await response.json()
  const content = data.choices[0]?.message?.content

  if (!content) {
    throw new Error('AI返回内容为空')
  }

  return content
}

// Claude API调用
async function callClaude(apiKey: string, model: string, prompt: string) {
  // Claude API实现
  throw new Error('Claude API暂未实现')
}

// 通义AI调用
async function callTongyi(apiKey: string, model: string, prompt: string) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 300000) // 5分钟超时

  try {
    // 根据模型选择正确的API端点
    let apiUrl = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation'
    let requestBody: any = {
      model: model,
      input: {
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      },
      parameters: {
        temperature: 0.3,
        max_tokens: 8000
      }
    }

    // 对于某些模型，可能需要不同的请求格式
    if (model.includes('turbo')) {
      // 确保使用正确的请求格式
      requestBody = {
        model: model,
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.3,
          max_tokens: 8000,
          result_format: 'message'
        }
      }
    }

    console.log(`🔍 调用通义API: ${apiUrl}`)
    console.log(`📝 模型: ${model}`)
    console.log(`🔑 API密钥前缀: ${apiKey.substring(0, 8)}...`)

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'X-DashScope-SSE': 'disable'
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`❌ 通义API响应错误 (${response.status}):`, errorText)

      let errorData: any = {}
      try {
        errorData = JSON.parse(errorText)
      } catch (e) {
        // 如果不是JSON格式，直接使用错误文本
        if (errorText.includes('url error')) {
          throw new Error('通义API URL错误，请检查模型名称和API密钥是否正确')
        }
        throw new Error(`通义AI API调用失败: ${errorText || response.statusText}`)
      }

      // 处理特定的错误类型
      if (errorData.code === 'Arrearage') {
        throw new Error('通义API账户欠费，请前往阿里云控制台充值后重试')
      } else if (errorData.code === 'InvalidApiKey') {
        throw new Error('通义API密钥无效，请检查配置')
      } else if (errorData.code === 'RateLimitExceeded') {
        throw new Error('通义API调用频率超限，请稍后重试')
      } else if (errorData.message && errorData.message.includes('url error')) {
        throw new Error('通义API URL错误，请检查模型名称和API密钥是否正确')
      } else {
        throw new Error(`通义AI API调用失败: ${errorData.message || response.statusText}`)
      }
    }

    const data = await response.json()

    // 检查响应中的错误
    if (data.code && data.code !== 'Success') {
      if (data.code === 'Arrearage') {
        throw new Error('通义API账户欠费，请前往阿里云控制台充值后重试')
      } else {
        throw new Error(`通义AI错误: ${data.message || data.code}`)
      }
    }

    const content = data.output?.text

    if (!content) {
      throw new Error('通义AI返回内容为空')
    }

    return content
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('通义AI调用超时，请稍后重试')
    }
    throw error
  }
}

// 豆包 (Doubao) API调用
async function callDoubao(apiKey: string, model: string, prompt: string) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 300000) // 5分钟超时

  try {
    console.log(`🔍 调用豆包API: https://ark.cn-beijing.volces.com/api/v3/chat/completions`)
    console.log(`📝 模型: ${model}`)
    console.log(`🔑 API密钥前缀: ${apiKey.substring(0, 8)}...`)

    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 8000
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`❌ 豆包API响应错误 (${response.status}):`, errorText)

      let errorData: any = {}
      try {
        errorData = JSON.parse(errorText)
      } catch (e) {
        throw new Error(`豆包API调用失败: ${errorText || response.statusText}`)
      }

      // 处理特定的错误类型
      if (errorData.error?.code === 'invalid_api_key') {
        throw new Error('豆包API密钥无效，请检查配置')
      } else if (errorData.error?.code === 'rate_limit_exceeded') {
        throw new Error('豆包API调用频率超限，请稍后重试')
      } else if (errorData.error?.code === 'insufficient_quota') {
        throw new Error('豆包API配额不足，请检查账户余额')
      } else {
        throw new Error(`豆包API调用失败: ${errorData.error?.message || response.statusText}`)
      }
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('豆包AI返回内容为空')
    }

    console.log(`✅ 豆包API调用成功，返回内容长度: ${content.length}`)
    return content
  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('豆包AI调用超时，请稍后重试')
    }
    console.error('❌ 豆包API调用失败:', error)
    throw error
  }
}
