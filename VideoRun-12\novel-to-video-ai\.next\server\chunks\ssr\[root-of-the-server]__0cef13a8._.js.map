{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Home,\n  FolderOpen,\n  Settings,\n  User,\n  Menu,\n  X,\n  Sparkles\n} from 'lucide-react'\n\ninterface LayoutProps {\n  children: React.ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navigation = [\n    {\n      name: '首页',\n      href: '/',\n      icon: Home,\n      current: pathname === '/'\n    },\n    {\n      name: '项目',\n      href: '/projects',\n      icon: FolderOpen,\n      current: pathname.startsWith('/projects')\n    },\n    {\n      name: '模型配置',\n      href: '/models',\n      icon: Settings,\n      current: pathname === '/models'\n    },\n    {\n      name: '账户',\n      href: '/account',\n      icon: User,\n      current: pathname === '/account',\n      disabled: true\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* 左侧 Logo */}\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <Sparkles className=\"text-purple-600 mr-2\" size={24} />\n                <span className=\"text-lg font-semibold text-gray-900 whitespace-nowrap\">\n                  灵犀Ai——小说转视频神器\n                </span>\n              </Link>\n            </div>\n\n            {/* 右侧导航菜单 */}\n            <div className=\"hidden md:flex md:items-center md:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    inline-flex items-center text-lg font-semibold\n                    ${item.current\n                      ? 'text-purple-600'\n                      : 'text-gray-900 hover:text-purple-600'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                >\n                  <item.icon className=\"mr-2\" size={20} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden flex items-center\">\n              <button\n                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <Menu size={20} />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端菜单 */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`\n                    flex items-center px-3 py-2 text-sm font-medium border-l-4\n                    ${item.current\n                      ? 'bg-purple-50 border-purple-500 text-purple-700'\n                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'\n                    }\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <item.icon className=\"mr-2\" size={16} />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* 主内容区域 */}\n      <main className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAmBe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;YACV,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,SAAS,SAAS,UAAU,CAAC;QAC/B;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,SAAS,aAAa;YACtB,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAuB,MAAM;;;;;;0DACjD,8OAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;;;;;;8CAO5E,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,oBACA,sCACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;;8DAED,8OAAC,KAAK,IAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDACjC,KAAK,IAAI;;2CAZL,KAAK,IAAI;;;;;;;;;;8CAkBpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,mDACA,wEACH;oBACD,EAAE,KAAK,QAAQ,GAAG,sDAAsD,GAAG;kBAC7E,CAAC;oCACD,SAAS,IAAM,kBAAkB;;sDAEjC,8OAAC,KAAK,IAAI;4CAAC,WAAU;4CAAO,MAAM;;;;;;wCACjC,KAAK,IAAI;;mCAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BAsB1B,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport {\n  Sparkles,\n  Upload,\n  Users,\n  Film,\n  Video,\n  ArrowRight,\n  CheckCircle,\n  Settings\n} from 'lucide-react'\nimport Layout from '@/components/Layout'\n\nexport default function Home() {\n  const steps = [\n    {\n      icon: Settings,\n      title: '配置AI模型',\n      description: '选择DeepSeek等大模型，配置API密钥',\n      href: '/models'\n    },\n    {\n      icon: Upload,\n      title: '上传小说文件',\n      description: '支持.txt、.docx格式，创建新项目并上传',\n      href: '/projects/new'\n    },\n    {\n      icon: Users,\n      title: '自动提取角色',\n      description: 'AI分析小说，提取角色的五官、身份、性格等信息',\n      href: '/projects'\n    },\n    {\n      icon: Film,\n      title: '拆分剧集',\n      description: '按章节自动拆分成独立剧集，提取剧情信息',\n      href: '/projects'\n    },\n    {\n      icon: Video,\n      title: '生成视频脚本',\n      description: '基于多维度信息，为每集生成视频脚本',\n      href: '/projects'\n    }\n  ]\n\n  return (\n    <Layout>\n      {/* 英雄区域 */}\n      <div className=\"text-center\">\n        <div className=\"mx-auto max-w-2xl\">\n          <div className=\"flex justify-center mb-6\">\n            <Sparkles className=\"text-purple-600\" size={64} />\n          </div>\n          <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl whitespace-nowrap\">\n            灵犀Ai——小说转视频神器\n          </h1>\n          <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n            基于DeepSeek大模型的智能视频内容创作平台，将小说自动转换为结构化的视频脚本，\n            提取角色信息、拆分剧集、生成多维度视频内容。\n          </p>\n          <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n            <Link\n              href=\"/projects/new\"\n              className=\"rounded-md bg-purple-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600\"\n            >\n              开始创作\n            </Link>\n            <Link\n              href=\"/models\"\n              className=\"text-sm font-semibold leading-6 text-gray-900\"\n            >\n              配置模型 <span aria-hidden=\"true\">→</span>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* 生成流程 */}\n      <div className=\"mt-24\">\n        <div className=\"mx-auto max-w-2xl text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n            AI视频生成流程\n          </h2>\n          <p className=\"mt-4 text-lg leading-8 text-gray-600\">\n            简单5步，将您的小说转换为专业的视频脚本\n          </p>\n        </div>\n\n        <div className=\"mx-auto mt-16 max-w-5xl\">\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-5\">\n            {steps.map((step, index) => (\n              <div key={step.title} className=\"relative\">\n                <div className=\"flex flex-col items-center text-center\">\n                  <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-purple-100 text-purple-600\">\n                    <step.icon size={24} />\n                  </div>\n                  <h3 className=\"mt-4 text-lg font-semibold text-gray-900\">\n                    {step.title}\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-600\">\n                    {step.description}\n                  </p>\n                  <Link\n                    href={step.href}\n                    className=\"mt-3 text-sm font-medium text-purple-600 hover:text-purple-500\"\n                  >\n                    开始 →\n                  </Link>\n                </div>\n\n                {/* 连接线 */}\n                {index < steps.length - 1 && (\n                  <div className=\"absolute top-8 left-full hidden w-full lg:block\">\n                    <ArrowRight className=\"mx-auto text-gray-300\" size={20} />\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 项目特性 */}\n      <div className=\"mt-24\">\n        <div className=\"mx-auto max-w-7xl\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              项目特性\n            </h2>\n            <p className=\"mt-4 text-lg leading-8 text-gray-600\">\n              基于先进的AI技术，为您提供专业的小说转视频解决方案\n            </p>\n          </div>\n\n          <div className=\"mx-auto mt-16 max-w-5xl\">\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    智能角色提取\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  自动识别小说中的所有角色，提取五官、身份、外貌、性格、隐线等多维度信息\n                </p>\n              </div>\n\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    章节智能拆分\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  按原小说章节自动拆分成独立剧集，保持故事的完整性和连贯性\n                </p>\n              </div>\n\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    剧情信息提取\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  提取每集的本集人物、场景信息、事件三要素（正常-矛盾冲突-升级事件）\n                </p>\n              </div>\n\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    多格式支持\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  支持.txt、.docx等多种文件格式，最大支持50MB文件上传\n                </p>\n              </div>\n\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    DeepSeek模型\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  集成DeepSeek等先进大模型，提供Chat、R1、V3、Code多种模型选择\n                </p>\n              </div>\n\n              <div className=\"relative p-6 bg-white rounded-lg shadow-sm border border-gray-200\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"text-green-500 mr-3\" size={20} />\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\n                    项目管理\n                  </h3>\n                </div>\n                <p className=\"mt-3 text-gray-600\">\n                  完整的项目管理系统，支持多项目并行处理，数据安全可靠\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAee,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,8OAAC,4HAAA,CAAA,UAAM;;0BAEL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAkB,MAAM;;;;;;;;;;;sCAE9C,8OAAC;4BAAG,WAAU;sCAAgF;;;;;;sCAG9F,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDACM,8OAAC;4CAAK,eAAY;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;;;;;;;kCAKtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,KAAK,IAAI;wDAAC,MAAM;;;;;;;;;;;8DAEnB,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAEnB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DACX;;;;;;;;;;;;wCAMF,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;gDAAwB,MAAM;;;;;;;;;;;;mCAtBhD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAgC5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAKpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD", "debugId": null}}]}