# 双重约束角色一致性系统 - 快速开始指南

## 🚀 5分钟快速体验

### 前置条件
- ✅ 系统已启动（`npm run dev`）
- ✅ 数据库已配置
- ✅ AI配置已设置

### 第一步：创建测试项目 (1分钟)
1. 访问 `http://localhost:3001`
2. 点击"创建新项目"
3. 输入项目名称："一致性测试项目"
4. 上传测试小说文件或使用现有项目

### 第二步：体验一致性剧情分析 (2分钟)
1. 进入项目详情页面
2. 切换到"剧集"标签
3. 选择任意剧集，点击"提取详细剧情"
4. 观察**一致性增强功能**：
   - 🟢 绿色角色卡片 = 已知角色
   - 🔵 蓝色角色卡片 = 新角色
   - 📊 一致性评分百分比
   - ⚠️ 差异检测和约束建议

### 第三步：生成一致性视频 (1分钟)
1. 切换到"视频"标签
2. 找到目标剧集，点击"一致性视频"按钮
3. 在弹出的生成器中：
   - 选择一致性模式（推荐：平衡模式）
   - 启用参考图像约束
   - 点击"生成一致性视频"
4. 查看生成结果和一致性信息

### 第四步：验证一致性质量 (1分钟)
1. 生成完成后，自动显示"一致性验证器"
2. 点击"开始验证"
3. 查看验证结果：
   - 总体一致性评分
   - 角色和场景详细评分
   - 发现的问题和改进建议

## 🎯 核心功能演示路径

### 路径A：角色一致性识别
```
剧集内容 → AI分析 → 角色匹配 → 一致性评分 → 可视化显示
```
**观察点**：角色卡片的颜色变化和评分显示

### 路径B：双重约束生成
```
配置参数 → 文本约束 + 视觉约束 → 增强提示词 → 生成记录
```
**观察点**：生成的提示词长度和一致性特性列表

### 路径C：端到端验证
```
生成结果 → 多维度分析 → 评分计算 → 问题识别 → 改进建议
```
**观察点**：验证评分和具体的问题建议

## 🔍 关键界面元素说明

### 剧情提取界面
- **一致性增强功能区域**：显示系统的一致性特性
- **角色DNA信息区域**：
  - 右上角的一致性评分圆圈
  - 角色卡片的边框颜色
  - 一致性分析详情展开区域

### 一致性视频生成器
- **一致性模式选择**：三个彩色按钮（红/蓝/绿）
- **参考图像约束**：复选框开关
- **生成结果展示**：
  - 一致性信息摘要表格
  - 一致性特性标签列表
  - 提示词预览折叠区域

### 一致性验证器
- **总体评分区域**：大号评分和等级显示
- **详细评分网格**：角色/场景/问题统计
- **验证结果卡片**：角色和场景的详细评分
- **问题和建议区域**：带颜色标签的问题列表

## 📊 测试数据说明

### 测试剧集内容
系统包含一个测试剧集，内容包含：
- **已知角色**：张小雅、王美丽、李明轩
- **新角色**：林志强
- **场景**：温馨咖啡店

### 预期结果
- **张小雅**：高一致性评分（95%+），绿色显示
- **王美丽**：高一致性评分（88%+），绿色显示
- **李明轩**：高一致性评分（92%+），绿色显示
- **林志强**：新角色，蓝色显示，建议建立DNA档案

## 🛠️ 故障排除

### 常见问题
1. **角色显示为蓝色但应该是已知角色**
   - 检查角色名称是否完全匹配
   - 确认项目角色库中是否存在该角色

2. **一致性评分显示为0**
   - 检查AI配置是否正确
   - 确认网络连接正常

3. **验证器无法启动**
   - 确认生成记录已保存
   - 检查数据库连接状态

### 调试模式
在浏览器开发者工具中查看：
- **Console日志**：API调用和响应信息
- **Network标签**：API请求状态
- **Application标签**：本地存储数据

## 🎉 成功标志

完成快速体验后，您应该看到：
- ✅ 角色一致性指示器正常显示
- ✅ 一致性评分计算正确
- ✅ 视频生成配置完整
- ✅ 验证结果详细准确

## 📚 下一步学习

1. **深入了解**：阅读完整使用说明文档
2. **自定义配置**：尝试不同的一致性模式
3. **高级功能**：探索约束权重调节
4. **质量优化**：根据验证建议改进角色描述

---

🎊 **恭喜！您已成功体验双重约束角色一致性系统的核心功能。**

这个系统将帮助您创建具有高度角色一致性的AI视频内容，大大提升视频质量和观看体验。
