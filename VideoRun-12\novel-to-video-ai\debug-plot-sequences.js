const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugPlotSequences() {
  try {
    console.log('🔍 检查剧情序列的时长数据...');
    
    // 查询最近的剧集
    const episode = await prisma.episode.findFirst({
      where: {
        title: '雪夜初逢'
      }
    });
    
    if (!episode) {
      console.log('❌ 未找到"雪夜初逢"剧集');
      return;
    }
    
    console.log(`✅ 找到剧集: ${episode.title}`);
    
    if (episode.plotInfo) {
      try {
        const plotInfo = JSON.parse(episode.plotInfo);
        console.log('\n📋 剧情信息结构:');
        console.log('- plotInfo keys:', Object.keys(plotInfo));
        
        if (plotInfo.plotSequences) {
          const sequences = JSON.parse(plotInfo.plotSequences);
          console.log(`\n🎬 剧情序列 (${sequences.length}个):`);
          
          sequences.forEach((seq, i) => {
            console.log(`\n序列 ${i + 1}:`);
            console.log(`  动作: ${seq.action || '无'}`);
            console.log(`  时长: ${seq.duration || '无'} (类型: ${typeof seq.duration})`);
            console.log(`  描述: ${seq.description?.substring(0, 50) || '无'}...`);
            
            // 测试时长解析
            const parsedDuration = parseDuration(seq.duration);
            console.log(`  解析后时长: ${parsedDuration}秒`);
          });
          
          // 计算总时长
          const totalDuration = sequences.reduce((sum, seq) => {
            return sum + parseDuration(seq.duration);
          }, 0);
          console.log(`\n📊 总时长: ${totalDuration}秒`);
          
        } else {
          console.log('❌ 没有找到plotSequences');
        }
      } catch (e) {
        console.log(`❌ 解析剧情信息失败: ${e.message}`);
      }
    } else {
      console.log('❌ 剧集没有剧情信息');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 复制解析时长的函数
function parseDuration(duration) {
  if (typeof duration === 'number') return duration;
  if (typeof duration === 'string') {
    const match = duration.match(/(\d+)/);
    return match ? parseInt(match[1]) : 15;
  }
  return 15;
}

debugPlotSequences();
