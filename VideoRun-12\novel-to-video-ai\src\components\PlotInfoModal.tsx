'use client'

import { useState, useEffect } from 'react'
import { X, Users, MapPin, Zap, ChevronRight } from 'lucide-react'

interface PlotInfoModalProps {
  episodeId: string
  episodeTitle: string
  isOpen: boolean
  onClose: () => void
}

interface PlotInfo {
  characters: string[]
  scenes: Array<{
    location: string
    description: string
    atmosphere: string
  }>
  events: Array<{
    normal: string
    conflict: string
    escalation: string
    participants: string[]
    location: string
    actions: string[]
  }>
}

export default function PlotInfoModal({ episodeId, episodeTitle, isOpen, onClose }: PlotInfoModalProps) {
  const [plotInfo, setPlotInfo] = useState<PlotInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isOpen && episodeId) {
      loadPlotInfo()
    }
  }, [isOpen, episodeId])

  const loadPlotInfo = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/analyze-plot?episodeId=${episodeId}`)
      const data = await response.json()

      if (data.success) {
        const info: PlotInfo = {
          characters: JSON.parse(data.data.characters),
          scenes: JSON.parse(data.data.scenes),
          events: JSON.parse(data.data.events),
        }
        setPlotInfo(info)
      } else {
        setError(data.error || '获取剧情信息失败')
      }
    } catch (error) {
      setError('获取剧情信息失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">{episodeTitle} - 剧情信息</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X size={20} />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-gray-600 mt-2">加载中...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
              <button
                onClick={loadPlotInfo}
                className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                重试
              </button>
            </div>
          )}

          {plotInfo && (
            <div className="space-y-6">
              {/* 本集人物 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <Users className="mr-2 text-blue-600" size={20} />
                  本集人物
                </h3>
                <div className="flex flex-wrap gap-2">
                  {plotInfo.characters.map((character, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {character}
                    </span>
                  ))}
                </div>
              </div>

              {/* 场景信息 */}
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <MapPin className="mr-2 text-green-600" size={20} />
                  场景信息
                </h3>
                <div className="space-y-3">
                  {plotInfo.scenes.map((scene, index) => (
                    <div key={index} className="bg-white rounded p-3 border border-green-200">
                      <div className="font-medium text-green-800 mb-2">
                        📍 {scene.location}
                      </div>
                      <div className="text-sm text-gray-700 mb-2">
                        <strong>环境描述：</strong>{scene.description}
                      </div>
                      <div className="text-sm text-gray-700">
                        <strong>氛围设定：</strong>{scene.atmosphere}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 事件三要素 */}
              <div className="bg-purple-50 rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3 flex items-center">
                  <Zap className="mr-2 text-purple-600" size={20} />
                  事件三要素
                </h3>
                <div className="space-y-4">
                  {plotInfo.events.map((event, index) => (
                    <div key={index} className="bg-white rounded p-4 border border-purple-200">
                      <div className="mb-4">
                        <h4 className="font-medium text-purple-800 mb-2">
                          事件 {index + 1}
                        </h4>
                        
                        {/* 事件流程 */}
                        <div className="space-y-3">
                          <div className="flex items-start">
                            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                              <span className="text-green-600 text-xs">1</span>
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm text-green-700">正常状态</div>
                              <div className="text-sm text-gray-700 mt-1">{event.normal}</div>
                            </div>
                          </div>

                          <div className="flex items-center justify-center">
                            <ChevronRight className="text-gray-400" size={16} />
                          </div>

                          <div className="flex items-start">
                            <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                              <span className="text-yellow-600 text-xs">2</span>
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm text-yellow-700">矛盾冲突</div>
                              <div className="text-sm text-gray-700 mt-1">{event.conflict}</div>
                            </div>
                          </div>

                          <div className="flex items-center justify-center">
                            <ChevronRight className="text-gray-400" size={16} />
                          </div>

                          <div className="flex items-start">
                            <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                              <span className="text-red-600 text-xs">3</span>
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm text-red-700">升级事件</div>
                              <div className="text-sm text-gray-700 mt-1">{event.escalation}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 事件详情 */}
                      <div className="border-t pt-3 mt-3 space-y-2">
                        <div className="text-sm">
                          <strong className="text-gray-600">参与角色：</strong>
                          <span className="text-gray-700">{event.participants.join('、')}</span>
                        </div>
                        <div className="text-sm">
                          <strong className="text-gray-600">发生地点：</strong>
                          <span className="text-gray-700">{event.location}</span>
                        </div>
                        <div className="text-sm">
                          <strong className="text-gray-600">具体行为：</strong>
                          <span className="text-gray-700">{event.actions.join('、')}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
