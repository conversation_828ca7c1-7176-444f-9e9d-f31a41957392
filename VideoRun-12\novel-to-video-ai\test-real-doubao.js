// 测试真实豆包API密钥
async function testRealDoubao() {
  try {
    console.log('🔑 测试真实豆包API密钥...');
    
    const realApiKey = '6c29371e-9766-46bb-9d24-f436c8038c6a';
    const modelName = 'doubao-seedance-1-0-pro-250528';
    
    console.log('\n📝 配置信息:');
    console.log('   模型:', modelName);
    console.log('   API密钥:', realApiKey.substring(0, 8) + '...');
    
    // 直接测试豆包API
    console.log('\n🧪 直接测试豆包API...');
    
    const testResponse = await fetch('http://localhost:3000/api/models/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'doubao',
        model: modelName,
        apiKey: realApiKey
      })
    });
    
    const result = await testResponse.json();
    
    console.log('📊 测试结果:');
    if (result.success) {
      console.log('✅ 连接成功！');
    } else {
      console.log('❌ 连接失败:', result.error);
      
      // 分析具体错误
      if (result.error.includes('API key')) {
        console.log('🔍 API密钥问题');
      } else if (result.error.includes('does not exist')) {
        console.log('🔍 模型不存在或无权访问');
      } else if (result.error.includes('internal error')) {
        console.log('🔍 服务内部错误');
      } else if (result.error.includes('Not Found')) {
        console.log('🔍 API端点问题');
      } else {
        console.log('🔍 其他错误类型');
      }
    }
    
    // 如果失败，尝试直接调用火山方舟API
    if (!result.success) {
      console.log('\n🔄 直接调用火山方舟API测试...');
      
      try {
        const directResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${realApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: modelName,
            messages: [
              { role: 'user', content: '生成一个5秒的测试视频' }
            ],
            max_tokens: 10
          })
        });
        
        console.log('📊 直接API调用状态:', directResponse.status);
        
        if (directResponse.ok) {
          console.log('✅ 直接API调用成功');
          const data = await directResponse.json();
          console.log('📦 响应数据:', JSON.stringify(data, null, 2));
        } else {
          const errorText = await directResponse.text();
          console.log('❌ 直接API调用失败:', errorText);
        }
      } catch (error) {
        console.log('💥 直接API调用异常:', error.message);
      }
    }
    
    console.log('\n🎯 总结:');
    if (result.success) {
      console.log('   ✅ 豆包API配置正确');
      console.log('   ✅ 可以开始使用视频生成功能');
    } else {
      console.log('   ❌ 需要进一步排查问题');
      console.log('   🔍 检查模型权限和配置');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testRealDoubao();
