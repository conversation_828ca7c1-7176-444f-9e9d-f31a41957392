"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately";
exports.ids = ["vendor-chunks/@react-stately"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-stately/flags/dist/import.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@react-stately/flags/dist/import.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableShadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$12b151d9882e9985),\n/* harmony export */   enableTableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$d9d8a0f82de49530),\n/* harmony export */   shadowDOM: () => (/* binding */ $f4e2df6bd15f8569$export$98658e8c59125e6a),\n/* harmony export */   tableNestedRows: () => (/* binding */ $f4e2df6bd15f8569$export$1b00cb14a96194e6)\n/* harmony export */ });\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ let $f4e2df6bd15f8569$var$_tableNestedRows = false;\nlet $f4e2df6bd15f8569$var$_shadowDOM = false;\nfunction $f4e2df6bd15f8569$export$d9d8a0f82de49530() {\n    $f4e2df6bd15f8569$var$_tableNestedRows = true;\n}\nfunction $f4e2df6bd15f8569$export$1b00cb14a96194e6() {\n    return $f4e2df6bd15f8569$var$_tableNestedRows;\n}\nfunction $f4e2df6bd15f8569$export$12b151d9882e9985() {\n    $f4e2df6bd15f8569$var$_shadowDOM = true;\n}\nfunction $f4e2df6bd15f8569$export$98658e8c59125e6a() {\n    return $f4e2df6bd15f8569$var$_shadowDOM;\n}\n\n\n\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-stately/flags/dist/import.mjs\n");

/***/ })

};
;