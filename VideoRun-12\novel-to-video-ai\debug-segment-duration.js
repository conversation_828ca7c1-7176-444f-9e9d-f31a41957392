const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugSegmentDuration() {
  try {
    console.log('🔍 检查视频片段的时长设置...');
    
    // 查询最近的视频片段
    const segments = await prisma.videoSegment.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        storyVideo: true
      }
    });
    
    console.log(`找到 ${segments.length} 个视频片段:`);
    
    segments.forEach((segment, index) => {
      console.log(`\n${index + 1}. 片段: ${segment.title.substring(0, 50)}...`);
      console.log(`   片段时长: ${segment.duration}秒`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   创建时间: ${segment.createdAt}`);
      
      // 解析metadata查看更多信息
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   提供商: ${metadata.provider || '未知'}`);
          console.log(`   任务ID: ${metadata.taskId || '无'}`);
          if (metadata.error) {
            console.log(`   错误: ${metadata.error}`);
          }
        } catch (e) {
          console.log(`   元数据解析失败`);
        }
      }
    });
    
    // 检查剧集的剧情分析结果
    console.log('\n🎬 检查剧情分析结果中的时长设置...');
    
    const episodes = await prisma.episode.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3
    });
    
    episodes.forEach((episode, index) => {
      console.log(`\n剧集 ${index + 1}: ${episode.title}`);
      
      if (episode.plotInfo) {
        try {
          const plotInfo = JSON.parse(episode.plotInfo);
          
          if (plotInfo.plotSequences) {
            const sequences = JSON.parse(plotInfo.plotSequences);
            console.log(`   剧情序列数量: ${sequences.length}`);
            
            sequences.forEach((seq, i) => {
              console.log(`   序列 ${i + 1}: ${seq.duration || '无时长'} - ${seq.description?.substring(0, 30)}...`);
            });
          }
        } catch (e) {
          console.log(`   剧情信息解析失败: ${e.message}`);
        }
      }
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugSegmentDuration();
