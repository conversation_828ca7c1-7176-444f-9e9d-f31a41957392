// 测试增强的一致性分析功能
async function testConsistencyAnalysis() {
  try {
    console.log('🧪 测试增强的一致性分析功能...');
    
    const episodeId = 'cmc60kmf30001vm3cw6lvt6ki'; // 刚创建的测试剧集ID
    const episodeContent = `张小雅走进咖啡店，看到了正在工作的王美丽。王美丽依然是那个温柔的咖啡店老板，瓜子脸上带着微笑，波浪卷发在灯光下闪闪发光。

"小雅！"王美丽惊喜地叫道，"你怎么来了？"

张小雅露出了她标志性的甜美笑容，马尾辫随着她的动作轻轻摆动。"美丽姐，我想你了。"

这时，李明轩教授也走进了咖啡店。他依然是那副学者的模样，方脸上带着温和的表情，花白的短发整齐地梳着。

"两位美女在聊什么呢？"李明轩温文尔雅地问道。

突然，一个陌生的年轻人走了进来。他看起来大约二十五岁，高高瘦瘦的，戴着黑框眼镜，穿着简单的白色T恤和牛仔裤。

"请问这里有WiFi吗？"陌生人问道，"我是新来的程序员，叫做林志强。"`;

    console.log('📤 发送分析请求...');
    
    const response = await fetch('http://localhost:3001/api/ai/analyze-detailed-plot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        episodeId,
        episodeContent,
        customPrompt: '请特别注意角色一致性约束'
      })
    });

    console.log('📊 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API响应失败:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 一致性分析成功！');
      console.log('\n📋 分析结果：');
      
      // 角色分析结果
      if (result.data.characters && result.data.characters.length > 0) {
        console.log('\n👥 角色分析结果:');
        result.data.characters.forEach((character, index) => {
          console.log(`\n${index + 1}. ${character.name}`);
          console.log(`   身份: ${character.identity || '未设置'}`);
          console.log(`   外貌: ${character.appearance || '未设置'}`);
          console.log(`   性格: ${character.personality || '未设置'}`);
          console.log(`   已知角色: ${character.isKnownCharacter ? '是' : '否'}`);
          
          if (character.consistencyInfo) {
            console.log(`   一致性匹配度: ${character.consistencyInfo.consistencyMatch ? (character.consistencyInfo.consistencyMatch * 100).toFixed(1) + '%' : '未设置'}`);
            console.log(`   匹配角色ID: ${character.consistencyInfo.matchedCharacterId || '无'}`);
            if (character.consistencyInfo.differences && character.consistencyInfo.differences.length > 0) {
              console.log(`   差异: ${character.consistencyInfo.differences.join(', ')}`);
            }
            if (character.consistencyInfo.consistencyConstraints) {
              console.log(`   约束建议: ${character.consistencyInfo.consistencyConstraints}`);
            }
          }
        });
      }
      
      // 场景分析结果
      if (result.data.scenes && result.data.scenes.length > 0) {
        console.log('\n🏞️ 场景分析结果:');
        result.data.scenes.forEach((scene, index) => {
          console.log(`\n${index + 1}. ${scene.location}`);
          console.log(`   描述: ${scene.description || '未设置'}`);
          console.log(`   氛围: ${scene.atmosphere || '未设置'}`);
        });
      }
      
      // 情感弧线
      if (result.data.emotionalArc) {
        console.log('\n💫 情感弧线:');
        console.log(`   ${result.data.emotionalArc}`);
      }
      
      // 生成的提示词
      if (result.data.generatedPrompt) {
        console.log('\n🎬 生成的视频提示词:');
        console.log(`   长度: ${result.data.generatedPrompt.length} 字符`);
        console.log(`   预览: ${result.data.generatedPrompt.substring(0, 200)}...`);
      }
      
    } else {
      console.log('❌ 分析失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testConsistencyAnalysis();
