/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-image/route";
exports.ids = ["app/api/ai/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-image/route.ts */ \"(rsc)/./src/app/api/ai/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-image/route\",\n        pathname: \"/api/ai/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-image/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-image/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/ai/generate-image/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// 豆包图像生成API（基于官方文档）\nasync function generateImageWithDoubao(request) {\n    try {\n        console.log('🎨 调用豆包图像生成API');\n        console.log('使用端点: ep-20250626132353-nlrtf');\n        // 构建请求参数（基于官方文档格式）\n        const requestBody = {\n            model: 'ep-20250626132353-nlrtf',\n            prompt: request.prompt,\n            response_format: 'url',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: request.guidance || 3,\n            watermark: true\n        };\n        console.log('📝 请求参数:', {\n            model: requestBody.model,\n            promptLength: request.prompt.length,\n            size: requestBody.size,\n            guidance_scale: requestBody.guidance_scale\n        });\n        // 注意：豆包API需要复杂的AK/SK签名认证\n        // 这里先使用模拟响应来演示功能\n        console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证');\n        // 模拟API调用延迟\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // 智能模拟响应 - 根据提示词生成合适的占位图\n        const mockImageUrl = generateSmartMockImage(request.prompt, request.width || 1024, request.height || 1024);\n        console.log('✅ 模拟图像生成成功:', {\n            imageUrl: '已生成模拟图像',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: requestBody.guidance_scale,\n            promptType: detectPromptType(request.prompt)\n        });\n        return {\n            imageUrl: mockImageUrl,\n            width: request.width || 1024,\n            height: request.height || 1024,\n            guidance: requestBody.guidance_scale,\n            metadata: {\n                model: requestBody.model,\n                prompt: request.prompt,\n                provider: 'doubao-mock',\n                generatedAt: new Date().toISOString(),\n                promptType: detectPromptType(request.prompt),\n                note: '这是模拟响应，实际部署需要实现火山引擎AK/SK签名认证'\n            }\n        };\n    } catch (error) {\n        console.error('豆包图像生成失败:', error);\n        throw error;\n    }\n}\n// 检测提示词类型\nfunction detectPromptType(prompt) {\n    const lowerPrompt = prompt.toLowerCase();\n    if (lowerPrompt.includes('正面视图') || lowerPrompt.includes('front view') || lowerPrompt.includes('facing camera')) {\n        return 'character-front';\n    } else if (lowerPrompt.includes('侧面视图') || lowerPrompt.includes('side view') || lowerPrompt.includes('profile')) {\n        return 'character-side';\n    } else if (lowerPrompt.includes('背面视图') || lowerPrompt.includes('back view') || lowerPrompt.includes('rear')) {\n        return 'character-back';\n    } else if (lowerPrompt.includes('角色') || lowerPrompt.includes('character') || lowerPrompt.includes('人物')) {\n        return 'character-general';\n    } else if (lowerPrompt.includes('场景') || lowerPrompt.includes('scene') || lowerPrompt.includes('landscape')) {\n        return 'scene';\n    } else {\n        return 'general';\n    }\n}\n// 智能生成模拟图像URL\nfunction generateSmartMockImage(prompt, width, height) {\n    const promptType = detectPromptType(prompt);\n    const timestamp = Date.now();\n    // 根据提示词类型生成不同的占位图\n    switch(promptType){\n        case 'character-front':\n            // 正面角色 - 使用人像占位图\n            return generateCharacterPlaceholder('front', width, height, timestamp);\n        case 'character-side':\n            // 侧面角色 - 使用侧面人像占位图\n            return generateCharacterPlaceholder('side', width, height, timestamp);\n        case 'character-back':\n            // 背面角色 - 使用背影占位图\n            return generateCharacterPlaceholder('back', width, height, timestamp);\n        case 'character-general':\n            // 一般角色 - 使用通用人像占位图\n            return generateCharacterPlaceholder('general', width, height, timestamp);\n        case 'scene':\n            // 场景 - 使用风景占位图\n            return `https://picsum.photos/${width}/${height}?random=${timestamp}`;\n        default:\n            // 默认 - 使用随机占位图\n            return `https://picsum.photos/${width}/${height}?random=${timestamp}`;\n    }\n}\n// 生成角色占位图\nfunction generateCharacterPlaceholder(viewType, width, height, timestamp) {\n    // 使用SVG生成角色占位图\n    const svg = generateCharacterSVG(viewType, width, height);\n    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;\n}\n// 生成角色SVG占位图\nfunction generateCharacterSVG(viewType, width, height) {\n    const viewConfig = {\n        front: {\n            title: '正面视图',\n            color: '#8B5CF6',\n            icon: '👤'\n        },\n        side: {\n            title: '侧面视图',\n            color: '#3B82F6',\n            icon: '🧑‍💼'\n        },\n        back: {\n            title: '背面视图',\n            color: '#10B981',\n            icon: '🚶‍♂️'\n        },\n        general: {\n            title: '角色图像',\n            color: '#F59E0B',\n            icon: '👨‍🎨'\n        }\n    };\n    const config = viewConfig[viewType] || viewConfig.general;\n    return `\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${config.color}20;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${config.color}40;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n\n      <!-- 背景 -->\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#bg)\"/>\n\n      <!-- 边框 -->\n      <rect x=\"2\" y=\"2\" width=\"${width - 4}\" height=\"${height - 4}\"\n            fill=\"none\" stroke=\"${config.color}\" stroke-width=\"3\" stroke-dasharray=\"10,5\"/>\n\n      <!-- 中心图标 -->\n      <circle cx=\"${width / 2}\" cy=\"${height / 2 - 40}\" r=\"60\" fill=\"${config.color}30\" stroke=\"${config.color}\" stroke-width=\"2\"/>\n      <text x=\"${width / 2}\" y=\"${height / 2 - 30}\" text-anchor=\"middle\" font-size=\"48\" fill=\"${config.color}\">${config.icon}</text>\n\n      <!-- 标题 -->\n      <text x=\"${width / 2}\" y=\"${height / 2 + 40}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"24\" font-weight=\"bold\" fill=\"${config.color}\">\n        ${config.title}\n      </text>\n\n      <!-- 副标题 -->\n      <text x=\"${width / 2}\" y=\"${height / 2 + 70}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"16\" fill=\"${config.color}80\">\n        角色设计占位图\n      </text>\n\n      <!-- 尺寸信息 -->\n      <text x=\"${width / 2}\" y=\"${height - 30}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"${config.color}60\">\n        ${width} × ${height}\n      </text>\n    </svg>\n  `.trim();\n}\n// GET接口 - 获取配置信息\nasync function GET(request) {\n    try {\n        console.log('🔍 获取图像生成配置');\n        // 获取豆包图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                provider: 'doubao',\n                model: imageConfig.name,\n                endpoint: imageConfig.model,\n                status: imageConfig.status,\n                defaultSettings: {\n                    width: 1024,\n                    height: 1024,\n                    guidance: 3,\n                    quality: 'high'\n                },\n                availableSizes: [\n                    '512x512',\n                    '768x768',\n                    '1024x1024',\n                    '768x1024',\n                    '1024x768'\n                ]\n            }\n        });\n    } catch (error) {\n        console.error('获取图像生成配置失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '获取配置失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST接口 - 生成图像\nasync function POST(request) {\n    try {\n        console.log('🎨 开始图像生成');\n        const body = await request.json();\n        if (!body.prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '提示词不能为空'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📝 图像生成请求:', {\n            promptLength: body.prompt.length,\n            width: body.width || 1024,\n            height: body.height || 1024,\n            guidance: body.guidance || 3\n        });\n        // 获取图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`);\n        // 调用豆包图像生成\n        const result = await generateImageWithDoubao(body);\n        console.log('✅ 图像生成成功:', {\n            hasImageUrl: !!result.imageUrl,\n            width: result.width,\n            height: result.height,\n            guidance: result.guidance\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result\n        });\n    } catch (error) {\n        console.error('图像生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '图像生成失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-image/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();