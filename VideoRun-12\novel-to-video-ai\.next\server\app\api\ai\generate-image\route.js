/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-image/route";
exports.ids = ["app/api/ai/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-image/route.ts */ \"(rsc)/./src/app/api/ai/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-image/route\",\n        pathname: \"/api/ai/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-image/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-image/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/ai/generate-image/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// 豆包图像生成API（基于官方文档）\nasync function generateImageWithDoubao(request) {\n    try {\n        console.log('🎨 调用豆包图像生成API');\n        console.log('使用端点: ep-20250626132353-nlrtf');\n        // 构建请求参数（基于官方文档格式）\n        const requestBody = {\n            model: 'ep-20250626132353-nlrtf',\n            prompt: request.prompt,\n            response_format: 'url',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: request.guidance || 3,\n            watermark: true\n        };\n        console.log('📝 请求参数:', {\n            model: requestBody.model,\n            promptLength: request.prompt.length,\n            size: requestBody.size,\n            guidance_scale: requestBody.guidance_scale\n        });\n        // 注意：豆包API需要复杂的AK/SK签名认证\n        // 这里先使用模拟响应来演示功能\n        console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证');\n        // 模拟API调用延迟\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // 智能模拟响应 - 根据提示词生成合适的占位图\n        const mockImageUrl = generateSmartMockImage(request.prompt, request.width || 1024, request.height || 1024);\n        console.log('✅ 模拟图像生成成功:', {\n            imageUrl: '已生成模拟图像',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: requestBody.guidance_scale,\n            promptType: detectPromptType(request.prompt)\n        });\n        return {\n            imageUrl: mockImageUrl,\n            width: request.width || 1024,\n            height: request.height || 1024,\n            guidance: requestBody.guidance_scale,\n            metadata: {\n                model: requestBody.model,\n                prompt: request.prompt,\n                provider: 'doubao-mock',\n                generatedAt: new Date().toISOString(),\n                promptType: detectPromptType(request.prompt),\n                note: '这是模拟响应，实际部署需要实现火山引擎AK/SK签名认证'\n            }\n        };\n    } catch (error) {\n        console.error('豆包图像生成失败:', error);\n        throw error;\n    }\n}\n// 检测提示词类型\nfunction detectPromptType(prompt) {\n    const lowerPrompt = prompt.toLowerCase();\n    if (lowerPrompt.includes('正面视图') || lowerPrompt.includes('front view') || lowerPrompt.includes('facing camera')) {\n        return 'character-front';\n    } else if (lowerPrompt.includes('侧面视图') || lowerPrompt.includes('side view') || lowerPrompt.includes('profile')) {\n        return 'character-side';\n    } else if (lowerPrompt.includes('背面视图') || lowerPrompt.includes('back view') || lowerPrompt.includes('rear')) {\n        return 'character-back';\n    } else if (lowerPrompt.includes('角色') || lowerPrompt.includes('character') || lowerPrompt.includes('人物')) {\n        return 'character-general';\n    } else if (lowerPrompt.includes('场景') || lowerPrompt.includes('scene') || lowerPrompt.includes('landscape')) {\n        return 'scene';\n    } else {\n        return 'general';\n    }\n}\n// 智能生成模拟图像URL\nfunction generateSmartMockImage(prompt, width, height) {\n    const promptType = detectPromptType(prompt);\n    const timestamp = Date.now();\n    // 根据提示词类型生成不同的占位图\n    switch(promptType){\n        case 'character-front':\n            // 正面角色 - 使用人像占位图\n            return generateCharacterPlaceholder('front', width, height, timestamp, prompt);\n        case 'character-side':\n            // 侧面角色 - 使用侧面人像占位图\n            return generateCharacterPlaceholder('side', width, height, timestamp, prompt);\n        case 'character-back':\n            // 背面角色 - 使用背影占位图\n            return generateCharacterPlaceholder('back', width, height, timestamp, prompt);\n        case 'character-general':\n            // 一般角色 - 使用通用人像占位图\n            return generateCharacterPlaceholder('general', width, height, timestamp, prompt);\n        case 'scene':\n            // 场景 - 使用风景占位图\n            return `https://picsum.photos/${width}/${height}?random=${timestamp}`;\n        default:\n            // 默认 - 使用随机占位图\n            return `https://picsum.photos/${width}/${height}?random=${timestamp}`;\n    }\n}\n// 生成角色占位图\nfunction generateCharacterPlaceholder(viewType, width, height, timestamp, prompt) {\n    // 使用SVG生成角色占位图，包含角色信息\n    const svg = generateCharacterSVG(viewType, width, height, prompt);\n    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;\n}\n// 生成角色SVG占位图\nfunction generateCharacterSVG(viewType, width, height, prompt) {\n    const viewConfig = {\n        front: {\n            title: '正面视图',\n            color: '#8B5CF6',\n            icon: '👤'\n        },\n        side: {\n            title: '侧面视图',\n            color: '#3B82F6',\n            icon: '🧑‍💼'\n        },\n        back: {\n            title: '背面视图',\n            color: '#10B981',\n            icon: '🚶‍♂️'\n        },\n        general: {\n            title: '角色图像',\n            color: '#F59E0B',\n            icon: '👨‍🎨'\n        }\n    };\n    const config = viewConfig[viewType] || viewConfig.general;\n    // 从提示词中提取角色信息\n    const characterInfo = extractCharacterInfo(prompt || '');\n    return `\n    <svg width=\"${width}\" height=\"${height}\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:${config.color}20;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:${config.color}40;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n\n      <!-- 背景 -->\n      <rect width=\"100%\" height=\"100%\" fill=\"url(#bg)\"/>\n\n      <!-- 边框 -->\n      <rect x=\"2\" y=\"2\" width=\"${width - 4}\" height=\"${height - 4}\"\n            fill=\"none\" stroke=\"${config.color}\" stroke-width=\"3\" stroke-dasharray=\"10,5\"/>\n\n      <!-- 中心图标 -->\n      <circle cx=\"${width / 2}\" cy=\"${height / 2 - 80}\" r=\"60\" fill=\"${config.color}30\" stroke=\"${config.color}\" stroke-width=\"2\"/>\n      <text x=\"${width / 2}\" y=\"${height / 2 - 70}\" text-anchor=\"middle\" font-size=\"48\" fill=\"${config.color}\">${config.icon}</text>\n\n      <!-- 标题 -->\n      <text x=\"${width / 2}\" y=\"${height / 2 - 10}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"24\" font-weight=\"bold\" fill=\"${config.color}\">\n        ${config.title}\n      </text>\n\n      <!-- 角色信息 -->\n      ${characterInfo.map((info, index)=>`\n        <text x=\"${width / 2}\" y=\"${height / 2 + 20 + index * 25}\" text-anchor=\"middle\"\n              font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"${config.color}\">\n          ${info}\n        </text>\n      `).join('')}\n\n      <!-- 提示信息 -->\n      <text x=\"${width / 2}\" y=\"${height - 60}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"${config.color}60\">\n        模拟响应 - 实际部署将生成真实图像\n      </text>\n\n      <!-- 尺寸信息 -->\n      <text x=\"${width / 2}\" y=\"${height - 30}\" text-anchor=\"middle\"\n            font-family=\"Arial, sans-serif\" font-size=\"12\" fill=\"${config.color}60\">\n        ${width} × ${height}\n      </text>\n    </svg>\n  `.trim();\n}\n// 从提示词中提取角色信息\nfunction extractCharacterInfo(prompt) {\n    const info = [];\n    // 提取关键特征\n    const features = [\n        {\n            pattern: /(\\d+岁?[男女性]?[性别]?)/g,\n            label: '年龄性别'\n        },\n        {\n            pattern: /(黑发|金发|棕发|银发|短发|长发|卷发|直发)/g,\n            label: '发型'\n        },\n        {\n            pattern: /(蓝眼|棕眼|黑眼|绿眼|灰眼)/g,\n            label: '眼睛'\n        },\n        {\n            pattern: /(高挑|娇小|健壮|苗条|魁梧)/g,\n            label: '身材'\n        },\n        {\n            pattern: /(军装|西装|长裙|短裙|盔甲|便装)/g,\n            label: '服装'\n        },\n        {\n            pattern: /(战士|法师|弓箭手|骑士|商人|学者|贵族)/g,\n            label: '职业'\n        }\n    ];\n    features.forEach(({ pattern, label })=>{\n        const matches = prompt.match(pattern);\n        if (matches && matches.length > 0) {\n            info.push(`${label}: ${matches[0]}`);\n        }\n    });\n    // 如果没有提取到特征，显示通用信息\n    if (info.length === 0) {\n        info.push('基于角色描述生成');\n        info.push('包含详细特征信息');\n    }\n    // 限制显示的信息条数\n    return info.slice(0, 4);\n}\n// GET接口 - 获取配置信息\nasync function GET(request) {\n    try {\n        console.log('🔍 获取图像生成配置');\n        // 获取豆包图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                provider: 'doubao',\n                model: imageConfig.name,\n                endpoint: imageConfig.model,\n                status: imageConfig.status,\n                defaultSettings: {\n                    width: 1024,\n                    height: 1024,\n                    guidance: 3,\n                    quality: 'high'\n                },\n                availableSizes: [\n                    '512x512',\n                    '768x768',\n                    '1024x1024',\n                    '768x1024',\n                    '1024x768'\n                ]\n            }\n        });\n    } catch (error) {\n        console.error('获取图像生成配置失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '获取配置失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST接口 - 生成图像\nasync function POST(request) {\n    try {\n        console.log('🎨 开始图像生成');\n        const body = await request.json();\n        if (!body.prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '提示词不能为空'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📝 图像生成请求:', {\n            promptLength: body.prompt.length,\n            width: body.width || 1024,\n            height: body.height || 1024,\n            guidance: body.guidance || 3\n        });\n        // 获取图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`);\n        // 调用豆包图像生成\n        const result = await generateImageWithDoubao(body);\n        console.log('✅ 图像生成成功:', {\n            hasImageUrl: !!result.imageUrl,\n            width: result.width,\n            height: result.height,\n            guidance: result.guidance\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result\n        });\n    } catch (error) {\n        console.error('图像生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '图像生成失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-image/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();