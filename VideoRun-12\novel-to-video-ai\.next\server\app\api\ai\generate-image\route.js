/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-image/route";
exports.ids = ["app/api/ai/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-image/route.ts */ \"(rsc)/./src/app/api/ai/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-image/route\",\n        pathname: \"/api/ai/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-image/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-image/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/ai/generate-image/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// 豆包图像生成API（基于官方文档）\nasync function generateImageWithDoubao(request) {\n    try {\n        console.log('🎨 调用豆包图像生成API');\n        console.log('使用端点: ep-20250626132353-nlrtf');\n        // 构建请求参数（基于官方文档格式）\n        const requestBody = {\n            model: 'ep-20250626132353-nlrtf',\n            prompt: request.prompt,\n            response_format: 'url',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: request.guidance || 3,\n            watermark: true\n        };\n        console.log('📝 请求参数:', {\n            model: requestBody.model,\n            promptLength: request.prompt.length,\n            size: requestBody.size,\n            guidance_scale: requestBody.guidance_scale\n        });\n        // 注意：豆包API需要复杂的AK/SK签名认证\n        // 这里先使用模拟响应来演示功能\n        console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证');\n        // 模拟API调用延迟\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // 生成模拟的图像URL\n        const mockImageUrl = `https://picsum.photos/${request.width || 1024}/${request.height || 1024}?random=${Date.now()}`;\n        console.log('✅ 模拟图像生成成功:', {\n            imageUrl: '已生成模拟图像',\n            size: requestBody.size,\n            guidance_scale: requestBody.guidance_scale\n        });\n        return {\n            imageUrl: mockImageUrl,\n            width: request.width || 1024,\n            height: request.height || 1024,\n            guidance: requestBody.guidance_scale,\n            metadata: {\n                model: requestBody.model,\n                prompt: request.prompt,\n                provider: 'doubao-mock',\n                generatedAt: new Date().toISOString(),\n                note: '这是模拟响应，实际部署需要实现火山引擎AK/SK签名认证'\n            }\n        };\n    } catch (error) {\n        console.error('豆包图像生成失败:', error);\n        throw error;\n    }\n}\n// GET接口 - 获取配置信息\nasync function GET(request) {\n    try {\n        console.log('🔍 获取图像生成配置');\n        // 获取豆包图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                provider: 'doubao',\n                model: imageConfig.name,\n                endpoint: imageConfig.model,\n                status: imageConfig.status,\n                defaultSettings: {\n                    width: 1024,\n                    height: 1024,\n                    guidance: 3,\n                    quality: 'high'\n                },\n                availableSizes: [\n                    '512x512',\n                    '768x768',\n                    '1024x1024',\n                    '768x1024',\n                    '1024x768'\n                ]\n            }\n        });\n    } catch (error) {\n        console.error('获取图像生成配置失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '获取配置失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST接口 - 生成图像\nasync function POST(request) {\n    try {\n        console.log('🎨 开始图像生成');\n        const body = await request.json();\n        if (!body.prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '提示词不能为空'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📝 图像生成请求:', {\n            promptLength: body.prompt.length,\n            width: body.width || 1024,\n            height: body.height || 1024,\n            guidance: body.guidance || 3\n        });\n        // 获取图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`);\n        // 调用豆包图像生成\n        const result = await generateImageWithDoubao(body);\n        console.log('✅ 图像生成成功:', {\n            hasImageUrl: !!result.imageUrl,\n            width: result.width,\n            height: result.height,\n            guidance: result.guidance\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result\n        });\n    } catch (error) {\n        console.error('图像生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '图像生成失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-image/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();