/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/generate-image/route";
exports.ids = ["app/api/ai/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/generate-image/route.ts */ \"(rsc)/./src/app/api/ai/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/generate-image/route\",\n        pathname: \"/api/ai/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/generate-image/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\ai\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_ai_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/generate-image/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/ai/generate-image/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// 豆包图像生成API（基于官方文档）\nasync function generateImageWithDoubao(request) {\n    try {\n        console.log('🎨 调用豆包图像生成API');\n        console.log('使用端点: ep-20250626132353-nlrtf');\n        // 构建请求参数（基于官方文档格式）\n        const requestBody = {\n            model: 'ep-20250626132353-nlrtf',\n            prompt: request.prompt,\n            response_format: 'url',\n            size: `${request.width || 1024}x${request.height || 1024}`,\n            guidance_scale: request.guidance || 3,\n            watermark: true\n        };\n        console.log('📝 请求参数:', {\n            model: requestBody.model,\n            promptLength: request.prompt.length,\n            size: requestBody.size,\n            guidance_scale: requestBody.guidance_scale\n        });\n        // 调用豆包API（使用正确的认证方式）\n        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {\n            method: 'POST',\n            headers: {\n                'Authorization': 'Bearer $ARK_API_KEY',\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(requestBody)\n        });\n        console.log('豆包API响应状态:', response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('豆包API调用失败:', response.status, errorText);\n            throw new Error(`豆包API调用失败: ${response.status} ${errorText}`);\n        }\n        const result = await response.json();\n        console.log('豆包API响应成功:', {\n            hasData: !!result.data,\n            dataLength: result.data?.length || 0\n        });\n        if (!result.data || result.data.length === 0) {\n            throw new Error('豆包API未返回图像数据');\n        }\n        const imageData = result.data[0];\n        return {\n            imageUrl: imageData.url,\n            width: request.width || 1024,\n            height: request.height || 1024,\n            guidance: requestBody.guidance_scale,\n            metadata: {\n                model: requestBody.model,\n                prompt: request.prompt,\n                provider: 'doubao',\n                generatedAt: new Date().toISOString()\n            }\n        };\n    } catch (error) {\n        console.error('豆包图像生成失败:', error);\n        throw error;\n    }\n}\n// GET接口 - 获取配置信息\nasync function GET(request) {\n    try {\n        console.log('🔍 获取图像生成配置');\n        // 获取豆包图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                provider: 'doubao',\n                model: imageConfig.name,\n                endpoint: imageConfig.model,\n                status: imageConfig.status,\n                defaultSettings: {\n                    width: 1024,\n                    height: 1024,\n                    guidance: 3,\n                    quality: 'high'\n                },\n                availableSizes: [\n                    '512x512',\n                    '768x768',\n                    '1024x1024',\n                    '768x1024',\n                    '1024x768'\n                ]\n            }\n        });\n    } catch (error) {\n        console.error('获取图像生成配置失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '获取配置失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST接口 - 生成图像\nasync function POST(request) {\n    try {\n        console.log('🎨 开始图像生成');\n        const body = await request.json();\n        if (!body.prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '提示词不能为空'\n            }, {\n                status: 400\n            });\n        }\n        console.log('📝 图像生成请求:', {\n            promptLength: body.prompt.length,\n            width: body.width || 1024,\n            height: body.height || 1024,\n            guidance: body.guidance || 3\n        });\n        // 获取图像生成配置\n        const imageConfig = await prisma.aIConfig.findFirst({\n            where: {\n                provider: 'doubao',\n                supportsImage: true,\n                enabled: true\n            },\n            orderBy: {\n                updatedAt: 'desc'\n            }\n        });\n        if (!imageConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '未找到可用的图像生成配置'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`);\n        // 调用豆包图像生成\n        const result = await generateImageWithDoubao(body);\n        console.log('✅ 图像生成成功:', {\n            hasImageUrl: !!result.imageUrl,\n            width: result.width,\n            height: result.height,\n            guidance: result.guidance\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result\n        });\n    } catch (error) {\n        console.error('图像生成失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '图像生成失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/generate-image/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fgenerate-image%2Froute&page=%2Fapi%2Fai%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fgenerate-image%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();