import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 获取指定项目的AI配置
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const { projectId } = params

    const aiConfig = await prisma.aIConfig.findUnique({
      where: { projectId },
    })

    if (!aiConfig) {
      return NextResponse.json({
        success: true,
        data: null,
        message: '未找到AI配置',
      })
    }

    return NextResponse.json({
      success: true,
      data: aiConfig,
    })
  } catch (error) {
    console.error('获取AI配置失败:', error)
    return NextResponse.json(
      { success: false, error: '获取AI配置失败' },
      { status: 500 }
    )
  }
}
