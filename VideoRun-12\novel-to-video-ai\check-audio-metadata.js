// 检查片段音频元数据
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAudioMetadata() {
  try {
    console.log('🎵 检查片段音频元数据...\n');

    // 获取片段4的详细信息
    const segment = await prisma.videoSegment.findUnique({
      where: { id: 'cmcd1zlh40006vmsog1hji44i' }
    });

    if (!segment) {
      console.log('❌ 未找到片段4');
      return;
    }

    console.log(`📋 片段 ${segment.segmentIndex} 信息:`);
    console.log(`   标题: ${segment.title}`);
    console.log(`   状态: ${segment.status}`);
    console.log(`   视频URL: ${segment.videoUrl ? '已生成' : '未生成'}`);
    console.log(`   更新时间: ${segment.updatedAt}`);

    if (segment.metadata) {
      try {
        const metadata = JSON.parse(segment.metadata);
        console.log('\n🎵 音频信息:');
        
        if (metadata.audio) {
          console.log(`   ✅ 音频已生成`);
          console.log(`   音频URL: ${metadata.audio.audioUrl ? '已生成' : '未生成'}`);
          console.log(`   时长: ${metadata.audio.duration}秒`);
          console.log(`   提供商: ${metadata.audio.provider}`);
          console.log(`   生成时间: ${metadata.audio.generatedAt}`);
          
          // 检查音频URL格式
          if (metadata.audio.audioUrl) {
            if (metadata.audio.audioUrl.startsWith('data:audio/')) {
              console.log(`   格式: Base64编码音频 (${Math.round(metadata.audio.audioUrl.length / 1024)}KB)`);
            } else {
              console.log(`   格式: 外部URL`);
            }
          }
        } else {
          console.log(`   ❌ 未找到音频信息`);
        }

        // 显示其他元数据
        if (metadata.videoGeneration) {
          console.log('\n🎬 视频生成信息:');
          console.log(`   提供商: ${metadata.provider}`);
          console.log(`   模型: ${metadata.model}`);
          console.log(`   任务ID: ${metadata.videoGeneration.taskId}`);
          console.log(`   生成时间: ${metadata.generatedAt}`);
        }

      } catch (error) {
        console.error('❌ 解析元数据失败:', error);
      }
    } else {
      console.log('\n⚠️ 未找到元数据');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAudioMetadata();
