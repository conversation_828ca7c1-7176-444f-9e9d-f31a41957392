import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 图像生成请求接口
interface ImageGenerationRequest {
  prompt: string
  negativePrompt?: string
  width?: number
  height?: number
  guidance?: number
  style?: string
  quality?: string
}

// 豆包图像生成API（基于官方文档）
async function generateImageWithDoubao(request: ImageGenerationRequest) {
  try {
    console.log('🎨 调用豆包图像生成API')
    console.log('使用端点: ep-20250626132353-nlrtf')
    
    // 构建请求参数（基于官方文档格式）
    const requestBody = {
      model: 'ep-20250626132353-nlrtf',
      prompt: request.prompt,
      response_format: 'url',
      size: `${request.width || 1024}x${request.height || 1024}`,
      guidance_scale: request.guidance || 3,
      watermark: true
    }
    
    console.log('📝 请求参数:', {
      model: requestBody.model,
      promptLength: request.prompt.length,
      size: requestBody.size,
      guidance_scale: requestBody.guidance_scale
    })
    
    // 调用豆包API（使用正确的认证方式）
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer $ARK_API_KEY',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    
    console.log('豆包API响应状态:', response.status)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('豆包API调用失败:', response.status, errorText)
      throw new Error(`豆包API调用失败: ${response.status} ${errorText}`)
    }
    
    const result = await response.json()
    console.log('豆包API响应成功:', {
      hasData: !!result.data,
      dataLength: result.data?.length || 0
    })
    
    if (!result.data || result.data.length === 0) {
      throw new Error('豆包API未返回图像数据')
    }
    
    const imageData = result.data[0]
    return {
      imageUrl: imageData.url,
      width: request.width || 1024,
      height: request.height || 1024,
      guidance: requestBody.guidance_scale,
      metadata: {
        model: requestBody.model,
        prompt: request.prompt,
        provider: 'doubao',
        generatedAt: new Date().toISOString()
      }
    }
    
  } catch (error) {
    console.error('豆包图像生成失败:', error)
    throw error
  }
}

// GET接口 - 获取配置信息
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 获取图像生成配置')
    
    // 获取豆包图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    if (!imageConfig) {
      return NextResponse.json(
        { 
          success: false,
          error: '未找到可用的图像生成配置' 
        },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: {
        provider: 'doubao',
        model: imageConfig.name,
        endpoint: imageConfig.model,
        status: imageConfig.status,
        defaultSettings: {
          width: 1024,
          height: 1024,
          guidance: 3,
          quality: 'high'
        },
        availableSizes: [
          '512x512',
          '768x768', 
          '1024x1024',
          '768x1024',
          '1024x768'
        ]
      }
    })
    
  } catch (error) {
    console.error('获取图像生成配置失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '获取配置失败'
      },
      { status: 500 }
    )
  }
}

// POST接口 - 生成图像
export async function POST(request: NextRequest) {
  try {
    console.log('🎨 开始图像生成')
    
    const body: ImageGenerationRequest = await request.json()
    
    if (!body.prompt) {
      return NextResponse.json(
        { 
          success: false,
          error: '提示词不能为空' 
        },
        { status: 400 }
      )
    }
    
    console.log('📝 图像生成请求:', {
      promptLength: body.prompt.length,
      width: body.width || 1024,
      height: body.height || 1024,
      guidance: body.guidance || 3
    })
    
    // 获取图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    if (!imageConfig) {
      return NextResponse.json(
        { 
          success: false,
          error: '未找到可用的图像生成配置' 
        },
        { status: 404 }
      )
    }
    
    console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`)
    
    // 调用豆包图像生成
    const result = await generateImageWithDoubao(body)
    
    console.log('✅ 图像生成成功:', {
      hasImageUrl: !!result.imageUrl,
      width: result.width,
      height: result.height,
      guidance: result.guidance
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('图像生成失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '图像生成失败'
      },
      { status: 500 }
    )
  }
}
