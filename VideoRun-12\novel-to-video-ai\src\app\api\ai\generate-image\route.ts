import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 图像生成请求接口
interface ImageGenerationRequest {
  prompt: string
  negativePrompt?: string
  width?: number
  height?: number
  guidance?: number
  style?: string
  quality?: string
}

// 豆包图像生成API（基于官方文档）
async function generateImageWithDoubao(request: ImageGenerationRequest) {
  try {
    console.log('🎨 调用豆包图像生成API')
    console.log('使用端点: ep-20250626132353-nlrtf')
    
    // 构建请求参数（基于官方文档格式）
    const requestBody = {
      model: 'ep-20250626132353-nlrtf',
      prompt: request.prompt,
      response_format: 'url',
      size: `${request.width || 1024}x${request.height || 1024}`,
      guidance_scale: request.guidance || 3,
      watermark: true
    }
    
    console.log('📝 请求参数:', {
      model: requestBody.model,
      promptLength: request.prompt.length,
      size: requestBody.size,
      guidance_scale: requestBody.guidance_scale
    })
    
    // 注意：豆包API需要复杂的AK/SK签名认证
    // 这里先使用模拟响应来演示功能
    console.log('⚠️ 当前使用模拟响应，实际部署需要实现火山引擎AK/SK签名认证')

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 智能模拟响应 - 根据提示词生成合适的占位图
    const mockImageUrl = generateSmartMockImage(request.prompt, request.width || 1024, request.height || 1024)

    console.log('✅ 模拟图像生成成功:', {
      imageUrl: '已生成模拟图像',
      size: `${request.width || 1024}x${request.height || 1024}`,
      guidance_scale: requestBody.guidance_scale,
      promptType: detectPromptType(request.prompt)
    })
    return {
      imageUrl: mockImageUrl,
      width: request.width || 1024,
      height: request.height || 1024,
      guidance: requestBody.guidance_scale,
      metadata: {
        model: requestBody.model,
        prompt: request.prompt,
        provider: 'doubao-mock',
        generatedAt: new Date().toISOString(),
        promptType: detectPromptType(request.prompt),
        note: '这是模拟响应，实际部署需要实现火山引擎AK/SK签名认证'
      }
    }
    
  } catch (error) {
    console.error('豆包图像生成失败:', error)
    throw error
  }
}

// 检测提示词类型
function detectPromptType(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase()

  if (lowerPrompt.includes('正面视图') || lowerPrompt.includes('front view') || lowerPrompt.includes('facing camera')) {
    return 'character-front'
  } else if (lowerPrompt.includes('侧面视图') || lowerPrompt.includes('side view') || lowerPrompt.includes('profile')) {
    return 'character-side'
  } else if (lowerPrompt.includes('背面视图') || lowerPrompt.includes('back view') || lowerPrompt.includes('rear')) {
    return 'character-back'
  } else if (lowerPrompt.includes('角色') || lowerPrompt.includes('character') || lowerPrompt.includes('人物')) {
    return 'character-general'
  } else if (lowerPrompt.includes('场景') || lowerPrompt.includes('scene') || lowerPrompt.includes('landscape')) {
    return 'scene'
  } else {
    return 'general'
  }
}

// 智能生成模拟图像URL
function generateSmartMockImage(prompt: string, width: number, height: number): string {
  const promptType = detectPromptType(prompt)
  const timestamp = Date.now()

  // 根据提示词类型生成不同的占位图
  switch (promptType) {
    case 'character-front':
      // 正面角色 - 使用人像占位图
      return generateCharacterPlaceholder('front', width, height, timestamp)

    case 'character-side':
      // 侧面角色 - 使用侧面人像占位图
      return generateCharacterPlaceholder('side', width, height, timestamp)

    case 'character-back':
      // 背面角色 - 使用背影占位图
      return generateCharacterPlaceholder('back', width, height, timestamp)

    case 'character-general':
      // 一般角色 - 使用通用人像占位图
      return generateCharacterPlaceholder('general', width, height, timestamp)

    case 'scene':
      // 场景 - 使用风景占位图
      return `https://picsum.photos/${width}/${height}?random=${timestamp}`

    default:
      // 默认 - 使用随机占位图
      return `https://picsum.photos/${width}/${height}?random=${timestamp}`
  }
}

// 生成角色占位图
function generateCharacterPlaceholder(viewType: string, width: number, height: number, timestamp: number): string {
  // 使用SVG生成角色占位图
  const svg = generateCharacterSVG(viewType, width, height)
  return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`
}

// 生成角色SVG占位图
function generateCharacterSVG(viewType: string, width: number, height: number): string {
  const viewConfig = {
    front: {
      title: '正面视图',
      color: '#8B5CF6',
      icon: '👤'
    },
    side: {
      title: '侧面视图',
      color: '#3B82F6',
      icon: '🧑‍💼'
    },
    back: {
      title: '背面视图',
      color: '#10B981',
      icon: '🚶‍♂️'
    },
    general: {
      title: '角色图像',
      color: '#F59E0B',
      icon: '👨‍🎨'
    }
  }

  const config = viewConfig[viewType] || viewConfig.general

  return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${config.color}20;stop-opacity:1" />
          <stop offset="100%" style="stop-color:${config.color}40;stop-opacity:1" />
        </linearGradient>
      </defs>

      <!-- 背景 -->
      <rect width="100%" height="100%" fill="url(#bg)"/>

      <!-- 边框 -->
      <rect x="2" y="2" width="${width-4}" height="${height-4}"
            fill="none" stroke="${config.color}" stroke-width="3" stroke-dasharray="10,5"/>

      <!-- 中心图标 -->
      <circle cx="${width/2}" cy="${height/2-40}" r="60" fill="${config.color}30" stroke="${config.color}" stroke-width="2"/>
      <text x="${width/2}" y="${height/2-30}" text-anchor="middle" font-size="48" fill="${config.color}">${config.icon}</text>

      <!-- 标题 -->
      <text x="${width/2}" y="${height/2+40}" text-anchor="middle"
            font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="${config.color}">
        ${config.title}
      </text>

      <!-- 副标题 -->
      <text x="${width/2}" y="${height/2+70}" text-anchor="middle"
            font-family="Arial, sans-serif" font-size="16" fill="${config.color}80">
        角色设计占位图
      </text>

      <!-- 尺寸信息 -->
      <text x="${width/2}" y="${height-30}" text-anchor="middle"
            font-family="Arial, sans-serif" font-size="14" fill="${config.color}60">
        ${width} × ${height}
      </text>
    </svg>
  `.trim()
}

// GET接口 - 获取配置信息
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 获取图像生成配置')
    
    // 获取豆包图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    if (!imageConfig) {
      return NextResponse.json(
        { 
          success: false,
          error: '未找到可用的图像生成配置' 
        },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: {
        provider: 'doubao',
        model: imageConfig.name,
        endpoint: imageConfig.model,
        status: imageConfig.status,
        defaultSettings: {
          width: 1024,
          height: 1024,
          guidance: 3,
          quality: 'high'
        },
        availableSizes: [
          '512x512',
          '768x768', 
          '1024x1024',
          '768x1024',
          '1024x768'
        ]
      }
    })
    
  } catch (error) {
    console.error('获取图像生成配置失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '获取配置失败'
      },
      { status: 500 }
    )
  }
}

// POST接口 - 生成图像
export async function POST(request: NextRequest) {
  try {
    console.log('🎨 开始图像生成')
    
    const body: ImageGenerationRequest = await request.json()
    
    if (!body.prompt) {
      return NextResponse.json(
        { 
          success: false,
          error: '提示词不能为空' 
        },
        { status: 400 }
      )
    }
    
    console.log('📝 图像生成请求:', {
      promptLength: body.prompt.length,
      width: body.width || 1024,
      height: body.height || 1024,
      guidance: body.guidance || 3
    })
    
    // 获取图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    if (!imageConfig) {
      return NextResponse.json(
        { 
          success: false,
          error: '未找到可用的图像生成配置' 
        },
        { status: 404 }
      )
    }
    
    console.log(`🎨 使用配置: ${imageConfig.name} (${imageConfig.model})`)
    
    // 调用豆包图像生成
    const result = await generateImageWithDoubao(body)
    
    console.log('✅ 图像生成成功:', {
      hasImageUrl: !!result.imageUrl,
      width: result.width,
      height: result.height,
      guidance: result.guidance
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('图像生成失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '图像生成失败'
      },
      { status: 500 }
    )
  }
}
