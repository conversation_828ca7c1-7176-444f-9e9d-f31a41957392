(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/fileParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileParseError": (()=>FileParseError),
    "FileParser": (()=>FileParser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mammoth$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mammoth/lib/index.js [app-client] (ecmascript)");
;
class FileParseError extends Error {
    code;
    constructor(message, code){
        super(message), this.code = code;
        this.name = 'FileParseError';
    }
}
class FileParser {
    // 支持的文件类型
    static SUPPORTED_TYPES = {
        'text/plain': [
            '.txt'
        ],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
            '.docx'
        ],
        'application/msword': [
            '.doc'
        ] // 虽然不完全支持，但可以尝试
    };
    // 获取支持的文件扩展名
    static getSupportedExtensions() {
        return Object.values(this.SUPPORTED_TYPES).flat();
    }
    // 验证文件类型
    static validateFile(file) {
        const extension = this.getFileExtension(file.name);
        const supportedExtensions = this.getSupportedExtensions();
        if (!supportedExtensions.includes(extension)) {
            throw new FileParseError(`不支持的文件格式: ${extension}。支持的格式: ${supportedExtensions.join(', ')}`, 'UNSUPPORTED_FORMAT');
        }
        // 验证文件大小 (最大 50MB)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new FileParseError(`文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`, 'FILE_TOO_LARGE');
        }
        // 验证文件不为空
        if (file.size === 0) {
            throw new FileParseError('文件不能为空', 'EMPTY_FILE');
        }
    }
    // 解析文件内容
    static async parseFile(file) {
        this.validateFile(file);
        const extension = this.getFileExtension(file.name);
        try {
            switch(extension){
                case '.txt':
                    return await this.parseTxtFile(file);
                case '.docx':
                    return await this.parseDocxFile(file);
                case '.doc':
                    // .doc 文件尝试用 docx 解析器处理
                    return await this.parseDocxFile(file);
                default:
                    throw new FileParseError(`不支持的文件格式: ${extension}`, 'UNSUPPORTED_FORMAT');
            }
        } catch (error) {
            if (error instanceof FileParseError) {
                throw error;
            }
            throw new FileParseError(`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`, 'PARSE_ERROR');
        }
    }
    // 解析 TXT 文件
    static async parseTxtFile(file) {
        return new Promise((resolve, reject)=>{
            const reader = new FileReader();
            reader.onload = (e)=>{
                const content = e.target?.result;
                if (!content || content.trim().length === 0) {
                    reject(new FileParseError('文件内容为空', 'EMPTY_CONTENT'));
                    return;
                }
                resolve(content);
            };
            reader.onerror = ()=>{
                reject(new FileParseError('文件读取失败', 'READ_ERROR'));
            };
            // 尝试不同的编码
            reader.readAsText(file, 'utf-8');
        });
    }
    // 解析 DOCX 文件
    static async parseDocxFile(file) {
        try {
            const arrayBuffer = await this.fileToArrayBuffer(file);
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mammoth$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].extractRawText({
                arrayBuffer
            });
            if (!result.value || result.value.trim().length === 0) {
                throw new FileParseError('Word文档内容为空', 'EMPTY_CONTENT');
            }
            // 如果有警告，记录但不阻止处理
            if (result.messages && result.messages.length > 0) {
                console.warn('Word文档解析警告:', result.messages);
            }
            // 清理文本内容
            return this.cleanText(result.value);
        } catch (error) {
            if (error instanceof FileParseError) {
                throw error;
            }
            throw new FileParseError(`Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`, 'DOCX_PARSE_ERROR');
        }
    }
    // 将文件转换为 ArrayBuffer
    static async fileToArrayBuffer(file) {
        return new Promise((resolve, reject)=>{
            const reader = new FileReader();
            reader.onload = (e)=>{
                const result = e.target?.result;
                if (result instanceof ArrayBuffer) {
                    resolve(result);
                } else {
                    reject(new Error('文件读取结果不是 ArrayBuffer'));
                }
            };
            reader.onerror = ()=>{
                reject(new Error('文件读取失败'));
            };
            reader.readAsArrayBuffer(file);
        });
    }
    // 清理文本内容
    static cleanText(text) {
        return text.replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\r/g, '\n') // 处理单独的 \r
        .replace(/\n{3,}/g, '\n\n') // 合并多个连续换行
        .trim();
    }
    // 获取文件扩展名
    static getFileExtension(fileName) {
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex === -1) {
            return '';
        }
        return fileName.substring(lastDotIndex).toLowerCase();
    }
    // 获取文件类型描述
    static getFileTypeDescription(file) {
        const extension = this.getFileExtension(file.name);
        switch(extension){
            case '.txt':
                return '纯文本文件';
            case '.docx':
                return 'Word 文档 (新版)';
            case '.doc':
                return 'Word 文档 (旧版)';
            default:
                return '未知格式';
        }
    }
    // 格式化文件大小
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = [
            'B',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/promptGenerator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 角色形象提示词生成器
// 专门用于从小说角色信息生成高质量的图像生成提示词
__turbopack_context__.s({
    "generateCharacterPrompt": (()=>generateCharacterPrompt),
    "generateConsistencyPrompt": (()=>generateConsistencyPrompt),
    "generateNegativePrompt": (()=>generateNegativePrompt)
});
// 中文特征词汇到英文的映射
const FEATURE_MAPPING = {
    // 年龄相关
    '少女': 'young girl',
    '青年女性': 'young woman',
    '中年女性': 'middle-aged woman',
    '少年': 'young boy',
    '青年男性': 'young man',
    '中年男性': 'middle-aged man',
    // 脸型
    '瓜子脸': 'oval face',
    '圆脸': 'round face',
    '方脸': 'square face',
    '长脸': 'long face',
    '心形脸': 'heart-shaped face',
    // 眼睛
    '大眼睛': 'large eyes',
    '小眼睛': 'small eyes',
    '丹凤眼': 'phoenix eyes',
    '杏眼': 'almond eyes',
    '圆眼': 'round eyes',
    '深邃': 'deep-set eyes',
    // 发型
    '长发': 'long hair',
    '短发': 'short hair',
    '中长发': 'medium length hair',
    '卷发': 'curly hair',
    '直发': 'straight hair',
    '波浪发': 'wavy hair',
    '马尾': 'ponytail',
    '双马尾': 'twin tails',
    '刘海': 'bangs',
    // 身材
    '高挑': 'tall and slender',
    '娇小': 'petite',
    '匀称': 'well-proportioned',
    '丰满': 'full-figured',
    '苗条': 'slim',
    '健美': 'athletic build',
    // 气质
    '温柔': 'gentle',
    '冷酷': 'cold',
    '活泼': 'lively',
    '优雅': 'elegant',
    '知性': 'intellectual',
    '神秘': 'mysterious',
    '坚强': 'strong-willed',
    '可爱': 'cute',
    // 服装
    '职业装': 'business attire',
    '休闲装': 'casual wear',
    '正装': 'formal wear',
    '校服': 'school uniform',
    '古装': 'traditional costume',
    '现代装': 'modern clothing'
};
// 艺术风格配置
const ART_STYLES = {
    anime: {
        keywords: 'anime style, manga style, cel shading, clean lines',
        description: '日式动漫风格'
    },
    realistic: {
        keywords: 'photorealistic, hyperrealistic, professional photography',
        description: '写实摄影风格'
    },
    'semi-realistic': {
        keywords: 'semi-realistic, digital art, detailed illustration',
        description: '半写实插画风格'
    },
    'concept-art': {
        keywords: 'concept art, character design, professional illustration',
        description: '概念设计风格'
    }
};
// 视角配置
const VIEW_TYPES = {
    front: 'front view, facing camera, looking at viewer',
    side: 'side view, 90-degree profile, side angle',
    back: 'back view, rear angle, showing back',
    'three-quarter': 'three-quarter view, slight angle'
};
// 质量等级配置
const QUALITY_LEVELS = {
    standard: 'good quality, detailed',
    high: 'high quality, high resolution, detailed, sharp focus',
    masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'
};
function generateCharacterPrompt(character, options = {}) {
    const { artStyle = 'anime', viewType = 'front', background = 'white', quality = 'high', customEnhancement = '' } = options;
    // 构建提示词各部分
    const parts = [];
    // 1. 质量和风格
    parts.push(QUALITY_LEVELS[quality]);
    parts.push(ART_STYLES[artStyle].keywords);
    // 2. 基础信息提取
    const basicInfo = extractBasicInfo(character);
    if (basicInfo) parts.push(basicInfo);
    // 3. 外貌特征
    const appearance = extractAppearanceFeatures(character);
    if (appearance) parts.push(appearance);
    // 4. 服装风格
    const clothing = extractClothingStyle(character);
    if (clothing) parts.push(clothing);
    // 5. 气质表现
    const temperament = extractTemperament(character);
    if (temperament) parts.push(temperament);
    // 6. 视角和姿态
    parts.push(VIEW_TYPES[viewType]);
    parts.push('character design sheet, reference pose');
    // 7. 背景设置
    const backgroundDesc = getBackgroundDescription(background);
    parts.push(backgroundDesc);
    // 8. 技术参数
    parts.push('professional character design, clean composition');
    // 9. 自定义增强
    if (customEnhancement.trim()) {
        parts.push(customEnhancement);
    }
    return parts.filter(Boolean).join(', ');
}
/**
 * 提取基础信息（年龄、性别、身份）
 */ function extractBasicInfo(character) {
    const info = [];
    if (character.identity) {
        // 从身份信息中提取年龄和职业
        const ageMatch = character.identity.match(/(\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/);
        if (ageMatch) {
            const age = ageMatch[1] || getAgeFromDescription(ageMatch[0]);
            info.push(`${age} years old`);
        }
        // 提取性别（如果身份中包含）
        if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {
            info.push('woman');
        } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {
            info.push('man');
        }
        // 提取职业
        const profession = extractProfession(character.identity);
        if (profession) info.push(profession);
    }
    return info.join(' ');
}
/**
 * 提取外貌特征
 */ function extractAppearanceFeatures(character) {
    const features = [];
    // 处理五官特征
    if (character.facial) {
        const facialFeatures = translateFeatures(character.facial);
        features.push(facialFeatures);
    }
    // 处理身材特征
    if (character.physique) {
        const bodyFeatures = translateFeatures(character.physique);
        features.push(bodyFeatures);
    }
    // 处理发型
    if (character.hairstyle) {
        const hairFeatures = translateFeatures(character.hairstyle);
        features.push(hairFeatures);
    }
    return features.filter(Boolean).join(', ');
}
/**
 * 提取服装风格
 */ function extractClothingStyle(character) {
    if (!character.clothing) return '';
    return translateFeatures(character.clothing);
}
/**
 * 提取气质特征
 */ function extractTemperament(character) {
    if (!character.personality) return '';
    const temperamentWords = [];
    // 从性格描述中提取气质关键词
    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{
        if (character.personality.includes(chinese)) {
            temperamentWords.push(english);
        }
    });
    // 添加表情描述
    if (character.personality.includes('温柔') || character.personality.includes('善良')) {
        temperamentWords.push('gentle smile', 'warm expression');
    } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {
        temperamentWords.push('serious expression', 'cold gaze');
    } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {
        temperamentWords.push('cheerful smile', 'bright expression');
    }
    return temperamentWords.join(', ');
}
/**
 * 翻译特征描述
 */ function translateFeatures(chineseText) {
    let result = chineseText;
    // 使用映射表进行翻译
    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{
        const regex = new RegExp(chinese, 'g');
        result = result.replace(regex, english);
    });
    // 清理和优化
    result = result.replace(/[，。；：]/g, ',') // 替换中文标点
    .replace(/\s+/g, ' ') // 合并空格
    .replace(/,+/g, ',') // 合并逗号
    .trim();
    return result;
}
/**
 * 从描述中提取年龄
 */ function getAgeFromDescription(ageDesc) {
    const ageMap = {
        '十几岁': '16',
        '二十多岁': '25',
        '三十多岁': '35',
        '四十多岁': '45'
    };
    return ageMap[ageDesc] || '25';
}
/**
 * 提取职业信息
 */ function extractProfession(identity) {
    const professions = {
        '学生': 'student',
        '老师': 'teacher',
        '医生': 'doctor',
        '护士': 'nurse',
        '律师': 'lawyer',
        '工程师': 'engineer',
        '设计师': 'designer',
        '程序员': 'programmer',
        '经理': 'manager',
        '秘书': 'secretary',
        '销售': 'salesperson',
        '警察': 'police officer',
        '军人': 'soldier',
        '艺术家': 'artist',
        '作家': 'writer',
        '记者': 'journalist'
    };
    for (const [chinese, english] of Object.entries(professions)){
        if (identity.includes(chinese)) {
            return english;
        }
    }
    return '';
}
/**
 * 获取背景描述
 */ function getBackgroundDescription(background) {
    const backgrounds = {
        white: 'clean white background',
        transparent: 'transparent background',
        simple: 'simple background',
        detailed: 'detailed background scene'
    };
    return backgrounds[background] || 'clean white background';
}
function generateNegativePrompt() {
    return [
        'blurry',
        'low quality',
        'pixelated',
        'deformed',
        'distorted',
        'extra limbs',
        'missing limbs',
        'extra fingers',
        'missing fingers',
        'bad anatomy',
        'bad proportions',
        'ugly',
        'duplicate',
        'watermark',
        'signature',
        'text',
        'logo',
        'oversaturated',
        'undersaturated',
        'overexposed',
        'underexposed'
    ].join(', ');
}
function generateConsistencyPrompt(character, basePrompt) {
    // 提取身材和服饰特征（保持一致）
    const consistentFeatures = extractConsistentFeatures(character);
    // 添加强化细节一致性的约束
    const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories';
    return {
        // 正面：完整脸部可见
        front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,
        // 侧面：90度侧脸轮廓
        side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,
        // 背面：看不到脸
        back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`
    };
}
/**
 * 提取一致性特征（身材、服饰、发型等保持不变的特征）
 */ function extractConsistentFeatures(character) {
    const features = [];
    // 身材特征（保持一致）
    if (character.physique) {
        features.push(character.physique);
    }
    // 发型和发色（保持一致）
    if (character.hairstyle) {
        features.push(character.hairstyle);
    }
    // 服饰风格（保持一致，强调细节一致性）
    if (character.clothing) {
        features.push(character.clothing);
        // 添加细节一致性约束
        features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理');
    }
    // 身份特征（保持一致）
    if (character.identity) {
        features.push(character.identity);
    }
    // 添加强化一致性约束
    features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰');
    return features.join('，');
}
function extractFaceShape(facial) {
    for (const [chinese, english] of Object.entries(FEATURE_MAPPING)){
        if (facial.includes(chinese) && chinese.includes('脸')) {
            return english;
        }
    }
    return '';
}
function extractHairStyle(hairstyle) {
    const hairFeatures = [];
    Object.entries(FEATURE_MAPPING).forEach(([chinese, english])=>{
        if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {
            hairFeatures.push(english);
        }
    });
    return hairFeatures.join(' ');
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/projects/[id]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProjectDetailPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-client] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$film$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Film$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/film.js [app-client] (ecmascript) <export default as Film>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/book-open.js [app-client] (ecmascript) <export default as BookOpen>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ModelSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ModelSelector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProjectFileUpload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ProjectFileUpload.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/CharacterManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/EpisodeManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DetailedPlotExtraction$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/DetailedPlotExtraction.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoSegmentViewer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/VideoSegmentViewer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeVideoCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/EpisodeVideoCard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ConsistencyVideoGenerator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ConsistencyVideoGenerator.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
function ProjectDetailPage() {
    _s();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const projectId = params.id;
    const [project, setProject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [characters, setCharacters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [episodes, setEpisodes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // 从URL参数中获取当前标签页，如果没有则默认为'upload'
    const getInitialTab = ()=>{
        const tab = searchParams.get('tab');
        const validTabs = [
            'upload',
            'novel',
            'characters',
            'episodes',
            'videos'
        ];
        return validTabs.includes(tab) ? tab : 'upload';
    };
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getInitialTab());
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // 更新标签页并同时更新URL
    const updateActiveTab = (tab)=>{
        setActiveTab(tab);
        const newSearchParams = new URLSearchParams(searchParams.toString());
        newSearchParams.set('tab', tab);
        router.replace(`/projects/${projectId}?${newSearchParams.toString()}`, {
            scroll: false
        });
    };
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isAnalyzing, setIsAnalyzing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedModel, setSelectedModel] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 增强提示词相关状态
    const [showPromptDropdown, setShowPromptDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [enhancePrompt, setEnhancePrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [savedEnhancePrompt, setSavedEnhancePrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 详细剧情提取相关状态
    const [showDetailedPlotExtraction, setShowDetailedPlotExtraction] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [currentEpisodeId, setCurrentEpisodeId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [currentEpisodeTitle, setCurrentEpisodeTitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [currentEpisodeContent, setCurrentEpisodeContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 视频片段查看器相关状态
    const [showVideoSegmentViewer, setShowVideoSegmentViewer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [videoEpisodeId, setVideoEpisodeId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 视频生成模式：'auto' | 'manual'，默认为手动模式
    const [videoGenerationMode, setVideoGenerationMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('manual');
    // 一致性视频生成器相关状态
    const [showConsistencyVideoGenerator, setShowConsistencyVideoGenerator] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [consistencyEpisodeId, setConsistencyEpisodeId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [consistencyEpisodeTitle, setConsistencyEpisodeTitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 文件上传成功处理
    const handleUploadSuccess = (updatedProject)=>{
        setProject(updatedProject.project);
        setCharacters(updatedProject.characters || []);
        setEpisodes(updatedProject.episodes || []);
        updateActiveTab('characters');
    };
    // 文件上传错误处理
    const handleUploadError = (errorMessage)=>{
        setError(errorMessage);
    };
    // 剧情分析处理
    const handleAnalyzePlot = async (episodeId)=>{
        try {
            const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/analyze-plot`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                // 更新剧集状态
                setEpisodes((prev)=>prev.map((ep)=>ep.id === episodeId ? {
                            ...ep,
                            plotInfo: data.data
                        } : ep));
            } else {
                throw new Error(data.error || '剧情分析失败');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : '剧情分析失败');
        }
    };
    // 视频生成处理
    const handleGenerateVideo = async (episodeId)=>{
        try {
            const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/generate-video`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                // 更新剧集状态
                setEpisodes((prev)=>prev.map((ep)=>ep.id === episodeId ? {
                            ...ep,
                            status: 'video_generated'
                        } : ep));
            // 可以在这里显示视频脚本模态框
            } else {
                throw new Error(data.error || '视频生成失败');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : '视频生成失败');
        }
    };
    // 提取详细剧情信息处理
    const handleExtractDetailedPlot = (episodeId)=>{
        const episode = episodes.find((ep)=>ep.id === episodeId);
        if (episode) {
            setCurrentEpisodeId(episodeId);
            setCurrentEpisodeTitle(episode.title);
            setCurrentEpisodeContent(episode.content);
            setShowDetailedPlotExtraction(true);
        }
    };
    // 打开一致性视频生成器
    const handleOpenConsistencyVideoGenerator = (episodeId)=>{
        const episode = episodes.find((ep)=>ep.id === episodeId);
        if (episode) {
            setConsistencyEpisodeId(episodeId);
            setConsistencyEpisodeTitle(episode.title);
            setShowConsistencyVideoGenerator(true);
        }
    };
    // 一致性视频生成成功处理
    const handleConsistencyVideoGenerated = (videoData)=>{
        console.log('✅ 一致性视频生成成功:', videoData);
        // 可以在这里更新UI状态或显示成功消息
        alert(`一致性视频生成成功！\n生成ID: ${videoData.generationId}\n角色数量: ${videoData.consistencyInfo.characterCount}\n参考图像: ${videoData.consistencyInfo.referenceImageCount} 个`);
    };
    // 生成剧情视频处理
    const handleGenerateStoryVideo = async (episodeId, prompt, modelId)=>{
        try {
            console.log('🎬 开始生成剧情视频，episodeId:', episodeId);
            // 首先检查是否已有视频生成记录
            const checkResponse = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`);
            const checkData = await checkResponse.json();
            console.log('📊 检查现有片段结果:', checkData);
            if (checkData.success && checkData.data && checkData.data.segments.length > 0) {
                console.log(`⚠️ 发现现有片段 ${checkData.data.segments.length} 个，显示确认对话框`);
                // 如果已有视频记录，显示确认对话框
                const confirmed = confirm(`检测到该剧集已有 ${checkData.data.segments.length} 个视频片段。\n\n` + `重新生成将会：\n` + `• 删除所有现有的视频片段\n` + `• 停止正在进行的生成任务\n` + `• 重新开始生成新的视频片段\n\n` + `确定要继续吗？`);
                console.log('👤 用户确认结果:', confirmed);
                if (!confirmed) {
                    console.log('❌ 用户取消操作');
                    return; // 用户取消操作
                }
                console.log('✅ 用户确认继续，开始重新生成');
            } else {
                console.log('✨ 没有现有片段，直接开始生成');
            }
            const response = await fetch('/api/ai/generate-story-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    episodeId,
                    prompt,
                    projectId,
                    modelId,
                    generationMode: videoGenerationMode // 传递生成模式
                })
            });
            const data = await response.json();
            if (data.success) {
                // 显示详细的生成结果
                const message = data.data.message || '剧情视频片段创建完成！';
                const modeMessage = videoGenerationMode === 'auto' ? `共${data.data.totalSegments}个片段正在自动生成中...` : `共创建了${data.data.totalSegments}个片段，请在"剧情视频片段"页面手动生成每个片段。\n\n💡 当前为手动生成模式，您可以选择性地生成需要的片段。`;
                alert(`${message}\n\n${modeMessage}`);
                setShowDetailedPlotExtraction(false);
                // 直接打开视频片段查看器，不切换标签页
                // 这样用户可以继续在当前标签页查看剧情内容
                setVideoEpisodeId(episodeId);
                setShowVideoSegmentViewer(true);
            } else {
                // 处理角色图像先决条件错误
                if (data.requirementType === 'character_images') {
                    const missingChars = data.missingCharacters?.join('、') || '某些角色';
                    const errorMessage = `❌ 视频生成失败\n\n${data.error}\n\n解决方案：\n1. 点击"角色管理"标签页\n2. 为 ${missingChars} 生成角色形象\n3. 确保每个角色都有正面、侧面、背面三视图\n4. 重新尝试生成视频`;
                    alert(errorMessage);
                    // 自动切换到角色管理标签页
                    setActiveTab('characters');
                } else {
                    throw new Error(data.error || '剧情视频生成失败');
                }
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : '剧情视频生成失败');
        }
    };
    // 保存增强提示词
    const handleSaveEnhancePrompt = ()=>{
        setSavedEnhancePrompt(enhancePrompt);
        setShowPromptDropdown(false);
        // 保存到localStorage
        const storageKey = `enhance_prompt_reanalyze_${projectId}`;
        localStorage.setItem(storageKey, enhancePrompt);
    };
    // 从localStorage加载增强提示词
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProjectDetailPage.useEffect": ()=>{
            if (projectId) {
                const storageKey = `enhance_prompt_reanalyze_${projectId}`;
                const savedPrompt = localStorage.getItem(storageKey);
                if (savedPrompt) {
                    setSavedEnhancePrompt(savedPrompt);
                    setEnhancePrompt(savedPrompt);
                }
            }
        }
    }["ProjectDetailPage.useEffect"], [
        projectId
    ]);
    // 手动分析人物剧情
    const handleManualAnalyze = async ()=>{
        if (!project?.content) {
            setError('没有小说内容可供分析');
            return;
        }
        if (!selectedModel) {
            setError('请先选择分析模型');
            return;
        }
        setIsAnalyzing(true);
        setError(null);
        try {
            const response = await fetch(`/api/projects/${projectId}/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    modelId: selectedModel,
                    customPrompt: savedEnhancePrompt || undefined
                })
            });
            const data = await response.json();
            if (data.success) {
                // 更新项目数据
                setProject(data.data.project);
                setCharacters(data.data.characters || []);
                setEpisodes(data.data.episodes || []);
                // 切换到角色标签页
                updateActiveTab('characters');
            } else {
                throw new Error(data.error || 'AI分析失败');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : 'AI分析失败');
        } finally{
            setIsAnalyzing(false);
        }
    };
    // 监听URL参数变化，更新当前标签页
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProjectDetailPage.useEffect": ()=>{
            const tab = searchParams.get('tab');
            const validTabs = [
                'upload',
                'novel',
                'characters',
                'episodes',
                'videos'
            ];
            if (tab && validTabs.includes(tab) && tab !== activeTab) {
                setActiveTab(tab);
            }
        }
    }["ProjectDetailPage.useEffect"], [
        searchParams,
        activeTab
    ]);
    // 加载项目详情
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProjectDetailPage.useEffect": ()=>{
            if (projectId) {
                loadProjectDetail();
            }
        }
    }["ProjectDetailPage.useEffect"], [
        projectId
    ]);
    const loadProjectDetail = async ()=>{
        try {
            const response = await fetch(`/api/projects/${projectId}`);
            const data = await response.json();
            if (data.success) {
                setProject(data.data);
                setCharacters(data.data.characters || []);
                setEpisodes(data.data.episodes || []);
                // 只有在URL中没有指定标签页时，才根据项目状态设置默认标签页
                if (!searchParams.get('tab')) {
                    if (data.data.status === 'created') {
                        updateActiveTab('upload');
                    } else if (data.data.status === 'uploaded') {
                        updateActiveTab('novel');
                    } else {
                        updateActiveTab('characters');
                    }
                }
            } else {
                throw new Error(data.error || '加载项目失败');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : '加载项目失败');
        } finally{
            setLoading(false);
        }
    };
    // 重新加载项目数据的简化版本（用于角色管理器回调）
    const loadProject = async ()=>{
        try {
            const response = await fetch(`/api/projects/${projectId}`);
            const data = await response.json();
            if (data.success) {
                setProject(data.data);
                setCharacters(data.data.characters || []);
                setEpisodes(data.data.episodes || []);
            }
        } catch (error) {
            console.error('重新加载项目失败:', error);
        }
    };
    // 获取状态显示
    const getStatusDisplay = (status)=>{
        const statusMap = {
            created: {
                text: '已创建',
                color: 'bg-gray-100 text-gray-800',
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"]
            },
            uploaded: {
                text: '已上传',
                color: 'bg-blue-100 text-blue-800',
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"]
            },
            analyzing: {
                text: '分析中',
                color: 'bg-yellow-100 text-yellow-800',
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"]
            },
            completed: {
                text: '已完成',
                color: 'bg-green-100 text-green-800',
                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"]
            }
        };
        return statusMap[status] || statusMap.created;
    };
    // 标签页配置
    const tabs = [
        {
            id: 'upload',
            name: '上传文件',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"],
            description: '上传小说文件进行AI分析',
            disabled: false
        },
        {
            id: 'novel',
            name: '小说',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"],
            description: '查看已上传的小说内容',
            disabled: !project || project.status === 'created'
        },
        {
            id: 'characters',
            name: '角色',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
            description: '查看和管理提取的角色信息',
            disabled: !project || project.status === 'created'
        },
        {
            id: 'episodes',
            name: '剧集',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$film$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Film$3e$__["Film"],
            description: '查看和管理章节剧集',
            disabled: !project || project.status === 'created'
        },
        {
            id: 'videos',
            name: '视频',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"],
            description: '生成和管理视频脚本',
            disabled: !project || [
                'created',
                'uploaded'
            ].includes(project.status)
        }
    ];
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center h-64",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 455,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-2 text-gray-600",
                            children: "加载项目中..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 456,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 454,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/projects/[id]/page.tsx",
                lineNumber: 453,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/projects/[id]/page.tsx",
            lineNumber: 452,
            columnNumber: 7
        }, this);
    }
    if (error || !project) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                        className: "mx-auto h-12 w-12 text-red-400"
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 467,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "mt-2 text-sm font-medium text-gray-900",
                        children: "加载失败"
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 468,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-gray-500",
                        children: error || '项目不存在'
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 469,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/projects",
                            className: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                    className: "mr-2",
                                    size: 16
                                }, void 0, false, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 477,
                                    columnNumber: 15
                                }, this),
                                "返回项目列表"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 473,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 472,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/projects/[id]/page.tsx",
                lineNumber: 466,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/projects/[id]/page.tsx",
            lineNumber: 465,
            columnNumber: 7
        }, this);
    }
    const statusDisplay = getStatusDisplay(project.status);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "space-y-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/projects",
                            className: "inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                    className: "mr-2",
                                    size: 16
                                }, void 0, false, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 497,
                                    columnNumber: 13
                                }, this),
                                "返回项目列表"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 493,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold text-gray-900",
                                        children: project.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 503,
                                        columnNumber: 15
                                    }, this),
                                    project.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-gray-600",
                                        children: project.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 505,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 flex items-center space-x-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(statusDisplay.icon, {
                                                        className: "mr-1",
                                                        size: 12
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                        lineNumber: 509,
                                                        columnNumber: 19
                                                    }, this),
                                                    statusDisplay.text
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                lineNumber: 508,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center text-sm text-gray-500",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                        size: 14,
                                                        className: "mr-1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                        lineNumber: 513,
                                                        columnNumber: 19
                                                    }, this),
                                                    "创建于 ",
                                                    new Date(project.createdAt).toLocaleDateString('zh-CN')
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                lineNumber: 512,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 507,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                lineNumber: 502,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 501,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 492,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-b border-gray-200",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "-mb-px flex space-x-8",
                        children: tabs.map((tab)=>{
                            const isActive = activeTab === tab.id;
                            const isDisabled = tab.disabled;
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>!isDisabled && updateActiveTab(tab.id),
                                disabled: isDisabled,
                                className: `
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    ${isActive ? 'border-purple-500 text-purple-600' : isDisabled ? 'border-transparent text-gray-400 cursor-not-allowed' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
                  `,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(tab.icon, {
                                        className: `mr-2 ${isActive ? 'text-purple-500' : isDisabled ? 'text-gray-400' : 'text-gray-400 group-hover:text-gray-500'}`,
                                        size: 16
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 543,
                                        columnNumber: 19
                                    }, this),
                                    tab.name,
                                    tab.id === 'characters' && characters.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs",
                                        children: characters.length
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 549,
                                        columnNumber: 21
                                    }, this),
                                    tab.id === 'episodes' && episodes.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs",
                                        children: episodes.length
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                        lineNumber: 554,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, tab.id, true, {
                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                lineNumber: 529,
                                columnNumber: 17
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 523,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 522,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-6",
                    children: [
                        activeTab === 'upload' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProjectFileUpload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            projectId: projectId,
                            project: project,
                            onUploadSuccess: handleUploadSuccess,
                            onUploadError: handleUploadError
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 567,
                            columnNumber: 13
                        }, this),
                        activeTab === 'novel' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white shadow rounded-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "px-4 py-5 sm:p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between mb-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "text-lg font-medium text-gray-900",
                                                    children: "小说内容"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 580,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm text-gray-500",
                                                    children: [
                                                        "字数：",
                                                        project?.content?.length || 0,
                                                        " 字"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 581,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 579,
                                            columnNumber: 19
                                        }, this),
                                        project?.content ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "prose max-w-none",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                                            className: "whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed",
                                                            children: project.content
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 590,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                        lineNumber: 589,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 588,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-6 space-y-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-center space-x-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-sm font-medium text-gray-700",
                                                                    children: "分析模型:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                    lineNumber: 600,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ModelSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    selectedModel: selectedModel,
                                                                    onModelChange: setSelectedModel,
                                                                    className: "w-64"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                    lineNumber: 601,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 599,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-center space-x-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "relative flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: handleManualAnalyze,
                                                                            disabled: isAnalyzing || project.status === 'analyzing' || !selectedModel,
                                                                            className: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-l-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed",
                                                                            children: isAnalyzing || project.status === 'analyzing' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 618,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    "AI分析中..."
                                                                                ]
                                                                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                                                        className: "mr-2",
                                                                                        size: 20
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 623,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    project.status === 'completed' ? 'AI重新分析人物剧情' : '自动分析人物剧情'
                                                                                ]
                                                                            }, void 0, true)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                            lineNumber: 611,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: ()=>setShowPromptDropdown(!showPromptDropdown),
                                                                            disabled: isAnalyzing || project.status === 'analyzing' || !selectedModel,
                                                                            className: "px-3 py-3 border border-transparent rounded-r-md border-l border-purple-500 text-base font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                                                size: 16
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                lineNumber: 634,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                            lineNumber: 629,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        showPromptDropdown && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "absolute top-full left-1/2 transform -translate-x-1/2 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "p-4",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                                                                        children: "增强提示词设置"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 641,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                                                        value: enhancePrompt,
                                                                                        onChange: (e)=>setEnhancePrompt(e.target.value),
                                                                                        placeholder: "输入增强提示词，用于优化AI分析效果...",
                                                                                        className: "w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none",
                                                                                        rows: 4
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 644,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "mt-3",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                            onClick: handleSaveEnhancePrompt,
                                                                                            className: "w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
                                                                                            children: "保存增强提示词"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                            lineNumber: 654,
                                                                                            columnNumber: 37
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 653,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "flex justify-end mt-3",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                            onClick: ()=>setShowPromptDropdown(false),
                                                                                            className: "text-sm text-gray-600 hover:text-gray-800",
                                                                                            children: "关闭"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                            lineNumber: 664,
                                                                                            columnNumber: 37
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 663,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    savedEnhancePrompt && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600",
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                className: "font-medium mb-1",
                                                                                                children: "当前已保存的增强提示词："
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                                lineNumber: 675,
                                                                                                columnNumber: 39
                                                                                            }, this),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                className: "max-h-16 overflow-y-auto",
                                                                                                children: savedEnhancePrompt
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                                lineNumber: 676,
                                                                                                columnNumber: 39
                                                                                            }, this)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                        lineNumber: 674,
                                                                                        columnNumber: 37
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                                lineNumber: 640,
                                                                                columnNumber: 33
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                            lineNumber: 639,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                    lineNumber: 610,
                                                                    columnNumber: 27
                                                                }, this),
                                                                project.status === 'completed' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-sm text-green-600",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                                            className: "mr-1",
                                                                            size: 16
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                            lineNumber: 688,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        "已完成分析，可重新分析"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                                    lineNumber: 687,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 609,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 597,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center py-8",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$book$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BookOpen$3e$__["BookOpen"], {
                                                    className: "mx-auto h-12 w-12 text-gray-400"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 697,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "mt-2 text-sm font-medium text-gray-900",
                                                    children: "暂无小说内容"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 698,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "mt-1 text-sm text-gray-500",
                                                    children: "请先上传小说文件"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 699,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 696,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 578,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                lineNumber: 577,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 576,
                            columnNumber: 13
                        }, this),
                        activeTab === 'characters' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            projectId: params.id,
                            characters: project.characters || [],
                            onCharactersUpdate: loadProject
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 710,
                            columnNumber: 13
                        }, this),
                        activeTab === 'episodes' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            episodes: episodes,
                            onAnalyzePlot: handleAnalyzePlot,
                            onGenerateVideo: handleGenerateVideo,
                            onExtractDetailedPlot: handleExtractDetailedPlot
                        }, void 0, false, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 718,
                            columnNumber: 13
                        }, this),
                        activeTab === 'videos' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white border border-gray-200 rounded-lg p-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                            className: "text-purple-600 mr-2",
                                                            size: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 732,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg font-medium text-gray-900",
                                                            children: "剧情视频管理"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 733,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 731,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full",
                                                    children: [
                                                        "共 ",
                                                        episodes.length,
                                                        " 集"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 735,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 730,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mt-1 text-sm text-gray-600",
                                            children: "支持传统分段视频生成和新的一致性约束视频生成"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 739,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 729,
                                    columnNumber: 15
                                }, this),
                                episodes.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: episodes.map((episode, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeVideoCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            episode: episode,
                                            episodeIndex: index,
                                            projectId: projectId,
                                            onViewSegments: ()=>{
                                                setVideoEpisodeId(episode.id);
                                                setShowVideoSegmentViewer(true);
                                            },
                                            onGenerateConsistencyVideo: ()=>handleOpenConsistencyVideoGenerator(episode.id)
                                        }, episode.id, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 748,
                                            columnNumber: 21
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 746,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-12",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                            className: "mx-auto h-12 w-12 text-gray-400"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 763,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "mt-2 text-sm font-medium text-gray-900",
                                            children: "暂无剧集"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 764,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mt-1 text-sm text-gray-500",
                                            children: "请先上传小说并分析剧集"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 765,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 762,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-blue-50 border border-blue-200 rounded-lg p-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-sm font-medium text-blue-800 mb-2",
                                            children: "分段视频生成说明"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 773,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-blue-700 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "• ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "智能分段："
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 777,
                                                            columnNumber: 24
                                                        }, this),
                                                        "AI自动将剧情分解为3-8个短视频片段"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 777,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "• ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "并行生成："
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 778,
                                                            columnNumber: 24
                                                        }, this),
                                                        "多个片段同时生成，提高效率"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 778,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "• ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "实时进度："
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 779,
                                                            columnNumber: 24
                                                        }, this),
                                                        "可查看每个片段的生成状态和进度"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 779,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "• ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: "独立播放："
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                            lineNumber: 780,
                                                            columnNumber: 24
                                                        }, this),
                                                        "每个片段可单独播放和下载"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                    lineNumber: 780,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 776,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 772,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 727,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 565,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DetailedPlotExtraction$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    episodeId: currentEpisodeId,
                    episodeTitle: currentEpisodeTitle,
                    episodeContent: currentEpisodeContent,
                    isOpen: showDetailedPlotExtraction,
                    onClose: ()=>setShowDetailedPlotExtraction(false),
                    onGenerateStoryVideo: handleGenerateStoryVideo,
                    videoGenerationMode: videoGenerationMode,
                    onVideoGenerationModeChange: setVideoGenerationMode
                }, void 0, false, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 788,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoSegmentViewer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    episodeId: videoEpisodeId,
                    projectId: projectId,
                    isOpen: showVideoSegmentViewer,
                    onClose: ()=>setShowVideoSegmentViewer(false)
                }, void 0, false, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 800,
                    columnNumber: 9
                }, this),
                showConsistencyVideoGenerator && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-gray-900",
                                            children: "一致性视频生成"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 813,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setShowConsistencyVideoGenerator(false),
                                            className: "text-gray-400 hover:text-gray-600",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                size: 24
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/projects/[id]/page.tsx",
                                                lineNumber: 818,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                                            lineNumber: 814,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 812,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ConsistencyVideoGenerator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    episodeId: consistencyEpisodeId,
                                    episodeTitle: consistencyEpisodeTitle,
                                    onVideoGenerated: handleConsistencyVideoGenerated
                                }, void 0, false, {
                                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                                    lineNumber: 822,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/projects/[id]/page.tsx",
                            lineNumber: 811,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/projects/[id]/page.tsx",
                        lineNumber: 810,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/projects/[id]/page.tsx",
                    lineNumber: 809,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/projects/[id]/page.tsx",
            lineNumber: 490,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/projects/[id]/page.tsx",
        lineNumber: 489,
        columnNumber: 5
    }, this);
}
_s(ProjectDetailPage, "2QTt7MTbOH9DeSoGOwIKlipTMR8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = ProjectDetailPage;
var _c;
__turbopack_context__.k.register(_c, "ProjectDetailPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4bec29f9._.js.map