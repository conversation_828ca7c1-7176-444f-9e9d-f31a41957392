const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function comprehensiveCheck() {
  try {
    console.log('🔍 开始全面系统检查...\n');
    
    // 1. 检查AI配置
    console.log('1️⃣ 检查AI配置...');
    const aiConfigs = await prisma.aIConfig.findMany({
      orderBy: { createdAt: 'desc' }
    });
    
    console.log(`找到 ${aiConfigs.length} 个AI配置:`);
    aiConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.provider} - ${config.model}`);
      console.log(`     启用: ${config.enabled ? '✅' : '❌'}`);
      console.log(`     支持视频: ${config.supportsVideo ? '✅' : '❌'}`);
      console.log(`     API Key: ${config.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
    });
    
    // 2. 检查视频生成模型
    console.log('\n2️⃣ 检查视频生成模型...');
    const videoModels = await prisma.aIConfig.findMany({
      where: {
        enabled: true,
        supportsVideo: true
      }
    });
    
    console.log(`找到 ${videoModels.length} 个可用的视频生成模型:`);
    videoModels.forEach((model, index) => {
      console.log(`  ${index + 1}. ${model.provider} - ${model.model}`);
    });
    
    // 3. 检查豆包配置详情
    console.log('\n3️⃣ 检查豆包配置详情...');
    const doubaoConfig = videoModels.find(m => m.provider === 'doubao');
    if (doubaoConfig) {
      console.log('✅ 找到豆包配置:');
      console.log(`  模型: ${doubaoConfig.model}`);
      console.log(`  API Key长度: ${doubaoConfig.apiKey?.length || 0}`);
      console.log(`  创建时间: ${doubaoConfig.createdAt}`);
      console.log(`  更新时间: ${doubaoConfig.updatedAt}`);
    } else {
      console.log('❌ 未找到豆包配置');
    }
    
    // 4. 检查项目和剧集
    console.log('\n4️⃣ 检查项目和剧集...');
    const projects = await prisma.project.findMany({
      include: {
        episodes: {
          orderBy: { createdAt: 'desc' },
          take: 3
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 2
    });
    
    console.log(`找到 ${projects.length} 个项目:`);
    projects.forEach((project, index) => {
      console.log(`  ${index + 1}. 项目: ${project.title || project.id}`);
      console.log(`     剧集数量: ${project.episodes.length}`);
      project.episodes.forEach((episode, eIndex) => {
        console.log(`     ${eIndex + 1}. ${episode.title}`);
        console.log(`        剧情信息: ${episode.plotInfo ? '✅ 有' : '❌ 无'}`);
      });
    });
    
    // 5. 检查角色信息
    console.log('\n5️⃣ 检查角色信息...');
    const characters = await prisma.character.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    console.log(`找到 ${characters.length} 个角色:`);
    characters.forEach((char, index) => {
      console.log(`  ${index + 1}. ${char.name}`);
      console.log(`     外貌描述: ${char.facial ? '✅ 有' : '❌ 无'} (${char.facial?.length || 0}字)`);
      console.log(`     生成图像: ${char.generatedImages ? '✅ 有' : '❌ 无'}`);
      if (char.generatedImages) {
        try {
          const images = JSON.parse(char.generatedImages);
          console.log(`     图像详情: 正面=${!!images.front}, 侧面=${!!images.side}, 背面=${!!images.back}`);
        } catch (e) {
          console.log(`     图像解析失败`);
        }
      }
    });
    
    // 6. 检查最近的视频生成记录
    console.log('\n6️⃣ 检查最近的视频生成记录...');
    const storyVideos = await prisma.storyVideo.findMany({
      include: {
        videoSegments: true
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    });
    
    console.log(`找到 ${storyVideos.length} 个故事视频记录:`);
    storyVideos.forEach((video, index) => {
      console.log(`  ${index + 1}. 故事视频: ${video.id}`);
      console.log(`     状态: ${video.status}`);
      console.log(`     片段数量: ${video.videoSegments.length}`);
      console.log(`     创建时间: ${video.createdAt}`);
      
      video.videoSegments.forEach((segment, sIndex) => {
        console.log(`     片段 ${sIndex + 1}: ${segment.status} (${segment.duration}秒)`);
        if (segment.status === 'failed' && segment.metadata) {
          try {
            const metadata = JSON.parse(segment.metadata);
            console.log(`       错误: ${metadata.error?.substring(0, 100) || '未知'}...`);
          } catch (e) {
            console.log(`       元数据解析失败`);
          }
        }
      });
    });
    
    // 7. 测试豆包API连接
    console.log('\n7️⃣ 测试豆包API连接...');
    if (doubaoConfig) {
      try {
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${doubaoConfig.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: doubaoConfig.model,
            content: [
              {
                type: "text",
                text: "测试连接"
              }
            ]
          })
        });
        
        console.log(`API响应状态: ${response.status}`);
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ 豆包API连接正常');
          console.log(`任务ID: ${result.id || result.task_id || '未找到'}`);
        } else {
          const errorText = await response.text();
          console.log('❌ 豆包API连接失败:');
          console.log(errorText.substring(0, 200) + '...');
        }
      } catch (error) {
        console.log('❌ 豆包API测试异常:', error.message);
      }
    }
    
    console.log('\n✅ 全面检查完成');
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

comprehensiveCheck();
