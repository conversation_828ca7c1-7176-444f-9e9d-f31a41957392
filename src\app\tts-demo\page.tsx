'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { TTSPanel } from '@/components/tts/TTSPanel'
import { CharacterVoiceConfig } from '@/components/tts/CharacterVoiceConfig'
import { Button } from '@/components/ui/button'
import { Mic, User, Settings, Play, Volume2 } from 'lucide-react'

interface Character {
  id: string
  name: string
  description?: string
}

interface TTSConfig {
  id: string
  name: string
  provider: string
  enabled: boolean
  supportsTTS: boolean
}

export default function TTSDemoPage() {
  const [characters, setCharacters] = useState<Character[]>([])
  const [ttsConfigs, setTTSConfigs] = useState<TTSConfig[]>([])
  const [selected<PERSON><PERSON><PERSON>, setSelectedCharacter] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // 加载角色列表
      const charactersResponse = await fetch('/api/characters')
      if (charactersResponse.ok) {
        const charactersResult = await charactersResponse.json()
        if (charactersResult.success) {
          setCharacters(charactersResult.data)
          if (charactersResult.data.length > 0) {
            setSelectedCharacter(charactersResult.data[0].id)
          }
        }
      }

      // 加载TTS配置
      const configsResponse = await fetch('/api/ai-configs?supportsTTS=true')
      if (configsResponse.ok) {
        const configsResult = await configsResponse.json()
        if (configsResult.success) {
          setTTSConfigs(configsResult.data)
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const testSystemAudio = async () => {
    const testTexts = [
      '欢迎使用语音合成系统！',
      '这是一个完整的TTS解决方案。',
      '支持多种声音和情感表达。'
    ]

    for (const text of testTexts) {
      try {
        const response = await fetch('/api/ai/generate-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text,
            emotion: 'happy',
            speed: 1.0
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            const audio = new Audio(result.data.audioUrl)
            await audio.play()
            // 等待音频播放完成
            await new Promise(resolve => {
              audio.onended = resolve
            })
          }
        }
      } catch (error) {
        console.error('音频测试失败:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <div className="text-lg">加载中...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Volume2 className="h-8 w-8" />
          TTS语音合成系统演示
        </h1>
        <p className="text-muted-foreground">
          豆包语音合成大模型集成演示 - 支持多角色声音配置和情感表达
        </p>
      </div>

      {/* 系统状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            系统状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{ttsConfigs.length}</div>
              <div className="text-sm text-muted-foreground">TTS服务</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{characters.length}</div>
              <div className="text-sm text-muted-foreground">角色数量</div>
            </div>
            <div className="text-center">
              <Button onClick={testSystemAudio} variant="outline">
                <Play className="h-4 w-4 mr-2" />
                系统音频测试
              </Button>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="text-sm font-medium mb-2">可用TTS服务:</div>
            <div className="flex gap-2 flex-wrap">
              {ttsConfigs.map((config) => (
                <Badge 
                  key={config.id} 
                  variant={config.enabled ? "default" : "secondary"}
                >
                  {config.name} ({config.provider})
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要功能 */}
      <Tabs defaultValue="tts-test" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="tts-test" className="flex items-center gap-2">
            <Mic className="h-4 w-4" />
            TTS测试
          </TabsTrigger>
          <TabsTrigger value="character-config" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            角色配置
          </TabsTrigger>
          <TabsTrigger value="system-info" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            系统信息
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tts-test" className="space-y-4">
          <TTSPanel />
        </TabsContent>

        <TabsContent value="character-config" className="space-y-4">
          {characters.length > 0 ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>选择角色</CardTitle>
                  <CardDescription>
                    为角色配置专属的声音特征
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {characters.map((character) => (
                      <Button
                        key={character.id}
                        variant={selectedCharacter === character.id ? "default" : "outline"}
                        onClick={() => setSelectedCharacter(character.id)}
                        className="justify-start"
                      >
                        {character.name}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {selectedCharacter && (
                <CharacterVoiceConfig characterId={selectedCharacter} />
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-muted-foreground">
                  暂无角色数据，请先创建角色
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="system-info" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>TTS服务配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {ttsConfigs.map((config) => (
                    <div key={config.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <div className="font-medium">{config.name}</div>
                        <div className="text-sm text-muted-foreground">{config.provider}</div>
                      </div>
                      <Badge variant={config.enabled ? "default" : "secondary"}>
                        {config.enabled ? "已启用" : "已禁用"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>角色列表</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {characters.map((character) => (
                    <div key={character.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <div className="font-medium">{character.name}</div>
                        {character.description && (
                          <div className="text-sm text-muted-foreground">
                            {character.description.substring(0, 50)}...
                          </div>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCharacter(character.id)}
                      >
                        配置
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>功能说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">✅ 已实现功能</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                    <li>多TTS服务支持（豆包TTS、Edge TTS）</li>
                    <li>角色声音配置和管理</li>
                    <li>情感表达和参数调节</li>
                    <li>音频生成和预览</li>
                    <li>剧集音频批量生成</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium">🔄 待完善功能</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground mt-2 space-y-1">
                    <li>豆包TTS API端点确认和集成</li>
                    <li>音频文件存储和管理</li>
                    <li>音视频同步和合并</li>
                    <li>批量音频处理优化</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
