import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import ffmpeg from 'fluent-ffmpeg'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'
import fs from 'fs'
import path from 'path'
import { promisify } from 'util'

const prisma = new PrismaClient()

// 设置FFmpeg路径
ffmpeg.setFfmpegPath(ffmpegInstaller.path)

// 转场效果类型
enum TransitionType {
  CUT = 'cut',           // 直切
  FADE = 'fade',         // 淡入淡出
  DISSOLVE = 'dissolve', // 溶解
  WIPE = 'wipe',         // 擦除
  ZOOM = 'zoom'          // 缩放
}

// 片段类型
enum SegmentType {
  ENVIRONMENT = 'environment',    // 环境建立
  CHARACTER = 'character',        // 角色引入
  ACTION = 'action',             // 动作核心
  EMOTION = 'emotion',           // 情感转折
  DIALOGUE = 'dialogue',         // 对话互动
  SUSPENSE = 'suspense'          // 悬念结尾
}

// 视频合并配置
interface VideoMergeConfig {
  storyVideoId: string
  segments: {
    id: string
    videoUrl: string
    duration: number
    type: SegmentType
    order: number
  }[]
  transitions: TransitionType[]
  outputFormat: 'mp4' | 'webm'
  quality: 'high' | 'medium' | 'low'
}

// 智能转场规则
const TRANSITION_RULES: Record<string, TransitionType> = {
  'environment_character': TransitionType.FADE,
  'character_action': TransitionType.CUT,
  'action_emotion': TransitionType.DISSOLVE,
  'emotion_dialogue': TransitionType.CUT,
  'dialogue_suspense': TransitionType.FADE,
  'default': TransitionType.CUT
}

export async function POST(request: NextRequest) {
  try {
    const { storyVideoId, customTransitions, outputFormat = 'mp4', quality = 'high' } = await request.json()

    console.log(`🎬 开始合并故事视频片段: ${storyVideoId}`)

    // 1. 获取所有已完成的视频片段
    const segments = await prisma.videoSegment.findMany({
      where: {
        storyVideoId,
        status: 'completed',
        videoUrl: { not: null }
      },
      orderBy: { createdAt: 'asc' }
    })

    if (segments.length === 0) {
      return NextResponse.json({
        success: false,
        error: '没有找到已完成的视频片段'
      }, { status: 400 })
    }

    console.log(`📊 找到 ${segments.length} 个已完成片段`)

    // 2. 分析片段类型并生成转场建议
    const segmentData = segments.map((segment, index) => {
      const type = analyzeSegmentType(segment)
      return {
        id: segment.id,
        videoUrl: segment.videoUrl!,
        duration: segment.duration || 6,
        type,
        order: index,
        title: segment.title
      }
    })

    // 3. 生成智能转场序列
    const transitions = customTransitions || generateSmartTransitions(segmentData)

    // 4. 创建合并配置
    const mergeConfig: VideoMergeConfig = {
      storyVideoId,
      segments: segmentData,
      transitions,
      outputFormat,
      quality
    }

    console.log(`🔧 合并配置:`, JSON.stringify(mergeConfig, null, 2))

    // 5. 执行视频合并（这里先返回配置，实际合并需要视频处理库）
    const mergeResult = await performVideoMerge(mergeConfig)

    // 6. 更新数据库记录
    await prisma.storyVideo.update({
      where: { id: storyVideoId },
      data: {
        mergedVideoUrl: mergeResult.outputUrl,
        totalDuration: mergeResult.totalDuration,
        metadata: JSON.stringify({
          ...mergeResult.metadata,
          mergeConfig,
          mergedAt: new Date().toISOString()
        })
      }
    })

    console.log(`✅ 视频合并完成: ${mergeResult.outputUrl}`)

    return NextResponse.json({
      success: true,
      data: {
        mergedVideoUrl: mergeResult.outputUrl,
        totalDuration: mergeResult.totalDuration,
        segmentCount: segments.length,
        transitions: transitions,
        metadata: mergeResult.metadata
      }
    })

  } catch (error) {
    console.error('视频合并失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '视频合并失败'
    }, { status: 500 })
  }
}

// 分析片段类型
function analyzeSegmentType(segment: any): SegmentType {
  const title = segment.title.toLowerCase()
  const description = (segment.description || '').toLowerCase()
  const text = `${title} ${description}`

  // 基于关键词分析片段类型
  if (text.includes('环境') || text.includes('场景') || text.includes('建立') || text.includes('雪夜') || text.includes('城楼')) {
    return SegmentType.ENVIRONMENT
  }
  
  if (text.includes('登场') || text.includes('出现') || text.includes('角色') || text.includes('李四') || text.includes('张三')) {
    return SegmentType.CHARACTER
  }
  
  if (text.includes('动作') || text.includes('跑') || text.includes('扛') || text.includes('冲') || text.includes('抽刀')) {
    return SegmentType.ACTION
  }
  
  if (text.includes('表情') || text.includes('情感') || text.includes('内心') || text.includes('思考') || text.includes('回忆')) {
    return SegmentType.EMOTION
  }
  
  if (text.includes('对话') || text.includes('说') || text.includes('讲述') || text.includes('报告') || text.includes('讨论')) {
    return SegmentType.DIALOGUE
  }
  
  if (text.includes('悬念') || text.includes('远方') || text.includes('狼烟') || text.includes('威胁') || text.includes('危机')) {
    return SegmentType.SUSPENSE
  }

  // 默认类型
  return SegmentType.ACTION
}

// 生成智能转场序列
function generateSmartTransitions(segments: any[]): TransitionType[] {
  const transitions: TransitionType[] = []

  for (let i = 0; i < segments.length - 1; i++) {
    const currentType = segments[i].type
    const nextType = segments[i + 1].type
    const transitionKey = `${currentType}_${nextType}`
    
    const transition = TRANSITION_RULES[transitionKey] || TRANSITION_RULES.default
    transitions.push(transition)
  }

  return transitions
}

// 执行真实的FFmpeg视频合并
async function performVideoMerge(config: VideoMergeConfig): Promise<{
  outputUrl: string
  totalDuration: number
  metadata: any
}> {
  try {
    console.log(`🎬 开始执行FFmpeg视频合并...`)

    // 计算总时长
    const totalDuration = config.segments.reduce((sum, segment) => sum + segment.duration, 0)

    // 创建临时目录
    const tempDir = path.join(process.cwd(), 'temp', 'video-merge')
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }

    // 输出文件路径
    const outputFileName = `${config.storyVideoId}-merged-${Date.now()}.${config.outputFormat}`
    const outputPath = path.join(tempDir, outputFileName)

    console.log(`📊 合并统计:`)
    console.log(`   - 片段数量: ${config.segments.length}`)
    console.log(`   - 总时长: ${totalDuration}秒`)
    console.log(`   - 转场效果: ${config.transitions.length}个`)
    console.log(`   - 输出格式: ${config.outputFormat}`)
    console.log(`   - 质量设置: ${config.quality}`)
    console.log(`   - 输出路径: ${outputPath}`)

    // 下载所有视频片段到本地
    const localSegments = await downloadSegments(config.segments, tempDir)

    // 使用FFmpeg合并视频
    await mergeWithFFmpeg(localSegments, config.transitions, outputPath, config.quality)

    // 生成公共访问URL（这里需要根据实际部署环境调整）
    const publicUrl = `/api/video/download/${outputFileName}`

    const metadata = {
      segmentCount: config.segments.length,
      totalDuration,
      transitions: config.transitions,
      outputFormat: config.outputFormat,
      quality: config.quality,
      outputPath,
      localSegments: localSegments.map(s => s.localPath),
      processedAt: new Date().toISOString()
    }

    // 清理临时文件
    setTimeout(() => {
      cleanupTempFiles([...localSegments.map(s => s.localPath)])
    }, 60000) // 1分钟后清理

    return {
      outputUrl: publicUrl,
      totalDuration,
      metadata
    }

  } catch (error) {
    console.error('视频合并执行失败:', error)
    throw new Error(`视频合并失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 下载视频片段到本地
async function downloadSegments(segments: any[], tempDir: string): Promise<Array<{url: string, localPath: string, duration: number}>> {
  const localSegments = []

  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i]
    const fileName = `segment-${i}-${Date.now()}.mp4`
    const localPath = path.join(tempDir, fileName)

    try {
      console.log(`📥 下载片段 ${i + 1}/${segments.length}: ${segment.videoUrl}`)

      // 使用fetch下载视频文件
      const response = await fetch(segment.videoUrl)
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`)
      }

      const buffer = await response.arrayBuffer()
      fs.writeFileSync(localPath, Buffer.from(buffer))

      localSegments.push({
        url: segment.videoUrl,
        localPath,
        duration: segment.duration
      })

      console.log(`✅ 片段 ${i + 1} 下载完成: ${localPath}`)

    } catch (error) {
      console.error(`❌ 下载片段 ${i + 1} 失败:`, error)
      throw new Error(`下载片段失败: ${error}`)
    }
  }

  return localSegments
}

// 使用FFmpeg合并视频
async function mergeWithFFmpeg(
  segments: Array<{localPath: string, duration: number}>,
  transitions: TransitionType[],
  outputPath: string,
  quality: string
): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(`🎬 开始FFmpeg合并，输出: ${outputPath}`)

    const command = ffmpeg()

    // 添加所有输入文件
    segments.forEach(segment => {
      command.input(segment.localPath)
    })

    // 设置输出选项
    command
      .outputOptions([
        '-c:v libx264',
        `-preset ${quality === 'high' ? 'slow' : 'fast'}`,
        `-crf ${quality === 'high' ? '18' : '23'}`,
        '-c:a aac',
        '-b:a 128k',
        '-movflags +faststart' // 优化网络播放
      ])
      .output(outputPath)
      .on('start', (commandLine) => {
        console.log('🔧 FFmpeg命令:', commandLine)
      })
      .on('progress', (progress) => {
        console.log(`📊 合并进度: ${Math.round(progress.percent || 0)}%`)
      })
      .on('end', () => {
        console.log('✅ FFmpeg合并完成')
        resolve()
      })
      .on('error', (err) => {
        console.error('❌ FFmpeg合并失败:', err)
        reject(err)
      })
      .run()
  })
}

// 清理临时文件
function cleanupTempFiles(filePaths: string[]): void {
  filePaths.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
        console.log(`🗑️ 清理临时文件: ${filePath}`)
      }
    } catch (error) {
      console.warn(`⚠️ 清理文件失败: ${filePath}`, error)
    }
  })
}

// 生成FFmpeg合并脚本（保留用于调试）
function generateFFmpegScript(config: VideoMergeConfig): string {
  const inputs = config.segments.map((segment, index) => 
    `-i "${segment.videoUrl}"`
  ).join(' ')

  const filterComplex = config.segments.map((segment, index) => {
    if (index === 0) {
      return `[0:v]scale=1920:1080,setpts=PTS-STARTPTS[v0]`
    }
    
    const transition = config.transitions[index - 1] || TransitionType.CUT
    const transitionFilter = getTransitionFilter(transition, index)
    
    return `[${index}:v]scale=1920:1080,setpts=PTS-STARTPTS[v${index}]; ${transitionFilter}`
  }).join('; ')

  const script = `ffmpeg ${inputs} \\
  -filter_complex "${filterComplex}" \\
  -map "[vout]" -map 0:a \\
  -c:v libx264 -preset ${config.quality === 'high' ? 'slow' : 'fast'} \\
  -crf ${config.quality === 'high' ? '18' : '23'} \\
  -c:a aac -b:a 128k \\
  output.${config.outputFormat}`

  return script
}

// 获取转场滤镜
function getTransitionFilter(transition: TransitionType, index: number): string {
  switch (transition) {
    case TransitionType.FADE:
      return `[v${index-1}][v${index}]xfade=transition=fade:duration=0.5:offset=0[v${index}_out]`
    case TransitionType.DISSOLVE:
      return `[v${index-1}][v${index}]xfade=transition=dissolve:duration=0.3:offset=0[v${index}_out]`
    case TransitionType.WIPE:
      return `[v${index-1}][v${index}]xfade=transition=wiperight:duration=0.2:offset=0[v${index}_out]`
    case TransitionType.ZOOM:
      return `[v${index-1}][v${index}]xfade=transition=zoomin:duration=0.4:offset=0[v${index}_out]`
    default: // CUT
      return `[v${index-1}][v${index}]concat=n=2:v=1:a=0[v${index}_out]`
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const storyVideoId = searchParams.get('storyVideoId')

    if (!storyVideoId) {
      return NextResponse.json({
        success: false,
        error: '缺少storyVideoId参数'
      }, { status: 400 })
    }

    // 获取合并状态
    const storyVideo = await prisma.storyVideo.findUnique({
      where: { id: storyVideoId },
      include: {
        segments: {
          where: { status: 'completed' },
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!storyVideo) {
      return NextResponse.json({
        success: false,
        error: '故事视频不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: {
        storyVideoId,
        mergedVideoUrl: storyVideo.mergedVideoUrl,
        totalDuration: storyVideo.totalDuration,
        segmentCount: storyVideo.segments.length,
        segments: storyVideo.segments.map(segment => ({
          id: segment.id,
          title: segment.title,
          duration: segment.duration,
          videoUrl: segment.videoUrl,
          status: segment.status
        }))
      }
    })

  } catch (error) {
    console.error('获取合并状态失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取合并状态失败'
    }, { status: 500 })
  }
}
