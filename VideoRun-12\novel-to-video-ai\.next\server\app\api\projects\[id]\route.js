/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/[id]/route";
exports.ids = ["app/api/projects/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/[id]/route.ts */ \"(rsc)/./src/app/api/projects/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/[id]/route\",\n        pathname: \"/api/projects/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\projects\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/projects/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/projects/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// 获取单个项目详情\nasync function GET(request, { params }) {\n    try {\n        const { id } = await params;\n        const project = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findUnique({\n            where: {\n                id\n            },\n            include: {\n                characters: {\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                },\n                episodes: {\n                    orderBy: {\n                        createdAt: 'asc'\n                    },\n                    include: {\n                        plotInfo: true\n                    }\n                }\n            }\n        });\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 解析角色的生成图像数据\n        const processedProject = {\n            ...project,\n            characters: project.characters.map((character)=>({\n                    ...character,\n                    generatedImages: character.generatedImages ? JSON.parse(character.generatedImages) : null\n                }))\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: processedProject\n        });\n    } catch (error) {\n        console.error('获取项目详情失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '获取项目详情失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// 更新项目\nasync function PUT(request, { params }) {\n    try {\n        const { id } = await params;\n        const body = await request.json();\n        const { name, description, status } = body;\n        // 检查项目是否存在\n        const existingProject = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!existingProject) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 如果更新名称，检查是否与其他项目重名\n        if (name && name.trim() !== existingProject.name) {\n            const duplicateProject = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findFirst({\n                where: {\n                    name: name.trim(),\n                    id: {\n                        not: id\n                    }\n                }\n            });\n            if (duplicateProject) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '项目名称已存在，请使用其他名称'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // 更新项目\n        const updatedProject = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.update({\n            where: {\n                id\n            },\n            data: {\n                ...name && {\n                    name: name.trim()\n                },\n                ...description !== undefined && {\n                    description: description?.trim() || null\n                },\n                ...status && {\n                    status\n                },\n                updatedAt: new Date()\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedProject,\n            message: '项目更新成功'\n        });\n    } catch (error) {\n        console.error('更新项目失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '更新项目失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// 删除项目\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = await params;\n        // 检查项目是否存在\n        const existingProject = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findUnique({\n            where: {\n                id\n            }\n        });\n        if (!existingProject) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 删除项目（级联删除相关数据）\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.delete({\n            where: {\n                id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: '项目删除成功'\n        });\n    } catch (error) {\n        console.error('删除项目失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '删除项目失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();