'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Home,
  FolderOpen,
  Settings,
  User,
  Menu,
  X,
  Sparkles
} from 'lucide-react'

interface LayoutProps {
  children: React.ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  const navigation = [
    {
      name: '首页',
      href: '/',
      icon: Home,
      current: pathname === '/'
    },
    {
      name: '项目',
      href: '/projects',
      icon: FolderOpen,
      current: pathname.startsWith('/projects')
    },
    {
      name: '模型配置',
      href: '/models',
      icon: Settings,
      current: pathname === '/models'
    },
    {
      name: '账户',
      href: '/account',
      icon: User,
      current: pathname === '/account',
      disabled: true
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* 左侧 Logo */}
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="flex items-center">
                <Sparkles className="text-purple-600 mr-2" size={24} />
                <span className="text-lg font-semibold text-gray-900 whitespace-nowrap">
                  灵犀Ai——小说转视频神器
                </span>
              </Link>
            </div>

            {/* 右侧导航菜单 */}
            <div className="hidden md:flex md:items-center md:space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    inline-flex items-center text-lg font-semibold
                    ${item.current
                      ? 'text-purple-600'
                      : 'text-gray-900 hover:text-purple-600'
                    }
                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}
                  `}
                >
                  <item.icon className="mr-2" size={20} />
                  {item.name}
                </Link>
              ))}
            </div>

            {/* 移动端菜单按钮 */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                <Menu size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* 移动端菜单 */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    flex items-center px-3 py-2 text-sm font-medium border-l-4
                    ${item.current
                      ? 'bg-purple-50 border-purple-500 text-purple-700'
                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'
                    }
                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}
                  `}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <item.icon className="mr-2" size={16} />
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* 主内容区域 */}
      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
    </div>
  )
}
