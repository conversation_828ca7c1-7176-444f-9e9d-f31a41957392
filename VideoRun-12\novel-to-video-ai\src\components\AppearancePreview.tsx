'use client'

import { useState } from 'react'
import { X, Save, Refresh<PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'

interface AppearanceData {
  front: {
    url: string
    format: string
    error?: string
  }
  side: {
    url: string
    format: string
    error?: string
  }
  back: {
    url: string
    format: string
    error?: string
  }
  character: {
    name: string
    description: string
  }
}

interface AppearancePreviewProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: AppearanceData) => void
  onRegenerate: (customPrompt?: string) => void
  data: AppearanceData | null
  isGenerating: boolean
}

export default function AppearancePreview({
  isOpen,
  onClose,
  onSave,
  onRegenerate,
  data,
  isGenerating
}: AppearancePreviewProps) {
  const [showPromptInput, setShowPromptInput] = useState(false)
  const [customPrompt, setCustomPrompt] = useState('')

  if (!isOpen) return null

  const handleSave = () => {
    if (data) {
      onSave(data)
      onClose()
    }
  }

  const handleRegenerateWithPrompt = () => {
    onRegenerate(customPrompt)
    setShowPromptInput(false)
    setCustomPrompt('')
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <Sparkles className="text-purple-600 mr-3" size={24} />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">AI生成的角色三视图</h3>
                <p className="text-sm text-gray-500">正面、侧面、背面三视图，确认后保存到形象设置</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {isGenerating ? (
              /* 生成中状态 */
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">AI正在生成角色三视图...</h4>
                <p className="text-sm text-gray-500">
                  正在基于角色信息生成正面、侧面、背面三视图，请稍候
                </p>
              </div>
            ) : data ? (
              /* 生成结果展示 */
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">
                    ✨ AI生成完成
                  </h4>
                  <p className="text-sm text-blue-700">
                    以下是基于角色信息生成的正、侧、背三视图，您可以预览后选择保存到形象设置中。
                  </p>
                </div>

                {/* 角色信息 */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">角色：{data.character.name}</h5>
                  <p className="text-xs text-gray-600">{data.character.description}</p>
                </div>

                {/* 三视图展示 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* 正面视图 */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                      <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                      正面视图
                    </h5>
                    <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                      {data.front.error ? (
                        <div className="h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center">
                          <div>
                            <div className="text-red-500 mb-2">⚠️</div>
                            <div>{data.front.error}</div>
                          </div>
                        </div>
                      ) : (
                        <img
                          src={data.front.url}
                          alt="正面视图"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = '/placeholder-character.svg'
                          }}
                        />
                      )}
                    </div>
                  </div>

                  {/* 侧面视图 */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      侧面视图
                    </h5>
                    <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                      {data.side.error ? (
                        <div className="h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center">
                          <div>
                            <div className="text-red-500 mb-2">⚠️</div>
                            <div>{data.side.error}</div>
                          </div>
                        </div>
                      ) : (
                        <img
                          src={data.side.url}
                          alt="侧面视图"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = '/placeholder-character.svg'
                          }}
                        />
                      )}
                    </div>
                  </div>

                  {/* 背面视图 */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      背面视图
                    </h5>
                    <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                      {data.back.error ? (
                        <div className="h-full flex items-center justify-center text-gray-500 text-sm p-4 text-center">
                          <div>
                            <div className="text-red-500 mb-2">⚠️</div>
                            <div>{data.back.error}</div>
                          </div>
                        </div>
                      ) : (
                        <img
                          src={data.back.url}
                          alt="背面视图"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = '/placeholder-character.svg'
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* 错误状态 */
              <div className="text-center py-12">
                <div className="text-red-500 mb-4">
                  <X size={48} className="mx-auto" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">生成失败</h4>
                <p className="text-sm text-gray-500 mb-4">
                  AI三视图生成过程中出现错误，请重试
                </p>
                <button
                  onClick={() => onRegenerate()}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                >
                  <RefreshCw className="mr-2" size={16} />
                  重新生成
                </button>
              </div>
            )}
          </div>

          {/* 底部操作按钮 */}
          {!isGenerating && data && (
            <div className="p-6 border-t border-gray-200 bg-gray-50 space-y-4">
              {/* 修改提示词区域 */}
              {showPromptInput ? (
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">
                    修改提示词
                  </label>
                  <textarea
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    placeholder="输入自定义提示词来调整角色形象生成..."
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none"
                    rows={3}
                  />
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowPromptInput(false)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      取消
                    </button>
                    <button
                      onClick={handleRegenerateWithPrompt}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                    >
                      <RefreshCw className="mr-2" size={16} />
                      重新生成
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowPromptInput(true)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <RefreshCw className="mr-2" size={16} />
                      修改提示词
                    </button>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={onClose}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      取消
                    </button>
                    <button
                      onClick={handleSave}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                    >
                      <Save className="mr-2" size={16} />
                      保存三视图
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
