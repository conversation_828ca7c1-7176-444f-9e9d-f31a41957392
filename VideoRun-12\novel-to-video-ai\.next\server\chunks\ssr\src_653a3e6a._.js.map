{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/fileParser.ts"], "sourcesContent": ["import mammoth from 'mammoth'\n\nexport class FileParseError extends Error {\n  constructor(message: string, public code: string) {\n    super(message)\n    this.name = 'FileParseError'\n  }\n}\n\nexport class FileParser {\n  // 支持的文件类型\n  static readonly SUPPORTED_TYPES = {\n    'text/plain': ['.txt'],\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\n    'application/msword': ['.doc'] // 虽然不完全支持，但可以尝试\n  }\n\n  // 获取支持的文件扩展名\n  static getSupportedExtensions(): string[] {\n    return Object.values(this.SUPPORTED_TYPES).flat()\n  }\n\n  // 验证文件类型\n  static validateFile(file: File): void {\n    const extension = this.getFileExtension(file.name)\n    const supportedExtensions = this.getSupportedExtensions()\n    \n    if (!supportedExtensions.includes(extension)) {\n      throw new FileParseError(\n        `不支持的文件格式: ${extension}。支持的格式: ${supportedExtensions.join(', ')}`,\n        'UNSUPPORTED_FORMAT'\n      )\n    }\n\n    // 验证文件大小 (最大 50MB)\n    const maxSize = 50 * 1024 * 1024\n    if (file.size > maxSize) {\n      throw new FileParseError(\n        `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,\n        'FILE_TOO_LARGE'\n      )\n    }\n\n    // 验证文件不为空\n    if (file.size === 0) {\n      throw new FileParseError(\n        '文件不能为空',\n        'EMPTY_FILE'\n      )\n    }\n  }\n\n  // 解析文件内容\n  static async parseFile(file: File): Promise<string> {\n    this.validateFile(file)\n    \n    const extension = this.getFileExtension(file.name)\n    \n    try {\n      switch (extension) {\n        case '.txt':\n          return await this.parseTxtFile(file)\n        case '.docx':\n          return await this.parseDocxFile(file)\n        case '.doc':\n          // .doc 文件尝试用 docx 解析器处理\n          return await this.parseDocxFile(file)\n        default:\n          throw new FileParseError(\n            `不支持的文件格式: ${extension}`,\n            'UNSUPPORTED_FORMAT'\n          )\n      }\n    } catch (error) {\n      if (error instanceof FileParseError) {\n        throw error\n      }\n      \n      throw new FileParseError(\n        `文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        'PARSE_ERROR'\n      )\n    }\n  }\n\n  // 解析 TXT 文件\n  private static async parseTxtFile(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      \n      reader.onload = (e) => {\n        const content = e.target?.result as string\n        if (!content || content.trim().length === 0) {\n          reject(new FileParseError('文件内容为空', 'EMPTY_CONTENT'))\n          return\n        }\n        resolve(content)\n      }\n      \n      reader.onerror = () => {\n        reject(new FileParseError('文件读取失败', 'READ_ERROR'))\n      }\n      \n      // 尝试不同的编码\n      reader.readAsText(file, 'utf-8')\n    })\n  }\n\n  // 解析 DOCX 文件\n  private static async parseDocxFile(file: File): Promise<string> {\n    try {\n      const arrayBuffer = await this.fileToArrayBuffer(file)\n      const result = await mammoth.extractRawText({ arrayBuffer })\n      \n      if (!result.value || result.value.trim().length === 0) {\n        throw new FileParseError('Word文档内容为空', 'EMPTY_CONTENT')\n      }\n\n      // 如果有警告，记录但不阻止处理\n      if (result.messages && result.messages.length > 0) {\n        console.warn('Word文档解析警告:', result.messages)\n      }\n\n      // 清理文本内容\n      return this.cleanText(result.value)\n    } catch (error) {\n      if (error instanceof FileParseError) {\n        throw error\n      }\n      \n      throw new FileParseError(\n        `Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        'DOCX_PARSE_ERROR'\n      )\n    }\n  }\n\n  // 将文件转换为 ArrayBuffer\n  private static async fileToArrayBuffer(file: File): Promise<ArrayBuffer> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      \n      reader.onload = (e) => {\n        const result = e.target?.result\n        if (result instanceof ArrayBuffer) {\n          resolve(result)\n        } else {\n          reject(new Error('文件读取结果不是 ArrayBuffer'))\n        }\n      }\n      \n      reader.onerror = () => {\n        reject(new Error('文件读取失败'))\n      }\n      \n      reader.readAsArrayBuffer(file)\n    })\n  }\n\n  // 清理文本内容\n  private static cleanText(text: string): string {\n    return text\n      .replace(/\\r\\n/g, '\\n')  // 统一换行符\n      .replace(/\\r/g, '\\n')    // 处理单独的 \\r\n      .replace(/\\n{3,}/g, '\\n\\n')  // 合并多个连续换行\n      .trim()\n  }\n\n  // 获取文件扩展名\n  private static getFileExtension(fileName: string): string {\n    const lastDotIndex = fileName.lastIndexOf('.')\n    if (lastDotIndex === -1) {\n      return ''\n    }\n    return fileName.substring(lastDotIndex).toLowerCase()\n  }\n\n  // 获取文件类型描述\n  static getFileTypeDescription(file: File): string {\n    const extension = this.getFileExtension(file.name)\n    \n    switch (extension) {\n      case '.txt':\n        return '纯文本文件'\n      case '.docx':\n        return 'Word 文档 (新版)'\n      case '.doc':\n        return 'Word 文档 (旧版)'\n      default:\n        return '未知格式'\n    }\n  }\n\n  // 格式化文件大小\n  static formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B'\n    \n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,uBAAuB;;IAClC,YAAY,OAAe,EAAE,AAAO,IAAY,CAAE;QAChD,KAAK,CAAC,eAD4B,OAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM;IACX,UAAU;IACV,OAAgB,kBAAkB;QAChC,cAAc;YAAC;SAAO;QACtB,2EAA2E;YAAC;SAAQ;QACpF,sBAAsB;YAAC;SAAO,CAAC,gBAAgB;IACjD,EAAC;IAED,aAAa;IACb,OAAO,yBAAmC;QACxC,OAAO,OAAO,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI;IACjD;IAEA,SAAS;IACT,OAAO,aAAa,IAAU,EAAQ;QACpC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QACjD,MAAM,sBAAsB,IAAI,CAAC,sBAAsB;QAEvD,IAAI,CAAC,oBAAoB,QAAQ,CAAC,YAAY;YAC5C,MAAM,IAAI,eACR,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,oBAAoB,IAAI,CAAC,OAAO,EACjE;QAEJ;QAEA,mBAAmB;QACnB,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,MAAM,IAAI,eACR,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,UAAU,OAAO,MAAM,EAAE,CAAC,EACjD;QAEJ;QAEA,UAAU;QACV,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,MAAM,IAAI,eACR,UACA;QAEJ;IACF;IAEA,SAAS;IACT,aAAa,UAAU,IAAU,EAAmB;QAClD,IAAI,CAAC,YAAY,CAAC;QAElB,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAEjD,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC;gBACjC,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;gBAClC,KAAK;oBACH,wBAAwB;oBACxB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;gBAClC;oBACE,MAAM,IAAI,eACR,CAAC,UAAU,EAAE,WAAW,EACxB;YAEN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,MAAM,IAAI,eACR,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ,EAC5D;QAEJ;IACF;IAEA,YAAY;IACZ,aAAqB,aAAa,IAAU,EAAmB;QAC7D,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;oBAC3C,OAAO,IAAI,eAAe,UAAU;oBACpC;gBACF;gBACA,QAAQ;YACV;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,eAAe,UAAU;YACtC;YAEA,UAAU;YACV,OAAO,UAAU,CAAC,MAAM;QAC1B;IACF;IAEA,aAAa;IACb,aAAqB,cAAc,IAAU,EAAmB;QAC9D,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACjD,MAAM,SAAS,MAAM,uIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAAE;YAAY;YAE1D,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;gBACrD,MAAM,IAAI,eAAe,cAAc;YACzC;YAEA,iBAAiB;YACjB,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACjD,QAAQ,IAAI,CAAC,eAAe,OAAO,QAAQ;YAC7C;YAEA,SAAS;YACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,MAAM,IAAI,eACR,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ,EAChE;QAEJ;IACF;IAEA,qBAAqB;IACrB,aAAqB,kBAAkB,IAAU,EAAwB;QACvE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,IAAI,kBAAkB,aAAa;oBACjC,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,MAAM;YACnB;YAEA,OAAO,iBAAiB,CAAC;QAC3B;IACF;IAEA,SAAS;IACT,OAAe,UAAU,IAAY,EAAU;QAC7C,OAAO,KACJ,OAAO,CAAC,SAAS,MAAO,QAAQ;SAChC,OAAO,CAAC,OAAO,MAAS,WAAW;SACnC,OAAO,CAAC,WAAW,QAAS,WAAW;SACvC,IAAI;IACT;IAEA,UAAU;IACV,OAAe,iBAAiB,QAAgB,EAAU;QACxD,MAAM,eAAe,SAAS,WAAW,CAAC;QAC1C,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO;QACT;QACA,OAAO,SAAS,SAAS,CAAC,cAAc,WAAW;IACrD;IAEA,WAAW;IACX,OAAO,uBAAuB,IAAU,EAAU;QAChD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAEjD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,UAAU;IACV,OAAO,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;AACF", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/utils/promptGenerator.ts"], "sourcesContent": ["// 角色形象提示词生成器\n// 专门用于从小说角色信息生成高质量的图像生成提示词\n\ninterface CharacterInfo {\n  name: string\n  identity?: string\n  personality?: string\n  physique?: string\n  facial?: string\n  hairstyle?: string\n  clothing?: string\n}\n\ninterface PromptOptions {\n  artStyle?: 'anime' | 'realistic' | 'semi-realistic' | 'concept-art'\n  viewType?: 'front' | 'side' | 'back' | 'three-quarter'\n  background?: 'white' | 'transparent' | 'simple' | 'detailed'\n  quality?: 'standard' | 'high' | 'masterpiece'\n  customEnhancement?: string\n}\n\n// 中文特征词汇到英文的映射\nconst FEATURE_MAPPING = {\n  // 年龄相关\n  '少女': 'young girl',\n  '青年女性': 'young woman', \n  '中年女性': 'middle-aged woman',\n  '少年': 'young boy',\n  '青年男性': 'young man',\n  '中年男性': 'middle-aged man',\n  \n  // 脸型\n  '瓜子脸': 'oval face',\n  '圆脸': 'round face',\n  '方脸': 'square face',\n  '长脸': 'long face',\n  '心形脸': 'heart-shaped face',\n  \n  // 眼睛\n  '大眼睛': 'large eyes',\n  '小眼睛': 'small eyes',\n  '丹凤眼': 'phoenix eyes',\n  '杏眼': 'almond eyes',\n  '圆眼': 'round eyes',\n  '深邃': 'deep-set eyes',\n  \n  // 发型\n  '长发': 'long hair',\n  '短发': 'short hair',\n  '中长发': 'medium length hair',\n  '卷发': 'curly hair',\n  '直发': 'straight hair',\n  '波浪发': 'wavy hair',\n  '马尾': 'ponytail',\n  '双马尾': 'twin tails',\n  '刘海': 'bangs',\n  \n  // 身材\n  '高挑': 'tall and slender',\n  '娇小': 'petite',\n  '匀称': 'well-proportioned',\n  '丰满': 'full-figured',\n  '苗条': 'slim',\n  '健美': 'athletic build',\n  \n  // 气质\n  '温柔': 'gentle',\n  '冷酷': 'cold',\n  '活泼': 'lively',\n  '优雅': 'elegant',\n  '知性': 'intellectual',\n  '神秘': 'mysterious',\n  '坚强': 'strong-willed',\n  '可爱': 'cute',\n  \n  // 服装\n  '职业装': 'business attire',\n  '休闲装': 'casual wear',\n  '正装': 'formal wear',\n  '校服': 'school uniform',\n  '古装': 'traditional costume',\n  '现代装': 'modern clothing'\n}\n\n// 艺术风格配置\nconst ART_STYLES = {\n  anime: {\n    keywords: 'anime style, manga style, cel shading, clean lines',\n    description: '日式动漫风格'\n  },\n  realistic: {\n    keywords: 'photorealistic, hyperrealistic, professional photography',\n    description: '写实摄影风格'\n  },\n  'semi-realistic': {\n    keywords: 'semi-realistic, digital art, detailed illustration',\n    description: '半写实插画风格'\n  },\n  'concept-art': {\n    keywords: 'concept art, character design, professional illustration',\n    description: '概念设计风格'\n  }\n}\n\n// 视角配置\nconst VIEW_TYPES = {\n  front: 'front view, facing camera, looking at viewer',\n  side: 'side view, 90-degree profile, side angle',\n  back: 'back view, rear angle, showing back',\n  'three-quarter': 'three-quarter view, slight angle'\n}\n\n// 质量等级配置\nconst QUALITY_LEVELS = {\n  standard: 'good quality, detailed',\n  high: 'high quality, high resolution, detailed, sharp focus',\n  masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'\n}\n\n/**\n * 从角色信息生成图像提示词\n */\nexport function generateCharacterPrompt(\n  character: CharacterInfo, \n  options: PromptOptions = {}\n): string {\n  const {\n    artStyle = 'anime',\n    viewType = 'front',\n    background = 'white',\n    quality = 'high',\n    customEnhancement = ''\n  } = options\n\n  // 构建提示词各部分\n  const parts: string[] = []\n\n  // 1. 质量和风格\n  parts.push(QUALITY_LEVELS[quality])\n  parts.push(ART_STYLES[artStyle].keywords)\n\n  // 2. 基础信息提取\n  const basicInfo = extractBasicInfo(character)\n  if (basicInfo) parts.push(basicInfo)\n\n  // 3. 外貌特征\n  const appearance = extractAppearanceFeatures(character)\n  if (appearance) parts.push(appearance)\n\n  // 4. 服装风格\n  const clothing = extractClothingStyle(character)\n  if (clothing) parts.push(clothing)\n\n  // 5. 气质表现\n  const temperament = extractTemperament(character)\n  if (temperament) parts.push(temperament)\n\n  // 6. 视角和姿态\n  parts.push(VIEW_TYPES[viewType])\n  parts.push('character design sheet, reference pose')\n\n  // 7. 背景设置\n  const backgroundDesc = getBackgroundDescription(background)\n  parts.push(backgroundDesc)\n\n  // 8. 技术参数\n  parts.push('professional character design, clean composition')\n\n  // 9. 自定义增强\n  if (customEnhancement.trim()) {\n    parts.push(customEnhancement)\n  }\n\n  return parts.filter(Boolean).join(', ')\n}\n\n/**\n * 提取基础信息（年龄、性别、身份）\n */\nfunction extractBasicInfo(character: CharacterInfo): string {\n  const info: string[] = []\n  \n  if (character.identity) {\n    // 从身份信息中提取年龄和职业\n    const ageMatch = character.identity.match(/(\\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/)\n    if (ageMatch) {\n      const age = ageMatch[1] || getAgeFromDescription(ageMatch[0])\n      info.push(`${age} years old`)\n    }\n    \n    // 提取性别（如果身份中包含）\n    if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {\n      info.push('woman')\n    } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {\n      info.push('man')\n    }\n    \n    // 提取职业\n    const profession = extractProfession(character.identity)\n    if (profession) info.push(profession)\n  }\n  \n  return info.join(' ')\n}\n\n/**\n * 提取外貌特征\n */\nfunction extractAppearanceFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n  \n  // 处理五官特征\n  if (character.facial) {\n    const facialFeatures = translateFeatures(character.facial)\n    features.push(facialFeatures)\n  }\n  \n  // 处理身材特征\n  if (character.physique) {\n    const bodyFeatures = translateFeatures(character.physique)\n    features.push(bodyFeatures)\n  }\n  \n  // 处理发型\n  if (character.hairstyle) {\n    const hairFeatures = translateFeatures(character.hairstyle)\n    features.push(hairFeatures)\n  }\n  \n  return features.filter(Boolean).join(', ')\n}\n\n/**\n * 提取服装风格\n */\nfunction extractClothingStyle(character: CharacterInfo): string {\n  if (!character.clothing) return ''\n  \n  return translateFeatures(character.clothing)\n}\n\n/**\n * 提取气质特征\n */\nfunction extractTemperament(character: CharacterInfo): string {\n  if (!character.personality) return ''\n  \n  const temperamentWords: string[] = []\n  \n  // 从性格描述中提取气质关键词\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (character.personality!.includes(chinese)) {\n      temperamentWords.push(english)\n    }\n  })\n  \n  // 添加表情描述\n  if (character.personality.includes('温柔') || character.personality.includes('善良')) {\n    temperamentWords.push('gentle smile', 'warm expression')\n  } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {\n    temperamentWords.push('serious expression', 'cold gaze')\n  } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {\n    temperamentWords.push('cheerful smile', 'bright expression')\n  }\n  \n  return temperamentWords.join(', ')\n}\n\n/**\n * 翻译特征描述\n */\nfunction translateFeatures(chineseText: string): string {\n  let result = chineseText\n  \n  // 使用映射表进行翻译\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    const regex = new RegExp(chinese, 'g')\n    result = result.replace(regex, english)\n  })\n  \n  // 清理和优化\n  result = result\n    .replace(/[，。；：]/g, ',') // 替换中文标点\n    .replace(/\\s+/g, ' ') // 合并空格\n    .replace(/,+/g, ',') // 合并逗号\n    .trim()\n  \n  return result\n}\n\n/**\n * 从描述中提取年龄\n */\nfunction getAgeFromDescription(ageDesc: string): string {\n  const ageMap: Record<string, string> = {\n    '十几岁': '16',\n    '二十多岁': '25', \n    '三十多岁': '35',\n    '四十多岁': '45'\n  }\n  return ageMap[ageDesc] || '25'\n}\n\n/**\n * 提取职业信息\n */\nfunction extractProfession(identity: string): string {\n  const professions: Record<string, string> = {\n    '学生': 'student',\n    '老师': 'teacher',\n    '医生': 'doctor',\n    '护士': 'nurse',\n    '律师': 'lawyer',\n    '工程师': 'engineer',\n    '设计师': 'designer',\n    '程序员': 'programmer',\n    '经理': 'manager',\n    '秘书': 'secretary',\n    '销售': 'salesperson',\n    '警察': 'police officer',\n    '军人': 'soldier',\n    '艺术家': 'artist',\n    '作家': 'writer',\n    '记者': 'journalist'\n  }\n  \n  for (const [chinese, english] of Object.entries(professions)) {\n    if (identity.includes(chinese)) {\n      return english\n    }\n  }\n  \n  return ''\n}\n\n/**\n * 获取背景描述\n */\nfunction getBackgroundDescription(background: string): string {\n  const backgrounds: Record<string, string> = {\n    white: 'clean white background',\n    transparent: 'transparent background',\n    simple: 'simple background',\n    detailed: 'detailed background scene'\n  }\n  \n  return backgrounds[background] || 'clean white background'\n}\n\n/**\n * 生成负面提示词\n */\nexport function generateNegativePrompt(): string {\n  return [\n    'blurry', 'low quality', 'pixelated', 'deformed', 'distorted',\n    'extra limbs', 'missing limbs', 'extra fingers', 'missing fingers',\n    'bad anatomy', 'bad proportions', 'ugly', 'duplicate',\n    'watermark', 'signature', 'text', 'logo',\n    'oversaturated', 'undersaturated', 'overexposed', 'underexposed'\n  ].join(', ')\n}\n\n/**\n * 生成角色一致性提示词（用于三视图）\n */\nexport function generateConsistencyPrompt(character: CharacterInfo, basePrompt: string): {\n  front: string\n  side: string\n  back: string\n} {\n  // 提取身材和服饰特征（保持一致）\n  const consistentFeatures = extractConsistentFeatures(character)\n\n  // 添加强化细节一致性的约束\n  const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories'\n\n  return {\n    // 正面：完整脸部可见，站立姿态\n    front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 侧面：90度侧脸轮廓，站立姿态\n    side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 背面：看不到脸，站立姿态\n    back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`\n  }\n}\n\n/**\n * 提取一致性特征（身材、服饰、发型等保持不变的特征）\n */\nfunction extractConsistentFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n\n  // 身材特征（保持一致）\n  if (character.physique) {\n    features.push(character.physique)\n  }\n\n  // 发型和发色（保持一致）\n  if (character.hairstyle) {\n    features.push(character.hairstyle)\n  }\n\n  // 服饰风格（保持一致，强调细节一致性）\n  if (character.clothing) {\n    features.push(character.clothing)\n    // 添加细节一致性约束\n    features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理')\n  }\n\n  // 身份特征（保持一致）\n  if (character.identity) {\n    features.push(character.identity)\n  }\n\n  // 添加强化一致性约束\n  features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰')\n\n  return features.join('，')\n}\n\nfunction extractFaceShape(facial: string): string {\n  for (const [chinese, english] of Object.entries(FEATURE_MAPPING)) {\n    if (facial.includes(chinese) && chinese.includes('脸')) {\n      return english\n    }\n  }\n  return ''\n}\n\nfunction extractHairStyle(hairstyle: string): string {\n  const hairFeatures: string[] = []\n  \n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {\n      hairFeatures.push(english)\n    }\n  })\n  \n  return hairFeatures.join(' ')\n}\n"], "names": [], "mappings": "AAAA,aAAa;AACb,2BAA2B;;;;;;AAoB3B,eAAe;AACf,MAAM,kBAAkB;IACtB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IAER,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IAEP,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IA<PERSON>,KAAK;IAC<PERSON>,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;AACT;AAEA,SAAS;AACT,MAAM,aAAa;IACjB,OAAO;QACL,UAAU;QACV,aAAa;IACf;IACA,WAAW;QACT,UAAU;QACV,aAAa;IACf;IACA,kBAAkB;QAChB,UAAU;QACV,aAAa;IACf;IACA,eAAe;QACb,UAAU;QACV,aAAa;IACf;AACF;AAEA,OAAO;AACP,MAAM,aAAa;IACjB,OAAO;IACP,MAAM;IACN,MAAM;IACN,iBAAiB;AACnB;AAEA,SAAS;AACT,MAAM,iBAAiB;IACrB,UAAU;IACV,MAAM;IACN,aAAa;AACf;AAKO,SAAS,wBACd,SAAwB,EACxB,UAAyB,CAAC,CAAC;IAE3B,MAAM,EACJ,WAAW,OAAO,EAClB,WAAW,OAAO,EAClB,aAAa,OAAO,EACpB,UAAU,MAAM,EAChB,oBAAoB,EAAE,EACvB,GAAG;IAEJ,WAAW;IACX,MAAM,QAAkB,EAAE;IAE1B,WAAW;IACX,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ;IAClC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ;IAExC,YAAY;IACZ,MAAM,YAAY,iBAAiB;IACnC,IAAI,WAAW,MAAM,IAAI,CAAC;IAE1B,UAAU;IACV,MAAM,aAAa,0BAA0B;IAC7C,IAAI,YAAY,MAAM,IAAI,CAAC;IAE3B,UAAU;IACV,MAAM,WAAW,qBAAqB;IACtC,IAAI,UAAU,MAAM,IAAI,CAAC;IAEzB,UAAU;IACV,MAAM,cAAc,mBAAmB;IACvC,IAAI,aAAa,MAAM,IAAI,CAAC;IAE5B,WAAW;IACX,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;IAC/B,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,iBAAiB,yBAAyB;IAChD,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,IAAI,CAAC;IAEX,WAAW;IACX,IAAI,kBAAkB,IAAI,IAAI;QAC5B,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC;AACpC;AAEA;;CAEC,GACD,SAAS,iBAAiB,SAAwB;IAChD,MAAM,OAAiB,EAAE;IAEzB,IAAI,UAAU,QAAQ,EAAE;QACtB,gBAAgB;QAChB,MAAM,WAAW,UAAU,QAAQ,CAAC,KAAK,CAAC;QAC1C,IAAI,UAAU;YACZ,MAAM,MAAM,QAAQ,CAAC,EAAE,IAAI,sBAAsB,QAAQ,CAAC,EAAE;YAC5D,KAAK,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;QAC9B;QAEA,gBAAgB;QAChB,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YAC9G,KAAK,IAAI,CAAC;QACZ,OAAO,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YACrH,KAAK,IAAI,CAAC;QACZ;QAEA,OAAO;QACP,MAAM,aAAa,kBAAkB,UAAU,QAAQ;QACvD,IAAI,YAAY,KAAK,IAAI,CAAC;IAC5B;IAEA,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,SAAS;IACT,IAAI,UAAU,MAAM,EAAE;QACpB,MAAM,iBAAiB,kBAAkB,UAAU,MAAM;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,SAAS;IACT,IAAI,UAAU,QAAQ,EAAE;QACtB,MAAM,eAAe,kBAAkB,UAAU,QAAQ;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;IACP,IAAI,UAAU,SAAS,EAAE;QACvB,MAAM,eAAe,kBAAkB,UAAU,SAAS;QAC1D,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO,SAAS,MAAM,CAAC,SAAS,IAAI,CAAC;AACvC;AAEA;;CAEC,GACD,SAAS,qBAAqB,SAAwB;IACpD,IAAI,CAAC,UAAU,QAAQ,EAAE,OAAO;IAEhC,OAAO,kBAAkB,UAAU,QAAQ;AAC7C;AAEA;;CAEC,GACD,SAAS,mBAAmB,SAAwB;IAClD,IAAI,CAAC,UAAU,WAAW,EAAE,OAAO;IAEnC,MAAM,mBAA6B,EAAE;IAErC,gBAAgB;IAChB,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,WAAW,CAAE,QAAQ,CAAC,UAAU;YAC5C,iBAAiB,IAAI,CAAC;QACxB;IACF;IAEA,SAAS;IACT,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QAChF,iBAAiB,IAAI,CAAC,gBAAgB;IACxC,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,sBAAsB;IAC9C,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,kBAAkB;IAC1C;IAEA,OAAO,iBAAiB,IAAI,CAAC;AAC/B;AAEA;;CAEC,GACD,SAAS,kBAAkB,WAAmB;IAC5C,IAAI,SAAS;IAEb,YAAY;IACZ,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,MAAM,QAAQ,IAAI,OAAO,SAAS;QAClC,SAAS,OAAO,OAAO,CAAC,OAAO;IACjC;IAEA,QAAQ;IACR,SAAS,OACN,OAAO,CAAC,WAAW,KAAK,SAAS;KACjC,OAAO,CAAC,QAAQ,KAAK,OAAO;KAC5B,OAAO,CAAC,OAAO,KAAK,OAAO;KAC3B,IAAI;IAEP,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,OAAe;IAC5C,MAAM,SAAiC;QACrC,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,QAAQ,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,kBAAkB,QAAgB;IACzC,MAAM,cAAsC;QAC1C,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,aAAc;QAC5D,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,yBAAyB,UAAkB;IAClD,MAAM,cAAsC;QAC1C,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;IACZ;IAEA,OAAO,WAAW,CAAC,WAAW,IAAI;AACpC;AAKO,SAAS;IACd,OAAO;QACL;QAAU;QAAe;QAAa;QAAY;QAClD;QAAe;QAAiB;QAAiB;QACjD;QAAe;QAAmB;QAAQ;QAC1C;QAAa;QAAa;QAAQ;QAClC;QAAiB;QAAkB;QAAe;KACnD,CAAC,IAAI,CAAC;AACT;AAKO,SAAS,0BAA0B,SAAwB,EAAE,UAAkB;IAKpF,kBAAkB;IAClB,MAAM,qBAAqB,0BAA0B;IAErD,eAAe;IACf,MAAM,oBAAoB;IAE1B,OAAO;QACL,iBAAiB;QACjB,OAAO,CAAC,iDAAiD,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAE5H,kBAAkB;QAClB,MAAM,CAAC,sDAAsD,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAEhI,eAAe;QACf,MAAM,CAAC,mDAAmD,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;IAC/H;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,cAAc;IACd,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,IAAI,CAAC,UAAU,SAAS;IACnC;IAEA,qBAAqB;IACrB,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;QAChC,YAAY;QACZ,SAAS,IAAI,CAAC,WAAW,WAAW;IACtC;IAEA,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,YAAY;IACZ,SAAS,IAAI,CAAC,QAAQ,WAAW,QAAQ,WAAW;IAEpD,OAAO,SAAS,IAAI,CAAC;AACvB;AAEA,SAAS,iBAAiB,MAAc;IACtC,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,iBAAkB;QAChE,IAAI,OAAO,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,MAAM;YACrD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,SAAiB;IACzC,MAAM,eAAyB,EAAE;IAEjC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,QAAQ,CAAC,YAAY,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,KAAK,GAAG;YACpF,aAAa,IAAI,CAAC;QACpB;IACF;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}]}