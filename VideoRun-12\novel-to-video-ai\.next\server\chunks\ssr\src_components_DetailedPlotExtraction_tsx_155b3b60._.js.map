{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/components/DetailedPlotExtraction.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Users, MapPin, Zap, Play, Settings, Eye, RefreshCw, Sparkles, ChevronDown, Save, Shield, AlertTriangle, CheckCircle } from 'lucide-react'\nimport ModelSelector from './ModelSelector'\n\ninterface Character {\n  id: string\n  name: string\n  identity?: string\n  personality?: string\n  physique?: string\n  facial?: string\n  hairstyle?: string\n  clothing?: string\n  // 新增：一致性相关字段\n  isKnownCharacter?: boolean\n  consistencyInfo?: {\n    matchedCharacterId?: string\n    consistencyMatch?: number\n    differences?: string[]\n    consistencyConstraints?: string\n  }\n}\n\ninterface Scene {\n  id: string\n  location: string\n  description: string\n  atmosphere: string\n}\n\ninterface PlotSequence {\n  sequenceId: string\n  action: string\n  emotion: string\n  keyMoments: string[]\n}\n\ninterface DetailedPlotExtractionProps {\n  episodeId: string\n  episodeTitle: string\n  episodeContent: string\n  isOpen: boolean\n  onClose: () => void\n  onGenerateStoryVideo?: (episodeId: string, prompt: string, modelId?: string) => void\n  videoGenerationMode?: 'auto' | 'manual'\n  onVideoGenerationModeChange?: (mode: 'auto' | 'manual') => void\n}\n\nexport default function DetailedPlotExtraction({\n  episodeId,\n  episodeTitle,\n  episodeContent,\n  isOpen,\n  onClose,\n  onGenerateStoryVideo,\n  videoGenerationMode = 'manual',\n  onVideoGenerationModeChange\n}: DetailedPlotExtractionProps) {\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [selectedModel, setSelectedModel] = useState('')\n  const [customPrompt, setCustomPrompt] = useState('')\n  const [isCustomPromptExpanded, setIsCustomPromptExpanded] = useState(false)\n  \n  // 提取的剧情信息\n  const [characters, setCharacters] = useState<Character[]>([])\n  const [scenes, setScenes] = useState<Scene[]>([])\n  const [plotSequences, setPlotSequences] = useState<PlotSequence[]>([])\n  const [emotionalArc, setEmotionalArc] = useState('')\n  const [generatedPrompt, setGeneratedPrompt] = useState('')\n\n  // 分析剧情信息\n  const analyzeDetailedPlot = async () => {\n    setIsAnalyzing(true)\n    try {\n      const response = await fetch('/api/ai/analyze-detailed-plot', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          episodeId,\n          episodeContent,\n          customPrompt\n        })\n      })\n\n      if (!response.ok) {\n        throw new Error('分析失败')\n      }\n\n      const result = await response.json()\n      \n      if (result.success) {\n        setCharacters(result.data.characters || [])\n        setScenes(result.data.scenes || [])\n        setPlotSequences(result.data.plotSequences || [])\n        setEmotionalArc(result.data.emotionalArc || '')\n        setGeneratedPrompt(result.data.generatedPrompt || '')\n      } else {\n        alert('分析失败：' + result.error)\n      }\n    } catch (error) {\n      console.error('分析剧情失败:', error)\n      alert('分析剧情失败，请重试')\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 保存增强提示词\n  const saveCustomPrompt = async () => {\n    try {\n      const response = await fetch('/api/ai/save-custom-prompt', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          episodeId,\n          customPrompt\n        })\n      })\n\n      if (!response.ok) {\n        throw new Error('保存失败')\n      }\n\n      const result = await response.json()\n      if (result.success) {\n        alert('增强提示词保存成功！')\n      } else {\n        alert('保存失败：' + result.error)\n      }\n    } catch (error) {\n      console.error('保存增强提示词失败:', error)\n      alert('保存增强提示词失败，请重试')\n    }\n  }\n\n  // 生成剧情视频\n  const handleGenerateStoryVideo = async () => {\n    if (!selectedModel) {\n      alert('请先选择AI模型')\n      return\n    }\n\n    if (!generatedPrompt) {\n      alert('请先分析剧情信息')\n      return\n    }\n\n    setIsAnalyzing(true)\n    try {\n      if (onGenerateStoryVideo) {\n        await onGenerateStoryVideo(episodeId, generatedPrompt, selectedModel)\n      }\n    } catch (error) {\n      console.error('生成剧情视频失败:', error)\n      alert('生成剧情视频失败，请重试')\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 加载已保存的自定义提示词\n  const loadSavedCustomPrompt = async () => {\n    try {\n      const response = await fetch(`/api/ai/save-custom-prompt?episodeId=${episodeId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setCustomPrompt(data.data.customPrompt || '')\n      }\n    } catch (error) {\n      console.error('加载保存的自定义提示词失败:', error)\n    }\n  }\n\n  // 加载已保存的详细剧情信息\n  const loadSavedPlotInfo = async () => {\n    try {\n      const response = await fetch(`/api/ai/analyze-detailed-plot?episodeId=${episodeId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setCharacters(data.data.characters || [])\n        setScenes(data.data.scenes || [])\n        setPlotSequences(data.data.plotSequences || [])\n        setEmotionalArc(data.data.emotionalArc || '')\n        setGeneratedPrompt(data.data.generatedPrompt || '')\n        return true // 表示成功加载了保存的数据\n      }\n      return false // 表示没有保存的数据\n    } catch (error) {\n      console.error('加载保存的剧情信息失败:', error)\n      return false\n    }\n  }\n\n  // 初始化时先尝试加载保存的信息，如果没有则自动分析\n  useEffect(() => {\n    if (isOpen && episodeId) {\n      // 加载保存的自定义提示词\n      loadSavedCustomPrompt()\n\n      // 加载保存的剧情信息\n      loadSavedPlotInfo().then(hasData => {\n        if (!hasData && episodeContent) {\n          // 如果没有保存的数据，则进行新的分析\n          analyzeDetailedPlot()\n        }\n      })\n    }\n  }, [isOpen, episodeId])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <Zap className=\"text-purple-600 mr-2\" size={24} />\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              剧情信息提取 - {episodeTitle}\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        <div className=\"flex h-[calc(90vh-80px)]\">\n          {/* 左侧：剧情分析结果 */}\n          <div className=\"w-2/3 p-6 overflow-y-auto border-r border-gray-200\">\n            {isAnalyzing ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4\"></div>\n                  <p className=\"text-gray-600\">正在分析剧情信息...</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                {/* 剧集内容概览 */}\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">剧集内容</h3>\n                  <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4 max-h-32 overflow-y-auto\">\n                    <p className=\"text-sm text-gray-700\">{episodeContent}</p>\n                  </div>\n                </div>\n\n                {/* 角色DNA信息（增强版：包含一致性指示器） */}\n                <div>\n                  <div className=\"flex items-center mb-3\">\n                    <Users className=\"text-blue-600 mr-2\" size={20} />\n                    <h3 className=\"text-lg font-medium text-gray-900\">角色DNA信息</h3>\n                    <Shield className=\"text-purple-600 ml-2\" size={16} title=\"一致性约束\" />\n                  </div>\n                  {characters.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {characters.map((character, index) => (\n                        <div key={index} className={`border rounded-md p-4 relative ${\n                          character.isKnownCharacter\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-blue-50 border-blue-200'\n                        }`}>\n                          {/* 一致性状态指示器 */}\n                          <div className=\"absolute top-3 right-3 flex items-center space-x-2\">\n                            {character.isKnownCharacter ? (\n                              <>\n                                <CheckCircle className=\"text-green-600\" size={16} title=\"已知角色\" />\n                                {character.consistencyInfo?.consistencyMatch && (\n                                  <span className={`text-xs px-2 py-1 rounded-full ${\n                                    character.consistencyInfo.consistencyMatch >= 0.9\n                                      ? 'bg-green-100 text-green-800'\n                                      : character.consistencyInfo.consistencyMatch >= 0.7\n                                      ? 'bg-yellow-100 text-yellow-800'\n                                      : 'bg-red-100 text-red-800'\n                                  }`}>\n                                    {(character.consistencyInfo.consistencyMatch * 100).toFixed(0)}%\n                                  </span>\n                                )}\n                              </>\n                            ) : (\n                              <AlertTriangle className=\"text-orange-500\" size={16} title=\"新角色\" />\n                            )}\n                          </div>\n\n                          <h4 className={`font-medium pr-16 ${\n                            character.isKnownCharacter ? 'text-green-900' : 'text-blue-900'\n                          }`}>\n                            {character.name}\n                            {character.isKnownCharacter && (\n                              <span className=\"ml-2 text-xs text-green-600\">(已知角色)</span>\n                            )}\n                          </h4>\n\n                          <div className={`mt-2 space-y-1 text-sm ${\n                            character.isKnownCharacter ? 'text-green-800' : 'text-blue-800'\n                          }`}>\n                            {character.identity && <p><strong>身份：</strong>{character.identity}</p>}\n                            {(character.facial || character.appearance) && <p><strong>外貌：</strong>{character.facial || character.appearance}</p>}\n                            {character.personality && <p><strong>性格：</strong>{character.personality}</p>}\n                            {character.clothing && <p><strong>服装：</strong>{character.clothing}</p>}\n                            {character.role && <p><strong>作用：</strong>{character.role}</p>}\n\n                            {/* 一致性信息显示 */}\n                            {character.consistencyInfo && (\n                              <div className=\"mt-3 p-2 bg-white bg-opacity-50 rounded border\">\n                                <p className=\"text-xs font-medium text-gray-700 mb-1\">一致性分析：</p>\n                                {character.consistencyInfo.consistencyConstraints && (\n                                  <p className=\"text-xs text-gray-600 mb-1\">\n                                    <strong>约束建议：</strong>{character.consistencyInfo.consistencyConstraints}\n                                  </p>\n                                )}\n                                {character.consistencyInfo.differences && character.consistencyInfo.differences.length > 0 && (\n                                  <p className=\"text-xs text-orange-600\">\n                                    <strong>差异：</strong>{character.consistencyInfo.differences.join(', ')}\n                                  </p>\n                                )}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"text-gray-500\">暂无角色信息</p>\n                  )}\n                </div>\n\n                {/* 场景模板信息 */}\n                <div>\n                  <div className=\"flex items-center mb-3\">\n                    <MapPin className=\"text-green-600 mr-2\" size={20} />\n                    <h3 className=\"text-lg font-medium text-gray-900\">场景模板信息</h3>\n                  </div>\n                  {scenes.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {scenes.map((scene, index) => (\n                        <div key={index} className=\"bg-green-50 border border-green-200 rounded-md p-4\">\n                          <h4 className=\"font-medium text-green-900\">{scene.location}</h4>\n                          <div className=\"mt-2 space-y-1 text-sm text-green-800\">\n                            {scene.description && <p><strong>描述：</strong>{scene.description}</p>}\n                            {scene.atmosphere && <p><strong>氛围：</strong>{scene.atmosphere}</p>}\n                            {scene.timeOfDay && <p><strong>时间：</strong>{scene.timeOfDay}</p>}\n                            {scene.lighting && <p><strong>光线：</strong>{scene.lighting}</p>}\n                            {scene.keyElements && <p><strong>关键元素：</strong>{scene.keyElements}</p>}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"text-gray-500\">暂无场景信息</p>\n                  )}\n                </div>\n\n                {/* 情节分解结果 */}\n                <div>\n                  <div className=\"flex items-center mb-3\">\n                    <Settings className=\"text-orange-600 mr-2\" size={20} />\n                    <h3 className=\"text-lg font-medium text-gray-900\">情节分解结果</h3>\n                  </div>\n                  {plotSequences.length > 0 ? (\n                    <div className=\"space-y-3\">\n                      {plotSequences.map((sequence, index) => (\n                        <div key={index} className=\"bg-orange-50 border border-orange-200 rounded-md p-4\">\n                          <h4 className=\"font-medium text-orange-900\">序列 {index + 1}</h4>\n                          <div className=\"mt-2 space-y-1 text-sm text-orange-800\">\n                            {sequence.action && <p><strong>动作：</strong>{sequence.action}</p>}\n                            {sequence.emotion && <p><strong>情感：</strong>{sequence.emotion}</p>}\n                            {sequence.duration && <p><strong>时长：</strong>{sequence.duration}</p>}\n                            {sequence.keyMoments && sequence.keyMoments.length > 0 && (\n                              <p><strong>关键时刻：</strong>{Array.isArray(sequence.keyMoments) ? sequence.keyMoments.join(', ') : sequence.keyMoments}</p>\n                            )}\n                            {sequence.visualElements && <p><strong>视觉要点：</strong>{sequence.visualElements}</p>}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"text-gray-500\">暂无情节信息</p>\n                  )}\n                </div>\n\n                {/* 情感弧线 */}\n                {emotionalArc && (\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-3\">情感弧线</h3>\n                    <div className=\"bg-purple-50 border border-purple-200 rounded-md p-4\">\n                      <p className=\"text-sm text-purple-800\">{emotionalArc}</p>\n                    </div>\n                  </div>\n                )}\n\n                {/* 生成的提示词 */}\n                {generatedPrompt && (\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-3\">生成的视频提示词</h3>\n                    <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4\">\n                      <pre className=\"text-sm text-gray-700 whitespace-pre-wrap font-mono\">\n                        {generatedPrompt}\n                      </pre>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* 右侧：操作面板 */}\n          <div className=\"w-1/3 p-6 bg-gray-50\">\n            <div className=\"space-y-6\">\n              {/* 重新分析按钮 */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-3\">分析控制</h3>\n                <div className=\"flex\">\n                  <button\n                    onClick={analyzeDetailedPlot}\n                    disabled={isAnalyzing}\n                    className=\"flex-1 flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-l-md hover:bg-purple-700 disabled:opacity-50\"\n                  >\n                    <RefreshCw className=\"mr-2\" size={16} />\n                    {isAnalyzing ? '分析中...' : '重新分析'}\n                  </button>\n                  <button\n                    onClick={() => setIsCustomPromptExpanded(!isCustomPromptExpanded)}\n                    className=\"px-3 py-2 bg-purple-600 text-white rounded-r-md hover:bg-purple-700 border-l border-purple-500\"\n                  >\n                    <ChevronDown\n                      size={16}\n                      className={`transform transition-transform ${isCustomPromptExpanded ? 'rotate-180' : ''}`}\n                    />\n                  </button>\n                </div>\n              </div>\n\n              {/* 自定义增强 */}\n              {isCustomPromptExpanded && (\n                <div className=\"mb-8\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    自定义增强要求\n                  </label>\n                  <div className=\"relative\">\n                    <textarea\n                      value={customPrompt}\n                      onChange={(e) => setCustomPrompt(e.target.value)}\n                      placeholder=\"添加特殊要求，如特定镜头角度、特效等...\"\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    />\n                    <button\n                      onClick={saveCustomPrompt}\n                      className=\"absolute -bottom-7 right-0 flex items-center justify-center px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs shadow-sm\"\n                    >\n                      <Save className=\"mr-1\" size={12} />\n                      保存\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* 模型选择 */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-3\">视频生成设置</h3>\n                <ModelSelector\n                  selectedModel={selectedModel}\n                  onModelSelect={setSelectedModel}\n                />\n\n                {/* 生成模式选择 */}\n                <div className=\"mt-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    生成模式\n                  </label>\n                  <div className=\"flex space-x-3\">\n                    <label className={`flex items-center px-3 py-2 rounded-lg border cursor-pointer transition-colors ${\n                      videoGenerationMode === 'manual'\n                        ? 'border-purple-500 bg-purple-50 text-purple-700'\n                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                    }`}>\n                      <input\n                        type=\"radio\"\n                        name=\"generationMode\"\n                        value=\"manual\"\n                        checked={videoGenerationMode === 'manual'}\n                        onChange={(e) => onVideoGenerationModeChange?.(e.target.value as 'manual')}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm font-medium\">手动模式</span>\n                      <span className=\"ml-1 text-xs opacity-75\">(推荐)</span>\n                    </label>\n                    <label className={`flex items-center px-3 py-2 rounded-lg border cursor-pointer transition-colors ${\n                      videoGenerationMode === 'auto'\n                        ? 'border-purple-500 bg-purple-50 text-purple-700'\n                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                    }`}>\n                      <input\n                        type=\"radio\"\n                        name=\"generationMode\"\n                        value=\"auto\"\n                        checked={videoGenerationMode === 'auto'}\n                        onChange={(e) => onVideoGenerationModeChange?.(e.target.value as 'auto')}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm font-medium\">自动模式</span>\n                    </label>\n                  </div>\n                  <p className=\"text-xs text-gray-500 mt-2\">\n                    {videoGenerationMode === 'manual'\n                      ? '手动模式：创建片段后需要逐个点击生成，可选择性生成需要的片段'\n                      : '自动模式：创建片段后自动开始生成所有片段'}\n                  </p>\n                </div>\n              </div>\n\n              {/* 生成剧情视频按钮 */}\n              <button\n                onClick={handleGenerateStoryVideo}\n                disabled={!selectedModel || !generatedPrompt || isAnalyzing}\n                className=\"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <Sparkles className=\"mr-2\" size={16} />\n                生成剧情视频\n              </button>\n\n              {/* 说明信息（增强版：包含一致性功能） */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n                <h4 className=\"text-sm font-medium text-blue-800 mb-2\">一致性增强功能</h4>\n                <ul className=\"text-sm text-blue-700 space-y-1\">\n                  <li>• <strong>智能角色匹配：</strong>自动识别已知角色，保持一致性约束</li>\n                  <li>• <strong>一致性评分：</strong>实时评估角色与项目库的匹配度</li>\n                  <li>• <strong>差异检测：</strong>识别并标注角色描述的不一致之处</li>\n                  <li>• <strong>约束建议：</strong>提供专业的一致性改进建议</li>\n                  <li>• <strong>双重约束：</strong>结合文本和视觉约束生成视频</li>\n                  <li>• <strong>DNA档案：</strong>建立详细的角色和场景DNA库</li>\n                </ul>\n                <div className=\"mt-3 flex items-center space-x-4 text-xs\">\n                  <div className=\"flex items-center space-x-1\">\n                    <CheckCircle className=\"text-green-600\" size={12} />\n                    <span>已知角色</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <AlertTriangle className=\"text-orange-500\" size={12} />\n                    <span>新角色</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <Shield className=\"text-purple-600\" size={12} />\n                    <span>一致性约束</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAkDe,SAAS,uBAAuB,EAC7C,SAAS,EACT,YAAY,EACZ,cAAc,EACd,MAAM,EACN,OAAO,EACP,oBAAoB,EACpB,sBAAsB,QAAQ,EAC9B,2BAA2B,EACC;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,UAAU;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,SAAS;IACT,MAAM,sBAAsB;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,cAAc,OAAO,IAAI,CAAC,UAAU,IAAI,EAAE;gBAC1C,UAAU,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE;gBAClC,iBAAiB,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;gBAChD,gBAAgB,OAAO,IAAI,CAAC,YAAY,IAAI;gBAC5C,mBAAmB,OAAO,IAAI,CAAC,eAAe,IAAI;YACpD,OAAO;gBACL,MAAM,UAAU,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,UAAU;IACV,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;YACR,OAAO;gBACL,MAAM,UAAU,OAAO,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,2BAA2B;QAC/B,IAAI,CAAC,eAAe;YAClB,MAAM;YACN;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB,MAAM;YACN;QACF;QAEA,eAAe;QACf,IAAI;YACF,IAAI,sBAAsB;gBACxB,MAAM,qBAAqB,WAAW,iBAAiB;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,eAAe;IACf,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qCAAqC,EAAE,WAAW;YAChF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,wCAAwC,EAAE,WAAW;YACnF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,UAAU,KAAK,IAAI,CAAC,MAAM,IAAI,EAAE;gBAChC,iBAAiB,KAAK,IAAI,CAAC,aAAa,IAAI,EAAE;gBAC9C,gBAAgB,KAAK,IAAI,CAAC,YAAY,IAAI;gBAC1C,mBAAmB,KAAK,IAAI,CAAC,eAAe,IAAI;gBAChD,OAAO,KAAK,eAAe;;YAC7B;YACA,OAAO,MAAM,YAAY;;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,cAAc;YACd;YAEA,YAAY;YACZ,oBAAoB,IAAI,CAAC,CAAA;gBACvB,IAAI,CAAC,WAAW,gBAAgB;oBAC9B,oBAAoB;oBACpB;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;oCAAuB,MAAM;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;;wCAAsC;wCACxC;;;;;;;;;;;;;sCAGd,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;qDAIjC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;kDAK1C,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAqB,MAAM;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;wDAAuB,MAAM;wDAAI,OAAM;;;;;;;;;;;;4CAE1D,WAAW,MAAM,GAAG,kBACnB,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;wDAAgB,WAAW,CAAC,+BAA+B,EAC1D,UAAU,gBAAgB,GACtB,iCACA,8BACJ;;0EAEA,8OAAC;gEAAI,WAAU;0EACZ,UAAU,gBAAgB,iBACzB;;sFACE,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;4EAAiB,MAAM;4EAAI,OAAM;;;;;;wEACvD,UAAU,eAAe,EAAE,kCAC1B,8OAAC;4EAAK,WAAW,CAAC,+BAA+B,EAC/C,UAAU,eAAe,CAAC,gBAAgB,IAAI,MAC1C,gCACA,UAAU,eAAe,CAAC,gBAAgB,IAAI,MAC9C,kCACA,2BACJ;;gFACC,CAAC,UAAU,eAAe,CAAC,gBAAgB,GAAG,GAAG,EAAE,OAAO,CAAC;gFAAG;;;;;;;;iGAKrE,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;oEAAkB,MAAM;oEAAI,OAAM;;;;;;;;;;;0EAI/D,8OAAC;gEAAG,WAAW,CAAC,kBAAkB,EAChC,UAAU,gBAAgB,GAAG,mBAAmB,iBAChD;;oEACC,UAAU,IAAI;oEACd,UAAU,gBAAgB,kBACzB,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;;;;;;;0EAIlD,8OAAC;gEAAI,WAAW,CAAC,uBAAuB,EACtC,UAAU,gBAAgB,GAAG,mBAAmB,iBAChD;;oEACC,UAAU,QAAQ,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,UAAU,QAAQ;;;;;;;oEAChE,CAAC,UAAU,MAAM,IAAI,UAAU,UAAU,mBAAK,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,UAAU,MAAM,IAAI,UAAU,UAAU;;;;;;;oEAC9G,UAAU,WAAW,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,UAAU,WAAW;;;;;;;oEACtE,UAAU,QAAQ,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,UAAU,QAAQ;;;;;;;oEAChE,UAAU,IAAI,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,UAAU,IAAI;;;;;;;oEAGxD,UAAU,eAAe,kBACxB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAyC;;;;;;4EACrD,UAAU,eAAe,CAAC,sBAAsB,kBAC/C,8OAAC;gFAAE,WAAU;;kGACX,8OAAC;kGAAO;;;;;;oFAAe,UAAU,eAAe,CAAC,sBAAsB;;;;;;;4EAG1E,UAAU,eAAe,CAAC,WAAW,IAAI,UAAU,eAAe,CAAC,WAAW,CAAC,MAAM,GAAG,mBACvF,8OAAC;gFAAE,WAAU;;kGACX,8OAAC;kGAAO;;;;;;oFAAa,UAAU,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;uDAxDlE;;;;;;;;;qEAkEd,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAKjC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;wDAAsB,MAAM;;;;;;kEAC9C,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;4CAEnD,OAAO,MAAM,GAAG,kBACf,8OAAC;gDAAI,WAAU;0DACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAG,WAAU;0EAA8B,MAAM,QAAQ;;;;;;0EAC1D,8OAAC;gEAAI,WAAU;;oEACZ,MAAM,WAAW,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,MAAM,WAAW;;;;;;;oEAC9D,MAAM,UAAU,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,MAAM,UAAU;;;;;;;oEAC5D,MAAM,SAAS,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,MAAM,SAAS;;;;;;;oEAC1D,MAAM,QAAQ,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,MAAM,QAAQ;;;;;;;oEACxD,MAAM,WAAW,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAe,MAAM,WAAW;;;;;;;;;;;;;;uDAP3D;;;;;;;;;qEAad,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAKjC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAuB,MAAM;;;;;;kEACjD,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;4CAEnD,cAAc,MAAM,GAAG,kBACtB,8OAAC;gDAAI,WAAU;0DACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAG,WAAU;;oEAA8B;oEAAI,QAAQ;;;;;;;0EACxD,8OAAC;gEAAI,WAAU;;oEACZ,SAAS,MAAM,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,SAAS,MAAM;;;;;;;oEAC1D,SAAS,OAAO,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,SAAS,OAAO;;;;;;;oEAC5D,SAAS,QAAQ,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAa,SAAS,QAAQ;;;;;;;oEAC9D,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,MAAM,GAAG,mBACnD,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAe,MAAM,OAAO,CAAC,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ,SAAS,UAAU;;;;;;;oEAEpH,SAAS,cAAc,kBAAI,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAe,SAAS,cAAc;;;;;;;;;;;;;;uDATvE;;;;;;;;;qEAed,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;oCAKhC,8BACC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;;;;;;oCAM7C,iCACC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;;0EAEV,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DACjC,cAAc,WAAW;;;;;;;kEAE5B,8OAAC;wDACC,SAAS,IAAM,0BAA0B,CAAC;wDAC1C,WAAU;kEAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DACV,MAAM;4DACN,WAAW,CAAC,+BAA+B,EAAE,yBAAyB,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;oCAOhG,wCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,aAAY;wDACZ,MAAM;wDACN,WAAU;;;;;;kEAEZ,8OAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAO,MAAM;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;kDAQ3C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC,mIAAA,CAAA,UAAa;gDACZ,eAAe;gDACf,eAAe;;;;;;0DAIjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAW,CAAC,+EAA+E,EAChG,wBAAwB,WACpB,mDACA,0DACJ;;kFACA,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAM;wEACN,SAAS,wBAAwB;wEACjC,UAAU,CAAC,IAAM,8BAA8B,EAAE,MAAM,CAAC,KAAK;wEAC7D,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;wEAAK,WAAU;kFAA0B;;;;;;;;;;;;0EAE5C,8OAAC;gEAAM,WAAW,CAAC,+EAA+E,EAChG,wBAAwB,SACpB,mDACA,0DACJ;;kFACA,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,OAAM;wEACN,SAAS,wBAAwB;wEACjC,UAAU,CAAC,IAAM,8BAA8B,EAAE,MAAM,CAAC,KAAK;wEAC7D,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;;;;;;;;;;;;;kEAG1C,8OAAC;wDAAE,WAAU;kEACV,wBAAwB,WACrB,mCACA;;;;;;;;;;;;;;;;;;kDAMV,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,iBAAiB,CAAC,mBAAmB;wCAChD,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;kDAKzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAgB;;;;;;;kEAC9B,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAe;;;;;;;kEAC7B,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;kEAC5B,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;kEAC5B,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;kEAC5B,8OAAC;;4DAAG;0EAAE,8OAAC;0EAAO;;;;;;4DAAe;;;;;;;;;;;;;0DAE/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;gEAAiB,MAAM;;;;;;0EAC9C,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;gEAAkB,MAAM;;;;;;0EACjD,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;gEAAkB,MAAM;;;;;;0EAC1C,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}]}