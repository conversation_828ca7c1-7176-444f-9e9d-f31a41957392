const { PrismaClient } = require('@prisma/client');

async function checkDoubaoConfig() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 检查当前豆包配置...');
    
    const doubaoConfigs = await prisma.aIConfig.findMany({
      where: {
        provider: 'doubao'
      },
      orderBy: { createdAt: 'desc' }
    });
    
    if (doubaoConfigs.length === 0) {
      console.log('❌ 未找到豆包配置');
      return;
    }
    
    console.log(`✅ 找到 ${doubaoConfigs.length} 个豆包配置:`);
    
    doubaoConfigs.forEach((config, index) => {
      console.log(`\n${index + 1}. ${config.name}`);
      console.log(`   模型: ${config.model}`);
      console.log(`   API Key: ${config.apiKey.substring(0, 8)}...${config.apiKey.substring(-4)}`);
      console.log(`   启用状态: ${config.enabled ? '✅ 已启用' : '❌ 未启用'}`);
      console.log(`   支持视频: ${config.supportsVideo ? '✅' : '❌'}`);
      console.log(`   支持图生视频: ${config.supportsImageToVideo ? '✅' : '❌'}`);
    });
    
  } catch (error) {
    console.error('❌ 检查配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDoubaoConfig();
