{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/models/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\ninterface ModelConfig {\n  id?: string\n  provider: string\n  model: string\n  apiKey: string\n  name: string\n}\n\n// 测试模型连接\nasync function testModelConnection(config: ModelConfig): Promise<{ success: boolean; error?: string }> {\n  try {\n    // 根据不同的提供商进行测试\n    switch (config.provider) {\n      case 'deepseek':\n        return await testDeepSeekConnection(config)\n      case 'tongyi':\n        return await testTongyiConnection(config)\n      case 'minimax':\n        return await testMinimaxConnection(config)\n      case 'zhipu':\n        return await testZhipuConnection(config)\n      case 'openai':\n        return await testOpenAIConnection(config)\n      case 'claude':\n        return await testClaudeConnection(config)\n      case 'doubao':\n        return await testDoubaoConnection(config)\n      case 'skyreels':\n        return await testSkyReelsConnection(config)\n      default:\n        return { success: false, error: '不支持的模型提供商' }\n    }\n  } catch (error) {\n    console.error('测试模型连接失败:', error)\n    return { success: false, error: '连接测试失败' }\n  }\n}\n\n// 测试DeepSeek连接\nasync function testDeepSeekConnection(config: ModelConfig) {\n  try {\n    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`\n      },\n      body: JSON.stringify({\n        model: config.model,\n        messages: [\n          { role: 'user', content: '测试连接' }\n        ],\n        max_tokens: 10\n      })\n    })\n\n    if (response.ok) {\n      return { success: true }\n    } else {\n      const error = await response.text()\n      return { success: false, error: `DeepSeek API错误: ${error}` }\n    }\n  } catch (error) {\n    return { success: false, error: `DeepSeek连接失败: ${error}` }\n  }\n}\n\n// 测试通义连接\nasync function testTongyiConnection(config: ModelConfig) {\n  try {\n    // 通义的API测试逻辑\n    // 支持通义千问和通义万相模型\n    let requestBody: any = {\n      model: config.model,\n      input: {\n        messages: [\n          { role: 'user', content: '测试连接' }\n        ]\n      },\n      parameters: {\n        max_tokens: 10\n      }\n    }\n\n    // 对于turbo模型，添加result_format参数\n    if (config.model.includes('turbo')) {\n      requestBody.parameters.result_format = 'message'\n    }\n\n    console.log(`🔍 测试通义API连接: ${config.model}`)\n    console.log(`🔑 API密钥前缀: ${config.apiKey.substring(0, 8)}...`)\n\n    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`,\n        'X-DashScope-SSE': 'disable'\n      },\n      body: JSON.stringify(requestBody)\n    })\n\n    if (response.ok) {\n      const data = await response.json()\n\n      // 检查响应中的错误码\n      if (data.code && data.code !== 'Success') {\n        if (data.code === 'Arrearage') {\n          return { success: false, error: '通义API账户欠费，请前往阿里云控制台充值后重试' }\n        } else if (data.code === 'InvalidApiKey') {\n          return { success: false, error: '通义API密钥无效，请检查配置' }\n        } else {\n          return { success: false, error: `通义API错误: ${data.message || data.code}` }\n        }\n      }\n\n      return { success: true }\n    } else {\n      const errorText = await response.text()\n      console.error(`❌ 通义API测试失败 (${response.status}):`, errorText)\n\n      let errorData: any = {}\n      try {\n        errorData = JSON.parse(errorText)\n      } catch (e) {\n        // 如果不是JSON格式，检查是否包含url error\n        if (errorText.includes('url error')) {\n          return { success: false, error: '通义API URL错误，请检查模型名称是否正确' }\n        }\n        return { success: false, error: `通义API错误: ${errorText || response.statusText}` }\n      }\n\n      if (errorData.code === 'Arrearage') {\n        return { success: false, error: '通义API账户欠费，请前往阿里云控制台充值后重试' }\n      } else if (errorData.code === 'InvalidApiKey') {\n        return { success: false, error: '通义API密钥无效，请检查配置' }\n      } else if (errorData.message && errorData.message.includes('url error')) {\n        return { success: false, error: '通义API URL错误，请检查模型名称是否正确' }\n      } else {\n        return { success: false, error: `通义API错误: ${errorData.message || response.statusText}` }\n      }\n    }\n  } catch (error) {\n    console.error('通义API测试异常:', error)\n    return { success: false, error: `通义连接失败: ${error}` }\n  }\n}\n\n// 测试OpenAI连接\nasync function testOpenAIConnection(config: ModelConfig) {\n  try {\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`\n      },\n      body: JSON.stringify({\n        model: config.model,\n        messages: [\n          { role: 'user', content: '测试连接' }\n        ],\n        max_tokens: 10\n      })\n    })\n\n    if (response.ok) {\n      return { success: true }\n    } else {\n      const error = await response.text()\n      return { success: false, error: `OpenAI API错误: ${error}` }\n    }\n  } catch (error) {\n    return { success: false, error: `OpenAI连接失败: ${error}` }\n  }\n}\n\n// 测试MiniMax连接\nasync function testMinimaxConnection(config: ModelConfig) {\n  try {\n    // MiniMax Hailuo视频生成API测试\n    const response = await fetch('https://api.minimaxi.com/v1/video_generation', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`\n      },\n      body: JSON.stringify({\n        model: config.model,\n        prompt: '测试连接',\n        // 添加基本参数\n        video_setting: {\n          video_duration: 6,\n          video_aspect_ratio: '16:9'\n        }\n      })\n    })\n\n    if (response.ok) {\n      return { success: true }\n    } else {\n      const error = await response.text()\n      return { success: false, error: `MiniMax API错误: ${error}` }\n    }\n  } catch (error) {\n    return { success: false, error: `MiniMax连接失败: ${error}` }\n  }\n}\n\n// 测试智谱AI连接\nasync function testZhipuConnection(config: ModelConfig) {\n  try {\n    // 智谱AI CogView图像生成API测试\n    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/images/generations', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`\n      },\n      body: JSON.stringify({\n        model: config.model,\n        prompt: '测试连接',\n        size: '1024x1024',\n        quality: 'standard',\n        n: 1\n      })\n    })\n\n    if (response.ok) {\n      return { success: true }\n    } else {\n      const error = await response.text()\n      return { success: false, error: `智谱AI API错误: ${error}` }\n    }\n  } catch (error) {\n    return { success: false, error: `智谱AI连接失败: ${error}` }\n  }\n}\n\n// 测试Claude连接\nasync function testClaudeConnection(config: ModelConfig) {\n  try {\n    const response = await fetch('https://api.anthropic.com/v1/messages', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-api-key': config.apiKey,\n        'anthropic-version': '2023-06-01'\n      },\n      body: JSON.stringify({\n        model: config.model,\n        max_tokens: 10,\n        messages: [\n          { role: 'user', content: '测试连接' }\n        ]\n      })\n    })\n\n    if (response.ok) {\n      return { success: true }\n    } else {\n      const error = await response.text()\n      return { success: false, error: `Claude API错误: ${error}` }\n    }\n  } catch (error) {\n    return { success: false, error: `Claude连接失败: ${error}` }\n  }\n}\n\n// 测试豆包连接（带指数退避重试机制）\nasync function testDoubaoConnection(config: ModelConfig) {\n  const maxRetries = 5 // 增加重试次数\n  const baseDelay = 1000 // 基础延迟1秒\n\n  console.log(`🔍 测试豆包API连接: ${config.model}`)\n  console.log(`🔑 API密钥前缀: ${config.apiKey.substring(0, 8)}...`)\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    const controller = new AbortController()\n    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时\n\n    try {\n      if (attempt > 1) {\n        // 指数退避算法：1s, 2s, 4s, 8s, 16s\n        const delay = baseDelay * Math.pow(2, attempt - 2)\n        console.log(`🔄 第${attempt}次重试（延迟${delay}ms）...`)\n        await new Promise(resolve => setTimeout(resolve, delay))\n      }\n\n      // 豆包模型类型检测 - 根据endpoint ID映射到正确的模型名称\n      let actualModelName = config.model\n      let isImageToVideoModel = false\n      let isTextToVideoModel = false\n      let isVideoModel = false\n      let isImageModel = false\n\n      // 豆包视频模型直接使用endpoint ID，不需要映射\n      if (config.model === 'ep-20250624195026-qjsmk') {\n        // I2V模型 - 直接使用endpoint ID\n        actualModelName = config.model\n        isImageToVideoModel = true\n        isVideoModel = true\n      } else if (config.model === 'ep-20250624192345-5ccwj') {\n        // T2V模型 - 直接使用endpoint ID\n        actualModelName = config.model\n        isTextToVideoModel = true\n        isVideoModel = true\n      } else if (config.model === 'ep-20250624192235-zttm6') {\n        // Pro模型 - 直接使用endpoint ID\n        actualModelName = config.model\n        isTextToVideoModel = true\n        isVideoModel = true\n      } else if (config.model === 'ep-20250623162000-p9zzw') {\n        // 图像生成模型\n        actualModelName = 'doubao-seedream-3-0-t2i-250415'\n        isImageModel = true\n      } else {\n        // 其他模型保持原名\n        isVideoModel = config.model.includes('seedance') && !config.model.includes('t2i')\n        isImageModel = config.model.includes('seedream') || config.model.includes('t2i')\n\n        // 如果是其他豆包模型但不是视频/图像模型，可能是聊天模型\n        // 但根据诊断，当前API密钥可能没有聊天权限，所以跳过测试\n        if (!isVideoModel && !isImageModel && config.model.startsWith('ep-')) {\n          console.log('🎯 检测到豆包endpoint但非视频/图像模型，跳过测试')\n          return { success: true }\n        }\n      }\n\n      let apiUrl: string\n      let requestBody: any\n\n      if (isVideoModel) {\n        // 视频生成API\n        apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'\n\n        if (isImageToVideoModel) {\n          // 图生视频模型测试：跳过实际API调用，直接返回成功\n          // 因为图生视频需要有效的图像URL，在测试连接时很难提供\n          console.log('🎯 检测到图生视频模型，跳过API调用测试')\n          return { success: true }\n        } else {\n          // 文生视频模型只需要文本\n          requestBody = {\n            model: actualModelName,\n            content: [\n              {\n                type: \"text\",\n                text: \"测试连接\"\n              }\n            ]\n          }\n        }\n      } else if (isImageModel) {\n        // 图像生成API\n        apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/images/generations'\n\n        requestBody = {\n          model: actualModelName,\n          prompt: \"测试连接\",\n          response_format: \"url\",\n          size: \"1024x1024\",\n          guidance_scale: 3,\n          watermark: true\n        }\n      } else {\n        // 文本生成API\n        apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'\n        requestBody = {\n          model: actualModelName,\n          messages: [\n            { role: 'user', content: '测试连接' }\n          ],\n          max_tokens: 10,\n          temperature: 0.3\n        }\n      }\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${config.apiKey}`\n        },\n        body: JSON.stringify(requestBody),\n        signal: controller.signal\n      })\n\n      clearTimeout(timeoutId)\n\n      if (response.ok) {\n        console.log(`✅ 豆包API连接测试成功（第${attempt}次尝试）`)\n        return { success: true }\n      } else {\n        const errorText = await response.text()\n        console.error(`❌ 豆包API测试失败 (${response.status}):`, errorText)\n\n        let errorData: any = {}\n        let errorMessage = ''\n\n        try {\n          errorData = JSON.parse(errorText)\n          errorMessage = errorData.error?.message || response.statusText\n        } catch (e) {\n          errorMessage = errorText || response.statusText\n        }\n\n        // 检查是否是可重试的错误\n        const isRetryableError =\n          errorMessage.includes('internal error') ||\n          errorMessage.includes('service unavailable') ||\n          errorMessage.includes('timeout') ||\n          response.status >= 500\n\n        if (isRetryableError && attempt < maxRetries) {\n          console.log(`🔄 检测到可重试错误: ${errorMessage}`)\n          continue // 继续下一次重试\n        }\n\n        // 处理特定的错误类型（不可重试）\n        if (errorMessage.includes('API key') || errorMessage.includes('invalid')) {\n          return { success: false, error: '豆包API密钥无效，请检查配置' }\n        } else if (errorMessage.includes('rate limit')) {\n          return { success: false, error: '豆包API调用频率超限，请稍后重试' }\n        } else if (errorMessage.includes('quota') || errorMessage.includes('insufficient')) {\n          return { success: false, error: '豆包API配额不足，请检查账户余额' }\n        } else if (errorMessage.includes('does not exist') || errorMessage.includes('not found')) {\n          return { success: false, error: '豆包模型不存在或无权访问，请在火山方舟控制台开通权限' }\n        } else if (errorMessage.includes('internal error')) {\n          return {\n            success: false,\n            error: '豆包服务临时不可用（内部错误），请稍后重试或使用其他AI模型'\n          }\n        } else {\n          return { success: false, error: `豆包API错误: ${errorMessage}` }\n        }\n      }\n    } catch (error) {\n      clearTimeout(timeoutId)\n      console.error(`豆包API测试异常 (尝试 ${attempt}/${maxRetries}):`, error)\n\n      if (error.name === 'AbortError') {\n        if (attempt < maxRetries) {\n          console.log('🔄 连接超时，重试中...')\n          continue\n        }\n        return { success: false, error: '豆包API连接超时，请检查网络连接' }\n      }\n\n      // 网络错误等可以重试\n      if (attempt < maxRetries) {\n        console.log(`🔄 网络错误，准备重试...`)\n        continue\n      }\n\n      return { success: false, error: `豆包连接失败: ${error}` }\n    }\n  }\n\n  return {\n    success: false,\n    error: '豆包连接失败，已达到最大重试次数。建议稍后重试或使用其他AI模型。'\n  }\n}\n\n// 测试SkyReels连接\nasync function testSkyReelsConnection(config: ModelConfig) {\n  try {\n    console.log(`🔍 测试SkyReels API连接: ${config.model}`)\n    console.log(`🔗 API服务器地址: ${config.apiKey}`)\n\n    // SkyReels的apiKey实际上是服务器地址\n    const baseUrl = config.apiKey || 'http://localhost:8000'\n\n    const response = await fetch(`${baseUrl}/health`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      // 设置较短的超时时间，因为是本地服务\n      signal: AbortSignal.timeout(10000) // 10秒超时\n    })\n\n    if (response.ok) {\n      const data = await response.json()\n      console.log('✅ SkyReels健康检查响应:', data)\n\n      // 检查模型是否已加载\n      if (data.status === 'healthy' && data.model_loaded) {\n        return { success: true }\n      } else if (data.status === 'healthy' && !data.model_loaded) {\n        return { success: false, error: 'SkyReels服务器运行正常，但模型尚未加载完成，请稍后重试' }\n      } else {\n        return { success: false, error: `SkyReels服务器状态异常: ${data.status}` }\n      }\n    } else {\n      const errorText = await response.text()\n      return { success: false, error: `SkyReels API错误 (${response.status}): ${errorText}` }\n    }\n  } catch (error) {\n    console.error('SkyReels API测试异常:', error)\n\n    if (error.name === 'TimeoutError') {\n      return { success: false, error: 'SkyReels服务器连接超时，请检查服务器是否启动并运行在正确的端口' }\n    } else if (error.message.includes('fetch')) {\n      return { success: false, error: 'SkyReels服务器连接失败，请确认服务器地址正确且服务器已启动' }\n    } else {\n      return { success: false, error: `SkyReels连接失败: ${error.message}` }\n    }\n  }\n}\n\n// POST - 测试模型连接\nexport async function POST(request: NextRequest) {\n  try {\n    const modelConfig: ModelConfig = await request.json()\n    \n    // 验证必需字段\n    if (!modelConfig.provider || !modelConfig.model || !modelConfig.apiKey) {\n      return NextResponse.json(\n        { success: false, error: '缺少必需字段' },\n        { status: 400 }\n      )\n    }\n\n    // 如果有ID，更新数据库中的测试状态\n    if (modelConfig.id) {\n      await prisma.aIConfig.update({\n        where: { id: modelConfig.id },\n        data: { status: 'testing' }\n      })\n    }\n\n    // 测试连接\n    const testResult = await testModelConnection(modelConfig)\n    \n    // 更新数据库中的状态\n    if (modelConfig.id) {\n      await prisma.aIConfig.update({\n        where: { id: modelConfig.id },\n        data: { \n          status: testResult.success ? 'connected' : 'error',\n          lastTest: new Date()\n        }\n      })\n    }\n    \n    return NextResponse.json({\n      success: testResult.success,\n      error: testResult.error\n    })\n  } catch (error) {\n    console.error('测试模型连接失败:', error)\n    \n    // 如果有ID，更新状态为错误\n    if (request.body) {\n      try {\n        const body = await request.json()\n        if (body.id) {\n          await prisma.aIConfig.update({\n            where: { id: body.id },\n            data: { status: 'error' }\n          })\n        }\n      } catch (e) {\n        // 忽略解析错误\n      }\n    }\n    \n    return NextResponse.json(\n      { success: false, error: '测试连接失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAUA,SAAS;AACT,eAAe,oBAAoB,MAAmB;IACpD,IAAI;QACF,eAAe;QACf,OAAQ,OAAO,QAAQ;YACrB,KAAK;gBACH,OAAO,MAAM,uBAAuB;YACtC,KAAK;gBACH,OAAO,MAAM,qBAAqB;YACpC,KAAK;gBACH,OAAO,MAAM,sBAAsB;YACrC,KAAK;gBACH,OAAO,MAAM,oBAAoB;YACnC,KAAK;gBACH,OAAO,MAAM,qBAAqB;YACpC,KAAK;gBACH,OAAO,MAAM,qBAAqB;YACpC,KAAK;gBACH,OAAO,MAAM,qBAAqB;YACpC,KAAK;gBACH,OAAO,MAAM,uBAAuB;YACtC;gBACE,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAY;QAChD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAS;IAC3C;AACF;AAEA,eAAe;AACf,eAAe,uBAAuB,MAAmB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,gDAAgD;YAC3E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC5C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,UAAU;oBACR;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBACjC;gBACD,YAAY;YACd;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,OAAO;YAAC;QAC7D;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,cAAc,EAAE,OAAO;QAAC;IAC3D;AACF;AAEA,SAAS;AACT,eAAe,qBAAqB,MAAmB;IACrD,IAAI;QACF,aAAa;QACb,gBAAgB;QAChB,IAAI,cAAmB;YACrB,OAAO,OAAO,KAAK;YACnB,OAAO;gBACL,UAAU;oBACR;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBACjC;YACH;YACA,YAAY;gBACV,YAAY;YACd;QACF;QAEA,8BAA8B;QAC9B,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,UAAU;YAClC,YAAY,UAAU,CAAC,aAAa,GAAG;QACzC;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,KAAK,EAAE;QAC3C,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;QAE7D,MAAM,WAAW,MAAM,MAAM,kFAAkF;YAC7G,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,mBAAmB;YACrB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,YAAY;YACZ,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW;gBACxC,IAAI,KAAK,IAAI,KAAK,aAAa;oBAC7B,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAA2B;gBAC7D,OAAO,IAAI,KAAK,IAAI,KAAK,iBAAiB;oBACxC,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAkB;gBACpD,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO,CAAC,SAAS,EAAE,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;oBAAC;gBAC1E;YACF;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE;YAEnD,IAAI,YAAiB,CAAC;YACtB,IAAI;gBACF,YAAY,KAAK,KAAK,CAAC;YACzB,EAAE,OAAO,GAAG;gBACV,6BAA6B;gBAC7B,IAAI,UAAU,QAAQ,CAAC,cAAc;oBACnC,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAA0B;gBAC5D;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO,CAAC,SAAS,EAAE,aAAa,SAAS,UAAU,EAAE;gBAAC;YACjF;YAEA,IAAI,UAAU,IAAI,KAAK,aAAa;gBAClC,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA2B;YAC7D,OAAO,IAAI,UAAU,IAAI,KAAK,iBAAiB;gBAC7C,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAkB;YACpD,OAAO,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,cAAc;gBACvE,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA0B;YAC5D,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,CAAC,SAAS,EAAE,UAAU,OAAO,IAAI,SAAS,UAAU,EAAE;gBAAC;YACzF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,QAAQ,EAAE,OAAO;QAAC;IACrD;AACF;AAEA,aAAa;AACb,eAAe,qBAAqB,MAAmB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,8CAA8C;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC5C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,UAAU;oBACR;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBACjC;gBACD,YAAY;YACd;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,cAAc,EAAE,OAAO;YAAC;QAC3D;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,YAAY,EAAE,OAAO;QAAC;IACzD;AACF;AAEA,cAAc;AACd,eAAe,sBAAsB,MAAmB;IACtD,IAAI;QACF,0BAA0B;QAC1B,MAAM,WAAW,MAAM,MAAM,gDAAgD;YAC3E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC5C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,QAAQ;gBACR,SAAS;gBACT,eAAe;oBACb,gBAAgB;oBAChB,oBAAoB;gBACtB;YACF;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,eAAe,EAAE,OAAO;YAAC;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,aAAa,EAAE,OAAO;QAAC;IAC1D;AACF;AAEA,WAAW;AACX,eAAe,oBAAoB,MAAmB;IACpD,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW,MAAM,MAAM,2DAA2D;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC5C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,GAAG;YACL;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,YAAY,EAAE,OAAO;YAAC;QACzD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,UAAU,EAAE,OAAO;QAAC;IACvD;AACF;AAEA,aAAa;AACb,eAAe,qBAAqB,MAAmB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,aAAa,OAAO,MAAM;gBAC1B,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,YAAY;gBACZ,UAAU;oBACR;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBACjC;YACH;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBAAE,SAAS;YAAK;QACzB,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,cAAc,EAAE,OAAO;YAAC;QAC3D;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,YAAY,EAAE,OAAO;QAAC;IACzD;AACF;AAEA,oBAAoB;AACpB,eAAe,qBAAqB,MAAmB;IACrD,MAAM,aAAa,EAAE,SAAS;;IAC9B,MAAM,YAAY,KAAK,SAAS;;IAEhC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,KAAK,EAAE;IAC3C,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;IAE7D,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,OAAO,QAAQ;;QAEtE,IAAI;YACF,IAAI,UAAU,GAAG;gBACf,6BAA6B;gBAC7B,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU;gBAChD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,MAAM,EAAE,MAAM,MAAM,CAAC;gBAChD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,qCAAqC;YACrC,IAAI,kBAAkB,OAAO,KAAK;YAClC,IAAI,sBAAsB;YAC1B,IAAI,qBAAqB;YACzB,IAAI,eAAe;YACnB,IAAI,eAAe;YAEnB,8BAA8B;YAC9B,IAAI,OAAO,KAAK,KAAK,2BAA2B;gBAC9C,0BAA0B;gBAC1B,kBAAkB,OAAO,KAAK;gBAC9B,sBAAsB;gBACtB,eAAe;YACjB,OAAO,IAAI,OAAO,KAAK,KAAK,2BAA2B;gBACrD,0BAA0B;gBAC1B,kBAAkB,OAAO,KAAK;gBAC9B,qBAAqB;gBACrB,eAAe;YACjB,OAAO,IAAI,OAAO,KAAK,KAAK,2BAA2B;gBACrD,0BAA0B;gBAC1B,kBAAkB,OAAO,KAAK;gBAC9B,qBAAqB;gBACrB,eAAe;YACjB,OAAO,IAAI,OAAO,KAAK,KAAK,2BAA2B;gBACrD,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB,OAAO;gBACL,WAAW;gBACX,eAAe,OAAO,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,KAAK,CAAC,QAAQ,CAAC;gBAC3E,eAAe,OAAO,KAAK,CAAC,QAAQ,CAAC,eAAe,OAAO,KAAK,CAAC,QAAQ,CAAC;gBAE1E,8BAA8B;gBAC9B,+BAA+B;gBAC/B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO,KAAK,CAAC,UAAU,CAAC,QAAQ;oBACpE,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBAAE,SAAS;oBAAK;gBACzB;YACF;YAEA,IAAI;YACJ,IAAI;YAEJ,IAAI,cAAc;gBAChB,UAAU;gBACV,SAAS;gBAET,IAAI,qBAAqB;oBACvB,4BAA4B;oBAC5B,8BAA8B;oBAC9B,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,cAAc;oBACd,cAAc;wBACZ,OAAO;wBACP,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM;4BACR;yBACD;oBACH;gBACF;YACF,OAAO,IAAI,cAAc;gBACvB,UAAU;gBACV,SAAS;gBAET,cAAc;oBACZ,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,MAAM;oBACN,gBAAgB;oBAChB,WAAW;gBACb;YACF,OAAO;gBACL,UAAU;gBACV,SAAS;gBACT,cAAc;oBACZ,OAAO;oBACP,UAAU;wBACR;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBACjC;oBACD,YAAY;oBACZ,aAAa;gBACf;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC5C;gBACA,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,IAAI,CAAC;gBAC3C,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE;gBAEnD,IAAI,YAAiB,CAAC;gBACtB,IAAI,eAAe;gBAEnB,IAAI;oBACF,YAAY,KAAK,KAAK,CAAC;oBACvB,eAAe,UAAU,KAAK,EAAE,WAAW,SAAS,UAAU;gBAChE,EAAE,OAAO,GAAG;oBACV,eAAe,aAAa,SAAS,UAAU;gBACjD;gBAEA,cAAc;gBACd,MAAM,mBACJ,aAAa,QAAQ,CAAC,qBACtB,aAAa,QAAQ,CAAC,0BACtB,aAAa,QAAQ,CAAC,cACtB,SAAS,MAAM,IAAI;gBAErB,IAAI,oBAAoB,UAAU,YAAY;oBAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;oBAC1C,UAAS,UAAU;gBACrB;gBAEA,kBAAkB;gBAClB,IAAI,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,YAAY;oBACxE,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAkB;gBACpD,OAAO,IAAI,aAAa,QAAQ,CAAC,eAAe;oBAC9C,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAoB;gBACtD,OAAO,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,iBAAiB;oBAClF,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAoB;gBACtD,OAAO,IAAI,aAAa,QAAQ,CAAC,qBAAqB,aAAa,QAAQ,CAAC,cAAc;oBACxF,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAA6B;gBAC/D,OAAO,IAAI,aAAa,QAAQ,CAAC,mBAAmB;oBAClD,OAAO;wBACL,SAAS;wBACT,OAAO;oBACT;gBACF,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO,CAAC,SAAS,EAAE,cAAc;oBAAC;gBAC7D;YACF;QACF,EAAE,OAAO,OAAO;YACd,aAAa;YACb,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;YAE1D,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,IAAI,UAAU,YAAY;oBACxB,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBACA,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAoB;YACtD;YAEA,YAAY;YACZ,IAAI,UAAU,YAAY;gBACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC;gBAC7B;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,QAAQ,EAAE,OAAO;YAAC;QACrD;IACF;IAEA,OAAO;QACL,SAAS;QACT,OAAO;IACT;AACF;AAEA,eAAe;AACf,eAAe,uBAAuB,MAAmB;IACvD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,KAAK,EAAE;QAClD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,MAAM,EAAE;QAE3C,2BAA2B;QAC3B,MAAM,UAAU,OAAO,MAAM,IAAI;QAEjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,OAAO,CAAC,EAAE;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,oBAAoB;YACpB,QAAQ,YAAY,OAAO,CAAC,OAAO,QAAQ;QAC7C;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,YAAY;YACZ,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,YAAY,EAAE;gBAClD,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO,IAAI,KAAK,MAAM,KAAK,aAAa,CAAC,KAAK,YAAY,EAAE;gBAC1D,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAkC;YACpE,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,CAAC,iBAAiB,EAAE,KAAK,MAAM,EAAE;gBAAC;YACpE;QACF,OAAO;YACL,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YAAC;QACtF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;YAAC;QACnE;IACF;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAA2B,MAAM,QAAQ,IAAI;QAEnD,SAAS;QACT,IAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,MAAM,EAAE;YACtE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,IAAI,YAAY,EAAE,EAAE;YAClB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,IAAI,YAAY,EAAE;gBAAC;gBAC5B,MAAM;oBAAE,QAAQ;gBAAU;YAC5B;QACF;QAEA,OAAO;QACP,MAAM,aAAa,MAAM,oBAAoB;QAE7C,YAAY;QACZ,IAAI,YAAY,EAAE,EAAE;YAClB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,IAAI,YAAY,EAAE;gBAAC;gBAC5B,MAAM;oBACJ,QAAQ,WAAW,OAAO,GAAG,cAAc;oBAC3C,UAAU,IAAI;gBAChB;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAE3B,gBAAgB;QAChB,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI;gBACF,MAAM,OAAO,MAAM,QAAQ,IAAI;gBAC/B,IAAI,KAAK,EAAE,EAAE;oBACX,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC3B,OAAO;4BAAE,IAAI,KAAK,EAAE;wBAAC;wBACrB,MAAM;4BAAE,QAAQ;wBAAQ;oBAC1B;gBACF;YACF,EAAE,OAAO,GAAG;YACV,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}