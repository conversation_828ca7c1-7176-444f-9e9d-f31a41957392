const { PrismaClient } = require('@prisma/client');

async function setupVideoConfig() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 设置视频生成配置...');
    
    // 1. 检查是否已有配置
    const existingConfigs = await prisma.aIConfig.findMany();
    if (existingConfigs.length > 0) {
      console.log('⚠️ 已有配置存在，跳过创建');
      console.log('现有配置:');
      existingConfigs.forEach(config => {
        console.log(`- ${config.name}: 支持视频=${config.supportsVideo}`);
      });
      return;
    }

    // 2. 添加豆包视频生成模型
    const doubaoVideo = await prisma.aIConfig.create({
      data: {
        provider: 'doubao',
        apiKey: 'your-doubao-api-key', // 请替换为真实API密钥
        model: 'ep-20250622184757-q77k7',
        name: '豆包 Seedance 1.0 Pro (视频生成)',
        description: '豆包视频生成模型，支持文本到视频转换',
        enabled: true,
        supportsVideo: true, // 关键：标记为支持视频
        temperature: 0.7,
        maxTokens: 2000,
        topP: 0.9,
        status: 'connected'
      }
    });
    console.log('✅ 添加豆包视频生成模型');
    
    // 验证配置
    const allConfigs = await prisma.aIConfig.findMany();
    console.log('\n📋 当前所有AI配置:');
    allConfigs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.name}`);
      console.log(`   提供商: ${config.provider}`);
      console.log(`   模型: ${config.model}`);
      console.log(`   支持视频: ${config.supportsVideo ? '✅ 是' : '❌ 否'}`);
      console.log(`   状态: ${config.status}`);
      console.log('');
    });
    
    const videoConfigs = allConfigs.filter(c => c.supportsVideo);
    console.log(`🎬 支持视频生成的配置数量: ${videoConfigs.length}`);
    
    if (videoConfigs.length > 0) {
      console.log('\n✅ 视频配置设置成功！');
      console.log('\n📝 下一步：');
      console.log('1. 前往 AI配置 页面');
      console.log('2. 更新API密钥为真实的密钥');
      console.log('3. 测试连接确保配置正确');
      console.log('4. 然后就可以生成视频了！');
    }
    
  } catch (error) {
    console.error('❌ 设置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupVideoConfig();
