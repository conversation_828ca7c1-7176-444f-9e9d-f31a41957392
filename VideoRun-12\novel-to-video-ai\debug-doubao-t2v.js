// 彻底诊断豆包 Seedance 1.0 Lite T2V 生成失败的原因
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugDoubaoT2V() {
  try {
    console.log('🔍 开始彻底诊断豆包 Seedance 1.0 Lite T2V 问题...\n');

    // 1. 获取豆包T2V配置
    console.log('1. 检查豆包T2V配置...');
    const doubaoT2V = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj'
      }
    });

    if (!doubaoT2V) {
      console.log('❌ 未找到豆包T2V配置');
      return;
    }

    console.log('✅ 豆包T2V配置:');
    console.log('  ID:', doubaoT2V.id);
    console.log('  名称:', doubaoT2V.name);
    console.log('  模型:', doubaoT2V.model);
    console.log('  状态:', doubaoT2V.status);
    console.log('  API密钥:', doubaoT2V.apiKey ? '已配置' : '未配置');

    // 2. 测试豆包API基础连接
    console.log('\n2. 测试豆包API基础连接...');
    try {
      const testResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${doubaoT2V.apiKey}`
        },
        body: JSON.stringify({
          model: doubaoT2V.model,
          content: [
            {
              type: "text",
              text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
            }
          ]
        })
      });

      console.log('API响应状态:', testResponse.status);
      console.log('API响应头:', Object.fromEntries(testResponse.headers.entries()));

      if (testResponse.ok) {
        const data = await testResponse.json();
        console.log('✅ API响应成功:', data);
      } else {
        const errorText = await testResponse.text();
        console.log('❌ API响应失败:', errorText);
      }
    } catch (error) {
      console.log('❌ API连接异常:', error.message);
    }

    // 3. 检查最近的豆包T2V失败记录
    console.log('\n3. 检查最近的豆包T2V失败记录...');
    const failedSegments = await prisma.videoSegment.findMany({
      where: {
        status: 'failed'
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: 5
    });

    console.log(`找到 ${failedSegments.length} 个失败的片段:`);
    failedSegments.forEach((segment, index) => {
      console.log(`\n失败片段 ${index + 1}:`);
      console.log('  片段索引:', segment.segmentIndex);
      console.log('  状态:', segment.status);
      console.log('  创建时间:', segment.createdAt);
      console.log('  更新时间:', segment.updatedAt);
      
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log('  提供商:', metadata.provider || '未知');
          console.log('  模型:', metadata.model || '未知');
          console.log('  错误信息:', metadata.error || '无');
          console.log('  完整元数据:', metadata);
        } catch (e) {
          console.log('  原始元数据:', segment.metadata);
        }
      }
    });

    // 4. 模拟完整的豆包T2V调用流程
    console.log('\n4. 模拟完整的豆包T2V调用流程...');
    
    // 创建测试提示词
    const testPrompt = "一个美丽的花园，阳光明媚，微风轻拂，花朵摇摆 --ratio 16:9 --fps 24 --dur 5 --resolution 480p";
    
    console.log('测试提示词:', testPrompt);
    
    try {
      // 步骤1: 创建生成任务
      console.log('\n步骤1: 创建生成任务...');
      const createResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${doubaoT2V.apiKey}`
        },
        body: JSON.stringify({
          model: doubaoT2V.model,
          content: [
            {
              type: "text",
              text: testPrompt
            }
          ]
        })
      });

      console.log('创建任务响应状态:', createResponse.status);
      
      if (!createResponse.ok) {
        const errorText = await createResponse.text();
        console.log('❌ 创建任务失败:', errorText);
        return;
      }

      const createData = await createResponse.json();
      console.log('✅ 任务创建成功:', createData);
      
      const taskId = createData.id;
      if (!taskId) {
        console.log('❌ 未获取到任务ID');
        return;
      }

      // 步骤2: 轮询任务状态
      console.log('\n步骤2: 轮询任务状态...');
      let attempts = 0;
      const maxAttempts = 10;
      
      while (attempts < maxAttempts) {
        attempts++;
        console.log(`\n轮询第 ${attempts} 次...`);
        
        try {
          const statusResponse = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${doubaoT2V.apiKey}`
            }
          });

          console.log('状态查询响应:', statusResponse.status);
          
          if (!statusResponse.ok) {
            const errorText = await statusResponse.text();
            console.log('❌ 状态查询失败:', errorText);
            break;
          }

          const statusData = await statusResponse.json();
          console.log('任务状态:', statusData.status);
          console.log('完整状态数据:', statusData);

          if (statusData.status === 'success') {
            console.log('🎉 任务完成成功!');
            console.log('结果数据:', statusData.result);
            break;
          } else if (statusData.status === 'failed') {
            console.log('❌ 任务执行失败');
            console.log('失败原因:', statusData.error || statusData.message || '未知');
            break;
          } else if (statusData.status === 'processing' || statusData.status === 'pending') {
            console.log('⏳ 任务处理中，等待5秒后重试...');
            await new Promise(resolve => setTimeout(resolve, 5000));
          } else {
            console.log('❓ 未知状态:', statusData.status);
            break;
          }
        } catch (error) {
          console.log('❌ 状态查询异常:', error.message);
          break;
        }
      }

      if (attempts >= maxAttempts) {
        console.log('⏰ 轮询超时，任务可能仍在处理中');
      }

    } catch (error) {
      console.log('❌ 模拟调用流程异常:', error.message);
      console.log('错误堆栈:', error.stack);
    }

    // 5. 检查VideoRun中的豆包T2V调用代码
    console.log('\n5. 检查VideoRun中的豆包T2V调用逻辑...');
    
    // 模拟VideoRun中的调用
    try {
      console.log('模拟VideoRun API调用...');
      
      // 创建一个测试片段数据
      const testSegment = {
        id: 'test-segment',
        segmentIndex: 1,
        content: '测试内容',
        duration: 5
      };

      // 模拟调用VideoRun的生成API
      const videoGenResponse = await fetch('http://localhost:3000/api/ai/generate-story-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId: 'test-episode',
          segments: [testSegment],
          videoModelId: doubaoT2V.id,
          prompt: testPrompt
        })
      });

      console.log('VideoRun API响应状态:', videoGenResponse.status);
      
      if (videoGenResponse.ok) {
        const videoGenData = await videoGenResponse.json();
        console.log('✅ VideoRun API响应:', videoGenData);
      } else {
        const errorText = await videoGenResponse.text();
        console.log('❌ VideoRun API失败:', errorText);
      }

    } catch (error) {
      console.log('❌ VideoRun API调用异常:', error.message);
    }

    console.log('\n✅ 豆包T2V诊断完成!');

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行诊断
debugDoubaoT2V();
