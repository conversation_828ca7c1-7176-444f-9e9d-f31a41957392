'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, FileText } from 'lucide-react'
import { FileParser, FileParseError } from '@/lib/fileParser'

interface FileUploadProps {
  onUpload: (file: File, content: string) => Promise<void>
  isUploading: boolean
}

export default function FileUpload({ onUpload, isUploading }: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fileContent, setFileContent] = useState<string>('')
  const [error, setError] = useState<string | null>(null)
  const [isReading, setIsReading] = useState(false)

  // 文件解析函数
  const parseFileContent = useCallback(async (file: File): Promise<string> => {
    try {
      return await FileParser.parseFile(file)
    } catch (error) {
      if (error instanceof FileParseError) {
        throw new Error(error.message)
      }
      throw new Error('文件解析失败')
    }
  }, [])

  // 文件拖拽处理
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    setError(null)
    setIsReading(true)

    try {
      // 解析文件内容（包含格式验证）
      const content = await parseFileContent(file)

      setSelectedFile(file)
      setFileContent(content)
    } catch (error) {
      setError(error instanceof Error ? error.message : '文件处理失败')
      setSelectedFile(null)
      setFileContent('')
    } finally {
      setIsReading(false)
    }
  }, [parseFileContent])

  // 配置 dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
    },
    multiple: false,
    disabled: isUploading || isReading,
  })

  // 处理上传
  const handleUpload = async () => {
    if (!selectedFile || !fileContent) return

    try {
      setError(null)
      await onUpload(selectedFile, fileContent)
    } catch (error) {
      setError(error instanceof Error ? error.message : '上传失败')
    }
  }

  // 清除选择的文件
  const clearFile = () => {
    setSelectedFile(null)
    setFileContent('')
    setError(null)
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <Upload className="mr-2" size={20} />
        上传小说文件
      </h2>

      {!selectedFile ? (
        // 文件拖拽区域
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${isDragActive 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
            }
            ${(isUploading || isReading) ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center">
            <Upload 
              size={48} 
              className={`mb-4 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} 
            />
            
            {isReading ? (
              <p className="text-gray-600">正在读取文件...</p>
            ) : isDragActive ? (
              <p className="text-blue-600">松开鼠标上传文件</p>
            ) : (
              <>
                <p className="text-gray-600 mb-2">
                  拖拽文件到此处，或点击选择文件
                </p>
                <p className="text-sm text-gray-500">
                  支持 .txt、.docx 格式，最大 50MB
                </p>
              </>
            )}
          </div>
        </div>
      ) : (
        // 文件预览区域
        <div className="space-y-4">
          {/* 文件信息 */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <FileText className="mr-3 text-blue-500" size={20} />
              <div>
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {FileParser.getFileTypeDescription(selectedFile)} • {FileParser.formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            
            <button
              onClick={clearFile}
              disabled={isUploading}
              className="p-2 text-gray-500 hover:text-red-500 disabled:opacity-50"
            >
              <X size={20} />
            </button>
          </div>

          {/* 内容预览 */}
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">内容预览</h3>
            <div className="bg-gray-50 rounded p-3 max-h-40 overflow-y-auto">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {fileContent.substring(0, 500)}
                {fileContent.length > 500 && '...'}
              </pre>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              总字数: {fileContent.length.toLocaleString()} 字
            </p>
          </div>

          {/* 上传按钮 */}
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              disabled={isUploading}
              className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUploading ? '上传中...' : '确认上传'}
            </button>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
    </div>
  )
}
