module.exports = {

"[project]/.next-internal/server/app/api/ai/analyze-detailed-plot/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/skyreels.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SkyReelsClient": (()=>SkyReelsClient),
    "SkyReelsServiceError": (()=>SkyReelsServiceError)
});
class SkyReelsClient {
    baseUrl;
    apiKey;
    model;
    constructor(config){
        // SkyReels是本地API，apiKey用作baseUrl
        this.baseUrl = config.apiKey || 'http://localhost:8000';
        this.apiKey = config.apiKey;
        this.model = config.model || 'SkyReels-V2-DF-1.3B-540P';
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            return data.status === 'healthy' && data.model_loaded;
        } catch (error) {
            console.error('SkyReels连接测试失败:', error);
            return false;
        }
    }
    // 生成视频
    async generateVideo(params) {
        try {
            const response = await fetch(`${this.baseUrl}/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: params.prompt,
                    num_frames: params.num_frames || 257,
                    guidance_scale: params.guidance_scale || 6.0,
                    seed: params.seed,
                    fps: params.fps || 24,
                    resolution: params.resolution || '540P'
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels视频生成失败:', error);
            throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 查询任务状态
    async getTaskStatus(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/status/${taskId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels状态查询失败:', error);
            throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 下载视频
    async downloadVideo(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/download/${taskId}`, {
                method: 'GET'
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            return await response.blob();
        } catch (error) {
            console.error('SkyReels视频下载失败:', error);
            throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 等待任务完成并返回视频URL
    async generateAndWait(params, maxWaitTime = 1800000, pollInterval = 5000 // 5秒
    ) {
        console.log('🎬 开始SkyReels视频生成...');
        // 开始生成
        const task = await this.generateVideo(params);
        console.log(`📝 任务已创建: ${task.task_id}`);
        const startTime = Date.now();
        // 轮询状态直到完成
        while(Date.now() - startTime < maxWaitTime){
            const status = await this.getTaskStatus(task.task_id);
            console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`);
            if (status.status === 'completed') {
                console.log('✅ 视频生成完成!');
                return status.video_path || '';
            } else if (status.status === 'failed') {
                throw new Error(`视频生成失败: ${status.error || '未知错误'}`);
            }
            // 等待下次轮询
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        }
        throw new Error('视频生成超时');
    }
    // 获取所有任务列表
    async getTasks() {
        try {
            const response = await fetch(`${this.baseUrl}/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data.tasks || [];
        } catch (error) {
            console.error('SkyReels任务列表获取失败:', error);
            throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
class SkyReelsServiceError extends Error {
    statusCode;
    constructor(message, statusCode){
        super(message), this.statusCode = statusCode;
        this.name = 'SkyReelsServiceError';
    }
}
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIServiceError": (()=>AIServiceError),
    "DeepSeekClient": (()=>DeepSeekClient),
    "DoubaoClient": (()=>DoubaoClient),
    "createAIClient": (()=>createAIClient),
    "handleAIRequest": (()=>handleAIRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/skyreels.ts [app-route] (ecmascript)");
;
class AIServiceError extends Error {
    code;
    statusCode;
    constructor(message, code, statusCode = 500){
        super(message), this.code = code, this.statusCode = statusCode;
        this.name = 'AIServiceError';
    }
}
class DeepSeekClient {
    apiKey;
    model;
    baseUrl = 'https://api.deepseek.com/v1/chat/completions';
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model;
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: '测试连接'
                        }
                    ],
                    max_tokens: 10
                })
            });
            return response.ok;
        } catch (error) {
            console.error('DeepSeek连接测试失败:', error);
            return false;
        }
    }
    // 调用AI API的通用方法（公开方法）
    async callAPI(prompt, maxTokens = 4000) {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: maxTokens,
                    temperature: 0.7
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new AIServiceError(errorData.error?.message || 'API调用失败', 'API_ERROR', response.status);
            }
            const data = await response.json();
            return data.choices[0]?.message?.content || '';
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('AI服务调用失败，请检查网络连接', 'NETWORK_ERROR', 500);
        }
    }
    // 生成测试响应
    generateTestResponse(prompt) {
        if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {
            // 角色一致性分析的测试响应
            return `{
        "characters": [
          {
            "name": "张小雅",
            "identity": "高中生",
            "appearance": "马尾辫，甜美笑容，青春活泼",
            "personality": "开朗活泼，善良纯真",
            "clothing": "校服或休闲装",
            "role": "主要角色",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "zhang_xiaoya",
              "consistencyMatch": 0.95,
              "differences": [],
              "consistencyConstraints": "保持马尾辫和甜美笑容的标志性特征"
            }
          },
          {
            "name": "王美丽",
            "identity": "咖啡店老板",
            "appearance": "瓜子脸，波浪卷发，温柔气质",
            "personality": "温柔善良，热情好客",
            "clothing": "简约优雅的服装",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "wang_meili",
              "consistencyMatch": 0.88,
              "differences": [],
              "consistencyConstraints": "保持瓜子脸和波浪卷发的特征"
            }
          },
          {
            "name": "李明轩",
            "identity": "大学教授",
            "appearance": "方脸，花白短发，学者气质",
            "personality": "温文尔雅，博学睿智",
            "clothing": "正式的学者装扮",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "li_mingxuan",
              "consistencyMatch": 0.92,
              "differences": [],
              "consistencyConstraints": "保持方脸和花白短发的学者形象"
            }
          },
          {
            "name": "林志强",
            "identity": "程序员",
            "appearance": "高瘦身材，黑框眼镜，简约穿着",
            "personality": "内向专注，技术宅",
            "clothing": "白色T恤，牛仔裤",
            "role": "新角色",
            "isKnownCharacter": false,
            "consistencyInfo": {
              "matchedCharacterId": null,
              "consistencyMatch": 0.0,
              "differences": ["新出现的角色"],
              "consistencyConstraints": "建立新的角色DNA档案"
            }
          }
        ]
      }`;
        } else if (prompt.includes('场景信息')) {
            return `{
        "scenes": [
          {
            "location": "温馨咖啡店",
            "description": "装修温馨的小型咖啡店，木质桌椅，暖色调灯光",
            "atmosphere": "温馨舒适，充满生活气息",
            "timeOfDay": "下午",
            "lighting": "暖色调室内灯光",
            "keyElements": "咖啡香味，轻柔音乐，温馨装饰"
          }
        ]
      }`;
        } else if (prompt.includes('情节序列')) {
            return `{
        "plotSequences": [
          {
            "sequenceId": "reunion_1",
            "action": "张小雅进入咖啡店与王美丽重逢",
            "emotion": "惊喜和温暖",
            "duration": "2分钟",
            "keyMoments": ["进门", "相认", "拥抱"],
            "visualElements": "特写表情变化，温馨的重逢场面"
          },
          {
            "sequenceId": "professor_arrival",
            "action": "李明轩教授进入咖啡店",
            "emotion": "温和友善",
            "duration": "1分钟",
            "keyMoments": ["进门", "打招呼"],
            "visualElements": "学者风度，温和笑容"
          },
          {
            "sequenceId": "new_character",
            "action": "林志强询问WiFi",
            "emotion": "略显紧张的初次见面",
            "duration": "1分钟",
            "keyMoments": ["进门", "询问", "介绍"],
            "visualElements": "新角色登场，技术宅形象"
          }
        ]
      }`;
        } else {
            return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽';
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析AI响应失败:', error);
            throw new AIServiceError('AI响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析剧情分析响应失败:', error);
            throw new AIServiceError('剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
class DoubaoClient {
    apiKey;
    model;
    baseUrl;
    isVideoModel;
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model || 'doubao-seedance-1.0-pro';
        // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID
        this.isVideoModel = this.model.includes('seedance') || this.model.includes('video') || this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式
        ;
        // 根据模型类型选择正确的API端点
        if (this.isVideoModel) {
            // 豆包视频生成使用专门的视频生成API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks';
        } else {
            // 文本模型使用chat completions API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        }
        if (config.baseUrl) {
            this.baseUrl = config.baseUrl;
        }
    }
    // 测试API连接（带重试机制）
    async testConnection() {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: '测试连接'
                            }
                        ],
                        max_tokens: 10
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    return true;
                }
                // 检查是否是可重试的错误
                const errorText = await response.text();
                if (errorText.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            } catch (error) {
                console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error);
                if (attempt < maxRetries) {
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            }
        }
        return false;
    }
    // 调用AI API的通用方法（带重试机制）
    async callAPI(prompt, maxTokens = 4000) {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: maxTokens,
                        temperature: 0.7
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    const data = await response.json();
                    if (this.isVideoModel) {
                        // 视频生成返回任务信息
                        return JSON.stringify({
                            task_id: data.task_id,
                            status: data.status || 'submitted',
                            message: '视频生成任务已提交，请稍后查询结果'
                        });
                    } else {
                        // 文本生成返回内容
                        return data.choices[0]?.message?.content || '';
                    }
                }
                const errorData = await response.json();
                const errorMessage = errorData.error?.message || '豆包API调用失败';
                // 检查是否是可重试的内部错误
                if (errorMessage.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                // 不可重试的错误，直接抛出
                throw new AIServiceError(errorMessage, 'API_ERROR', response.status);
            } catch (error) {
                if (error instanceof AIServiceError) {
                    // 如果是已知的API错误且不可重试，直接抛出
                    throw error;
                }
                // 网络错误等，可以重试
                if (attempt < maxRetries) {
                    console.log(`豆包API调用失败，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                throw new AIServiceError('豆包服务调用失败，请检查网络连接和API密钥', 'NETWORK_ERROR', 500);
            }
        }
        throw new AIServiceError('豆包服务调用失败，已达到最大重试次数', 'MAX_RETRIES_EXCEEDED', 500);
    }
    // 专门的视频生成方法
    async generateVideo(prompt, duration = 5) {
        if (!this.isVideoModel) {
            throw new Error('此模型不支持视频生成');
        }
        try {
            const requestBody = {
                model: this.model,
                prompt: prompt,
                video_setting: {
                    video_duration: duration,
                    video_aspect_ratio: '16:9',
                    video_resolution: '720p'
                }
            };
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            if (response.ok) {
                const data = await response.json();
                return JSON.stringify({
                    task_id: data.task_id,
                    status: data.status || 'submitted',
                    message: '视频生成任务已提交，请稍后查询结果'
                });
            }
            const errorData = await response.json();
            throw new AIServiceError(errorData.error?.message || '视频生成失败', 'VIDEO_GENERATION_ERROR', response.status);
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('视频生成服务调用失败', 'NETWORK_ERROR', 500);
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析豆包响应失败:', error);
            throw new AIServiceError('豆包响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析豆包剧情分析响应失败:', error);
            throw new AIServiceError('豆包剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
function createAIClient(config) {
    switch(config.provider){
        case 'deepseek':
            return new DeepSeekClient(config);
        case 'doubao':
            return new DoubaoClient(config);
        case 'skyreels':
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SkyReelsClient"](config);
        default:
            // 默认使用DeepSeek客户端，但可以扩展支持其他提供商
            return new DeepSeekClient(config);
    }
}
async function handleAIRequest(request) {
    try {
        return await request();
    } catch (error) {
        if (error instanceof AIServiceError) {
            throw error;
        }
        // 网络错误
        if (error instanceof TypeError && error.message.includes('fetch')) {
            throw new AIServiceError('AI服务连接失败，请检查网络连接', 'CONNECTION_ERROR', 503);
        }
        // 通用错误
        throw new AIServiceError('AI服务处理失败，请重试', 'UNKNOWN_ERROR', 500);
    }
}
}}),
"[project]/src/utils/storyVideoPromptGenerator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 小说剧情视频提示词生成器
// 专门用于生成保持角色和场景一致性的视频提示词
__turbopack_context__.s({
    "createCharacterDNA": (()=>createCharacterDNA),
    "createSceneDNA": (()=>createSceneDNA),
    "generateConsistencyCheckPrompt": (()=>generateConsistencyCheckPrompt),
    "generateStoryVideoPrompt": (()=>generateStoryVideoPrompt)
});
// 角色DNA数据库
const CHARACTER_DNA_DATABASE = {
    'lin_xiaoyu': {
        id: 'lin_xiaoyu',
        name: '林晓雨',
        coreFeatures: {
            age: '25 years old',
            gender: 'Asian woman',
            profession: 'architect',
            faceShape: 'oval delicate face',
            eyes: 'large intelligent brown eyes',
            hair: 'long straight black hair',
            build: 'tall and slender build',
            signatureStyle: 'professional business attire'
        },
        consistentElements: {
            facialStructure: 'same facial bone structure',
            eyeCharacteristics: 'same eye shape and color',
            hairFeatures: 'consistent hair texture and length',
            bodyProportions: 'identical body proportions'
        }
    }
};
// 场景DNA数据库
const SCENE_DNA_DATABASE = {
    'modern_office': {
        id: 'modern_office',
        name: '现代办公室',
        baseDescription: {
            type: 'modern office interior',
            layout: 'open floor plan with glass partitions',
            furniture: 'white desks, ergonomic chairs, large windows',
            lighting: 'natural daylight with warm LED supplements',
            colorScheme: 'white, gray, and blue tones',
            atmosphere: 'professional and clean'
        },
        fixedElements: {
            architecture: 'floor-to-ceiling windows on east wall',
            furnitureLayout: 'desks arranged in rows facing windows',
            lightingSetup: 'consistent overhead LED grid',
            colorPalette: 'same neutral color scheme'
        },
        timeStates: {
            morning: {
                lighting: 'soft golden sunlight streaming through windows',
                activityLevel: 'quiet, few people arriving',
                atmosphere: 'fresh and energetic'
            },
            midday: {
                lighting: 'bright natural light, LED supplements on',
                activityLevel: 'busy, people working at desks',
                atmosphere: 'focused and productive'
            },
            evening: {
                lighting: 'warm LED lighting, sunset glow',
                activityLevel: 'some people leaving, quieter',
                atmosphere: 'calm and reflective'
            }
        }
    }
};
function generateStoryVideoPrompt(options) {
    const { characters, scene, timeOfDay, plotSequences, emotionalArc, customEnhancement, style = 'cinematic', quality = 'high', consistencyMode = 'balanced', referenceImages = {}, consistencyWeights = {
        textWeight: 0.7,
        imageWeight: 0.3
    } } = options;
    const promptSections = [];
    // 1. 一致性约束声明（新增）
    promptSections.push(buildConsistencyDeclarationSection(consistencyMode, consistencyWeights));
    // 2. 故事场景描述
    promptSections.push(buildStorySceneSection(plotSequences));
    // 3. 角色一致性描述（增强版）
    promptSections.push(buildEnhancedCharacterConsistencySection(characters, referenceImages, consistencyMode));
    // 4. 场景一致性描述
    promptSections.push(buildSceneConsistencySection(scene, timeOfDay));
    // 5. 动作序列描述
    promptSections.push(buildActionSequenceSection(plotSequences, emotionalArc));
    // 6. 叙事元素描述
    promptSections.push(buildNarrativeElementsSection(plotSequences, emotionalArc));
    // 7. 技术要求描述（增强版）
    promptSections.push(buildEnhancedTechnicalRequirementsSection(style, quality, consistencyMode));
    // 8. 一致性验证要求（新增）
    promptSections.push(buildConsistencyValidationSection(characters, consistencyMode));
    // 9. 自定义增强
    if (customEnhancement && customEnhancement.trim()) {
        promptSections.push(`CUSTOM ENHANCEMENT: ${customEnhancement}`);
    }
    return promptSections.join('\n\n');
}
/**
 * 构建故事场景部分
 */ function buildStorySceneSection(plotSequences) {
    const mainAction = plotSequences.map((seq)=>seq.action).join(', then ');
    return `STORY SCENE: ${mainAction}`;
}
/**
 * 构建角色一致性部分
 */ function buildCharacterConsistencySection(characters) {
    const characterDescriptions = characters.map((char)=>{
        const { coreFeatures, consistentElements } = char;
        return `${char.name}, same ${coreFeatures.age} ${coreFeatures.gender} ${coreFeatures.profession} as previous scenes,
${coreFeatures.faceShape}, ${coreFeatures.eyes},
${coreFeatures.hair}, ${coreFeatures.build},
${consistentElements.facialStructure},
${consistentElements.eyeCharacteristics},
maintaining identical facial features and professional demeanor`;
    });
    return `CHARACTER CONSISTENCY:\n${characterDescriptions.join('\n\n')}`;
}
/**
 * 构建场景一致性部分
 */ function buildSceneConsistencySection(scene, timeOfDay) {
    const { baseDescription, fixedElements, timeStates } = scene;
    const timeState = timeStates[timeOfDay] || timeStates.midday;
    return `SCENE CONSISTENCY:
Same ${baseDescription.type} as established,
${fixedElements.architecture},
${fixedElements.furnitureLayout},
maintaining identical ${fixedElements.lightingSetup},
${fixedElements.colorPalette},
${timeState.lighting},
${timeState.atmosphere},
consistent office environment design`;
}
/**
 * 构建动作序列部分
 */ function buildActionSequenceSection(plotSequences, emotionalArc) {
    const actionDescriptions = plotSequences.map((seq, index)=>{
        const timing = index === 0 ? 'starts' : index === plotSequences.length - 1 ? 'ends' : 'then';
        return `${timing} ${seq.action} with ${seq.emotion}`;
    });
    return `ACTION SEQUENCE:
${actionDescriptions.join(',\n')},
smooth emotional progression: ${emotionalArc}`;
}
/**
 * 构建叙事元素部分
 */ function buildNarrativeElementsSection(plotSequences, emotionalArc) {
    const keyMoments = plotSequences.flatMap((seq)=>seq.keyMoments).join(', ');
    return `NARRATIVE ELEMENTS:
capturing ${keyMoments},
showing the journey through ${emotionalArc},
emphasizing professional expertise and emotional depth,
visual storytelling of character development`;
}
/**
 * 构建技术要求部分
 */ function buildTechnicalRequirementsSection(style, quality) {
    const styleDescriptions = {
        cinematic: 'cinematic quality, professional cinematography',
        documentary: 'documentary style, natural lighting',
        artistic: 'artistic style, creative camera work'
    };
    const qualityDescriptions = {
        standard: 'good quality, stable footage',
        high: 'high quality, 4K resolution, sharp focus',
        cinematic: 'cinematic quality, professional grade, ultra high definition'
    };
    return `TECHNICAL REQUIREMENTS:
${styleDescriptions[style]},
${qualityDescriptions[quality]},
consistent lighting throughout,
smooth camera movements following emotional beats,
maintaining character and scene consistency across all frames`;
}
function createCharacterDNA(characterInfo) {
    return {
        id: characterInfo.id || generateId(),
        name: characterInfo.name,
        coreFeatures: {
            age: extractAge(characterInfo.identity) || '25 years old',
            gender: extractGender(characterInfo.identity) || 'person',
            profession: extractProfession(characterInfo.identity) || 'professional',
            faceShape: extractFaceShape(characterInfo.facial) || 'oval face',
            eyes: extractEyeFeatures(characterInfo.facial) || 'expressive eyes',
            hair: extractHairFeatures(characterInfo.hairstyle) || 'neat hair',
            build: extractBuildFeatures(characterInfo.physique) || 'average build',
            signatureStyle: extractClothingStyle(characterInfo.clothing) || 'casual attire'
        },
        consistentElements: {
            facialStructure: 'same facial bone structure',
            eyeCharacteristics: 'same eye shape and color',
            hairFeatures: 'consistent hair texture and length',
            bodyProportions: 'identical body proportions'
        }
    };
}
function createSceneDNA(sceneInfo) {
    return {
        id: sceneInfo.id || generateId(),
        name: sceneInfo.name,
        baseDescription: {
            type: sceneInfo.location || 'interior space',
            layout: sceneInfo.description || 'organized layout',
            furniture: extractFurniture(sceneInfo.description) || 'basic furniture',
            lighting: extractLighting(sceneInfo.atmosphere) || 'natural lighting',
            colorScheme: extractColorScheme(sceneInfo.description) || 'neutral colors',
            atmosphere: sceneInfo.atmosphere || 'comfortable atmosphere'
        },
        fixedElements: {
            architecture: 'consistent architectural elements',
            furnitureLayout: 'same furniture arrangement',
            lightingSetup: 'identical lighting setup',
            colorPalette: 'consistent color palette'
        },
        timeStates: {
            morning: {
                lighting: 'soft morning light',
                activityLevel: 'quiet and peaceful',
                atmosphere: 'fresh and energetic'
            },
            midday: {
                lighting: 'bright natural light',
                activityLevel: 'active and busy',
                atmosphere: 'focused and productive'
            },
            evening: {
                lighting: 'warm evening light',
                activityLevel: 'calm and relaxed',
                atmosphere: 'peaceful and reflective'
            }
        }
    };
}
function generateConsistencyCheckPrompt(previousVideoPrompt, currentVideoPrompt) {
    return `CONSISTENCY CHECK:
Ensure the following elements remain identical between videos:

PREVIOUS SCENE REFERENCE:
${previousVideoPrompt}

CURRENT SCENE REQUIREMENTS:
${currentVideoPrompt}

CONSISTENCY REQUIREMENTS:
- Same character facial features and body proportions
- Identical scene layout and architectural elements
- Consistent lighting setup and color scheme
- Matching furniture arrangement and spatial relationships
- Same character clothing and styling (unless story requires change)
- Continuous environmental details and atmosphere

MAINTAIN VISUAL CONTINUITY across all generated content`;
}
// 辅助函数
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function extractAge(identity) {
    const ageMatch = identity?.match(/(\d+)岁|(\d+)\s*years?\s*old/i);
    return ageMatch ? `${ageMatch[1] || ageMatch[2]} years old` : '';
}
function extractGender(identity) {
    if (identity?.includes('女') || identity?.includes('woman') || identity?.includes('female')) {
        return 'woman';
    } else if (identity?.includes('男') || identity?.includes('man') || identity?.includes('male')) {
        return 'man';
    }
    return 'person';
}
function extractProfession(identity) {
    const professions = [
        'architect',
        'doctor',
        'teacher',
        'engineer',
        'designer',
        'manager'
    ];
    for (const prof of professions){
        if (identity?.toLowerCase().includes(prof)) {
            return prof;
        }
    }
    return 'professional';
}
function extractFaceShape(facial) {
    if (facial?.includes('瓜子脸')) return 'oval face';
    if (facial?.includes('圆脸')) return 'round face';
    if (facial?.includes('方脸')) return 'square face';
    return 'oval face';
}
function extractEyeFeatures(facial) {
    if (facial?.includes('大眼睛')) return 'large expressive eyes';
    if (facial?.includes('小眼睛')) return 'small bright eyes';
    return 'expressive eyes';
}
function extractHairFeatures(hairstyle) {
    if (hairstyle?.includes('长发')) return 'long hair';
    if (hairstyle?.includes('短发')) return 'short hair';
    if (hairstyle?.includes('卷发')) return 'curly hair';
    return 'neat hair';
}
function extractBuildFeatures(physique) {
    if (physique?.includes('高挑')) return 'tall and slender build';
    if (physique?.includes('娇小')) return 'petite build';
    return 'average build';
}
function extractClothingStyle(clothing) {
    if (clothing?.includes('职业装')) return 'professional business attire';
    if (clothing?.includes('休闲')) return 'casual wear';
    return 'neat clothing';
}
function extractFurniture(description) {
    return 'modern furniture arrangement';
}
function extractLighting(atmosphere) {
    return 'natural lighting';
}
function extractColorScheme(description) {
    return 'neutral color scheme';
}
/**
 * 构建一致性约束声明（新增）
 */ function buildConsistencyDeclarationSection(mode, weights) {
    const modeDescriptions = {
        strict: 'STRICT CONSISTENCY MODE: Absolute adherence to character and scene specifications. Zero tolerance for deviations.',
        balanced: 'BALANCED CONSISTENCY MODE: High fidelity to specifications with minor creative adaptations allowed.',
        creative: 'CREATIVE CONSISTENCY MODE: Core character identity maintained while allowing artistic interpretation.'
    };
    return `CONSISTENCY CONSTRAINT SYSTEM ACTIVATED
${modeDescriptions[mode]}

DUAL CONSTRAINT WEIGHTS:
- Text Constraint Weight: ${(weights.textWeight * 100).toFixed(0)}%
- Visual Constraint Weight: ${(weights.imageWeight * 100).toFixed(0)}%

CONSISTENCY PRIORITY: Character identity > Scene continuity > Artistic style`;
}
/**
 * 构建增强的角色一致性描述
 */ function buildEnhancedCharacterConsistencySection(characters, referenceImages, consistencyMode) {
    if (characters.length === 0) return '';
    const characterDescriptions = characters.map((char)=>{
        const features = char.coreFeatures;
        const consistent = char.consistentElements;
        const hasReferenceImages = referenceImages[char.id] && referenceImages[char.id].length > 0;
        let description = `${char.name}: ${features.age} ${features.gender}, ${features.profession}.
FACIAL DNA: ${features.faceShape}, ${features.eyes}, ${features.hair}.
BODY DNA: ${features.build}.
STYLE DNA: ${features.signatureStyle}.
CONSISTENCY ANCHORS: ${consistent.facialStructure}, ${consistent.eyeCharacteristics}, ${consistent.hairFeatures}, ${consistent.bodyProportions}.`;
        if (hasReferenceImages) {
            description += `
VISUAL REFERENCE: Character has ${referenceImages[char.id].length} reference image(s) for visual consistency validation.`;
        }
        if (consistencyMode === 'strict') {
            description += `
STRICT MODE: ZERO deviation allowed from specified features. Exact replication required.`;
        }
        return description;
    }).join('\n\n');
    return `CHARACTER CONSISTENCY MATRIX:
${characterDescriptions}

CONSISTENCY ENFORCEMENT:
- Facial features must remain identical across all frames
- Hair style and color must be consistent
- Body proportions and posture must match specifications
- Clothing style must align with character DNA
- Expression changes allowed only within character personality bounds

VALIDATION CHECKPOINTS:
✓ Frame-by-frame facial feature verification
✓ Hair consistency across camera angles
✓ Body proportion maintenance
✓ Clothing style adherence`;
}
/**
 * 构建增强的技术要求描述
 */ function buildEnhancedTechnicalRequirementsSection(style, quality, consistencyMode) {
    const styleDescriptions = {
        cinematic: 'cinematic quality, professional cinematography, film-grade lighting',
        documentary: 'documentary style, natural lighting, authentic feel',
        artistic: 'artistic style, creative camera work, stylized visuals'
    };
    const qualityDescriptions = {
        standard: 'good quality, stable footage, clear details',
        high: 'high quality, 4K resolution, sharp focus, professional grade',
        cinematic: 'cinematic quality, ultra high definition, film-grade production'
    };
    const consistencyRequirements = {
        strict: 'STRICT: Pixel-perfect consistency, identical lighting conditions, exact camera positioning',
        balanced: 'BALANCED: High consistency with natural variations, smooth transitions',
        creative: 'CREATIVE: Core consistency maintained with artistic freedom in presentation'
    };
    return `ENHANCED TECHNICAL REQUIREMENTS:
${styleDescriptions[style]},
${qualityDescriptions[quality]},
${consistencyRequirements[consistencyMode]},

CONSISTENCY TECHNICAL SPECS:
- Maintain identical character proportions across all frames
- Consistent lighting temperature and direction
- Smooth camera movements preserving character visibility
- Color grading consistency for scene continuity
- Frame-by-frame character feature validation
- Temporal consistency in motion and expression`;
}
/**
 * 构建一致性验证要求
 */ function buildConsistencyValidationSection(characters, consistencyMode) {
    const characterNames = characters.map((char)=>char.name).join(', ');
    const validationLevels = {
        strict: 'STRICT VALIDATION: Every frame must pass character identity verification',
        balanced: 'BALANCED VALIDATION: Core features verified with tolerance for natural variation',
        creative: 'CREATIVE VALIDATION: Character essence maintained with artistic interpretation allowed'
    };
    return `CONSISTENCY VALIDATION PROTOCOL:
${validationLevels[consistencyMode]}

VALIDATION TARGETS: ${characterNames}

AUTOMATED CHECKS:
1. Facial feature consistency (eyes, nose, mouth, face shape)
2. Hair style and color maintenance
3. Body proportion verification
4. Clothing style adherence
5. Scene element continuity
6. Lighting consistency
7. Color palette maintenance

QUALITY GATES:
- Pre-generation: Prompt consistency verification
- Mid-generation: Frame sampling validation
- Post-generation: Full video consistency audit

FAILURE HANDLING:
- Inconsistency detected → Regeneration with enhanced constraints
- Partial inconsistency → Selective frame regeneration
- Systematic inconsistency → DNA profile refinement`;
}
}}),
"[project]/src/app/api/ai/analyze-detailed-plot/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storyVideoPromptGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/storyVideoPromptGenerator.ts [app-route] (ecmascript)");
;
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const episodeId = searchParams.get('episodeId');
        if (!episodeId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少episodeId参数'
            }, {
                status: 400
            });
        }
        // 查找已保存的详细剧情信息
        const plotInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].plotInfo.findUnique({
            where: {
                episodeId
            }
        });
        if (!plotInfo) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '未找到详细剧情信息'
            });
        }
        // 检查是否有任何详细信息
        const hasDetailedInfo = plotInfo.detailedCharacters || plotInfo.detailedScenes || plotInfo.plotSequences || plotInfo.emotionalArc || plotInfo.generatedPrompt;
        if (!hasDetailedInfo) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '未找到详细剧情信息'
            });
        }
        // 解析保存的数据
        let characters = [];
        let scenes = [];
        let plotSequences = [];
        try {
            characters = plotInfo.detailedCharacters ? JSON.parse(plotInfo.detailedCharacters) : [];
        } catch (error) {
            console.error('解析detailedCharacters失败:', error);
            characters = [];
        }
        try {
            scenes = plotInfo.detailedScenes ? JSON.parse(plotInfo.detailedScenes) : [];
        } catch (error) {
            console.error('解析detailedScenes失败:', error);
            scenes = [];
        }
        try {
            plotSequences = plotInfo.plotSequences ? JSON.parse(plotInfo.plotSequences) : [];
        } catch (error) {
            console.error('解析plotSequences失败:', error);
            plotSequences = [];
        }
        const analysisResult = {
            characters,
            scenes,
            plotSequences,
            emotionalArc: plotInfo.emotionalArc || '',
            generatedPrompt: plotInfo.generatedPrompt || ''
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: analysisResult,
            message: '详细剧情信息获取成功'
        });
    } catch (error) {
        console.error('获取详细剧情信息失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取失败，请重试'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        console.log('📥 收到POST请求');
        const body = await request.json();
        console.log('📥 请求体:', {
            episodeId: body.episodeId,
            hasContent: !!body.episodeContent
        });
        const { episodeId, episodeContent, customPrompt } = body;
        if (!episodeId || !episodeContent) {
            console.log('❌ 缺少必要参数');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少必要参数'
            }, {
                status: 400
            });
        }
        // 获取AI配置
        const aiConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
            where: {
                enabled: true
            }
        });
        if (!aiConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请先配置AI模型'
            }, {
                status: 400
            });
        }
        // 分析详细剧情信息（传入episodeId用于一致性约束）
        let analysisResult;
        try {
            console.log('开始调用analyzeDetailedPlotInfo...');
            analysisResult = await analyzeDetailedPlotInfo(aiConfig, episodeContent, customPrompt, episodeId);
            console.log('分析完成，结果:', analysisResult);
        } catch (error) {
            console.error('分析过程出错:', error);
            console.error('错误详情:', error.stack);
            throw error;
        }
        // 保存详细剧情信息到数据库
        try {
            await saveDetailedPlotInfo(episodeId, analysisResult);
            console.log('保存完成');
        } catch (error) {
            console.error('保存过程出错:', error);
        // 继续执行，不影响返回结果
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: analysisResult,
            message: '剧情信息分析完成'
        });
    } catch (error) {
        console.error('分析详细剧情失败:', error);
        // 返回更详细的错误信息用于调试
        const errorMessage = error instanceof Error ? error.message : '分析失败，请重试';
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: errorMessage,
            details: error
        }, {
            status: 500
        });
    }
}
// 分析详细剧情信息（增强版：集成角色一致性约束）
async function analyzeDetailedPlotInfo(aiConfig, episodeContent, customPrompt, episodeId) {
    try {
        console.log('开始详细剧情分析（一致性增强版）...');
        console.log('AI配置:', {
            provider: aiConfig.provider,
            model: aiConfig.model,
            hasApiKey: !!aiConfig.apiKey
        });
        const aiClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAIClient"])(aiConfig);
        // 1. 获取项目角色库（用于一致性约束）
        let projectCharacters = [];
        if (episodeId) {
            try {
                const episode = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].episode.findUnique({
                    where: {
                        id: episodeId
                    },
                    include: {
                        project: {
                            include: {
                                characters: true
                            }
                        }
                    }
                });
                projectCharacters = episode?.project?.characters || [];
                console.log('获取项目角色库，角色数量:', projectCharacters.length);
            } catch (error) {
                console.error('获取项目角色库失败:', error);
            }
        }
        // 强制进行真实的AI分析，不使用任何测试数据
        console.log('正在调用真实AI API进行分析...');
        // 2. 分析角色信息（增强版：包含一致性约束）
        console.log('开始分析角色信息（一致性增强）...');
        const charactersResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAIRequest"])(()=>analyzeCharactersWithConsistency(aiClient, episodeContent, projectCharacters));
        console.log('角色分析完成，结果数量:', charactersResult.length);
        // 2. 分析场景信息
        console.log('开始分析场景信息...');
        const scenesResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAIRequest"])(()=>analyzeScenes(aiClient, episodeContent));
        console.log('场景分析完成，结果数量:', scenesResult.length);
        // 3. 分析情节序列
        console.log('开始分析情节序列...');
        const plotSequencesResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAIRequest"])(()=>analyzePlotSequences(aiClient, episodeContent));
        console.log('情节序列分析完成，结果数量:', plotSequencesResult.length);
        // 4. 分析情感弧线
        console.log('开始分析情感弧线...');
        const emotionalArcResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleAIRequest"])(()=>analyzeEmotionalArc(aiClient, episodeContent));
        console.log('情感弧线分析完成，长度:', emotionalArcResult.length);
        // 5. 创建角色DNA档案（增强版：包含一致性信息）
        const characterDNAs = charactersResult.map((char)=>{
            const dna = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storyVideoPromptGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCharacterDNA"])({
                id: char.name?.toLowerCase().replace(/\s+/g, '_') || 'character',
                name: char.name || '角色',
                identity: char.identity,
                facial: char.appearance,
                personality: char.personality,
                clothing: char.clothing
            });
            // 添加一致性信息
            if (char.consistencyInfo) {
                dna.consistencyInfo = char.consistencyInfo;
            }
            return dna;
        });
        // 6. 创建场景DNA档案
        const sceneDNAs = scenesResult.map((scene)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storyVideoPromptGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSceneDNA"])({
                id: scene.location?.toLowerCase().replace(/\s+/g, '_') || 'scene',
                name: scene.location || '场景',
                location: scene.location,
                description: scene.description,
                atmosphere: scene.atmosphere
            }));
        // 7. 生成专业视频提示词
        const generatedPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storyVideoPromptGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateStoryVideoPrompt"])({
            characters: characterDNAs,
            scene: sceneDNAs[0] || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$storyVideoPromptGenerator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSceneDNA"])({
                id: 'default',
                name: '默认场景'
            }),
            timeOfDay: 'midday',
            plotSequences: plotSequencesResult,
            emotionalArc: emotionalArcResult,
            customEnhancement: customPrompt,
            style: 'cinematic',
            quality: 'high'
        });
        return {
            characters: charactersResult,
            scenes: scenesResult,
            plotSequences: plotSequencesResult,
            emotionalArc: emotionalArcResult,
            characterDNAs,
            sceneDNAs,
            generatedPrompt
        };
    } catch (error) {
        console.error('详细剧情分析失败:', error);
        throw error;
    }
}
// 分析角色信息
async function analyzeCharacters(aiClient, episodeContent) {
    const prompt = `你是一个专业的小说分析师。请仔细分析以下剧集内容，提取其中出现的所有角色信息。

剧集内容：
${episodeContent}

请按照以下JSON格式返回角色信息：
{
  "characters": [
    {
      "name": "角色姓名",
      "identity": "身份职业和基本信息",
      "appearance": "外貌特征描述",
      "personality": "性格特点",
      "clothing": "服装描述",
      "role": "在本集中的作用"
    }
  ]
}

要求：
1. 只提取在本集中实际出现的角色
2. 外貌描述要具体详细
3. 性格分析要基于文本中的行为表现
4. 如果文中没有明确描述某项信息，可以合理推断但要标注
5. 返回标准JSON格式`;
    const response = await aiClient.callAPI(prompt, 4000);
    try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const result = JSON.parse(jsonMatch[0]);
        return result.characters || [];
    } catch (error) {
        console.error('解析角色分析响应失败:', error);
        return [];
    }
}
// 分析场景信息
async function analyzeScenes(aiClient, episodeContent) {
    const prompt = `你是一个专业的场景设计师。请仔细分析以下剧集内容，提取其中的场景信息。

剧集内容：
${episodeContent}

请按照以下JSON格式返回场景信息：
{
  "scenes": [
    {
      "location": "场景地点名称",
      "description": "场景详细描述",
      "atmosphere": "氛围和环境特点",
      "timeOfDay": "时间段",
      "lighting": "光线条件",
      "keyElements": "关键环境元素"
    }
  ]
}

要求：
1. 提取所有在本集中出现的场景
2. 描述要具体详细，包含空间布局
3. 氛围描述要体现情感色彩
4. 注意光线和时间的描述
5. 返回标准JSON格式`;
    const response = await aiClient.callAPI(prompt, 3000);
    try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const result = JSON.parse(jsonMatch[0]);
        return result.scenes || [];
    } catch (error) {
        console.error('解析场景分析响应失败:', error);
        return [];
    }
}
// 分析情节序列
async function analyzePlotSequences(aiClient, episodeContent) {
    const prompt = `你是一个专业的剧本分析师。请仔细分析以下剧集内容，将情节分解为可视化的动作序列。

剧集内容：
${episodeContent}

请按照以下JSON格式返回情节序列：
{
  "plotSequences": [
    {
      "sequenceId": "序列编号",
      "action": "具体动作描述",
      "emotion": "情感状态",
      "duration": "预估时长",
      "keyMoments": ["关键时刻1", "关键时刻2"],
      "visualElements": "视觉表现要点"
    }
  ]
}

要求：
1. 将剧情分解为3-5个主要序列
2. 每个序列要有明确的动作和情感
3. 关键时刻要具体可视化
4. 考虑视频制作的可行性
5. 返回标准JSON格式`;
    const response = await aiClient.callAPI(prompt, 3000);
    try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const result = JSON.parse(jsonMatch[0]);
        return result.plotSequences || [];
    } catch (error) {
        console.error('解析情节序列响应失败:', error);
        return [];
    }
}
// 分析情感弧线
async function analyzeEmotionalArc(aiClient, episodeContent) {
    const prompt = `你是一个专业的情感分析师。请仔细分析以下剧集内容，总结整体的情感发展弧线。

剧集内容：
${episodeContent}

请分析并返回情感弧线描述，格式如下：
开始情感 → 发展过程 → 转折点 → 高潮情感 → 结束情感

要求：
1. 分析主要角色的情感变化
2. 识别关键的情感转折点
3. 描述要简洁明了
4. 适合视频表现的情感节奏
5. 用箭头连接不同阶段

示例：平静专注 → 遇到困难 → 灵感突现 → 兴奋创作 → 满足成就`;
    const response = await aiClient.callAPI(prompt, 2000);
    // 直接返回文本，不需要JSON解析
    return response.trim();
}
// 分析角色信息（增强版：包含一致性约束）
async function analyzeCharactersWithConsistency(aiClient, episodeContent, projectCharacters) {
    // 构建包含项目角色库信息的提示词
    let characterConstraints = '';
    if (projectCharacters.length > 0) {
        characterConstraints = `\n\n已知项目角色库：\n${projectCharacters.map((char)=>{
            const details = [];
            if (char.identity) details.push(`身份: ${char.identity}`);
            if (char.facial) details.push(`外貌: ${char.facial}`);
            if (char.personality) details.push(`性格: ${char.personality}`);
            if (char.consistencyScore) details.push(`一致性评分: ${char.consistencyScore}`);
            return `- ${char.name}: ${details.join(', ')}`;
        }).join('\n')}\n\n请在分析时：
1. 如果剧集中的角色与已知角色匹配，请保持一致性约束
2. 标注角色是否为已知角色（isKnownCharacter: true/false）
3. 如果是已知角色，提供一致性匹配度评分（consistencyMatch: 0.0-1.0）
4. 指出与已知角色的差异（如有）`;
    }
    const prompt = `你是一个专业的小说分析师和角色一致性专家。请仔细分析以下剧集内容，提取其中出现的所有角色信息，并进行一致性约束分析。

剧集内容：
${episodeContent}${characterConstraints}

请按照以下JSON格式返回角色信息：
{
  "characters": [
    {
      "name": "角色姓名",
      "identity": "身份职业和基本信息",
      "appearance": "外貌特征描述",
      "personality": "性格特点",
      "clothing": "服装描述",
      "role": "在本集中的作用",
      "isKnownCharacter": true/false,
      "consistencyInfo": {
        "matchedCharacterId": "匹配的已知角色ID（如果有）",
        "consistencyMatch": 0.95,
        "differences": ["与已知角色的差异列表"],
        "consistencyConstraints": "一致性约束建议"
      }
    }
  ]
}

要求：
1. 只提取在本集中实际出现的角色
2. 外貌描述要具体详细，符合一致性要求
3. 性格分析要基于文本中的行为表现
4. 如果是已知角色，必须保持与项目角色库的一致性
5. 提供详细的一致性分析和约束建议
6. 返回标准JSON格式`;
    const response = await aiClient.callAPI(prompt, 5000);
    try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const result = JSON.parse(jsonMatch[0]);
        return result.characters || [];
    } catch (error) {
        console.error('解析角色分析响应失败:', error);
        // 降级到原始分析方法
        return await analyzeCharacters(aiClient, episodeContent);
    }
}
// 保存详细剧情信息到数据库 - 使用与基础剧情信息相同的存储方式
async function saveDetailedPlotInfo(episodeId, analysisResult) {
    try {
        // 使用upsert操作，与基础剧情分析保持一致
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].plotInfo.upsert({
            where: {
                episodeId
            },
            update: {
                // 更新详细信息字段
                detailedCharacters: JSON.stringify(analysisResult.characters || []),
                detailedScenes: JSON.stringify(analysisResult.scenes || []),
                plotSequences: JSON.stringify(analysisResult.plotSequences || []),
                emotionalArc: analysisResult.emotionalArc || '',
                generatedPrompt: analysisResult.generatedPrompt || '',
                updatedAt: new Date()
            },
            create: {
                episodeId,
                // 基础字段，保持与基础剧情分析的兼容性
                characters: '[]',
                scenes: '[]',
                events: '[]',
                // 详细信息字段
                detailedCharacters: JSON.stringify(analysisResult.characters || []),
                detailedScenes: JSON.stringify(analysisResult.scenes || []),
                plotSequences: JSON.stringify(analysisResult.plotSequences || []),
                emotionalArc: analysisResult.emotionalArc || '',
                generatedPrompt: analysisResult.generatedPrompt || ''
            }
        });
    } catch (error) {
        console.error('保存详细剧情信息失败:', error);
    // 不抛出错误，避免影响主流程
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ccf11cbd._.js.map