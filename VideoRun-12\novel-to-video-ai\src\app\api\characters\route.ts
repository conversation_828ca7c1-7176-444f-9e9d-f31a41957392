import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取角色列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '缺少项目ID参数' },
        { status: 400 }
      )
    }

    // 查询指定项目的所有角色
    const characters = await prisma.character.findMany({
      where: {
        projectId: projectId
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: characters
    })

  } catch (error) {
    console.error('获取角色列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取角色列表失败'
      },
      { status: 500 }
    )
  }
}

// 创建新角色
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      projectId,
      name,
      identity,
      personality,
      physique,
      facial,
      hairstyle,
      clothing,
      detailedDNA,
      consistencyScore,
      referenceImages
    } = body

    if (!projectId || !name) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      )
    }

    // 创建新角色
    const character = await prisma.character.create({
      data: {
        projectId,
        name,
        identity,
        personality,
        physique,
        facial,
        hairstyle,
        clothing,
        detailedDNA: detailedDNA ? JSON.stringify(detailedDNA) : null,
        consistencyScore,
        referenceImages: referenceImages ? JSON.stringify(referenceImages) : null
      }
    })

    return NextResponse.json({
      success: true,
      data: character
    })

  } catch (error) {
    console.error('创建角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '创建角色失败'
      },
      { status: 500 }
    )
  }
}
