'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  Plus,
  FolderOpen,
  Calendar,
  FileText,
  Users,
  Film,
  Video,
  MoreVertical,
  Trash2,
  Edit,
  X
} from 'lucide-react'
import Layout from '@/components/Layout'
import { Project } from '@/types'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载项目列表
  useEffect(() => {
    loadProjects()
  }, [])

  const loadProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      const data = await response.json()
      
      if (data.success) {
        setProjects(data.data)
      } else {
        throw new Error(data.error || '加载项目失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除项目
  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('确定要删除这个项目吗？此操作不可恢复。')) {
      return
    }

    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        setProjects(prev => prev.filter(p => p.id !== projectId))
      } else {
        throw new Error(data.error || '删除项目失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '删除项目失败')
    }
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      created: { text: '已创建', color: 'bg-gray-100 text-gray-800' },
      uploaded: { text: '已上传', color: 'bg-blue-100 text-blue-800' },
      analyzing: { text: '分析中', color: 'bg-yellow-100 text-yellow-800' },
      completed: { text: '已完成', color: 'bg-green-100 text-green-800' }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.created
  }

  // 格式化日期
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载项目中...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">项目管理</h1>
            <p className="mt-1 text-sm text-gray-600">
              管理您的小说转视频项目
            </p>
          </div>
          <Link
            href="/projects/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            <Plus className="mr-2" size={16} />
            新建项目
          </Link>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">
              {error}
              <button
                onClick={() => setError(null)}
                className="ml-2 p-1 text-red-800 hover:text-red-900 rounded-full hover:bg-red-100 transition-colors"
                title="关闭"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        )}

        {/* 项目列表 */}
        {projects.length === 0 ? (
          <div className="text-center py-12">
            <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
            <p className="mt-1 text-sm text-gray-500">
              开始创建您的第一个小说转视频项目
            </p>
            <div className="mt-6">
              <Link
                href="/projects/new"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <Plus className="mr-2" size={16} />
                新建项目
              </Link>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {projects.map((project) => {
              const statusDisplay = getStatusDisplay(project.status)
              
              return (
                <Link
                  key={project.id}
                  href={`/projects/${project.id}`}
                  className="relative group bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow block"
                >
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FolderOpen className="text-purple-600 mr-3" size={20} />
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {project.name}
                        </h3>
                      </div>
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <MoreVertical size={16} />
                        </button>
                      </div>
                    </div>

                    {project.description && (
                      <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                        {project.description}
                      </p>
                    )}

                    <div className="mt-4 flex items-center justify-between">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                        {statusDisplay.text}
                      </span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar size={14} className="mr-1" />
                        {formatDate(project.createdAt)}
                      </div>
                    </div>

                    <div className="mt-4 flex justify-between">
                      <span className="text-sm font-medium text-purple-600">
                        查看详情 →
                      </span>
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          handleDeleteProject(project.id)
                        }}
                        className="text-sm text-red-600 hover:text-red-500"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        )}
      </div>
    </Layout>
  )
}
