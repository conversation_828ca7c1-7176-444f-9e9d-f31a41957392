import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { createAIClient } from '@/lib/ai'

const prisma = new PrismaClient()

// POST - 验证一致性
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 收到一致性验证请求')
    const body = await request.json()
    const { 
      generationId,
      videoUrl,
      validationType = 'full', // full, character, scene
      strictMode = false
    } = body

    if (!generationId) {
      return NextResponse.json(
        { success: false, error: '缺少生成ID' },
        { status: 400 }
      )
    }

    console.log('📋 参数:', { generationId, hasVideoUrl: !!videoUrl, validationType, strictMode })

    // 1. 获取生成记录
    const generation = await prisma.videoGeneration.findUnique({
      where: { id: generationId },
      include: {
        episode: {
          include: {
            project: {
              include: {
                characters: true
              }
            },
            plotInfo: true
          }
        }
      }
    })

    if (!generation) {
      return NextResponse.json(
        { success: false, error: '生成记录不存在' },
        { status: 404 }
      )
    }

    console.log('✅ 生成记录获取成功')

    // 2. 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '未找到可用的AI配置' },
        { status: 500 }
      )
    }

    // 3. 解析原始数据
    const plotInfo = generation.episode.plotInfo
    let characters: any[] = []
    let scenes: any[] = []

    if (plotInfo) {
      try {
        characters = JSON.parse(plotInfo.detailedCharacters || plotInfo.characters || '[]')
        scenes = JSON.parse(plotInfo.detailedScenes || plotInfo.scenes || '[]')
      } catch (error) {
        console.error('解析剧情信息失败:', error)
      }
    }

    const projectCharacters = generation.episode.project.characters

    console.log('📊 数据统计:', { 
      charactersCount: characters.length, 
      projectCharactersCount: projectCharacters.length,
      scenesCount: scenes.length 
    })

    // 4. 执行一致性验证
    const validationResults = await performConsistencyValidation({
      aiConfig,
      generation,
      characters,
      projectCharacters,
      scenes,
      videoUrl,
      validationType,
      strictMode
    })

    console.log('✅ 一致性验证完成')

    // 5. 保存验证结果
    const validationRecord = await saveValidationResults(generationId, validationResults)

    console.log('💾 验证结果已保存:', validationRecord.id)

    // 6. 返回结果
    return NextResponse.json({
      success: true,
      data: {
        validationId: validationRecord.id,
        generationId,
        validationType,
        strictMode,
        results: validationResults,
        summary: {
          overallScore: validationResults.overallScore,
          characterConsistency: validationResults.characterValidation?.averageScore || 0,
          sceneConsistency: validationResults.sceneValidation?.averageScore || 0,
          issuesFound: validationResults.issuesFound?.length || 0,
          recommendations: validationResults.recommendations?.length || 0
        }
      }
    })

  } catch (error) {
    console.error('一致性验证失败:', error)
    const errorMessage = error instanceof Error ? error.message : '验证失败，请重试'
    return NextResponse.json(
      { success: false, error: errorMessage, details: error },
      { status: 500 }
    )
  }
}

// GET - 获取验证结果
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const validationId = searchParams.get('validationId')
    const generationId = searchParams.get('generationId')

    if (!validationId && !generationId) {
      return NextResponse.json(
        { success: false, error: '缺少验证ID或生成ID' },
        { status: 400 }
      )
    }

    let validationRecord
    if (validationId) {
      validationRecord = await prisma.consistencyValidation.findUnique({
        where: { id: validationId },
        include: {
          character: true
        }
      })
    } else {
      // 根据generationId查找最新的验证记录
      validationRecord = await prisma.consistencyValidation.findFirst({
        where: {
          validationDetails: {
            contains: generationId
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          character: true
        }
      })
    }

    if (!validationRecord) {
      return NextResponse.json(
        { success: false, error: '验证记录不存在' },
        { status: 404 }
      )
    }

    const validationDetails = validationRecord.validationDetails 
      ? JSON.parse(validationRecord.validationDetails) 
      : null

    return NextResponse.json({
      success: true,
      data: {
        id: validationRecord.id,
        characterId: validationRecord.characterId,
        character: validationRecord.character,
        textConsistencyScore: validationRecord.textConsistencyScore,
        imageConsistencyScore: validationRecord.imageConsistencyScore,
        overallScore: validationRecord.overallScore,
        validationDetails,
        issuesFound: validationRecord.issuesFound ? JSON.parse(validationRecord.issuesFound) : [],
        textWeight: validationRecord.textWeight,
        imageWeight: validationRecord.imageWeight,
        generatedImageUrl: validationRecord.generatedImageUrl,
        createdAt: validationRecord.createdAt
      }
    })

  } catch (error) {
    console.error('获取验证结果失败:', error)
    return NextResponse.json(
      { success: false, error: '获取验证结果失败' },
      { status: 500 }
    )
  }
}

// 执行一致性验证
async function performConsistencyValidation(params: {
  aiConfig: any
  generation: any
  characters: any[]
  projectCharacters: any[]
  scenes: any[]
  videoUrl?: string
  validationType: string
  strictMode: boolean
}) {
  const { aiConfig, generation, characters, projectCharacters, scenes, videoUrl, validationType, strictMode } = params
  
  const aiClient = createAIClient(aiConfig)
  
  const results: any = {
    validationType,
    strictMode,
    timestamp: new Date().toISOString(),
    overallScore: 0,
    characterValidation: null,
    sceneValidation: null,
    issuesFound: [],
    recommendations: []
  }

  // 角色一致性验证
  if (validationType === 'full' || validationType === 'character') {
    console.log('🎭 开始角色一致性验证...')
    results.characterValidation = await validateCharacterConsistency(
      aiClient, 
      characters, 
      projectCharacters, 
      generation.prompt,
      strictMode
    )
  }

  // 场景一致性验证
  if (validationType === 'full' || validationType === 'scene') {
    console.log('🏞️ 开始场景一致性验证...')
    results.sceneValidation = await validateSceneConsistency(
      aiClient,
      scenes,
      generation.prompt,
      strictMode
    )
  }

  // 计算综合评分
  let totalScore = 0
  let validationCount = 0

  if (results.characterValidation) {
    totalScore += results.characterValidation.averageScore
    validationCount++
  }

  if (results.sceneValidation) {
    totalScore += results.sceneValidation.averageScore
    validationCount++
  }

  results.overallScore = validationCount > 0 ? totalScore / validationCount : 0

  // 生成问题和建议
  results.issuesFound = generateIssuesFound(results)
  results.recommendations = generateRecommendations(results, strictMode)

  return results
}

// 验证角色一致性
async function validateCharacterConsistency(
  aiClient: any,
  characters: any[],
  projectCharacters: any[],
  prompt: string,
  strictMode: boolean
) {
  if (characters.length === 0) {
    return { averageScore: 1.0, characterScores: [], issues: [] }
  }

  const characterScores = []
  const issues = []

  for (const character of characters) {
    // 查找匹配的项目角色
    const matchedProjectCharacter = projectCharacters.find(pc => 
      pc.name.toLowerCase() === character.name.toLowerCase()
    )

    let score = 0.8 // 默认评分

    if (matchedProjectCharacter) {
      // 使用AI评估一致性
      const consistencyPrompt = `请评估以下角色在视频生成提示词中的一致性：

角色名称：${character.name}

项目角色库信息：
- 身份：${matchedProjectCharacter.identity || '未设置'}
- 外貌：${matchedProjectCharacter.facial || '未设置'}
- 性格：${matchedProjectCharacter.personality || '未设置'}

剧集角色信息：
- 身份：${character.identity || '未设置'}
- 外貌：${character.appearance || character.facial || '未设置'}
- 性格：${character.personality || '未设置'}

视频提示词片段：
${prompt.substring(0, 1000)}...

请返回JSON格式的评估结果：
{
  "consistencyScore": 0.95,
  "issues": ["发现的不一致问题"],
  "strengths": ["一致性优点"],
  "recommendations": ["改进建议"]
}`

      try {
        const response = await aiClient.callAPI(consistencyPrompt, 1000)
        const evaluation = JSON.parse(response.match(/\{[\s\S]*\}/)?.[0] || '{}')
        score = evaluation.consistencyScore || score
        
        if (evaluation.issues && evaluation.issues.length > 0) {
          issues.push(...evaluation.issues.map((issue: string) => ({
            character: character.name,
            type: 'character_inconsistency',
            description: issue,
            severity: strictMode ? 'high' : 'medium'
          })))
        }
      } catch (error) {
        console.error(`角色 ${character.name} 一致性评估失败:`, error)
      }
    }

    characterScores.push({
      characterName: character.name,
      score,
      hasProjectReference: !!matchedProjectCharacter,
      consistencyScore: matchedProjectCharacter?.consistencyScore || null
    })
  }

  const averageScore = characterScores.reduce((sum, cs) => sum + cs.score, 0) / characterScores.length

  return {
    averageScore,
    characterScores,
    issues
  }
}

// 验证场景一致性
async function validateSceneConsistency(
  aiClient: any,
  scenes: any[],
  prompt: string,
  strictMode: boolean
) {
  if (scenes.length === 0) {
    return { averageScore: 1.0, sceneScores: [], issues: [] }
  }

  // 简化的场景一致性验证
  const sceneScores = scenes.map(scene => ({
    sceneName: scene.location || scene.name || '未命名场景',
    score: 0.85, // 默认评分
    elements: {
      lighting: 0.9,
      atmosphere: 0.8,
      layout: 0.85
    }
  }))

  const averageScore = sceneScores.reduce((sum, ss) => sum + ss.score, 0) / sceneScores.length

  return {
    averageScore,
    sceneScores,
    issues: []
  }
}

// 生成发现的问题
function generateIssuesFound(results: any) {
  const issues = []

  if (results.characterValidation?.issues) {
    issues.push(...results.characterValidation.issues)
  }

  if (results.sceneValidation?.issues) {
    issues.push(...results.sceneValidation.issues)
  }

  // 基于评分生成问题
  if (results.overallScore < 0.7) {
    issues.push({
      type: 'overall_consistency',
      description: '整体一致性评分较低，需要改进',
      severity: 'high'
    })
  }

  return issues
}

// 生成改进建议
function generateRecommendations(results: any, strictMode: boolean) {
  const recommendations = []

  if (results.characterValidation?.averageScore < 0.8) {
    recommendations.push({
      type: 'character_improvement',
      description: '建议加强角色DNA约束，确保角色特征的一致性',
      priority: 'high'
    })
  }

  if (results.sceneValidation?.averageScore < 0.8) {
    recommendations.push({
      type: 'scene_improvement',
      description: '建议优化场景描述，保持环境元素的连续性',
      priority: 'medium'
    })
  }

  if (strictMode && results.overallScore < 0.9) {
    recommendations.push({
      type: 'strict_mode_improvement',
      description: '严格模式下建议提高一致性约束强度',
      priority: 'high'
    })
  }

  return recommendations
}

// 保存验证结果
async function saveValidationResults(generationId: string, validationResults: any) {
  // 创建一个简化的验证记录，不依赖于特定角色
  // 我们将验证结果保存为JSON格式，避免外键约束问题

  try {
    // 首先尝试找到一个现有的角色作为关联
    const generation = await prisma.videoGeneration.findUnique({
      where: { id: generationId },
      include: {
        episode: {
          include: {
            project: {
              include: {
                characters: {
                  take: 1 // 只取第一个角色
                }
              }
            }
          }
        }
      }
    })

    let characterId = null
    if (generation?.episode?.project?.characters?.length > 0) {
      characterId = generation.episode.project.characters[0].id
    }

    if (!characterId) {
      // 如果没有找到角色，创建一个临时的验证记录结构
      // 直接返回结果而不保存到数据库
      return {
        id: `temp_${Date.now()}`,
        generationId,
        validationResults,
        createdAt: new Date()
      }
    }

    return await prisma.consistencyValidation.create({
      data: {
        characterId,
        textConsistencyScore: validationResults.characterValidation?.averageScore || 0,
        imageConsistencyScore: validationResults.sceneValidation?.averageScore || 0,
        overallScore: validationResults.overallScore,
        validationDetails: JSON.stringify({
          generationId,
          ...validationResults
        }),
        issuesFound: JSON.stringify(validationResults.issuesFound || []),
        textWeight: 0.7,
        imageWeight: 0.3
      }
    })
  } catch (error) {
    console.error('保存验证结果失败:', error)
    // 返回临时记录
    return {
      id: `temp_${Date.now()}`,
      generationId,
      validationResults,
      createdAt: new Date()
    }
  }
}
