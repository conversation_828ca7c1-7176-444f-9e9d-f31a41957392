import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { DeepSeekClient } from '@/lib/ai-clients'
import { AIServiceError } from '@/lib/errors'

// POST - 重试失败的视频片段
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { segmentId } = body

    if (!segmentId) {
      return NextResponse.json(
        { success: false, error: '片段ID不能为空' },
        { status: 400 }
      )
    }

    // 查找失败的片段
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId },
      include: {
        storyVideo: {
          include: {
            episode: true
          }
        }
      }
    })

    if (!segment) {
      return NextResponse.json(
        { success: false, error: '片段不存在' },
        { status: 404 }
      )
    }

    if (segment.status !== 'failed') {
      return NextResponse.json(
        { success: false, error: '只能重试失败的片段' },
        { status: 400 }
      )
    }

    console.log(`🔄 开始重试片段: ${segment.title}`)

    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: { enabled: true }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '请先配置AI模型' },
        { status: 400 }
      )
    }

    // 重置片段状态为generating
    await prisma.videoSegment.update({
      where: { id: segmentId },
      data: {
        status: 'generating',
        videoUrl: null,
        metadata: JSON.stringify({
          ...JSON.parse(segment.metadata || '{}'),
          retryAt: new Date().toISOString(),
          retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1,
          startTime: new Date().toISOString()
        })
      }
    })

    // 异步重新生成单个片段
    retrySingleSegmentAsync(aiConfig, segment)
      .then(() => {
        console.log(`✅ 片段重试完成: ${segment.title}`)
      })
      .catch((error) => {
        console.error(`❌ 片段重试失败: ${segment.title}`, error)
      })

    return NextResponse.json({
      success: true,
      message: '片段重试已启动',
      data: {
        segmentId: segmentId,
        status: 'generating'
      }
    })

  } catch (error) {
    console.error('重试失败片段失败:', error)
    return NextResponse.json(
      { success: false, error: '重试失败，请稍后再试' },
      { status: 500 }
    )
  }
}

// 异步重试单个片段
async function retrySingleSegmentAsync(aiConfig: any, segment: any) {
  try {
    console.log(`🎬 开始重新生成片段: ${segment.title}`)

    // 调用视频生成函数
    const videoResult = await generateSingleSegment(aiConfig, segment)

    // 根据生成结果更新片段信息
    const updateData: any = {
      duration: videoResult.duration,
      metadata: JSON.stringify({
        ...JSON.parse(segment.metadata || '{}'),
        retryAt: new Date().toISOString(),
        generatedAt: new Date().toISOString(),
        optimizedPrompt: videoResult.optimizedPrompt,
        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1
      })
    }

    // 根据生成结果更新状态和URL
    if (videoResult.status === 'completed' && videoResult.videoUrl) {
      updateData.videoUrl = videoResult.videoUrl
      updateData.thumbnailUrl = videoResult.thumbnailUrl
      updateData.status = 'completed'
      console.log(`✅ 片段重试成功: ${segment.title}`)
    } else if (videoResult.status === 'generating') {
      updateData.status = 'generating'
      updateData.metadata = JSON.stringify({
        ...JSON.parse(segment.metadata || '{}'),
        taskId: videoResult.taskId,
        retryAt: new Date().toISOString(),
        generatedAt: new Date().toISOString(),
        optimizedPrompt: videoResult.optimizedPrompt,
        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1
      })
      console.log(`🔄 片段重试已提交，等待完成: ${segment.title}`)
    } else if (videoResult.status === 'failed') {
      updateData.status = 'failed'
      updateData.metadata = JSON.stringify({
        ...JSON.parse(segment.metadata || '{}'),
        error: videoResult.error,
        retryAt: new Date().toISOString(),
        generatedAt: new Date().toISOString(),
        optimizedPrompt: videoResult.optimizedPrompt,
        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1
      })
      console.log(`❌ 片段重试失败: ${videoResult.error}`)
    } else {
      updateData.status = 'pending'
      console.log(`⏳ 片段重试状态待定`)
    }

    await prisma.videoSegment.update({
      where: { id: segment.id },
      data: updateData
    })

  } catch (error) {
    console.error(`❌ 片段重试异常:`, error)

    // 更新片段状态为失败
    await prisma.videoSegment.update({
      where: { id: segment.id },
      data: {
        status: 'failed',
        metadata: JSON.stringify({
          ...JSON.parse(segment.metadata || '{}'),
          error: error instanceof Error ? error.message : '重试失败',
          retryAt: new Date().toISOString(),
          failedAt: new Date().toISOString(),
          retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1
        })
      }
    })
  }
}

// 生成单个视频片段
async function generateSingleSegment(aiConfig: any, segment: any) {
  try {
    // 1. 优化片段提示词
    const deepSeekClient = new DeepSeekClient(aiConfig)
    const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：

片段标题：${segment.title}
片段描述：${segment.description}
原始提示词：${segment.prompt}

请按照以下要求优化：
1. 确保描述具体且可视化
2. 添加适合的镜头运动和角度
3. 强调画面质量和风格
4. 控制在100字以内
5. 适合${segment.segmentType}类型的片段

优化后的提示词：`

    const optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800)

    // 2. 调用视频生成API
    const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment)

    return {
      videoUrl: videoResult.videoUrl,
      thumbnailUrl: videoResult.thumbnailUrl,
      optimizedPrompt: optimizedPrompt.trim(),
      duration: videoResult.duration || segment.duration || 15,
      status: videoResult.status || 'completed',
      taskId: videoResult.taskId
    }
  } catch (error) {
    console.error('单个视频片段生成失败:', error)
    return {
      videoUrl: null,
      thumbnailUrl: null,
      optimizedPrompt: segment.prompt,
      duration: segment.duration || 15,
      status: 'failed',
      error: error instanceof Error ? error.message : '生成失败'
    }
  }
}

// 调用视频生成API - 简化版本，直接调用通义万相
async function callVideoGenerationAPI(prompt: string, segment: any) {
  try {
    // 获取通义万相配置
    const tongyiConfig = await prisma.aIConfig.findFirst({
      where: {
        enabled: true,
        provider: 'tongyi'
      }
    })

    if (!tongyiConfig) {
      throw new Error('没有找到可用的通义万相配置')
    }

    console.log(`🎬 使用通义万相生成片段: ${segment.title}`)

    // 优化提示词
    const optimizedPrompt = optimizePrompt(prompt)
    console.log(`片段 ${segment.segmentIndex} 提示词优化:`)
    console.log(`  原始: ${prompt.substring(0, 100)}...`)
    console.log(`  优化: ${optimizedPrompt.substring(0, 100)}...`)

    // 创建视频生成任务
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${tongyiConfig.apiKey}`,
        'Content-Type': 'application/json',
        'X-DashScope-Async': 'enable'
      },
      body: JSON.stringify({
        model: tongyiConfig.model,
        input: {
          prompt: optimizedPrompt
        },
        parameters: {
          size: '1280*720',
          duration: 5,
          prompt_extend: true
        }
      })
    })

    if (!response.ok) {
      throw new Error(`通义万相API调用失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    console.log('通义万相API响应:', JSON.stringify(result, null, 2))

    if (result.output && result.output.task_id) {
      const taskId = result.output.task_id
      console.log(`通义万相任务创建成功，任务ID: ${taskId}`)

      // 等待任务完成
      console.log(`⏳ 等待通义万相任务完成: ${taskId}`)
      const finalResult = await waitForTongyiTask(tongyiConfig, taskId, segment)

      return finalResult
    } else {
      throw new Error(`通义万相API返回格式异常: ${JSON.stringify(result)}`)
    }
  } catch (error) {
    console.error('调用视频生成API失败:', error)
    throw error
  }
}

// 优化提示词
function optimizePrompt(prompt: string): string {
  // 基础优化规则
  let optimized = prompt
    .replace(/[，。！？；：]/g, ', ') // 中文标点转英文
    .replace(/\s+/g, ' ') // 多个空格合并
    .trim()

  // 添加质量提升关键词
  const qualityKeywords = [
    'high quality',
    'cinematic',
    'detailed',
    'professional lighting'
  ]

  // 检查是否已包含质量关键词
  const hasQualityKeywords = qualityKeywords.some(keyword =>
    optimized.toLowerCase().includes(keyword.toLowerCase())
  )

  if (!hasQualityKeywords) {
    optimized += ', high quality, cinematic, detailed'
  }

  // 限制长度
  if (optimized.length > 200) {
    optimized = optimized.substring(0, 197) + '...'
  }

  return optimized
}

// 等待片段完成
async function waitForSegmentCompletion(segmentId: string, taskId: string, maxWaitTime: number) {
  const startTime = Date.now()
  const pollInterval = 5000 // 5秒轮询一次

  while (Date.now() - startTime < maxWaitTime) {
    try {
      // 检查片段状态
      const segment = await prisma.videoSegment.findUnique({
        where: { id: segmentId }
      })

      if (!segment) {
        console.log(`片段 ${segmentId} 不存在，停止等待`)
        break
      }

      if (segment.status === 'completed') {
        console.log(`✅ 片段 ${segmentId} 已完成`)
        break
      } else if (segment.status === 'failed') {
        console.log(`❌ 片段 ${segmentId} 生成失败`)
        break
      }

      console.log(`⏳ 片段 ${segmentId} 仍在生成中，继续等待...`)
      await new Promise(resolve => setTimeout(resolve, pollInterval))

    } catch (error) {
      console.error(`等待片段完成时出错:`, error)
      break
    }
  }

  if (Date.now() - startTime >= maxWaitTime) {
    console.log(`⏰ 片段 ${segmentId} 等待超时`)
  }
}

// 等待通义万相任务完成
async function waitForTongyiTask(tongyiConfig: any, taskId: string, segment: any) {
  const maxAttempts = 60 // 最多查询60次
  const pollInterval = 5000 // 每5秒查询一次

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔍 查询通义万相任务状态 (${attempt}/${maxAttempts}): ${taskId}`)

      const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tongyiConfig.apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`查询任务状态失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`📊 任务状态查询结果:`, JSON.stringify(result, null, 2))

      if (result.output && result.output.task_status) {
        const status = result.output.task_status

        if (status === 'SUCCEEDED') {
          console.log(`✅ 通义万相任务完成: ${taskId}`)

          if (result.output.video_url) {
            return {
              videoUrl: result.output.video_url,
              thumbnailUrl: result.output.thumbnail_url || null,
              duration: 5,
              status: 'completed',
              taskId: taskId,
              provider: 'tongyi'
            }
          } else {
            throw new Error('任务完成但没有返回视频URL')
          }
        } else if (status === 'FAILED') {
          console.log(`❌ 通义万相任务失败: ${taskId}`)
          throw new Error(`任务失败: ${result.output.message || '未知错误'}`)
        } else if (status === 'PENDING' || status === 'RUNNING') {
          console.log(`⏳ 任务仍在处理中: ${status}`)
          // 继续等待
        } else {
          console.log(`❓ 未知任务状态: ${status}`)
        }
      } else {
        console.log(`❓ 任务状态查询返回格式异常`)
      }

      // 等待下次查询
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }

    } catch (error) {
      console.error(`❌ 查询任务状态异常 (${attempt}/${maxAttempts}):`, error)

      if (attempt === maxAttempts) {
        throw error
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, pollInterval))
    }
  }

  throw new Error(`任务超时: 等待${maxAttempts * pollInterval / 1000}秒后仍未完成`)
}
