const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoI2VConnection() {
  try {
    console.log('🔧 测试豆包图生视频连接修复...\n');
    
    // 1. 获取I2V模型配置
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    if (!i2vModel) {
      console.log('❌ 未找到I2V模型配置');
      return;
    }
    
    console.log('✅ 找到I2V模型配置:');
    console.log(`   ID: ${i2vModel.id}`);
    console.log(`   名称: ${i2vModel.name}`);
    console.log(`   模型: ${i2vModel.model}`);
    console.log(`   启用: ${i2vModel.enabled}`);
    console.log(`   API密钥: ${i2vModel.apiKey ? '已配置' : '未配置'}`);
    
    if (!i2vModel.apiKey) {
      console.log('❌ API密钥未配置');
      return;
    }
    
    // 2. 测试修复后的连接逻辑
    console.log('\n🧪 测试修复后的连接逻辑...');
    
    // 模拟测试连接API调用
    const testData = {
      id: i2vModel.id,
      provider: i2vModel.provider,
      model: i2vModel.model,
      apiKey: i2vModel.apiKey,
      name: i2vModel.name
    };
    
    try {
      const response = await fetch('http://localhost:3000/api/models/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      });
      
      console.log(`📊 测试连接响应状态: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ 测试连接成功!');
        console.log('📋 响应结果:', JSON.stringify(result, null, 2));
        
        if (result.success) {
          console.log('🎉 豆包图生视频模型连接测试通过！');
        } else {
          console.log('❌ 连接测试失败:', result.error);
        }
      } else {
        const errorText = await response.text();
        console.log('❌ 测试连接请求失败:', errorText);
      }
      
    } catch (error) {
      console.error('❌ 测试连接异常:', error.message);
    }
    
    // 3. 验证数据库状态更新
    console.log('\n🔍 检查数据库状态更新...');
    
    const updatedModel = await prisma.aIConfig.findFirst({
      where: { id: i2vModel.id }
    });
    
    if (updatedModel) {
      console.log(`📊 模型状态: ${updatedModel.status}`);
      console.log(`📅 最后测试时间: ${updatedModel.lastTest}`);
    }
    
    // 4. 直接测试API调用格式
    console.log('\n🔬 直接测试API调用格式...');
    
    const testContent = [
      {
        type: "text",
        text: "测试连接 --ratio adaptive --dur 5"
      },
      {
        type: "image_url",
        image_url: {
          url: "https://via.placeholder.com/512x512/FF0000/FFFFFF?text=Test"
        }
      }
    ];
    
    console.log('📋 测试内容格式:', JSON.stringify(testContent, null, 2));
    
    try {
      const directResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${i2vModel.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: i2vModel.model,
          content: testContent
        })
      });
      
      console.log(`📊 直接API调用状态: ${directResponse.status}`);
      
      if (directResponse.ok) {
        const result = await directResponse.json();
        console.log('✅ 直接API调用成功!');
        console.log('📋 响应:', JSON.stringify(result, null, 2));
        
        const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
        if (taskId) {
          console.log(`🎯 任务ID: ${taskId}`);
          console.log('🎉 豆包图生视频API格式完全正确！');
        }
      } else {
        const errorText = await directResponse.text();
        console.log('❌ 直接API调用失败:', errorText);
      }
      
    } catch (error) {
      console.log('❌ 直接API调用异常:', error.message);
    }
    
    // 5. 对比T2V模型测试
    console.log('\n📊 对比T2V模型测试...');
    
    const t2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj'
      }
    });
    
    if (t2vModel && t2vModel.apiKey) {
      console.log('🧪 测试T2V模型连接...');
      
      const t2vTestData = {
        id: t2vModel.id,
        provider: t2vModel.provider,
        model: t2vModel.model,
        apiKey: t2vModel.apiKey,
        name: t2vModel.name
      };
      
      try {
        const t2vResponse = await fetch('http://localhost:3000/api/models/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(t2vTestData)
        });
        
        if (t2vResponse.ok) {
          const t2vResult = await t2vResponse.json();
          console.log(`📊 T2V模型测试结果: ${t2vResult.success ? '成功' : '失败'}`);
          if (!t2vResult.success) {
            console.log(`   错误: ${t2vResult.error}`);
          }
        }
      } catch (error) {
        console.log('❌ T2V模型测试异常:', error.message);
      }
    }
    
    // 6. 总结
    console.log('\n📊 测试总结:');
    console.log('修复内容:');
    console.log('1. ✅ 识别图生视频模型 (ep-20250624013749-dbrbn)');
    console.log('2. ✅ 为图生视频模型添加测试图像');
    console.log('3. ✅ 使用正确的 --ratio adaptive 参数');
    console.log('4. ✅ 区分I2V和T2V模型的测试逻辑');
    
    console.log('\n💡 现在可以在AI配置页面正常测试豆包图生视频模型连接了！');
    
    console.log('\n🎬 豆包图生视频连接测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoI2VConnection();
