const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoMultimodal() {
  try {
    console.log('🧪 测试豆包API多模态调用...\n');
    
    // 获取豆包配置
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        enabled: true,
        supportsVideo: true
      }
    });
    
    if (!doubaoConfig) {
      console.log('❌ 未找到豆包配置');
      return;
    }
    
    // 获取张三的参考图像
    const zhangsan = await prisma.character.findFirst({
      where: { name: '张三' }
    });
    
    if (!zhangsan || !zhangsan.generatedImages) {
      console.log('❌ 未找到张三的参考图像');
      return;
    }
    
    const images = JSON.parse(zhangsan.generatedImages);
    const referenceImage = images.front;
    
    if (!referenceImage) {
      console.log('❌ 张三没有正面参考图像');
      return;
    }
    
    console.log(`✅ 找到张三参考图像: ${referenceImage.substring(0, 50)}...`);
    
    // 测试多模态API调用
    const testPrompt = "张三在雪夜中巡逻，身穿军装，表情严肃";
    
    console.log('📡 测试多模态API调用...');
    
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${doubaoConfig.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: doubaoConfig.model,
        content: [
          {
            type: "text",
            text: testPrompt
          },
          {
            type: "image_url",
            image_url: {
              url: referenceImage
            }
          }
        ]
      })
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ 多模态API调用失败: ${response.status}`);
      console.error(`错误详情: ${errorText.substring(0, 500)}...`);
      
      // 如果多模态失败，测试纯文本调用
      console.log('\n🔄 回退测试纯文本API调用...');
      
      const textOnlyResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${doubaoConfig.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: doubaoConfig.model,
          content: [
            {
              type: "text",
              text: testPrompt
            }
          ]
        })
      });
      
      console.log(`纯文本API响应状态: ${textOnlyResponse.status}`);
      
      if (textOnlyResponse.ok) {
        const textResult = await textOnlyResponse.json();
        console.log('✅ 纯文本API调用成功');
        console.log(`任务ID: ${textResult.id || textResult.task_id || '未找到'}`);
      } else {
        const textError = await textOnlyResponse.text();
        console.error(`❌ 纯文本API也失败: ${textOnlyResponse.status}`);
        console.error(textError.substring(0, 200));
      }
      
      return;
    }
    
    const result = await response.json();
    console.log('✅ 多模态API调用成功!');
    console.log('📋 响应数据:', JSON.stringify(result, null, 2));
    
    const taskId = result.id || result.task_id || result.data?.task_id;
    if (taskId) {
      console.log(`🎯 多模态任务创建成功，任务ID: ${taskId}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoMultimodal();
