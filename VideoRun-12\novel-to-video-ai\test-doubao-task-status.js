const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoTaskStatus() {
  try {
    console.log('🔄 测试豆包任务状态查询API');
    console.log('=' .repeat(50));

    // 获取配置
    const config = await prisma.aIConfig.findFirst({
      where: {
        name: 'Doubao-Seedance-1.0-pro',
        enabled: true
      }
    });

    if (!config) {
      console.log('❌ 未找到配置');
      return;
    }

    // 使用刚才创建的任务ID
    const taskId = 'cgt-20250626162849-nvlpf';
    
    console.log('🎯 查询任务状态:');
    console.log('   任务ID:', taskId);
    console.log('   API密钥:', config.apiKey.substring(0, 8) + '...');

    // 测试任务状态查询API
    const statusUrl = `https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`;
    console.log('\n📡 查询URL:', statusUrl);

    const response = await fetch(statusUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('\n📊 响应状态:', response.status);
    console.log('📊 响应状态文本:', response.statusText);

    const responseText = await response.text();
    console.log('\n📄 原始响应:');
    console.log(responseText);

    if (!response.ok) {
      console.log('\n❌ 查询失败');
      
      // 尝试其他可能的API端点
      console.log('\n🔍 尝试其他API端点...');
      
      const alternativeUrls = [
        `https://ark.cn-beijing.volces.com/api/v3/contents/generations/${taskId}`,
        `https://ark.cn-beijing.volces.com/api/v3/tasks/${taskId}`,
        `https://ark.cn-beijing.volces.com/api/v3/generations/${taskId}`,
        `https://ark.cn-beijing.volces.com/api/v3/video/tasks/${taskId}`
      ];

      for (const url of alternativeUrls) {
        console.log(`\n🔗 尝试: ${url}`);
        try {
          const altResponse = await fetch(url, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${config.apiKey}`,
              'Content-Type': 'application/json'
            }
          });
          
          console.log('   状态:', altResponse.status);
          if (altResponse.ok) {
            const altText = await altResponse.text();
            console.log('   ✅ 成功响应:', altText);
            break;
          } else {
            const altText = await altResponse.text();
            console.log('   ❌ 失败响应:', altText.substring(0, 200));
          }
        } catch (e) {
          console.log('   ❌ 请求失败:', e.message);
        }
      }
      
      return;
    }

    // 解析成功响应
    try {
      const result = JSON.parse(responseText);
      console.log('\n✅ 查询成功!');
      console.log('📋 任务状态:', JSON.stringify(result, null, 2));
      
      console.log('\n📊 状态分析:');
      console.log('   任务状态:', result.status || '未知');
      console.log('   创建时间:', result.created_at || '未知');
      console.log('   更新时间:', result.updated_at || '未知');
      
      if (result.output) {
        console.log('   输出信息:', result.output);
        if (result.output.video_url) {
          console.log('   🎬 视频URL:', result.output.video_url);
        }
      }
      
      if (result.error) {
        console.log('   ❌ 错误信息:', result.error);
      }
      
    } catch (e) {
      console.log('❌ 解析响应失败:', e.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoTaskStatus();
