const { PrismaClient } = require('@prisma/client');

async function updateDoubaoKey() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 更新豆包API密钥...');
    
    // 查找豆包视频配置
    const doubaoVideoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250622184757-q77k7',
        supportsVideo: true
      }
    });
    
    if (!doubaoVideoConfig) {
      console.log('❌ 未找到豆包视频配置');
      return;
    }
    
    console.log(`找到豆包视频配置: ${doubaoVideoConfig.name}`);
    console.log(`当前API密钥: ${doubaoVideoConfig.apiKey}`);
    
    // 这里需要您提供真实的豆包API密钥
    const realApiKey = 'YOUR_REAL_DOUBAO_API_KEY'; // 请替换为真实密钥
    
    if (realApiKey === 'YOUR_REAL_DOUBAO_API_KEY') {
      console.log('\n⚠️ 请先在脚本中设置真实的豆包API密钥！');
      console.log('编辑 update-doubao-key.js 文件，将 YOUR_REAL_DOUBAO_API_KEY 替换为真实密钥');
      return;
    }
    
    // 更新API密钥
    await prisma.aIConfig.update({
      where: { id: doubaoVideoConfig.id },
      data: { 
        apiKey: realApiKey,
        status: 'disconnected' // 重置状态，需要重新测试
      }
    });
    
    console.log('✅ 豆包API密钥更新成功！');
    console.log('\n📝 下一步:');
    console.log('1. 前往 AI配置 页面');
    console.log('2. 点击"测试连接"验证密钥');
    console.log('3. 确保连接状态变为"已连接"');
    console.log('4. 然后就可以生成视频了！');
    
  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateDoubaoKey();
