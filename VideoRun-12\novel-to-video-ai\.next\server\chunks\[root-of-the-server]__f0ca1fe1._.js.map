{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/retry-failed-segment/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { DeepSeekClient } from '@/lib/ai-clients'\nimport { AIServiceError } from '@/lib/errors'\n\n// POST - 重试失败的视频片段\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { segmentId } = body\n\n    if (!segmentId) {\n      return NextResponse.json(\n        { success: false, error: '片段ID不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 查找失败的片段\n    const segment = await prisma.videoSegment.findUnique({\n      where: { id: segmentId },\n      include: {\n        storyVideo: {\n          include: {\n            episode: true\n          }\n        }\n      }\n    })\n\n    if (!segment) {\n      return NextResponse.json(\n        { success: false, error: '片段不存在' },\n        { status: 404 }\n      )\n    }\n\n    if (segment.status !== 'failed') {\n      return NextResponse.json(\n        { success: false, error: '只能重试失败的片段' },\n        { status: 400 }\n      )\n    }\n\n    console.log(`🔄 开始重试片段: ${segment.title}`)\n\n    // 获取AI配置\n    const aiConfig = await prisma.aIConfig.findFirst({\n      where: { enabled: true }\n    })\n\n    if (!aiConfig) {\n      return NextResponse.json(\n        { success: false, error: '请先配置AI模型' },\n        { status: 400 }\n      )\n    }\n\n    // 重置片段状态为generating\n    await prisma.videoSegment.update({\n      where: { id: segmentId },\n      data: {\n        status: 'generating',\n        videoUrl: null,\n        metadata: JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          retryAt: new Date().toISOString(),\n          retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1,\n          startTime: new Date().toISOString()\n        })\n      }\n    })\n\n    // 异步重新生成单个片段\n    retrySingleSegmentAsync(aiConfig, segment)\n      .then(() => {\n        console.log(`✅ 片段重试完成: ${segment.title}`)\n      })\n      .catch((error) => {\n        console.error(`❌ 片段重试失败: ${segment.title}`, error)\n      })\n\n    return NextResponse.json({\n      success: true,\n      message: '片段重试已启动',\n      data: {\n        segmentId: segmentId,\n        status: 'generating'\n      }\n    })\n\n  } catch (error) {\n    console.error('重试失败片段失败:', error)\n    return NextResponse.json(\n      { success: false, error: '重试失败，请稍后再试' },\n      { status: 500 }\n    )\n  }\n}\n\n// 异步重试单个片段\nasync function retrySingleSegmentAsync(aiConfig: any, segment: any) {\n  try {\n    console.log(`🎬 开始重新生成片段: ${segment.title}`)\n\n    // 调用视频生成函数\n    const videoResult = await generateSingleSegment(aiConfig, segment)\n\n    // 根据生成结果更新片段信息\n    const updateData: any = {\n      duration: videoResult.duration,\n      metadata: JSON.stringify({\n        ...JSON.parse(segment.metadata || '{}'),\n        retryAt: new Date().toISOString(),\n        generatedAt: new Date().toISOString(),\n        optimizedPrompt: videoResult.optimizedPrompt,\n        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1\n      })\n    }\n\n    // 根据生成结果更新状态和URL\n    if (videoResult.status === 'completed' && videoResult.videoUrl) {\n      updateData.videoUrl = videoResult.videoUrl\n      updateData.thumbnailUrl = videoResult.thumbnailUrl\n      updateData.status = 'completed'\n      console.log(`✅ 片段重试成功: ${segment.title}`)\n    } else if (videoResult.status === 'generating') {\n      updateData.status = 'generating'\n      updateData.metadata = JSON.stringify({\n        ...JSON.parse(segment.metadata || '{}'),\n        taskId: videoResult.taskId,\n        retryAt: new Date().toISOString(),\n        generatedAt: new Date().toISOString(),\n        optimizedPrompt: videoResult.optimizedPrompt,\n        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1\n      })\n      console.log(`🔄 片段重试已提交，等待完成: ${segment.title}`)\n    } else if (videoResult.status === 'failed') {\n      updateData.status = 'failed'\n      updateData.metadata = JSON.stringify({\n        ...JSON.parse(segment.metadata || '{}'),\n        error: videoResult.error,\n        retryAt: new Date().toISOString(),\n        generatedAt: new Date().toISOString(),\n        optimizedPrompt: videoResult.optimizedPrompt,\n        retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1\n      })\n      console.log(`❌ 片段重试失败: ${videoResult.error}`)\n    } else {\n      updateData.status = 'pending'\n      console.log(`⏳ 片段重试状态待定`)\n    }\n\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: updateData\n    })\n\n  } catch (error) {\n    console.error(`❌ 片段重试异常:`, error)\n\n    // 更新片段状态为失败\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: {\n        status: 'failed',\n        metadata: JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          error: error instanceof Error ? error.message : '重试失败',\n          retryAt: new Date().toISOString(),\n          failedAt: new Date().toISOString(),\n          retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1\n        })\n      }\n    })\n  }\n}\n\n// 生成单个视频片段\nasync function generateSingleSegment(aiConfig: any, segment: any) {\n  try {\n    // 1. 优化片段提示词\n    const deepSeekClient = new DeepSeekClient(aiConfig)\n    const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：\n\n片段标题：${segment.title}\n片段描述：${segment.description}\n原始提示词：${segment.prompt}\n\n请按照以下要求优化：\n1. 确保描述具体且可视化\n2. 添加适合的镜头运动和角度\n3. 强调画面质量和风格\n4. 控制在100字以内\n5. 适合${segment.segmentType}类型的片段\n\n优化后的提示词：`\n\n    const optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800)\n\n    // 2. 调用视频生成API\n    const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment)\n\n    return {\n      videoUrl: videoResult.videoUrl,\n      thumbnailUrl: videoResult.thumbnailUrl,\n      optimizedPrompt: optimizedPrompt.trim(),\n      duration: videoResult.duration || segment.duration || 15,\n      status: videoResult.status || 'completed',\n      taskId: videoResult.taskId\n    }\n  } catch (error) {\n    console.error('单个视频片段生成失败:', error)\n    return {\n      videoUrl: null,\n      thumbnailUrl: null,\n      optimizedPrompt: segment.prompt,\n      duration: segment.duration || 15,\n      status: 'failed',\n      error: error instanceof Error ? error.message : '生成失败'\n    }\n  }\n}\n\n// 调用视频生成API - 简化版本，直接调用通义万相\nasync function callVideoGenerationAPI(prompt: string, segment: any) {\n  try {\n    // 获取通义万相配置\n    const tongyiConfig = await prisma.aIConfig.findFirst({\n      where: {\n        enabled: true,\n        provider: 'tongyi'\n      }\n    })\n\n    if (!tongyiConfig) {\n      throw new Error('没有找到可用的通义万相配置')\n    }\n\n    console.log(`🎬 使用通义万相生成片段: ${segment.title}`)\n\n    // 优化提示词\n    const optimizedPrompt = optimizePrompt(prompt)\n    console.log(`片段 ${segment.segmentIndex} 提示词优化:`)\n    console.log(`  原始: ${prompt.substring(0, 100)}...`)\n    console.log(`  优化: ${optimizedPrompt.substring(0, 100)}...`)\n\n    // 创建视频生成任务\n    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${tongyiConfig.apiKey}`,\n        'Content-Type': 'application/json',\n        'X-DashScope-Async': 'enable'\n      },\n      body: JSON.stringify({\n        model: tongyiConfig.model,\n        input: {\n          prompt: optimizedPrompt\n        },\n        parameters: {\n          size: '1280*720',\n          duration: 5,\n          prompt_extend: true\n        }\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error(`通义万相API调用失败: ${response.status} ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    console.log('通义万相API响应:', JSON.stringify(result, null, 2))\n\n    if (result.output && result.output.task_id) {\n      const taskId = result.output.task_id\n      console.log(`通义万相任务创建成功，任务ID: ${taskId}`)\n\n      // 等待任务完成\n      console.log(`⏳ 等待通义万相任务完成: ${taskId}`)\n      const finalResult = await waitForTongyiTask(tongyiConfig, taskId, segment)\n\n      return finalResult\n    } else {\n      throw new Error(`通义万相API返回格式异常: ${JSON.stringify(result)}`)\n    }\n  } catch (error) {\n    console.error('调用视频生成API失败:', error)\n    throw error\n  }\n}\n\n// 优化提示词\nfunction optimizePrompt(prompt: string): string {\n  // 基础优化规则\n  let optimized = prompt\n    .replace(/[，。！？；：]/g, ', ') // 中文标点转英文\n    .replace(/\\s+/g, ' ') // 多个空格合并\n    .trim()\n\n  // 添加质量提升关键词\n  const qualityKeywords = [\n    'high quality',\n    'cinematic',\n    'detailed',\n    'professional lighting'\n  ]\n\n  // 检查是否已包含质量关键词\n  const hasQualityKeywords = qualityKeywords.some(keyword =>\n    optimized.toLowerCase().includes(keyword.toLowerCase())\n  )\n\n  if (!hasQualityKeywords) {\n    optimized += ', high quality, cinematic, detailed'\n  }\n\n  // 限制长度\n  if (optimized.length > 200) {\n    optimized = optimized.substring(0, 197) + '...'\n  }\n\n  return optimized\n}\n\n// 等待片段完成\nasync function waitForSegmentCompletion(segmentId: string, taskId: string, maxWaitTime: number) {\n  const startTime = Date.now()\n  const pollInterval = 5000 // 5秒轮询一次\n\n  while (Date.now() - startTime < maxWaitTime) {\n    try {\n      // 检查片段状态\n      const segment = await prisma.videoSegment.findUnique({\n        where: { id: segmentId }\n      })\n\n      if (!segment) {\n        console.log(`片段 ${segmentId} 不存在，停止等待`)\n        break\n      }\n\n      if (segment.status === 'completed') {\n        console.log(`✅ 片段 ${segmentId} 已完成`)\n        break\n      } else if (segment.status === 'failed') {\n        console.log(`❌ 片段 ${segmentId} 生成失败`)\n        break\n      }\n\n      console.log(`⏳ 片段 ${segmentId} 仍在生成中，继续等待...`)\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n\n    } catch (error) {\n      console.error(`等待片段完成时出错:`, error)\n      break\n    }\n  }\n\n  if (Date.now() - startTime >= maxWaitTime) {\n    console.log(`⏰ 片段 ${segmentId} 等待超时`)\n  }\n}\n\n// 等待通义万相任务完成\nasync function waitForTongyiTask(tongyiConfig: any, taskId: string, segment: any) {\n  const maxAttempts = 60 // 最多查询60次\n  const pollInterval = 5000 // 每5秒查询一次\n\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      console.log(`🔍 查询通义万相任务状态 (${attempt}/${maxAttempts}): ${taskId}`)\n\n      const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${tongyiConfig.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (!response.ok) {\n        throw new Error(`查询任务状态失败: ${response.status} ${response.statusText}`)\n      }\n\n      const result = await response.json()\n      console.log(`📊 任务状态查询结果:`, JSON.stringify(result, null, 2))\n\n      if (result.output && result.output.task_status) {\n        const status = result.output.task_status\n\n        if (status === 'SUCCEEDED') {\n          console.log(`✅ 通义万相任务完成: ${taskId}`)\n\n          if (result.output.video_url) {\n            return {\n              videoUrl: result.output.video_url,\n              thumbnailUrl: result.output.thumbnail_url || null,\n              duration: 5,\n              status: 'completed',\n              taskId: taskId,\n              provider: 'tongyi'\n            }\n          } else {\n            throw new Error('任务完成但没有返回视频URL')\n          }\n        } else if (status === 'FAILED') {\n          console.log(`❌ 通义万相任务失败: ${taskId}`)\n          throw new Error(`任务失败: ${result.output.message || '未知错误'}`)\n        } else if (status === 'PENDING' || status === 'RUNNING') {\n          console.log(`⏳ 任务仍在处理中: ${status}`)\n          // 继续等待\n        } else {\n          console.log(`❓ 未知任务状态: ${status}`)\n        }\n      } else {\n        console.log(`❓ 任务状态查询返回格式异常`)\n      }\n\n      // 等待下次查询\n      if (attempt < maxAttempts) {\n        await new Promise(resolve => setTimeout(resolve, pollInterval))\n      }\n\n    } catch (error) {\n      console.error(`❌ 查询任务状态异常 (${attempt}/${maxAttempts}):`, error)\n\n      if (attempt === maxAttempts) {\n        throw error\n      }\n\n      // 等待后重试\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n  }\n\n  throw new Error(`任务超时: 等待${maxAttempts * pollInterval / 1000}秒后仍未完成`)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;;;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,GAAG;QAEtB,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,MAAM,KAAK,UAAU;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,KAAK,EAAE;QAEzC,SAAS;QACT,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ,QAAQ;gBACR,UAAU;gBACV,UAAU,KAAK,SAAS,CAAC;oBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,SAAS,IAAI,OAAO,WAAW;oBAC/B,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,UAAU,IAAI,CAAC,IAAI;oBACrE,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF;QAEA,aAAa;QACb,wBAAwB,UAAU,SAC/B,IAAI,CAAC;YACJ,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,KAAK,EAAE;QAC1C,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,QAAQ,KAAK,EAAE,EAAE;QAC9C;QAEF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,QAAQ;YACV;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,eAAe,wBAAwB,QAAa,EAAE,OAAY;IAChE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,KAAK,EAAE;QAE3C,WAAW;QACX,MAAM,cAAc,MAAM,sBAAsB,UAAU;QAE1D,eAAe;QACf,MAAM,aAAkB;YACtB,UAAU,YAAY,QAAQ;YAC9B,UAAU,KAAK,SAAS,CAAC;gBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;gBACvC,SAAS,IAAI,OAAO,WAAW;gBAC/B,aAAa,IAAI,OAAO,WAAW;gBACnC,iBAAiB,YAAY,eAAe;gBAC5C,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,UAAU,IAAI,CAAC,IAAI;YACvE;QACF;QAEA,iBAAiB;QACjB,IAAI,YAAY,MAAM,KAAK,eAAe,YAAY,QAAQ,EAAE;YAC9D,WAAW,QAAQ,GAAG,YAAY,QAAQ;YAC1C,WAAW,YAAY,GAAG,YAAY,YAAY;YAClD,WAAW,MAAM,GAAG;YACpB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,KAAK,EAAE;QAC1C,OAAO,IAAI,YAAY,MAAM,KAAK,cAAc;YAC9C,WAAW,MAAM,GAAG;YACpB,WAAW,QAAQ,GAAG,KAAK,SAAS,CAAC;gBACnC,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;gBACvC,QAAQ,YAAY,MAAM;gBAC1B,SAAS,IAAI,OAAO,WAAW;gBAC/B,aAAa,IAAI,OAAO,WAAW;gBACnC,iBAAiB,YAAY,eAAe;gBAC5C,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,UAAU,IAAI,CAAC,IAAI;YACvE;YACA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,KAAK,EAAE;QACjD,OAAO,IAAI,YAAY,MAAM,KAAK,UAAU;YAC1C,WAAW,MAAM,GAAG;YACpB,WAAW,QAAQ,GAAG,KAAK,SAAS,CAAC;gBACnC,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;gBACvC,OAAO,YAAY,KAAK;gBACxB,SAAS,IAAI,OAAO,WAAW;gBAC/B,aAAa,IAAI,OAAO,WAAW;gBACnC,iBAAiB,YAAY,eAAe;gBAC5C,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,UAAU,IAAI,CAAC,IAAI;YACvE;YACA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY,KAAK,EAAE;QAC9C,OAAO;YACL,WAAW,MAAM,GAAG;YACpB,QAAQ,GAAG,CAAC,CAAC,UAAU,CAAC;QAC1B;QAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE;QAE3B,YAAY;QACZ,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;gBACJ,QAAQ;gBACR,UAAU,KAAK,SAAS,CAAC;oBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,SAAS,IAAI,OAAO,WAAW;oBAC/B,UAAU,IAAI,OAAO,WAAW;oBAChC,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,MAAM,UAAU,IAAI,CAAC,IAAI;gBACvE;YACF;QACF;IACF;AACF;AAEA,WAAW;AACX,eAAe,sBAAsB,QAAa,EAAE,OAAY;IAC9D,IAAI;QACF,aAAa;QACb,MAAM,iBAAiB,IAAI,eAAe;QAC1C,MAAM,qBAAqB,CAAC;;KAE3B,EAAE,QAAQ,KAAK,CAAC;KAChB,EAAE,QAAQ,WAAW,CAAC;MACrB,EAAE,QAAQ,MAAM,CAAC;;;;;;;KAOlB,EAAE,QAAQ,WAAW,CAAC;;QAEnB,CAAC;QAEL,MAAM,kBAAkB,MAAM,eAAe,OAAO,CAAC,oBAAoB;QAEzE,eAAe;QACf,MAAM,cAAc,MAAM,uBAAuB,gBAAgB,IAAI,IAAI;QAEzE,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,cAAc,YAAY,YAAY;YACtC,iBAAiB,gBAAgB,IAAI;YACrC,UAAU,YAAY,QAAQ,IAAI,QAAQ,QAAQ,IAAI;YACtD,QAAQ,YAAY,MAAM,IAAI;YAC9B,QAAQ,YAAY,MAAM;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YACL,UAAU;YACV,cAAc;YACd,iBAAiB,QAAQ,MAAM;YAC/B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA,4BAA4B;AAC5B,eAAe,uBAAuB,MAAc,EAAE,OAAY;IAChE,IAAI;QACF,WAAW;QACX,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,KAAK,EAAE;QAE7C,QAAQ;QACR,MAAM,kBAAkB,eAAe;QACvC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC;QAC/C,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAClD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QAE3D,WAAW;QACX,MAAM,WAAW,MAAM,MAAM,wFAAwF;YACnH,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,MAAM,EAAE;gBAChD,gBAAgB;gBAChB,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,aAAa,KAAK;gBACzB,OAAO;oBACL,QAAQ;gBACV;gBACA,YAAY;oBACV,MAAM;oBACN,UAAU;oBACV,eAAe;gBACjB;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC1E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,cAAc,KAAK,SAAS,CAAC,QAAQ,MAAM;QAEvD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE;YAC1C,MAAM,SAAS,OAAO,MAAM,CAAC,OAAO;YACpC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ;YAExC,SAAS;YACT,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;YACrC,MAAM,cAAc,MAAM,kBAAkB,cAAc,QAAQ;YAElE,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,KAAK,SAAS,CAAC,SAAS;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM;IACR;AACF;AAEA,QAAQ;AACR,SAAS,eAAe,MAAc;IACpC,SAAS;IACT,IAAI,YAAY,OACb,OAAO,CAAC,aAAa,MAAM,UAAU;KACrC,OAAO,CAAC,QAAQ,KAAK,SAAS;KAC9B,IAAI;IAEP,YAAY;IACZ,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;KACD;IAED,eAAe;IACf,MAAM,qBAAqB,gBAAgB,IAAI,CAAC,CAAA,UAC9C,UAAU,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;IAGtD,IAAI,CAAC,oBAAoB;QACvB,aAAa;IACf;IAEA,OAAO;IACP,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,YAAY,UAAU,SAAS,CAAC,GAAG,OAAO;IAC5C;IAEA,OAAO;AACT;AAEA,SAAS;AACT,eAAe,yBAAyB,SAAiB,EAAE,MAAc,EAAE,WAAmB;IAC5F,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,eAAe,KAAK,SAAS;;IAEnC,MAAO,KAAK,GAAG,KAAK,YAAY,YAAa;QAC3C,IAAI;YACF,SAAS;YACT,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBACnD,OAAO;oBAAE,IAAI;gBAAU;YACzB;YAEA,IAAI,CAAC,SAAS;gBACZ,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,UAAU,SAAS,CAAC;gBACtC;YACF;YAEA,IAAI,QAAQ,MAAM,KAAK,aAAa;gBAClC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC;gBACnC;YACF,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU;gBACtC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,KAAK,CAAC;gBACpC;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,cAAc,CAAC;YAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,CAAC,EAAE;YAC5B;QACF;IACF;IAEA,IAAI,KAAK,GAAG,KAAK,aAAa,aAAa;QACzC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,KAAK,CAAC;IACtC;AACF;AAEA,aAAa;AACb,eAAe,kBAAkB,YAAiB,EAAE,MAAc,EAAE,OAAY;IAC9E,MAAM,cAAc,GAAG,UAAU;;IACjC,MAAM,eAAe,KAAK,UAAU;;IAEpC,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,YAAY,GAAG,EAAE,QAAQ;YAElE,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,QAAQ,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,MAAM,EAAE;oBAChD,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YACvE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM;YAEzD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,WAAW,EAAE;gBAC9C,MAAM,SAAS,OAAO,MAAM,CAAC,WAAW;gBAExC,IAAI,WAAW,aAAa;oBAC1B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;oBAEnC,IAAI,OAAO,MAAM,CAAC,SAAS,EAAE;wBAC3B,OAAO;4BACL,UAAU,OAAO,MAAM,CAAC,SAAS;4BACjC,cAAc,OAAO,MAAM,CAAC,aAAa,IAAI;4BAC7C,UAAU;4BACV,QAAQ;4BACR,QAAQ;4BACR,UAAU;wBACZ;oBACF,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO,IAAI,WAAW,UAAU;oBAC9B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;oBACnC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC,OAAO,IAAI,QAAQ;gBAC5D,OAAO,IAAI,WAAW,aAAa,WAAW,WAAW;oBACvD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ;gBAClC,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ;gBACnC;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC;YAC9B;YAEA,SAAS;YACT,IAAI,UAAU,aAAa;gBACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE;YAEzD,IAAI,YAAY,aAAa;gBAC3B,MAAM;YACR;YAEA,QAAQ;YACR,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,cAAc,eAAe,KAAK,MAAM,CAAC;AACtE", "debugId": null}}]}