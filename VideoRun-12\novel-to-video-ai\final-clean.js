const { PrismaClient } = require('@prisma/client');

async function finalClean() {
  const prisma = new PrismaClient();
  
  try {
    // 等待10秒让服务器启动
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const result = await prisma.videoSegment.deleteMany({
      where: { status: 'failed' }
    });
    console.log('✅ 清理了', result.count, '个失败记录');
    
    // 测试服务器
    const response = await fetch('http://localhost:3000/api/models');
    if (response.ok) {
      console.log('✅ VideoRun服务器已就绪');
    } else {
      console.log('❌ VideoRun服务器未就绪');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

finalClean();
