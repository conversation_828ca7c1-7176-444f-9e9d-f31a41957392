
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  fileName: 'fileName',
  content: 'content',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIConfigScalarFieldEnum = {
  id: 'id',
  provider: 'provider',
  apiKey: 'apiKey',
  model: 'model',
  name: 'name',
  description: 'description',
  enabled: 'enabled',
  supportsVideo: 'supportsVideo',
  supportsImage: 'supportsImage',
  supportsImageToVideo: 'supportsImageToVideo',
  supportsTTS: 'supportsTTS',
  temperature: 'temperature',
  maxTokens: 'maxTokens',
  topP: 'topP',
  status: 'status',
  lastTest: 'lastTest',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CharacterScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  name: 'name',
  identity: 'identity',
  personality: 'personality',
  physique: 'physique',
  facial: 'facial',
  hairstyle: 'hairstyle',
  clothing: 'clothing',
  generatedImages: 'generatedImages',
  detailedDNA: 'detailedDNA',
  consistencyScore: 'consistencyScore',
  consistencySettings: 'consistencySettings',
  referenceImages: 'referenceImages',
  globalCharacterId: 'globalCharacterId',
  isGlobalCharacter: 'isGlobalCharacter',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EpisodeScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  title: 'title',
  content: 'content',
  orderIndex: 'orderIndex',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlotInfoScalarFieldEnum = {
  id: 'id',
  episodeId: 'episodeId',
  characters: 'characters',
  scenes: 'scenes',
  events: 'events',
  detailedCharacters: 'detailedCharacters',
  detailedScenes: 'detailedScenes',
  plotSequences: 'plotSequences',
  emotionalArc: 'emotionalArc',
  generatedPrompt: 'generatedPrompt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoryVideoScalarFieldEnum = {
  id: 'id',
  episodeId: 'episodeId',
  projectId: 'projectId',
  prompt: 'prompt',
  videoUrl: 'videoUrl',
  status: 'status',
  mergedVideoUrl: 'mergedVideoUrl',
  totalDuration: 'totalDuration',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VideoSegmentScalarFieldEnum = {
  id: 'id',
  storyVideoId: 'storyVideoId',
  episodeId: 'episodeId',
  projectId: 'projectId',
  segmentIndex: 'segmentIndex',
  title: 'title',
  description: 'description',
  prompt: 'prompt',
  videoUrl: 'videoUrl',
  thumbnailUrl: 'thumbnailUrl',
  duration: 'duration',
  status: 'status',
  segmentType: 'segmentType',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ConsistencyValidationScalarFieldEnum = {
  id: 'id',
  characterId: 'characterId',
  episodeId: 'episodeId',
  textConsistencyScore: 'textConsistencyScore',
  imageConsistencyScore: 'imageConsistencyScore',
  overallScore: 'overallScore',
  validationDetails: 'validationDetails',
  issuesFound: 'issuesFound',
  textWeight: 'textWeight',
  imageWeight: 'imageWeight',
  generatedImageUrl: 'generatedImageUrl',
  createdAt: 'createdAt'
};

exports.Prisma.VideoGenerationScalarFieldEnum = {
  id: 'id',
  episodeId: 'episodeId',
  prompt: 'prompt',
  status: 'status',
  videoUrl: 'videoUrl',
  consistencyMode: 'consistencyMode',
  includeReferenceImages: 'includeReferenceImages',
  style: 'style',
  quality: 'quality',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CharacterVoiceScalarFieldEnum = {
  id: 'id',
  characterId: 'characterId',
  ttsConfigId: 'ttsConfigId',
  voiceId: 'voiceId',
  voiceName: 'voiceName',
  basePitch: 'basePitch',
  baseSpeed: 'baseSpeed',
  baseVolume: 'baseVolume',
  emotionMappings: 'emotionMappings',
  sampleAudioUrl: 'sampleAudioUrl',
  enabled: 'enabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AudioFileScalarFieldEnum = {
  id: 'id',
  episodeId: 'episodeId',
  segmentIndex: 'segmentIndex',
  dialogueType: 'dialogueType',
  characterId: 'characterId',
  text: 'text',
  emotion: 'emotion',
  audioUrl: 'audioUrl',
  duration: 'duration',
  fileSize: 'fileSize',
  format: 'format',
  status: 'status',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Project: 'Project',
  AIConfig: 'AIConfig',
  Character: 'Character',
  Episode: 'Episode',
  PlotInfo: 'PlotInfo',
  StoryVideo: 'StoryVideo',
  VideoSegment: 'VideoSegment',
  ConsistencyValidation: 'ConsistencyValidation',
  VideoGeneration: 'VideoGeneration',
  CharacterVoice: 'CharacterVoice',
  AudioFile: 'AudioFile'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
