import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')
    
    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId参数' },
        { status: 400 }
      )
    }

    // 获取视频片段详细信息
    const segments = await prisma.videoSegment.findMany({
      where: { episodeId },
      orderBy: { segmentIndex: 'asc' },
      select: {
        id: true,
        segmentIndex: true,
        title: true,
        status: true,
        duration: true,
        videoUrl: true,
        metadata: true,
        createdAt: true,
        updatedAt: true
      }
    })

    // 计算详细进度信息
    const totalSegments = segments.length
    const completedSegments = segments.filter(s => s.status === 'completed').length
    const generatingSegments = segments.filter(s => s.status === 'generating').length
    const failedSegments = segments.filter(s => s.status === 'failed').length
    const pendingSegments = segments.filter(s => s.status === 'pending').length

    // 计算进度百分比
    const progress = totalSegments > 0 ? Math.round((completedSegments / totalSegments) * 100) : 0

    // 估算剩余时间（基于已完成片段的平均生成时间）
    let estimatedRemainingTime = null
    const completedWithMetadata = segments.filter(s => {
      if (s.status !== 'completed' || !s.metadata) return false
      try {
        const metadata = JSON.parse(s.metadata as string)
        return metadata.generationTime && metadata.completedAt
      } catch {
        return false
      }
    })

    if (completedWithMetadata.length > 0) {
      const avgGenerationTime = completedWithMetadata.reduce((sum, segment) => {
        try {
          const metadata = JSON.parse(segment.metadata as string)
          return sum + (metadata.generationTime || 0)
        } catch {
          return sum
        }
      }, 0) / completedWithMetadata.length

      const remainingSegments = totalSegments - completedSegments
      estimatedRemainingTime = Math.round((avgGenerationTime * remainingSegments) / 1000) // 转换为秒
    }

    // 获取当前处理的片段信息
    const currentProcessingSegments = segments
      .filter(s => s.status === 'generating')
      .map(s => ({
        index: s.segmentIndex,
        title: s.title,
        startTime: s.updatedAt,
        metadata: s.metadata ? JSON.parse(s.metadata as string) : null
      }))

    // 计算总视频大小
    let totalVideoSize = 0
    let totalVideoDuration = 0
    segments.forEach(segment => {
      if (segment.status === 'completed' && segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata as string)
          if (metadata.videoSize && typeof metadata.videoSize === 'number') {
            totalVideoSize += metadata.videoSize
          }
        } catch {}
      }
      if (segment.duration) {
        totalVideoDuration += segment.duration
      }
    })

    // 获取队列状态
    const queueStatus = {
      total: totalSegments,
      completed: completedSegments,
      processing: generatingSegments,
      pending: pendingSegments,
      failed: failedSegments
    }

    // 详细的片段信息
    const detailedSegments = segments.map(segment => {
      let metadata = null
      try {
        metadata = segment.metadata ? JSON.parse(segment.metadata as string) : null
      } catch {}

      return {
        id: segment.id,
        index: segment.segmentIndex,
        title: segment.title,
        status: segment.status,
        duration: segment.duration,
        hasVideo: !!segment.videoUrl,
        videoUrl: segment.videoUrl,
        createdAt: segment.createdAt,
        updatedAt: segment.updatedAt,
        metadata: {
          taskId: metadata?.taskId,
          provider: metadata?.provider,
          videoSize: metadata?.videoSize,
          responseTime: metadata?.responseTime,
          totalAttempts: metadata?.totalAttempts,
          generationTime: metadata?.generationTime,
          completedAt: metadata?.completedAt,
          error: metadata?.error
        }
      }
    })

    const response = {
      success: true,
      episodeId,
      progress: {
        percentage: progress,
        completed: completedSegments,
        total: totalSegments,
        estimatedRemainingTime,
        currentProcessing: currentProcessingSegments,
        queueStatus
      },
      statistics: {
        totalVideoSize,
        totalVideoDuration,
        averageSegmentDuration: totalSegments > 0 ? totalVideoDuration / totalSegments : 0
      },
      segments: detailedSegments,
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('获取视频生成进度失败:', error)
    return NextResponse.json(
      { success: false, error: '获取进度信息失败' },
      { status: 500 }
    )
  }
}
