const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function cleanAndTest() {
  try {
    // 清理失败记录
    const result = await prisma.videoSegment.deleteMany({
      where: { status: 'failed' }
    });
    console.log('✅ 已清理', result.count, '个失败记录');
    
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 测试服务器
    const response = await fetch('http://localhost:3000/api/models');
    if (response.ok) {
      console.log('✅ VideoRun服务器已启动');
    } else {
      console.log('❌ VideoRun服务器未就绪');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanAndTest();
