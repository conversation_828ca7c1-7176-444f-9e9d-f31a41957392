import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 获取角色的声音配置
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const characterId = params.id

    // 获取角色信息
    const character = await prisma.character.findUnique({
      where: { id: characterId }
    })

    if (!character) {
      return NextResponse.json(
        { error: '角色不存在' },
        { status: 404 }
      )
    }

    // 获取角色的声音配置
    const voiceConfigs = await prisma.characterVoice.findMany({
      where: { characterId },
      include: {
        ttsConfig: {
          select: {
            id: true,
            name: true,
            provider: true,
            model: true,
            enabled: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        character: {
          id: character.id,
          name: character.name,
          description: character.description
        },
        voiceConfigs
      }
    })

  } catch (error) {
    console.error('获取角色声音配置失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取声音配置失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 创建或更新角色的声音配置
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const characterId = params.id
    const body = await request.json()

    const {
      ttsConfigId,
      voiceId,
      voiceName,
      basePitch = 0,
      baseSpeed = 1.0,
      baseVolume = 80,
      emotionMappings,
      enabled = true
    } = body

    // 验证必需字段
    if (!ttsConfigId || !voiceId) {
      return NextResponse.json(
        { error: 'TTS配置ID和声音ID不能为空' },
        { status: 400 }
      )
    }

    // 检查角色是否存在
    const character = await prisma.character.findUnique({
      where: { id: characterId }
    })

    if (!character) {
      return NextResponse.json(
        { error: '角色不存在' },
        { status: 404 }
      )
    }

    // 检查TTS配置是否存在
    const ttsConfig = await prisma.aIConfig.findUnique({
      where: { id: ttsConfigId }
    })

    if (!ttsConfig || !ttsConfig.supportsTTS) {
      return NextResponse.json(
        { error: 'TTS配置不存在或不支持语音合成' },
        { status: 404 }
      )
    }

    // 检查是否已有配置
    const existingConfig = await prisma.characterVoice.findFirst({
      where: {
        characterId,
        ttsConfigId
      }
    })

    let voiceConfig
    if (existingConfig) {
      // 更新现有配置
      voiceConfig = await prisma.characterVoice.update({
        where: { id: existingConfig.id },
        data: {
          voiceId,
          voiceName,
          basePitch,
          baseSpeed,
          baseVolume,
          emotionMappings: emotionMappings ? JSON.stringify(emotionMappings) : null,
          enabled
        },
        include: {
          ttsConfig: {
            select: {
              id: true,
              name: true,
              provider: true,
              model: true
            }
          }
        }
      })
    } else {
      // 创建新配置
      voiceConfig = await prisma.characterVoice.create({
        data: {
          characterId,
          ttsConfigId,
          voiceId,
          voiceName,
          basePitch,
          baseSpeed,
          baseVolume,
          emotionMappings: emotionMappings ? JSON.stringify(emotionMappings) : null,
          enabled
        },
        include: {
          ttsConfig: {
            select: {
              id: true,
              name: true,
              provider: true,
              model: true
            }
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: voiceConfig
    })

  } catch (error) {
    console.error('保存角色声音配置失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '保存声音配置失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 删除角色的声音配置
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const characterId = params.id
    const { searchParams } = new URL(request.url)
    const voiceConfigId = searchParams.get('voiceConfigId')

    if (!voiceConfigId) {
      return NextResponse.json(
        { error: '声音配置ID不能为空' },
        { status: 400 }
      )
    }

    // 检查配置是否存在且属于该角色
    const voiceConfig = await prisma.characterVoice.findFirst({
      where: {
        id: voiceConfigId,
        characterId
      }
    })

    if (!voiceConfig) {
      return NextResponse.json(
        { error: '声音配置不存在' },
        { status: 404 }
      )
    }

    // 删除配置
    await prisma.characterVoice.delete({
      where: { id: voiceConfigId }
    })

    return NextResponse.json({
      success: true,
      message: '声音配置已删除'
    })

  } catch (error) {
    console.error('删除角色声音配置失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '删除声音配置失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
