# 双重约束的角色一致性系统设计方案

## 概述

本文档描述了一套融合文本约束和视觉约束的角色一致性解决方案，旨在解决小说转视频过程中同一角色在不同剧集、不同视频中外貌不一致的问题。

## 问题分析

### 当前系统的局限性

1. **角色描述过于简单**
   - 现有的 `facial?: string` 字段过于笼统
   - 缺乏具体的五官特征描述
   - AI模型基于模糊描述产生不同结果

2. **缺乏跨剧集一致性机制**
   - 每个剧集独立分析角色
   - 没有全局角色库管理
   - 无法保证同一角色的外貌延续性

3. **生成过程缺乏约束**
   - 视频生成时缺乏强约束条件
   - 没有质量验证机制
   - 依赖AI模型的"想象"而非标准

## 解决方案：双重约束系统

### 核心理念

**现有系统 + 文本约束 + 视觉约束 = 三重保障**

- 📖 **剧情约束**：利用现有的详细剧情提取策略，确保故事连贯性
- 📝 **文本约束**：通过极其详细和标准化的文字描述约束AI生成
- 🖼️ **视觉约束**：通过预设的标准头像图片约束AI生成
- 🔒 **三重验证**：剧情、文本、图像三者相互验证，确保最高一致性

### 与现有系统的融合优势

#### 1. **充分利用现有投资**
- ✅ **现有角色管理系统**：Character数据结构、CharacterManager组件
- ✅ **现有AI生成功能**：generate-appearance API、三视图生成
- ✅ **现有剧情提取策略**：DetailedPlotExtraction组件、角色DNA提取
- ✅ **现有用户界面**：CharacterDetail弹窗、编辑保存功能

#### 2. **渐进式升级路径**
- 🔄 **数据结构扩展**：在现有Character模型基础上添加字段
- 🔄 **API功能增强**：扩展现有API而非重建
- 🔄 **UI渐进增强**：在现有界面基础上添加一致性功能
- 🔄 **用户体验平滑**：保持现有操作习惯，新功能作为增强出现

## 现有系统集成方案

### 1. 现有角色系统的升级策略

#### 1.1 Character数据结构扩展
```typescript
// 基于现有Character接口的扩展
interface EnhancedCharacter extends Character {
  // 现有字段保持不变
  id: string
  projectId: string
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
  generatedImages?: {
    front?: string
    side?: string
    back?: string
  }

  // 新增：一致性相关字段
  detailedDNA?: DetailedCharacterDNA
  consistencyScore?: number
  consistencySettings?: ConsistencySettings
  referenceImages?: EnhancedReferenceImages
  isGlobalCharacter?: boolean
}

// 增强的参考图像结构
interface EnhancedReferenceImages {
  // 基于现有的三视图扩展
  front?: string    // 利用现有的正面图
  side?: string     // 利用现有的侧面图
  back?: string     // 利用现有的背面图

  // 新增：表情图像
  expressions?: {
    neutral?: string
    happy?: string
    sad?: string
    angry?: string
  }

  // 新增：一致性评分
  consistencyScore?: number
}
```

#### 1.2 现有组件的增强策略
```typescript
// 升级CharacterManager组件
export default function CharacterManager({ characters, onCharacterUpdate }: CharacterManagerProps) {
  // 现有逻辑保持不变
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  // 新增：一致性相关状态
  const [showConsistencyView, setShowConsistencyView] = useState(false)

  return (
    <div className="space-y-6">
      {/* 现有的角色统计保持不变 */}

      {/* 角色列表 - 在现有卡片基础上添加一致性指示器 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {characters.map((character) => (
          <div
            key={character.id}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer relative"
            onClick={() => openCharacterDetail(character)}
          >
            {/* 新增：一致性评分指示器 */}
            {character.consistencyScore && (
              <div className={`absolute top-2 right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                character.consistencyScore >= 0.8 ? 'bg-green-100 text-green-800' :
                character.consistencyScore >= 0.6 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {Math.round(character.consistencyScore * 100)}
              </div>
            )}

            {/* 现有的头像和信息保持不变 */}
            <div className="flex flex-col items-center text-center">
              {/* 头像 - 如果有参考图像则使用，否则使用默认图标 */}
              <div className="w-16 h-16 rounded-full mb-4 shadow-lg overflow-hidden">
                {character.referenceImages?.front || character.generatedImages?.front ? (
                  <img
                    src={character.referenceImages?.front || character.generatedImages?.front}
                    alt={character.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                    <User className="text-white" size={28} />
                  </div>
                )}
              </div>

              {/* 现有的名称和信息保持不变 */}
            </div>
          </div>
        ))}
      </div>

      {/* 现有的角色详情弹窗保持不变 */}
    </div>
  )
}
```

### 2. 现有剧情提取系统的集成

#### 2.1 DetailedPlotExtraction组件增强
```typescript
// 在现有DetailedPlotExtraction基础上添加一致性功能
export default function DetailedPlotExtraction({
  episodeId,
  episodeTitle,
  episodeContent,
  isOpen,
  onClose,
  onGenerateStoryVideo
}: DetailedPlotExtractionProps) {

  // 现有状态保持不变
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [characters, setCharacters] = useState<Character[]>([])
  const [scenes, setScenes] = useState<Scene[]>([])
  const [plotSequences, setPlotSequences] = useState<PlotSequence[]>([])
  const [emotionalArc, setEmotionalArc] = useState('')
  const [generatedPrompt, setGeneratedPrompt] = useState('')
  const [customPrompt, setCustomPrompt] = useState('')

  // 新增：一致性相关状态
  const [consistencyMode, setConsistencyMode] = useState<'plot' | 'consistency' | 'hybrid'>('hybrid')
  const [globalCharacters, setGlobalCharacters] = useState<EnhancedCharacter[]>([])
  const [consistencyAnalysis, setConsistencyAnalysis] = useState<ConsistencyAnalysis | null>(null)

  // 升级现有的分析函数
  const analyzeDetailedPlot = async () => {
    setIsAnalyzing(true)
    try {
      const response = await fetch('/api/ai/analyze-detailed-plot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          episodeId,
          episodeContent,
          customPrompt,
          // 新增：一致性相关参数
          consistencyMode,
          useGlobalCharacters: true,
          syncWithGlobalLibrary: true
        })
      })

      const result = await response.json()

      if (result.success) {
        // 现有数据设置保持不变
        setCharacters(result.data.characters || [])
        setScenes(result.data.scenes || [])
        setPlotSequences(result.data.plotSequences || [])
        setEmotionalArc(result.data.emotionalArc || '')
        setGeneratedPrompt(result.data.generatedPrompt || '')

        // 新增：一致性相关数据
        setGlobalCharacters(result.data.globalCharacters || [])
        setConsistencyAnalysis(result.data.consistencyAnalysis || null)
      }
    } catch (error) {
      console.error('分析剧情失败:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 现有头部保持不变 */}

        <div className="flex h-[calc(90vh-80px)]">
          {/* 左侧：剧情分析结果 - 在现有基础上添加一致性分析 */}
          <div className="w-2/3 p-6 overflow-y-auto border-r border-gray-200">
            {/* 现有的剧情分析内容保持不变 */}

            {/* 新增：角色一致性分析 */}
            {globalCharacters.length > 0 && (
              <div>
                <div className="flex items-center mb-3">
                  <Users className="text-purple-600 mr-2" size={20} />
                  <h3 className="text-lg font-medium text-gray-900">角色一致性分析</h3>
                </div>
                <div className="space-y-3">
                  {globalCharacters.map((character, index) => (
                    <div key={index} className="bg-purple-50 border border-purple-200 rounded-md p-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-purple-900">{character.name}</h4>
                        <div className="flex items-center space-x-2">
                          {character.referenceImages?.front && (
                            <img
                              src={character.referenceImages.front}
                              alt={character.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          )}
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            (character.consistencyScore || 0) >= 0.8 ? 'bg-green-100 text-green-800' :
                            (character.consistencyScore || 0) >= 0.6 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            一致性: {((character.consistencyScore || 0) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="mt-2 text-sm text-purple-800">
                        <p><strong>剧情描述：</strong>{character.facial}</p>
                        {character.detailedDNA && (
                          <p><strong>DNA特征：</strong>
                            {character.detailedDNA.facial?.faceShape}, {character.detailedDNA.facial?.eyeShape}
                          </p>
                        )}
                        {character.detailedDNA?.uniqueIdentifiers && (
                          <p><strong>独特标识：</strong>
                            {character.detailedDNA.uniqueIdentifiers.join(', ')}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 右侧：操作面板 - 在现有基础上添加一致性控制 */}
          <div className="w-1/3 p-6 bg-gray-50">
            <div className="space-y-6">
              {/* 现有的重新分析按钮保持不变 */}

              {/* 新增：一致性模式选择 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">一致性模式</h3>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="plot"
                      checked={consistencyMode === 'plot'}
                      onChange={(e) => setConsistencyMode(e.target.value as any)}
                      className="mr-2"
                    />
                    <span className="text-sm">剧情优先模式</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="consistency"
                      checked={consistencyMode === 'consistency'}
                      onChange={(e) => setConsistencyMode(e.target.value as any)}
                      className="mr-2"
                    />
                    <span className="text-sm">一致性优先模式</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="hybrid"
                      checked={consistencyMode === 'hybrid'}
                      onChange={(e) => setConsistencyMode(e.target.value as any)}
                      className="mr-2"
                    />
                    <span className="text-sm">混合模式（推荐）</span>
                  </label>
                </div>
              </div>

              {/* 现有的自定义增强要求保持不变 */}

              {/* 现有的模型选择和生成按钮保持不变 */}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
```

#### 2.2 现有API的增强策略
```typescript
// 升级现有的analyze-detailed-plot API
export async function POST(request: NextRequest) {
  try {
    const {
      episodeId,
      episodeContent,
      customPrompt,
      // 新增参数
      consistencyMode = 'hybrid',
      useGlobalCharacters = true,
      syncWithGlobalLibrary = true
    } = await request.json()

    // 1. 现有的剧情分析逻辑保持不变
    const plotAnalysis = await analyzeEpisodePlot(episodeContent, customPrompt)

    // 2. 新增：全局角色库集成
    let globalCharacters = []
    let consistencyAnalysis = null

    if (useGlobalCharacters) {
      // 查找或创建全局角色
      globalCharacters = await syncWithGlobalCharacterLibrary(
        plotAnalysis.characters,
        episodeId,
        consistencyMode
      )

      // 生成一致性分析
      consistencyAnalysis = await generateConsistencyAnalysis(
        plotAnalysis,
        globalCharacters,
        consistencyMode
      )
    }

    // 3. 基于一致性模式增强提示词
    const enhancedPrompt = await enhancePromptWithConsistency(
      plotAnalysis.generatedPrompt,
      globalCharacters,
      consistencyMode
    )

    return NextResponse.json({
      success: true,
      data: {
        // 现有数据保持不变
        characters: plotAnalysis.characters,
        scenes: plotAnalysis.scenes,
        plotSequences: plotAnalysis.plotSequences,
        emotionalArc: plotAnalysis.emotionalArc,
        generatedPrompt: enhancedPrompt,

        // 新增数据
        globalCharacters,
        consistencyAnalysis,
        consistencyMode
      }
    })
  } catch (error) {
    console.error('详细剧情分析失败:', error)
    return NextResponse.json({
      success: false,
      error: '详细剧情分析失败'
    }, { status: 500 })
  }
}
```

### 系统架构

```typescript
interface UnifiedCharacterConsistency {
  // 文本约束部分
  detailedDNA: {
    facial: DetailedFacialFeatures
    physique: DetailedPhysique
    clothing: DetailedClothing
    uniqueIdentifiers: string[]  // 独特标识
  }
  
  // 视觉约束部分
  referenceImages: {
    standardPortrait: string     // 标准头像
    expressions: ExpressionSet   // 不同表情
    angles: AngleSet            // 不同角度
  }
  
  // 融合生成策略
  generationStrategy: {
    textWeight: number          // 文本约束权重
    imageWeight: number         // 图像约束权重
    consistencyThreshold: number // 一致性阈值
  }
}
```

## 详细设计

### 1. 结构化角色DNA

```typescript
interface DetailedFacialFeatures {
  faceShape: string      // 脸型：圆脸/方脸/瓜子脸等
  eyeShape: string       // 眼型：丹凤眼/杏眼/桃花眼等
  eyeColor: string       // 眼色：黑色/棕色等
  noseShape: string      // 鼻型：高挺/小巧等
  mouthShape: string     // 嘴型：薄唇/厚唇等
  skinTone: string       // 肤色：白皙/小麦色等
  facialFeatures: string // 其他特征：酒窝/雀斑/痣等
}

interface DetailedPhysique {
  height: string         // 身高描述
  build: string          // 体型：纤细/健壮等
  posture: string        // 体态特征
}

interface DetailedClothing {
  style: string          // 服装风格
  colors: string[]       // 常用颜色
  accessories: string[]  // 配饰特征
}
```

### 2. 参考图像系统

```typescript
interface ReferenceImageSet {
  standardPortrait: string    // 标准正面头像
  profileView: string        // 侧面头像
  fullBody: string          // 全身图
  expressions: {
    neutral: string         // 中性表情
    happy: string          // 开心表情
    sad: string            // 悲伤表情
    angry: string          // 愤怒表情
  }
  angles: {
    front: string          // 正面
    leftProfile: string    // 左侧面
    rightProfile: string   // 右侧面
    threeQuarter: string   // 四分之三角度
  }
}
```

### 3. 一致性验证机制

```typescript
interface ConsistencyValidator {
  // 文本一致性验证
  validateTextConsistency(
    generatedFrame: string, 
    characterDNA: DetailedCharacterDNA
  ): Promise<number>
  
  // 图像一致性验证
  validateImageConsistency(
    generatedFrame: string, 
    referenceImages: ReferenceImageSet
  ): Promise<number>
  
  // 综合一致性评分
  calculateOverallConsistency(
    textScore: number, 
    imageScore: number, 
    weights: GenerationStrategy
  ): number
}
```

## 实施流程

### 第一步：建立角色标准档案

1. **提取详细角色信息**
   - 从小说文本中提取角色基础信息
   - 使用AI模型生成详细的外貌描述
   - 标准化描述格式和术语

2. **生成参考图像**
   - 基于详细文本描述生成标准头像
   - 生成不同表情和角度的参考图
   - 验证图像与文本的一致性

3. **建立角色档案**
   - 将文本和图像信息整合
   - 存储到全局角色库
   - 设置一致性阈值和权重

### 第二步：双重约束的视频生成

1. **构建融合提示词**
   - 结合详细文本描述
   - 引用标准参考图像
   - 添加场景特定信息

2. **生成视频帧**
   - 使用融合提示词调用AI模型
   - 实时监控生成质量
   - 应用双重约束条件

3. **质量验证**
   - 文本一致性检查
   - 图像相似度对比
   - 综合评分和决策

### 第三步：持续优化

1. **学习反馈**
   - 收集用户反馈
   - 分析一致性问题
   - 优化约束策略

2. **自适应调整**
   - 动态调整权重比例
   - 更新参考图像库
   - 完善描述模板

## 优势分析

### 1. 互补性强化
- 文本补充图像的模糊部分
- 图像补充文本的歧义部分
- 相互验证提高准确性

### 2. 容错性提升
- 单一约束失效时的降级策略
- 多重验证机制
- 智能错误恢复

### 3. 适应性增强
- 不同场景的灵活适配
- 多种表情和角度支持
- 可配置的约束强度

## 预期效果

- **一致性准确度**：从当前的60-70%提升到90%+
- **用户满意度**：显著提升角色识别度和连续性
- **制作效率**：减少重复修改和人工干预
- **系统稳定性**：建立可靠的质量保障机制
- **开发成本**：基于现有系统升级，成本降低60%+
- **上线时间**：相比重新开发，时间缩短70%+

## 实施优先级和风险评估

### 实施优先级

#### 🔥 高优先级（第1-2周）
1. **扩展Character数据结构**
   - 风险：低，向后兼容
   - 收益：立即支持一致性数据存储
   - 依赖：现有Prisma Schema

2. **升级generate-appearance API**
   - 风险：中，需要测试现有功能
   - 收益：立即提升角色生成一致性
   - 依赖：现有AI生成逻辑

3. **CharacterDetail组件增强**
   - 风险：低，UI增量修改
   - 收益：用户立即看到一致性功能
   - 依赖：现有组件结构

#### ⚡ 中优先级（第3-4周）
1. **DetailedPlotExtraction集成**
   - 风险：中，涉及复杂的剧情分析逻辑
   - 收益：实现剧情和角色的双重一致性
   - 依赖：现有剧情提取策略

2. **全局角色库建立**
   - 风险：中，需要跨剧集数据同步
   - 收益：实现真正的角色一致性
   - 依赖：数据库扩展

3. **一致性验证机制**
   - 风险：高，需要AI模型集成
   - 收益：自动化质量保障
   - 依赖：图像识别API

#### 🔄 低优先级（第5-6周）
1. **高级一致性算法**
   - 风险：高，算法复杂度
   - 收益：进一步提升准确度
   - 依赖：前期功能稳定

2. **性能优化**
   - 风险：低，优化现有功能
   - 收益：提升用户体验
   - 依赖：基础功能完成

### 风险评估和缓解策略

#### 🚨 高风险项目
1. **AI模型集成风险**
   - **风险**：第三方API不稳定、成本过高
   - **缓解**：多模型备选方案、本地模型备份
   - **监控**：API调用成功率、响应时间

2. **数据一致性风险**
   - **风险**：跨剧集数据同步错误
   - **缓解**：事务处理、数据校验、回滚机制
   - **监控**：数据完整性检查、异常日志

#### ⚠️ 中风险项目
1. **性能影响风险**
   - **风险**：一致性验证影响生成速度
   - **缓解**：异步处理、缓存策略、分级验证
   - **监控**：响应时间、系统负载

2. **用户体验风险**
   - **风险**：新功能复杂度影响易用性
   - **缓解**：渐进式发布、用户培训、默认设置
   - **监控**：用户反馈、使用率统计

#### ✅ 低风险项目
1. **现有功能兼容性**
   - **风险**：新功能影响现有功能
   - **缓解**：充分测试、功能开关、灰度发布
   - **监控**：回归测试、错误率监控

## 实施计划

### 阶段一：现有系统增强（1-2周）

#### 第1周：数据结构和基础API扩展
**目标**：在不影响现有功能的前提下，为一致性功能做好基础准备

- **周一-周二**：扩展Prisma Schema
  - 在Character模型中添加一致性相关字段
  - 创建数据库迁移脚本
  - 测试数据结构兼容性

- **周三-周四**：升级generate-appearance API
  - 添加detailedDNA生成逻辑
  - 扩展API参数支持一致性选项
  - 保持现有API向后兼容

- **周五**：基础测试和验证
  - 现有功能回归测试
  - 新增字段数据验证
  - API兼容性测试

#### 第2周：UI组件增强
**目标**：用户界面支持一致性功能，保持现有体验

- **周一-周二**：CharacterDetail组件升级
  - 添加一致性评分显示
  - 集成详细DNA编辑功能
  - 保持现有编辑流程

- **周三-周四**：CharacterManager组件增强
  - 角色卡片添加一致性指示器
  - 支持一致性筛选和排序
  - 保持现有布局和交互

- **周五**：用户体验测试
  - UI组件功能测试
  - 用户交互流程验证
  - 性能影响评估

### 阶段二：双重约束集成（2-3周）

#### 第3周：剧情提取系统集成
**目标**：将一致性约束融入现有剧情分析流程

- **周一-周二**：DetailedPlotExtraction组件升级
  - 添加一致性模式选择
  - 集成全局角色库查询
  - 显示角色一致性分析结果

- **周三-周四**：analyze-detailed-plot API增强
  - 实现全局角色库同步逻辑
  - 添加一致性分析功能
  - 生成增强的提示词

- **周五**：集成测试
  - 剧情分析功能测试
  - 角色一致性验证
  - 性能基准测试

#### 第4周：视频生成约束应用
**目标**：在视频生成过程中应用一致性约束

- **周一-周二**：generate-story-video API升级
  - 集成角色一致性约束
  - 实现多重验证机制
  - 添加质量评分功能

- **周三-周四**：一致性验证器实现
  - 文本一致性验证算法
  - 图像相似度检测
  - 综合评分计算

- **周五**：端到端测试
  - 完整流程测试
  - 一致性效果验证
  - 用户接受度测试

#### 第5周：系统完善（可选）
**目标**：根据测试结果优化和完善系统

- **周一-周二**：问题修复和优化
  - 修复测试中发现的问题
  - 性能优化和调整
  - 用户反馈处理

- **周三-周四**：高级功能实现
  - 自适应权重调整
  - 批量一致性验证
  - 高级监控功能

- **周五**：最终验收
  - 全功能验收测试
  - 文档更新完善
  - 上线准备

### 阶段三：系统优化完善（1-2周）

#### 第6周：性能优化和监控
**目标**：确保系统稳定性和最佳性能

- **周一-周二**：性能优化
  - 数据库查询优化
  - 缓存策略实施
  - 异步处理优化

- **周三-周四**：监控和日志
  - 一致性指标监控
  - 错误日志完善
  - 性能监控仪表板

- **周五**：生产环境部署
  - 生产环境配置
  - 灰度发布策略
  - 监控告警设置

### 每周交付物

#### 第1周交付物
- ✅ 扩展的Prisma Schema
- ✅ 升级的generate-appearance API
- ✅ 数据库迁移脚本
- ✅ 基础功能测试报告

#### 第2周交付物
- ✅ 增强的CharacterDetail组件
- ✅ 升级的CharacterManager组件
- ✅ UI/UX测试报告
- ✅ 用户体验评估

#### 第3周交付物
- ✅ 集成的DetailedPlotExtraction组件
- ✅ 增强的analyze-detailed-plot API
- ✅ 全局角色库同步机制
- ✅ 集成测试报告

#### 第4周交付物
- ✅ 约束化的generate-story-video API
- ✅ 一致性验证器
- ✅ 端到端测试报告
- ✅ 性能基准报告

#### 第5-6周交付物
- ✅ 优化的系统性能
- ✅ 完善的监控机制
- ✅ 生产环境部署
- ✅ 最终验收报告

---

## 技术实现方法

### 1. 数据库设计

#### 1.1 基于现有Prisma Schema的扩展

```prisma
// 扩展现有的Character模型
model Character {
  id              String   @id @default(cuid())
  projectId       String
  name            String   // 角色姓名
  identity        String?  // 身份信息
  personality     String?  // 性格特点
  physique        String?  // 身材特征
  facial          String?  // 五官特征
  hairstyle       String?  // 发型样式
  clothing        String?  // 服饰风格
  generatedImages String?  // AI生成的三视图JSON格式

  // 新增：详细DNA信息
  detailedDNA     Json?    // 详细的角色DNA结构化数据

  // 新增：一致性相关字段
  consistencyScore     Float?   @default(0.0)  // 一致性评分
  consistencySettings  Json?    // 一致性设置
  referenceImages      Json?    // 参考图像集合

  // 新增：全局角色标识
  globalCharacterId    String?  // 关联全局角色库
  isGlobalCharacter    Boolean  @default(false) // 是否为全局角色

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // 关联关系
  project         Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 新增：一致性验证记录
  validationRecords ConsistencyValidation[]

  @@map("characters")
}

// 新增：一致性验证记录表
model ConsistencyValidation {
  id                    String   @id @default(cuid())
  characterId           String
  episodeId             String?

  textConsistencyScore  Float?
  imageConsistencyScore Float?
  overallScore          Float?

  validationDetails     Json?    // 验证详情
  issuesFound          Json?    // 发现的问题

  textWeight           Float?
  imageWeight          Float?

  generatedImageUrl    String?  // 生成的图像URL

  createdAt            DateTime @default(now())

  character            Character @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@map("consistency_validations")
}

-- 参考图像表
CREATE TABLE character_reference_images (
  id VARCHAR(36) PRIMARY KEY,
  character_id VARCHAR(36) NOT NULL,

  -- 标准图像
  standard_portrait VARCHAR(500),
  profile_view VARCHAR(500),
  full_body VARCHAR(500),

  -- 表情图像
  expression_neutral VARCHAR(500),
  expression_happy VARCHAR(500),
  expression_sad VARCHAR(500),
  expression_angry VARCHAR(500),

  -- 角度图像
  angle_front VARCHAR(500),
  angle_left_profile VARCHAR(500),
  angle_right_profile VARCHAR(500),
  angle_three_quarter VARCHAR(500),

  -- 一致性评分
  consistency_score DECIMAL(3,2) DEFAULT 0.00,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (character_id) REFERENCES global_characters(id) ON DELETE CASCADE
);

-- 一致性验证记录表
CREATE TABLE consistency_validations (
  id VARCHAR(36) PRIMARY KEY,
  character_id VARCHAR(36) NOT NULL,
  episode_id VARCHAR(36),

  -- 验证结果
  text_consistency_score DECIMAL(3,2),
  image_consistency_score DECIMAL(3,2),
  overall_score DECIMAL(3,2),

  -- 验证详情
  validation_details JSON,
  issues_found JSON,

  -- 生成参数
  text_weight DECIMAL(3,2),
  image_weight DECIMAL(3,2),

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (character_id) REFERENCES global_characters(id),
  INDEX idx_character_episode (character_id, episode_id)
);
```

#### 1.2 基于现有API的扩展设计

```typescript
// 扩展现有的角色相关API
interface EnhancedCharacterAPI {
  // 扩展现有的角色更新API
  PUT /api/characters/:id
  {
    // 现有字段保持不变
    name?: string
    identity?: string
    personality?: string
    physique?: string
    facial?: string
    hairstyle?: string
    clothing?: string

    // 新增：一致性相关字段
    detailedDNA?: DetailedCharacterDNA
    consistencySettings?: ConsistencySettings
    generateDetailedDNA?: boolean
  }

  // 扩展现有的AI生成API
  POST /api/ai/generate-appearance
  {
    character: Character
    modelId: string
    customPrompt?: string

    // 新增：一致性相关参数
    generateDetailedDNA?: boolean
    useConsistencyConstraints?: boolean
    consistencyMode?: 'plot' | 'consistency' | 'hybrid'
  }

  // 扩展现有的剧情分析API
  POST /api/ai/analyze-detailed-plot
  {
    episodeId: string
    episodeContent: string
    customPrompt?: string

    // 新增：一致性相关参数
    useGlobalCharacters?: boolean
    consistencyMode?: 'plot' | 'consistency' | 'hybrid'
    consistencySettings?: ConsistencySettings
  }

  // 扩展现有的视频生成API
  POST /api/ai/generate-story-video
  {
    episodeId: string
    prompt: string
    projectId: string

    // 新增：一致性约束
    applyConsistencyConstraints?: boolean
    consistencyMode?: 'plot' | 'consistency' | 'hybrid'
  }
}
```

### 2. 核心组件实现

#### 2.1 全局角色管理器

```typescript
// /src/lib/characterConsistency/GlobalCharacterManager.ts
import { v4 as uuidv4 } from 'uuid'

export class GlobalCharacterManager {
  private db: Database

  constructor(database: Database) {
    this.db = database
  }

  /**
   * 查找或创建全局角色
   */
  async findOrCreateCharacter(
    projectId: string,
    characterName: string,
    extractedInfo?: Partial<Character>
  ): Promise<GlobalCharacter> {

    // 1. 查找已存在的角色
    const existing = await this.db.query(`
      SELECT gc.*, cd.*, cri.*
      FROM global_characters gc
      LEFT JOIN character_dna cd ON gc.id = cd.character_id
      LEFT JOIN character_reference_images cri ON gc.id = cri.character_id
      WHERE gc.project_id = ? AND gc.name = ?
    `, [projectId, characterName])

    if (existing.length > 0) {
      return this.mapToGlobalCharacter(existing[0])
    }

    // 2. 创建新角色
    const characterId = uuidv4()

    await this.db.query(`
      INSERT INTO global_characters (id, project_id, name)
      VALUES (?, ?, ?)
    `, [characterId, projectId, characterName])

    // 3. 如果有提取的信息，生成详细DNA
    if (extractedInfo) {
      await this.generateDetailedDNA(characterId, extractedInfo)
    }

    return await this.getCharacterById(characterId)
  }

  /**
   * 生成详细角色DNA
   */
  async generateDetailedDNA(
    characterId: string,
    basicInfo: Partial<Character>
  ): Promise<DetailedCharacterDNA> {

    // 1. 使用AI模型扩展基础信息为详细DNA
    const prompt = this.buildDNAExtractionPrompt(basicInfo)
    const aiResponse = await this.callAIModel(prompt)
    const detailedDNA = this.parseAIResponse(aiResponse)

    // 2. 保存到数据库
    await this.db.query(`
      INSERT INTO character_dna (
        id, character_id, face_shape, eye_shape, eye_color,
        nose_shape, mouth_shape, skin_tone, facial_features,
        height, build, posture, clothing_style, clothing_colors,
        accessories, unique_identifiers, standard_prompt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      uuidv4(), characterId, detailedDNA.facial.faceShape,
      detailedDNA.facial.eyeShape, detailedDNA.facial.eyeColor,
      // ... 其他字段
      JSON.stringify(detailedDNA.uniqueIdentifiers),
      detailedDNA.standardPrompt
    ])

    return detailedDNA
  }

  /**
   * 构建DNA提取提示词
   */
  private buildDNAExtractionPrompt(basicInfo: Partial<Character>): string {
    return `
请基于以下基础角色信息，生成详细的外貌特征描述：

角色名称：${basicInfo.name}
基础描述：${basicInfo.facial || ''}
身份：${basicInfo.identity || ''}
性格：${basicInfo.personality || ''}

请按照以下JSON格式返回详细信息：
{
  "facial": {
    "faceShape": "具体脸型（如：瓜子脸、圆脸、方脸）",
    "eyeShape": "具体眼型（如：丹凤眼、杏眼、桃花眼）",
    "eyeColor": "具体眼色（如：深邃的黑色、明亮的棕色）",
    "noseShape": "具体鼻型（如：高挺的鼻梁、小巧的鼻子）",
    "mouthShape": "具体嘴型（如：樱桃小嘴、薄唇）",
    "skinTone": "具体肤色（如：白皙透亮、健康的小麦色）",
    "facialFeatures": "独特面部特征（如：左脸颊有一颗小痣、眉间有疤痕）"
  },
  "physique": {
    "height": "身高描述",
    "build": "体型描述",
    "posture": "体态特征"
  },
  "clothing": {
    "style": "服装风格",
    "colors": ["常用颜色1", "常用颜色2"],
    "accessories": ["配饰1", "配饰2"]
  },
  "uniqueIdentifiers": ["独特标识1", "独特标识2"],
  "standardPrompt": "标准化的AI生成提示词"
}

要求：
1. 描述要具体、明确，避免模糊词汇
2. 独特标识要能够作为强约束条件
3. 标准化提示词要适合AI图像生成模型使用
`
  }
}
```

#### 2.2 参考图像生成器

```typescript
// /src/lib/characterConsistency/ReferenceImageGenerator.ts

export class ReferenceImageGenerator {
  private aiImageModel: AIImageModel
  private db: Database

  constructor(aiModel: AIImageModel, database: Database) {
    this.aiImageModel = aiModel
    this.db = database
  }

  /**
   * 生成完整的参考图像集
   */
  async generateReferenceImageSet(
    characterId: string,
    detailedDNA: DetailedCharacterDNA
  ): Promise<ReferenceImageSet> {

    const imageSet: Partial<ReferenceImageSet> = {}

    // 1. 生成标准头像
    imageSet.standardPortrait = await this.generateStandardPortrait(detailedDNA)

    // 2. 生成不同表情
    imageSet.expressions = await this.generateExpressions(detailedDNA)

    // 3. 生成不同角度
    imageSet.angles = await this.generateAngles(detailedDNA)

    // 4. 验证一致性
    const consistencyScore = await this.validateImageSetConsistency(imageSet as ReferenceImageSet)

    // 5. 保存到数据库
    await this.saveReferenceImages(characterId, imageSet as ReferenceImageSet, consistencyScore)

    return imageSet as ReferenceImageSet
  }

  /**
   * 生成标准头像
   */
  private async generateStandardPortrait(dna: DetailedCharacterDNA): Promise<string> {
    const prompt = `
高质量人物头像，正面视角，专业摄影：

【面部特征】
- 脸型：${dna.facial.faceShape}
- 眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}
- 鼻型：${dna.facial.noseShape}
- 嘴型：${dna.facial.mouthShape}
- 肤色：${dna.facial.skinTone}
- 特殊标识：${dna.facial.facialFeatures}

【独特标识】
${dna.uniqueIdentifiers.join('，')}

【技术要求】
- 高清晰度，4K质量
- 正面光照，无阴影
- 中性表情
- 纯色背景
- 专业人像摄影风格

【约束条件】
- 严格按照上述特征生成
- 确保独特标识清晰可见
- 避免任何模糊或歧义的特征
`

    const imageUrl = await this.aiImageModel.generate(prompt)
    return imageUrl
  }

  /**
   * 生成不同表情
   */
  private async generateExpressions(dna: DetailedCharacterDNA): Promise<ExpressionSet> {
    const basePrompt = this.buildBasePrompt(dna)

    const expressions = {
      neutral: await this.aiImageModel.generate(`${basePrompt}\n表情：中性，平静`),
      happy: await this.aiImageModel.generate(`${basePrompt}\n表情：开心，微笑`),
      sad: await this.aiImageModel.generate(`${basePrompt}\n表情：悲伤，眉头微皱`),
      angry: await this.aiImageModel.generate(`${basePrompt}\n表情：愤怒，眉头紧锁`)
    }

    return expressions
  }

  /**
   * 验证图像集一致性
   */
  private async validateImageSetConsistency(imageSet: ReferenceImageSet): Promise<number> {
    // 使用图像相似度算法比较不同图像中的面部特征
    const similarities: number[] = []

    // 比较标准头像与各表情图像的相似度
    for (const expression of Object.values(imageSet.expressions)) {
      const similarity = await this.calculateFacialSimilarity(
        imageSet.standardPortrait,
        expression
      )
      similarities.push(similarity)
    }

    // 比较标准头像与各角度图像的相似度
    for (const angle of Object.values(imageSet.angles)) {
      const similarity = await this.calculateFacialSimilarity(
        imageSet.standardPortrait,
        angle
      )
      similarities.push(similarity)
    }

    // 计算平均相似度
    const averageSimilarity = similarities.reduce((a, b) => a + b, 0) / similarities.length
    return averageSimilarity
  }

  /**
   * 计算面部相似度
   */
  private async calculateFacialSimilarity(image1: string, image2: string): Promise<number> {
    // 这里可以集成面部识别API，如：
    // - Azure Face API
    // - AWS Rekognition
    // - Google Cloud Vision API
    // 或者使用开源的面部识别库

    // 示例实现（需要根据实际选择的服务调整）
    const faceAPI = new FaceRecognitionAPI()
    const features1 = await faceAPI.extractFeatures(image1)
    const features2 = await faceAPI.extractFeatures(image2)

    return faceAPI.calculateSimilarity(features1, features2)
  }
}
```

#### 2.3 一致性验证器

```typescript
// /src/lib/characterConsistency/ConsistencyValidator.ts

export class ConsistencyValidator {
  private textAnalyzer: TextAnalyzer
  private imageAnalyzer: ImageAnalyzer
  private db: Database

  constructor(textAnalyzer: TextAnalyzer, imageAnalyzer: ImageAnalyzer, database: Database) {
    this.textAnalyzer = textAnalyzer
    this.imageAnalyzer = imageAnalyzer
    this.db = database
  }

  /**
   * 综合一致性验证
   */
  async validateConsistency(
    characterId: string,
    generatedImage: string,
    episodeId?: string
  ): Promise<ConsistencyResult> {

    // 1. 获取角色档案
    const character = await this.getCharacterProfile(characterId)

    // 2. 文本一致性验证
    const textScore = await this.validateTextConsistency(generatedImage, character.detailedDNA)

    // 3. 图像一致性验证
    const imageScore = await this.validateImageConsistency(generatedImage, character.referenceImages)

    // 4. 计算综合评分
    const overallScore = this.calculateOverallScore(textScore, imageScore, character.generationStrategy)

    // 5. 记录验证结果
    await this.recordValidationResult(characterId, episodeId, {
      textScore,
      imageScore,
      overallScore,
      generatedImage
    })

    return {
      textConsistency: textScore,
      imageConsistency: imageScore,
      overallConsistency: overallScore,
      passed: overallScore >= character.generationStrategy.consistencyThreshold,
      issues: this.identifyIssues(textScore, imageScore, overallScore)
    }
  }

  /**
   * 文本一致性验证
   */
  private async validateTextConsistency(
    generatedImage: string,
    detailedDNA: DetailedCharacterDNA
  ): Promise<number> {

    // 1. 使用图像识别提取生成图像的特征
    const extractedFeatures = await this.imageAnalyzer.extractFeatures(generatedImage)

    // 2. 与标准DNA进行对比
    const comparisons = {
      faceShape: this.compareFeature(extractedFeatures.faceShape, detailedDNA.facial.faceShape),
      eyeShape: this.compareFeature(extractedFeatures.eyeShape, detailedDNA.facial.eyeShape),
      eyeColor: this.compareFeature(extractedFeatures.eyeColor, detailedDNA.facial.eyeColor),
      noseShape: this.compareFeature(extractedFeatures.noseShape, detailedDNA.facial.noseShape),
      mouthShape: this.compareFeature(extractedFeatures.mouthShape, detailedDNA.facial.mouthShape),
      skinTone: this.compareFeature(extractedFeatures.skinTone, detailedDNA.facial.skinTone),
      uniqueIdentifiers: this.validateUniqueIdentifiers(extractedFeatures, detailedDNA.uniqueIdentifiers)
    }

    // 3. 计算加权平均分
    const weights = {
      faceShape: 0.15,
      eyeShape: 0.20,
      eyeColor: 0.15,
      noseShape: 0.15,
      mouthShape: 0.10,
      skinTone: 0.10,
      uniqueIdentifiers: 0.15  // 独特标识权重较高
    }

    let totalScore = 0
    for (const [feature, score] of Object.entries(comparisons)) {
      totalScore += score * weights[feature as keyof typeof weights]
    }

    return totalScore
  }

  /**
   * 图像一致性验证
   */
  private async validateImageConsistency(
    generatedImage: string,
    referenceImages: ReferenceImageSet
  ): Promise<number> {

    // 1. 与标准头像对比
    const portraitSimilarity = await this.calculateFacialSimilarity(
      generatedImage,
      referenceImages.standardPortrait
    )

    // 2. 与最相似的表情图像对比
    const expressionSimilarities = await Promise.all([
      this.calculateFacialSimilarity(generatedImage, referenceImages.expressions.neutral),
      this.calculateFacialSimilarity(generatedImage, referenceImages.expressions.happy),
      this.calculateFacialSimilarity(generatedImage, referenceImages.expressions.sad),
      this.calculateFacialSimilarity(generatedImage, referenceImages.expressions.angry)
    ])

    const bestExpressionSimilarity = Math.max(...expressionSimilarities)

    // 3. 与最相似的角度图像对比
    const angleSimilarities = await Promise.all([
      this.calculateFacialSimilarity(generatedImage, referenceImages.angles.front),
      this.calculateFacialSimilarity(generatedImage, referenceImages.angles.leftProfile),
      this.calculateFacialSimilarity(generatedImage, referenceImages.angles.rightProfile),
      this.calculateFacialSimilarity(generatedImage, referenceImages.angles.threeQuarter)
    ])

    const bestAngleSimilarity = Math.max(...angleSimilarities)

    // 4. 计算综合图像相似度
    const imageConsistency = (portraitSimilarity * 0.5) +
                            (bestExpressionSimilarity * 0.3) +
                            (bestAngleSimilarity * 0.2)

    return imageConsistency
  }

  /**
   * 特征对比
   */
  private compareFeature(extracted: string, expected: string): number {
    // 使用语义相似度算法对比特征描述
    // 可以使用预训练的NLP模型，如BERT、Sentence-BERT等
    return this.textAnalyzer.calculateSimilarity(extracted, expected)
  }

  /**
   * 验证独特标识
   */
  private validateUniqueIdentifiers(
    extractedFeatures: ExtractedFeatures,
    expectedIdentifiers: string[]
  ): number {
    let foundCount = 0

    for (const identifier of expectedIdentifiers) {
      if (this.textAnalyzer.findIdentifier(extractedFeatures.description, identifier)) {
        foundCount++
      }
    }

    return foundCount / expectedIdentifiers.length
  }
}
```

#### 2.4 双重约束视频生成器

```typescript
// /src/lib/characterConsistency/DualConstraintVideoGenerator.ts

export class DualConstraintVideoGenerator {
  private aiVideoModel: AIVideoModel
  private consistencyValidator: ConsistencyValidator
  private characterManager: GlobalCharacterManager

  constructor(
    aiModel: AIVideoModel,
    validator: ConsistencyValidator,
    manager: GlobalCharacterManager
  ) {
    this.aiVideoModel = aiModel
    this.consistencyValidator = validator
    this.characterManager = manager
  }

  /**
   * 生成一致性约束的视频帧
   */
  async generateConsistentVideoFrame(
    characterId: string,
    sceneContext: SceneContext,
    maxRetries: number = 3
  ): Promise<VideoFrame> {

    // 1. 获取角色档案
    const character = await this.characterManager.getCharacterById(characterId)

    // 2. 构建双重约束提示词
    const prompt = this.buildDualConstraintPrompt(character, sceneContext)

    let attempt = 0
    let bestFrame: VideoFrame | null = null
    let bestScore = 0

    while (attempt < maxRetries) {
      try {
        // 3. 生成视频帧
        const frame = await this.aiVideoModel.generateFrame(prompt)

        // 4. 验证一致性
        const validation = await this.consistencyValidator.validateConsistency(
          characterId,
          frame.imageUrl
        )

        // 5. 检查是否达到阈值
        if (validation.passed) {
          return frame
        }

        // 6. 记录最佳结果
        if (validation.overallConsistency > bestScore) {
          bestScore = validation.overallConsistency
          bestFrame = frame
        }

        // 7. 根据验证结果调整提示词
        prompt = this.adjustPromptBasedOnValidation(prompt, validation)

        attempt++

      } catch (error) {
        console.error(`视频生成尝试 ${attempt + 1} 失败:`, error)
        attempt++
      }
    }

    // 如果所有尝试都未达到阈值，返回最佳结果
    if (bestFrame) {
      console.warn(`角色 ${characterId} 的视频帧未达到一致性阈值，返回最佳结果 (评分: ${bestScore})`)
      return bestFrame
    }

    throw new Error(`角色 ${characterId} 的视频帧生成失败`)
  }

  /**
   * 构建双重约束提示词
   */
  private buildDualConstraintPrompt(
    character: GlobalCharacter,
    scene: SceneContext
  ): string {
    const dna = character.detailedDNA
    const references = character.referenceImages

    return `
【双重约束视频生成】

【文本约束 - 角色DNA】
角色名称：${character.name}
脸型：${dna.facial.faceShape}
眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}
鼻型：${dna.facial.noseShape}
嘴型：${dna.facial.mouthShape}
肤色：${dna.facial.skinTone}
独特标识：${dna.uniqueIdentifiers.join('，')}

体型：${dna.physique.height}，${dna.physique.build}
服装：${dna.clothing.style}，主色调：${dna.clothing.colors.join('、')}

【视觉约束 - 参考图像】
标准头像参考：${references.standardPortrait}
要求：严格按照参考图像中的面部特征生成，特别注意：
- 面部轮廓和五官比例必须与参考图像一致
- 独特标识（${dna.uniqueIdentifiers.join('，')}）必须清晰可见
- 整体风格和气质要与参考图像保持一致

【场景信息】
场景：${scene.location}
描述：${scene.description}
氛围：${scene.atmosphere}
时间：${scene.timeOfDay || ''}
光线：${scene.lighting || ''}

【动作和表情】
动作：${scene.action}
情感：${scene.emotion}

【技术要求】
- 高清晰度，电影级质量
- 确保角色面部特征与参考图像高度一致
- 保持角色的独特标识清晰可见
- 适应场景氛围但不改变角色基本外貌

【约束优先级】
1. 角色面部特征一致性（最高优先级）
2. 独特标识可见性
3. 场景适应性
4. 动作和表情表现
`
  }

  /**
   * 根据验证结果调整提示词
   */
  private adjustPromptBasedOnValidation(
    originalPrompt: string,
    validation: ConsistencyResult
  ): string {
    let adjustedPrompt = originalPrompt

    // 根据具体问题调整提示词
    if (validation.issues.includes('面部特征不匹配')) {
      adjustedPrompt += `\n\n【强化约束】\n特别注意：严格保持面部特征与参考图像一致，不允许任何偏差`
    }

    if (validation.issues.includes('独特标识缺失')) {
      adjustedPrompt += `\n\n【独特标识强化】\n必须确保以下标识清晰可见且位置准确`
    }

    if (validation.textConsistency < 0.7) {
      adjustedPrompt += `\n\n【文本约束强化】\n严格按照角色DNA描述生成，不允许创意发挥`
    }

    if (validation.imageConsistency < 0.7) {
      adjustedPrompt += `\n\n【视觉约束强化】\n参考图像权重提升，必须高度还原参考图像特征`
    }

    return adjustedPrompt
  }
}
```

### 3. 前端组件实现

#### 3.1 角色一致性管理面板

```typescript
// /src/components/CharacterConsistencyPanel.tsx

interface CharacterConsistencyPanelProps {
  projectId: string
  onCharacterUpdated?: (character: GlobalCharacter) => void
}

export default function CharacterConsistencyPanel({
  projectId,
  onCharacterUpdated
}: CharacterConsistencyPanelProps) {
  const [characters, setCharacters] = useState<GlobalCharacter[]>([])
  const [selectedCharacter, setSelectedCharacter] = useState<GlobalCharacter | null>(null)
  const [isGeneratingReferences, setIsGeneratingReferences] = useState(false)
  const [consistencySettings, setConsistencySettings] = useState({
    textWeight: 0.4,
    imageWeight: 0.6,
    consistencyThreshold: 0.8
  })

  // 加载全局角色库
  useEffect(() => {
    loadGlobalCharacters()
  }, [projectId])

  const loadGlobalCharacters = async () => {
    try {
      const response = await fetch(`/api/characters/consistency/global/${projectId}`)
      const data = await response.json()
      if (data.success) {
        setCharacters(data.data)
      }
    } catch (error) {
      console.error('加载全局角色库失败:', error)
    }
  }

  // 生成参考图像
  const generateReferenceImages = async (characterId: string) => {
    setIsGeneratingReferences(true)
    try {
      const response = await fetch('/api/characters/consistency/generate-references', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          characterId,
          imageTypes: ['portrait', 'expressions', 'angles']
        })
      })

      const data = await response.json()
      if (data.success) {
        await loadGlobalCharacters() // 重新加载数据
        alert('参考图像生成成功！')
      } else {
        throw new Error(data.error)
      }
    } catch (error) {
      console.error('生成参考图像失败:', error)
      alert('生成参考图像失败，请重试')
    } finally {
      setIsGeneratingReferences(false)
    }
  }

  return (
    <div className="character-consistency-panel">
      <div className="panel-header">
        <h3>角色一致性管理</h3>
        <div className="consistency-settings">
          <label>
            文本权重:
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={consistencySettings.textWeight}
              onChange={(e) => setConsistencySettings(prev => ({
                ...prev,
                textWeight: parseFloat(e.target.value),
                imageWeight: 1 - parseFloat(e.target.value)
              }))}
            />
            {consistencySettings.textWeight}
          </label>
          <label>
            一致性阈值:
            <input
              type="range"
              min="0.5"
              max="1"
              step="0.05"
              value={consistencySettings.consistencyThreshold}
              onChange={(e) => setConsistencySettings(prev => ({
                ...prev,
                consistencyThreshold: parseFloat(e.target.value)
              }))}
            />
            {consistencySettings.consistencyThreshold}
          </label>
        </div>
      </div>

      <div className="characters-grid">
        {characters.map(character => (
          <div key={character.id} className="character-card">
            <div className="character-info">
              <h4>{character.name}</h4>
              <div className="character-dna">
                <p>脸型: {character.detailedDNA?.facial.faceShape}</p>
                <p>眼型: {character.detailedDNA?.facial.eyeShape}</p>
                <p>独特标识: {character.detailedDNA?.uniqueIdentifiers.join(', ')}</p>
              </div>
            </div>

            <div className="reference-images">
              {character.referenceImages?.standardPortrait ? (
                <img
                  src={character.referenceImages.standardPortrait}
                  alt={`${character.name} 标准头像`}
                  className="standard-portrait"
                />
              ) : (
                <div className="no-reference">
                  <p>暂无参考图像</p>
                  <button
                    onClick={() => generateReferenceImages(character.id)}
                    disabled={isGeneratingReferences}
                    className="generate-btn"
                  >
                    {isGeneratingReferences ? '生成中...' : '生成参考图像'}
                  </button>
                </div>
              )}
            </div>

            <div className="consistency-score">
              <div className="score-bar">
                <div
                  className="score-fill"
                  style={{
                    width: `${(character.referenceImages?.consistency_score || 0) * 100}%`
                  }}
                />
              </div>
              <span>一致性: {((character.referenceImages?.consistency_score || 0) * 100).toFixed(1)}%</span>
            </div>

            <div className="character-actions">
              <button onClick={() => setSelectedCharacter(character)}>
                查看详情
              </button>
              <button onClick={() => generateReferenceImages(character.id)}>
                更新参考图像
              </button>
            </div>
          </div>
        ))}
      </div>

      {selectedCharacter && (
        <CharacterDetailModal
          character={selectedCharacter}
          onClose={() => setSelectedCharacter(null)}
          onUpdate={onCharacterUpdated}
        />
      )}
    </div>
  )
}
```

### 4. 配置和部署

#### 4.1 环境配置

```bash
# .env 文件配置
# AI模型配置
OPENAI_API_KEY=your_openai_key
DEEPSEEK_API_KEY=your_deepseek_key

# 图像识别服务配置
AZURE_FACE_API_KEY=your_azure_face_key
AZURE_FACE_ENDPOINT=your_azure_endpoint

# 或者使用AWS Rekognition
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION=us-east-1

# 数据库配置
DATABASE_URL=mysql://user:password@localhost:3306/novel_video_ai

# 文件存储配置
STORAGE_TYPE=local # 或 s3, azure, gcp
STORAGE_PATH=./uploads/character_references

# 一致性验证配置
CONSISTENCY_THRESHOLD_DEFAULT=0.8
TEXT_WEIGHT_DEFAULT=0.4
IMAGE_WEIGHT_DEFAULT=0.6
MAX_GENERATION_RETRIES=3
```

#### 4.2 数据库迁移脚本

```sql
-- 创建数据库迁移脚本
-- migrations/001_create_character_consistency_tables.sql

-- 全局角色库表
CREATE TABLE IF NOT EXISTS global_characters (
  id VARCHAR(36) PRIMARY KEY,
  project_id VARCHAR(36) NOT NULL,
  name VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_project_name (project_id, name),
  INDEX idx_created_at (created_at)
);

-- 详细角色DNA表
CREATE TABLE IF NOT EXISTS character_dna (
  id VARCHAR(36) PRIMARY KEY,
  character_id VARCHAR(36) NOT NULL,

  -- 面部特征
  face_shape VARCHAR(50),
  eye_shape VARCHAR(50),
  eye_color VARCHAR(50),
  nose_shape VARCHAR(50),
  mouth_shape VARCHAR(50),
  skin_tone VARCHAR(50),
  facial_features TEXT,

  -- 体型特征
  height VARCHAR(50),
  build VARCHAR(50),
  posture VARCHAR(100),

  -- 服装特征
  clothing_style VARCHAR(100),
  clothing_colors JSON,
  accessories JSON,

  -- 独特标识
  unique_identifiers JSON,

  -- 生成的标准化提示词
  standard_prompt TEXT,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (character_id) REFERENCES global_characters(id) ON DELETE CASCADE,
  INDEX idx_character_id (character_id)
);

-- 参考图像表
CREATE TABLE IF NOT EXISTS character_reference_images (
  id VARCHAR(36) PRIMARY KEY,
  character_id VARCHAR(36) NOT NULL,

  -- 标准图像
  standard_portrait VARCHAR(500),
  profile_view VARCHAR(500),
  full_body VARCHAR(500),

  -- 表情图像
  expression_neutral VARCHAR(500),
  expression_happy VARCHAR(500),
  expression_sad VARCHAR(500),
  expression_angry VARCHAR(500),

  -- 角度图像
  angle_front VARCHAR(500),
  angle_left_profile VARCHAR(500),
  angle_right_profile VARCHAR(500),
  angle_three_quarter VARCHAR(500),

  -- 一致性评分
  consistency_score DECIMAL(3,2) DEFAULT 0.00,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (character_id) REFERENCES global_characters(id) ON DELETE CASCADE,
  INDEX idx_character_id (character_id),
  INDEX idx_consistency_score (consistency_score)
);

-- 一致性验证记录表
CREATE TABLE IF NOT EXISTS consistency_validations (
  id VARCHAR(36) PRIMARY KEY,
  character_id VARCHAR(36) NOT NULL,
  episode_id VARCHAR(36),

  -- 验证结果
  text_consistency_score DECIMAL(3,2),
  image_consistency_score DECIMAL(3,2),
  overall_score DECIMAL(3,2),

  -- 验证详情
  validation_details JSON,
  issues_found JSON,

  -- 生成参数
  text_weight DECIMAL(3,2),
  image_weight DECIMAL(3,2),

  -- 生成的图像URL（用于后续分析）
  generated_image_url VARCHAR(500),

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (character_id) REFERENCES global_characters(id),
  INDEX idx_character_episode (character_id, episode_id),
  INDEX idx_overall_score (overall_score),
  INDEX idx_created_at (created_at)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS consistency_settings (
  id VARCHAR(36) PRIMARY KEY,
  project_id VARCHAR(36) NOT NULL,

  -- 默认权重设置
  default_text_weight DECIMAL(3,2) DEFAULT 0.40,
  default_image_weight DECIMAL(3,2) DEFAULT 0.60,
  default_consistency_threshold DECIMAL(3,2) DEFAULT 0.80,

  -- 生成设置
  max_generation_retries INT DEFAULT 3,
  auto_generate_references BOOLEAN DEFAULT TRUE,

  -- AI模型设置
  preferred_image_model VARCHAR(100),
  preferred_text_model VARCHAR(100),

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_project (project_id),
  INDEX idx_project_id (project_id)
);
```

#### 4.3 部署脚本

```bash
#!/bin/bash
# deploy_character_consistency.sh

echo "开始部署角色一致性系统..."

# 1. 安装依赖
echo "安装新增依赖..."
npm install face-api.js @azure/cognitiveservices-face uuid

# 2. 运行数据库迁移
echo "运行数据库迁移..."
npx prisma migrate deploy
# 或者使用原生SQL
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < migrations/001_create_character_consistency_tables.sql

# 3. 创建存储目录
echo "创建存储目录..."
mkdir -p ./uploads/character_references
mkdir -p ./uploads/character_references/portraits
mkdir -p ./uploads/character_references/expressions
mkdir -p ./uploads/character_references/angles

# 4. 设置权限
chmod 755 ./uploads/character_references
chmod 755 ./uploads/character_references/*

# 5. 构建项目
echo "构建项目..."
npm run build

# 6. 重启服务
echo "重启服务..."
pm2 restart novel-video-ai

echo "部署完成！"
```

#### 4.4 监控和日志配置

```typescript
// /src/lib/monitoring/ConsistencyMonitor.ts

export class ConsistencyMonitor {
  private logger: Logger

  constructor() {
    this.logger = new Logger('CharacterConsistency')
  }

  /**
   * 记录一致性验证结果
   */
  logValidationResult(result: ConsistencyResult, characterId: string, episodeId?: string) {
    this.logger.info('角色一致性验证完成', {
      characterId,
      episodeId,
      textScore: result.textConsistency,
      imageScore: result.imageConsistency,
      overallScore: result.overallConsistency,
      passed: result.passed,
      issues: result.issues
    })

    // 如果一致性较低，记录警告
    if (result.overallConsistency < 0.7) {
      this.logger.warn('角色一致性较低', {
        characterId,
        score: result.overallConsistency,
        issues: result.issues
      })
    }
  }

  /**
   * 记录参考图像生成结果
   */
  logReferenceGeneration(characterId: string, imageType: string, success: boolean, error?: string) {
    if (success) {
      this.logger.info('参考图像生成成功', {
        characterId,
        imageType
      })
    } else {
      this.logger.error('参考图像生成失败', {
        characterId,
        imageType,
        error
      })
    }
  }

  /**
   * 生成一致性报告
   */
  async generateConsistencyReport(projectId: string, timeRange: TimeRange): Promise<ConsistencyReport> {
    const validations = await this.getValidationHistory(projectId, timeRange)

    const report = {
      projectId,
      timeRange,
      totalValidations: validations.length,
      averageConsistency: this.calculateAverageConsistency(validations),
      passRate: this.calculatePassRate(validations),
      topIssues: this.identifyTopIssues(validations),
      characterPerformance: this.analyzeCharacterPerformance(validations)
    }

    this.logger.info('一致性报告生成完成', report)
    return report
  }
}
```

### 5. 测试策略

#### 5.1 单元测试

```typescript
// /tests/characterConsistency/GlobalCharacterManager.test.ts

describe('GlobalCharacterManager', () => {
  let manager: GlobalCharacterManager
  let mockDb: jest.Mocked<Database>

  beforeEach(() => {
    mockDb = createMockDatabase()
    manager = new GlobalCharacterManager(mockDb)
  })

  describe('findOrCreateCharacter', () => {
    it('应该返回已存在的角色', async () => {
      // 模拟数据库返回已存在角色
      mockDb.query.mockResolvedValueOnce([mockExistingCharacter])

      const result = await manager.findOrCreateCharacter('project1', '张三')

      expect(result.name).toBe('张三')
      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT gc.*'),
        ['project1', '张三']
      )
    })

    it('应该创建新角色当角色不存在时', async () => {
      // 模拟数据库返回空结果
      mockDb.query.mockResolvedValueOnce([])
      mockDb.query.mockResolvedValueOnce([]) // INSERT 操作

      const result = await manager.findOrCreateCharacter('project1', '李四', mockCharacterInfo)

      expect(mockDb.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO global_characters'),
        expect.arrayContaining(['project1', '李四'])
      )
    })
  })

  describe('generateDetailedDNA', () => {
    it('应该生成详细的角色DNA', async () => {
      const mockAIResponse = {
        facial: {
          faceShape: '瓜子脸',
          eyeShape: '丹凤眼',
          // ...
        }
      }

      jest.spyOn(manager as any, 'callAIModel').mockResolvedValue(JSON.stringify(mockAIResponse))

      const result = await manager.generateDetailedDNA('char1', mockBasicInfo)

      expect(result.facial.faceShape).toBe('瓜子脸')
      expect(result.facial.eyeShape).toBe('丹凤眼')
    })
  })
})
```

#### 5.2 集成测试

```typescript
// /tests/integration/characterConsistency.integration.test.ts

describe('角色一致性系统集成测试', () => {
  let app: Application
  let testDb: Database

  beforeAll(async () => {
    app = await createTestApp()
    testDb = await createTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase(testDb)
  })

  describe('完整的角色一致性流程', () => {
    it('应该完成从角色提取到一致性验证的完整流程', async () => {
      // 1. 创建测试项目
      const project = await createTestProject()

      // 2. 上传小说内容
      const uploadResponse = await request(app)
        .post(`/api/projects/${project.id}/upload`)
        .attach('file', 'test-novel.txt')
        .expect(200)

      // 3. 分析角色
      const analyzeResponse = await request(app)
        .post(`/api/projects/${project.id}/analyze`)
        .send({ modelId: 'test-model' })
        .expect(200)

      expect(analyzeResponse.body.data.characters).toHaveLength(2)

      // 4. 生成参考图像
      const character = analyzeResponse.body.data.characters[0]
      const referenceResponse = await request(app)
        .post('/api/characters/consistency/generate-references')
        .send({
          characterId: character.id,
          imageTypes: ['portrait', 'expressions']
        })
        .expect(200)

      // 5. 生成视频并验证一致性
      const videoResponse = await request(app)
        .post('/api/ai/generate-story-video')
        .send({
          episodeId: 'test-episode',
          prompt: 'test prompt',
          projectId: project.id
        })
        .expect(200)

      // 6. 验证一致性结果
      expect(videoResponse.body.data.consistencyScore).toBeGreaterThan(0.8)
    })
  })
})
```

### 6. 性能优化建议

#### 6.1 缓存策略
- 角色DNA信息缓存（Redis）
- 参考图像CDN缓存
- 一致性验证结果缓存

#### 6.2 异步处理
- 参考图像生成异步化
- 批量一致性验证
- 后台数据同步

#### 6.3 资源优化
- 图像压缩和格式优化
- 数据库查询优化
- API响应时间监控

---

## 总结

本双重约束角色一致性系统通过融合文本约束和视觉约束，为小说转视频项目提供了完整的角色外貌一致性解决方案。系统具备以下特点：

1. **技术完整性**：从数据库设计到前端组件的完整技术栈
2. **可扩展性**：模块化设计，支持后续功能扩展
3. **可维护性**：清晰的代码结构和完善的测试覆盖
4. **高可用性**：完善的监控、日志和错误处理机制

通过本系统的实施，预期可以将角色外貌一致性从当前的60-70%提升到90%以上，显著改善用户体验和内容质量。

*本文档将持续更新，随着技术实现的进展补充更多细节。*
