import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 图像生成请求接口
interface ImageGenerationRequest {
  prompt: string
  negativePrompt?: string
  width?: number
  height?: number
  steps?: number
  guidance?: number
  seed?: number
  style?: string
  quality?: 'standard' | 'high' | 'ultra'
}

// 图像生成响应接口
interface ImageGenerationResponse {
  imageUrl: string
  width: number
  height: number
  seed: number
  steps: number
  guidance: number
  metadata?: any
}

// 豆包图像生成服务类
class DoubaoImageService {
  private baseUrl: string = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'
  private accessKeyId: string = 'AKLTOTgwMzIxY2VlNDIxNDNiMWFlZjAzOWY1OTU3ZDIwOWE'
  private secretAccessKey: string = 'WXpBMU9ETmtNamxoTmpZMk5EQTNZV0psWVdZelpqRXlOREkxT1dJM01ETQ=='
  private modelEndpoint: string = 'ep-20250626132353-nlrtf' // 最新端点

  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    try {
      console.log('🎨 调用豆包图像生成API')
      console.log('使用最新端点:', this.modelEndpoint)
      
      // 构建豆包图像生成请求参数
      const requestBody = {
        model: this.modelEndpoint,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: request.prompt
              }
            ]
          }
        ],
        parameters: {
          width: request.width || 1024,
          height: request.height || 1024,
          steps: request.steps || 20,
          guidance_scale: request.guidance || 7.5,
          seed: request.seed || Math.floor(Math.random() * 1000000),
          negative_prompt: request.negativePrompt || '',
          style: request.style || 'realistic',
          quality: request.quality || 'high'
        }
      }

      console.log('📝 豆包图像生成请求参数:', {
        model: requestBody.model,
        promptLength: request.prompt.length,
        width: requestBody.parameters.width,
        height: requestBody.parameters.height,
        steps: requestBody.parameters.steps,
        guidance: requestBody.parameters.guidance_scale
      })

      // 生成认证签名
      const authorization = this.generateAuthorization()

      // 调用豆包图像生成API
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': authorization,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      console.log('豆包图像API响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`豆包图像API调用失败: ${response.status}`)
        console.error('错误详情:', errorText)
        throw new Error(`豆包图像API调用失败: ${response.status} ${errorText}`)
      }

      // 解析响应
      const result = await response.json()
      console.log('豆包图像API响应:', {
        success: !!result.data,
        hasImages: !!(result.data && result.data.images),
        imageCount: result.data?.images?.length || 0
      })

      if (!result.data || !result.data.images || result.data.images.length === 0) {
        throw new Error('豆包图像API未返回图像数据')
      }

      // 处理图像数据
      const imageData = result.data.images[0]
      const imageUrl = imageData.url || imageData.image_url || imageData.data

      console.log('✅ 豆包图像生成成功:', {
        imageUrl: imageUrl ? '已生成' : '未生成',
        width: requestBody.parameters.width,
        height: requestBody.parameters.height,
        seed: requestBody.parameters.seed
      })

      return {
        imageUrl,
        width: requestBody.parameters.width,
        height: requestBody.parameters.height,
        seed: requestBody.parameters.seed,
        steps: requestBody.parameters.steps,
        guidance: requestBody.parameters.guidance_scale,
        metadata: {
          model: this.modelEndpoint,
          prompt: request.prompt,
          negativePrompt: request.negativePrompt,
          style: request.style,
          quality: request.quality,
          provider: 'doubao',
          generatedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('豆包图像生成失败:', error)
      throw error
    }
  }

  // 生成认证签名
  private generateAuthorization(): string {
    // 简化的认证方式，实际应该使用正确的签名算法
    return `Bearer ${this.accessKeyId}`
  }

  // 获取可用的图像风格
  getAvailableStyles(): string[] {
    return [
      'realistic',
      'anime',
      'cartoon',
      'oil_painting',
      'watercolor',
      'sketch',
      'digital_art',
      'concept_art'
    ]
  }
}

// 图像生成API
export async function POST(request: NextRequest) {
  try {
    const body: ImageGenerationRequest = await request.json()
    
    if (!body.prompt) {
      return NextResponse.json(
        { error: '提示词不能为空' },
        { status: 400 }
      )
    }

    // 获取可用的图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })

    if (!imageConfig) {
      return NextResponse.json(
        { error: '未找到可用的图像生成配置' },
        { status: 404 }
      )
    }

    console.log(`🎨 使用图像生成服务: ${imageConfig.name} (${imageConfig.model})`)

    // 构建图像生成请求
    const imageRequest: ImageGenerationRequest = {
      prompt: body.prompt,
      negativePrompt: body.negativePrompt,
      width: body.width || 1024,
      height: body.height || 1024,
      steps: body.steps || 20,
      guidance: body.guidance || 7.5,
      seed: body.seed,
      style: body.style || 'realistic',
      quality: body.quality || 'high'
    }

    // 调用豆包图像生成服务
    const imageService = new DoubaoImageService()
    const result = await imageService.generateImage(imageRequest)

    console.log('✅ 图像生成成功:', {
      provider: 'doubao',
      model: imageConfig.model,
      width: result.width,
      height: result.height,
      seed: result.seed
    })

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('图像生成失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '图像生成失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 获取图像生成配置的GET接口
export async function GET(request: NextRequest) {
  try {
    // 获取可用的图像生成配置
    const imageConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsImage: true,
        enabled: true
      },
      orderBy: { updatedAt: 'desc' }
    })

    if (!imageConfig) {
      return NextResponse.json(
        { error: '未找到可用的图像生成配置' },
        { status: 404 }
      )
    }

    const imageService = new DoubaoImageService()
    const availableStyles = imageService.getAvailableStyles()

    return NextResponse.json({
      success: true,
      data: {
        provider: 'doubao',
        model: imageConfig.name,
        endpoint: imageConfig.model,
        availableStyles,
        defaultSettings: {
          width: 1024,
          height: 1024,
          steps: 20,
          guidance: 7.5,
          quality: 'high'
        }
      }
    })

  } catch (error) {
    console.error('获取图像生成配置失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取配置失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
