# 角色管理升级需求文档

## 需求背景

在"灵犀Ai——小说转视频神器"项目中，角色管理是核心功能之一。为了更好地支持连续剧制作中的人物形象一致性，需要对角色管理功能进行全面升级。

## 核心需求

### 1. 角色展示方式升级

#### 当前状态
- 简单的列表式展示
- 点击展开查看详情
- 信息密度较高，不够直观

#### 目标状态
- **头像+名称卡片式展示**
- 每个角色显示为独立的卡片
- 包含头像、名称、身份标签、简要信息
- 响应式网格布局（1-4列自适应）

### 2. 角色详情交互升级

#### 交互方式
- **点击角色头像或名称** → 打开角色详情弹窗
- 全屏模态弹窗设计
- 支持点击外部区域关闭

#### 弹窗结构
- 头部：角色头像、名称、编辑按钮
- 主体：双标签页内容
- 底部：操作按钮区域

### 3. 双标签页设计

#### 标签页1：角色信息
**功能定位**：展示AI提取的角色信息
**内容结构**：
- **五官** - 面部特征、眼睛、鼻子、嘴巴等具体描述
- **身份** - 职业、社会地位、背景等身份信息
- **外貌** - 身材、穿着、整体形象等外在表现
- **性格** - 性格特征、行为习惯、心理特点等内在特质
- **隐线伏笔** - 角色相关的隐藏线索、伏笔、秘密等深层信息

**数据来源**：大模型阅读小说后提炼总结，要求准确、详细

#### 标签页2：形象
**功能定位**：人物形象编辑窗口，为连续剧人物一致性准备
**内容结构**：
- **五官** - 眼睛形状、鼻子特征、嘴唇样式、脸型等
- **发型** - 发型样式、发色、发长等
- **身材** - 身高、体型、体态特征等
- **服装** - 服装风格、颜色搭配、配饰等

**AI生成功能**：
- **AI一键生成角色形象按钮** - 基于角色信息自动生成形象描述
- **选择模型按钮** - 为AI生成功能配置驱动模型
- **预览和保存** - 生成结果先预览，确认后保存到形象编辑区域

**使用目的**：确保同一角色在不同剧集中保持视觉统一

### 4. 编辑功能需求

#### 编辑模式
- 点击"编辑"按钮进入编辑状态
- 所有文本字段变为可编辑的文本框
- 支持多行文本输入

#### 保存机制
- 实时编辑，点击"保存"提交更改
- 支持"取消"操作，恢复原始数据
- 数据同步到数据库

### 5. AI形象生成功能需求

#### 功能组件
- **AI一键生成角色形象按钮**
  - 位置：形象标签页顶部
  - 样式：紫色主题按钮，带AI图标
  - 功能：基于角色信息生成形象描述

- **选择模型按钮**
  - 位置：AI生成按钮旁边
  - 样式：小型下拉按钮
  - 功能：打开模型选择器

#### 模型选择器
- **显示内容**：所有已配置的AI模型列表
- **模型信息**：模型名称、提供商、状态
- **选择逻辑**：记住用户选择，作为默认模型
- **状态检查**：只显示连接正常的模型

#### AI生成流程
1. **数据收集**：读取角色信息标签页的五大信息
2. **提示词构建**：基于角色信息构建形象生成提示词
3. **模型调用**：使用选定模型生成形象描述
4. **结果处理**：解析AI返回的四维度形象描述

#### 预览和保存
- **预览区域**：在弹窗中展示生成的形象描述
- **内容展示**：分别显示五官、发型、身材、服装四个维度
- **操作按钮**：
  - "重新生成" - 重新调用AI生成
  - "保存到形象" - 将生成结果保存到编辑区域
  - "取消" - 关闭预览，不保存

## 技术实现要求

### 1. 组件设计

#### CharacterManager组件
- 负责角色列表的卡片式展示
- 处理角色点击事件
- 管理角色详情弹窗的开关状态

#### CharacterDetail组件
- 独立的角色详情弹窗组件
- 支持双标签页切换
- 包含完整的编辑功能

### 2. 数据结构

#### Character接口更新
```typescript
interface Character {
  id: string;
  projectId: string;
  name: string;
  facial?: string;      // 五官描述
  identity?: string;    // 身份信息
  appearance?: string;  // 外貌描述
  personality?: string; // 性格特点
  hiddenLines?: string; // 隐线伏笔
  createdAt: Date;
  updatedAt: Date;
}
```

#### 形象设置数据结构
```typescript
interface CharacterAppearanceSettings {
  facial: string;    // 五官特征
  hairstyle: string; // 发型样式
  physique: string;  // 身材特征
  clothing: string;  // 服装风格
}
```

### 3. 用户体验要求

#### 视觉设计
- 使用紫色主题色，与整体设计保持一致
- 头像使用渐变紫色圆形设计
- 卡片悬停效果和点击反馈
- 清晰的视觉层次和信息组织

#### 交互体验
- 流畅的动画过渡效果
- 直观的操作反馈
- 响应式设计，适配不同屏幕尺寸
- 键盘导航支持

#### 性能要求
- 角色列表快速加载
- 弹窗打开关闭流畅
- 编辑操作响应及时
- 数据保存可靠

## 业务价值

### 1. 提升用户体验
- 更直观的角色信息展示
- 更便捷的角色管理操作
- 更专业的界面设计

### 2. 支持连续剧制作
- 为视频生成提供标准化的人物形象描述
- 确保角色在不同剧集中的视觉一致性
- 提高视频制作的专业度

### 3. 增强产品竞争力
- 独特的角色管理功能
- 专业的视频制作支持
- 完整的创作工具链

## 验收标准

### 功能验收
- [x] 角色列表以卡片形式展示，包含头像、名称、身份标签
- [x] 点击角色头像或名称打开详情弹窗
- [x] 详情弹窗包含"角色信息"和"形象"两个标签页
- [x] 角色信息标签页显示五大角色信息（五官、身份、外貌、性格、隐线）
- [x] 形象标签页包含四个编辑区域（五官、发型、身材、服装）
- [x] 支持角色信息的在线编辑和保存
- [x] 响应式设计，适配桌面端和移动端
- [x] AI一键生成角色形象按钮，支持基于角色信息生成形象描述
- [x] 选择模型功能，支持为AI生成配置不同的驱动模型
- [x] 预览和保存机制，生成结果先预览后保存
- [x] 四维度形象生成（五官、发型、身材、服装）

### 性能验收
- [ ] 角色列表加载时间 < 1秒
- [ ] 弹窗打开关闭动画流畅（60fps）
- [ ] 编辑操作响应时间 < 200ms
- [ ] 数据保存成功率 > 99%

### 用户体验验收
- [ ] 界面设计美观，符合整体风格
- [ ] 操作流程直观，无需学习成本
- [ ] 错误提示清晰，帮助用户解决问题
- [ ] 支持键盘导航和无障碍访问

## 开发计划

### 第一阶段：基础组件开发
- 创建CharacterDetail弹窗组件
- 实现双标签页结构
- 完成基础的显示功能

### 第二阶段：编辑功能实现
- 添加编辑模式切换
- 实现表单数据绑定
- 完成保存和取消功能

### 第三阶段：界面优化
- 更新CharacterManager为卡片式布局
- 优化视觉设计和交互效果
- 完善响应式适配

### 第四阶段：AI生成功能开发
- 创建ModelSelector模型选择器组件
- 实现AppearancePreview预览弹窗组件
- 开发AI形象生成API接口
- 集成DeepSeek、OpenAI等AI模型调用

### 第五阶段：测试和优化
- 功能测试和bug修复
- AI生成功能测试和优化
- 性能优化和用户体验改进
- 文档更新和代码整理

## 风险评估

### 技术风险
- **弹窗组件复杂度**：需要处理多种状态和交互
- **数据同步**：编辑后的数据需要可靠地同步到数据库
- **性能影响**：大量角色时的渲染性能

### 解决方案
- 采用成熟的模态弹窗设计模式
- 实现完善的错误处理和重试机制
- 使用虚拟滚动等性能优化技术

### 用户接受度风险
- **学习成本**：新的交互方式可能需要用户适应
- **功能复杂度**：双标签页设计可能增加操作复杂度

### 解决方案
- 提供清晰的操作指引和帮助文档
- 保持界面简洁，突出核心功能
- 收集用户反馈，持续优化体验
