{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/fileParser.ts"], "sourcesContent": ["import mammoth from 'mammoth'\n\nexport class FileParseError extends Error {\n  constructor(message: string, public code: string) {\n    super(message)\n    this.name = 'FileParseError'\n  }\n}\n\nexport class FileParser {\n  // 支持的文件类型\n  static readonly SUPPORTED_TYPES = {\n    'text/plain': ['.txt'],\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\n    'application/msword': ['.doc'] // 虽然不完全支持，但可以尝试\n  }\n\n  // 获取支持的文件扩展名\n  static getSupportedExtensions(): string[] {\n    return Object.values(this.SUPPORTED_TYPES).flat()\n  }\n\n  // 验证文件类型\n  static validateFile(file: File): void {\n    const extension = this.getFileExtension(file.name)\n    const supportedExtensions = this.getSupportedExtensions()\n    \n    if (!supportedExtensions.includes(extension)) {\n      throw new FileParseError(\n        `不支持的文件格式: ${extension}。支持的格式: ${supportedExtensions.join(', ')}`,\n        'UNSUPPORTED_FORMAT'\n      )\n    }\n\n    // 验证文件大小 (最大 50MB)\n    const maxSize = 50 * 1024 * 1024\n    if (file.size > maxSize) {\n      throw new FileParseError(\n        `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,\n        'FILE_TOO_LARGE'\n      )\n    }\n\n    // 验证文件不为空\n    if (file.size === 0) {\n      throw new FileParseError(\n        '文件不能为空',\n        'EMPTY_FILE'\n      )\n    }\n  }\n\n  // 解析文件内容\n  static async parseFile(file: File): Promise<string> {\n    this.validateFile(file)\n    \n    const extension = this.getFileExtension(file.name)\n    \n    try {\n      switch (extension) {\n        case '.txt':\n          return await this.parseTxtFile(file)\n        case '.docx':\n          return await this.parseDocxFile(file)\n        case '.doc':\n          // .doc 文件尝试用 docx 解析器处理\n          return await this.parseDocxFile(file)\n        default:\n          throw new FileParseError(\n            `不支持的文件格式: ${extension}`,\n            'UNSUPPORTED_FORMAT'\n          )\n      }\n    } catch (error) {\n      if (error instanceof FileParseError) {\n        throw error\n      }\n      \n      throw new FileParseError(\n        `文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        'PARSE_ERROR'\n      )\n    }\n  }\n\n  // 解析 TXT 文件\n  private static async parseTxtFile(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      \n      reader.onload = (e) => {\n        const content = e.target?.result as string\n        if (!content || content.trim().length === 0) {\n          reject(new FileParseError('文件内容为空', 'EMPTY_CONTENT'))\n          return\n        }\n        resolve(content)\n      }\n      \n      reader.onerror = () => {\n        reject(new FileParseError('文件读取失败', 'READ_ERROR'))\n      }\n      \n      // 尝试不同的编码\n      reader.readAsText(file, 'utf-8')\n    })\n  }\n\n  // 解析 DOCX 文件\n  private static async parseDocxFile(file: File): Promise<string> {\n    try {\n      const arrayBuffer = await this.fileToArrayBuffer(file)\n      const result = await mammoth.extractRawText({ arrayBuffer })\n      \n      if (!result.value || result.value.trim().length === 0) {\n        throw new FileParseError('Word文档内容为空', 'EMPTY_CONTENT')\n      }\n\n      // 如果有警告，记录但不阻止处理\n      if (result.messages && result.messages.length > 0) {\n        console.warn('Word文档解析警告:', result.messages)\n      }\n\n      // 清理文本内容\n      return this.cleanText(result.value)\n    } catch (error) {\n      if (error instanceof FileParseError) {\n        throw error\n      }\n      \n      throw new FileParseError(\n        `Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        'DOCX_PARSE_ERROR'\n      )\n    }\n  }\n\n  // 将文件转换为 ArrayBuffer\n  private static async fileToArrayBuffer(file: File): Promise<ArrayBuffer> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      \n      reader.onload = (e) => {\n        const result = e.target?.result\n        if (result instanceof ArrayBuffer) {\n          resolve(result)\n        } else {\n          reject(new Error('文件读取结果不是 ArrayBuffer'))\n        }\n      }\n      \n      reader.onerror = () => {\n        reject(new Error('文件读取失败'))\n      }\n      \n      reader.readAsArrayBuffer(file)\n    })\n  }\n\n  // 清理文本内容\n  private static cleanText(text: string): string {\n    return text\n      .replace(/\\r\\n/g, '\\n')  // 统一换行符\n      .replace(/\\r/g, '\\n')    // 处理单独的 \\r\n      .replace(/\\n{3,}/g, '\\n\\n')  // 合并多个连续换行\n      .trim()\n  }\n\n  // 获取文件扩展名\n  private static getFileExtension(fileName: string): string {\n    const lastDotIndex = fileName.lastIndexOf('.')\n    if (lastDotIndex === -1) {\n      return ''\n    }\n    return fileName.substring(lastDotIndex).toLowerCase()\n  }\n\n  // 获取文件类型描述\n  static getFileTypeDescription(file: File): string {\n    const extension = this.getFileExtension(file.name)\n    \n    switch (extension) {\n      case '.txt':\n        return '纯文本文件'\n      case '.docx':\n        return 'Word 文档 (新版)'\n      case '.doc':\n        return 'Word 文档 (旧版)'\n      default:\n        return '未知格式'\n    }\n  }\n\n  // 格式化文件大小\n  static formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B'\n    \n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,uBAAuB;;IAClC,YAAY,OAAe,EAAE,AAAO,IAAY,CAAE;QAChD,KAAK,CAAC,eAD4B,OAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM;IACX,UAAU;IACV,OAAgB,kBAAkB;QAChC,cAAc;YAAC;SAAO;QACtB,2EAA2E;YAAC;SAAQ;QACpF,sBAAsB;YAAC;SAAO,CAAC,gBAAgB;IACjD,EAAC;IAED,aAAa;IACb,OAAO,yBAAmC;QACxC,OAAO,OAAO,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI;IACjD;IAEA,SAAS;IACT,OAAO,aAAa,IAAU,EAAQ;QACpC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QACjD,MAAM,sBAAsB,IAAI,CAAC,sBAAsB;QAEvD,IAAI,CAAC,oBAAoB,QAAQ,CAAC,YAAY;YAC5C,MAAM,IAAI,eACR,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,oBAAoB,IAAI,CAAC,OAAO,EACjE;QAEJ;QAEA,mBAAmB;QACnB,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,MAAM,IAAI,eACR,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,UAAU,OAAO,MAAM,EAAE,CAAC,EACjD;QAEJ;QAEA,UAAU;QACV,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,MAAM,IAAI,eACR,UACA;QAEJ;IACF;IAEA,SAAS;IACT,aAAa,UAAU,IAAU,EAAmB;QAClD,IAAI,CAAC,YAAY,CAAC;QAElB,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAEjD,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC;gBACjC,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;gBAClC,KAAK;oBACH,wBAAwB;oBACxB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;gBAClC;oBACE,MAAM,IAAI,eACR,CAAC,UAAU,EAAE,WAAW,EACxB;YAEN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,MAAM,IAAI,eACR,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ,EAC5D;QAEJ;IACF;IAEA,YAAY;IACZ,aAAqB,aAAa,IAAU,EAAmB;QAC7D,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;oBAC3C,OAAO,IAAI,eAAe,UAAU;oBACpC;gBACF;gBACA,QAAQ;YACV;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,eAAe,UAAU;YACtC;YAEA,UAAU;YACV,OAAO,UAAU,CAAC,MAAM;QAC1B;IACF;IAEA,aAAa;IACb,aAAqB,cAAc,IAAU,EAAmB;QAC9D,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACjD,MAAM,SAAS,MAAM,0IAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAAE;YAAY;YAE1D,IAAI,CAAC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;gBACrD,MAAM,IAAI,eAAe,cAAc;YACzC;YAEA,iBAAiB;YACjB,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACjD,QAAQ,IAAI,CAAC,eAAe,OAAO,QAAQ;YAC7C;YAEA,SAAS;YACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,MAAM,IAAI,eACR,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ,EAChE;QAEJ;IACF;IAEA,qBAAqB;IACrB,aAAqB,kBAAkB,IAAU,EAAwB;QACvE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,IAAI,kBAAkB,aAAa;oBACjC,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,MAAM;YACnB;YAEA,OAAO,iBAAiB,CAAC;QAC3B;IACF;IAEA,SAAS;IACT,OAAe,UAAU,IAAY,EAAU;QAC7C,OAAO,KACJ,OAAO,CAAC,SAAS,MAAO,QAAQ;SAChC,OAAO,CAAC,OAAO,MAAS,WAAW;SACnC,OAAO,CAAC,WAAW,QAAS,WAAW;SACvC,IAAI;IACT;IAEA,UAAU;IACV,OAAe,iBAAiB,QAAgB,EAAU;QACxD,MAAM,eAAe,SAAS,WAAW,CAAC;QAC1C,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO;QACT;QACA,OAAO,SAAS,SAAS,CAAC,cAAc,WAAW;IACrD;IAEA,WAAW;IACX,OAAO,uBAAuB,IAAU,EAAU;QAChD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAEjD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,UAAU;IACV,OAAO,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;AACF", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/utils/promptGenerator.ts"], "sourcesContent": ["// 角色形象提示词生成器\n// 专门用于从小说角色信息生成高质量的图像生成提示词\n\ninterface CharacterInfo {\n  name: string\n  identity?: string\n  personality?: string\n  physique?: string\n  facial?: string\n  hairstyle?: string\n  clothing?: string\n}\n\ninterface PromptOptions {\n  artStyle?: 'anime' | 'realistic' | 'semi-realistic' | 'concept-art'\n  viewType?: 'front' | 'side' | 'back' | 'three-quarter'\n  background?: 'white' | 'transparent' | 'simple' | 'detailed'\n  quality?: 'standard' | 'high' | 'masterpiece'\n  customEnhancement?: string\n}\n\n// 中文特征词汇到英文的映射\nconst FEATURE_MAPPING = {\n  // 年龄相关\n  '少女': 'young girl',\n  '青年女性': 'young woman', \n  '中年女性': 'middle-aged woman',\n  '少年': 'young boy',\n  '青年男性': 'young man',\n  '中年男性': 'middle-aged man',\n  \n  // 脸型\n  '瓜子脸': 'oval face',\n  '圆脸': 'round face',\n  '方脸': 'square face',\n  '长脸': 'long face',\n  '心形脸': 'heart-shaped face',\n  \n  // 眼睛\n  '大眼睛': 'large eyes',\n  '小眼睛': 'small eyes',\n  '丹凤眼': 'phoenix eyes',\n  '杏眼': 'almond eyes',\n  '圆眼': 'round eyes',\n  '深邃': 'deep-set eyes',\n  \n  // 发型\n  '长发': 'long hair',\n  '短发': 'short hair',\n  '中长发': 'medium length hair',\n  '卷发': 'curly hair',\n  '直发': 'straight hair',\n  '波浪发': 'wavy hair',\n  '马尾': 'ponytail',\n  '双马尾': 'twin tails',\n  '刘海': 'bangs',\n  \n  // 身材\n  '高挑': 'tall and slender',\n  '娇小': 'petite',\n  '匀称': 'well-proportioned',\n  '丰满': 'full-figured',\n  '苗条': 'slim',\n  '健美': 'athletic build',\n  \n  // 气质\n  '温柔': 'gentle',\n  '冷酷': 'cold',\n  '活泼': 'lively',\n  '优雅': 'elegant',\n  '知性': 'intellectual',\n  '神秘': 'mysterious',\n  '坚强': 'strong-willed',\n  '可爱': 'cute',\n  \n  // 服装\n  '职业装': 'business attire',\n  '休闲装': 'casual wear',\n  '正装': 'formal wear',\n  '校服': 'school uniform',\n  '古装': 'traditional costume',\n  '现代装': 'modern clothing'\n}\n\n// 艺术风格配置\nconst ART_STYLES = {\n  anime: {\n    keywords: 'anime style, manga style, cel shading, clean lines',\n    description: '日式动漫风格'\n  },\n  realistic: {\n    keywords: 'photorealistic, hyperrealistic, professional photography',\n    description: '写实摄影风格'\n  },\n  'semi-realistic': {\n    keywords: 'semi-realistic, digital art, detailed illustration',\n    description: '半写实插画风格'\n  },\n  'concept-art': {\n    keywords: 'concept art, character design, professional illustration',\n    description: '概念设计风格'\n  }\n}\n\n// 视角配置\nconst VIEW_TYPES = {\n  front: 'front view, facing camera, looking at viewer',\n  side: 'side view, 90-degree profile, side angle',\n  back: 'back view, rear angle, showing back',\n  'three-quarter': 'three-quarter view, slight angle'\n}\n\n// 质量等级配置\nconst QUALITY_LEVELS = {\n  standard: 'good quality, detailed',\n  high: 'high quality, high resolution, detailed, sharp focus',\n  masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'\n}\n\n/**\n * 从角色信息生成图像提示词\n */\nexport function generateCharacterPrompt(\n  character: CharacterInfo, \n  options: PromptOptions = {}\n): string {\n  const {\n    artStyle = 'anime',\n    viewType = 'front',\n    background = 'white',\n    quality = 'high',\n    customEnhancement = ''\n  } = options\n\n  // 构建提示词各部分\n  const parts: string[] = []\n\n  // 1. 质量和风格\n  parts.push(QUALITY_LEVELS[quality])\n  parts.push(ART_STYLES[artStyle].keywords)\n\n  // 2. 基础信息提取\n  const basicInfo = extractBasicInfo(character)\n  if (basicInfo) parts.push(basicInfo)\n\n  // 3. 外貌特征\n  const appearance = extractAppearanceFeatures(character)\n  if (appearance) parts.push(appearance)\n\n  // 4. 服装风格\n  const clothing = extractClothingStyle(character)\n  if (clothing) parts.push(clothing)\n\n  // 5. 气质表现\n  const temperament = extractTemperament(character)\n  if (temperament) parts.push(temperament)\n\n  // 6. 视角和姿态\n  parts.push(VIEW_TYPES[viewType])\n  parts.push('character design sheet, reference pose')\n\n  // 7. 背景设置\n  const backgroundDesc = getBackgroundDescription(background)\n  parts.push(backgroundDesc)\n\n  // 8. 技术参数\n  parts.push('professional character design, clean composition')\n\n  // 9. 自定义增强\n  if (customEnhancement.trim()) {\n    parts.push(customEnhancement)\n  }\n\n  return parts.filter(Boolean).join(', ')\n}\n\n/**\n * 提取基础信息（年龄、性别、身份）\n */\nfunction extractBasicInfo(character: CharacterInfo): string {\n  const info: string[] = []\n  \n  if (character.identity) {\n    // 从身份信息中提取年龄和职业\n    const ageMatch = character.identity.match(/(\\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/)\n    if (ageMatch) {\n      const age = ageMatch[1] || getAgeFromDescription(ageMatch[0])\n      info.push(`${age} years old`)\n    }\n    \n    // 提取性别（如果身份中包含）\n    if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {\n      info.push('woman')\n    } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {\n      info.push('man')\n    }\n    \n    // 提取职业\n    const profession = extractProfession(character.identity)\n    if (profession) info.push(profession)\n  }\n  \n  return info.join(' ')\n}\n\n/**\n * 提取外貌特征\n */\nfunction extractAppearanceFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n  \n  // 处理五官特征\n  if (character.facial) {\n    const facialFeatures = translateFeatures(character.facial)\n    features.push(facialFeatures)\n  }\n  \n  // 处理身材特征\n  if (character.physique) {\n    const bodyFeatures = translateFeatures(character.physique)\n    features.push(bodyFeatures)\n  }\n  \n  // 处理发型\n  if (character.hairstyle) {\n    const hairFeatures = translateFeatures(character.hairstyle)\n    features.push(hairFeatures)\n  }\n  \n  return features.filter(Boolean).join(', ')\n}\n\n/**\n * 提取服装风格\n */\nfunction extractClothingStyle(character: CharacterInfo): string {\n  if (!character.clothing) return ''\n  \n  return translateFeatures(character.clothing)\n}\n\n/**\n * 提取气质特征\n */\nfunction extractTemperament(character: CharacterInfo): string {\n  if (!character.personality) return ''\n  \n  const temperamentWords: string[] = []\n  \n  // 从性格描述中提取气质关键词\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (character.personality!.includes(chinese)) {\n      temperamentWords.push(english)\n    }\n  })\n  \n  // 添加表情描述\n  if (character.personality.includes('温柔') || character.personality.includes('善良')) {\n    temperamentWords.push('gentle smile', 'warm expression')\n  } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {\n    temperamentWords.push('serious expression', 'cold gaze')\n  } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {\n    temperamentWords.push('cheerful smile', 'bright expression')\n  }\n  \n  return temperamentWords.join(', ')\n}\n\n/**\n * 翻译特征描述\n */\nfunction translateFeatures(chineseText: string): string {\n  let result = chineseText\n  \n  // 使用映射表进行翻译\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    const regex = new RegExp(chinese, 'g')\n    result = result.replace(regex, english)\n  })\n  \n  // 清理和优化\n  result = result\n    .replace(/[，。；：]/g, ',') // 替换中文标点\n    .replace(/\\s+/g, ' ') // 合并空格\n    .replace(/,+/g, ',') // 合并逗号\n    .trim()\n  \n  return result\n}\n\n/**\n * 从描述中提取年龄\n */\nfunction getAgeFromDescription(ageDesc: string): string {\n  const ageMap: Record<string, string> = {\n    '十几岁': '16',\n    '二十多岁': '25', \n    '三十多岁': '35',\n    '四十多岁': '45'\n  }\n  return ageMap[ageDesc] || '25'\n}\n\n/**\n * 提取职业信息\n */\nfunction extractProfession(identity: string): string {\n  const professions: Record<string, string> = {\n    '学生': 'student',\n    '老师': 'teacher',\n    '医生': 'doctor',\n    '护士': 'nurse',\n    '律师': 'lawyer',\n    '工程师': 'engineer',\n    '设计师': 'designer',\n    '程序员': 'programmer',\n    '经理': 'manager',\n    '秘书': 'secretary',\n    '销售': 'salesperson',\n    '警察': 'police officer',\n    '军人': 'soldier',\n    '艺术家': 'artist',\n    '作家': 'writer',\n    '记者': 'journalist'\n  }\n  \n  for (const [chinese, english] of Object.entries(professions)) {\n    if (identity.includes(chinese)) {\n      return english\n    }\n  }\n  \n  return ''\n}\n\n/**\n * 获取背景描述\n */\nfunction getBackgroundDescription(background: string): string {\n  const backgrounds: Record<string, string> = {\n    white: 'clean white background',\n    transparent: 'transparent background',\n    simple: 'simple background',\n    detailed: 'detailed background scene'\n  }\n  \n  return backgrounds[background] || 'clean white background'\n}\n\n/**\n * 生成负面提示词\n */\nexport function generateNegativePrompt(): string {\n  return [\n    'blurry', 'low quality', 'pixelated', 'deformed', 'distorted',\n    'extra limbs', 'missing limbs', 'extra fingers', 'missing fingers',\n    'bad anatomy', 'bad proportions', 'ugly', 'duplicate',\n    'watermark', 'signature', 'text', 'logo',\n    'oversaturated', 'undersaturated', 'overexposed', 'underexposed'\n  ].join(', ')\n}\n\n/**\n * 生成角色一致性提示词（用于三视图）\n */\nexport function generateConsistencyPrompt(character: CharacterInfo, basePrompt: string): {\n  front: string\n  side: string\n  back: string\n} {\n  // 提取身材和服饰特征（保持一致）\n  const consistentFeatures = extractConsistentFeatures(character)\n\n  // 添加强化细节一致性的约束\n  const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories'\n\n  return {\n    // 正面：完整脸部可见\n    front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 侧面：90度侧脸轮廓\n    side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 背面：看不到脸\n    back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`\n  }\n}\n\n/**\n * 提取一致性特征（身材、服饰、发型等保持不变的特征）\n */\nfunction extractConsistentFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n\n  // 身材特征（保持一致）\n  if (character.physique) {\n    features.push(character.physique)\n  }\n\n  // 发型和发色（保持一致）\n  if (character.hairstyle) {\n    features.push(character.hairstyle)\n  }\n\n  // 服饰风格（保持一致，强调细节一致性）\n  if (character.clothing) {\n    features.push(character.clothing)\n    // 添加细节一致性约束\n    features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理')\n  }\n\n  // 身份特征（保持一致）\n  if (character.identity) {\n    features.push(character.identity)\n  }\n\n  // 添加强化一致性约束\n  features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰')\n\n  return features.join('，')\n}\n\nfunction extractFaceShape(facial: string): string {\n  for (const [chinese, english] of Object.entries(FEATURE_MAPPING)) {\n    if (facial.includes(chinese) && chinese.includes('脸')) {\n      return english\n    }\n  }\n  return ''\n}\n\nfunction extractHairStyle(hairstyle: string): string {\n  const hairFeatures: string[] = []\n  \n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {\n      hairFeatures.push(english)\n    }\n  })\n  \n  return hairFeatures.join(' ')\n}\n"], "names": [], "mappings": "AAAA,aAAa;AACb,2BAA2B;;;;;;AAoB3B,eAAe;AACf,MAAM,kBAAkB;IACtB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IAER,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IAEP,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IA<PERSON>,KAAK;IAC<PERSON>,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;AACT;AAEA,SAAS;AACT,MAAM,aAAa;IACjB,OAAO;QACL,UAAU;QACV,aAAa;IACf;IACA,WAAW;QACT,UAAU;QACV,aAAa;IACf;IACA,kBAAkB;QAChB,UAAU;QACV,aAAa;IACf;IACA,eAAe;QACb,UAAU;QACV,aAAa;IACf;AACF;AAEA,OAAO;AACP,MAAM,aAAa;IACjB,OAAO;IACP,MAAM;IACN,MAAM;IACN,iBAAiB;AACnB;AAEA,SAAS;AACT,MAAM,iBAAiB;IACrB,UAAU;IACV,MAAM;IACN,aAAa;AACf;AAKO,SAAS,wBACd,SAAwB,EACxB,UAAyB,CAAC,CAAC;IAE3B,MAAM,EACJ,WAAW,OAAO,EAClB,WAAW,OAAO,EAClB,aAAa,OAAO,EACpB,UAAU,MAAM,EAChB,oBAAoB,EAAE,EACvB,GAAG;IAEJ,WAAW;IACX,MAAM,QAAkB,EAAE;IAE1B,WAAW;IACX,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ;IAClC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ;IAExC,YAAY;IACZ,MAAM,YAAY,iBAAiB;IACnC,IAAI,WAAW,MAAM,IAAI,CAAC;IAE1B,UAAU;IACV,MAAM,aAAa,0BAA0B;IAC7C,IAAI,YAAY,MAAM,IAAI,CAAC;IAE3B,UAAU;IACV,MAAM,WAAW,qBAAqB;IACtC,IAAI,UAAU,MAAM,IAAI,CAAC;IAEzB,UAAU;IACV,MAAM,cAAc,mBAAmB;IACvC,IAAI,aAAa,MAAM,IAAI,CAAC;IAE5B,WAAW;IACX,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;IAC/B,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,iBAAiB,yBAAyB;IAChD,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,IAAI,CAAC;IAEX,WAAW;IACX,IAAI,kBAAkB,IAAI,IAAI;QAC5B,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC;AACpC;AAEA;;CAEC,GACD,SAAS,iBAAiB,SAAwB;IAChD,MAAM,OAAiB,EAAE;IAEzB,IAAI,UAAU,QAAQ,EAAE;QACtB,gBAAgB;QAChB,MAAM,WAAW,UAAU,QAAQ,CAAC,KAAK,CAAC;QAC1C,IAAI,UAAU;YACZ,MAAM,MAAM,QAAQ,CAAC,EAAE,IAAI,sBAAsB,QAAQ,CAAC,EAAE;YAC5D,KAAK,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;QAC9B;QAEA,gBAAgB;QAChB,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YAC9G,KAAK,IAAI,CAAC;QACZ,OAAO,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YACrH,KAAK,IAAI,CAAC;QACZ;QAEA,OAAO;QACP,MAAM,aAAa,kBAAkB,UAAU,QAAQ;QACvD,IAAI,YAAY,KAAK,IAAI,CAAC;IAC5B;IAEA,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,SAAS;IACT,IAAI,UAAU,MAAM,EAAE;QACpB,MAAM,iBAAiB,kBAAkB,UAAU,MAAM;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,SAAS;IACT,IAAI,UAAU,QAAQ,EAAE;QACtB,MAAM,eAAe,kBAAkB,UAAU,QAAQ;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;IACP,IAAI,UAAU,SAAS,EAAE;QACvB,MAAM,eAAe,kBAAkB,UAAU,SAAS;QAC1D,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO,SAAS,MAAM,CAAC,SAAS,IAAI,CAAC;AACvC;AAEA;;CAEC,GACD,SAAS,qBAAqB,SAAwB;IACpD,IAAI,CAAC,UAAU,QAAQ,EAAE,OAAO;IAEhC,OAAO,kBAAkB,UAAU,QAAQ;AAC7C;AAEA;;CAEC,GACD,SAAS,mBAAmB,SAAwB;IAClD,IAAI,CAAC,UAAU,WAAW,EAAE,OAAO;IAEnC,MAAM,mBAA6B,EAAE;IAErC,gBAAgB;IAChB,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,WAAW,CAAE,QAAQ,CAAC,UAAU;YAC5C,iBAAiB,IAAI,CAAC;QACxB;IACF;IAEA,SAAS;IACT,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QAChF,iBAAiB,IAAI,CAAC,gBAAgB;IACxC,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,sBAAsB;IAC9C,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,kBAAkB;IAC1C;IAEA,OAAO,iBAAiB,IAAI,CAAC;AAC/B;AAEA;;CAEC,GACD,SAAS,kBAAkB,WAAmB;IAC5C,IAAI,SAAS;IAEb,YAAY;IACZ,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,MAAM,QAAQ,IAAI,OAAO,SAAS;QAClC,SAAS,OAAO,OAAO,CAAC,OAAO;IACjC;IAEA,QAAQ;IACR,SAAS,OACN,OAAO,CAAC,WAAW,KAAK,SAAS;KACjC,OAAO,CAAC,QAAQ,KAAK,OAAO;KAC5B,OAAO,CAAC,OAAO,KAAK,OAAO;KAC3B,IAAI;IAEP,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,OAAe;IAC5C,MAAM,SAAiC;QACrC,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,QAAQ,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,kBAAkB,QAAgB;IACzC,MAAM,cAAsC;QAC1C,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,aAAc;QAC5D,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,yBAAyB,UAAkB;IAClD,MAAM,cAAsC;QAC1C,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;IACZ;IAEA,OAAO,WAAW,CAAC,WAAW,IAAI;AACpC;AAKO,SAAS;IACd,OAAO;QACL;QAAU;QAAe;QAAa;QAAY;QAClD;QAAe;QAAiB;QAAiB;QACjD;QAAe;QAAmB;QAAQ;QAC1C;QAAa;QAAa;QAAQ;QAClC;QAAiB;QAAkB;QAAe;KACnD,CAAC,IAAI,CAAC;AACT;AAKO,SAAS,0BAA0B,SAAwB,EAAE,UAAkB;IAKpF,kBAAkB;IAClB,MAAM,qBAAqB,0BAA0B;IAErD,eAAe;IACf,MAAM,oBAAoB;IAE1B,OAAO;QACL,YAAY;QACZ,OAAO,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAExG,aAAa;QACb,MAAM,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAE5G,UAAU;QACV,MAAM,CAAC,+BAA+B,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;IAC3G;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,cAAc;IACd,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,IAAI,CAAC,UAAU,SAAS;IACnC;IAEA,qBAAqB;IACrB,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;QAChC,YAAY;QACZ,SAAS,IAAI,CAAC,WAAW,WAAW;IACtC;IAEA,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,YAAY;IACZ,SAAS,IAAI,CAAC,QAAQ,WAAW,QAAQ,WAAW;IAEpD,OAAO,SAAS,IAAI,CAAC;AACvB;AAEA,SAAS,iBAAiB,MAAc;IACtC,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,iBAAkB;QAChE,IAAI,OAAO,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,MAAM;YACrD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,SAAiB;IACzC,MAAM,eAAyB,EAAE;IAEjC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,QAAQ,CAAC,YAAY,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,KAAK,GAAG;YACpF,aAAa,IAAI,CAAC;QACpB;IACF;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\nimport {\n  ArrowLeft,\n  Upload,\n  Users,\n  Film,\n  Video,\n  FileText,\n  Calendar,\n  AlertCircle,\n  CheckCircle,\n  Play,\n  BookOpen,\n  ChevronDown,\n  X\n} from 'lucide-react'\nimport ModelSelector from '@/components/ModelSelector'\nimport Layout from '@/components/Layout'\nimport ProjectFileUpload from '@/components/ProjectFileUpload'\nimport CharacterManager from '@/components/CharacterManager'\nimport EpisodeManager from '@/components/EpisodeManager'\nimport DetailedPlotExtraction from '@/components/DetailedPlotExtraction'\nimport VideoSegmentViewer from '@/components/VideoSegmentViewer'\nimport EpisodeVideoCard from '@/components/EpisodeVideoCard'\nimport ConsistencyVideoGenerator from '@/components/ConsistencyVideoGenerator'\nimport { Project, Character, Episode } from '@/types'\n\ntype TabType = 'upload' | 'novel' | 'characters' | 'episodes' | 'videos'\n\nexport default function ProjectDetailPage() {\n  const params = useParams()\n  const router = useRouter()\n  const searchParams = useSearchParams()\n  const projectId = params.id as string\n  \n  const [project, setProject] = useState<Project | null>(null)\n  const [characters, setCharacters] = useState<Character[]>([])\n  const [episodes, setEpisodes] = useState<Episode[]>([])\n\n  // 从URL参数中获取当前标签页，如果没有则默认为'upload'\n  const getInitialTab = (): TabType => {\n    const tab = searchParams.get('tab') as TabType\n    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']\n    return validTabs.includes(tab) ? tab : 'upload'\n  }\n\n  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab())\n  const [loading, setLoading] = useState(true)\n\n  // 更新标签页并同时更新URL\n  const updateActiveTab = (tab: TabType) => {\n    setActiveTab(tab)\n    const newSearchParams = new URLSearchParams(searchParams.toString())\n    newSearchParams.set('tab', tab)\n    router.replace(`/projects/${projectId}?${newSearchParams.toString()}`, { scroll: false })\n  }\n  const [error, setError] = useState<string | null>(null)\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [selectedModel, setSelectedModel] = useState<string>('')\n\n  // 增强提示词相关状态\n  const [showPromptDropdown, setShowPromptDropdown] = useState(false)\n  const [enhancePrompt, setEnhancePrompt] = useState('')\n  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')\n\n  // 详细剧情提取相关状态\n  const [showDetailedPlotExtraction, setShowDetailedPlotExtraction] = useState(false)\n  const [currentEpisodeId, setCurrentEpisodeId] = useState('')\n  const [currentEpisodeTitle, setCurrentEpisodeTitle] = useState('')\n  const [currentEpisodeContent, setCurrentEpisodeContent] = useState('')\n\n  // 视频片段查看器相关状态\n  const [showVideoSegmentViewer, setShowVideoSegmentViewer] = useState(false)\n  const [videoEpisodeId, setVideoEpisodeId] = useState('')\n\n  // 视频生成模式：'auto' | 'manual'，默认为手动模式\n  const [videoGenerationMode, setVideoGenerationMode] = useState<'auto' | 'manual'>('manual')\n\n  // 一致性视频生成器相关状态\n  const [showConsistencyVideoGenerator, setShowConsistencyVideoGenerator] = useState(false)\n  const [consistencyEpisodeId, setConsistencyEpisodeId] = useState('')\n  const [consistencyEpisodeTitle, setConsistencyEpisodeTitle] = useState('')\n\n  // 文件上传成功处理\n  const handleUploadSuccess = (updatedProject: any) => {\n    setProject(updatedProject.project)\n    setCharacters(updatedProject.characters || [])\n    setEpisodes(updatedProject.episodes || [])\n    updateActiveTab('characters')\n  }\n\n  // 文件上传错误处理\n  const handleUploadError = (errorMessage: string) => {\n    setError(errorMessage)\n  }\n\n  // 剧情分析处理\n  const handleAnalyzePlot = async (episodeId: string) => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/analyze-plot`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新剧集状态\n        setEpisodes(prev =>\n          prev.map(ep =>\n            ep.id === episodeId\n              ? { ...ep, plotInfo: data.data }\n              : ep\n          )\n        )\n      } else {\n        throw new Error(data.error || '剧情分析失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '剧情分析失败')\n    }\n  }\n\n  // 视频生成处理\n  const handleGenerateVideo = async (episodeId: string) => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/generate-video`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新剧集状态\n        setEpisodes(prev =>\n          prev.map(ep =>\n            ep.id === episodeId\n              ? { ...ep, status: 'video_generated' }\n              : ep\n          )\n        )\n        // 可以在这里显示视频脚本模态框\n      } else {\n        throw new Error(data.error || '视频生成失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '视频生成失败')\n    }\n  }\n\n  // 提取详细剧情信息处理\n  const handleExtractDetailedPlot = (episodeId: string) => {\n    const episode = episodes.find(ep => ep.id === episodeId)\n    if (episode) {\n      setCurrentEpisodeId(episodeId)\n      setCurrentEpisodeTitle(episode.title)\n      setCurrentEpisodeContent(episode.content)\n      setShowDetailedPlotExtraction(true)\n    }\n  }\n\n  // 打开一致性视频生成器\n  const handleOpenConsistencyVideoGenerator = (episodeId: string) => {\n    const episode = episodes.find(ep => ep.id === episodeId)\n    if (episode) {\n      setConsistencyEpisodeId(episodeId)\n      setConsistencyEpisodeTitle(episode.title)\n      setShowConsistencyVideoGenerator(true)\n    }\n  }\n\n  // 一致性视频生成成功处理\n  const handleConsistencyVideoGenerated = (videoData: any) => {\n    console.log('✅ 一致性视频生成成功:', videoData)\n    // 可以在这里更新UI状态或显示成功消息\n    alert(`一致性视频生成成功！\\n生成ID: ${videoData.generationId}\\n角色数量: ${videoData.consistencyInfo.characterCount}\\n参考图像: ${videoData.consistencyInfo.referenceImageCount} 个`)\n  }\n\n  // 生成剧情视频处理\n  const handleGenerateStoryVideo = async (episodeId: string, prompt: string, modelId?: string) => {\n    try {\n      console.log('🎬 开始生成剧情视频，episodeId:', episodeId)\n\n      // 首先检查是否已有视频生成记录\n      const checkResponse = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`)\n      const checkData = await checkResponse.json()\n\n      console.log('📊 检查现有片段结果:', checkData)\n\n      if (checkData.success && checkData.data && checkData.data.segments.length > 0) {\n        console.log(`⚠️ 发现现有片段 ${checkData.data.segments.length} 个，显示确认对话框`)\n\n        // 如果已有视频记录，显示确认对话框\n        const confirmed = confirm(\n          `检测到该剧集已有 ${checkData.data.segments.length} 个视频片段。\\n\\n` +\n          `重新生成将会：\\n` +\n          `• 删除所有现有的视频片段\\n` +\n          `• 停止正在进行的生成任务\\n` +\n          `• 重新开始生成新的视频片段\\n\\n` +\n          `确定要继续吗？`\n        )\n\n        console.log('👤 用户确认结果:', confirmed)\n\n        if (!confirmed) {\n          console.log('❌ 用户取消操作')\n          return // 用户取消操作\n        }\n\n        console.log('✅ 用户确认继续，开始重新生成')\n      } else {\n        console.log('✨ 没有现有片段，直接开始生成')\n      }\n\n      const response = await fetch('/api/ai/generate-story-video', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          episodeId,\n          prompt,\n          projectId,\n          modelId,\n          generationMode: videoGenerationMode // 传递生成模式\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 显示详细的生成结果\n        const message = data.data.message || '剧情视频片段创建完成！'\n        const modeMessage = videoGenerationMode === 'auto'\n          ? `共${data.data.totalSegments}个片段正在自动生成中...`\n          : `共创建了${data.data.totalSegments}个片段，请在\"剧情视频片段\"页面手动生成每个片段。\\n\\n💡 当前为手动生成模式，您可以选择性地生成需要的片段。`\n        alert(`${message}\\n\\n${modeMessage}`)\n        setShowDetailedPlotExtraction(false)\n\n        // 直接打开视频片段查看器，不切换标签页\n        // 这样用户可以继续在当前标签页查看剧情内容\n        setVideoEpisodeId(episodeId)\n        setShowVideoSegmentViewer(true)\n      } else {\n        // 处理角色图像先决条件错误\n        if (data.requirementType === 'character_images') {\n          const missingChars = data.missingCharacters?.join('、') || '某些角色'\n          const errorMessage = `❌ 视频生成失败\\n\\n${data.error}\\n\\n解决方案：\\n1. 点击\"角色管理\"标签页\\n2. 为 ${missingChars} 生成角色形象\\n3. 确保每个角色都有正面、侧面、背面三视图\\n4. 重新尝试生成视频`\n          alert(errorMessage)\n\n          // 自动切换到角色管理标签页\n          setActiveTab('characters')\n        } else {\n          throw new Error(data.error || '剧情视频生成失败')\n        }\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '剧情视频生成失败')\n    }\n  }\n\n  // 保存增强提示词\n  const handleSaveEnhancePrompt = () => {\n    setSavedEnhancePrompt(enhancePrompt)\n    setShowPromptDropdown(false)\n    // 保存到localStorage\n    const storageKey = `enhance_prompt_reanalyze_${projectId}`\n    localStorage.setItem(storageKey, enhancePrompt)\n  }\n\n  // 从localStorage加载增强提示词\n  useEffect(() => {\n    if (projectId) {\n      const storageKey = `enhance_prompt_reanalyze_${projectId}`\n      const savedPrompt = localStorage.getItem(storageKey)\n      if (savedPrompt) {\n        setSavedEnhancePrompt(savedPrompt)\n        setEnhancePrompt(savedPrompt)\n      }\n    }\n  }, [projectId])\n\n  // 手动分析人物剧情\n  const handleManualAnalyze = async () => {\n    if (!project?.content) {\n      setError('没有小说内容可供分析')\n      return\n    }\n\n    if (!selectedModel) {\n      setError('请先选择分析模型')\n      return\n    }\n\n    setIsAnalyzing(true)\n    setError(null)\n\n    try {\n      const response = await fetch(`/api/projects/${projectId}/analyze`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          modelId: selectedModel,\n          customPrompt: savedEnhancePrompt || undefined\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新项目数据\n        setProject(data.data.project)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n        // 切换到角色标签页\n        updateActiveTab('characters')\n      } else {\n        throw new Error(data.error || 'AI分析失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'AI分析失败')\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 监听URL参数变化，更新当前标签页\n  useEffect(() => {\n    const tab = searchParams.get('tab') as TabType\n    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']\n    if (tab && validTabs.includes(tab) && tab !== activeTab) {\n      setActiveTab(tab)\n    }\n  }, [searchParams, activeTab])\n\n  // 加载项目详情\n  useEffect(() => {\n    if (projectId) {\n      loadProjectDetail()\n    }\n  }, [projectId])\n\n  const loadProjectDetail = async () => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setProject(data.data)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n\n        // 只有在URL中没有指定标签页时，才根据项目状态设置默认标签页\n        if (!searchParams.get('tab')) {\n          if (data.data.status === 'created') {\n            updateActiveTab('upload')\n          } else if (data.data.status === 'uploaded') {\n            updateActiveTab('novel')\n          } else {\n            updateActiveTab('characters')\n          }\n        }\n      } else {\n        throw new Error(data.error || '加载项目失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '加载项目失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 重新加载项目数据的简化版本（用于角色管理器回调）\n  const loadProject = async () => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setProject(data.data)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n      }\n    } catch (error) {\n      console.error('重新加载项目失败:', error)\n    }\n  }\n\n  // 获取状态显示\n  const getStatusDisplay = (status: string) => {\n    const statusMap = {\n      created: { text: '已创建', color: 'bg-gray-100 text-gray-800', icon: FileText },\n      uploaded: { text: '已上传', color: 'bg-blue-100 text-blue-800', icon: Upload },\n      analyzing: { text: '分析中', color: 'bg-yellow-100 text-yellow-800', icon: Play },\n      completed: { text: '已完成', color: 'bg-green-100 text-green-800', icon: CheckCircle }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.created\n  }\n\n  // 标签页配置\n  const tabs = [\n    {\n      id: 'upload' as TabType,\n      name: '上传文件',\n      icon: Upload,\n      description: '上传小说文件进行AI分析',\n      disabled: false\n    },\n    {\n      id: 'novel' as TabType,\n      name: '小说',\n      icon: BookOpen,\n      description: '查看已上传的小说内容',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'characters' as TabType,\n      name: '角色',\n      icon: Users,\n      description: '查看和管理提取的角色信息',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'episodes' as TabType,\n      name: '剧集',\n      icon: Film,\n      description: '查看和管理章节剧集',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'videos' as TabType,\n      name: '视频',\n      icon: Video,\n      description: '生成和管理视频脚本',\n      disabled: !project || ['created', 'uploaded'].includes(project.status)\n    }\n  ]\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">加载项目中...</p>\n          </div>\n        </div>\n      </Layout>\n    )\n  }\n\n  if (error || !project) {\n    return (\n      <Layout>\n        <div className=\"text-center py-12\">\n          <AlertCircle className=\"mx-auto h-12 w-12 text-red-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">加载失败</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            {error || '项目不存在'}\n          </p>\n          <div className=\"mt-6\">\n            <Link\n              href=\"/projects\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700\"\n            >\n              <ArrowLeft className=\"mr-2\" size={16} />\n              返回项目列表\n            </Link>\n          </div>\n        </div>\n      </Layout>\n    )\n  }\n\n  const statusDisplay = getStatusDisplay(project.status)\n\n  return (\n    <Layout>\n      <div className=\"space-y-6\">\n        {/* 页面头部 */}\n        <div>\n          <Link\n            href=\"/projects\"\n            className=\"inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4\"\n          >\n            <ArrowLeft className=\"mr-2\" size={16} />\n            返回项目列表\n          </Link>\n          \n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">{project.name}</h1>\n              {project.description && (\n                <p className=\"mt-1 text-sm text-gray-600\">{project.description}</p>\n              )}\n              <div className=\"mt-2 flex items-center space-x-4\">\n                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>\n                  <statusDisplay.icon className=\"mr-1\" size={12} />\n                  {statusDisplay.text}\n                </span>\n                <div className=\"flex items-center text-sm text-gray-500\">\n                  <Calendar size={14} className=\"mr-1\" />\n                  创建于 {new Date(project.createdAt).toLocaleDateString('zh-CN')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 标签页导航 */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {tabs.map((tab) => {\n              const isActive = activeTab === tab.id\n              const isDisabled = tab.disabled\n              \n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => !isDisabled && updateActiveTab(tab.id)}\n                  disabled={isDisabled}\n                  className={`\n                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm\n                    ${isActive\n                      ? 'border-purple-500 text-purple-600'\n                      : isDisabled\n                      ? 'border-transparent text-gray-400 cursor-not-allowed'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }\n                  `}\n                >\n                  <tab.icon \n                    className={`mr-2 ${isActive ? 'text-purple-500' : isDisabled ? 'text-gray-400' : 'text-gray-400 group-hover:text-gray-500'}`} \n                    size={16} \n                  />\n                  {tab.name}\n                  {tab.id === 'characters' && characters.length > 0 && (\n                    <span className=\"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs\">\n                      {characters.length}\n                    </span>\n                  )}\n                  {tab.id === 'episodes' && episodes.length > 0 && (\n                    <span className=\"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs\">\n                      {episodes.length}\n                    </span>\n                  )}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 标签页内容 */}\n        <div className=\"mt-6\">\n          {activeTab === 'upload' && (\n            <ProjectFileUpload\n              projectId={projectId}\n              project={project}\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {activeTab === 'novel' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">小说内容</h3>\n                    <div className=\"text-sm text-gray-500\">\n                      字数：{project?.content?.length || 0} 字\n                    </div>\n                  </div>\n\n                  {project?.content ? (\n                    <>\n                      <div className=\"prose max-w-none\">\n                        <div className=\"bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto\">\n                          <pre className=\"whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed\">\n                            {project.content}\n                          </pre>\n                        </div>\n                      </div>\n\n                      {/* 分析区域 */}\n                      <div className=\"mt-6 space-y-4\">\n                        {/* 模型选择器 */}\n                        <div className=\"flex items-center justify-center space-x-3\">\n                          <span className=\"text-sm font-medium text-gray-700\">分析模型:</span>\n                          <ModelSelector\n                            selectedModel={selectedModel}\n                            onModelChange={setSelectedModel}\n                            className=\"w-64\"\n                          />\n                        </div>\n\n                        {/* 分析按钮 */}\n                        <div className=\"flex justify-center space-x-3\">\n                          <div className=\"relative flex\">\n                            <button\n                              onClick={handleManualAnalyze}\n                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}\n                              className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-l-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                              {isAnalyzing || project.status === 'analyzing' ? (\n                                <>\n                                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                                  AI分析中...\n                                </>\n                              ) : (\n                                <>\n                                  <Users className=\"mr-2\" size={20} />\n                                  {project.status === 'completed' ? 'AI重新分析人物剧情' : '自动分析人物剧情'}\n                                </>\n                              )}\n                            </button>\n\n                            <button\n                              onClick={() => setShowPromptDropdown(!showPromptDropdown)}\n                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}\n                              className=\"px-3 py-3 border border-transparent rounded-r-md border-l border-purple-500 text-base font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                              <ChevronDown size={16} />\n                            </button>\n\n                            {/* 增强提示词下拉框 */}\n                            {showPromptDropdown && (\n                              <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10\">\n                                <div className=\"p-4\">\n                                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    增强提示词设置\n                                  </label>\n                                  <textarea\n                                    value={enhancePrompt}\n                                    onChange={(e) => setEnhancePrompt(e.target.value)}\n                                    placeholder=\"输入增强提示词，用于优化AI分析效果...\"\n                                    className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none\"\n                                    rows={4}\n                                  />\n\n                                  {/* 保存按钮 */}\n                                  <div className=\"mt-3\">\n                                    <button\n                                      onClick={handleSaveEnhancePrompt}\n                                      className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2\"\n                                    >\n                                      保存增强提示词\n                                    </button>\n                                  </div>\n\n                                  {/* 操作按钮 */}\n                                  <div className=\"flex justify-end mt-3\">\n                                    <button\n                                      onClick={() => setShowPromptDropdown(false)}\n                                      className=\"text-sm text-gray-600 hover:text-gray-800\"\n                                    >\n                                      关闭\n                                    </button>\n                                  </div>\n\n                                  {/* 当前保存的提示词预览 */}\n                                  {savedEnhancePrompt && (\n                                    <div className=\"mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600\">\n                                      <div className=\"font-medium mb-1\">当前已保存的增强提示词：</div>\n                                      <div className=\"max-h-16 overflow-y-auto\">\n                                        {savedEnhancePrompt}\n                                      </div>\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n\n                          {project.status === 'completed' && (\n                            <div className=\"flex items-center text-sm text-green-600\">\n                              <CheckCircle className=\"mr-1\" size={16} />\n                              已完成分析，可重新分析\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <BookOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无小说内容</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        请先上传小说文件\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'characters' && (\n            <CharacterManager\n              projectId={params.id}\n              characters={project.characters || []}\n              onCharactersUpdate={loadProject}\n            />\n          )}\n\n          {activeTab === 'episodes' && (\n            <EpisodeManager\n              episodes={episodes}\n              onAnalyzePlot={handleAnalyzePlot}\n              onGenerateVideo={handleGenerateVideo}\n              onExtractDetailedPlot={handleExtractDetailedPlot}\n            />\n          )}\n\n          {activeTab === 'videos' && (\n            <div className=\"space-y-6\">\n              {/* 视频管理头部 */}\n              <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <Video className=\"text-purple-600 mr-2\" size={20} />\n                    <h3 className=\"text-lg font-medium text-gray-900\">剧情视频管理</h3>\n                  </div>\n                  <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n                    共 {episodes.length} 集\n                  </span>\n                </div>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  支持传统分段视频生成和新的一致性约束视频生成\n                </p>\n              </div>\n\n              {/* 剧集视频列表 */}\n              {episodes.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {episodes.map((episode, index) => (\n                    <EpisodeVideoCard\n                      key={episode.id}\n                      episode={episode}\n                      episodeIndex={index}\n                      projectId={projectId}\n                      onViewSegments={() => {\n                        setVideoEpisodeId(episode.id)\n                        setShowVideoSegmentViewer(true)\n                      }}\n                      onGenerateConsistencyVideo={() => handleOpenConsistencyVideoGenerator(episode.id)}\n                    />\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <Video className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无剧集</h3>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    请先上传小说并分析剧集\n                  </p>\n                </div>\n              )}\n\n              {/* 说明信息 */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n                  分段视频生成说明\n                </h4>\n                <div className=\"text-sm text-blue-700 space-y-1\">\n                  <p>• <strong>智能分段：</strong>AI自动将剧情分解为3-8个短视频片段</p>\n                  <p>• <strong>并行生成：</strong>多个片段同时生成，提高效率</p>\n                  <p>• <strong>实时进度：</strong>可查看每个片段的生成状态和进度</p>\n                  <p>• <strong>独立播放：</strong>每个片段可单独播放和下载</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 详细剧情提取模态框 */}\n        <DetailedPlotExtraction\n          episodeId={currentEpisodeId}\n          episodeTitle={currentEpisodeTitle}\n          episodeContent={currentEpisodeContent}\n          isOpen={showDetailedPlotExtraction}\n          onClose={() => setShowDetailedPlotExtraction(false)}\n          onGenerateStoryVideo={handleGenerateStoryVideo}\n          videoGenerationMode={videoGenerationMode}\n          onVideoGenerationModeChange={setVideoGenerationMode}\n        />\n\n        {/* 视频片段查看器 */}\n        <VideoSegmentViewer\n          episodeId={videoEpisodeId}\n          projectId={projectId}\n          isOpen={showVideoSegmentViewer}\n          onClose={() => setShowVideoSegmentViewer(false)}\n        />\n\n        {/* 一致性视频生成器模态框 */}\n        {showConsistencyVideoGenerator && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">一致性视频生成</h2>\n                  <button\n                    onClick={() => setShowConsistencyVideoGenerator(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <X size={24} />\n                  </button>\n                </div>\n\n                <ConsistencyVideoGenerator\n                  episodeId={consistencyEpisodeId}\n                  episodeTitle={consistencyEpisodeTitle}\n                  onVideoGenerated={handleConsistencyVideoGenerated}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,OAAO,EAAE;IAE3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAEtD,kCAAkC;IAClC,MAAM,gBAAgB;QACpB,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,MAAM,YAAuB;YAAC;YAAU;YAAS;YAAc;YAAY;SAAS;QACpF,OAAO,UAAU,QAAQ,CAAC,OAAO,MAAM;IACzC;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gBAAgB;IAChB,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,MAAM,kBAAkB,IAAI,gBAAgB,aAAa,QAAQ;QACjE,gBAAgB,GAAG,CAAC,OAAO;QAC3B,OAAO,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,gBAAgB,QAAQ,IAAI,EAAE;YAAE,QAAQ;QAAM;IACzF;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,YAAY;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,aAAa;IACb,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,cAAc;IACd,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,mCAAmC;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAElF,eAAe;IACf,MAAM,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,WAAW;IACX,MAAM,sBAAsB,CAAC;QAC3B,WAAW,eAAe,OAAO;QACjC,cAAc,eAAe,UAAU,IAAI,EAAE;QAC7C,YAAY,eAAe,QAAQ,IAAI,EAAE;QACzC,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,SAAS;IACT,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,UAAU,aAAa,CAAC,EAAE;gBAC5F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,KACP,GAAG,EAAE,KAAK,YACN;4BAAE,GAAG,EAAE;4BAAE,UAAU,KAAK,IAAI;wBAAC,IAC7B;YAGV,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,UAAU,eAAe,CAAC,EAAE;gBAC9F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,KACP,GAAG,EAAE,KAAK,YACN;4BAAE,GAAG,EAAE;4BAAE,QAAQ;wBAAkB,IACnC;YAGR,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,aAAa;IACb,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QAC9C,IAAI,SAAS;YACX,oBAAoB;YACpB,uBAAuB,QAAQ,KAAK;YACpC,yBAAyB,QAAQ,OAAO;YACxC,8BAA8B;QAChC;IACF;IAEA,aAAa;IACb,MAAM,sCAAsC,CAAC;QAC3C,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QAC9C,IAAI,SAAS;YACX,wBAAwB;YACxB,2BAA2B,QAAQ,KAAK;YACxC,iCAAiC;QACnC;IACF;IAEA,cAAc;IACd,MAAM,kCAAkC,CAAC;QACvC,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,qBAAqB;QACrB,MAAM,CAAC,kBAAkB,EAAE,UAAU,YAAY,CAAC,QAAQ,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,eAAe,CAAC,mBAAmB,CAAC,EAAE,CAAC;IAClK;IAEA,WAAW;IACX,MAAM,2BAA2B,OAAO,WAAmB,QAAgB;QACzE,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,iBAAiB;YACjB,MAAM,gBAAgB,MAAM,MAAM,CAAC,iCAAiC,EAAE,WAAW;YACjF,MAAM,YAAY,MAAM,cAAc,IAAI;YAE1C,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,IAAI,UAAU,OAAO,IAAI,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7E,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;gBAEnE,mBAAmB;gBACnB,MAAM,YAAY,QAChB,CAAC,SAAS,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,GACvD,CAAC,SAAS,CAAC,GACX,CAAC,eAAe,CAAC,GACjB,CAAC,eAAe,CAAC,GACjB,CAAC,kBAAkB,CAAC,GACpB,CAAC,OAAO,CAAC;gBAGX,QAAQ,GAAG,CAAC,cAAc;gBAE1B,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CAAC;oBACZ,QAAO,SAAS;gBAClB;gBAEA,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;oBACA,gBAAgB,oBAAoB,SAAS;gBAC/C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY;gBACZ,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,IAAI;gBACrC,MAAM,cAAc,wBAAwB,SACxC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,GAC1C,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,yDAAyD,CAAC;gBAC7F,MAAM,GAAG,QAAQ,IAAI,EAAE,aAAa;gBACpC,8BAA8B;gBAE9B,qBAAqB;gBACrB,uBAAuB;gBACvB,kBAAkB;gBAClB,0BAA0B;YAC5B,OAAO;gBACL,eAAe;gBACf,IAAI,KAAK,eAAe,KAAK,oBAAoB;oBAC/C,MAAM,eAAe,KAAK,iBAAiB,EAAE,KAAK,QAAQ;oBAC1D,MAAM,eAAe,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,gCAAgC,EAAE,aAAa,4CAA4C,CAAC;oBAC3I,MAAM;oBAEN,eAAe;oBACf,aAAa;gBACf,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,UAAU;IACV,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,sBAAsB;QACtB,kBAAkB;QAClB,MAAM,aAAa,CAAC,yBAAyB,EAAE,WAAW;QAC1D,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW;gBACb,MAAM,aAAa,CAAC,yBAAyB,EAAE,WAAW;gBAC1D,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,sBAAsB;oBACtB,iBAAiB;gBACnB;YACF;QACF;sCAAG;QAAC;KAAU;IAEd,WAAW;IACX,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,SAAS;YACrB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,eAAe;YAClB,SAAS;YACT;QACF;QAEA,eAAe;QACf,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,QAAQ,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,cAAc,sBAAsB;gBACtC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,WAAW,KAAK,IAAI,CAAC,OAAO;gBAC5B,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;gBACpC,WAAW;gBACX,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,MAAM,YAAuB;gBAAC;gBAAU;gBAAS;gBAAc;gBAAY;aAAS;YACpF,IAAI,OAAO,UAAU,QAAQ,CAAC,QAAQ,QAAQ,WAAW;gBACvD,aAAa;YACf;QACF;sCAAG;QAAC;QAAc;KAAU;IAE5B,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;sCAAG;QAAC;KAAU;IAEd,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;gBACpB,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAEpC,iCAAiC;gBACjC,IAAI,CAAC,aAAa,GAAG,CAAC,QAAQ;oBAC5B,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,WAAW;wBAClC,gBAAgB;oBAClB,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,YAAY;wBAC1C,gBAAgB;oBAClB,OAAO;wBACL,gBAAgB;oBAClB;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;gBACpB,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY;YAChB,SAAS;gBAAE,MAAM;gBAAO,OAAO;gBAA6B,MAAM,iNAAA,CAAA,WAAQ;YAAC;YAC3E,UAAU;gBAAE,MAAM;gBAAO,OAAO;gBAA6B,MAAM,yMAAA,CAAA,SAAM;YAAC;YAC1E,WAAW;gBAAE,MAAM;gBAAO,OAAO;gBAAiC,MAAM,qMAAA,CAAA,OAAI;YAAC;YAC7E,WAAW;gBAAE,MAAM;gBAAO,OAAO;gBAA+B,MAAM,8NAAA,CAAA,cAAW;YAAC;QACpF;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAEA,QAAQ;IACR,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,MAAM,yMAAA,CAAA,SAAM;YACZ,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,UAAU,CAAC,WAAW;gBAAC;gBAAW;aAAW,CAAC,QAAQ,CAAC,QAAQ,MAAM;QACvE;KACD;IAED,IAAI,SAAS;QACX,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,6LAAC,+HAAA,CAAA,UAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,SAAS;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,MAAM,gBAAgB,iBAAiB,QAAQ,MAAM;IAErD,qBACE,6LAAC,+HAAA,CAAA,UAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,QAAQ,IAAI;;;;;;oCAC7D,QAAQ,WAAW,kBAClB,6LAAC;wCAAE,WAAU;kDAA8B,QAAQ,WAAW;;;;;;kDAEhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,wEAAwE,EAAE,cAAc,KAAK,EAAE;;kEAC/G,6LAAC,cAAc,IAAI;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAC1C,cAAc,IAAI;;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;oDAClC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,WAAW,cAAc,IAAI,EAAE;4BACrC,MAAM,aAAa,IAAI,QAAQ;4BAE/B,qBACE,6LAAC;gCAEC,SAAS,IAAM,CAAC,cAAc,gBAAgB,IAAI,EAAE;gCACpD,UAAU;gCACV,WAAW,CAAC;;oBAEV,EAAE,WACE,sCACA,aACA,wDACA,6EACH;kBACH,CAAC;;kDAED,6LAAC,IAAI,IAAI;wCACP,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,aAAa,kBAAkB,2CAA2C;wCAC5H,MAAM;;;;;;oCAEP,IAAI,IAAI;oCACR,IAAI,EAAE,KAAK,gBAAgB,WAAW,MAAM,GAAG,mBAC9C,6LAAC;wCAAK,WAAU;kDACb,WAAW,MAAM;;;;;;oCAGrB,IAAI,EAAE,KAAK,cAAc,SAAS,MAAM,GAAG,mBAC1C,6LAAC;wCAAK,WAAU;kDACb,SAAS,MAAM;;;;;;;+BAzBf,IAAI,EAAE;;;;;wBA8BjB;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;;wBACZ,cAAc,0BACb,6LAAC,0IAAA,CAAA,UAAiB;4BAChB,WAAW;4BACX,SAAS;4BACT,iBAAiB;4BACjB,eAAe;;;;;;wBAIlB,cAAc,yBACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;;wDAAwB;wDACjC,SAAS,SAAS,UAAU;wDAAE;;;;;;;;;;;;;wCAIrC,SAAS,wBACR;;8DACE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO;;;;;;;;;;;;;;;;8DAMtB,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC,sIAAA,CAAA,UAAa;oEACZ,eAAe;oEACf,eAAe;oEACf,WAAU;;;;;;;;;;;;sEAKd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,SAAS;4EACT,UAAU,eAAe,QAAQ,MAAM,KAAK,eAAe,CAAC;4EAC5D,WAAU;sFAET,eAAe,QAAQ,MAAM,KAAK,4BACjC;;kGACE,6LAAC;wFAAI,WAAU;;;;;;oFAAuE;;6GAIxF;;kGACE,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;wFAAO,MAAM;;;;;;oFAC7B,QAAQ,MAAM,KAAK,cAAc,eAAe;;;;;;;;sFAKvD,6LAAC;4EACC,SAAS,IAAM,sBAAsB,CAAC;4EACtC,UAAU,eAAe,QAAQ,MAAM,KAAK,eAAe,CAAC;4EAC5D,WAAU;sFAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gFAAC,MAAM;;;;;;;;;;;wEAIpB,oCACC,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAM,WAAU;kGAA+C;;;;;;kGAGhE,6LAAC;wFACC,OAAO;wFACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wFAChD,aAAY;wFACZ,WAAU;wFACV,MAAM;;;;;;kGAIR,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FACC,SAAS;4FACT,WAAU;sGACX;;;;;;;;;;;kGAMH,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FACC,SAAS,IAAM,sBAAsB;4FACrC,WAAU;sGACX;;;;;;;;;;;oFAMF,oCACC,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAI,WAAU;0GAAmB;;;;;;0GAClC,6LAAC;gGAAI,WAAU;0GACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gEASd,QAAQ,MAAM,KAAK,6BAClB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;4EAAO,MAAM;;;;;;wEAAM;;;;;;;;;;;;;;;;;;;;yEAQpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUrD,cAAc,8BACb,6LAAC,yIAAA,CAAA,UAAgB;4BACf,WAAW,OAAO,EAAE;4BACpB,YAAY,QAAQ,UAAU,IAAI,EAAE;4BACpC,oBAAoB;;;;;;wBAIvB,cAAc,4BACb,6LAAC,uIAAA,CAAA,UAAc;4BACb,UAAU;4BACV,eAAe;4BACf,iBAAiB;4BACjB,uBAAuB;;;;;;wBAI1B,cAAc,0BACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAuB,MAAM;;;;;;sEAC9C,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;8DAEpD,6LAAC;oDAAK,WAAU;;wDAA+E;wDAC1F,SAAS,MAAM;wDAAC;;;;;;;;;;;;;sDAGvB,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;gCAM3C,SAAS,MAAM,GAAG,kBACjB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,yIAAA,CAAA,UAAgB;4CAEf,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,gBAAgB;gDACd,kBAAkB,QAAQ,EAAE;gDAC5B,0BAA0B;4CAC5B;4CACA,4BAA4B,IAAM,oCAAoC,QAAQ,EAAE;2CAR3E,QAAQ,EAAE;;;;;;;;;yDAarB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAO9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;sEAAE,6LAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,6LAAC;;wDAAE;sEAAE,6LAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,6LAAC;;wDAAE;sEAAE,6LAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,6LAAC;;wDAAE;sEAAE,6LAAC;sEAAO;;;;;;wDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrC,6LAAC,+IAAA,CAAA,UAAsB;oBACrB,WAAW;oBACX,cAAc;oBACd,gBAAgB;oBAChB,QAAQ;oBACR,SAAS,IAAM,8BAA8B;oBAC7C,sBAAsB;oBACtB,qBAAqB;oBACrB,6BAA6B;;;;;;8BAI/B,6LAAC,2IAAA,CAAA,UAAkB;oBACjB,WAAW;oBACX,WAAW;oBACX,QAAQ;oBACR,SAAS,IAAM,0BAA0B;;;;;;gBAI1C,+CACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CACC,SAAS,IAAM,iCAAiC;4CAChD,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAIb,6LAAC,kJAAA,CAAA,UAAyB;oCACxB,WAAW;oCACX,cAAc;oCACd,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC;GAhyBwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAHd", "debugId": null}}]}