const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateDoubaoModels() {
  try {
    console.log('🔄 更新豆包模型配置...\n');
    
    // 1. 检查现有的豆包模型
    const existingModels = await prisma.aIConfig.findMany({
      where: {
        provider: 'doubao'
      }
    });
    
    console.log(`📊 找到 ${existingModels.length} 个现有豆包模型:`);
    for (const model of existingModels) {
      console.log(`   - ${model.name} (${model.model})`);
    }
    
    // 2. 添加新的豆包 Lite 模型
    const newModels = [
      {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn',
        name: '豆包 Seedance 1.0 Lite I2V',
        description: '豆包图生视频模型，支持基于图像生成5-8秒视频，适合角色动作和场景动画',
        enabled: false,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: true,
        temperature: 0.7,
        apiKey: ''
      },
      {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj',
        name: '豆包 Seedance 1.0 Lite T2V',
        description: '豆包文生视频模型，支持基于文本生成5-8秒视频，适合创意场景和环境建立',
        enabled: false,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        apiKey: ''
      }
    ];
    
    console.log('\n📝 添加新模型...');
    
    for (const modelData of newModels) {
      // 检查模型是否已存在
      const existing = await prisma.aIConfig.findFirst({
        where: {
          provider: modelData.provider,
          model: modelData.model
        }
      });
      
      if (existing) {
        console.log(`⚠️ 模型 ${modelData.name} 已存在，跳过添加`);
        
        // 更新现有模型的属性
        await prisma.aIConfig.update({
          where: { id: existing.id },
          data: {
            name: modelData.name,
            description: modelData.description,
            supportsVideo: modelData.supportsVideo,
            supportsImage: modelData.supportsImage,
            supportsImageToVideo: modelData.supportsImageToVideo
          }
        });
        console.log(`✅ 已更新模型 ${modelData.name} 的属性`);
      } else {
        const created = await prisma.aIConfig.create({
          data: modelData
        });
        console.log(`✅ 已添加新模型: ${created.name} (ID: ${created.id})`);
      }
    }
    
    // 3. 更新旧模型的属性
    console.log('\n🔄 更新现有模型属性...');
    
    // 更新原有的豆包 Pro 模型
    const proModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250622184757-q77k7'
      }
    });
    
    if (proModel) {
      await prisma.aIConfig.update({
        where: { id: proModel.id },
        data: {
          name: '豆包 Seedance 1.0 Pro',
          description: '豆包专业版视频生成模型，支持高质量文生视频，适合复杂场景和专业制作',
          supportsVideo: true,
          supportsImage: false,
          supportsImageToVideo: false
        }
      });
      console.log(`✅ 已更新模型: ${proModel.name}`);
    }
    
    // 4. 显示最终配置
    console.log('\n📋 最终豆包模型配置:');
    
    const finalModels = await prisma.aIConfig.findMany({
      where: {
        provider: 'doubao'
      },
      orderBy: { createdAt: 'asc' }
    });
    
    for (const model of finalModels) {
      console.log(`\n🎬 ${model.name}:`);
      console.log(`   模型ID: ${model.model}`);
      console.log(`   启用状态: ${model.enabled ? '✅ 启用' : '❌ 未启用'}`);
      console.log(`   支持视频: ${model.supportsVideo ? '✅' : '❌'}`);
      console.log(`   支持图生视频: ${model.supportsImageToVideo ? '✅' : '❌'}`);
      console.log(`   API密钥: ${model.apiKey ? '已配置' : '未配置'}`);
      console.log(`   描述: ${model.description}`);
    }
    
    // 5. 使用指南
    console.log('\n📖 使用指南:');
    console.log('1. 在AI配置页面为新模型配置API密钥');
    console.log('2. 启用需要使用的模型');
    console.log('3. 系统将根据片段类型智能选择最适合的模型:');
    console.log('   - 有角色图像的片段 → I2V (图生视频)');
    console.log('   - 环境建立场景 → T2V (文生视频)');
    console.log('   - 复杂动作场景 → Pro (专业版)');
    
    console.log('\n✅ 豆包模型配置更新完成！');
    
  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateDoubaoModels();
