const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCompleteMicroEpisode() {
  try {
    console.log('🎬 测试完整微剧集制作流程...\n');
    
    // 1. 清理之前的测试数据
    console.log('🧹 清理之前的测试数据...');
    await prisma.videoSegment.deleteMany({
      where: {
        storyVideo: {
          episodeId: { contains: '第三盏灯' }
        }
      }
    });
    
    await prisma.storyVideo.deleteMany({
      where: {
        episodeId: { contains: '第三盏灯' }
      }
    });
    
    // 2. 找到测试剧集
    const episode = await prisma.episode.findFirst({
      where: { title: { contains: '第三盏灯' } },
      include: { plotInfo: true }
    });
    
    if (!episode) {
      console.log('❌ 未找到测试剧集');
      return;
    }
    
    console.log(`✅ 找到测试剧集: ${episode.title}`);
    console.log(`📋 剧情信息: ${episode.plotInfo ? '有' : '无'}`);
    
    // 3. 生成微剧集视频
    console.log('\n🎬 开始生成微剧集视频...');
    
    const generateResponse = await fetch('http://localhost:3000/api/ai/generate-story-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        episodeId: episode.id,
        plotInfo: episode.plotInfo || {
          generatedPrompt: '张三在雪夜雁门关的惊险遭遇，发现内鬼阴谋的故事'
        }
      })
    });
    
    if (!generateResponse.ok) {
      const errorText = await generateResponse.text();
      console.log('❌ 视频生成请求失败:', errorText);
      return;
    }
    
    const generateResult = await generateResponse.json();
    console.log('✅ 视频生成请求成功');
    console.log(`📹 故事视频ID: ${generateResult.storyVideoId}`);
    
    const storyVideoId = generateResult.storyVideoId;
    
    // 4. 监控片段生成进度
    console.log('\n⏳ 监控片段生成进度...');
    
    let allCompleted = false;
    let attempts = 0;
    const maxAttempts = 30; // 最多等待5分钟
    
    while (!allCompleted && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
      attempts++;
      
      const segments = await prisma.videoSegment.findMany({
        where: { storyVideoId },
        orderBy: { segmentIndex: 'asc' }
      });
      
      console.log(`\n📊 第${attempts}次检查 (${new Date().toLocaleTimeString()}):`);
      
      let completed = 0;
      let failed = 0;
      let generating = 0;
      
      for (const segment of segments) {
        console.log(`   片段${segment.segmentIndex}: ${segment.title.substring(0, 30)}...`);
        console.log(`      状态: ${segment.status}, 时长: ${segment.duration}秒`);
        
        if (segment.status === 'completed') completed++;
        else if (segment.status === 'failed') failed++;
        else generating++;
      }
      
      console.log(`📈 进度统计: 完成${completed}个, 生成中${generating}个, 失败${failed}个`);
      
      if (completed > 0 && generating === 0) {
        allCompleted = true;
        console.log('🎉 所有片段生成完成！');
      } else if (failed > 0 && generating === 0) {
        console.log('❌ 有片段生成失败，停止等待');
        break;
      }
    }
    
    // 5. 检查微剧集标准符合度
    console.log('\n📏 检查微剧集标准符合度...');
    
    const completedSegments = await prisma.videoSegment.findMany({
      where: { 
        storyVideoId,
        status: 'completed'
      },
      orderBy: { segmentIndex: 'asc' }
    });
    
    if (completedSegments.length === 0) {
      console.log('❌ 没有完成的片段，无法进行微剧集分析');
      return;
    }
    
    console.log(`✅ 分析 ${completedSegments.length} 个完成的片段:`);
    
    let totalDuration = 0;
    let microEpisodeCompliant = 0;
    
    for (const segment of completedSegments) {
      const duration = segment.duration || 0;
      totalDuration += duration;
      
      const isCompliant = duration >= 5 && duration <= 8;
      if (isCompliant) microEpisodeCompliant++;
      
      console.log(`   片段${segment.segmentIndex}: ${duration}秒 ${isCompliant ? '✅' : '❌'} (${segment.segmentType})`);
    }
    
    const avgDuration = totalDuration / completedSegments.length;
    const complianceRate = (microEpisodeCompliant / completedSegments.length) * 100;
    
    console.log(`\n📊 微剧集标准分析:`);
    console.log(`   总片段数: ${completedSegments.length}`);
    console.log(`   总时长: ${totalDuration}秒`);
    console.log(`   平均时长: ${avgDuration.toFixed(1)}秒`);
    console.log(`   符合5-8秒标准: ${microEpisodeCompliant}/${completedSegments.length} (${complianceRate.toFixed(1)}%)`);
    
    // 6. 测试视频合并功能
    if (completedSegments.length >= 2) {
      console.log('\n🔗 测试视频合并功能...');
      
      const mergeResponse = await fetch('http://localhost:3000/api/video/merge-segments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          storyVideoId,
          outputFormat: 'mp4',
          quality: 'high'
        })
      });
      
      if (mergeResponse.ok) {
        const mergeResult = await mergeResponse.json();
        console.log('✅ 视频合并成功!');
        console.log(`📹 合并视频URL: ${mergeResult.data.mergedVideoUrl}`);
        console.log(`⏱️ 总时长: ${mergeResult.data.totalDuration}秒`);
        console.log(`🎬 转场效果: ${mergeResult.data.transitions.length}个`);
        
        // 更新数据库记录
        await prisma.storyVideo.update({
          where: { id: storyVideoId },
          data: {
            status: 'completed',
            mergedVideoUrl: mergeResult.data.mergedVideoUrl,
            totalDuration: mergeResult.data.totalDuration
          }
        });
        
      } else {
        const errorText = await mergeResponse.text();
        console.log('❌ 视频合并失败:', errorText);
      }
    } else {
      console.log('⚠️ 完成片段不足2个，跳过合并测试');
    }
    
    // 7. 微剧集方案效果评估
    console.log('\n📈 微剧集方案效果评估:');
    
    console.log('\n✅ 成功实现的功能:');
    console.log('   - 片段化生成: 将长剧情分解为多个短片段');
    console.log('   - 时长控制: 每个片段控制在5-8秒');
    console.log('   - 类型分类: 自动识别片段类型');
    console.log('   - 智能合并: FFmpeg自动合并片段');
    console.log('   - 转场效果: 根据片段类型智能选择转场');
    
    console.log('\n🎯 微剧集方案优势验证:');
    console.log(`   - 技术可行性: ${complianceRate > 80 ? '✅ 高' : complianceRate > 50 ? '⚠️ 中' : '❌ 低'}`);
    console.log(`   - 时长精确性: ${avgDuration >= 5 && avgDuration <= 8 ? '✅ 符合标准' : '❌ 需要调优'}`);
    console.log(`   - 片段完整性: ${completedSegments.length >= 6 ? '✅ 充足' : '⚠️ 偏少'}`);
    console.log(`   - 合并功能: ${completedSegments.length >= 2 ? '✅ 可用' : '❌ 不可用'}`);
    
    console.log('\n🚀 下一步优化建议:');
    if (complianceRate < 80) {
      console.log('   - 优化时长控制算法，提高5-8秒标准符合率');
    }
    if (avgDuration > 8) {
      console.log('   - 调整AI模型参数，缩短片段时长');
    }
    if (completedSegments.length < 6) {
      console.log('   - 增加片段数量，达到微剧集标准的6-8个片段');
    }
    
    console.log('\n🎬 完整微剧集制作流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCompleteMicroEpisode();
