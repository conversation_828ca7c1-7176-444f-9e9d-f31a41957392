// 智能模型选择器 - 根据片段类型和内容自动选择最适合的AI模型

interface ModelConfig {
  id: string
  provider: string
  model: string
  name: string
  supportsVideo: boolean
  supportsImage: boolean
  supportsImageToVideo: boolean
  enabled: boolean
  apiKey: string
}

interface SegmentInfo {
  type: string
  title: string
  description?: string
  prompt?: string
  hasCharacterImages: boolean
  characterCount: number
  sceneComplexity: 'low' | 'medium' | 'high'
  emotionalIntensity: 'low' | 'medium' | 'high'
}

interface ModelSelectionResult {
  selectedModel: ModelConfig
  reason: string
  confidence: number
  alternatives: ModelConfig[]
}

export class SmartModelSelector {
  private models: ModelConfig[]

  constructor(models: ModelConfig[]) {
    this.models = models.filter(m => m.enabled && m.apiKey)
  }

  /**
   * 智能选择最适合的模型
   */
  selectBestModel(segmentInfo: SegmentInfo): ModelSelectionResult {
    const availableModels = this.getAvailableModels()
    
    if (availableModels.length === 0) {
      throw new Error('没有可用的视频生成模型')
    }

    // 计算每个模型的适配分数
    const modelScores = availableModels.map(model => ({
      model,
      score: this.calculateModelScore(model, segmentInfo),
      reason: this.getSelectionReason(model, segmentInfo)
    }))

    // 按分数排序
    modelScores.sort((a, b) => b.score - a.score)

    const best = modelScores[0]
    const alternatives = modelScores.slice(1, 3).map(item => item.model)

    return {
      selectedModel: best.model,
      reason: best.reason,
      confidence: best.score,
      alternatives
    }
  }

  /**
   * 获取可用的视频生成模型
   */
  private getAvailableModels(): ModelConfig[] {
    return this.models.filter(model => 
      model.supportsVideo || model.supportsImageToVideo
    )
  }

  /**
   * 计算模型适配分数 (0-100)
   */
  private calculateModelScore(model: ModelConfig, segmentInfo: SegmentInfo): number {
    let score = 50 // 基础分数

    // 1. 模型类型适配性
    if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {
      score += 30 // 图生视频模型 + 有角色图像 = 高分
    } else if (model.supportsVideo && !segmentInfo.hasCharacterImages) {
      score += 20 // 文生视频模型 + 无角色图像 = 中等分
    } else if (model.supportsVideo) {
      score += 10 // 文生视频模型作为备选
    }

    // 2. 片段类型适配性
    score += this.getSegmentTypeScore(model, segmentInfo.type)

    // 3. 角色数量适配性
    if (segmentInfo.characterCount > 0 && model.supportsImageToVideo) {
      score += Math.min(segmentInfo.characterCount * 5, 15)
    }

    // 4. 场景复杂度适配性
    score += this.getComplexityScore(model, segmentInfo.sceneComplexity)

    // 5. 情感强度适配性
    score += this.getEmotionalScore(model, segmentInfo.emotionalIntensity)

    // 6. 特定模型优势
    score += this.getModelSpecificBonus(model, segmentInfo)

    return Math.min(Math.max(score, 0), 100)
  }

  /**
   * 根据片段类型计算分数
   */
  private getSegmentTypeScore(model: ModelConfig, segmentType: string): number {
    const typeScores: Record<string, Record<string, number>> = {
      'environment': {
        'text-to-video': 15,    // 环境建立适合文生视频
        'image-to-video': 5
      },
      'character': {
        'text-to-video': 5,
        'image-to-video': 20    // 角色引入适合图生视频
      },
      'action': {
        'text-to-video': 10,
        'image-to-video': 15    // 动作场景图生视频略好
      },
      'emotion': {
        'text-to-video': 5,
        'image-to-video': 25    // 情感表达强烈推荐图生视频
      },
      'dialogue': {
        'text-to-video': 8,
        'image-to-video': 18    // 对话场景推荐图生视频
      },
      'suspense': {
        'text-to-video': 12,
        'image-to-video': 8     // 悬念场景文生视频更有创意
      }
    }

    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'
    return typeScores[segmentType]?.[modelType] || 0
  }

  /**
   * 根据场景复杂度计算分数
   */
  private getComplexityScore(model: ModelConfig, complexity: string): number {
    const complexityScores: Record<string, Record<string, number>> = {
      'low': {
        'image-to-video': 10,   // 简单场景图生视频更稳定
        'text-to-video': 5
      },
      'medium': {
        'image-to-video': 8,
        'text-to-video': 8      // 中等复杂度两者相当
      },
      'high': {
        'image-to-video': 5,
        'text-to-video': 12     // 复杂场景文生视频更灵活
      }
    }

    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'
    return complexityScores[complexity]?.[modelType] || 0
  }

  /**
   * 根据情感强度计算分数
   */
  private getEmotionalScore(model: ModelConfig, intensity: string): number {
    const emotionalScores: Record<string, Record<string, number>> = {
      'low': {
        'image-to-video': 5,
        'text-to-video': 8
      },
      'medium': {
        'image-to-video': 10,
        'text-to-video': 6
      },
      'high': {
        'image-to-video': 15,   // 高情感强度推荐图生视频
        'text-to-video': 3
      }
    }

    const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video'
    return emotionalScores[intensity]?.[modelType] || 0
  }

  /**
   * 特定模型的额外加分
   */
  private getModelSpecificBonus(model: ModelConfig, segmentInfo: SegmentInfo): number {
    let bonus = 0

    // 豆包模型特定优势
    if (model.provider === 'doubao') {
      if (model.model === 'ep-20250624013749-dbrbn') {
        // Seedance 1.0 Lite I2V - 图生视频模型在角色一致性方面的优势
        if (segmentInfo.hasCharacterImages) {
          bonus += 15
        }
      } else if (model.model === 'ep-20250624013223-bwdtj') {
        // Seedance 1.0 Lite T2V - 文生视频模型在创意性方面的优势
        if (segmentInfo.sceneComplexity === 'high') {
          bonus += 12
        }
      } else if (model.model === 'ep-20250622184757-q77k7') {
        // Seedance 1.0 Pro - 原有文生视频模型
        if (segmentInfo.sceneComplexity === 'high') {
          bonus += 8
        }
      }
    }

    return bonus
  }

  /**
   * 生成选择理由
   */
  private getSelectionReason(model: ModelConfig, segmentInfo: SegmentInfo): string {
    const reasons: string[] = []

    if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {
      reasons.push('基于角色参考图像生成，确保角色一致性')
    }

    if (segmentInfo.type === 'emotion' && model.supportsImageToVideo) {
      reasons.push('情感表达场景，图生视频效果更佳')
    }

    if (segmentInfo.type === 'environment' && !model.supportsImageToVideo) {
      reasons.push('环境建立场景，文生视频创意性更强')
    }

    if (segmentInfo.characterCount > 1 && model.supportsImageToVideo) {
      reasons.push('多角色场景，图生视频角色识别更准确')
    }

    if (segmentInfo.sceneComplexity === 'high' && !model.supportsImageToVideo) {
      reasons.push('复杂场景，文生视频适应性更好')
    }

    if (model.provider === 'doubao') {
      reasons.push('豆包模型在中文内容理解方面表现优秀')
    }

    return reasons.length > 0 ? reasons.join('；') : '综合评估最适合的模型'
  }

  /**
   * 分析片段信息
   */
  static analyzeSegment(
    segment: any, 
    characterImages: string[] = []
  ): SegmentInfo {
    const title = segment.title || ''
    const description = segment.description || ''
    const prompt = segment.prompt || ''
    const text = `${title} ${description} ${prompt}`.toLowerCase()

    // 分析片段类型
    const type = this.detectSegmentType(text)

    // 分析角色数量
    const characterCount = this.countCharacters(text)

    // 分析场景复杂度
    const sceneComplexity = this.analyzeSceneComplexity(text)

    // 分析情感强度
    const emotionalIntensity = this.analyzeEmotionalIntensity(text)

    return {
      type,
      title,
      description,
      prompt,
      hasCharacterImages: characterImages.length > 0,
      characterCount,
      sceneComplexity,
      emotionalIntensity
    }
  }

  /**
   * 检测片段类型
   */
  private static detectSegmentType(text: string): string {
    const typeKeywords = {
      environment: ['环境', '场景', '建立', '雪夜', '城楼', '关城', '山峰'],
      character: ['登场', '出现', '角色', '人物', '李四', '张三'],
      action: ['动作', '跑', '扛', '冲', '抽刀', '战斗', '移动'],
      emotion: ['表情', '情感', '内心', '思考', '回忆', '愤怒', '悲伤'],
      dialogue: ['对话', '说', '讲述', '报告', '讨论', '交谈'],
      suspense: ['悬念', '远方', '狼烟', '威胁', '危机', '神秘']
    }

    for (const [type, keywords] of Object.entries(typeKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return type
      }
    }

    return 'action' // 默认类型
  }

  /**
   * 统计角色数量
   */
  private static countCharacters(text: string): number {
    const characterNames = ['张三', '李四', '王五', '赵六']
    return characterNames.filter(name => text.includes(name.toLowerCase())).length
  }

  /**
   * 分析场景复杂度
   */
  private static analyzeSceneComplexity(text: string): 'low' | 'medium' | 'high' {
    const complexityIndicators = {
      high: ['战斗', '追逐', '多人', '复杂', '混乱', '激烈'],
      medium: ['对话', '互动', '移动', '检查', '讨论'],
      low: ['站立', '观察', '思考', '静止', '单人']
    }

    for (const [level, indicators] of Object.entries(complexityIndicators)) {
      if (indicators.some(indicator => text.includes(indicator))) {
        return level as 'low' | 'medium' | 'high'
      }
    }

    return 'medium'
  }

  /**
   * 分析情感强度
   */
  private static analyzeEmotionalIntensity(text: string): 'low' | 'medium' | 'high' {
    const intensityIndicators = {
      high: ['愤怒', '恐惧', '激动', '震惊', '绝望', '狂怒'],
      medium: ['担心', '疑惑', '严肃', '警惕', '紧张'],
      low: ['平静', '思考', '观察', '正常', '淡定']
    }

    for (const [level, indicators] of Object.entries(intensityIndicators)) {
      if (indicators.some(indicator => text.includes(indicator))) {
        return level as 'low' | 'medium' | 'high'
      }
    }

    return 'medium'
  }
}
