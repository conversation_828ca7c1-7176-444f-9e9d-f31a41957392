'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, AlertCircle, User, Image } from 'lucide-react'

interface Character {
  id: string
  name: string
  generatedImages: string | null
}

interface CharacterImageStatus {
  name: string
  hasImages: boolean
  imageCount: number
  missingImages: string[]
}

interface CharacterImageCheckerProps {
  projectId: string
  onAllReady?: (ready: boolean) => void
}

export default function CharacterImageChecker({ projectId, onAllReady }: CharacterImageCheckerProps) {
  const [characters, setCharacters] = useState<Character[]>([])
  const [characterStatus, setCharacterStatus] = useState<CharacterImageStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [allReady, setAllReady] = useState(false)

  useEffect(() => {
    loadCharacters()
  }, [projectId])

  useEffect(() => {
    const ready = characterStatus.length > 0 && characterStatus.every(char => char.hasImages)
    setAllReady(ready)
    onAllReady?.(ready)
  }, [characterStatus, onAllReady])

  const loadCharacters = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/characters?projectId=${projectId}`)
      const data = await response.json()

      if (data.success) {
        setCharacters(data.data)
        analyzeCharacterImages(data.data)
      }
    } catch (error) {
      console.error('加载角色失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const analyzeCharacterImages = (characters: Character[]) => {
    const status: CharacterImageStatus[] = characters.map(character => {
      let hasImages = false
      let imageCount = 0
      let missingImages: string[] = []

      if (character.generatedImages) {
        try {
          const images = JSON.parse(character.generatedImages)
          const imageTypes = ['front', 'side', 'back']
          
          imageTypes.forEach(type => {
            if (images[type]) {
              imageCount++
            } else {
              missingImages.push(type === 'front' ? '正面' : type === 'side' ? '侧面' : '背面')
            }
          })

          hasImages = imageCount === 3
        } catch (e) {
          missingImages = ['正面', '侧面', '背面']
        }
      } else {
        missingImages = ['正面', '侧面', '背面']
      }

      return {
        name: character.name,
        hasImages,
        imageCount,
        missingImages
      }
    })

    setCharacterStatus(status)
  }

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="text-blue-500 mr-2" size={20} />
          <span className="text-blue-700">检查角色图像状态中...</span>
        </div>
      </div>
    )
  }

  if (characters.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="text-yellow-500 mr-2" size={20} />
          <span className="text-yellow-700">项目中没有角色信息，请先创建角色</span>
        </div>
      </div>
    )
  }

  const readyCount = characterStatus.filter(char => char.hasImages).length
  const totalCount = characterStatus.length

  return (
    <div className={`border rounded-lg p-4 ${allReady ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium flex items-center">
          {allReady ? (
            <CheckCircle className="text-green-500 mr-2" size={20} />
          ) : (
            <XCircle className="text-red-500 mr-2" size={20} />
          )}
          角色图像状态检查
        </h3>
        <span className={`text-sm px-2 py-1 rounded ${allReady ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {readyCount}/{totalCount} 就绪
        </span>
      </div>

      {allReady ? (
        <div className="text-green-700 text-sm">
          ✅ 所有角色都有完整的参考图像，可以开始生成视频
        </div>
      ) : (
        <div className="space-y-2">
          <div className="text-red-700 text-sm mb-3">
            ❌ 以下角色缺少参考图像，需要先生成角色形象：
          </div>
          
          {characterStatus.map((char, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
              <div className="flex items-center">
                <User className="text-gray-400 mr-2" size={16} />
                <span className="font-medium">{char.name}</span>
              </div>
              
              <div className="flex items-center">
                {char.hasImages ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle size={16} className="mr-1" />
                    <span className="text-sm">完整 (3/3)</span>
                  </div>
                ) : (
                  <div className="flex items-center text-red-600">
                    <XCircle size={16} className="mr-1" />
                    <span className="text-sm">
                      缺少: {char.missingImages.join('、')} ({char.imageCount}/3)
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}

          <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
            <div className="text-blue-700 text-sm">
              <strong>解决方案：</strong>
              <ol className="mt-1 ml-4 list-decimal">
                <li>点击"角色管理"标签页</li>
                <li>点击角色卡片进入详情</li>
                <li>点击"形象设置"标签</li>
                <li>点击"AI一键生成角色形象"</li>
                <li>等待生成完成后重新尝试</li>
              </ol>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
