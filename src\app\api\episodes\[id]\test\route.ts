import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    success: true,
    message: 'Episode test API is working',
    episodeId: params.id,
    timestamp: new Date().toISOString()
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const body = await request.json()
  
  return NextResponse.json({
    success: true,
    message: 'Episode test POST API is working',
    episodeId: params.id,
    receivedData: body,
    timestamp: new Date().toISOString()
  })
}
