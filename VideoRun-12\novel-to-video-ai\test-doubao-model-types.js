// 精确测试豆包两个模型的实际类型
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoModelTypes() {
  try {
    console.log('🔍 精确测试豆包两个模型的实际类型...\n');

    // 获取豆包模型配置
    const doubaoModels = await prisma.aIConfig.findMany({
      where: {
        provider: 'doubao',
        OR: [
          { model: 'ep-20250624013223-bwdtj' },
          { model: 'ep-20250624013749-dbrbn' }
        ]
      }
    });

    console.log('找到的豆包模型:');
    doubaoModels.forEach(model => {
      console.log(`- ${model.name} (${model.model})`);
    });

    const testPrompt = "一个美丽的花园，阳光明媚，微风轻拂，花朵摇摆";
    const testImageUrl = "https://example.com/test-image.jpg";

    for (const model of doubaoModels) {
      console.log(`\n🧪 测试模型: ${model.name} (${model.model})`);
      
      // 测试1: 纯文本请求 (T2V)
      console.log('  测试1: 纯文本请求 (T2V模式)...');
      try {
        const textOnlyResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.apiKey}`
          },
          body: JSON.stringify({
            model: model.model,
            content: [
              {
                type: "text",
                text: testPrompt
              }
            ]
          })
        });

        console.log(`    响应状态: ${textOnlyResponse.status}`);
        
        if (textOnlyResponse.ok) {
          const data = await textOnlyResponse.json();
          console.log(`    ✅ 纯文本请求成功 - 这是T2V模型!`);
          console.log(`    任务ID: ${data.id}`);
        } else {
          const errorText = await textOnlyResponse.text();
          console.log(`    ❌ 纯文本请求失败:`);
          console.log(`    ${errorText}`);
          
          // 检查是否是"需要图像"的错误
          if (errorText.includes('image') && errorText.includes('require')) {
            console.log(`    🎯 确认: 这是I2V模型 (需要图像输入)`);
          }
        }
      } catch (error) {
        console.log(`    ❌ 请求异常: ${error.message}`);
      }

      // 测试2: 图像+文本请求 (I2V)
      console.log('  测试2: 图像+文本请求 (I2V模式)...');
      try {
        const imageTextResponse = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${model.apiKey}`
          },
          body: JSON.stringify({
            model: model.model,
            content: [
              {
                type: "text",
                text: testPrompt
              },
              {
                type: "image_url",
                image_url: {
                  url: testImageUrl
                }
              }
            ]
          })
        });

        console.log(`    响应状态: ${imageTextResponse.status}`);
        
        if (imageTextResponse.ok) {
          const data = await imageTextResponse.json();
          console.log(`    ✅ 图像+文本请求成功 - 这是I2V模型!`);
          console.log(`    任务ID: ${data.id}`);
        } else {
          const errorText = await imageTextResponse.text();
          console.log(`    ❌ 图像+文本请求失败:`);
          console.log(`    ${errorText}`);
          
          // 检查是否是"不支持图像"的错误
          if (errorText.includes('image') && (errorText.includes('not') || errorText.includes('invalid'))) {
            console.log(`    🎯 确认: 这是T2V模型 (不支持图像输入)`);
          }
        }
      } catch (error) {
        console.log(`    ❌ 请求异常: ${error.message}`);
      }

      console.log('  ----------------------------------------');
    }

    // 总结结论
    console.log('\n📊 测试结论总结:');
    console.log('根据API响应，我们可以确定:');
    console.log('- 如果纯文本请求成功 → T2V模型');
    console.log('- 如果纯文本请求失败且提示需要图像 → I2V模型');
    console.log('- 如果图像+文本请求成功 → I2V模型');
    console.log('- 如果图像+文本请求失败且提示不支持图像 → T2V模型');

    console.log('\n🔧 建议的配置修正:');
    console.log('根据测试结果，请更新模型配置中的 supportsImageToVideo 字段');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testDoubaoModelTypes();
