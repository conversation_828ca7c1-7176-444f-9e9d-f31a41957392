'use client'

import { useState } from 'react'
import { X, Video, Download, Copy, Camera, Palette, Music, Zap } from 'lucide-react'

interface VideoScriptModalProps {
  isOpen: boolean
  onClose: () => void
  videoScript: any
  episodeTitle: string
}

export default function VideoScriptModal({ 
  isOpen, 
  onClose, 
  videoScript, 
  episodeTitle 
}: VideoScriptModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'scenes' | 'technical'>('overview')
  const [copiedSection, setCopiedSection] = useState<string | null>(null)

  if (!isOpen || !videoScript) return null

  const script = videoScript.videoScript

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedSection(section)
      setTimeout(() => setCopiedSection(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const downloadScript = () => {
    const content = JSON.stringify(videoScript, null, 2)
    const blob = new Blob([content], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${episodeTitle}-视频脚本.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-purple-500 to-blue-500 text-white">
          <div>
            <h2 className="text-xl font-semibold flex items-center">
              <Video className="mr-2" size={24} />
              {episodeTitle} - 视频制作脚本
            </h2>
            <p className="text-purple-100 text-sm mt-1">
              AI生成的专业视频制作指导脚本
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={downloadScript}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              title="下载脚本"
            >
              <Download size={20} />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* 标签页 */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'overview'
                ? 'border-b-2 border-purple-500 text-purple-600 bg-purple-50'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Palette className="inline mr-2" size={16} />
            概览信息
          </button>
          <button
            onClick={() => setActiveTab('scenes')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'scenes'
                ? 'border-b-2 border-purple-500 text-purple-600 bg-purple-50'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Camera className="inline mr-2" size={16} />
            分镜脚本
          </button>
          <button
            onClick={() => setActiveTab('technical')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'technical'
                ? 'border-b-2 border-purple-500 text-purple-600 bg-purple-50'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Zap className="inline mr-2" size={16} />
            技术要求
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* 概览信息 */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-800 mb-2">基本信息</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>标题：</strong>{script.title}</div>
                    <div><strong>预计时长：</strong>{script.duration}</div>
                    <div><strong>场景数量：</strong>{script.scenes?.length || 0} 个</div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-semibold text-green-800 mb-2">风格定位</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>整体风格：</strong>{script.overallStyle}</div>
                    <div><strong>色彩基调：</strong>{script.colorPalette}</div>
                    <div><strong>目标观众：</strong>{script.targetAudience}</div>
                  </div>
                </div>
              </div>

              {/* 核心信息 */}
              {script.keyMessages && (
                <div className="bg-purple-50 rounded-lg p-4">
                  <h3 className="font-semibold text-purple-800 mb-3">核心信息</h3>
                  <div className="flex flex-wrap gap-2">
                    {script.keyMessages.map((message: string, index: number) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                      >
                        {message}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 分镜脚本 */}
          {activeTab === 'scenes' && (
            <div className="space-y-6">
              {script.scenes?.map((scene: any, index: number) => (
                <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-800">
                        场景 {scene.sceneNumber} - {scene.location}
                      </h3>
                      <button
                        onClick={() => copyToClipboard(JSON.stringify(scene, null, 2), `scene-${index}`)}
                        className="text-gray-500 hover:text-gray-700"
                        title="复制场景脚本"
                      >
                        <Copy size={16} />
                        {copiedSection === `scene-${index}` && (
                          <span className="ml-1 text-green-600 text-xs">已复制</span>
                        )}
                      </button>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {scene.timeOfDay} | 出场角色: {scene.characters?.join('、')}
                    </div>
                  </div>

                  <div className="p-4 space-y-4">
                    {/* 画面描述 */}
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2">📹 画面描述</h4>
                      <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">
                        {scene.visualDescription}
                      </p>
                    </div>

                    {/* 对话和动作 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {scene.dialogue && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">💬 对话内容</h4>
                          <p className="text-sm text-gray-700 bg-blue-50 p-3 rounded">
                            {scene.dialogue}
                          </p>
                        </div>
                      )}

                      {scene.action && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">🎬 动作描述</h4>
                          <p className="text-sm text-gray-700 bg-green-50 p-3 rounded">
                            {scene.action}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* 技术要求 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {scene.cameraWork && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">📷 镜头运用</h4>
                          <p className="text-xs text-gray-600 bg-yellow-50 p-2 rounded">
                            {scene.cameraWork}
                          </p>
                        </div>
                      )}

                      {scene.lighting && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">💡 灯光要求</h4>
                          <p className="text-xs text-gray-600 bg-orange-50 p-2 rounded">
                            {scene.lighting}
                          </p>
                        </div>
                      )}

                      {scene.mood && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">🎭 情绪氛围</h4>
                          <p className="text-xs text-gray-600 bg-pink-50 p-2 rounded">
                            {scene.mood}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* 道具和服装 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {scene.props && scene.props.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">🎪 道具清单</h4>
                          <div className="flex flex-wrap gap-1">
                            {scene.props.map((prop: string, propIndex: number) => (
                              <span
                                key={propIndex}
                                className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs"
                              >
                                {prop}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {scene.costume && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">👗 服装要求</h4>
                          <p className="text-xs text-gray-600 bg-purple-50 p-2 rounded">
                            {scene.costume}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* 音效和配乐 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {scene.soundEffects && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">🔊 音效要求</h4>
                          <p className="text-xs text-gray-600 bg-teal-50 p-2 rounded">
                            {scene.soundEffects}
                          </p>
                        </div>
                      )}

                      {scene.music && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">🎵 配乐建议</h4>
                          <p className="text-xs text-gray-600 bg-cyan-50 p-2 rounded">
                            {scene.music}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 技术要求 */}
          {activeTab === 'technical' && script.technicalRequirements && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 设备需求 */}
                {script.technicalRequirements.equipment && (
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-800 mb-3">📹 设备需求</h3>
                    <ul className="space-y-1">
                      {script.technicalRequirements.equipment.map((item: string, index: number) => (
                        <li key={index} className="text-sm text-blue-700">• {item}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* 后期软件 */}
                {script.technicalRequirements.software && (
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-3">💻 后期软件</h3>
                    <ul className="space-y-1">
                      {script.technicalRequirements.software.map((item: string, index: number) => (
                        <li key={index} className="text-sm text-green-700">• {item}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* 特效需求 */}
                {script.technicalRequirements.specialEffects && (
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-800 mb-3">✨ 特效需求</h3>
                    <ul className="space-y-1">
                      {script.technicalRequirements.specialEffects.map((item: string, index: number) => (
                        <li key={index} className="text-sm text-purple-700">• {item}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
