// 测试豆包视频生成API
async function testDoubaoVideoAPI() {
  try {
    console.log('🎬 测试豆包视频生成API...');
    
    const modelName = 'doubao-seedance-1-0-pro-250528';
    
    console.log('\n📝 API配置:');
    console.log('   模型名称:', modelName);
    console.log('   API端点: https://ark.cn-beijing.volces.com/api/v3/video_generation');
    console.log('   请求格式: 视频生成专用格式');
    
    // 1. 测试视频生成API格式
    console.log('\n🧪 1. 测试视频生成API格式...');
    
    const testResponse = await fetch('http://localhost:3000/api/models/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'doubao',
        model: modelName,
        apiKey: 'test-key'
      })
    });
    
    const testResult = await testResponse.json();
    
    console.log('📊 测试结果:');
    if (testResult.success) {
      console.log('✅ 连接测试成功');
    } else {
      console.log('❌ 连接测试失败:', testResult.error);
      
      if (testResult.error.includes('API key')) {
        console.log('💡 这是预期的（使用测试密钥）');
      } else if (testResult.error.includes('internal error')) {
        console.log('💡 服务临时不可用，会自动重试');
      } else if (testResult.error.includes('does not exist')) {
        console.log('💡 需要在火山方舟开通模型权限');
      } else if (testResult.error.includes('Not Found')) {
        console.log('💡 API端点可能需要调整');
      }
    }
    
    // 2. 验证请求格式
    console.log('\n📋 2. 验证请求格式...');
    
    const expectedRequestBody = {
      model: modelName,
      prompt: '测试连接',
      video_setting: {
        video_duration: 5,
        video_aspect_ratio: '16:9',
        video_resolution: '480p'
      }
    };
    
    console.log('✅ 视频生成请求格式:');
    console.log('   模型:', expectedRequestBody.model);
    console.log('   提示词:', expectedRequestBody.prompt);
    console.log('   视频时长:', expectedRequestBody.video_setting.video_duration, '秒');
    console.log('   宽高比:', expectedRequestBody.video_setting.video_aspect_ratio);
    console.log('   分辨率:', expectedRequestBody.video_setting.video_resolution);
    
    // 3. 检查API端点变化
    console.log('\n🔍 3. 检查API端点...');
    
    const possibleEndpoints = [
      'https://ark.cn-beijing.volces.com/api/v3/video_generation',
      'https://ark.cn-beijing.volces.com/api/v3/video-generation',
      'https://ark.cn-beijing.volces.com/api/v3/video/generation',
      'https://ark.cn-beijing.volces.com/api/v3/video/create',
      'https://ark.cn-beijing.volces.com/api/v3/models/video/generate'
    ];
    
    console.log('🔗 可能的API端点:');
    possibleEndpoints.forEach((endpoint, index) => {
      console.log(`   ${index + 1}. ${endpoint}`);
    });
    
    // 4. 显示完整的API调用示例
    console.log('\n📝 4. 完整的API调用示例...');
    
    const apiExample = {
      method: 'POST',
      url: 'https://ark.cn-beijing.volces.com/api/v3/video_generation',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
      },
      body: {
        model: 'doubao-seedance-1-0-pro-250528',
        prompt: '一个美丽的日落海滩场景，海浪轻拍沙滩',
        video_setting: {
          video_duration: 5,
          video_aspect_ratio: '16:9',
          video_resolution: '1080p'
        }
      }
    };
    
    console.log('✅ API调用示例:');
    console.log('   方法:', apiExample.method);
    console.log('   URL:', apiExample.url);
    console.log('   认证: Bearer Token');
    console.log('   内容类型: application/json');
    
    console.log('\n📦 请求体示例:');
    console.log(JSON.stringify(apiExample.body, null, 2));
    
    // 5. 预期响应格式
    console.log('\n📥 5. 预期响应格式...');
    
    const expectedResponse = {
      task_id: 'video_task_12345',
      status: 'submitted',
      message: '视频生成任务已提交'
    };
    
    console.log('✅ 成功响应示例:');
    console.log(JSON.stringify(expectedResponse, null, 2));
    
    // 6. 使用指南
    console.log('\n📖 6. 使用指南...');
    
    console.log('\n🔑 配置步骤:');
    console.log('   1. 获取火山引擎API密钥');
    console.log('   2. 开通豆包视频生成模型权限');
    console.log('   3. 在系统中配置API密钥');
    console.log('   4. 测试连接验证配置');
    console.log('   5. 开始使用视频生成功能');
    
    console.log('\n🎯 功能特性:');
    console.log('   • 支持文本到视频生成');
    console.log('   • 可配置视频时长（5-10秒）');
    console.log('   • 支持多种分辨率（480p, 720p, 1080p）');
    console.log('   • 支持多种宽高比（16:9, 9:16, 1:1）');
    
    console.log('\n⚠️ 注意事项:');
    console.log('   • 视频生成是异步任务');
    console.log('   • 需要轮询查询任务状态');
    console.log('   • 生成时间通常需要几分钟');
    console.log('   • 消耗的配额比文本生成更多');
    
    // 7. 故障排除
    console.log('\n🔧 7. 故障排除...');
    
    console.log('\n常见错误及解决方案:');
    console.log('   • "Not Found" → 检查API端点是否正确');
    console.log('   • "API key invalid" → 检查密钥配置');
    console.log('   • "Model not found" → 确认已开通模型权限');
    console.log('   • "Internal error" → 服务临时问题，自动重试');
    
    console.log('\n📞 获取帮助:');
    console.log('   • 火山引擎控制台: https://console.volcengine.com/');
    console.log('   • 官方文档: https://www.volcengine.com/docs/82379/1520757');
    console.log('   • 技术支持: 通过控制台提交工单');
    
    console.log('\n🎉 豆包视频生成API测试完成！');
    
    console.log('\n📋 总结:');
    console.log('   ✅ 已切换到视频生成API端点');
    console.log('   ✅ 使用正确的请求格式');
    console.log('   ✅ 支持视频生成参数配置');
    console.log('   🔑 需要真实API密钥进行测试');
    console.log('   🎯 准备好使用豆包视频生成功能');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoVideoAPI();
