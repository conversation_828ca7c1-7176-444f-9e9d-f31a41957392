module.exports = {

"[project]/.next-internal/server/app/api/ai/generate-story-video/segment/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/skyreels.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SkyReelsClient": (()=>SkyReelsClient),
    "SkyReelsServiceError": (()=>SkyReelsServiceError)
});
class SkyReelsClient {
    baseUrl;
    apiKey;
    model;
    constructor(config){
        // SkyReels是本地API，apiKey用作baseUrl
        this.baseUrl = config.apiKey || 'http://localhost:8000';
        this.apiKey = config.apiKey;
        this.model = config.model || 'SkyReels-V2-DF-1.3B-540P';
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            return data.status === 'healthy' && data.model_loaded;
        } catch (error) {
            console.error('SkyReels连接测试失败:', error);
            return false;
        }
    }
    // 生成视频
    async generateVideo(params) {
        try {
            const response = await fetch(`${this.baseUrl}/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: params.prompt,
                    num_frames: params.num_frames || 257,
                    guidance_scale: params.guidance_scale || 6.0,
                    seed: params.seed,
                    fps: params.fps || 24,
                    resolution: params.resolution || '540P'
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels视频生成失败:', error);
            throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 查询任务状态
    async getTaskStatus(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/status/${taskId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels状态查询失败:', error);
            throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 下载视频
    async downloadVideo(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/download/${taskId}`, {
                method: 'GET'
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            return await response.blob();
        } catch (error) {
            console.error('SkyReels视频下载失败:', error);
            throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 等待任务完成并返回视频URL
    async generateAndWait(params, maxWaitTime = 1800000, pollInterval = 5000 // 5秒
    ) {
        console.log('🎬 开始SkyReels视频生成...');
        // 开始生成
        const task = await this.generateVideo(params);
        console.log(`📝 任务已创建: ${task.task_id}`);
        const startTime = Date.now();
        // 轮询状态直到完成
        while(Date.now() - startTime < maxWaitTime){
            const status = await this.getTaskStatus(task.task_id);
            console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`);
            if (status.status === 'completed') {
                console.log('✅ 视频生成完成!');
                return status.video_path || '';
            } else if (status.status === 'failed') {
                throw new Error(`视频生成失败: ${status.error || '未知错误'}`);
            }
            // 等待下次轮询
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        }
        throw new Error('视频生成超时');
    }
    // 获取所有任务列表
    async getTasks() {
        try {
            const response = await fetch(`${this.baseUrl}/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data.tasks || [];
        } catch (error) {
            console.error('SkyReels任务列表获取失败:', error);
            throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
class SkyReelsServiceError extends Error {
    statusCode;
    constructor(message, statusCode){
        super(message), this.statusCode = statusCode;
        this.name = 'SkyReelsServiceError';
    }
}
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIServiceError": (()=>AIServiceError),
    "DeepSeekClient": (()=>DeepSeekClient),
    "DoubaoClient": (()=>DoubaoClient),
    "createAIClient": (()=>createAIClient),
    "handleAIRequest": (()=>handleAIRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/skyreels.ts [app-route] (ecmascript)");
;
class AIServiceError extends Error {
    code;
    statusCode;
    constructor(message, code, statusCode = 500){
        super(message), this.code = code, this.statusCode = statusCode;
        this.name = 'AIServiceError';
    }
}
class DeepSeekClient {
    apiKey;
    model;
    baseUrl = 'https://api.deepseek.com/v1/chat/completions';
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model;
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: '测试连接'
                        }
                    ],
                    max_tokens: 10
                })
            });
            return response.ok;
        } catch (error) {
            console.error('DeepSeek连接测试失败:', error);
            return false;
        }
    }
    // 调用AI API的通用方法（公开方法）
    async callAPI(prompt, maxTokens = 4000) {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: maxTokens,
                    temperature: 0.7
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new AIServiceError(errorData.error?.message || 'API调用失败', 'API_ERROR', response.status);
            }
            const data = await response.json();
            return data.choices[0]?.message?.content || '';
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('AI服务调用失败，请检查网络连接', 'NETWORK_ERROR', 500);
        }
    }
    // 生成测试响应
    generateTestResponse(prompt) {
        if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {
            // 角色一致性分析的测试响应
            return `{
        "characters": [
          {
            "name": "张小雅",
            "identity": "高中生",
            "appearance": "马尾辫，甜美笑容，青春活泼",
            "personality": "开朗活泼，善良纯真",
            "clothing": "校服或休闲装",
            "role": "主要角色",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "zhang_xiaoya",
              "consistencyMatch": 0.95,
              "differences": [],
              "consistencyConstraints": "保持马尾辫和甜美笑容的标志性特征"
            }
          },
          {
            "name": "王美丽",
            "identity": "咖啡店老板",
            "appearance": "瓜子脸，波浪卷发，温柔气质",
            "personality": "温柔善良，热情好客",
            "clothing": "简约优雅的服装",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "wang_meili",
              "consistencyMatch": 0.88,
              "differences": [],
              "consistencyConstraints": "保持瓜子脸和波浪卷发的特征"
            }
          },
          {
            "name": "李明轩",
            "identity": "大学教授",
            "appearance": "方脸，花白短发，学者气质",
            "personality": "温文尔雅，博学睿智",
            "clothing": "正式的学者装扮",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "li_mingxuan",
              "consistencyMatch": 0.92,
              "differences": [],
              "consistencyConstraints": "保持方脸和花白短发的学者形象"
            }
          },
          {
            "name": "林志强",
            "identity": "程序员",
            "appearance": "高瘦身材，黑框眼镜，简约穿着",
            "personality": "内向专注，技术宅",
            "clothing": "白色T恤，牛仔裤",
            "role": "新角色",
            "isKnownCharacter": false,
            "consistencyInfo": {
              "matchedCharacterId": null,
              "consistencyMatch": 0.0,
              "differences": ["新出现的角色"],
              "consistencyConstraints": "建立新的角色DNA档案"
            }
          }
        ]
      }`;
        } else if (prompt.includes('场景信息')) {
            return `{
        "scenes": [
          {
            "location": "温馨咖啡店",
            "description": "装修温馨的小型咖啡店，木质桌椅，暖色调灯光",
            "atmosphere": "温馨舒适，充满生活气息",
            "timeOfDay": "下午",
            "lighting": "暖色调室内灯光",
            "keyElements": "咖啡香味，轻柔音乐，温馨装饰"
          }
        ]
      }`;
        } else if (prompt.includes('情节序列')) {
            return `{
        "plotSequences": [
          {
            "sequenceId": "reunion_1",
            "action": "张小雅进入咖啡店与王美丽重逢",
            "emotion": "惊喜和温暖",
            "duration": "2分钟",
            "keyMoments": ["进门", "相认", "拥抱"],
            "visualElements": "特写表情变化，温馨的重逢场面"
          },
          {
            "sequenceId": "professor_arrival",
            "action": "李明轩教授进入咖啡店",
            "emotion": "温和友善",
            "duration": "1分钟",
            "keyMoments": ["进门", "打招呼"],
            "visualElements": "学者风度，温和笑容"
          },
          {
            "sequenceId": "new_character",
            "action": "林志强询问WiFi",
            "emotion": "略显紧张的初次见面",
            "duration": "1分钟",
            "keyMoments": ["进门", "询问", "介绍"],
            "visualElements": "新角色登场，技术宅形象"
          }
        ]
      }`;
        } else {
            return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽';
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析AI响应失败:', error);
            throw new AIServiceError('AI响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析剧情分析响应失败:', error);
            throw new AIServiceError('剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
class DoubaoClient {
    apiKey;
    model;
    baseUrl;
    isVideoModel;
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model || 'doubao-seedance-1.0-pro';
        // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID
        this.isVideoModel = this.model.includes('seedance') || this.model.includes('video') || this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式
        ;
        // 根据模型类型选择正确的API端点
        if (this.isVideoModel) {
            // 豆包视频生成使用专门的视频生成API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks';
        } else {
            // 文本模型使用chat completions API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        }
        if (config.baseUrl) {
            this.baseUrl = config.baseUrl;
        }
    }
    // 测试API连接（带重试机制）
    async testConnection() {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: '测试连接'
                            }
                        ],
                        max_tokens: 10
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    return true;
                }
                // 检查是否是可重试的错误
                const errorText = await response.text();
                if (errorText.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            } catch (error) {
                console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error);
                if (attempt < maxRetries) {
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            }
        }
        return false;
    }
    // 调用AI API的通用方法（带重试机制）
    async callAPI(prompt, maxTokens = 4000) {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: maxTokens,
                        temperature: 0.7
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    const data = await response.json();
                    if (this.isVideoModel) {
                        // 视频生成返回任务信息
                        return JSON.stringify({
                            task_id: data.task_id,
                            status: data.status || 'submitted',
                            message: '视频生成任务已提交，请稍后查询结果'
                        });
                    } else {
                        // 文本生成返回内容
                        return data.choices[0]?.message?.content || '';
                    }
                }
                const errorData = await response.json();
                const errorMessage = errorData.error?.message || '豆包API调用失败';
                // 检查是否是可重试的内部错误
                if (errorMessage.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                // 不可重试的错误，直接抛出
                throw new AIServiceError(errorMessage, 'API_ERROR', response.status);
            } catch (error) {
                if (error instanceof AIServiceError) {
                    // 如果是已知的API错误且不可重试，直接抛出
                    throw error;
                }
                // 网络错误等，可以重试
                if (attempt < maxRetries) {
                    console.log(`豆包API调用失败，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                throw new AIServiceError('豆包服务调用失败，请检查网络连接和API密钥', 'NETWORK_ERROR', 500);
            }
        }
        throw new AIServiceError('豆包服务调用失败，已达到最大重试次数', 'MAX_RETRIES_EXCEEDED', 500);
    }
    // 专门的视频生成方法
    async generateVideo(prompt, duration = 5) {
        if (!this.isVideoModel) {
            throw new Error('此模型不支持视频生成');
        }
        try {
            const requestBody = {
                model: this.model,
                prompt: prompt,
                video_setting: {
                    video_duration: duration,
                    video_aspect_ratio: '16:9',
                    video_resolution: '720p'
                }
            };
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            if (response.ok) {
                const data = await response.json();
                return JSON.stringify({
                    task_id: data.task_id,
                    status: data.status || 'submitted',
                    message: '视频生成任务已提交，请稍后查询结果'
                });
            }
            const errorData = await response.json();
            throw new AIServiceError(errorData.error?.message || '视频生成失败', 'VIDEO_GENERATION_ERROR', response.status);
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('视频生成服务调用失败', 'NETWORK_ERROR', 500);
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析豆包响应失败:', error);
            throw new AIServiceError('豆包响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析豆包剧情分析响应失败:', error);
            throw new AIServiceError('豆包剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
function createAIClient(config) {
    switch(config.provider){
        case 'deepseek':
            return new DeepSeekClient(config);
        case 'doubao':
            return new DoubaoClient(config);
        case 'skyreels':
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SkyReelsClient"](config);
        default:
            // 默认使用DeepSeek客户端，但可以扩展支持其他提供商
            return new DeepSeekClient(config);
    }
}
async function handleAIRequest(request) {
    try {
        return await request();
    } catch (error) {
        if (error instanceof AIServiceError) {
            throw error;
        }
        // 网络错误
        if (error instanceof TypeError && error.message.includes('fetch')) {
            throw new AIServiceError('AI服务连接失败，请检查网络连接', 'CONNECTION_ERROR', 503);
        }
        // 通用错误
        throw new AIServiceError('AI服务处理失败，请重试', 'UNKNOWN_ERROR', 500);
    }
}
}}),
"[project]/src/app/api/ai/generate-story-video/segment/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { segmentId, modelId } = await request.json();
        console.log('🎬 内部API：生成单个视频片段');
        console.log('📋 参数:', {
            segmentId,
            modelId
        });
        if (!segmentId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少片段ID'
            }, {
                status: 400
            });
        }
        // 获取片段信息
        const segment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.findUnique({
            where: {
                id: segmentId
            }
        });
        if (!segment) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '片段不存在'
            }, {
                status: 404
            });
        }
        console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`);
        // 更新片段状态为生成中
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segmentId
            },
            data: {
                status: 'generating',
                updatedAt: new Date()
            }
        });
        // 异步调用真实的视频生成逻辑
        generateSingleSegmentAsync(segment, modelId).then(()=>{
            console.log(`✅ 片段 ${segment.segmentIndex} 生成完成`);
        }).catch(async (error)=>{
            console.error(`❌ 片段 ${segment.segmentIndex} 生成失败:`, error);
            // 更新数据库状态为失败
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                    where: {
                        id: segment.id
                    },
                    data: {
                        status: 'failed',
                        updatedAt: new Date()
                    }
                });
                console.log(`📝 已更新片段 ${segment.segmentIndex} 状态为失败`);
            } catch (dbError) {
                console.error(`❌ 更新片段状态失败:`, dbError);
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                message: `片段 ${segment.segmentIndex} 开始生成`,
                segmentId: segment.id,
                segmentIndex: segment.segmentIndex,
                title: segment.title
            }
        });
    } catch (error) {
        console.error('生成单个视频片段失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '生成视频片段失败'
        }, {
            status: 500
        });
    }
}
// 异步生成单个片段
async function generateSingleSegmentAsync(segment, modelId) {
    try {
        console.log(`🎬 开始异步生成片段: ${segment.title}`);
        // 获取AI配置
        const aiConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
            where: {
                enabled: true
            }
        });
        if (!aiConfig) {
            throw new Error('请先配置AI模型');
        }
        // 调用简化的视频生成函数
        const videoResult = await generateSingleSegmentSimplified(aiConfig, segment, modelId);
        // 根据生成结果更新片段信息
        const updateData = {
            duration: videoResult.duration,
            metadata: JSON.stringify({
                ...JSON.parse(segment.metadata || '{}'),
                generatedAt: new Date().toISOString(),
                optimizedPrompt: videoResult.optimizedPrompt
            })
        };
        if (videoResult.videoUrl) {
            updateData.status = 'completed';
            updateData.videoUrl = videoResult.videoUrl;
            updateData.thumbnailUrl = videoResult.thumbnailUrl;
        } else {
            updateData.status = 'failed';
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segment.id
            },
            data: updateData
        });
        console.log(`✅ 片段 ${segment.segmentIndex} 异步生成完成`);
    } catch (error) {
        console.error(`❌ 片段异步生成失败:`, error);
        // 更新片段状态为失败
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segment.id
            },
            data: {
                status: 'failed',
                metadata: JSON.stringify({
                    ...JSON.parse(segment.metadata || '{}'),
                    error: error instanceof Error ? error.message : '生成失败',
                    failedAt: new Date().toISOString()
                })
            }
        });
    }
}
// 简化版的单个片段生成函数
async function generateSingleSegmentSimplified(aiConfig, segment, modelId) {
    try {
        console.log(`🎬 开始生成片段: ${segment.title}`);
        // 1. 获取视频生成模型配置
        let videoModel;
        if (modelId) {
            // 如果指定了模型ID，使用该模型
            videoModel = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findUnique({
                where: {
                    id: modelId
                }
            });
            if (!videoModel) {
                throw new Error(`指定的模型 ${modelId} 不存在`);
            }
            if (!videoModel.enabled) {
                throw new Error(`指定的模型 ${videoModel.name} 未启用`);
            }
            if (!videoModel.supportsVideo && !videoModel.supportsImageToVideo) {
                throw new Error(`指定的模型 ${videoModel.name} 不支持视频生成`);
            }
            console.log(`🎯 使用用户选择的模型: ${videoModel.name} (${videoModel.provider})`);
        } else {
            // 如果没有指定模型ID，使用第一个启用的视频模型作为后备
            videoModel = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
                where: {
                    enabled: true,
                    OR: [
                        {
                            supportsVideo: true
                        },
                        {
                            supportsImageToVideo: true
                        }
                    ]
                }
            });
            if (!videoModel) {
                throw new Error('未找到可用的视频生成模型');
            }
            console.log(`⚠️ 未指定模型，使用默认模型: ${videoModel.name} (${videoModel.provider})`);
        }
        // 2. 优化提示词（可选）
        let optimizedPrompt = segment.prompt;
        const deepSeekConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
            where: {
                provider: 'deepseek',
                enabled: true
            }
        });
        if (deepSeekConfig) {
            try {
                const deepSeekClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DeepSeekClient"](deepSeekConfig);
                const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：

片段标题：${segment.title}
片段描述：${segment.description}
原始提示词：${segment.prompt}

请按照以下要求优化：
1. 确保描述具体且可视化
2. 添加适合的镜头运动和角度
3. 强调画面质量和风格
4. 控制在100字以内

优化后的提示词：`;
                optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800);
                console.log(`✅ 提示词优化完成`);
            } catch (error) {
                console.warn('提示词优化失败，使用原始提示词:', error.message);
            }
        }
        // 3. 调用视频生成API
        const videoResult = await callVideoAPI(videoModel, optimizedPrompt.trim(), segment);
        return {
            videoUrl: videoResult.videoUrl,
            thumbnailUrl: videoResult.thumbnailUrl,
            optimizedPrompt: optimizedPrompt.trim(),
            duration: videoResult.duration || segment.duration || 5,
            status: videoResult.status || 'completed'
        };
    } catch (error) {
        console.error('视频生成失败:', error);
        return {
            videoUrl: null,
            thumbnailUrl: null,
            optimizedPrompt: segment.prompt,
            duration: segment.duration || 5,
            status: 'failed',
            error: error.message
        };
    }
}
// 简化版的视频API调用函数
async function callVideoAPI(videoModel, prompt, segment) {
    try {
        console.log(`🎬 调用${videoModel.provider}视频API`);
        // 根据不同的提供商调用相应的API
        if (videoModel.provider === 'doubao') {
            return await callDoubaoVideoAPISimplified(videoModel, prompt, segment);
        } else if (videoModel.provider === 'minimax') {
            return await callMinimaxVideoAPISimplified(videoModel, prompt, segment);
        } else if (videoModel.provider === 'tongyi') {
            return await callTongyiVideoAPISimplified(videoModel, prompt, segment);
        } else {
            // 对于其他提供商，返回模拟结果
            console.warn(`暂不支持${videoModel.provider}提供商，返回模拟结果`);
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 5,
                status: 'pending'
            };
        }
    } catch (error) {
        console.error('视频API调用失败:', error);
        throw error;
    }
}
// 简化版豆包视频API调用
async function callDoubaoVideoAPISimplified(config, prompt, segment) {
    try {
        console.log(`🎬 调用豆包视频API，片段: ${segment.segmentIndex}`);
        // 构建请求内容
        const content = [
            {
                type: "text",
                text: prompt
            }
        ];
        // 创建视频生成任务
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                content: content
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`豆包API调用失败: ${response.status} ${errorText}`);
            throw new Error(`豆包视频生成失败: ${response.status} ${errorText}`);
        }
        const result = await response.json();
        console.log('豆包API响应:', result);
        // 豆包API返回的是 id 字段，不是 task_id
        const taskId = result.id || result.task_id;
        if (taskId) {
            // 开始轮询任务状态
            const videoResult = await pollDoubaoTaskStatus(config.apiKey, taskId, segment);
            return videoResult;
        } else {
            throw new Error('豆包API未返回任务ID');
        }
    } catch (error) {
        console.error('豆包视频API调用失败:', error);
        throw error;
    }
}
// 轮询豆包任务状态
async function pollDoubaoTaskStatus(apiKey, taskId, segment) {
    const maxAttempts = 60 // 最多轮询60次（10分钟）
    ;
    const pollInterval = 10000 // 10秒轮询一次
    ;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            console.log(`🔄 轮询豆包任务状态 (${attempt}/${maxAttempts}): ${taskId}`);
            const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`轮询失败: ${response.status}`);
            }
            const result = await response.json();
            console.log(`📊 任务状态: ${result.status}`);
            // 豆包API的实际响应格式
            if (result.status === 'succeeded' && result.content?.video_url) {
                console.log(`✅ 豆包视频生成完成: ${result.content.video_url}`);
                return {
                    videoUrl: result.content.video_url,
                    thumbnailUrl: result.content.thumbnail_url || null,
                    duration: segment.duration || 5,
                    status: 'completed'
                };
            } else if (result.status === 'failed') {
                throw new Error(`豆包视频生成失败: ${result.error || '未知错误'}`);
            }
            // 如果还在处理中，等待后继续轮询
            if (attempt < maxAttempts) {
                await new Promise((resolve)=>setTimeout(resolve, pollInterval));
            }
        } catch (error) {
            console.error(`轮询第${attempt}次失败:`, error);
            if (attempt === maxAttempts) {
                throw error;
            }
        }
    }
    throw new Error('豆包视频生成超时');
}
// 简化版MiniMax视频API调用（占位符）
async function callMinimaxVideoAPISimplified(config, prompt, segment) {
    console.warn('MiniMax视频生成暂未实现，返回模拟结果');
    return {
        videoUrl: null,
        thumbnailUrl: null,
        duration: segment.duration || 5,
        status: 'pending'
    };
}
// 简化版通义万相视频API调用（占位符）
async function callTongyiVideoAPISimplified(config, prompt, segment) {
    console.warn('通义万相视频生成暂未实现，返回模拟结果');
    return {
        videoUrl: null,
        thumbnailUrl: null,
        duration: segment.duration || 5,
        status: 'pending'
    };
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__16ea4927._.js.map