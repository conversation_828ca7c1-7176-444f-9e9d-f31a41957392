import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 上传文件到项目
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: projectId } = await params
    const body = await request.json()
    const { fileName, content } = body

    if (!fileName || !content) {
      return NextResponse.json(
        { success: false, error: '文件名和内容不能为空' },
        { status: 400 }
      )
    }

    // 验证文件格式
    const supportedExtensions = ['.txt', '.docx', '.doc']
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    
    if (!supportedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { success: false, error: `不支持的文件格式。支持的格式: ${supportedExtensions.join(', ')}` },
        { status: 400 }
      )
    }

    // 验证内容长度
    if (content.length > 2000000) { // 2MB 文本限制
      return NextResponse.json(
        { success: false, error: '文件内容太长，请控制在200万字符以内' },
        { status: 400 }
      )
    }

    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 更新项目，添加文件内容
    const updatedProject = await prisma.project.update({
      where: { id: projectId },
      data: {
        fileName,
        content,
        status: 'uploaded',
        updatedAt: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedProject,
      message: '文件上传成功'
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { success: false, error: '文件上传失败' },
      { status: 500 }
    )
  }
}
