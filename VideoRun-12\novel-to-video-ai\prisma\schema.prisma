// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 项目表 - 存储上传的小说项目
model Project {
  id          String   @id @default(cuid())
  name        String   // 项目名称
  description String?  // 项目描述
  fileName    String?  // 原始文件名
  content     String?  // 小说全文内容
  status      String   @default("created") // created, uploaded, analyzing, completed
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  characters  Character[]
  episodes    Episode[]

  @@map("projects")
}

// AI配置表 - 存储大模型配置信息（支持多个模型）
model AIConfig {
  id          String   @id @default(cuid()) // 每个模型配置的唯一ID
  provider    String   // deepseek, openai, claude, tongyi, doubao, minimax, zhipu, skyreels
  apiKey      String   // API密钥
  model       String   // 模型名称
  name        String   // 显示名称
  description String?  // 模型描述
  enabled     Boolean  @default(true) // 是否启用
  supportsVideo Boolean @default(false) // 是否支持视频生成
  supportsImage Boolean @default(false) // 是否支持图像生成
  supportsImageToVideo Boolean @default(false) // 是否支持图生视频
  supportsTTS Boolean @default(false) // 是否支持语音合成
  temperature Float    @default(0.7) // 温度参数
  maxTokens   Int      @default(4000) // 最大token数
  topP        Float    @default(0.9) // top_p参数
  status      String   @default("disconnected") // connected, disconnected, error, testing
  lastTest    DateTime? // 最后测试时间
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  characterVoices CharacterVoice[]

  @@map("ai_configs")
}

// 角色表 - 存储提取的角色信息（扩展支持一致性功能）
model Character {
  id              String   @id @default(cuid())
  projectId       String
  name            String   // 角色姓名
  identity        String?  // 身份信息
  personality     String?  // 性格特点
  physique        String?  // 身材特征
  facial          String?  // 五官特征
  hairstyle       String?  // 发型样式
  clothing        String?  // 服饰风格
  generatedImages String?  // AI生成的三视图JSON格式 {front: string, side: string, back: string}

  // 新增：详细DNA信息
  detailedDNA     String?  // 详细的角色DNA结构化数据 JSON格式

  // 新增：一致性相关字段
  consistencyScore     Float?   @default(0.0)  // 一致性评分 (0.0-1.0)
  consistencySettings  String?  // 一致性设置 JSON格式
  referenceImages      String?  // 参考图像集合 JSON格式

  // 新增：全局角色标识
  globalCharacterId    String?  // 关联全局角色库的ID
  isGlobalCharacter    Boolean  @default(false) // 是否为全局角色

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // 关联关系
  project         Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 新增：一致性验证记录
  validationRecords ConsistencyValidation[]

  // 新增：声音配置和音频文件关联
  voiceConfigs    CharacterVoice[]
  audioFiles      AudioFile[]

  @@map("characters")
}

// 剧集表 - 存储拆分的剧集信息
model Episode {
  id          String   @id @default(cuid())
  projectId   String
  title       String   // 故事标题
  content     String   // 详细剧情内容
  orderIndex  Int      // 排序索引
  status      String   @default("created") // created, analyzed, video_generated
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  project          Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  plotInfo         PlotInfo?
  videoGenerations VideoGeneration[]
  audioFiles       AudioFile[]

  @@map("episodes")
}

// 剧情信息表 - 存储每集的三大信息
model PlotInfo {
  id          String   @id @default(cuid())
  episodeId   String   @unique
  characters  String   // 本集人物列表 JSON格式
  scenes      String   // 场景信息 JSON格式
  events      String   // 事件三要素 JSON格式
  // 新增详细剧情信息字段
  detailedCharacters String? // 详细角色DNA信息 JSON格式
  detailedScenes     String? // 详细场景模板信息 JSON格式
  plotSequences      String? // 情节分解序列 JSON格式
  emotionalArc       String? // 情感弧线描述
  generatedPrompt    String? // 生成的视频提示词
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  episode     Episode  @relation(fields: [episodeId], references: [id], onDelete: Cascade)

  @@map("plot_infos")
}

// 剧情视频表 - 存储生成的剧情视频信息
model StoryVideo {
  id          String   @id @default(cuid())
  episodeId   String
  projectId   String
  prompt      String   // 生成视频的提示词
  videoUrl    String?  // 生成的视频URL
  status      String   @default("generating") // generating, completed, failed

  // 合并视频功能
  mergedVideoUrl String? // 合并后的完整视频URL
  totalDuration  Int?    // 合并后的总时长（秒）

  metadata    String?  // 额外的元数据 JSON格式
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  segments    VideoSegment[]

  @@map("story_videos")
}

// 视频片段表 - 存储分段生成的短视频
model VideoSegment {
  id            String   @id @default(cuid())
  storyVideoId  String
  episodeId     String
  projectId     String
  segmentIndex  Int      // 片段序号
  title         String   // 片段标题
  description   String   // 片段描述
  prompt        String   // 生成该片段的提示词
  videoUrl      String?  // 生成的视频URL
  thumbnailUrl  String?  // 缩略图URL
  duration      Int?     // 视频时长(秒)
  status        String   @default("pending") // pending, generating, completed, failed
  segmentType   String   // scene, action, dialogue, transition
  metadata      String?  // 额外的元数据 JSON格式
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  storyVideo    StoryVideo @relation(fields: [storyVideoId], references: [id], onDelete: Cascade)

  @@map("video_segments")
}

// 一致性验证记录表 - 存储角色一致性验证结果
model ConsistencyValidation {
  id                    String   @id @default(cuid())
  characterId           String
  episodeId             String?

  textConsistencyScore  Float?   // 文本一致性评分 (0.0-1.0)
  imageConsistencyScore Float?   // 图像一致性评分 (0.0-1.0)
  overallScore          Float?   // 综合一致性评分 (0.0-1.0)

  validationDetails     String?  // 验证详情 JSON格式
  issuesFound          String?  // 发现的问题 JSON格式

  textWeight           Float?   // 文本约束权重
  imageWeight          Float?   // 图像约束权重

  generatedImageUrl    String?  // 生成的图像URL

  createdAt            DateTime @default(now())

  // 关联关系
  character            Character @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@map("consistency_validations")
}

// 一致性视频生成记录表
model VideoGeneration {
  id                     String   @id @default(cuid())
  episodeId              String
  prompt                 String
  status                 String   @default("pending") // pending, processing, completed, failed
  videoUrl               String?
  consistencyMode        String?  // strict, balanced, creative
  includeReferenceImages Boolean  @default(false)
  style                  String?  // cinematic, documentary, artistic
  quality                String?  // standard, high, cinematic
  metadata               String?  // JSON metadata
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt

  episode Episode @relation(fields: [episodeId], references: [id], onDelete: Cascade)

  @@map("video_generations")
}

// 角色声音配置表 - 存储每个角色的TTS声音配置
model CharacterVoice {
  id              String   @id @default(cuid())
  characterId     String   // 关联的角色ID
  ttsConfigId     String   // 关联的TTS配置ID
  voiceId         String   // TTS服务中的声音ID
  voiceName       String?  // 声音名称
  basePitch       Int      @default(0) // 基础音调 (-20 to +20)
  baseSpeed       Float    @default(1.0) // 基础语速 (0.5 to 2.0)
  baseVolume      Int      @default(80) // 基础音量 (0 to 100)
  emotionMappings String?  // JSON格式的情感映射配置
  sampleAudioUrl  String?  // 示例音频URL
  enabled         Boolean  @default(true) // 是否启用
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // 关联关系
  character Character @relation(fields: [characterId], references: [id], onDelete: Cascade)
  ttsConfig AIConfig  @relation(fields: [ttsConfigId], references: [id], onDelete: Cascade)

  @@map("character_voices")
}

// 音频文件表 - 存储生成的音频文件信息
model AudioFile {
  id            String   @id @default(cuid())
  episodeId     String   // 关联的剧集ID
  segmentIndex  Int      // 视频片段索引
  dialogueType  String   // 对话类型: dialogue, narration, thought
  characterId   String?  // 角色ID（旁白可能为空）
  text          String   // 原始文本
  emotion       String   // 情感类型
  audioUrl      String?  // 音频文件URL
  duration      Float?   // 音频时长（秒）
  fileSize      Int?     // 文件大小（字节）
  format        String   @default("mp3") // 音频格式
  status        String   @default("pending") // pending, processing, completed, failed
  metadata      String?  // JSON格式的元数据
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  episode   Episode    @relation(fields: [episodeId], references: [id], onDelete: Cascade)
  character Character? @relation(fields: [characterId], references: [id], onDelete: SetNull)

  @@map("audio_files")
}
