async function testRealTTSAPI() {
  try {
    console.log('🎵 测试真实豆包TTS API...');
    
    // 测试基础TTS功能
    const testRequest = {
      text: '你好，这是豆包语音合成的真实API测试。现在我们验证系统是否正常工作。',
      voiceId: 'zh_male_M392_conversation_wvae_bigtts',
      emotion: 'neutral',
      speed: 1.0,
      pitch: 0,
      volume: 80,
      format: 'mp3'
    };
    
    console.log('📡 调用TTS API...');
    console.log('测试文本:', testRequest.text);
    
    const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testRequest)
    });
    
    console.log('API响应状态:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', response.status);
      console.error('错误详情:', errorText);
      return;
    }
    
    const result = await response.json();
    console.log('API响应结果:', {
      success: result.success,
      hasData: !!result.data,
      error: result.error
    });
    
    if (result.success && result.data) {
      console.log('✅ TTS生成成功！');
      console.log('音频信息:', {
        duration: result.data.duration.toFixed(1) + '秒',
        size: (result.data.size / 1024).toFixed(1) + 'KB',
        format: result.data.format,
        audioUrlLength: result.data.audioUrl.length
      });
      
      if (result.data.metadata) {
        console.log('元数据:', {
          provider: result.data.metadata.provider,
          voice_type: result.data.metadata.voice_type,
          speed_ratio: result.data.metadata.speed_ratio,
          code: result.data.metadata.code,
          message: result.data.metadata.message
        });
      }
      
      // 验证音频数据
      if (result.data.audioUrl && result.data.audioUrl.startsWith('data:audio/mp3;base64,')) {
        console.log('🎵 音频数据格式正确，可以播放！');
        
        // 可以选择保存音频文件进行测试
        const fs = require('fs');
        const base64Data = result.data.audioUrl.split(',')[1];
        const audioBuffer = Buffer.from(base64Data, 'base64');
        const audioPath = `real_tts_test_${Date.now()}.mp3`;
        fs.writeFileSync(audioPath, audioBuffer);
        console.log('✅ 音频已保存到:', audioPath);
      }
    } else {
      console.error('❌ TTS生成失败:', result.error);
    }
    
    // 测试获取声音列表
    console.log('\n🎤 测试获取声音列表...');
    
    const voicesResponse = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'GET'
    });
    
    if (voicesResponse.ok) {
      const voicesResult = await voicesResponse.json();
      if (voicesResult.success) {
        console.log('✅ 成功获取声音列表');
        console.log('可用声音数量:', voicesResult.data.length);
        
        if (voicesResult.data.length > 0) {
          console.log('可用声音:');
          voicesResult.data.forEach((voice, index) => {
            console.log(`  ${index + 1}. ${voice.name} (${voice.id})`);
          });
        }
      } else {
        console.error('❌ 获取声音列表失败:', voicesResult.error);
      }
    } else {
      console.error('❌ 声音列表API调用失败:', voicesResponse.status);
    }
    
    // 测试角色声音（如果有角色配置）
    console.log('\n🎭 测试角色声音...');
    
    const characterTTSRequest = {
      text: '我是小说中的角色，这是我的专属声音测试。',
      emotion: 'happy',
      speed: 1.1,
      format: 'mp3'
    };
    
    const characterResponse = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(characterTTSRequest)
    });
    
    if (characterResponse.ok) {
      const characterResult = await characterResponse.json();
      if (characterResult.success) {
        console.log('✅ 角色声音测试成功');
        console.log('角色音频信息:', {
          duration: characterResult.data.duration.toFixed(1) + '秒',
          size: (characterResult.data.size / 1024).toFixed(1) + 'KB'
        });
      } else {
        console.error('❌ 角色声音测试失败:', characterResult.error);
      }
    } else {
      console.error('❌ 角色声音API调用失败:', characterResponse.status);
    }
    
    console.log('\n🎉 真实豆包TTS API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔗 连接被拒绝，请确保开发服务器正在运行');
      console.error('请运行: npm run dev');
    } else {
      console.error('详细错误:', error.message);
    }
  }
}

// 检查服务器状态
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'GET'
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 检查开发服务器状态...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ 开发服务器未运行或TTS API不可用');
    console.log('请确保开发服务器正在运行: npm run dev');
    return;
  }
  
  console.log('✅ 开发服务器正在运行');
  console.log('🎯 开始测试真实豆包TTS API...\n');
  
  await testRealTTSAPI();
}

main();
