import mammoth from 'mammoth'

export class FileParseError extends Error {
  constructor(message: string, public code: string) {
    super(message)
    this.name = 'FileParseError'
  }
}

export class FileParser {
  // 支持的文件类型
  static readonly SUPPORTED_TYPES = {
    'text/plain': ['.txt'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/msword': ['.doc'] // 虽然不完全支持，但可以尝试
  }

  // 获取支持的文件扩展名
  static getSupportedExtensions(): string[] {
    return Object.values(this.SUPPORTED_TYPES).flat()
  }

  // 验证文件类型
  static validateFile(file: File): void {
    const extension = this.getFileExtension(file.name)
    const supportedExtensions = this.getSupportedExtensions()
    
    if (!supportedExtensions.includes(extension)) {
      throw new FileParseError(
        `不支持的文件格式: ${extension}。支持的格式: ${supportedExtensions.join(', ')}`,
        'UNSUPPORTED_FORMAT'
      )
    }

    // 验证文件大小 (最大 50MB)
    const maxSize = 50 * 1024 * 1024
    if (file.size > maxSize) {
      throw new FileParseError(
        `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,
        'FILE_TOO_LARGE'
      )
    }

    // 验证文件不为空
    if (file.size === 0) {
      throw new FileParseError(
        '文件不能为空',
        'EMPTY_FILE'
      )
    }
  }

  // 解析文件内容
  static async parseFile(file: File): Promise<string> {
    this.validateFile(file)
    
    const extension = this.getFileExtension(file.name)
    
    try {
      switch (extension) {
        case '.txt':
          return await this.parseTxtFile(file)
        case '.docx':
          return await this.parseDocxFile(file)
        case '.doc':
          // .doc 文件尝试用 docx 解析器处理
          return await this.parseDocxFile(file)
        default:
          throw new FileParseError(
            `不支持的文件格式: ${extension}`,
            'UNSUPPORTED_FORMAT'
          )
      }
    } catch (error) {
      if (error instanceof FileParseError) {
        throw error
      }
      
      throw new FileParseError(
        `文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        'PARSE_ERROR'
      )
    }
  }

  // 解析 TXT 文件
  private static async parseTxtFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        const content = e.target?.result as string
        if (!content || content.trim().length === 0) {
          reject(new FileParseError('文件内容为空', 'EMPTY_CONTENT'))
          return
        }
        resolve(content)
      }
      
      reader.onerror = () => {
        reject(new FileParseError('文件读取失败', 'READ_ERROR'))
      }
      
      // 尝试不同的编码
      reader.readAsText(file, 'utf-8')
    })
  }

  // 解析 DOCX 文件
  private static async parseDocxFile(file: File): Promise<string> {
    try {
      const arrayBuffer = await this.fileToArrayBuffer(file)
      const result = await mammoth.extractRawText({ arrayBuffer })
      
      if (!result.value || result.value.trim().length === 0) {
        throw new FileParseError('Word文档内容为空', 'EMPTY_CONTENT')
      }

      // 如果有警告，记录但不阻止处理
      if (result.messages && result.messages.length > 0) {
        console.warn('Word文档解析警告:', result.messages)
      }

      // 清理文本内容
      return this.cleanText(result.value)
    } catch (error) {
      if (error instanceof FileParseError) {
        throw error
      }
      
      throw new FileParseError(
        `Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        'DOCX_PARSE_ERROR'
      )
    }
  }

  // 将文件转换为 ArrayBuffer
  private static async fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        const result = e.target?.result
        if (result instanceof ArrayBuffer) {
          resolve(result)
        } else {
          reject(new Error('文件读取结果不是 ArrayBuffer'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  // 清理文本内容
  private static cleanText(text: string): string {
    return text
      .replace(/\r\n/g, '\n')  // 统一换行符
      .replace(/\r/g, '\n')    // 处理单独的 \r
      .replace(/\n{3,}/g, '\n\n')  // 合并多个连续换行
      .trim()
  }

  // 获取文件扩展名
  private static getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.')
    if (lastDotIndex === -1) {
      return ''
    }
    return fileName.substring(lastDotIndex).toLowerCase()
  }

  // 获取文件类型描述
  static getFileTypeDescription(file: File): string {
    const extension = this.getFileExtension(file.name)
    
    switch (extension) {
      case '.txt':
        return '纯文本文件'
      case '.docx':
        return 'Word 文档 (新版)'
      case '.doc':
        return 'Word 文档 (旧版)'
      default:
        return '未知格式'
    }
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }
}
