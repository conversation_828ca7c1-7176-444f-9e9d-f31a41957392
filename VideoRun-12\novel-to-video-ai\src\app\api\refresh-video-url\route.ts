import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { segmentId } = await request.json()
    
    if (!segmentId) {
      return NextResponse.json(
        { error: '缺少片段ID' },
        { status: 400 }
      )
    }

    // 获取片段信息
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId }
    })

    if (!segment) {
      return NextResponse.json(
        { error: '片段不存在' },
        { status: 404 }
      )
    }

    // 解析metadata获取taskId
    let metadata
    try {
      metadata = JSON.parse(segment.metadata || '{}')
    } catch {
      return NextResponse.json(
        { error: '无法解析片段元数据' },
        { status: 400 }
      )
    }

    const taskId = metadata.taskId
    if (!taskId) {
      return NextResponse.json(
        { error: '片段缺少任务ID' },
        { status: 400 }
      )
    }

    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'tongyi',
        model: 'wanx2.1-t2v-turbo',
        enabled: true
      }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { error: '未找到通义万相配置' },
        { status: 500 }
      )
    }

    console.log(`刷新片段 ${segmentId} 的视频URL，任务ID: ${taskId}`)

    // 重新查询任务状态获取新的URL
    const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${aiConfig.apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      return NextResponse.json(
        { error: `查询任务状态失败: ${response.status}` },
        { status: response.status }
      )
    }

    const result = await response.json()
    console.log('任务状态查询结果:', JSON.stringify(result, null, 2))

    if (result.output && result.output.task_status === 'SUCCEEDED') {
      const newVideoUrl = result.output.video_url
      
      if (newVideoUrl) {
        // 更新数据库中的URL
        await prisma.videoSegment.update({
          where: { id: segmentId },
          data: {
            videoUrl: newVideoUrl,
            metadata: JSON.stringify({
              ...metadata,
              refreshedAt: new Date().toISOString(),
              refreshedUrl: true
            })
          }
        })

        console.log(`✅ 片段 ${segmentId} URL已刷新: ${newVideoUrl}`)

        return NextResponse.json({
          success: true,
          videoUrl: newVideoUrl,
          message: 'URL刷新成功'
        })
      } else {
        return NextResponse.json(
          { error: '任务完成但未找到视频URL' },
          { status: 500 }
        )
      }
    } else {
      return NextResponse.json(
        { error: `任务状态异常: ${result.output?.task_status || 'unknown'}` },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('刷新视频URL失败:', error)
    return NextResponse.json(
      { error: '刷新视频URL失败' },
      { status: 500 }
    )
  }
}
