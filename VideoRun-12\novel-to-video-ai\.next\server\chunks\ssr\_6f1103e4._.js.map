{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\n// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\n// const geistSans = Geist({\n//   variable: \"--font-geist-sans\",\n//   subsets: [\"latin\"],\n// });\n\n// const geistMono = Geist_Mono({\n//   variable: \"--font-geist-mono\",\n//   subsets: [\"latin\"],\n// });\n\nexport const metadata: Metadata = {\n  title: \"灵犀Ai——小说转视频神器\",\n  description: \"基于DeepSeek大模型的智能视频内容创作平台，将小说自动转换为结构化的视频脚本，提取角色信息、拆分剧集、生成多维度视频内容。\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className=\"antialiased\"\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAU;sBAET;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}