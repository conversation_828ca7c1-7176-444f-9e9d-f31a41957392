import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// POST - 重试失败的视频片段
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { segmentId } = body

    if (!segmentId) {
      return NextResponse.json(
        { success: false, error: '片段ID不能为空' },
        { status: 400 }
      )
    }

    // 查找失败的片段
    const segment = await prisma.videoSegment.findUnique({
      where: { id: segmentId },
      include: {
        storyVideo: {
          include: {
            episode: true
          }
        }
      }
    })

    if (!segment) {
      return NextResponse.json(
        { success: false, error: '片段不存在' },
        { status: 404 }
      )
    }

    if (segment.status !== 'failed') {
      return NextResponse.json(
        { success: false, error: '只能重试失败的片段' },
        { status: 400 }
      )
    }

    // 重置片段状态为pending
    await prisma.videoSegment.update({
      where: { id: segmentId },
      data: {
        status: 'pending',
        videoUrl: null,
        metadata: JSON.stringify({
          ...JSON.parse(segment.metadata || '{}'),
          retryAt: new Date().toISOString(),
          retryCount: (JSON.parse(segment.metadata || '{}').retryCount || 0) + 1
        })
      }
    })

    // 重新生成视频
    const generateResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/ai/generate-story-video`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        episodeId: segment.storyVideo.episodeId,
        projectId: segment.storyVideo.episode.projectId,
        prompt: `重新生成片段: ${segment.title} - ${segment.description}`,
        retrySegmentId: segmentId
      })
    })

    if (!generateResponse.ok) {
      throw new Error('重新生成视频失败')
    }

    return NextResponse.json({
      success: true,
      message: '片段重试已启动',
      data: {
        segmentId: segmentId,
        status: 'pending'
      }
    })

  } catch (error) {
    console.error('重试失败片段失败:', error)
    return NextResponse.json(
      { success: false, error: '重试失败，请稍后再试' },
      { status: 500 }
    )
  }
}
