'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Play, Square, Volume2, Settings, Mic } from 'lucide-react'

interface Voice {
  id: string
  name: string
  gender: string
  language: string
}

interface TTSRequest {
  text: string
  characterId?: string
  voiceId?: string
  emotion?: string
  speed?: number
  pitch?: number
  volume?: number
  format?: 'mp3' | 'wav' | 'ogg'
}

interface TTSResponse {
  audioUrl: string
  duration: number
  format: string
  size: number
  metadata?: any
}

export function TTSPanel() {
  const [text, setText] = useState('')
  const [selectedVoice, setSelectedVoice] = useState('')
  const [emotion, setEmotion] = useState('neutral')
  const [speed, setSpeed] = useState([1.0])
  const [pitch, setPitch] = useState([0])
  const [volume, setVolume] = useState([80])
  const [format, setFormat] = useState<'mp3' | 'wav' | 'ogg'>('mp3')
  
  const [voices, setVoices] = useState<Voice[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const [generationResult, setGenerationResult] = useState<TTSResponse | null>(null)

  // 情感选项
  const emotions = [
    { value: 'neutral', label: '中性' },
    { value: 'happy', label: '高兴' },
    { value: 'sad', label: '悲伤' },
    { value: 'angry', label: '愤怒' },
    { value: 'excited', label: '兴奋' },
    { value: 'worried', label: '担心' },
    { value: 'surprised', label: '惊讶' },
    { value: 'gentle', label: '温柔' }
  ]

  // 加载可用声音列表
  useEffect(() => {
    loadVoices()
  }, [])

  const loadVoices = async () => {
    try {
      const response = await fetch('/api/ai/generate-tts', {
        method: 'GET'
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setVoices(result.data)
          if (result.data.length > 0 && !selectedVoice) {
            setSelectedVoice(result.data[0].id)
          }
        }
      }
    } catch (error) {
      console.error('加载声音列表失败:', error)
    }
  }

  // 生成语音
  const generateSpeech = async () => {
    if (!text.trim()) {
      alert('请输入要转换的文本')
      return
    }

    setIsLoading(true)
    try {
      const request: TTSRequest = {
        text: text.trim(),
        voiceId: selectedVoice,
        emotion,
        speed: speed[0],
        pitch: pitch[0],
        volume: volume[0],
        format
      }

      const response = await fetch('/api/ai/generate-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      const result = await response.json()
      
      if (result.success) {
        setAudioUrl(result.data.audioUrl)
        setGenerationResult(result.data)
        
        // 创建音频元素
        const audio = new Audio(result.data.audioUrl)
        setAudioElement(audio)
        
        // 设置音频事件监听
        audio.onended = () => setIsPlaying(false)
        audio.onerror = () => {
          setIsPlaying(false)
          alert('音频播放失败')
        }
        
      } else {
        alert(`语音生成失败: ${result.error}`)
      }
    } catch (error) {
      console.error('语音生成失败:', error)
      alert('语音生成失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 播放/停止音频
  const togglePlayback = () => {
    if (!audioElement) return

    if (isPlaying) {
      audioElement.pause()
      setIsPlaying(false)
    } else {
      audioElement.play()
      setIsPlaying(true)
    }
  }

  // 下载音频
  const downloadAudio = () => {
    if (!audioUrl) return

    const link = document.createElement('a')
    link.href = audioUrl
    link.download = `tts_audio_${Date.now()}.${format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            豆包语音合成
          </CardTitle>
          <CardDescription>
            使用豆包大模型将文本转换为自然语音
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 文本输入 */}
          <div className="space-y-2">
            <Label htmlFor="text">文本内容</Label>
            <Textarea
              id="text"
              placeholder="请输入要转换为语音的文本..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <div className="text-sm text-muted-foreground">
              字符数: {text.length}
            </div>
          </div>

          {/* 声音选择 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>声音选择</Label>
              <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                <SelectTrigger>
                  <SelectValue placeholder="选择声音" />
                </SelectTrigger>
                <SelectContent>
                  {voices.map((voice) => (
                    <SelectItem key={voice.id} value={voice.id}>
                      {voice.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>情感</Label>
              <Select value={emotion} onValueChange={setEmotion}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {emotions.map((emo) => (
                    <SelectItem key={emo.value} value={emo.value}>
                      {emo.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 参数调节 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>语速: {speed[0]}</Label>
              <Slider
                value={speed}
                onValueChange={setSpeed}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>音调: {pitch[0]}</Label>
              <Slider
                value={pitch}
                onValueChange={setPitch}
                min={-20}
                max={20}
                step={1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>音量: {volume[0]}</Label>
              <Slider
                value={volume}
                onValueChange={setVolume}
                min={0}
                max={100}
                step={5}
                className="w-full"
              />
            </div>
          </div>

          {/* 格式选择 */}
          <div className="space-y-2">
            <Label>音频格式</Label>
            <Select value={format} onValueChange={(value: 'mp3' | 'wav' | 'ogg') => setFormat(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mp3">MP3</SelectItem>
                <SelectItem value="wav">WAV</SelectItem>
                <SelectItem value="ogg">OGG</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button 
              onClick={generateSpeech} 
              disabled={isLoading || !text.trim()}
              className="flex-1"
            >
              {isLoading ? '生成中...' : '生成语音'}
            </Button>
            
            {audioUrl && (
              <>
                <Button
                  variant="outline"
                  onClick={togglePlayback}
                  disabled={!audioElement}
                >
                  {isPlaying ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={downloadAudio}
                >
                  下载
                </Button>
              </>
            )}
          </div>

          {/* 生成结果 */}
          {generationResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">生成结果</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex gap-2 flex-wrap">
                  <Badge variant="secondary">
                    时长: {generationResult.duration.toFixed(1)}秒
                  </Badge>
                  <Badge variant="secondary">
                    大小: {(generationResult.size / 1024).toFixed(1)}KB
                  </Badge>
                  <Badge variant="secondary">
                    格式: {generationResult.format.toUpperCase()}
                  </Badge>
                </div>
                
                {generationResult.metadata && (
                  <div className="text-sm text-muted-foreground">
                    声音: {generationResult.metadata.voice} | 
                    语速: {generationResult.metadata.speed} | 
                    音调: {generationResult.metadata.pitch}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
