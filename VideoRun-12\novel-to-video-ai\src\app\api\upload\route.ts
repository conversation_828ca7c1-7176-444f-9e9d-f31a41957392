import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 处理文件上传
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { fileName, content } = body

    // 验证输入
    if (!fileName || !content) {
      return NextResponse.json(
        { success: false, error: '文件名和内容不能为空' },
        { status: 400 }
      )
    }

    // 验证文件格式
    const supportedExtensions = ['.txt', '.docx', '.doc']
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))

    if (!supportedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { success: false, error: `不支持的文件格式。支持的格式: ${supportedExtensions.join(', ')}` },
        { status: 400 }
      )
    }

    // 验证内容长度
    if (content.length < 100) {
      return NextResponse.json(
        { success: false, error: '文件内容太短，至少需要100个字符' },
        { status: 400 }
      )
    }

    if (content.length > 2000000) { // 2MB 文本限制（Word文档可能更大）
      return NextResponse.json(
        { success: false, error: '文件内容太长，请控制在200万字符以内' },
        { status: 400 }
      )
    }

    // 创建项目记录
    const project = await prisma.project.create({
      data: {
        name: fileName.replace('.txt', ''),
        fileName: fileName,
        content: content,
        status: 'uploaded',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        projectId: project.id,
        fileName: project.fileName,
        contentLength: content.length,
      },
      message: '文件上传成功',
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { success: false, error: '文件上传失败，请重试' },
      { status: 500 }
    )
  }
}

// 获取项目信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')

    if (!projectId) {
      return NextResponse.json(
        { success: false, error: '项目ID不能为空' },
        { status: 400 }
      )
    }

    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        aiConfig: true,
        characters: true,
        episodes: {
          include: {
            plotInfo: true,
          },
          orderBy: {
            orderIndex: 'asc',
          },
        },
      },
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: project,
    })
  } catch (error) {
    console.error('获取项目信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取项目信息失败' },
      { status: 500 }
    )
  }
}
