'use client'

import { useState } from 'react'

export default function TestDoubaoPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [apiKey, setApiKey] = useState('')

  const testDoubaoAPI = async () => {
    if (!apiKey.trim()) {
      setResult('请输入API密钥')
      return
    }

    setLoading(true)
    setResult('测试中...')

    try {
      const response = await fetch('/api/ai-config/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'doubao',
          model: 'ep-20250622184757-q77k7',
          apiKey: apiKey.trim()
        })
      })

      const data = await response.json()
      
      if (response.ok) {
        setResult(`✅ 成功: ${data.message}`)
      } else {
        setResult(`❌ 失败: ${data.error}`)
      }
    } catch (error) {
      setResult(`❌ 网络错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">豆包API测试</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            豆包API密钥:
          </label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="输入你的豆包API密钥"
          />
        </div>

        <button
          onClick={testDoubaoAPI}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? '测试中...' : '测试豆包API连接'}
        </button>

        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-medium mb-2">测试结果:</h3>
            <pre className="whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 className="font-medium mb-2">测试信息:</h3>
        <p>模型: ep-20250622184757-q77k7</p>
        <p>API格式: 使用官方确认的content数组格式</p>
        <p>请求示例: {`{"model":"ep-20250622184757-q77k7","content":[{"type":"text","text":"测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"}]}`}</p>
      </div>
    </div>
  )
}
