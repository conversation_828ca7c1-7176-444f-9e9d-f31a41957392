'use client'

import { useState, useEffect } from 'react'
import { Play, Download, Refresh<PERSON><PERSON>, Clock, CheckCircle, XCircle, Loader, Film, BarChart3, RotateCcw, X } from 'lucide-react'
import VideoGenerationProgress from './VideoGenerationProgress'
import ModelSelector from './ModelSelector'

interface VideoSegment {
  id: string
  segmentIndex: number
  title: string
  description: string
  videoUrl?: string
  thumbnailUrl?: string
  duration?: number
  status: 'pending' | 'generating' | 'completed' | 'failed'
  segmentType: string
  createdAt: string
  updatedAt: string
  metadata: any
}

interface VideoSegmentData {
  storyVideoId: string
  episodeId: string
  segments: VideoSegment[]
  summary: {
    totalSegments: number
    completedSegments: number
    generatingSegments: number
    failedSegments: number
    pendingSegments: number
    progress: number
    overallStatus: string
  }
}

interface VideoSegmentViewerProps {
  episodeId: string
  projectId: string
  isOpen: boolean
  onClose: () => void
  selectedModelId?: string // 用户选择的模型ID
}

export default function VideoSegmentViewer({ episodeId, projectId, isOpen, onClose, selectedModelId }: VideoSegmentViewerProps) {
  const [segmentData, setSegmentData] = useState<VideoSegmentData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showDetailedProgress, setShowDetailedProgress] = useState(true) // 默认显示详细进度
  const [generatingSegments, setGeneratingSegments] = useState<Set<string>>(new Set()) // 正在生成的片段ID
  const [currentModelId, setCurrentModelId] = useState<string>(selectedModelId || '') // 当前选择的模型ID

  // 加载视频片段数据
  const loadSegmentData = async () => {
    if (!episodeId) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`)
      const data = await response.json()

      if (data.success) {
        setSegmentData(data.data)
      } else {
        setError(data.error || '加载失败')
      }
    } catch (error) {
      console.error('加载视频片段失败:', error)
      setError('加载失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 生成单个片段
  const handleGenerateSingleSegment = async (segmentId: string) => {
    try {
      // 检查是否选择了模型
      if (!currentModelId) {
        alert('请先选择视频生成模型')
        return
      }

      // 添加到正在生成的集合
      setGeneratingSegments(prev => new Set([...prev, segmentId]))

      console.log(`🎬 开始生成单个片段: ${segmentId}，使用模型: ${currentModelId}`)

      const response = await fetch('/api/ai/generate-single-segment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          segmentId,
          modelId: currentModelId // 传递用户选择的模型ID
        })
      })

      const data = await response.json()

      if (data.success) {
        console.log(`✅ 片段 ${data.data.segmentIndex} 开始生成`)

        // 更新本地状态
        if (segmentData) {
          setSegmentData(prev => {
            if (!prev) return prev
            return {
              ...prev,
              segments: prev.segments.map(seg =>
                seg.id === segmentId
                  ? { ...seg, status: 'generating' as const }
                  : seg
              )
            }
          })
        }

        // 显示成功消息
        alert(`片段 "${data.data.title}" 开始生成，请稍后查看进度`)
      } else {
        console.error('生成片段失败:', data.error)

        // 处理角色图像先决条件错误
        if (data.requirementType === 'character_images') {
          const missingChars = data.missingCharacters?.join('、') || '某些角色'
          alert(`❌ 生成失败\n\n${data.error}\n\n请前往"角色管理"页面为 ${missingChars} 生成角色形象后再试。`)
        } else {
          alert(`生成失败: ${data.error}`)
        }
      }
    } catch (error) {
      console.error('生成单个片段失败:', error)
      alert('生成片段时发生错误，请稍后重试')
    } finally {
      // 从正在生成的集合中移除
      setGeneratingSegments(prev => {
        const newSet = new Set(prev)
        newSet.delete(segmentId)
        return newSet
      })
    }
  }

  // 重试失败的片段
  const retryFailedSegment = async (segmentId: string) => {
    try {
      console.log(`🔄 开始重试片段: ${segmentId}`)

      // 调用专门的重试API
      const response = await fetch('/api/ai/retry-failed-segment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          segmentId: segmentId
        })
      })

      const data = await response.json()
      if (data.success) {
        console.log('✅ 重试请求成功提交')
        alert('重试已启动，片段正在重新生成中...')
        loadSegmentData() // 刷新数据
      } else {
        console.error('❌ 重试请求失败:', data.error)
        alert(`重试失败: ${data.error || '请稍后再试'}`)
      }
    } catch (error) {
      console.error('❌ 重试请求异常:', error)
      alert('重试失败，请检查网络连接后再试')
    }
  }

  // 自动刷新正在生成的片段
  useEffect(() => {
    if (!isOpen || !episodeId) return

    loadSegmentData()

    // 如果有正在生成的片段，定期刷新
    const interval = setInterval(() => {
      if (segmentData && (segmentData.summary.generatingSegments > 0 || segmentData.summary.pendingSegments > 0)) {
        loadSegmentData()
      }
    }, 5000) // 每5秒刷新一次

    return () => clearInterval(interval)
  }, [isOpen, episodeId, segmentData?.summary.generatingSegments, segmentData?.summary.pendingSegments])

  // 获取状态图标和颜色
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return { icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-50', text: '已完成' }
      case 'generating':
        return { icon: Loader, color: 'text-blue-600', bgColor: 'bg-blue-50', text: '生成中' }
      case 'failed':
        return { icon: XCircle, color: 'text-red-600', bgColor: 'bg-red-50', text: '失败' }
      case 'pending':
        return { icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', text: '等待中' }
      default:
        return { icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', text: '未知' }
    }
  }

  // 获取片段类型显示
  const getSegmentTypeDisplay = (type: string) => {
    const typeMap: Record<string, string> = {
      scene: '场景',
      action: '动作',
      dialogue: '对话',
      transition: '转场'
    }
    return typeMap[type] || type
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Film className="text-purple-600 mr-2" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                剧情视频片段
                {showDetailedProgress && (
                  <span className="ml-2 text-sm font-normal text-purple-600">• 详细进度监控</span>
                )}
              </h2>
              <div className="flex items-center mt-1 space-x-4">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  手动生成模式
                </span>
                <span className="text-xs text-gray-500">
                  点击片段上的生成按钮来逐个生成视频
                </span>
              </div>
              {/* 模型选择器 */}
              <div className="flex items-center mt-3 space-x-3">
                <span className="text-sm font-medium text-gray-700">生成模型:</span>
                <div className="w-64">
                  <ModelSelector
                    selectedModel={currentModelId}
                    onModelSelect={setCurrentModelId}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowDetailedProgress(!showDetailedProgress)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                showDetailedProgress
                  ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title={showDetailedProgress ? "切换到简化视图" : "切换到详细进度"}
            >
              <BarChart3 size={16} className="mr-1" />
              {showDetailedProgress ? "简化视图" : "详细进度"}
            </button>
            <button
              onClick={loadSegmentData}
              disabled={loading}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              title="刷新"
            >
              <RefreshCw className={loading ? 'animate-spin' : ''} size={20} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
              title="关闭"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <div className="p-6 max-h-[calc(90vh-80px)] overflow-y-auto">
          {loading && !segmentData ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                <p className="text-gray-600">加载视频片段中...</p>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <XCircle className="mx-auto h-12 w-12 text-red-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={loadSegmentData}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                重试
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* 详细进度组件 - 总是显示，即使没有segmentData */}
              {showDetailedProgress && (
                <VideoGenerationProgress
                  episodeId={episodeId}
                  isGenerating={segmentData ? (segmentData.summary.generatingSegments > 0 || segmentData.summary.pendingSegments > 0) : true}
                  onComplete={() => {
                    loadSegmentData()
                  }}
                />
              )}

              {segmentData ? (
                <>
                  {/* 简化的进度概览 */}
                  {!showDetailedProgress && (
                    <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900">生成进度</h3>
                      <span className="text-sm text-gray-600">
                        {segmentData.summary.completedSegments}/{segmentData.summary.totalSegments} 片段完成
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${segmentData.summary.progress}%` }}
                      ></div>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>进度: {segmentData.summary.progress}%</span>
                      <div className="flex space-x-4">
                        <span>生成中: {segmentData.summary.generatingSegments}</span>
                        <span>等待: {segmentData.summary.pendingSegments}</span>
                        <span>失败: {segmentData.summary.failedSegments}</span>
                      </div>
                    </div>
                  </div>
                  )}

                  {/* 视频片段列表 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {segmentData.segments.map((segment) => {
                      const statusDisplay = getStatusDisplay(segment.status)

                      return (
                        <div key={segment.id} className="border border-gray-200 rounded-lg overflow-hidden">
                          {/* 缩略图区域 */}
                          <div className="aspect-video bg-gray-100 relative">
                            {segment.thumbnailUrl ? (
                              <img
                                src={segment.thumbnailUrl}
                                alt={segment.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Film className="text-gray-400" size={48} />
                              </div>
                            )}

                            {/* 状态覆盖层 */}
                            <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.bgColor} ${statusDisplay.color}`}>
                              <statusDisplay.icon className="inline mr-1" size={12} />
                              {statusDisplay.text}
                            </div>

                            {/* 播放按钮 */}
                            {segment.status === 'completed' && segment.videoUrl && (
                              <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                                <button
                                  onClick={() => window.open(segment.videoUrl, '_blank')}
                                  className="bg-black bg-opacity-50 text-white rounded-full p-3 hover:bg-opacity-70 transition-all"
                                >
                                  <Play size={24} />
                                </button>
                              </div>
                            )}

                            {/* 生成按钮 - 针对失败、待生成或等待中的片段 */}
                            {(segment.status === 'failed' || segment.status === 'pending' || segment.status === 'waiting') && !generatingSegments.has(segment.id) && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 transition-all duration-200">
                                <button
                                  onClick={() => handleGenerateSingleSegment(segment.id)}
                                  className="bg-purple-600 text-white rounded-full p-4 hover:bg-purple-700 hover:scale-110 transition-all duration-200 shadow-xl border-2 border-white"
                                  title={
                                    segment.status === 'failed' ? '重新生成' :
                                    segment.status === 'waiting' ? '立即生成' :
                                    '开始生成'
                                  }
                                >
                                  <RefreshCw size={28} />
                                </button>
                                <div className="absolute bottom-2 left-2 right-2 text-center">
                                  <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
                                    {segment.status === 'failed' ? '重新生成' :
                                     segment.status === 'waiting' ? '立即生成' :
                                     '点击生成'}
                                  </span>
                                </div>
                              </div>
                            )}

                            {/* 正在生成的按钮状态 */}
                            {generatingSegments.has(segment.id) && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                <div className="text-white text-center">
                                  <Loader className="animate-spin mx-auto mb-2" size={24} />
                                  <span className="text-sm">启动中...</span>
                                </div>
                              </div>
                            )}

                            {/* 生成中动画 */}
                            {segment.status === 'generating' && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                <div className="text-white text-center">
                                  <Loader className="animate-spin mx-auto mb-2" size={24} />
                                  <span className="text-sm">生成中...</span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* 片段信息 */}
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-gray-900 truncate">{segment.title}</h4>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {getSegmentTypeDisplay(segment.segmentType)}
                              </span>
                            </div>

                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">{segment.description}</p>

                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>片段 {segment.segmentIndex}</span>
                              {segment.duration && (
                                <span>{segment.duration}秒</span>
                              )}
                            </div>

                            {/* 操作按钮 */}
                            {segment.status === 'completed' && segment.videoUrl && (
                              <div className="mt-3 flex space-x-2">
                                <button
                                  onClick={() => {
                                    try {
                                      console.log('视频URL:', segment.videoUrl)
                                      const videoUrl = segment.videoUrl

                                      // 如果是本地路径，直接在新窗口中播放
                                      if (videoUrl.startsWith('/downloads/')) {
                                        window.open(videoUrl, '_blank')
                                      } else {
                                        // 如果是远程URL，下载文件
                                        const cleanUrl = videoUrl.replace(/&amp;/g, '&')
                                        const link = document.createElement('a')
                                        link.href = cleanUrl
                                        link.download = `${segment.title}.mp4`
                                        document.body.appendChild(link)
                                        link.click()
                                        document.body.removeChild(link)
                                      }
                                    } catch (error) {
                                      console.error('播放/下载视频失败:', error)
                                      alert('播放/下载视频失败，请稍后重试')
                                    }
                                  }}
                                  className="flex-1 px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                                >
                                  {segment.videoUrl.startsWith('/downloads/') ? (
                                    <>
                                      <Play className="inline mr-1" size={12} />
                                      播放
                                    </>
                                  ) : (
                                    <>
                                      <Download className="inline mr-1" size={12} />
                                      下载
                                    </>
                                  )}
                                </button>
                                <button
                                  onClick={() => {
                                    try {
                                      const cleanUrl = segment.videoUrl.replace(/&amp;/g, '&')
                                      // 复制链接到剪贴板
                                      navigator.clipboard.writeText(cleanUrl).then(() => {
                                        alert('视频链接已复制到剪贴板，您可以在浏览器中直接粘贴下载')
                                      }).catch(() => {
                                        // 如果复制失败，显示链接
                                        prompt('视频链接（请手动复制）:', cleanUrl)
                                      })
                                    } catch (error) {
                                      console.error('复制链接失败:', error)
                                      alert('复制链接失败，请稍后重试')
                                    }
                                  }}
                                  className="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50"
                                >
                                  <Download className="inline mr-1" size={12} />
                                  复制链接
                                </button>
                              </div>
                            )}

                            {/* 失败重试按钮 */}
                            {segment.status === 'failed' && (
                              <div className="mt-3 space-y-2">
                                {/* 显示失败原因 */}
                                {segment.metadata && (() => {
                                  try {
                                    const metadata = JSON.parse(segment.metadata)
                                    if (metadata.error) {
                                      return (
                                        <div className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                                          <strong>失败原因:</strong> {metadata.error}
                                        </div>
                                      )
                                    }
                                  } catch (e) {
                                    return null
                                  }
                                  return null
                                })()}

                                <button
                                  onClick={() => retryFailedSegment(segment.id)}
                                  className="w-full px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 flex items-center justify-center"
                                >
                                  <RotateCcw className="inline mr-1" size={12} />
                                  重新生成
                                </button>
                              </div>
                            )}

                            {/* 视频使用提示 */}
                            {segment.status === 'completed' && segment.videoUrl && (
                              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
                                💡 {segment.videoUrl.startsWith('/downloads/')
                                  ? '视频已自动下载到本地，点击"播放"直接观看。'
                                  : '点击"下载"获取视频文件，或"复制链接"获取下载地址。'}
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <Film className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无视频片段</h3>
                  <p className="text-gray-600">请先生成剧情视频</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
