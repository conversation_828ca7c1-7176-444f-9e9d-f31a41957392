# 豆包视频生成模型配置指南

## 概述
本文档详细记录豆包视频生成模型的正确配置方法，包括今天遇到的关键问题和解决方案。

## 模型配置信息

### 当前可用模型
1. **豆包T2V (文生视频)** - `ep-20250624192345-5ccwj`
   - 用途：纯文本生成5-8秒视频
   - 适合：创意场景和剧情动画
   - API类型：文生视频 (T2V)

2. **豆包I2V (图生视频)** - `ep-20250624195026-qjsmk`
   - 用途：基于图像生成5-8秒视频
   - 适合：角色动作和场景动画
   - API类型：图生视频 (I2V)

3. **豆包Pro (专业版)** - `ep-20250624192235-zttm6`
   - 用途：高质量文生视频
   - 适合：专业级视频制作
   - API类型：文生视频 (T2V)

### 统一配置参数
```json
{
  "provider": "doubao",
  "apiKey": "e7fc00da-28b5-4628-9c59-588d559cdf1c",
  "enabled": true,
  "temperature": 0.7,
  "maxTokens": 4000,
  "topP": 0.9,
  "status": "connected"
}
```

## 数据库配置清理脚本

### 清理重复配置
创建文件 `fix-duplicate-models.js`：

```javascript
const { PrismaClient } = require('@prisma/client');

async function cleanDoubaoModels() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧹 清理重复的豆包模型配置...');
    
    // 1. 删除所有豆包配置
    const deleteResult = await prisma.aIConfig.deleteMany({
      where: { provider: 'doubao' }
    });
    console.log(`🗑️ 已删除 ${deleteResult.count} 个旧配置`);
    
    // 2. 添加标准配置
    const models = [
      {
        provider: 'doubao',
        model: 'ep-20250624192345-5ccwj',
        name: '豆包T2V (文生视频)',
        description: '豆包文生视频模型，支持纯文本生成5-8秒视频，适合创意场景和剧情动画',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624195026-qjsmk',
        name: '豆包I2V (图生视频)',
        description: '豆包图生视频模型，支持基于图像生成5-8秒视频，适合角色动作和场景动画',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: true,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624192235-zttm6',
        name: '豆包Pro (专业版)',
        description: '豆包专业版视频生成模型，支持高质量文生视频，已通过API连接测试',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      }
    ];
    
    for (const model of models) {
      await prisma.aIConfig.create({ data: model });
      console.log(`✅ 已添加: ${model.name}`);
    }
    
    console.log('\n🎉 豆包模型配置清理完成！');
  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanDoubaoModels();
```

运行清理脚本：
```bash
node fix-duplicate-models.js
```

## 今天遇到的关键问题及解决方案

### 问题1：用户选择的模型被智能选择器覆盖
**现象**：用户选择T2V模型，但系统自动选择了I2V模型
**原因**：智能模型选择器忽略了用户的手动选择
**解决方案**：修改API接收并使用用户选择的modelId

### 问题2：modelId参数传递链断裂
**现象**：`ReferenceError: modelId is not defined`
**原因**：函数调用链中缺少modelId参数传递
**解决方案**：完整修复所有函数签名和调用

#### 需要修改的文件和函数

**1. 前端组件** - `src/components/DetailedPlotExtraction.tsx`
```typescript
// 修改接口定义
onGenerateStoryVideo?: (episodeId: string, prompt: string, modelId?: string) => void

// 修改函数调用
await onGenerateStoryVideo(episodeId, generatedPrompt, selectedModel)
```

**2. 页面组件** - `src/app/projects/[id]/page.tsx`
```typescript
// 修改函数签名
const handleGenerateStoryVideo = async (episodeId: string, prompt: string, modelId?: string) => {

// 修改API调用
body: JSON.stringify({
  episodeId,
  prompt,
  projectId,
  modelId  // 添加这行
})
```

**3. 后端API** - `src/app/api/ai/generate-story-video/route.ts`
```typescript
// 修改请求参数解析
const { episodeId, prompt, projectId, retrySegmentId, modelId } = body

// 修改所有函数签名，添加modelId参数：
- generateSegmentedStoryVideo(..., modelId?: string)
- generateSegmentsAsync(..., modelId?: string)  
- generateSingleSegment(..., modelId?: string)
- callVideoGenerationAPI(..., modelId?: string)

// 修改所有函数调用，传递modelId参数
```

### 问题3：模型选择下拉菜单重名
**现象**：下拉菜单中有重复的模型名称
**原因**：数据库中存在重复的模型配置
**解决方案**：使用清理脚本删除重复配置，保持唯一性

## 正确的使用流程

### 1. 清理数据库配置
```bash
node fix-duplicate-models.js
```

### 2. 重启开发服务器
```bash
npm run dev
```

### 3. 选择正确的模型
- **文生视频**：选择"豆包T2V (文生视频)"
- **图生视频**：选择"豆包I2V (图生视频)"  
- **高质量**：选择"豆包Pro (专业版)"

### 4. 验证模型选择生效
查看后端日志，应该显示：
```
🎯 使用用户指定的模型:
   模型: 豆包T2V (文生视频) (ep-20250624192345-5ccwj)
   提供商: doubao
```

## 常见错误及解决方案

### 错误1：`modelId is not defined`
**解决方案**：检查函数调用链，确保所有函数都接收并传递modelId参数

### 错误2：`image to video models require image in content`
**解决方案**：确认选择了正确的T2V模型，而不是I2V模型

### 错误3：模型选择下拉重名
**解决方案**：运行清理脚本，删除重复配置

### 错误4：500内部服务器错误
**解决方案**：检查后端日志，通常是参数传递问题

## 调试技巧

### 1. 查看后端日志
```bash
# 查看模型选择日志
grep "使用用户指定的模型" logs

# 查看错误日志  
grep "ERROR\|Failed\|Error" logs
```

### 2. 验证数据库配置
```sql
SELECT id, name, model, provider, enabled FROM AIConfig WHERE provider = 'doubao';
```

### 3. 测试API调用
```bash
curl -X POST http://localhost:3001/api/ai/generate-story-video \
  -H "Content-Type: application/json" \
  -d '{"episodeId":"test","prompt":"test","projectId":"test","modelId":"cmcaqi6gk0000vms0mynh86ms"}'
```

## 注意事项

1. **模型ID必须准确**：确保使用正确的模型ID，不要混淆T2V和I2V
2. **参数传递完整**：确保整个调用链都传递了modelId参数
3. **数据库配置唯一**：避免重复的模型配置导致选择混乱
4. **重启服务器**：修改代码后必须重启开发服务器
5. **清理缓存**：必要时清理浏览器缓存和Next.js缓存

## 成功标志

当配置正确时，应该看到：
1. 下拉菜单中只有3个清晰命名的选项
2. 后端日志显示使用了用户选择的模型
3. 视频生成成功，没有API错误
4. 前端显示生成进度和结果

## 详细修复步骤记录

### 完整的代码修改清单

#### 1. 前端组件修改
**文件**: `src/components/DetailedPlotExtraction.tsx`

```typescript
// 第46行 - 修改接口定义
- onGenerateStoryVideo?: (episodeId: string, prompt: string) => void
+ onGenerateStoryVideo?: (episodeId: string, prompt: string, modelId?: string) => void

// 第151-154行 - 修改函数调用
- await onGenerateStoryVideo(episodeId, generatedPrompt)
+ await onGenerateStoryVideo(episodeId, generatedPrompt, selectedModel)
```

#### 2. 页面组件修改
**文件**: `src/app/projects/[id]/page.tsx`

```typescript
// 第185-186行 - 修改函数签名
- const handleGenerateStoryVideo = async (episodeId: string, prompt: string) => {
+ const handleGenerateStoryVideo = async (episodeId: string, prompt: string, modelId?: string) => {

// 第221-231行 - 修改API调用
body: JSON.stringify({
  episodeId,
  prompt,
  projectId,
+ modelId  // 添加这行
})
```

#### 3. 后端API修改
**文件**: `src/app/api/ai/generate-story-video/route.ts`

```typescript
// 第320行 - 修改请求参数解析
- const { episodeId, prompt, projectId, retrySegmentId } = body
+ const { episodeId, prompt, projectId, retrySegmentId, modelId } = body

// 第409行 - 修改函数调用
- const videoResult = await generateSegmentedStoryVideo(aiConfig, episodeId, projectId, plotInfo, isRegeneration)
+ const videoResult = await generateSegmentedStoryVideo(aiConfig, episodeId, projectId, plotInfo, isRegeneration, modelId)

// 第435行 - 修改函数签名
- async function generateSegmentedStoryVideo(aiConfig: any, episodeId: string, projectId: string, plotInfo: any, isRegeneration: boolean = false) {
+ async function generateSegmentedStoryVideo(aiConfig: any, episodeId: string, projectId: string, plotInfo: any, isRegeneration: boolean = false, modelId?: string) {

// 第481行 - 修改函数调用
- generateSegmentsAsync(aiConfig, storyVideo.id, videoSegments)
+ generateSegmentsAsync(aiConfig, storyVideo.id, videoSegments, modelId)

// 第589行 - 修改函数签名
- async function generateSegmentsAsync(aiConfig: any, storyVideoId: string, segments: any[]) {
+ async function generateSegmentsAsync(aiConfig: any, storyVideoId: string, segments: any[], modelId?: string) {

// 第614行 - 修改函数调用
- const videoResult = await generateSingleSegment(aiConfig, segment)
+ const videoResult = await generateSingleSegment(aiConfig, segment, modelId)

// 第972行 - 修改函数签名
- async function generateSingleSegment(aiConfig: any, segment: any) {
+ async function generateSingleSegment(aiConfig: any, segment: any, modelId?: string) {

// 第1014行 - 修改函数调用
- const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment)
+ const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment, modelId)

// 第1037行 - 修改函数签名
- async function callVideoGenerationAPI(prompt: string, segment: any) {
+ async function callVideoGenerationAPI(prompt: string, segment: any, modelId?: string) {
```

### 关键修改点说明

1. **参数传递链完整性**：确保从前端到后端的每个函数都能接收和传递modelId
2. **用户选择优先**：后端优先使用用户选择的模型，而不是智能选择器
3. **错误处理**：添加了模型不存在的错误处理
4. **日志输出**：添加了详细的模型选择日志

### 验证修复成功的标志

1. **前端选择生效**：用户选择的模型名称正确显示
2. **后端日志正确**：显示"🎯 使用用户指定的模型"
3. **API调用成功**：不再出现I2V/T2V混淆错误
4. **视频生成成功**：片段生成不再失败

### 故障排除检查清单

- [ ] 运行了数据库清理脚本
- [ ] 重启了开发服务器
- [ ] 清理了浏览器缓存
- [ ] 检查了所有函数签名修改
- [ ] 验证了参数传递链完整性
- [ ] 确认了模型ID正确性

## 智能序列拆分逻辑

### 核心问题解决
**问题**：原始剧情80秒内容 vs 生成视频20秒，内容覆盖率仅25%
**解决方案**：智能拆分长序列为多个5秒片段，确保内容完整覆盖

### 拆分算法实现

#### 1. 基于动作类型的智能拆分
```typescript
// 遇险类动作拆分为4个阶段
const phases = ['接近危险', '察觉危险', '发生意外', '应对危机']

// 救援类动作拆分为4个阶段
const phases = ['发现情况', '准备救援', '执行救援', '完成救援']

// 通用动作按时间进度拆分
const phases = ['开始', '发展', '高潮', '结束']
```

#### 2. 拆分效果对比
```
修改前：4个序列 × 5秒 = 20秒视频（25%覆盖率）
修改后：4个序列 → 16个片段 × 5秒 = 80秒视频（100%覆盖率）
```

#### 3. 实际拆分示例
**原始序列1**：张三遇险（20秒）
- 片段1：张三接近危险（5秒）
- 片段2：张三察觉危险（5秒）
- 片段3：张三发生意外（5秒）
- 片段4：张三应对危机（5秒）

**原始序列2**：李四救援（25秒）
- 片段5：李四发现情况（5秒）
- 片段6：李四准备救援（5秒）
- 片段7：李四执行救援（5秒）
- 片段8：李四完成救援（5秒）
- 片段9：李四确认安全（5秒）

### 技术实现要点

1. **固定5秒时长**：基于当前大模型技术限制
2. **智能阶段划分**：根据动作类型自动选择拆分策略
3. **内容连贯性**：确保片段间逻辑连接
4. **元数据保持**：保留原始序列信息用于后期合并

## 人物一致性问题及解决方案

### 当前问题
1. **角色图像缺失**：项目角色没有生成参考图像
2. **一致性约束未应用**：视频生成时没有使用角色DNA
3. **前后不一致**：同一角色在不同片段中外观差异很大

### 解决步骤

#### 1. 生成角色参考图像
```bash
# 访问角色管理页面
http://localhost:3001/projects/[projectId]?tab=characters

# 为每个角色生成三视图
1. 点击角色卡片
2. 点击"生成外观"按钮
3. 选择AI模型（推荐通义万相）
4. 等待生成完成
```

#### 2. 启用I2V模型
```bash
# 确保豆包I2V模型已启用
模型：豆包I2V (图生视频) - ep-20250624195026-qjsmk
用途：基于角色图像生成一致性视频
```

#### 3. 使用一致性视频生成
```bash
# 在视频生成时选择I2V模型
1. 选择"豆包I2V (图生视频)"
2. 系统会自动使用角色参考图像
3. 确保角色外观前后一致
```

#### 4. 验证一致性效果
```bash
# 检查生成的视频
1. 角色面部特征是否一致
2. 发型、服装是否保持不变
3. 独特标识是否清晰可见
```

### 技术实现要点

1. **角色DNA系统**：详细记录角色特征
2. **参考图像约束**：使用角色图像指导视频生成
3. **智能模型选择**：有图像时自动选择I2V模型
4. **一致性验证**：生成后验证角色一致性

---

**最后更新**：2025-06-24
**状态**：已验证可用
**重要提醒**：遇到问题时，首先运行清理脚本，然后重启服务器
**核心教训**：用户选择的模型必须在整个调用链中正确传递，不能被智能选择器覆盖
**新增功能**：智能序列拆分，确保内容完整覆盖，解决时长不匹配问题
**人物一致性**：必须先生成角色参考图像，然后使用I2V模型确保一致性
