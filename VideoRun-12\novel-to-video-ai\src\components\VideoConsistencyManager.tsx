'use client'

import { useState, useEffect } from 'react'
import { Users, MapPin, Clock, Eye, Settings, RefreshCw, X } from 'lucide-react'

interface Character {
  id: string
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
}

interface Scene {
  id: string
  location: string
  description: string
  atmosphere: string
}

interface VideoConsistencyManagerProps {
  characters: Character[]
  scenes: Scene[]
  currentEpisode: any
  onPromptGenerated?: (prompt: string) => void
  isOpen: boolean
  onClose: () => void
}

export default function VideoConsistencyManager({
  characters,
  scenes,
  currentEpisode,
  onPromptGenerated,
  isOpen,
  onClose
}: VideoConsistencyManagerProps) {
  const [selectedCharacters, setSelectedCharacters] = useState<string[]>([])
  const [selectedScene, setSelectedScene] = useState<string>('')
  const [timeOfDay, setTimeOfDay] = useState<'morning' | 'midday' | 'evening'>('midday')
  const [emotionalArc, setEmotionalArc] = useState('')
  const [customEnhancement, setCustomEnhancement] = useState('')
  const [generatedPrompt, setGeneratedPrompt] = useState('')

  // 初始化选择
  useEffect(() => {
    if (characters.length > 0 && selectedCharacters.length === 0) {
      setSelectedCharacters([characters[0].id])
    }
    if (scenes.length > 0 && !selectedScene) {
      setSelectedScene(scenes[0].id)
    }
  }, [characters, scenes])

  // 生成视频提示词
  const generateVideoPrompt = () => {
    try {
      // 获取选中的角色信息
      const selectedCharacterData = characters.filter(char => 
        selectedCharacters.includes(char.id)
      )

      // 获取选中的场景信息
      const selectedSceneData = scenes.find(scene => scene.id === selectedScene)

      if (!selectedSceneData || selectedCharacterData.length === 0) {
        alert('请选择角色和场景')
        return
      }

      // 构建提示词（这里简化处理，实际应该调用 storyVideoPromptGenerator）
      const prompt = buildConsistencyPrompt(
        selectedCharacterData,
        selectedSceneData,
        timeOfDay,
        emotionalArc,
        customEnhancement
      )

      setGeneratedPrompt(prompt)
      if (onPromptGenerated) {
        onPromptGenerated(prompt)
      }
    } catch (error) {
      console.error('生成提示词失败:', error)
      alert('生成提示词失败，请重试')
    }
  }

  // 构建一致性提示词
  const buildConsistencyPrompt = (
    chars: Character[],
    scene: Scene,
    time: string,
    arc: string,
    enhancement: string
  ): string => {
    const characterDescriptions = chars.map(char => {
      const features = []
      if (char.identity) features.push(char.identity)
      if (char.facial) features.push(char.facial)
      if (char.hairstyle) features.push(char.hairstyle)
      if (char.clothing) features.push(char.clothing)
      
      return `${char.name}: ${features.join(', ')}, maintaining consistent appearance across scenes`
    }).join('\n')

    const timeDescriptions = {
      morning: 'soft morning light, fresh atmosphere',
      midday: 'bright natural light, active atmosphere', 
      evening: 'warm evening light, calm atmosphere'
    }

    let prompt = `STORY VIDEO GENERATION:

CHARACTER CONSISTENCY:
${characterDescriptions}

SCENE CONSISTENCY:
Location: ${scene.location}
Description: ${scene.description}
Atmosphere: ${scene.atmosphere}
Time: ${time} - ${timeDescriptions[time]}
Maintaining same spatial layout and environmental elements

ACTION SEQUENCE:
Based on episode content: ${currentEpisode?.content || '剧情内容'}
Emotional progression: ${arc || 'natural story development'}

TECHNICAL REQUIREMENTS:
- 6-second duration
- Cinematic quality
- Consistent character appearance
- Consistent scene layout
- Smooth camera movement
- Professional lighting`

    if (enhancement.trim()) {
      prompt += `\n\nCUSTOM ENHANCEMENT:\n${enhancement}`
    }

    return prompt
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Settings className="text-blue-600 mr-2" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">
              视频一致性管理 - {currentEpisode?.title}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
            title="关闭"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* 左侧配置面板 */}
          <div className="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
            <div className="space-y-6">
              {/* 角色选择 */}
              <div>
                <div className="flex items-center mb-3">
                  <Users className="text-blue-600 mr-2" size={20} />
                  <h3 className="text-lg font-medium text-gray-900">角色选择</h3>
                </div>
                <div className="space-y-2">
                  {characters.map(character => (
                    <label key={character.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedCharacters.includes(character.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCharacters([...selectedCharacters, character.id])
                          } else {
                            setSelectedCharacters(selectedCharacters.filter(id => id !== character.id))
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm font-medium">{character.name}</span>
                      {character.identity && (
                        <span className="ml-2 text-xs text-gray-500">
                          ({character.identity.substring(0, 20)}...)
                        </span>
                      )}
                    </label>
                  ))}
                </div>
              </div>

              {/* 场景选择 */}
              <div>
                <div className="flex items-center mb-3">
                  <MapPin className="text-green-600 mr-2" size={20} />
                  <h3 className="text-lg font-medium text-gray-900">场景选择</h3>
                </div>
                <select
                  value={selectedScene}
                  onChange={(e) => setSelectedScene(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {scenes.map(scene => (
                    <option key={scene.id} value={scene.id}>
                      {scene.location}
                    </option>
                  ))}
                </select>
                {selectedScene && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
                    {scenes.find(s => s.id === selectedScene)?.description}
                  </div>
                )}
              </div>

              {/* 时间设置 */}
              <div>
                <div className="flex items-center mb-3">
                  <Clock className="text-orange-600 mr-2" size={20} />
                  <h3 className="text-lg font-medium text-gray-900">时间设置</h3>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  {(['morning', 'midday', 'evening'] as const).map(time => (
                    <button
                      key={time}
                      onClick={() => setTimeOfDay(time)}
                      className={`px-3 py-2 text-sm rounded-md ${
                        timeOfDay === time
                          ? 'bg-orange-100 text-orange-700 border border-orange-300'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {time === 'morning' ? '早晨' : time === 'midday' ? '中午' : '傍晚'}
                    </button>
                  ))}
                </div>
              </div>

              {/* 情感弧线 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  情感弧线
                </label>
                <input
                  type="text"
                  value={emotionalArc}
                  onChange={(e) => setEmotionalArc(e.target.value)}
                  placeholder="例如：专注 → 困惑 → 灵感 → 兴奋 → 满足"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 自定义增强 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义增强要求
                </label>
                <textarea
                  value={customEnhancement}
                  onChange={(e) => setCustomEnhancement(e.target.value)}
                  placeholder="添加特殊要求，如特定镜头角度、特效等..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 生成按钮 */}
              <button
                onClick={generateVideoPrompt}
                className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <RefreshCw className="mr-2" size={16} />
                生成一致性提示词
              </button>
            </div>
          </div>

          {/* 右侧预览面板 */}
          <div className="w-1/2 p-6 overflow-y-auto">
            <div className="flex items-center mb-4">
              <Eye className="text-purple-600 mr-2" size={20} />
              <h3 className="text-lg font-medium text-gray-900">生成的提示词</h3>
            </div>

            {generatedPrompt ? (
              <div className="space-y-4">
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
                    {generatedPrompt}
                  </pre>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => navigator.clipboard.writeText(generatedPrompt)}
                    className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200"
                  >
                    复制提示词
                  </button>
                  <button
                    onClick={() => {
                      if (onPromptGenerated) {
                        onPromptGenerated(generatedPrompt)
                      }
                      onClose()
                    }}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                  >
                    使用此提示词
                  </button>
                </div>

                {/* 一致性检查提示 */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">一致性检查要点</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• 角色外貌特征在所有场景中保持一致</li>
                    <li>• 场景布局和环境细节保持连贯</li>
                    <li>• 光线和色彩风格统一</li>
                    <li>• 服装和道具符合时间线逻辑</li>
                  </ul>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Eye className="mx-auto mb-2" size={48} />
                <p>点击"生成一致性提示词"查看结果</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
