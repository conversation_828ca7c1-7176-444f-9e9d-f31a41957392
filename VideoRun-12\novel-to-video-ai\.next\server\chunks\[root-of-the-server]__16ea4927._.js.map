{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/skyreels.ts"], "sourcesContent": ["import { AIConfig } from '@/types'\n\n// SkyReels-V2 API响应类型\nexport interface SkyReelsTaskResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsStatusResponse {\n  task_id: string\n  status: 'queued' | 'processing' | 'completed' | 'failed'\n  progress: number\n  message: string\n  video_path?: string\n  error?: string\n}\n\nexport interface SkyReelsHealthResponse {\n  status: 'healthy' | 'unhealthy'\n  model_loaded: boolean\n  timestamp: string\n}\n\nexport interface SkyReelsGenerationParams {\n  prompt: string\n  num_frames?: number\n  guidance_scale?: number\n  seed?: number\n  fps?: number\n  resolution?: string\n}\n\n// SkyReels-V2 API客户端\nexport class SkyReelsClient {\n  private baseUrl: string\n  private apiKey: string\n  private model: string\n\n  constructor(config: AIConfig) {\n    // SkyReels是本地API，apiKey用作baseUrl\n    this.baseUrl = config.apiKey || 'http://localhost:8000'\n    this.apiKey = config.apiKey\n    this.model = config.model || 'SkyReels-V2-DF-1.3B-540P'\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(`${this.baseUrl}/health`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data: SkyReelsHealthResponse = await response.json()\n      return data.status === 'healthy' && data.model_loaded\n    } catch (error) {\n      console.error('SkyReels连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 生成视频\n  async generateVideo(params: SkyReelsGenerationParams): Promise<SkyReelsTaskResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: params.prompt,\n          num_frames: params.num_frames || 257, // 默认10秒视频\n          guidance_scale: params.guidance_scale || 6.0,\n          seed: params.seed,\n          fps: params.fps || 24,\n          resolution: params.resolution || '540P',\n        }),\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsTaskResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels视频生成失败:', error)\n      throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 查询任务状态\n  async getTaskStatus(taskId: string): Promise<SkyReelsStatusResponse> {\n    try {\n      const response = await fetch(`${this.baseUrl}/status/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data: SkyReelsStatusResponse = await response.json()\n      return data\n    } catch (error) {\n      console.error('SkyReels状态查询失败:', error)\n      throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 下载视频\n  async downloadVideo(taskId: string): Promise<Blob> {\n    try {\n      const response = await fetch(`${this.baseUrl}/download/${taskId}`, {\n        method: 'GET',\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      return await response.blob()\n    } catch (error) {\n      console.error('SkyReels视频下载失败:', error)\n      throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n\n  // 等待任务完成并返回视频URL\n  async generateAndWait(\n    params: SkyReelsGenerationParams,\n    maxWaitTime: number = 1800000, // 30分钟\n    pollInterval: number = 5000 // 5秒\n  ): Promise<string> {\n    console.log('🎬 开始SkyReels视频生成...')\n    \n    // 开始生成\n    const task = await this.generateVideo(params)\n    console.log(`📝 任务已创建: ${task.task_id}`)\n\n    const startTime = Date.now()\n    \n    // 轮询状态直到完成\n    while (Date.now() - startTime < maxWaitTime) {\n      const status = await this.getTaskStatus(task.task_id)\n      console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`)\n\n      if (status.status === 'completed') {\n        console.log('✅ 视频生成完成!')\n        return status.video_path || ''\n      } else if (status.status === 'failed') {\n        throw new Error(`视频生成失败: ${status.error || '未知错误'}`)\n      }\n\n      // 等待下次轮询\n      await new Promise(resolve => setTimeout(resolve, pollInterval))\n    }\n\n    throw new Error('视频生成超时')\n  }\n\n  // 获取所有任务列表\n  async getTasks(): Promise<any[]> {\n    try {\n      const response = await fetch(`${this.baseUrl}/tasks`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      if (!response.ok) {\n        const errorText = await response.text()\n        throw new Error(`HTTP ${response.status}: ${errorText}`)\n      }\n\n      const data = await response.json()\n      return data.tasks || []\n    } catch (error) {\n      console.error('SkyReels任务列表获取失败:', error)\n      throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`)\n    }\n  }\n}\n\n// AI服务错误类\nexport class SkyReelsServiceError extends Error {\n  constructor(message: string, public statusCode?: number) {\n    super(message)\n    this.name = 'SkyReelsServiceError'\n  }\n}\n"], "names": [], "mappings": ";;;;AAoCO,MAAM;IACH,QAAe;IACf,OAAc;IACd,MAAa;IAErB,YAAY,MAAgB,CAAE;QAC5B,iCAAiC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,IAAI;QAChC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;IAC/B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO,KAAK,MAAM,KAAK,aAAa,KAAK,YAAY;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAgC,EAAiC;QACnF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,OAAO,MAAM;oBACrB,YAAY,OAAO,UAAU,IAAI;oBACjC,gBAAgB,OAAO,cAAc,IAAI;oBACzC,MAAM,OAAO,IAAI;oBACjB,KAAK,OAAO,GAAG,IAAI;oBACnB,YAAY,OAAO,UAAU,IAAI;gBACnC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,SAAS;IACT,MAAM,cAAc,MAAc,EAAmC;QACnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAA+B,MAAM,SAAS,IAAI;YACxD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,OAAO;IACP,MAAM,cAAc,MAAc,EAAiB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE;gBACjE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA,iBAAiB;IACjB,MAAM,gBACJ,MAAgC,EAChC,cAAsB,OAAO,EAC7B,eAAuB,KAAK,KAAK;IAAN,EACV;QACjB,QAAQ,GAAG,CAAC;QAEZ,OAAO;QACP,MAAM,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;QACtC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAEvC,MAAM,YAAY,KAAK,GAAG;QAE1B,WAAW;QACX,MAAO,KAAK,GAAG,KAAK,YAAY,YAAa;YAC3C,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,OAAO;YACpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,OAAO,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YAEnF,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,UAAU,IAAI;YAC9B,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU;gBACrC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,QAAQ;YACrD;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA,WAAW;IACX,MAAM,WAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,WAAW;YACzD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,KAAK,IAAI,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;AACF;AAGO,MAAM,6BAA6B;;IACxC,YAAY,OAAe,EAAE,AAAO,UAAmB,CAAE;QACvD,KAAK,CAAC,eAD4B,aAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/ai.ts"], "sourcesContent": ["import { AIConfig, Character, Episode, PlotInfo, AnalysisResponse } from '@/types'\nimport { SkyReelsClient } from './skyreels'\n\n// AI服务错误类\nexport class AIServiceError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode: number = 500\n  ) {\n    super(message)\n    this.name = 'AIServiceError'\n  }\n}\n\n// DeepSeek API客户端\nexport class DeepSeekClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string = 'https://api.deepseek.com/v1/chat/completions'\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model\n  }\n\n  // 测试API连接\n  async testConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: '测试连接' }],\n          max_tokens: 10,\n        }),\n      })\n\n      return response.ok\n    } catch (error) {\n      console.error('DeepSeek连接测试失败:', error)\n      return false\n    }\n  }\n\n  // 调用AI API的通用方法（公开方法）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: this.model,\n          messages: [{ role: 'user', content: prompt }],\n          max_tokens: maxTokens,\n          temperature: 0.7,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new AIServiceError(\n          errorData.error?.message || 'API调用失败',\n          'API_ERROR',\n          response.status\n        )\n      }\n\n      const data = await response.json()\n      return data.choices[0]?.message?.content || ''\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        'AI服务调用失败，请检查网络连接',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 生成测试响应\n  private generateTestResponse(prompt: string): string {\n    if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {\n      // 角色一致性分析的测试响应\n      return `{\n        \"characters\": [\n          {\n            \"name\": \"张小雅\",\n            \"identity\": \"高中生\",\n            \"appearance\": \"马尾辫，甜美笑容，青春活泼\",\n            \"personality\": \"开朗活泼，善良纯真\",\n            \"clothing\": \"校服或休闲装\",\n            \"role\": \"主要角色\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"zhang_xiaoya\",\n              \"consistencyMatch\": 0.95,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持马尾辫和甜美笑容的标志性特征\"\n            }\n          },\n          {\n            \"name\": \"王美丽\",\n            \"identity\": \"咖啡店老板\",\n            \"appearance\": \"瓜子脸，波浪卷发，温柔气质\",\n            \"personality\": \"温柔善良，热情好客\",\n            \"clothing\": \"简约优雅的服装\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"wang_meili\",\n              \"consistencyMatch\": 0.88,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持瓜子脸和波浪卷发的特征\"\n            }\n          },\n          {\n            \"name\": \"李明轩\",\n            \"identity\": \"大学教授\",\n            \"appearance\": \"方脸，花白短发，学者气质\",\n            \"personality\": \"温文尔雅，博学睿智\",\n            \"clothing\": \"正式的学者装扮\",\n            \"role\": \"重要配角\",\n            \"isKnownCharacter\": true,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": \"li_mingxuan\",\n              \"consistencyMatch\": 0.92,\n              \"differences\": [],\n              \"consistencyConstraints\": \"保持方脸和花白短发的学者形象\"\n            }\n          },\n          {\n            \"name\": \"林志强\",\n            \"identity\": \"程序员\",\n            \"appearance\": \"高瘦身材，黑框眼镜，简约穿着\",\n            \"personality\": \"内向专注，技术宅\",\n            \"clothing\": \"白色T恤，牛仔裤\",\n            \"role\": \"新角色\",\n            \"isKnownCharacter\": false,\n            \"consistencyInfo\": {\n              \"matchedCharacterId\": null,\n              \"consistencyMatch\": 0.0,\n              \"differences\": [\"新出现的角色\"],\n              \"consistencyConstraints\": \"建立新的角色DNA档案\"\n            }\n          }\n        ]\n      }`\n    } else if (prompt.includes('场景信息')) {\n      return `{\n        \"scenes\": [\n          {\n            \"location\": \"温馨咖啡店\",\n            \"description\": \"装修温馨的小型咖啡店，木质桌椅，暖色调灯光\",\n            \"atmosphere\": \"温馨舒适，充满生活气息\",\n            \"timeOfDay\": \"下午\",\n            \"lighting\": \"暖色调室内灯光\",\n            \"keyElements\": \"咖啡香味，轻柔音乐，温馨装饰\"\n          }\n        ]\n      }`\n    } else if (prompt.includes('情节序列')) {\n      return `{\n        \"plotSequences\": [\n          {\n            \"sequenceId\": \"reunion_1\",\n            \"action\": \"张小雅进入咖啡店与王美丽重逢\",\n            \"emotion\": \"惊喜和温暖\",\n            \"duration\": \"2分钟\",\n            \"keyMoments\": [\"进门\", \"相认\", \"拥抱\"],\n            \"visualElements\": \"特写表情变化，温馨的重逢场面\"\n          },\n          {\n            \"sequenceId\": \"professor_arrival\",\n            \"action\": \"李明轩教授进入咖啡店\",\n            \"emotion\": \"温和友善\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"打招呼\"],\n            \"visualElements\": \"学者风度，温和笑容\"\n          },\n          {\n            \"sequenceId\": \"new_character\",\n            \"action\": \"林志强询问WiFi\",\n            \"emotion\": \"略显紧张的初次见面\",\n            \"duration\": \"1分钟\",\n            \"keyMoments\": [\"进门\", \"询问\", \"介绍\"],\n            \"visualElements\": \"新角色登场，技术宅形象\"\n          }\n        ]\n      }`\n    } else {\n      return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽'\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n    \n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析AI响应失败:', error)\n      throw new AIServiceError(\n        'AI响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n    \n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n    \n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// 豆包 (Doubao) API客户端\nexport class DoubaoClient {\n  private apiKey: string\n  private model: string\n  private baseUrl: string\n  private isVideoModel: boolean\n\n  constructor(config: AIConfig) {\n    this.apiKey = config.apiKey\n    this.model = config.model || 'doubao-seedance-1.0-pro'\n    // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID\n    this.isVideoModel = this.model.includes('seedance') ||\n                       this.model.includes('video') ||\n                       this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式\n\n    // 根据模型类型选择正确的API端点\n    if (this.isVideoModel) {\n      // 豆包视频生成使用专门的视频生成API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks'\n    } else {\n      // 文本模型使用chat completions API\n      this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'\n    }\n\n    if (config.baseUrl) {\n      this.baseUrl = config.baseUrl\n    }\n  }\n\n  // 测试API连接（带重试机制）\n  async testConnection(): Promise<boolean> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: \"测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p\"\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: '测试连接' }],\n            max_tokens: 10\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          return true\n        }\n\n        // 检查是否是可重试的错误\n        const errorText = await response.text()\n        if (errorText.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        return false\n      } catch (error) {\n        console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error)\n        if (attempt < maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n        return false\n      }\n    }\n\n    return false\n  }\n\n  // 调用AI API的通用方法（带重试机制）\n  async callAPI(prompt: string, maxTokens: number = 4000): Promise<string> {\n    const maxRetries = 3\n    const retryDelay = 1000 // 1秒\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        let requestBody: any\n\n        if (this.isVideoModel) {\n          // 豆包视频生成使用官方确认的API格式\n          requestBody = {\n            model: this.model,\n            content: [\n              {\n                type: \"text\",\n                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`\n              }\n            ]\n          }\n        } else {\n          // 文本模型使用chat completions格式\n          requestBody = {\n            model: this.model,\n            messages: [{ role: 'user', content: prompt }],\n            max_tokens: maxTokens,\n            temperature: 0.7\n          }\n        }\n\n        const response = await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody),\n        })\n\n        if (response.ok) {\n          const data = await response.json()\n\n          if (this.isVideoModel) {\n            // 视频生成返回任务信息\n            return JSON.stringify({\n              task_id: data.task_id,\n              status: data.status || 'submitted',\n              message: '视频生成任务已提交，请稍后查询结果'\n            })\n          } else {\n            // 文本生成返回内容\n            return data.choices[0]?.message?.content || ''\n          }\n        }\n\n        const errorData = await response.json()\n        const errorMessage = errorData.error?.message || '豆包API调用失败'\n\n        // 检查是否是可重试的内部错误\n        if (errorMessage.includes('internal error') && attempt < maxRetries) {\n          console.log(`豆包API内部错误，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        // 不可重试的错误，直接抛出\n        throw new AIServiceError(\n          errorMessage,\n          'API_ERROR',\n          response.status\n        )\n      } catch (error) {\n        if (error instanceof AIServiceError) {\n          // 如果是已知的API错误且不可重试，直接抛出\n          throw error\n        }\n\n        // 网络错误等，可以重试\n        if (attempt < maxRetries) {\n          console.log(`豆包API调用失败，第${attempt}次重试...`)\n          await new Promise(resolve => setTimeout(resolve, retryDelay))\n          continue\n        }\n\n        throw new AIServiceError(\n          '豆包服务调用失败，请检查网络连接和API密钥',\n          'NETWORK_ERROR',\n          500\n        )\n      }\n    }\n\n    throw new AIServiceError(\n      '豆包服务调用失败，已达到最大重试次数',\n      'MAX_RETRIES_EXCEEDED',\n      500\n    )\n  }\n\n  // 专门的视频生成方法\n  async generateVideo(prompt: string, duration: number = 5): Promise<string> {\n    if (!this.isVideoModel) {\n      throw new Error('此模型不支持视频生成')\n    }\n\n    try {\n      const requestBody = {\n        model: this.model,\n        prompt: prompt,\n        video_setting: {\n          video_duration: duration,\n          video_aspect_ratio: '16:9',\n          video_resolution: '720p'\n        }\n      }\n\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        return JSON.stringify({\n          task_id: data.task_id,\n          status: data.status || 'submitted',\n          message: '视频生成任务已提交，请稍后查询结果'\n        })\n      }\n\n      const errorData = await response.json()\n      throw new AIServiceError(\n        errorData.error?.message || '视频生成失败',\n        'VIDEO_GENERATION_ERROR',\n        response.status\n      )\n    } catch (error) {\n      if (error instanceof AIServiceError) {\n        throw error\n      }\n      throw new AIServiceError(\n        '视频生成服务调用失败',\n        'NETWORK_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析小说，提取角色和剧集信息\n  async analyzeNovel(content: string, customPrompt?: string): Promise<AnalysisResponse> {\n    const prompt = this.buildAnalysisPrompt(content, customPrompt)\n    const response = await this.callAPI(prompt, 8000)\n\n    try {\n      return this.parseAnalysisResponse(response)\n    } catch (error) {\n      console.error('解析豆包响应失败:', error)\n      throw new AIServiceError(\n        '豆包响应解析失败，请重试',\n        'PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 分析单个剧集的剧情信息\n  async analyzePlot(episodeContent: string): Promise<PlotInfo> {\n    const prompt = this.buildPlotAnalysisPrompt(episodeContent)\n    const response = await this.callAPI(prompt, 4000)\n\n    try {\n      return this.parsePlotResponse(response)\n    } catch (error) {\n      console.error('解析豆包剧情分析响应失败:', error)\n      throw new AIServiceError(\n        '豆包剧情分析失败，请重试',\n        'PLOT_PARSE_ERROR',\n        500\n      )\n    }\n  }\n\n  // 构建小说分析提示词\n  private buildAnalysisPrompt(content: string, customPrompt?: string): string {\n    let basePrompt = `请分析以下小说文本，同时完成两个任务：`\n\n    if (customPrompt && customPrompt.trim()) {\n      basePrompt += `\\n\\n增强要求：${customPrompt}\\n`\n    }\n\n    return basePrompt + `\n\n任务1：提取所有主要角色信息\n任务2：按章节拆分成独立剧集\n\n要求：\n1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔\n2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节\n3. 严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\n    {\n      \"name\": \"角色姓名\",\n      \"appearance\": {\n        \"face\": \"五官描述\",\n        \"body\": \"身体特征\",\n        \"clothing\": \"服装描述\"\n      },\n      \"identity\": \"身份信息\",\n      \"personality\": \"性格特点\",\n      \"hiddenLines\": \"隐线伏笔\"\n    }\n  ],\n  \"episodes\": [\n    {\n      \"title\": \"第X章 标题\",\n      \"content\": \"完整章节内容\",\n      \"orderIndex\": 1\n    }\n  ]\n}\n\n小说文本：\n${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`\n  }\n\n  // 构建剧情分析提示词\n  private buildPlotAnalysisPrompt(episodeContent: string): string {\n    return `请分析以下剧集内容，提取三大核心信息：\n\n1. 本集人物：当前剧集中出场的所有角色名称\n2. 场景信息：故事发生的地点、环境描述、氛围设定\n3. 事件三要素：按照\"正常→矛盾冲突→升级事件\"的结构分析\n\n严格按照以下JSON格式返回：\n\n{\n  \"characters\": [\"角色名1\", \"角色名2\"],\n  \"scenes\": [\n    {\n      \"location\": \"场景地点\",\n      \"description\": \"环境描述\",\n      \"atmosphere\": \"氛围设定\"\n    }\n  ],\n  \"events\": [\n    {\n      \"normal\": \"正常状态描述\",\n      \"conflict\": \"矛盾冲突描述\",\n      \"escalation\": \"升级事件描述\",\n      \"participants\": [\"参与角色\"],\n      \"location\": \"发生地点\",\n      \"actions\": [\"具体行为\"]\n    }\n  ]\n}\n\n剧集内容：\n${episodeContent}`\n  }\n\n  // 解析小说分析响应\n  private parseAnalysisResponse(response: string): AnalysisResponse {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: parsed.characters?.map((char: any) => ({\n        name: char.name || '',\n        appearance: JSON.stringify(char.appearance || {}),\n        identity: char.identity || '',\n        personality: char.personality || '',\n        hiddenLines: char.hiddenLines || '',\n      })) || [],\n      episodes: parsed.episodes?.map((ep: any, index: number) => ({\n        title: ep.title || `第${index + 1}章`,\n        content: ep.content || '',\n        orderIndex: ep.orderIndex || index + 1,\n        status: 'created' as const,\n      })) || [],\n    }\n  }\n\n  // 解析剧情分析响应\n  private parsePlotResponse(response: string): any {\n    const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\n    if (!jsonMatch) {\n      throw new Error('未找到有效的JSON响应')\n    }\n\n    const parsed = JSON.parse(jsonMatch[0])\n\n    return {\n      characters: JSON.stringify(parsed.characters || []),\n      scenes: JSON.stringify(parsed.scenes || []),\n      events: JSON.stringify(parsed.events || []),\n    }\n  }\n}\n\n// AI客户端工厂函数\nexport function createAIClient(config: AIConfig): DeepSeekClient | DoubaoClient | SkyReelsClient {\n  switch (config.provider) {\n    case 'deepseek':\n      return new DeepSeekClient(config)\n    case 'doubao':\n      return new DoubaoClient(config)\n    case 'skyreels':\n      return new SkyReelsClient(config)\n    default:\n      // 默认使用DeepSeek客户端，但可以扩展支持其他提供商\n      return new DeepSeekClient(config)\n  }\n}\n\n// 错误处理包装器\nexport async function handleAIRequest<T>(\n  request: () => Promise<T>\n): Promise<T> {\n  try {\n    return await request()\n  } catch (error) {\n    if (error instanceof AIServiceError) {\n      throw error\n    }\n    \n    // 网络错误\n    if (error instanceof TypeError && error.message.includes('fetch')) {\n      throw new AIServiceError(\n        'AI服务连接失败，请检查网络连接',\n        'CONNECTION_ERROR',\n        503\n      )\n    }\n    \n    // 通用错误\n    throw new AIServiceError(\n      'AI服务处理失败，请重试',\n      'UNKNOWN_ERROR',\n      500\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;AAGO,MAAM,uBAAuB;;;IAClC,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,aAAqB,GAAG,CAC/B;QACA,KAAK,CAAC,eAHC,OAAA,WACA,aAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,UAAkB,+CAA8C;IAExE,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;IAC3B;IAEA,UAAU;IACV,MAAM,iBAAmC;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QAEvE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,IAAI,CAAC,KAAK;oBACjB,UAAU;wBAAC;4BAAE,MAAM;4BAAQ,SAAS;wBAAO;qBAAE;oBAC7C,YAAY;oBACZ,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,WAC5B,aACA,SAAS,MAAM;YAEnB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAC9C,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,oBACA,iBACA;QAEJ;IACF;IAEA,SAAS;IACD,qBAAqB,MAAc,EAAU;QACnD,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,UAAU;YACvD,eAAe;YACf,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+DP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;OAWP,CAAC;QACJ,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS;YAClC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BP,CAAC;QACJ,OAAO;YACL,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,eACR,cACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,MAAM;IACH,OAAc;IACd,MAAa;IACb,QAAe;IACf,aAAqB;IAE7B,YAAY,MAAgB,CAAE;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI;QAC7B,sDAAsD;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YACpB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,uBAAuB;;QAEvE,mBAAmB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,qBAAqB;YACrB,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,6BAA6B;YAC7B,IAAI,CAAC,OAAO,GAAG;QACjB;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC/B;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAmC;QACvC,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM;4BACR;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;oBACd;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,OAAO;gBACT;gBAEA,cAAc;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBAChE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;gBACzD,IAAI,UAAU,YAAY;oBACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBACA,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,QAAQ,MAAc,EAAE,YAAoB,IAAI,EAAmB;QACvE,MAAM,aAAa;QACnB,MAAM,aAAa,KAAK,KAAK;;QAE7B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI;gBAEJ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,qBAAqB;oBACrB,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,GAAG,OAAO,gDAAgD,CAAC;4BACnE;yBACD;oBACH;gBACF,OAAO;oBACL,2BAA2B;oBAC3B,cAAc;wBACZ,OAAO,IAAI,CAAC,KAAK;wBACjB,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAO;yBAAE;wBAC7C,YAAY;wBACZ,aAAa;oBACf;gBACF;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;wBACxC,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,IAAI,CAAC,YAAY,EAAE;wBACrB,aAAa;wBACb,OAAO,KAAK,SAAS,CAAC;4BACpB,SAAS,KAAK,OAAO;4BACrB,QAAQ,KAAK,MAAM,IAAI;4BACvB,SAAS;wBACX;oBACF,OAAO;wBACL,WAAW;wBACX,OAAO,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;oBAC9C;gBACF;gBAEA,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,eAAe,UAAU,KAAK,EAAE,WAAW;gBAEjD,gBAAgB;gBAChB,IAAI,aAAa,QAAQ,CAAC,qBAAqB,UAAU,YAAY;oBACnE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,eAAe;gBACf,MAAM,IAAI,eACR,cACA,aACA,SAAS,MAAM;YAEnB,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,gBAAgB;oBACnC,wBAAwB;oBACxB,MAAM;gBACR;gBAEA,aAAa;gBACb,IAAI,UAAU,YAAY;oBACxB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC;oBACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,MAAM,IAAI,eACR,0BACA,iBACA;YAEJ;QACF;QAEA,MAAM,IAAI,eACR,sBACA,wBACA;IAEJ;IAEA,YAAY;IACZ,MAAM,cAAc,MAAc,EAAE,WAAmB,CAAC,EAAmB;QACzE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,IAAI,CAAC,KAAK;gBACjB,QAAQ;gBACR,eAAe;oBACb,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,SAAS,CAAC;oBACpB,SAAS,KAAK,OAAO;oBACrB,QAAQ,KAAK,MAAM,IAAI;oBACvB,SAAS;gBACX;YACF;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,eACR,UAAU,KAAK,EAAE,WAAW,UAC5B,0BACA,SAAS,MAAM;QAEnB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YACA,MAAM,IAAI,eACR,cACA,iBACA;QAEJ;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,OAAe,EAAE,YAAqB,EAA6B;QACpF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,SAAS;QACjD,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,eACR,gBACA,eACA;QAEJ;IACF;IAEA,cAAc;IACd,MAAM,YAAY,cAAsB,EAAqB;QAC3D,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ;QAE5C,IAAI;YACF,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,eACR,gBACA,oBACA;QAEJ;IACF;IAEA,YAAY;IACJ,oBAAoB,OAAe,EAAE,YAAqB,EAAU;QAC1E,IAAI,aAAa,CAAC,mBAAmB,CAAC;QAEtC,IAAI,gBAAgB,aAAa,IAAI,IAAI;YACvC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QAC5C;QAEA,OAAO,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB,EAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,QAAQ,MAAM,GAAG,OAAO,QAAQ,IAAI;IACjE;IAEA,YAAY;IACJ,wBAAwB,cAAsB,EAAU;QAC9D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BZ,EAAE,gBAAgB;IAChB;IAEA,WAAW;IACH,sBAAsB,QAAgB,EAAoB;QAChE,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,OAAO,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;oBACjD,MAAM,KAAK,IAAI,IAAI;oBACnB,YAAY,KAAK,SAAS,CAAC,KAAK,UAAU,IAAI,CAAC;oBAC/C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,MAAM,EAAE;YACT,UAAU,OAAO,QAAQ,EAAE,IAAI,CAAC,IAAS,QAAkB,CAAC;oBAC1D,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACnC,SAAS,GAAG,OAAO,IAAI;oBACvB,YAAY,GAAG,UAAU,IAAI,QAAQ;oBACrC,QAAQ;gBACV,CAAC,MAAM,EAAE;QACX;IACF;IAEA,WAAW;IACH,kBAAkB,QAAgB,EAAO;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAEtC,OAAO;YACL,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,IAAI,EAAE;YAClD,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;YAC1C,QAAQ,KAAK,SAAS,CAAC,OAAO,MAAM,IAAI,EAAE;QAC5C;IACF;AACF;AAGO,SAAS,eAAe,MAAgB;IAC7C,OAAQ,OAAO,QAAQ;QACrB,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B,KAAK;YACH,OAAO,IAAI,aAAa;QAC1B,KAAK;YACH,OAAO,IAAI,wHAAA,CAAA,iBAAc,CAAC;QAC5B;YACE,+BAA+B;YAC/B,OAAO,IAAI,eAAe;IAC9B;AACF;AAGO,eAAe,gBACpB,OAAyB;IAEzB,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,gBAAgB;YACnC,MAAM;QACR;QAEA,OAAO;QACP,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,MAAM,IAAI,eACR,oBACA,oBACA;QAEJ;QAEA,OAAO;QACP,MAAM,IAAI,eACR,gBACA,iBACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-story-video/segment/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { DeepSeekClient } from '@/lib/ai'\n\n// POST - 生成单个视频片段（内部API）\nexport async function POST(request: NextRequest) {\n  try {\n    const { segmentId, modelId } = await request.json()\n\n    console.log('🎬 内部API：生成单个视频片段')\n    console.log('📋 参数:', { segmentId, modelId })\n\n    if (!segmentId) {\n      return NextResponse.json(\n        { success: false, error: '缺少片段ID' },\n        { status: 400 }\n      )\n    }\n\n    // 获取片段信息\n    const segment = await prisma.videoSegment.findUnique({\n      where: { id: segmentId }\n    })\n\n    if (!segment) {\n      return NextResponse.json(\n        { success: false, error: '片段不存在' },\n        { status: 404 }\n      )\n    }\n\n    console.log(`🎬 开始生成片段 ${segment.segmentIndex}: ${segment.title}`)\n\n    // 更新片段状态为生成中\n    await prisma.videoSegment.update({\n      where: { id: segmentId },\n      data: {\n        status: 'generating',\n        updatedAt: new Date()\n      }\n    })\n\n    // 异步调用真实的视频生成逻辑\n    generateSingleSegmentAsync(segment, modelId)\n      .then(() => {\n        console.log(`✅ 片段 ${segment.segmentIndex} 生成完成`)\n      })\n      .catch(async (error) => {\n        console.error(`❌ 片段 ${segment.segmentIndex} 生成失败:`, error)\n\n        // 更新数据库状态为失败\n        try {\n          await prisma.videoSegment.update({\n            where: { id: segment.id },\n            data: {\n              status: 'failed',\n              updatedAt: new Date()\n            }\n          })\n          console.log(`📝 已更新片段 ${segment.segmentIndex} 状态为失败`)\n        } catch (dbError) {\n          console.error(`❌ 更新片段状态失败:`, dbError)\n        }\n      })\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: `片段 ${segment.segmentIndex} 开始生成`,\n        segmentId: segment.id,\n        segmentIndex: segment.segmentIndex,\n        title: segment.title\n      }\n    })\n\n  } catch (error) {\n    console.error('生成单个视频片段失败:', error)\n    return NextResponse.json(\n      { success: false, error: '生成视频片段失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 异步生成单个片段\nasync function generateSingleSegmentAsync(segment: any, modelId?: string) {\n  try {\n    console.log(`🎬 开始异步生成片段: ${segment.title}`)\n\n    // 获取AI配置\n    const aiConfig = await prisma.aIConfig.findFirst({\n      where: { enabled: true }\n    })\n\n    if (!aiConfig) {\n      throw new Error('请先配置AI模型')\n    }\n\n    // 调用简化的视频生成函数\n    const videoResult = await generateSingleSegmentSimplified(aiConfig, segment, modelId)\n\n    // 根据生成结果更新片段信息\n    const updateData: any = {\n      duration: videoResult.duration,\n      metadata: JSON.stringify({\n        ...JSON.parse(segment.metadata || '{}'),\n        generatedAt: new Date().toISOString(),\n        optimizedPrompt: videoResult.optimizedPrompt\n      })\n    }\n\n    if (videoResult.videoUrl) {\n      updateData.status = 'completed'\n      updateData.videoUrl = videoResult.videoUrl\n      updateData.thumbnailUrl = videoResult.thumbnailUrl\n    } else {\n      updateData.status = 'failed'\n    }\n\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: updateData\n    })\n\n    console.log(`✅ 片段 ${segment.segmentIndex} 异步生成完成`)\n\n  } catch (error) {\n    console.error(`❌ 片段异步生成失败:`, error)\n\n    // 更新片段状态为失败\n    await prisma.videoSegment.update({\n      where: { id: segment.id },\n      data: {\n        status: 'failed',\n        metadata: JSON.stringify({\n          ...JSON.parse(segment.metadata || '{}'),\n          error: error instanceof Error ? error.message : '生成失败',\n          failedAt: new Date().toISOString()\n        })\n      }\n    })\n  }\n}\n\n// 简化版的单个片段生成函数\nasync function generateSingleSegmentSimplified(aiConfig: any, segment: any, modelId?: string) {\n  try {\n    console.log(`🎬 开始生成片段: ${segment.title}`)\n\n    // 1. 获取视频生成模型配置\n    let videoModel\n    if (modelId) {\n      videoModel = await prisma.aIConfig.findUnique({\n        where: { id: modelId }\n      })\n    } else {\n      // 优先选择豆包视频模型，特别是用户选择的 Doubao-Seedance-1.0-pro\n      videoModel = await prisma.aIConfig.findFirst({\n        where: {\n          name: 'Doubao-Seedance-1.0-pro',\n          enabled: true,\n          supportsVideo: true\n        }\n      })\n\n      // 如果没有找到豆包专业版，使用其他豆包模型\n      if (!videoModel) {\n        videoModel = await prisma.aIConfig.findFirst({\n          where: {\n            provider: 'doubao',\n            enabled: true,\n            OR: [\n              { supportsVideo: true },\n              { supportsImageToVideo: true }\n            ]\n          }\n        })\n      }\n\n      // 如果还没有找到，使用任何启用的视频模型\n      if (!videoModel) {\n        videoModel = await prisma.aIConfig.findFirst({\n          where: {\n            enabled: true,\n            OR: [\n              { supportsVideo: true },\n              { supportsImageToVideo: true }\n            ]\n          }\n        })\n      }\n    }\n\n    if (!videoModel) {\n      throw new Error('未找到可用的视频生成模型')\n    }\n\n    console.log(`🎯 使用模型: ${videoModel.name} (${videoModel.provider})`)\n\n    // 2. 优化提示词（可选）\n    let optimizedPrompt = segment.prompt\n    const deepSeekConfig = await prisma.aIConfig.findFirst({\n      where: {\n        provider: 'deepseek',\n        enabled: true\n      }\n    })\n\n    if (deepSeekConfig) {\n      try {\n        const deepSeekClient = new DeepSeekClient(deepSeekConfig)\n        const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：\n\n片段标题：${segment.title}\n片段描述：${segment.description}\n原始提示词：${segment.prompt}\n\n请按照以下要求优化：\n1. 确保描述具体且可视化\n2. 添加适合的镜头运动和角度\n3. 强调画面质量和风格\n4. 控制在100字以内\n\n优化后的提示词：`\n\n        optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800)\n        console.log(`✅ 提示词优化完成`)\n      } catch (error) {\n        console.warn('提示词优化失败，使用原始提示词:', error.message)\n      }\n    }\n\n    // 3. 调用视频生成API\n    const videoResult = await callVideoAPI(videoModel, optimizedPrompt.trim(), segment)\n\n    return {\n      videoUrl: videoResult.videoUrl,\n      thumbnailUrl: videoResult.thumbnailUrl,\n      optimizedPrompt: optimizedPrompt.trim(),\n      duration: videoResult.duration || segment.duration || 5,\n      status: videoResult.status || 'completed'\n    }\n\n  } catch (error) {\n    console.error('视频生成失败:', error)\n    return {\n      videoUrl: null,\n      thumbnailUrl: null,\n      optimizedPrompt: segment.prompt,\n      duration: segment.duration || 5,\n      status: 'failed',\n      error: error.message\n    }\n  }\n}\n\n// 简化版的视频API调用函数\nasync function callVideoAPI(videoModel: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 调用${videoModel.provider}视频API`)\n\n    // 根据不同的提供商调用相应的API\n    if (videoModel.provider === 'doubao') {\n      return await callDoubaoVideoAPISimplified(videoModel, prompt, segment)\n    } else if (videoModel.provider === 'minimax') {\n      return await callMinimaxVideoAPISimplified(videoModel, prompt, segment)\n    } else if (videoModel.provider === 'tongyi') {\n      return await callTongyiVideoAPISimplified(videoModel, prompt, segment)\n    } else {\n      // 对于其他提供商，返回模拟结果\n      console.warn(`暂不支持${videoModel.provider}提供商，返回模拟结果`)\n      return {\n        videoUrl: null,\n        thumbnailUrl: null,\n        duration: segment.duration || 5,\n        status: 'pending'\n      }\n    }\n  } catch (error) {\n    console.error('视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 简化版豆包视频API调用\nasync function callDoubaoVideoAPISimplified(config: any, prompt: string, segment: any) {\n  try {\n    console.log(`🎬 调用豆包视频API，片段: ${segment.segmentIndex}`)\n\n    // 构建请求内容\n    const content = [\n      {\n        type: \"text\",\n        text: prompt\n      }\n    ]\n\n    // 创建视频生成任务\n    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        model: config.model,\n        content: content\n      })\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error(`豆包API调用失败: ${response.status} ${errorText}`)\n      throw new Error(`豆包视频生成失败: ${response.status} ${errorText}`)\n    }\n\n    const result = await response.json()\n    console.log('豆包API响应:', result)\n\n    // 豆包API返回的是 id 字段，不是 task_id\n    const taskId = result.id || result.task_id\n    if (taskId) {\n      // 开始轮询任务状态\n      const videoResult = await pollDoubaoTaskStatus(config.apiKey, taskId, segment)\n      return videoResult\n    } else {\n      throw new Error('豆包API未返回任务ID')\n    }\n\n  } catch (error) {\n    console.error('豆包视频API调用失败:', error)\n    throw error\n  }\n}\n\n// 轮询豆包任务状态\nasync function pollDoubaoTaskStatus(apiKey: string, taskId: string, segment: any) {\n  const maxAttempts = 60 // 最多轮询60次（10分钟）\n  const pollInterval = 10000 // 10秒轮询一次\n\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      console.log(`🔄 轮询豆包任务状态 (${attempt}/${maxAttempts}): ${taskId}`)\n\n      const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (!response.ok) {\n        throw new Error(`轮询失败: ${response.status}`)\n      }\n\n      const result = await response.json()\n      console.log(`📊 任务状态: ${result.status}`)\n\n      // 豆包API的实际响应格式\n      if (result.status === 'succeeded' && result.content?.video_url) {\n        console.log(`✅ 豆包视频生成完成: ${result.content.video_url}`)\n        return {\n          videoUrl: result.content.video_url,\n          thumbnailUrl: result.content.thumbnail_url || null,\n          duration: segment.duration || 5,\n          status: 'completed'\n        }\n      } else if (result.status === 'failed') {\n        throw new Error(`豆包视频生成失败: ${result.error || '未知错误'}`)\n      }\n\n      // 如果还在处理中，等待后继续轮询\n      if (attempt < maxAttempts) {\n        await new Promise(resolve => setTimeout(resolve, pollInterval))\n      }\n\n    } catch (error) {\n      console.error(`轮询第${attempt}次失败:`, error)\n      if (attempt === maxAttempts) {\n        throw error\n      }\n    }\n  }\n\n  throw new Error('豆包视频生成超时')\n}\n\n// 简化版MiniMax视频API调用（占位符）\nasync function callMinimaxVideoAPISimplified(config: any, prompt: string, segment: any) {\n  console.warn('MiniMax视频生成暂未实现，返回模拟结果')\n  return {\n    videoUrl: null,\n    thumbnailUrl: null,\n    duration: segment.duration || 5,\n    status: 'pending'\n  }\n}\n\n// 简化版通义万相视频API调用（占位符）\nasync function callTongyiVideoAPISimplified(config: any, prompt: string, segment: any) {\n  console.warn('通义万相视频生成暂未实现，返回模拟结果')\n  return {\n    videoUrl: null,\n    thumbnailUrl: null,\n    duration: segment.duration || 5,\n    status: 'pending'\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,UAAU;YAAE;YAAW;QAAQ;QAE3C,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,YAAY,CAAC,EAAE,EAAE,QAAQ,KAAK,EAAE;QAEjE,aAAa;QACb,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ,QAAQ;gBACR,WAAW,IAAI;YACjB;QACF;QAEA,gBAAgB;QAChB,2BAA2B,SAAS,SACjC,IAAI,CAAC;YACJ,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,KAAK,CAAC;QACjD,GACC,KAAK,CAAC,OAAO;YACZ,QAAQ,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC,EAAE;YAEpD,aAAa;YACb,IAAI;gBACF,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,OAAO;wBAAE,IAAI,QAAQ,EAAE;oBAAC;oBACxB,MAAM;wBACJ,QAAQ;wBACR,WAAW,IAAI;oBACjB;gBACF;gBACA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC;YACtD,EAAE,OAAO,SAAS;gBAChB,QAAQ,KAAK,CAAC,CAAC,WAAW,CAAC,EAAE;YAC/B;QACF;QAEF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS,CAAC,GAAG,EAAE,QAAQ,YAAY,CAAC,KAAK,CAAC;gBAC1C,WAAW,QAAQ,EAAE;gBACrB,cAAc,QAAQ,YAAY;gBAClC,OAAO,QAAQ,KAAK;YACtB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,eAAe,2BAA2B,OAAY,EAAE,OAAgB;IACtE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,KAAK,EAAE;QAE3C,SAAS;QACT,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,cAAc;QACd,MAAM,cAAc,MAAM,gCAAgC,UAAU,SAAS;QAE7E,eAAe;QACf,MAAM,aAAkB;YACtB,UAAU,YAAY,QAAQ;YAC9B,UAAU,KAAK,SAAS,CAAC;gBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;gBACvC,aAAa,IAAI,OAAO,WAAW;gBACnC,iBAAiB,YAAY,eAAe;YAC9C;QACF;QAEA,IAAI,YAAY,QAAQ,EAAE;YACxB,WAAW,MAAM,GAAG;YACpB,WAAW,QAAQ,GAAG,YAAY,QAAQ;YAC1C,WAAW,YAAY,GAAG,YAAY,YAAY;QACpD,OAAO;YACL,WAAW,MAAM,GAAG;QACtB;QAEA,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,YAAY,CAAC,OAAO,CAAC;IAEnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,WAAW,CAAC,EAAE;QAE7B,YAAY;QACZ,MAAM,kHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,MAAM;gBACJ,QAAQ;gBACR,UAAU,KAAK,SAAS,CAAC;oBACvB,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,IAAI,KAAK;oBACvC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,UAAU,IAAI,OAAO,WAAW;gBAClC;YACF;QACF;IACF;AACF;AAEA,eAAe;AACf,eAAe,gCAAgC,QAAa,EAAE,OAAY,EAAE,OAAgB;IAC1F,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,KAAK,EAAE;QAEzC,gBAAgB;QAChB,IAAI;QACJ,IAAI,SAAS;YACX,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC5C,OAAO;oBAAE,IAAI;gBAAQ;YACvB;QACF,OAAO;YACL,8CAA8C;YAC9C,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC3C,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,eAAe;gBACjB;YACF;YAEA,uBAAuB;YACvB,IAAI,CAAC,YAAY;gBACf,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAC3C,OAAO;wBACL,UAAU;wBACV,SAAS;wBACT,IAAI;4BACF;gCAAE,eAAe;4BAAK;4BACtB;gCAAE,sBAAsB;4BAAK;yBAC9B;oBACH;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,CAAC,YAAY;gBACf,aAAa,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAC3C,OAAO;wBACL,SAAS;wBACT,IAAI;4BACF;gCAAE,eAAe;4BAAK;4BACtB;gCAAE,sBAAsB;4BAAK;yBAC9B;oBACH;gBACF;YACF;QACF;QAEA,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,WAAW,QAAQ,CAAC,CAAC,CAAC;QAElE,eAAe;QACf,IAAI,kBAAkB,QAAQ,MAAM;QACpC,MAAM,iBAAiB,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,UAAU;gBACV,SAAS;YACX;QACF;QAEA,IAAI,gBAAgB;YAClB,IAAI;gBACF,MAAM,iBAAiB,IAAI,kHAAA,CAAA,iBAAc,CAAC;gBAC1C,MAAM,qBAAqB,CAAC;;KAE/B,EAAE,QAAQ,KAAK,CAAC;KAChB,EAAE,QAAQ,WAAW,CAAC;MACrB,EAAE,QAAQ,MAAM,CAAC;;;;;;;;QAQf,CAAC;gBAED,kBAAkB,MAAM,eAAe,OAAO,CAAC,oBAAoB;gBACnE,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,oBAAoB,MAAM,OAAO;YAChD;QACF;QAEA,eAAe;QACf,MAAM,cAAc,MAAM,aAAa,YAAY,gBAAgB,IAAI,IAAI;QAE3E,OAAO;YACL,UAAU,YAAY,QAAQ;YAC9B,cAAc,YAAY,YAAY;YACtC,iBAAiB,gBAAgB,IAAI;YACrC,UAAU,YAAY,QAAQ,IAAI,QAAQ,QAAQ,IAAI;YACtD,QAAQ,YAAY,MAAM,IAAI;QAChC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO;YACL,UAAU;YACV,cAAc;YACd,iBAAiB,QAAQ,MAAM;YAC/B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ;YACR,OAAO,MAAM,OAAO;QACtB;IACF;AACF;AAEA,gBAAgB;AAChB,eAAe,aAAa,UAAe,EAAE,MAAc,EAAE,OAAY;IACvE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,QAAQ,CAAC,KAAK,CAAC;QAE9C,mBAAmB;QACnB,IAAI,WAAW,QAAQ,KAAK,UAAU;YACpC,OAAO,MAAM,6BAA6B,YAAY,QAAQ;QAChE,OAAO,IAAI,WAAW,QAAQ,KAAK,WAAW;YAC5C,OAAO,MAAM,8BAA8B,YAAY,QAAQ;QACjE,OAAO,IAAI,WAAW,QAAQ,KAAK,UAAU;YAC3C,OAAO,MAAM,6BAA6B,YAAY,QAAQ;QAChE,OAAO;YACL,iBAAiB;YACjB,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,WAAW,QAAQ,CAAC,UAAU,CAAC;YACnD,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM;IACR;AACF;AAEA,eAAe;AACf,eAAe,6BAA6B,MAAW,EAAE,MAAc,EAAE,OAAY;IACnF,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE;QAEtD,SAAS;QACT,MAAM,UAAU;YACd;gBACE,MAAM;gBACN,MAAM;YACR;SACD;QAED,WAAW;QACX,MAAM,WAAW,MAAM,MAAM,uEAAuE;YAClG,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC1C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK;gBACnB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YAC1D,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;QAC7D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,YAAY;QAExB,6BAA6B;QAC7B,MAAM,SAAS,OAAO,EAAE,IAAI,OAAO,OAAO;QAC1C,IAAI,QAAQ;YACV,WAAW;YACX,MAAM,cAAc,MAAM,qBAAqB,OAAO,MAAM,EAAE,QAAQ;YACtE,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM;IACR;AACF;AAEA,WAAW;AACX,eAAe,qBAAqB,MAAc,EAAE,MAAc,EAAE,OAAY;IAC9E,MAAM,cAAc,GAAG,gBAAgB;;IACvC,MAAM,eAAe,MAAM,UAAU;;IAErC,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,YAAY,GAAG,EAAE,QAAQ;YAEhE,MAAM,WAAW,MAAM,MAAM,CAAC,oEAAoE,EAAE,QAAQ,EAAE;gBAC5G,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ;oBACnC,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;YAC5C;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,MAAM,EAAE;YAEvC,eAAe;YACf,IAAI,OAAO,MAAM,KAAK,eAAe,OAAO,OAAO,EAAE,WAAW;gBAC9D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,SAAS,EAAE;gBACrD,OAAO;oBACL,UAAU,OAAO,OAAO,CAAC,SAAS;oBAClC,cAAc,OAAO,OAAO,CAAC,aAAa,IAAI;oBAC9C,UAAU,QAAQ,QAAQ,IAAI;oBAC9B,QAAQ;gBACV;YACF,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU;gBACrC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,OAAO,KAAK,IAAI,QAAQ;YACvD;YAEA,kBAAkB;YAClB,IAAI,UAAU,aAAa;gBACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,QAAQ,IAAI,CAAC,EAAE;YACnC,IAAI,YAAY,aAAa;gBAC3B,MAAM;YACR;QACF;IACF;IAEA,MAAM,IAAI,MAAM;AAClB;AAEA,yBAAyB;AACzB,eAAe,8BAA8B,MAAW,EAAE,MAAc,EAAE,OAAY;IACpF,QAAQ,IAAI,CAAC;IACb,OAAO;QACL,UAAU;QACV,cAAc;QACd,UAAU,QAAQ,QAAQ,IAAI;QAC9B,QAAQ;IACV;AACF;AAEA,sBAAsB;AACtB,eAAe,6BAA6B,MAAW,EAAE,MAAc,EAAE,OAAY;IACnF,QAAQ,IAAI,CAAC;IACb,OAAO;QACL,UAAU;QACV,cAAc;QACd,UAAU,QAAQ,QAAQ,IAAI;QAC9B,QAAQ;IACV;AACF", "debugId": null}}]}