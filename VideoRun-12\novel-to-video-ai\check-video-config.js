const { PrismaClient } = require('@prisma/client');

async function checkVideoConfig() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 检查AI配置...');
    
    // 获取所有AI配置
    const configs = await prisma.aIConfig.findMany();
    
    console.log('\n📋 当前AI配置:');
    configs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.name}`);
      console.log(`   提供商: ${config.provider}`);
      console.log(`   模型: ${config.model}`);
      console.log(`   支持视频: ${config.supportsVideo ? '✅ 是' : '❌ 否'}`);
      console.log(`   API密钥: ${config.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
      console.log('');
    });
    
    // 检查支持视频的配置
    const videoConfigs = configs.filter(c => c.supportsVideo);
    console.log(`🎬 支持视频生成的配置数量: ${videoConfigs.length}`);
    
    if (videoConfigs.length === 0) {
      console.log('\n❌ 问题发现: 没有配置支持视频生成的AI模型！');
      console.log('\n💡 解决方案:');
      console.log('1. 前往 AI配置 页面');
      console.log('2. 添加支持视频生成的模型，如:');
      console.log('   - 通义万相 (wanx-v1)');
      console.log('   - MiniMax (video-01)');
      console.log('   - 豆包 (doubao-seedance-1.0-pro)');
      console.log('3. 确保API密钥正确配置');
    } else {
      console.log('\n✅ 视频配置正常');
      videoConfigs.forEach(config => {
        console.log(`   - ${config.name}: ${config.model}`);
      });
    }
    
    // 检查最近的视频生成记录
    console.log('\n📊 检查最近的视频生成记录...');
    const recentSegments = await prisma.videoSegment.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        storyVideo: {
          include: {
            episode: {
              select: { title: true }
            }
          }
        }
      }
    });
    
    if (recentSegments.length > 0) {
      console.log('最近的视频片段:');
      recentSegments.forEach(segment => {
        console.log(`   - ${segment.storyVideo?.episode?.title || '未知剧集'}: ${segment.status}`);
        if (segment.status === 'failed') {
          try {
            const metadata = JSON.parse(segment.metadata || '{}');
            console.log(`     错误: ${metadata.error || '未知错误'}`);
          } catch (e) {
            console.log(`     错误信息解析失败`);
          }
        }
      });
    } else {
      console.log('   没有视频生成记录');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkVideoConfig();
