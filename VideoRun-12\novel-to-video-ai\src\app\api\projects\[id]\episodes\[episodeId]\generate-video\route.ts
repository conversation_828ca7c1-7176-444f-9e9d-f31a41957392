import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 视频生成API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; episodeId: string }> }
) {
  try {
    const { id: projectId, episodeId } = await params
    const body = await request.json()
    const { modelId } = body

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: '请选择视频生成模型' },
        { status: 400 }
      )
    }

    // 获取项目信息
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      )
    }

    // 获取剧集信息
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId },
      include: {
        plotInfo: true
      }
    })

    if (!episode) {
      return NextResponse.json(
        { success: false, error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 获取AI配置
    const aiConfig = await prisma.aIConfig.findUnique({
      where: { id: modelId }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { success: false, error: '指定的模型配置不存在' },
        { status: 400 }
      )
    }

    if (!aiConfig.enabled) {
      return NextResponse.json(
        { success: false, error: '指定的模型未启用' },
        { status: 400 }
      )
    }

    // 检查是否为视频生成模型
    if (!aiConfig.supportsVideo) {
      return NextResponse.json(
        { success: false, error: '请选择支持视频生成的模型' },
        { status: 400 }
      )
    }

    // 构建视频生成提示词
    let prompt = `根据以下剧集内容生成视频：\n\n标题：${episode.title}\n\n内容：${episode.content}`
    
    // 如果有剧情信息，添加到提示词中
    if (episode.plotInfo) {
      prompt += `\n\n剧情信息：`
      if (episode.plotInfo.characters) {
        prompt += `\n角色：${episode.plotInfo.characters}`
      }
      if (episode.plotInfo.scenes) {
        prompt += `\n场景：${episode.plotInfo.scenes}`
      }
      if (episode.plotInfo.events) {
        prompt += `\n事件：${episode.plotInfo.events}`
      }
    }

    try {
      // 根据提供商调用相应的视频生成API
      let videoResult
      if (aiConfig.provider === 'doubao') {
        videoResult = await callDoubaoVideo(aiConfig.apiKey, aiConfig.model, prompt, episode)
      } else if (aiConfig.provider === 'tongyi') {
        videoResult = await callTongyiVideo(aiConfig.apiKey, aiConfig.model, prompt, episode)
      } else {
        throw new Error(`不支持的视频生成提供商: ${aiConfig.provider}`)
      }

      // 更新剧集状态
      await prisma.episode.update({
        where: { id: episodeId },
        data: {
          status: 'video_generated',
          updatedAt: new Date()
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          videoUrl: videoResult.videoUrl,
          taskId: videoResult.taskId,
          status: videoResult.status
        },
        message: '视频生成成功'
      })

    } catch (error) {
      console.error('视频生成失败:', error)
      return NextResponse.json(
        { success: false, error: '视频生成失败，请稍后重试' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('视频生成API失败:', error)
    return NextResponse.json(
      { success: false, error: '视频生成失败' },
      { status: 500 }
    )
  }
}

// 豆包视频生成API调用
async function callDoubaoVideo(apiKey: string, model: string, prompt: string, episode?: any) {
  try {
    console.log('🎬 调用豆包视频生成API...')

    // 从剧情分析结果中获取时长
    let duration = 5 // 默认5秒
    if (episode?.plotInfo?.plotSequences) {
      try {
        const plotSequences = JSON.parse(episode.plotInfo.plotSequences)
        if (plotSequences && plotSequences.length > 0) {
          // 使用第一个序列的时长，或计算总时长
          const firstSequence = plotSequences[0]
          if (firstSequence.duration) {
            const match = firstSequence.duration.match(/(\d+)/)
            duration = match ? parseInt(match[1]) : 5
          }
        }
      } catch (e) {
        console.warn('解析剧情序列时长失败，使用默认时长')
      }
    }

    console.log(`📏 使用视频时长: ${duration}秒`)

    // 创建视频生成任务
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        content: [
          {
            type: "text",
            text: `${prompt} --ratio 16:9 --fps 24 --dur ${duration} --resolution 720p`
          }
        ]
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`豆包API调用失败: ${response.status} ${errorText}`)
      throw new Error(`豆包视频生成失败: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('豆包API响应:', JSON.stringify(result, null, 2))

    // 获取任务ID - 支持多种格式
    const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id
    if (!taskId) {
      console.error('豆包API响应中未找到任务ID，完整响应:', result)
      throw new Error('豆包视频任务创建失败：未获取到任务ID')
    }

    console.log(`豆包视频任务创建成功，任务ID: ${taskId}`)

    // 轮询任务状态
    const videoResult = await pollDoubaoVideoTask(apiKey, taskId)

    return videoResult

  } catch (error) {
    console.error('豆包视频生成失败:', error)
    throw error
  }
}

// 轮询豆包视频任务状态
async function pollDoubaoVideoTask(apiKey: string, taskId: string, maxAttempts: number = 60) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        console.error(`轮询请求失败: ${response.status} ${response.statusText}`)
        await new Promise(resolve => setTimeout(resolve, 10000))
        continue
      }

      const result = await response.json()
      // 豆包API实际响应格式：{ "id": "xxx", "status": "succeeded", "content": { "video_url": "xxx" } }
      const status = result.status

      console.log(`豆包视频任务状态: ${status} (${attempt}/${maxAttempts})`)

      if (status === 'succeeded' || status === 'completed') {
        const videoUrl = result.content?.video_url || result.video_url
        if (videoUrl) {
          console.log('豆包视频生成成功:', videoUrl)
          return {
            videoUrl,
            taskId,
            status: 'SUCCEEDED',
            duration: result.duration || 5
          }
        } else {
          throw new Error('视频生成成功但未获取到视频URL')
        }
      } else if (status === 'failed' || status === 'error') {
        const errorMessage = result.message || result.error || '任务失败'
        throw new Error(`豆包视频生成失败: ${errorMessage}`)
      } else if (status === 'pending' || status === 'running' || status === 'processing') {
        // 继续等待
        await new Promise(resolve => setTimeout(resolve, 10000))
      } else {
        throw new Error(`未知的任务状态: ${status}`)
      }
    } catch (error) {
      if (attempt === maxAttempts) {
        throw new Error('豆包视频生成超时，请稍后重试')
      }
      console.error(`查询豆包任务状态失败 (${attempt}/${maxAttempts}):`, error)
      await new Promise(resolve => setTimeout(resolve, 5000))
    }
  }

  throw new Error('豆包视频生成超时')
}

// 通义万相视频生成API调用
async function callTongyiVideo(apiKey: string, model: string, prompt: string, episode?: any) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 600000) // 10分钟超时

  try {
    // 从剧情分析结果中获取时长
    let duration = 6.0 // 默认6秒
    if (episode?.plotInfo?.plotSequences) {
      try {
        const plotSequences = JSON.parse(episode.plotInfo.plotSequences)
        if (plotSequences && plotSequences.length > 0) {
          const firstSequence = plotSequences[0]
          if (firstSequence.duration) {
            const match = firstSequence.duration.match(/(\d+)/)
            duration = match ? parseFloat(match[1]) : 6.0
          }
        }
      } catch (e) {
        console.warn('解析剧情序列时长失败，使用默认时长')
      }
    }

    console.log(`📏 通义万相使用视频时长: ${duration}秒`)

    // 创建视频生成任务
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text2video/generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'X-DashScope-Async': 'enable'
      },
      body: JSON.stringify({
        model: model,
        input: {
          text: prompt
        },
        parameters: {
          resolution: '1280x720',
          duration: duration
        }
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`通义万相视频生成失败: ${errorData.message || response.statusText}`)
    }

    const data = await response.json()
    const taskId = data.output?.task_id

    if (!taskId) {
      throw new Error('视频生成任务创建失败')
    }

    // 轮询任务状态
    const videoResult = await pollVideoTask(apiKey, taskId)
    
    return videoResult

  } catch (error) {
    clearTimeout(timeoutId)
    if (error.name === 'AbortError') {
      throw new Error('视频生成超时，请稍后重试')
    }
    throw error
  }
}

// 轮询视频生成任务状态
async function pollVideoTask(apiKey: string, taskId: string, maxAttempts: number = 60) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      })

      if (!response.ok) {
        throw new Error(`查询任务状态失败: ${response.statusText}`)
      }

      const data = await response.json()
      const status = data.output?.task_status

      console.log(`通义万相视频任务状态: ${status} (${attempt}/${maxAttempts})`)

      if (status === 'SUCCEEDED') {
        const videoUrl = data.output?.results?.[0]?.url
        if (videoUrl) {
          console.log('通义万相视频生成成功:', videoUrl)
          return {
            videoUrl,
            taskId,
            status: 'SUCCEEDED'
          }
        } else {
          throw new Error('视频生成成功但未获取到视频URL')
        }
      } else if (status === 'FAILED') {
        throw new Error('视频生成失败')
      } else if (status === 'RUNNING' || status === 'PENDING') {
        // 继续等待
        await new Promise(resolve => setTimeout(resolve, 10000)) // 等待10秒
      } else {
        throw new Error(`未知的任务状态: ${status}`)
      }
    } catch (error) {
      if (attempt === maxAttempts) {
        throw new Error('视频生成超时，请稍后重试')
      }
      console.error(`查询任务状态失败 (${attempt}/${maxAttempts}):`, error)
      await new Promise(resolve => setTimeout(resolve, 5000)) // 等待5秒后重试
    }
  }

  throw new Error('视频生成超时')
}
