import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const { episodeId, customPrompt } = await request.json()

    if (!episodeId) {
      return NextResponse.json({
        success: false,
        error: '缺少剧集ID'
      }, { status: 400 })
    }

    // 确保数据目录存在
    const dataDir = path.join(process.cwd(), 'data', 'custom-prompts')
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
    }

    // 保存自定义提示词到文件
    const filePath = path.join(dataDir, `${episodeId}.json`)
    const data = {
      episodeId,
      customPrompt,
      savedAt: new Date().toISOString()
    }

    fs.writeFileSync(filePath, JSON.stringify(data, null, 2))

    return NextResponse.json({
      success: true,
      message: '自定义提示词保存成功'
    })

  } catch (error) {
    console.error('保存自定义提示词失败:', error)
    return NextResponse.json({
      success: false,
      error: '保存失败'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')

    if (!episodeId) {
      return NextResponse.json({
        success: false,
        error: '缺少剧集ID'
      }, { status: 400 })
    }

    const filePath = path.join(process.cwd(), 'data', 'custom-prompts', `${episodeId}.json`)
    
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({
        success: true,
        data: {
          customPrompt: ''
        }
      })
    }

    const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
    
    return NextResponse.json({
      success: true,
      data: {
        customPrompt: data.customPrompt || ''
      }
    })

  } catch (error) {
    console.error('读取自定义提示词失败:', error)
    return NextResponse.json({
      success: false,
      error: '读取失败'
    }, { status: 500 })
  }
}
