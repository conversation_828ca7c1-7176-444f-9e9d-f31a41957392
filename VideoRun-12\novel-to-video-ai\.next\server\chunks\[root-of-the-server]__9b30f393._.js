module.exports = {

"[project]/.next-internal/server/app/api/ai/generate-story-video/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/skyreels.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SkyReelsClient": (()=>SkyReelsClient),
    "SkyReelsServiceError": (()=>SkyReelsServiceError)
});
class SkyReelsClient {
    baseUrl;
    apiKey;
    model;
    constructor(config){
        // SkyReels是本地API，apiKey用作baseUrl
        this.baseUrl = config.apiKey || 'http://localhost:8000';
        this.apiKey = config.apiKey;
        this.model = config.model || 'SkyReels-V2-DF-1.3B-540P';
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            return data.status === 'healthy' && data.model_loaded;
        } catch (error) {
            console.error('SkyReels连接测试失败:', error);
            return false;
        }
    }
    // 生成视频
    async generateVideo(params) {
        try {
            const response = await fetch(`${this.baseUrl}/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: params.prompt,
                    num_frames: params.num_frames || 257,
                    guidance_scale: params.guidance_scale || 6.0,
                    seed: params.seed,
                    fps: params.fps || 24,
                    resolution: params.resolution || '540P'
                })
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels视频生成失败:', error);
            throw new Error(`视频生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 查询任务状态
    async getTaskStatus(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/status/${taskId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('SkyReels状态查询失败:', error);
            throw new Error(`状态查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 下载视频
    async downloadVideo(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/download/${taskId}`, {
                method: 'GET'
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            return await response.blob();
        } catch (error) {
            console.error('SkyReels视频下载失败:', error);
            throw new Error(`视频下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    // 等待任务完成并返回视频URL
    async generateAndWait(params, maxWaitTime = 1800000, pollInterval = 5000 // 5秒
    ) {
        console.log('🎬 开始SkyReels视频生成...');
        // 开始生成
        const task = await this.generateVideo(params);
        console.log(`📝 任务已创建: ${task.task_id}`);
        const startTime = Date.now();
        // 轮询状态直到完成
        while(Date.now() - startTime < maxWaitTime){
            const status = await this.getTaskStatus(task.task_id);
            console.log(`📊 任务状态: ${status.status}, 进度: ${(status.progress * 100).toFixed(1)}%`);
            if (status.status === 'completed') {
                console.log('✅ 视频生成完成!');
                return status.video_path || '';
            } else if (status.status === 'failed') {
                throw new Error(`视频生成失败: ${status.error || '未知错误'}`);
            }
            // 等待下次轮询
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        }
        throw new Error('视频生成超时');
    }
    // 获取所有任务列表
    async getTasks() {
        try {
            const response = await fetch(`${this.baseUrl}/tasks`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            const data = await response.json();
            return data.tasks || [];
        } catch (error) {
            console.error('SkyReels任务列表获取失败:', error);
            throw new Error(`任务列表获取失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
class SkyReelsServiceError extends Error {
    statusCode;
    constructor(message, statusCode){
        super(message), this.statusCode = statusCode;
        this.name = 'SkyReelsServiceError';
    }
}
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIServiceError": (()=>AIServiceError),
    "DeepSeekClient": (()=>DeepSeekClient),
    "DoubaoClient": (()=>DoubaoClient),
    "createAIClient": (()=>createAIClient),
    "handleAIRequest": (()=>handleAIRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/skyreels.ts [app-route] (ecmascript)");
;
class AIServiceError extends Error {
    code;
    statusCode;
    constructor(message, code, statusCode = 500){
        super(message), this.code = code, this.statusCode = statusCode;
        this.name = 'AIServiceError';
    }
}
class DeepSeekClient {
    apiKey;
    model;
    baseUrl = 'https://api.deepseek.com/v1/chat/completions';
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model;
    }
    // 测试API连接
    async testConnection() {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: '测试连接'
                        }
                    ],
                    max_tokens: 10
                })
            });
            return response.ok;
        } catch (error) {
            console.error('DeepSeek连接测试失败:', error);
            return false;
        }
    }
    // 调用AI API的通用方法（公开方法）
    async callAPI(prompt, maxTokens = 4000) {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: maxTokens,
                    temperature: 0.7
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new AIServiceError(errorData.error?.message || 'API调用失败', 'API_ERROR', response.status);
            }
            const data = await response.json();
            return data.choices[0]?.message?.content || '';
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('AI服务调用失败，请检查网络连接', 'NETWORK_ERROR', 500);
        }
    }
    // 生成测试响应
    generateTestResponse(prompt) {
        if (prompt.includes('角色信息') && prompt.includes('一致性约束')) {
            // 角色一致性分析的测试响应
            return `{
        "characters": [
          {
            "name": "张小雅",
            "identity": "高中生",
            "appearance": "马尾辫，甜美笑容，青春活泼",
            "personality": "开朗活泼，善良纯真",
            "clothing": "校服或休闲装",
            "role": "主要角色",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "zhang_xiaoya",
              "consistencyMatch": 0.95,
              "differences": [],
              "consistencyConstraints": "保持马尾辫和甜美笑容的标志性特征"
            }
          },
          {
            "name": "王美丽",
            "identity": "咖啡店老板",
            "appearance": "瓜子脸，波浪卷发，温柔气质",
            "personality": "温柔善良，热情好客",
            "clothing": "简约优雅的服装",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "wang_meili",
              "consistencyMatch": 0.88,
              "differences": [],
              "consistencyConstraints": "保持瓜子脸和波浪卷发的特征"
            }
          },
          {
            "name": "李明轩",
            "identity": "大学教授",
            "appearance": "方脸，花白短发，学者气质",
            "personality": "温文尔雅，博学睿智",
            "clothing": "正式的学者装扮",
            "role": "重要配角",
            "isKnownCharacter": true,
            "consistencyInfo": {
              "matchedCharacterId": "li_mingxuan",
              "consistencyMatch": 0.92,
              "differences": [],
              "consistencyConstraints": "保持方脸和花白短发的学者形象"
            }
          },
          {
            "name": "林志强",
            "identity": "程序员",
            "appearance": "高瘦身材，黑框眼镜，简约穿着",
            "personality": "内向专注，技术宅",
            "clothing": "白色T恤，牛仔裤",
            "role": "新角色",
            "isKnownCharacter": false,
            "consistencyInfo": {
              "matchedCharacterId": null,
              "consistencyMatch": 0.0,
              "differences": ["新出现的角色"],
              "consistencyConstraints": "建立新的角色DNA档案"
            }
          }
        ]
      }`;
        } else if (prompt.includes('场景信息')) {
            return `{
        "scenes": [
          {
            "location": "温馨咖啡店",
            "description": "装修温馨的小型咖啡店，木质桌椅，暖色调灯光",
            "atmosphere": "温馨舒适，充满生活气息",
            "timeOfDay": "下午",
            "lighting": "暖色调室内灯光",
            "keyElements": "咖啡香味，轻柔音乐，温馨装饰"
          }
        ]
      }`;
        } else if (prompt.includes('情节序列')) {
            return `{
        "plotSequences": [
          {
            "sequenceId": "reunion_1",
            "action": "张小雅进入咖啡店与王美丽重逢",
            "emotion": "惊喜和温暖",
            "duration": "2分钟",
            "keyMoments": ["进门", "相认", "拥抱"],
            "visualElements": "特写表情变化，温馨的重逢场面"
          },
          {
            "sequenceId": "professor_arrival",
            "action": "李明轩教授进入咖啡店",
            "emotion": "温和友善",
            "duration": "1分钟",
            "keyMoments": ["进门", "打招呼"],
            "visualElements": "学者风度，温和笑容"
          },
          {
            "sequenceId": "new_character",
            "action": "林志强询问WiFi",
            "emotion": "略显紧张的初次见面",
            "duration": "1分钟",
            "keyMoments": ["进门", "询问", "介绍"],
            "visualElements": "新角色登场，技术宅形象"
          }
        ]
      }`;
        } else {
            return '平静专注 → 遇到熟人 → 温馨重逢 → 新朋友加入 → 和谐融洽';
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析AI响应失败:', error);
            throw new AIServiceError('AI响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析剧情分析响应失败:', error);
            throw new AIServiceError('剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
class DoubaoClient {
    apiKey;
    model;
    baseUrl;
    isVideoModel;
    constructor(config){
        this.apiKey = config.apiKey;
        this.model = config.model || 'doubao-seedance-1.0-pro';
        // 检查是否为视频模型：包含seedance、video关键词，或者是豆包视频生成的endpoint ID
        this.isVideoModel = this.model.includes('seedance') || this.model.includes('video') || this.model.startsWith('ep-') // 豆包视频生成的endpoint ID格式
        ;
        // 根据模型类型选择正确的API端点
        if (this.isVideoModel) {
            // 豆包视频生成使用专门的视频生成API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks';
        } else {
            // 文本模型使用chat completions API
            this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
        }
        if (config.baseUrl) {
            this.baseUrl = config.baseUrl;
        }
    }
    // 测试API连接（带重试机制）
    async testConnection() {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: '测试连接'
                            }
                        ],
                        max_tokens: 10
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    return true;
                }
                // 检查是否是可重试的错误
                const errorText = await response.text();
                if (errorText.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            } catch (error) {
                console.error(`豆包连接测试失败 (尝试 ${attempt}/${maxRetries}):`, error);
                if (attempt < maxRetries) {
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                return false;
            }
        }
        return false;
    }
    // 调用AI API的通用方法（带重试机制）
    async callAPI(prompt, maxTokens = 4000) {
        const maxRetries = 3;
        const retryDelay = 1000 // 1秒
        ;
        for(let attempt = 1; attempt <= maxRetries; attempt++){
            try {
                let requestBody;
                if (this.isVideoModel) {
                    // 豆包视频生成使用官方确认的API格式
                    requestBody = {
                        model: this.model,
                        content: [
                            {
                                type: "text",
                                text: `${prompt} --ratio 16:9 --fps 24 --dur 5 --resolution 720p`
                            }
                        ]
                    };
                } else {
                    // 文本模型使用chat completions格式
                    requestBody = {
                        model: this.model,
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: maxTokens,
                        temperature: 0.7
                    };
                }
                const response = await fetch(this.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                if (response.ok) {
                    const data = await response.json();
                    if (this.isVideoModel) {
                        // 视频生成返回任务信息
                        return JSON.stringify({
                            task_id: data.task_id,
                            status: data.status || 'submitted',
                            message: '视频生成任务已提交，请稍后查询结果'
                        });
                    } else {
                        // 文本生成返回内容
                        return data.choices[0]?.message?.content || '';
                    }
                }
                const errorData = await response.json();
                const errorMessage = errorData.error?.message || '豆包API调用失败';
                // 检查是否是可重试的内部错误
                if (errorMessage.includes('internal error') && attempt < maxRetries) {
                    console.log(`豆包API内部错误，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                // 不可重试的错误，直接抛出
                throw new AIServiceError(errorMessage, 'API_ERROR', response.status);
            } catch (error) {
                if (error instanceof AIServiceError) {
                    // 如果是已知的API错误且不可重试，直接抛出
                    throw error;
                }
                // 网络错误等，可以重试
                if (attempt < maxRetries) {
                    console.log(`豆包API调用失败，第${attempt}次重试...`);
                    await new Promise((resolve)=>setTimeout(resolve, retryDelay));
                    continue;
                }
                throw new AIServiceError('豆包服务调用失败，请检查网络连接和API密钥', 'NETWORK_ERROR', 500);
            }
        }
        throw new AIServiceError('豆包服务调用失败，已达到最大重试次数', 'MAX_RETRIES_EXCEEDED', 500);
    }
    // 专门的视频生成方法
    async generateVideo(prompt, duration = 5) {
        if (!this.isVideoModel) {
            throw new Error('此模型不支持视频生成');
        }
        try {
            const requestBody = {
                model: this.model,
                prompt: prompt,
                video_setting: {
                    video_duration: duration,
                    video_aspect_ratio: '16:9',
                    video_resolution: '720p'
                }
            };
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            if (response.ok) {
                const data = await response.json();
                return JSON.stringify({
                    task_id: data.task_id,
                    status: data.status || 'submitted',
                    message: '视频生成任务已提交，请稍后查询结果'
                });
            }
            const errorData = await response.json();
            throw new AIServiceError(errorData.error?.message || '视频生成失败', 'VIDEO_GENERATION_ERROR', response.status);
        } catch (error) {
            if (error instanceof AIServiceError) {
                throw error;
            }
            throw new AIServiceError('视频生成服务调用失败', 'NETWORK_ERROR', 500);
        }
    }
    // 分析小说，提取角色和剧集信息
    async analyzeNovel(content, customPrompt) {
        const prompt = this.buildAnalysisPrompt(content, customPrompt);
        const response = await this.callAPI(prompt, 8000);
        try {
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('解析豆包响应失败:', error);
            throw new AIServiceError('豆包响应解析失败，请重试', 'PARSE_ERROR', 500);
        }
    }
    // 分析单个剧集的剧情信息
    async analyzePlot(episodeContent) {
        const prompt = this.buildPlotAnalysisPrompt(episodeContent);
        const response = await this.callAPI(prompt, 4000);
        try {
            return this.parsePlotResponse(response);
        } catch (error) {
            console.error('解析豆包剧情分析响应失败:', error);
            throw new AIServiceError('豆包剧情分析失败，请重试', 'PLOT_PARSE_ERROR', 500);
        }
    }
    // 构建小说分析提示词
    buildAnalysisPrompt(content, customPrompt) {
        let basePrompt = `请分析以下小说文本，同时完成两个任务：`;
        if (customPrompt && customPrompt.trim()) {
            basePrompt += `\n\n增强要求：${customPrompt}\n`;
        }
        return basePrompt + `

任务1：提取所有主要角色信息
任务2：按章节拆分成独立剧集

要求：
1. 角色信息包括：姓名、外貌描述（五官、身体特征、服装）、身份、性格、隐线伏笔
2. 剧集按原文章节结构拆分，每个剧集包含完整故事情节
3. 严格按照以下JSON格式返回：

{
  "characters": [
    {
      "name": "角色姓名",
      "appearance": {
        "face": "五官描述",
        "body": "身体特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ],
  "episodes": [
    {
      "title": "第X章 标题",
      "content": "完整章节内容",
      "orderIndex": 1
    }
  ]
}

小说文本：
${content.substring(0, 6000)}${content.length > 6000 ? '...' : ''}`;
    }
    // 构建剧情分析提示词
    buildPlotAnalysisPrompt(episodeContent) {
        return `请分析以下剧集内容，提取三大核心信息：

1. 本集人物：当前剧集中出场的所有角色名称
2. 场景信息：故事发生的地点、环境描述、氛围设定
3. 事件三要素：按照"正常→矛盾冲突→升级事件"的结构分析

严格按照以下JSON格式返回：

{
  "characters": ["角色名1", "角色名2"],
  "scenes": [
    {
      "location": "场景地点",
      "description": "环境描述",
      "atmosphere": "氛围设定"
    }
  ],
  "events": [
    {
      "normal": "正常状态描述",
      "conflict": "矛盾冲突描述",
      "escalation": "升级事件描述",
      "participants": ["参与角色"],
      "location": "发生地点",
      "actions": ["具体行为"]
    }
  ]
}

剧集内容：
${episodeContent}`;
    }
    // 解析小说分析响应
    parseAnalysisResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: parsed.characters?.map((char)=>({
                    name: char.name || '',
                    appearance: JSON.stringify(char.appearance || {}),
                    identity: char.identity || '',
                    personality: char.personality || '',
                    hiddenLines: char.hiddenLines || ''
                })) || [],
            episodes: parsed.episodes?.map((ep, index)=>({
                    title: ep.title || `第${index + 1}章`,
                    content: ep.content || '',
                    orderIndex: ep.orderIndex || index + 1,
                    status: 'created'
                })) || []
        };
    }
    // 解析剧情分析响应
    parsePlotResponse(response) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const parsed = JSON.parse(jsonMatch[0]);
        return {
            characters: JSON.stringify(parsed.characters || []),
            scenes: JSON.stringify(parsed.scenes || []),
            events: JSON.stringify(parsed.events || [])
        };
    }
}
function createAIClient(config) {
    switch(config.provider){
        case 'deepseek':
            return new DeepSeekClient(config);
        case 'doubao':
            return new DoubaoClient(config);
        case 'skyreels':
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$skyreels$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SkyReelsClient"](config);
        default:
            // 默认使用DeepSeek客户端，但可以扩展支持其他提供商
            return new DeepSeekClient(config);
    }
}
async function handleAIRequest(request) {
    try {
        return await request();
    } catch (error) {
        if (error instanceof AIServiceError) {
            throw error;
        }
        // 网络错误
        if (error instanceof TypeError && error.message.includes('fetch')) {
            throw new AIServiceError('AI服务连接失败，请检查网络连接', 'CONNECTION_ERROR', 503);
        }
        // 通用错误
        throw new AIServiceError('AI服务处理失败，请重试', 'UNKNOWN_ERROR', 500);
    }
}
}}),
"[project]/src/utils/smartModelSelector.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 智能模型选择器 - 根据片段类型和内容自动选择最适合的AI模型
__turbopack_context__.s({
    "SmartModelSelector": (()=>SmartModelSelector)
});
class SmartModelSelector {
    models;
    constructor(models){
        this.models = models.filter((m)=>m.enabled && m.apiKey);
    }
    /**
   * 智能选择最适合的模型
   */ selectBestModel(segmentInfo) {
        const availableModels = this.getAvailableModels();
        if (availableModels.length === 0) {
            throw new Error('没有可用的视频生成模型');
        }
        // 计算每个模型的适配分数
        const modelScores = availableModels.map((model)=>({
                model,
                score: this.calculateModelScore(model, segmentInfo),
                reason: this.getSelectionReason(model, segmentInfo)
            }));
        // 按分数排序
        modelScores.sort((a, b)=>b.score - a.score);
        const best = modelScores[0];
        const alternatives = modelScores.slice(1, 3).map((item)=>item.model);
        return {
            selectedModel: best.model,
            reason: best.reason,
            confidence: best.score,
            alternatives
        };
    }
    /**
   * 获取可用的视频生成模型
   */ getAvailableModels() {
        return this.models.filter((model)=>model.supportsVideo || model.supportsImageToVideo);
    }
    /**
   * 计算模型适配分数 (0-100)
   */ calculateModelScore(model, segmentInfo) {
        let score = 50 // 基础分数
        ;
        // 1. 模型类型适配性
        if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {
            score += 30 // 图生视频模型 + 有角色图像 = 高分
            ;
        } else if (model.supportsVideo && !segmentInfo.hasCharacterImages) {
            score += 20 // 文生视频模型 + 无角色图像 = 中等分
            ;
        } else if (model.supportsVideo) {
            score += 10 // 文生视频模型作为备选
            ;
        }
        // 2. 片段类型适配性
        score += this.getSegmentTypeScore(model, segmentInfo.type);
        // 3. 角色数量适配性
        if (segmentInfo.characterCount > 0 && model.supportsImageToVideo) {
            score += Math.min(segmentInfo.characterCount * 5, 15);
        }
        // 4. 场景复杂度适配性
        score += this.getComplexityScore(model, segmentInfo.sceneComplexity);
        // 5. 情感强度适配性
        score += this.getEmotionalScore(model, segmentInfo.emotionalIntensity);
        // 6. 特定模型优势
        score += this.getModelSpecificBonus(model, segmentInfo);
        return Math.min(Math.max(score, 0), 100);
    }
    /**
   * 根据片段类型计算分数
   */ getSegmentTypeScore(model, segmentType) {
        const typeScores = {
            'environment': {
                'text-to-video': 15,
                'image-to-video': 5
            },
            'character': {
                'text-to-video': 5,
                'image-to-video': 20 // 角色引入适合图生视频
            },
            'action': {
                'text-to-video': 10,
                'image-to-video': 15 // 动作场景图生视频略好
            },
            'emotion': {
                'text-to-video': 5,
                'image-to-video': 25 // 情感表达强烈推荐图生视频
            },
            'dialogue': {
                'text-to-video': 8,
                'image-to-video': 18 // 对话场景推荐图生视频
            },
            'suspense': {
                'text-to-video': 12,
                'image-to-video': 8 // 悬念场景文生视频更有创意
            }
        };
        const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video';
        return typeScores[segmentType]?.[modelType] || 0;
    }
    /**
   * 根据场景复杂度计算分数
   */ getComplexityScore(model, complexity) {
        const complexityScores = {
            'low': {
                'image-to-video': 10,
                'text-to-video': 5
            },
            'medium': {
                'image-to-video': 8,
                'text-to-video': 8 // 中等复杂度两者相当
            },
            'high': {
                'image-to-video': 5,
                'text-to-video': 12 // 复杂场景文生视频更灵活
            }
        };
        const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video';
        return complexityScores[complexity]?.[modelType] || 0;
    }
    /**
   * 根据情感强度计算分数
   */ getEmotionalScore(model, intensity) {
        const emotionalScores = {
            'low': {
                'image-to-video': 5,
                'text-to-video': 8
            },
            'medium': {
                'image-to-video': 10,
                'text-to-video': 6
            },
            'high': {
                'image-to-video': 15,
                'text-to-video': 3
            }
        };
        const modelType = model.supportsImageToVideo ? 'image-to-video' : 'text-to-video';
        return emotionalScores[intensity]?.[modelType] || 0;
    }
    /**
   * 特定模型的额外加分
   */ getModelSpecificBonus(model, segmentInfo) {
        let bonus = 0;
        // 豆包模型特定优势
        if (model.provider === 'doubao') {
            if (model.model === 'ep-20250624013749-dbrbn') {
                // Seedance 1.0 Lite I2V - 图生视频模型在角色一致性方面的优势
                if (segmentInfo.hasCharacterImages) {
                    bonus += 15;
                }
            } else if (model.model === 'ep-20250624013223-bwdtj') {
                // Seedance 1.0 Lite T2V - 文生视频模型在创意性方面的优势
                if (segmentInfo.sceneComplexity === 'high') {
                    bonus += 12;
                }
            } else if (model.model === 'ep-20250622184757-q77k7') {
                // Seedance 1.0 Pro - 原有文生视频模型
                if (segmentInfo.sceneComplexity === 'high') {
                    bonus += 8;
                }
            }
        }
        return bonus;
    }
    /**
   * 生成选择理由
   */ getSelectionReason(model, segmentInfo) {
        const reasons = [];
        if (model.supportsImageToVideo && segmentInfo.hasCharacterImages) {
            reasons.push('基于角色参考图像生成，确保角色一致性');
        }
        if (segmentInfo.type === 'emotion' && model.supportsImageToVideo) {
            reasons.push('情感表达场景，图生视频效果更佳');
        }
        if (segmentInfo.type === 'environment' && !model.supportsImageToVideo) {
            reasons.push('环境建立场景，文生视频创意性更强');
        }
        if (segmentInfo.characterCount > 1 && model.supportsImageToVideo) {
            reasons.push('多角色场景，图生视频角色识别更准确');
        }
        if (segmentInfo.sceneComplexity === 'high' && !model.supportsImageToVideo) {
            reasons.push('复杂场景，文生视频适应性更好');
        }
        if (model.provider === 'doubao') {
            reasons.push('豆包模型在中文内容理解方面表现优秀');
        }
        return reasons.length > 0 ? reasons.join('；') : '综合评估最适合的模型';
    }
    /**
   * 分析片段信息
   */ static analyzeSegment(segment, characterImages = []) {
        const title = segment.title || '';
        const description = segment.description || '';
        const prompt = segment.prompt || '';
        const text = `${title} ${description} ${prompt}`.toLowerCase();
        // 分析片段类型
        const type = this.detectSegmentType(text);
        // 分析角色数量
        const characterCount = this.countCharacters(text);
        // 分析场景复杂度
        const sceneComplexity = this.analyzeSceneComplexity(text);
        // 分析情感强度
        const emotionalIntensity = this.analyzeEmotionalIntensity(text);
        return {
            type,
            title,
            description,
            prompt,
            hasCharacterImages: characterImages.length > 0,
            characterCount,
            sceneComplexity,
            emotionalIntensity
        };
    }
    /**
   * 检测片段类型
   */ static detectSegmentType(text) {
        const typeKeywords = {
            environment: [
                '环境',
                '场景',
                '建立',
                '雪夜',
                '城楼',
                '关城',
                '山峰'
            ],
            character: [
                '登场',
                '出现',
                '角色',
                '人物',
                '李四',
                '张三'
            ],
            action: [
                '动作',
                '跑',
                '扛',
                '冲',
                '抽刀',
                '战斗',
                '移动'
            ],
            emotion: [
                '表情',
                '情感',
                '内心',
                '思考',
                '回忆',
                '愤怒',
                '悲伤'
            ],
            dialogue: [
                '对话',
                '说',
                '讲述',
                '报告',
                '讨论',
                '交谈'
            ],
            suspense: [
                '悬念',
                '远方',
                '狼烟',
                '威胁',
                '危机',
                '神秘'
            ]
        };
        for (const [type, keywords] of Object.entries(typeKeywords)){
            if (keywords.some((keyword)=>text.includes(keyword))) {
                return type;
            }
        }
        return 'action' // 默认类型
        ;
    }
    /**
   * 统计角色数量
   */ static countCharacters(text) {
        const characterNames = [
            '张三',
            '李四',
            '王五',
            '赵六'
        ];
        return characterNames.filter((name)=>text.includes(name.toLowerCase())).length;
    }
    /**
   * 分析场景复杂度
   */ static analyzeSceneComplexity(text) {
        const complexityIndicators = {
            high: [
                '战斗',
                '追逐',
                '多人',
                '复杂',
                '混乱',
                '激烈'
            ],
            medium: [
                '对话',
                '互动',
                '移动',
                '检查',
                '讨论'
            ],
            low: [
                '站立',
                '观察',
                '思考',
                '静止',
                '单人'
            ]
        };
        for (const [level, indicators] of Object.entries(complexityIndicators)){
            if (indicators.some((indicator)=>text.includes(indicator))) {
                return level;
            }
        }
        return 'medium';
    }
    /**
   * 分析情感强度
   */ static analyzeEmotionalIntensity(text) {
        const intensityIndicators = {
            high: [
                '愤怒',
                '恐惧',
                '激动',
                '震惊',
                '绝望',
                '狂怒'
            ],
            medium: [
                '担心',
                '疑惑',
                '严肃',
                '警惕',
                '紧张'
            ],
            low: [
                '平静',
                '思考',
                '观察',
                '正常',
                '淡定'
            ]
        };
        for (const [level, indicators] of Object.entries(intensityIndicators)){
            if (indicators.some((indicator)=>text.includes(indicator))) {
                return level;
            }
        }
        return 'medium';
    }
}
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/ai/generate-story-video/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$smartModelSelector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/smartModelSelector.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
// 内容安全检查
function validatePromptContent(prompt) {
    // 检查敏感词汇
    const sensitiveWords = [
        '暴力',
        '血腥',
        '政治',
        '色情',
        '赌博',
        '毒品'
    ];
    const foundSensitive = sensitiveWords.find((word)=>prompt.includes(word));
    if (foundSensitive) {
        return {
            isValid: false,
            reason: `包含敏感词汇: ${foundSensitive}`
        };
    }
    // 检查长度
    if (prompt.length > 800) {
        return {
            isValid: false,
            reason: '提示词过长，超过800字符限制'
        };
    }
    // 检查是否为空
    if (!prompt.trim()) {
        return {
            isValid: false,
            reason: '提示词不能为空'
        };
    }
    return {
        isValid: true
    };
}
// 优化提示词
function optimizePrompt(prompt) {
    return prompt.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？、：；""''（）【】]/g, '') // 保留中文、英文、数字和基本标点
    .replace(/\s+/g, ' ') // 合并多个空格
    .substring(0, 500) // 限制长度为500字符
    .trim();
}
// 构建结构化角色DNA描述
function buildStructuredCharacterDNA(character) {
    const name = character.name || '未知角色';
    // 解析或生成详细DNA
    const dna = parseCharacterDNA(character);
    if (!dna) {
        return `【角色 - ${name}】基础信息不足，无法生成详细DNA`;
    }
    // 构建结构化DNA描述
    return `【角色DNA - ${name}】
📋 基础信息：
- 姓名：${name}
- 身份：${character.identity || '未知'}
- 性格：${character.personality || '未知'}

🎭 面部特征：
- 脸型：${dna.facial.faceShape}
- 眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}
- 鼻型：${dna.facial.noseShape}
- 嘴型：${dna.facial.mouthShape}
- 肤色：${dna.facial.skinTone}
- 面部特征：${dna.facial.facialFeatures}

👤 体型特征：
- 身高：${dna.physique.height}
- 体型：${dna.physique.build}
- 体态：${dna.physique.posture}

👔 服装特征：
- 风格：${dna.clothing.style}
- 颜色：${dna.clothing.colors.join('、')}
- 配饰：${dna.clothing.accessories.join('、') || '无'}

🔍 独特标识：
${dna.uniqueIdentifiers.map((id)=>`- ${id}`).join('\n')}

📝 标准提示词：
${dna.standardPrompt}

【生成约束】
- 严格按照上述DNA特征生成
- 确保面部特征高度一致
- 保持角色独特标识清晰可见
- 适应场景但不改变基本外貌`;
}
// 提取特征的辅助函数
function extractFeature(text, pattern, defaultValue) {
    if (!text) return defaultValue;
    const regex = new RegExp(`(${pattern})[^，。]*`, 'i');
    const match = text.match(regex);
    if (match) {
        return match[0].replace(/^(脸型|眼睛?|鼻子?|嘴唇?|肤色|身材|体型|身高|体态|姿态|发型|头发|发色|颜色|风格|款式|材质|面料|气质|特质)[:：]?/, '').trim();
    }
    return defaultValue;
}
// 提取独特特征
function extractUniqueFeatures(facial, physique) {
    const text = `${facial} ${physique}`.toLowerCase();
    const uniqueFeatures = [];
    // 检查常见独特标识
    if (text.includes('疤') || text.includes('伤')) uniqueFeatures.push('疤痕');
    if (text.includes('痣') || text.includes('黑点')) uniqueFeatures.push('痣');
    if (text.includes('酒窝')) uniqueFeatures.push('酒窝');
    if (text.includes('雀斑')) uniqueFeatures.push('雀斑');
    if (text.includes('胡子') || text.includes('胡须')) uniqueFeatures.push('胡须');
    if (text.includes('眼镜')) uniqueFeatures.push('眼镜');
    return uniqueFeatures.length > 0 ? uniqueFeatures.join('、') : '无明显特殊标记';
}
// 构建结构化提示词
function buildStructuredPrompt(plotContent, characterDNA, sceneDescription, cameraAngle, duration) {
    const durationText = duration ? `${duration}秒` : '15-30秒';
    return `【双重约束视频生成提示词】

📖 剧情内容：
${plotContent}

${characterDNA}

🎬 场景要求：
- 场景描述：${sceneDescription}
- 镜头角度：${cameraAngle}
- 视频时长：${durationText}
- 画面质量：高清晰度，电影级质量
- 光影效果：专业级光影，细节丰富

⏱️ 时长要求：
- 生成${durationText}的视频内容
- 确保动作和剧情完整展现
- 避免过快或过慢的节奏
- 保持自然的时间流逝感

🔒 生成约束：
1. 【最高优先级】严格按照角色DNA特征生成，确保面部特征高度一致
2. 【视觉约束】参考提供的角色图像，保持外貌一致性
3. 【文本约束】遵循详细的角色DNA描述，不允许偏差
4. 【时长约束】生成${durationText}的完整视频内容
5. 【场景适应】在保持角色一致性前提下适应场景氛围
6. 【独特标识】确保角色独特标识清晰可见且位置准确

⚠️ 严格禁止：
- 改变角色基本面部特征
- 忽略独特标识
- 模糊或歧义的特征表现
- 与DNA描述不符的外貌
- 生成过短或过长的视频内容`;
}
// 解析角色DNA数据
function parseCharacterDNA(character) {
    try {
        if (character.detailedDNA) {
            return JSON.parse(character.detailedDNA);
        }
        // 如果没有详细DNA，从基础字段构建
        return generateBasicDNA(character);
    } catch (error) {
        console.warn(`解析角色 ${character.name} 的DNA失败:`, error);
        return generateBasicDNA(character);
    }
}
// 从基础字段生成DNA
function generateBasicDNA(character) {
    return {
        facial: {
            faceShape: extractFeature(character.facial, '脸型', '方正轮廓'),
            eyeShape: extractFeature(character.facial, '眼', '中等大小'),
            eyeColor: extractFeature(character.facial, '眼色|眼珠', '深褐色'),
            noseShape: extractFeature(character.facial, '鼻', '高挺'),
            mouthShape: extractFeature(character.facial, '嘴|唇', '适中'),
            skinTone: extractFeature(character.facial, '肤色|皮肤', '健康'),
            facialFeatures: extractUniqueFeatures(character.facial, character.physique)
        },
        physique: {
            height: extractFeature(character.physique, '身高', '中等偏高'),
            build: extractFeature(character.physique, '身材|体型', '魁梧'),
            posture: extractFeature(character.physique, '体态|姿态', '挺拔')
        },
        clothing: {
            style: extractFeature(character.clothing, '风格|款式', '实用'),
            colors: extractColors(character.clothing),
            accessories: extractAccessories(character.clothing)
        },
        uniqueIdentifiers: extractUniqueIdentifiers(character),
        standardPrompt: generateStandardPrompt(character)
    };
}
// 提取颜色信息
function extractColors(clothing) {
    if (!clothing) return [
        '深色系'
    ];
    const colors = [];
    const colorMap = {
        '黑': '黑色',
        '白': '白色',
        '红': '红色',
        '蓝': '蓝色',
        '绿': '绿色',
        '黄': '黄色',
        '灰': '灰色',
        '棕': '棕色'
    };
    for (const [key, value] of Object.entries(colorMap)){
        if (clothing.includes(key)) {
            colors.push(value);
        }
    }
    return colors.length > 0 ? colors : [
        '深色系'
    ];
}
// 提取配饰信息
function extractAccessories(clothing) {
    if (!clothing) return [];
    const accessories = [];
    const accessoryMap = {
        '帽': '帽子',
        '眼镜': '眼镜',
        '手表': '手表',
        '项链': '项链',
        '戒指': '戒指',
        '耳环': '耳环',
        '腰带': '腰带',
        '徽章': '徽章'
    };
    for (const [key, value] of Object.entries(accessoryMap)){
        if (clothing.includes(key)) {
            accessories.push(value);
        }
    }
    return accessories;
}
// 提取独特标识符
function extractUniqueIdentifiers(character) {
    const identifiers = [];
    const text = `${character.facial || ''} ${character.physique || ''} ${character.personality || ''}`.toLowerCase();
    // 检查物理特征
    if (text.includes('疤') || text.includes('伤痕')) identifiers.push('疤痕');
    if (text.includes('痣') || text.includes('黑痣')) identifiers.push('痣');
    if (text.includes('酒窝')) identifiers.push('酒窝');
    if (text.includes('雀斑')) identifiers.push('雀斑');
    if (text.includes('胡子') || text.includes('胡须')) identifiers.push('胡须');
    if (text.includes('眼镜')) identifiers.push('眼镜');
    // 检查性格特征转化为外貌特征
    if (text.includes('严肃') || text.includes('冷峻')) identifiers.push('严肃表情');
    if (text.includes('温和') || text.includes('和善')) identifiers.push('温和气质');
    if (text.includes('威严') || text.includes('威武')) identifiers.push('威严气场');
    return identifiers.length > 0 ? identifiers : [
        '无明显特殊标记'
    ];
}
// 生成标准提示词
function generateStandardPrompt(character) {
    const name = character.name || '角色';
    const facial = character.facial || '';
    const physique = character.physique || '';
    const clothing = character.clothing || '';
    return `${name}，${facial}，${physique}，${clothing}，高质量人物肖像，细节丰富，专业摄影`;
}
// 下载视频到本地存储
async function downloadVideoToLocal(videoUrl, segmentId, segmentTitle) {
    try {
        console.log(`📥 开始下载视频: ${videoUrl}`);
        // 创建下载目录
        const downloadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'downloads', 'videos');
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(downloadDir)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(downloadDir, {
                recursive: true
            });
        }
        // 生成文件名
        const fileName = `${segmentId}_${segmentTitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.mp4`;
        const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(downloadDir, fileName);
        const publicPath = `/downloads/videos/${fileName}`;
        // 下载视频
        const response = await fetch(videoUrl);
        if (!response.ok) {
            console.error(`下载视频失败: ${response.status} ${response.statusText}`);
            return null;
        }
        // 保存到本地
        const buffer = await response.arrayBuffer();
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, Buffer.from(buffer));
        console.log(`✅ 视频下载成功: ${publicPath}`);
        return publicPath;
    } catch (error) {
        console.error('下载视频到本地失败:', error);
        return null;
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { episodeId: episodeId1, prompt, projectId, retrySegmentId, modelId, generationMode = 'manual' } = body;
        if (!episodeId1 || !prompt || !projectId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少必要参数'
            }, {
                status: 400
            });
        }
        // 🔒 先决条件检查：主要角色必须有参考图像
        console.log('🔍 检查角色参考图像先决条件...');
        const characterCheck = await checkCharacterImagePrerequisites(projectId);
        if (!characterCheck.success) {
            console.log('❌ 角色图像先决条件检查失败:', characterCheck.error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: characterCheck.error,
                missingCharacters: characterCheck.missingCharacters,
                requirementType: 'character_images'
            }, {
                status: 400
            });
        }
        console.log('✅ 角色图像先决条件检查通过');
        // 如果是重试特定片段，重定向到专门的重试API
        if (retrySegmentId) {
            console.log(`🔄 重试片段请求重定向到专门的重试API: ${retrySegmentId}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请使用专门的重试API: /api/ai/retry-failed-segment'
            });
        }
        // 检查是否已有该剧集的视频生成记录，并在事务中清理
        console.log(`🔍 检查剧集 ${episodeId1} 是否已有视频生成记录...`);
        let isRegeneration = false;
        // 使用事务确保删除操作的原子性
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
            const existingStoryVideos = await tx.storyVideo.findMany({
                where: {
                    episodeId: episodeId1
                },
                include: {
                    segments: true
                }
            });
            if (existingStoryVideos.length > 0) {
                isRegeneration = true;
                console.log(`🔄 检测到已有 ${existingStoryVideos.length} 个视频生成记录，准备清理并重新生成...`);
                let totalDeletedSegments = 0;
                for (const existingVideo of existingStoryVideos){
                    console.log(`   - 现有视频ID: ${existingVideo.id}`);
                    console.log(`   - 现有片段数量: ${existingVideo.segments.length}`);
                    // 删除所有相关的视频片段
                    const deletedSegments = await tx.videoSegment.deleteMany({
                        where: {
                            storyVideoId: existingVideo.id
                        }
                    });
                    totalDeletedSegments += deletedSegments.count;
                    console.log(`   - 删除了 ${deletedSegments.count} 个视频片段`);
                    // 删除主视频记录
                    await tx.storyVideo.delete({
                        where: {
                            id: existingVideo.id
                        }
                    });
                    console.log(`   - 删除了主视频记录`);
                }
                console.log(`✅ 已清理 ${existingStoryVideos.length} 个视频记录和 ${totalDeletedSegments} 个片段，开始重新生成`);
            } else {
                console.log(`✨ 没有发现现有视频记录，开始首次生成`);
            }
        });
        // 获取AI配置
        const aiConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
            where: {
                enabled: true
            }
        });
        if (!aiConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请先配置AI模型'
            }, {
                status: 400
            });
        }
        // 检查是否有详细剧情信息
        const plotInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].plotInfo.findUnique({
            where: {
                episodeId: episodeId1
            }
        });
        if (!plotInfo || !plotInfo.generatedPrompt) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请先提取具体剧情信息'
            }, {
                status: 400
            });
        }
        // 使用已提取的详细剧情信息生成视频
        const videoResult = await generateSegmentedStoryVideo(aiConfig, episodeId1, projectId, plotInfo, isRegeneration, modelId, generationMode);
        const message = isRegeneration ? `剧情视频重新生成已启动，共${videoResult.totalSegments}个片段` : `剧情视频分段生成已启动，共${videoResult.totalSegments}个片段`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                videoId: videoResult.storyVideoId,
                segments: videoResult.segments,
                totalSegments: videoResult.totalSegments,
                status: 'generating',
                isRegeneration
            },
            message
        });
    } catch (error) {
        console.error('生成剧情视频失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '生成失败，请重试'
        }, {
            status: 500
        });
    }
}
// 生成分段剧情视频
async function generateSegmentedStoryVideo(aiConfig, episodeId1, projectId, plotInfo, isRegeneration = false, modelId, generationMode = 'manual') {
    try {
        const deepSeekClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DeepSeekClient"](aiConfig);
        // 1. 使用已有的详细剧情信息，分解为视频片段
        const segments = await createVideoSegmentsFromPlotInfo(deepSeekClient, plotInfo, episodeId1);
        // 2. 创建主视频记录
        const storyVideo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].storyVideo.create({
            data: {
                episodeId: episodeId1,
                projectId,
                prompt: plotInfo.generatedPrompt || '',
                status: 'generating',
                metadata: JSON.stringify({
                    totalSegments: segments.length,
                    generatedAt: new Date().toISOString(),
                    basedOnDetailedPlot: true,
                    isRegeneration,
                    regeneratedAt: isRegeneration ? new Date().toISOString() : undefined
                })
            }
        });
        // 3. 创建视频片段记录
        const videoSegments = await Promise.all(segments.map((segment, index)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.create({
                data: {
                    storyVideoId: storyVideo.id,
                    episodeId: episodeId1,
                    projectId,
                    segmentIndex: index + 1,
                    title: segment.title,
                    description: segment.description,
                    prompt: segment.prompt,
                    segmentType: segment.type,
                    duration: segment.duration || 15,
                    status: 'pending',
                    metadata: JSON.stringify(segment.metadata || {})
                }
            })));
        // 4. 根据生成模式决定是否自动生成片段
        if (generationMode === 'auto') {
            // 自动模式：立即开始生成所有片段
            generateSegmentsAsync(aiConfig, storyVideo.id, videoSegments, modelId);
        }
        // 手动模式：不自动生成，等待用户手动触发
        return {
            storyVideoId: storyVideo.id,
            segments: videoSegments.map((seg)=>({
                    id: seg.id,
                    title: seg.title,
                    description: seg.description,
                    segmentIndex: seg.segmentIndex,
                    status: seg.status,
                    segmentType: seg.segmentType
                })),
            totalSegments: segments.length
        };
    } catch (error) {
        console.error('分段视频生成失败:', error);
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIServiceError"]('分段视频生成失败', 'SEGMENTED_VIDEO_GENERATION_FAILED');
    }
}
// 基于详细剧情信息创建视频片段
async function createVideoSegmentsFromPlotInfo(deepSeekClient, plotInfo, episodeId1) {
    try {
        // 解析已有的详细剧情信息
        const detailedCharacters = plotInfo.detailedCharacters ? JSON.parse(plotInfo.detailedCharacters) : [];
        const detailedScenes = plotInfo.detailedScenes ? JSON.parse(plotInfo.detailedScenes) : [];
        const plotSequences = plotInfo.plotSequences ? JSON.parse(plotInfo.plotSequences) : [];
        const emotionalArc = plotInfo.emotionalArc || '';
        const generatedPrompt = plotInfo.generatedPrompt || '';
        // 如果有剧情序列，智能拆分为多个短片段
        if (plotSequences && plotSequences.length > 0) {
            const allSegments = [];
            for (const sequence of plotSequences){
                const originalDuration = parseDuration(sequence.duration) || 20;
                const segmentDuration = 5 // 固定5秒片段（技术限制）
                ;
                const segmentCount = Math.ceil(originalDuration / segmentDuration);
                // 智能拆分为5秒片段
                const subSegments = splitSequenceIntoFiveSecondSegments(sequence, segmentCount, detailedCharacters, detailedScenes);
                allSegments.push(...subSegments);
            }
            console.log(`📊 拆分结果: ${plotSequences.length}个原始序列 → ${allSegments.length}个5秒片段`);
            return allSegments;
        }
        // 如果没有剧情序列，基于生成的提示词进行智能分段
        const segmentPrompt = `基于以下已分析的详细剧情信息，将其分解为适合视频生成的短片段：

角色信息：
${JSON.stringify(detailedCharacters, null, 2)}

场景信息：
${JSON.stringify(detailedScenes, null, 2)}

情感弧线：
${emotionalArc}

生成的剧情提示词：
${generatedPrompt}

请按照以下JSON格式返回分段结果：
{
  "segments": [
    {
      "title": "片段标题",
      "description": "片段描述",
      "prompt": "适合AI视频生成的详细提示词",
      "type": "scene|action|dialogue|transition",
      "duration": 预估时长秒数,
      "metadata": {
        "characters": ["出现的角色"],
        "location": "场景地点",
        "mood": "情感氛围",
        "cameraAngle": "镜头角度"
      }
    }
  ]
}

要求：
1. 每个片段时长严格控制在5秒（技术限制）
2. 充分利用已分析的角色和场景信息
3. 确保片段之间有逻辑连贯性
4. 提示词要具体且可视化
5. 分段数量控制在12-16个（确保内容完整覆盖）
6. 每个片段表达一个核心情节点
7. 返回标准JSON格式`;
        const response = await deepSeekClient.callAPI(segmentPrompt, 4000);
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('未找到有效的JSON响应');
        }
        const result = JSON.parse(jsonMatch[0]);
        return result.segments || [];
    } catch (error) {
        console.error('基于详细剧情信息创建视频片段失败:', error);
        // 返回基于基础信息的默认分段
        return await createFallbackSegments(plotInfo, episodeId1);
    }
}
// 智能拆分序列为5秒片段
function splitSequenceIntoFiveSecondSegments(sequence, segmentCount, detailedCharacters, detailedScenes) {
    const segments = [];
    const baseAction = sequence.action || '';
    const baseEmotion = sequence.emotion || 'neutral';
    const keyMoments = sequence.keyMoments || [];
    // 根据动作类型进行智能拆分
    if (baseAction.includes('遇险') || baseAction.includes('坠落') || baseAction.includes('失足')) {
        // 遇险类动作拆分
        const phases = [
            '接近危险',
            '察觉危险',
            '发生意外',
            '应对危机'
        ];
        for(let i = 0; i < segmentCount; i++){
            const phase = phases[i] || phases[phases.length - 1];
            segments.push({
                title: `${baseAction} - ${phase}`,
                description: `${baseAction}的${phase}阶段`,
                prompt: generateVideoPrompt({
                    ...sequence,
                    action: `${baseAction}的${phase}部分：${getPhaseDescription(baseAction, phase, i, segmentCount)}`,
                    visualElements: sequence.visualElements
                }, detailedCharacters, detailedScenes),
                type: determineSegmentType(sequence),
                duration: 5,
                metadata: {
                    characters: extractCharactersFromSequence(sequence, detailedCharacters),
                    location: extractLocationFromSequence(sequence, detailedScenes),
                    mood: baseEmotion,
                    cameraAngle: sequence.visualElements || 'medium shot',
                    sequenceId: sequence.sequenceId,
                    segmentIndex: i + 1,
                    totalSegments: segmentCount,
                    phase: phase
                }
            });
        }
    } else if (baseAction.includes('救援') || baseAction.includes('帮助') || baseAction.includes('扛')) {
        // 救援类动作拆分
        const phases = [
            '发现情况',
            '准备救援',
            '执行救援',
            '完成救援'
        ];
        for(let i = 0; i < segmentCount; i++){
            const phase = phases[i] || phases[phases.length - 1];
            segments.push({
                title: `${baseAction} - ${phase}`,
                description: `${baseAction}的${phase}阶段`,
                prompt: generateVideoPrompt({
                    ...sequence,
                    action: `${baseAction}的${phase}部分：${getPhaseDescription(baseAction, phase, i, segmentCount)}`,
                    visualElements: sequence.visualElements
                }, detailedCharacters, detailedScenes),
                type: determineSegmentType(sequence),
                duration: 5,
                metadata: {
                    characters: extractCharactersFromSequence(sequence, detailedCharacters),
                    location: extractLocationFromSequence(sequence, detailedScenes),
                    mood: baseEmotion,
                    cameraAngle: sequence.visualElements || 'medium shot',
                    sequenceId: sequence.sequenceId,
                    segmentIndex: i + 1,
                    totalSegments: segmentCount,
                    phase: phase
                }
            });
        }
    } else {
        // 通用拆分：基于时间进度
        for(let i = 0; i < segmentCount; i++){
            const progress = (i + 1) / segmentCount;
            const phaseDesc = progress <= 0.25 ? '开始' : progress <= 0.5 ? '发展' : progress <= 0.75 ? '高潮' : '结束';
            segments.push({
                title: `${baseAction} - 第${i + 1}部分`,
                description: `${baseAction}的${phaseDesc}阶段`,
                prompt: generateVideoPrompt({
                    ...sequence,
                    action: `${baseAction}的第${i + 1}部分（${phaseDesc}阶段）`,
                    visualElements: sequence.visualElements
                }, detailedCharacters, detailedScenes),
                type: determineSegmentType(sequence),
                duration: 5,
                metadata: {
                    characters: extractCharactersFromSequence(sequence, detailedCharacters),
                    location: extractLocationFromSequence(sequence, detailedScenes),
                    mood: baseEmotion,
                    cameraAngle: sequence.visualElements || 'medium shot',
                    sequenceId: sequence.sequenceId,
                    segmentIndex: i + 1,
                    totalSegments: segmentCount,
                    progress: progress
                }
            });
        }
    }
    return segments;
}
// 获取阶段描述
function getPhaseDescription(baseAction, phase, index, total) {
    const actionMap = {
        '遇险': {
            '接近危险': '张三小心翼翼地走在悬崖边缘，脚下的石头看起来并不稳固',
            '察觉危险': '张三感觉到脚下石头的松动，开始意识到危险的存在',
            '发生意外': '石头突然松动，张三失去平衡开始坠落',
            '应对危机': '张三在坠落过程中努力寻找可以抓住的东西'
        },
        '救援': {
            '发现情况': '李四听到呼救声，迅速确定声音来源',
            '准备救援': '李四准备救援工具，评估救援方案',
            '执行救援': '李四开始实施救援行动',
            '完成救援': '李四成功救援，确保安全'
        }
    };
    // 查找匹配的动作类型
    for (const [action, phases] of Object.entries(actionMap)){
        if (baseAction.includes(action)) {
            return phases[phase] || `${baseAction}的${phase}部分`;
        }
    }
    return `${baseAction}的${phase}部分`;
}
// 异步生成各个视频片段 - 串行生成，一个接一个
async function generateSegmentsAsync(aiConfig, storyVideoId, segments, modelId) {
    console.log(`开始串行生成${segments.length}个视频片段...`);
    for(let i = 0; i < segments.length; i++){
        const segment = segments[i];
        console.log(`\n🎬 开始生成第 ${i + 1}/${segments.length} 个片段: ${segment.title}`);
        try {
            // 更新片段状态为生成中
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                where: {
                    id: segment.id
                },
                data: {
                    status: 'generating',
                    metadata: JSON.stringify({
                        ...JSON.parse(segment.metadata || '{}'),
                        startTime: new Date().toISOString(),
                        segmentOrder: i + 1,
                        totalSegments: segments.length
                    })
                }
            });
            console.log(`📝 片段 ${i + 1} 开始生成提示词优化...`);
            // 调用真实的视频生成API
            const videoResult = await generateSingleSegment(aiConfig, segment, modelId);
            // 根据生成结果更新片段信息
            const updateData = {
                duration: videoResult.duration,
                metadata: JSON.stringify({
                    ...JSON.parse(segment.metadata || '{}'),
                    startTime: new Date().toISOString(),
                    generatedAt: new Date().toISOString(),
                    optimizedPrompt: videoResult.optimizedPrompt,
                    segmentOrder: i + 1,
                    totalSegments: segments.length
                })
            };
            // 根据生成结果更新状态和URL
            if (videoResult.status === 'completed' && videoResult.videoUrl) {
                updateData.videoUrl = videoResult.videoUrl;
                updateData.thumbnailUrl = videoResult.thumbnailUrl;
                updateData.status = 'completed';
                console.log(`✅ 片段 ${i + 1} 生成完成: ${segment.title}`);
                // 执行一致性验证
                try {
                    const metadata = JSON.parse(segment.metadata || '{}');
                    const characterNames = metadata.characters || [];
                    for (const characterName of characterNames){
                        const character = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findFirst({
                            where: {
                                name: characterName
                            }
                        });
                        if (character) {
                            const consistencyResult = await validateBasicConsistency(videoResult.videoUrl, character, segment);
                            await saveConsistencyValidation(character.id, episodeId, consistencyResult, videoResult.videoUrl);
                            console.log(`🔍 角色 ${characterName} 一致性验证: ${consistencyResult.overallConsistency.toFixed(2)} (${consistencyResult.passed ? '通过' : '未通过'})`);
                        }
                    }
                } catch (error) {
                    console.warn('一致性验证失败，但不影响主流程:', error);
                }
            } else if (videoResult.status === 'generating') {
                updateData.status = 'generating';
                updateData.metadata = JSON.stringify({
                    ...JSON.parse(segment.metadata || '{}'),
                    taskId: videoResult.taskId,
                    startTime: new Date().toISOString(),
                    generatedAt: new Date().toISOString(),
                    optimizedPrompt: videoResult.optimizedPrompt,
                    segmentOrder: i + 1,
                    totalSegments: segments.length
                });
                console.log(`🔄 片段 ${i + 1} 已提交生成任务，等待完成: ${segment.title}`);
                // 串行生成：等待当前片段完成后再开始下一个
                if (videoResult.taskId) {
                    console.log(`⏳ 等待片段 ${i + 1} 完成生成...`);
                    await waitForSegmentCompletion(segment.id, videoResult.taskId, 600000) // 最多等待10分钟
                    ;
                }
            } else if (videoResult.status === 'failed') {
                updateData.status = 'failed';
                updateData.metadata = JSON.stringify({
                    ...JSON.parse(segment.metadata || '{}'),
                    error: videoResult.error,
                    startTime: new Date().toISOString(),
                    generatedAt: new Date().toISOString(),
                    optimizedPrompt: videoResult.optimizedPrompt,
                    segmentOrder: i + 1,
                    totalSegments: segments.length
                });
                console.log(`❌ 片段 ${i + 1} 生成失败: ${videoResult.error}`);
            } else {
                updateData.status = 'pending';
                console.log(`⏳ 片段 ${i + 1} 状态待定`);
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                where: {
                    id: segment.id
                },
                data: updateData
            });
            // 串行生成：等待当前片段完成后再开始下一个
            if (i < segments.length - 1) {
                console.log(`⏱️ 等待 3 秒后开始下一个片段...`);
                await new Promise((resolve)=>setTimeout(resolve, 3000));
            }
        } catch (error) {
            console.error(`❌ 片段 ${i + 1} 生成失败:`, error);
            // 更新片段状态为失败
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                where: {
                    id: segment.id
                },
                data: {
                    status: 'failed',
                    metadata: JSON.stringify({
                        ...JSON.parse(segment.metadata || '{}'),
                        error: error instanceof Error ? error.message : '生成失败',
                        failedAt: new Date().toISOString(),
                        segmentOrder: i + 1,
                        totalSegments: segments.length
                    })
                }
            });
            // 串行生成：即使失败也继续下一个片段
            if (i < segments.length - 1) {
                console.log(`⏱️ 片段失败，等待 2 秒后继续下一个片段...`);
                await new Promise((resolve)=>setTimeout(resolve, 2000));
            }
        }
    }
    // 检查所有片段是否完成，更新主视频状态
    const completedSegments = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.findMany({
        where: {
            storyVideoId,
            status: 'completed'
        }
    });
    const totalSegments = segments.length;
    const isAllCompleted = completedSegments.length === totalSegments;
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].storyVideo.update({
        where: {
            id: storyVideoId
        },
        data: {
            status: isAllCompleted ? 'completed' : 'generating',
            metadata: JSON.stringify({
                totalSegments,
                completedSegments: completedSegments.length,
                completedAt: isAllCompleted ? new Date().toISOString() : null
            })
        }
    });
    console.log(`剧情视频生成完成: ${completedSegments.length}/${totalSegments} 片段成功`);
}
// 辅助函数：为剧情序列生成视频提示词（增强版：支持角色一致性）
function generateVideoPrompt(sequence, characters, scenes) {
    const action = sequence.action || '';
    const emotion = sequence.emotion || '';
    const visualElements = sequence.visualElements || '';
    // 查找相关角色信息
    const relevantCharacters = characters.filter((char)=>action.includes(char.name) || sequence.keyMoments && sequence.keyMoments.some((moment)=>moment.includes(char.name)));
    // 查找相关场景信息
    const relevantScenes = scenes.filter((scene)=>action.includes(scene.location) || visualElements.includes(scene.location));
    // 构建一致性约束的提示词
    return buildConsistencyVideoPrompt({
        action,
        emotion,
        visualElements,
        relevantCharacters,
        relevantScenes,
        sequence
    });
}
// 构建一致性视频提示词
function buildConsistencyVideoPrompt(options) {
    const { action, emotion, visualElements, relevantCharacters, relevantScenes } = options;
    const sections = [];
    // 1. 剧情动作描述
    sections.push(`${action}`);
    // 2. 角色一致性约束（详细）
    if (relevantCharacters.length > 0) {
        const characterConstraints = relevantCharacters.map((char)=>{
            const features = [];
            // 收集角色特征
            if (char.name) features.push(`角色：${char.name}`);
            if (char.appearance) features.push(`外貌：${char.appearance}`);
            if (char.facial) features.push(`面部：${char.facial}`);
            if (char.hairstyle) features.push(`发型：${char.hairstyle}`);
            if (char.clothing) features.push(`服装：${char.clothing}`);
            if (char.physique) features.push(`体型：${char.physique}`);
            // 添加一致性约束
            features.push('【一致性要求：面部特征、发型、服装必须与之前片段完全一致，独特标识清晰可见】');
            return features.join('，');
        }).join('；');
        sections.push(characterConstraints);
    }
    // 3. 场景约束
    if (relevantScenes.length > 0) {
        const sceneDesc = relevantScenes[0];
        const sceneFeatures = [];
        if (sceneDesc.description) sceneFeatures.push(`场景：${sceneDesc.description}`);
        if (sceneDesc.atmosphere) sceneFeatures.push(`氛围：${sceneDesc.atmosphere}`);
        if (sceneFeatures.length > 0) {
            sections.push(sceneFeatures.join('，'));
        }
    }
    // 4. 情感和视觉要素
    if (emotion) sections.push(`情感：${emotion}`);
    if (visualElements) sections.push(`视觉：${visualElements}`);
    // 5. 技术要求
    sections.push('高质量电影级画面，5秒视频，角色外貌前后一致，细节丰富，光影效果佳');
    return sections.join('。') + '。';
}
// 辅助函数：确定片段类型
function determineSegmentType(sequence) {
    const action = (sequence.action || '').toLowerCase();
    const visualElements = (sequence.visualElements || '').toLowerCase();
    if (action.includes('对话') || action.includes('说话') || action.includes('交谈')) {
        return 'dialogue';
    } else if (action.includes('战斗') || action.includes('打斗') || action.includes('追逐')) {
        return 'action';
    } else if (visualElements.includes('转场') || action.includes('转向') || action.includes('切换')) {
        return 'transition';
    } else {
        return 'scene';
    }
}
// 辅助函数：解析时长
function parseDuration(duration) {
    if (typeof duration === 'number') return duration;
    if (typeof duration === 'string') {
        const match = duration.match(/(\d+)/);
        return match ? parseInt(match[1]) : 15;
    }
    return 15;
}
// 辅助函数：从序列中提取角色
function extractCharactersFromSequence(sequence, characters) {
    const action = sequence.action || '';
    const keyMoments = sequence.keyMoments || [];
    return characters.filter((char)=>action.includes(char.name) || keyMoments.some((moment)=>moment.includes(char.name))).map((char)=>char.name);
}
// 辅助函数：从序列中提取位置
function extractLocationFromSequence(sequence, scenes) {
    const action = sequence.action || '';
    const visualElements = sequence.visualElements || '';
    const relevantScene = scenes.find((scene)=>action.includes(scene.location) || visualElements.includes(scene.location));
    return relevantScene ? relevantScene.location : '未知场景';
}
// 辅助函数：创建备用片段（增强角色信息）
async function createFallbackSegments(plotInfo, episodeId1) {
    const generatedPrompt = plotInfo.generatedPrompt || '';
    // 获取项目中的角色信息
    let projectCharacters = [];
    try {
        const targetEpisodeId = episodeId1 || plotInfo.episodeId;
        if (targetEpisodeId) {
            const episode = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].episode.findUnique({
                where: {
                    id: targetEpisodeId
                },
                include: {
                    project: true
                }
            });
            if (episode?.project?.id) {
                projectCharacters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
                    where: {
                        projectId: episode.project.id
                    }
                });
                console.log(`📋 找到 ${projectCharacters.length} 个项目角色`);
            }
        }
    } catch (error) {
        console.warn('获取项目角色失败:', error);
    }
    // 生成结构化角色DNA描述
    const characterDescriptions = projectCharacters.map((char)=>{
        return buildStructuredCharacterDNA(char);
    }).join('\n\n');
    if (generatedPrompt.length > 100) {
        // 将长提示词分成3个片段，并添加结构化角色DNA
        const length = generatedPrompt.length;
        const segmentLength = Math.floor(length / 3);
        return [
            {
                title: "开场片段",
                description: "剧情开始",
                prompt: buildStructuredPrompt(generatedPrompt.substring(0, segmentLength), characterDescriptions, "开场建立，展现角色初始状态", "wide shot", 8),
                type: "scene",
                duration: 8,
                metadata: {
                    characters: projectCharacters.map((c)=>c.name),
                    location: "场景1",
                    mood: "neutral",
                    cameraAngle: "wide shot"
                }
            },
            {
                title: "发展片段",
                description: "剧情发展",
                prompt: buildStructuredPrompt(generatedPrompt.substring(segmentLength, segmentLength * 2), characterDescriptions, "剧情推进，角色互动发展", "medium shot", 8),
                type: "action",
                duration: 8,
                metadata: {
                    characters: projectCharacters.map((c)=>c.name),
                    location: "场景2",
                    mood: "tense",
                    cameraAngle: "medium shot"
                }
            },
            {
                title: "结尾片段",
                description: "剧情结尾",
                prompt: buildStructuredPrompt(generatedPrompt.substring(segmentLength * 2), characterDescriptions, "剧情收尾，角色状态变化", "close up", 6),
                type: "scene",
                duration: 6,
                metadata: {
                    characters: projectCharacters.map((c)=>c.name),
                    location: "场景3",
                    mood: "resolution",
                    cameraAngle: "close up"
                }
            }
        ];
    }
    // 如果提示词太短，创建单个片段
    return [
        {
            title: "完整片段",
            description: "完整剧情",
            prompt: buildStructuredPrompt(generatedPrompt, characterDescriptions, "完整剧情展现，角色完整呈现", "medium shot", 8),
            type: "scene",
            duration: 8,
            metadata: {
                characters: projectCharacters.map((c)=>c.name),
                location: "主场景",
                mood: "neutral",
                cameraAngle: "medium shot"
            }
        }
    ];
}
// 生成单个视频片段
async function generateSingleSegment(aiConfig, segment, modelId) {
    try {
        // 1. 获取DeepSeek配置用于提示词优化
        const deepSeekConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
            where: {
                provider: 'deepseek',
                enabled: true
            }
        });
        let optimizedPrompt = segment.prompt;
        if (deepSeekConfig) {
            try {
                const deepSeekClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DeepSeekClient"](deepSeekConfig);
                const optimizationPrompt = `请优化以下视频片段的生成提示词，使其更适合AI视频生成：

片段标题：${segment.title}
片段描述：${segment.description}
原始提示词：${segment.prompt}

请按照以下要求优化：
1. 确保描述具体且可视化
2. 添加适合的镜头运动和角度
3. 强调画面质量和风格
4. 控制在100字以内
5. 适合${segment.segmentType}类型的片段

优化后的提示词：`;
                optimizedPrompt = await deepSeekClient.callAPI(optimizationPrompt, 800);
                console.log(`✅ 使用DeepSeek优化提示词: ${optimizedPrompt.substring(0, 100)}...`);
            } catch (error) {
                console.warn('DeepSeek提示词优化失败，使用原始提示词:', error.message);
                optimizedPrompt = segment.prompt;
            }
        } else {
            console.warn('未找到DeepSeek配置，跳过提示词优化');
            optimizedPrompt = segment.prompt;
        }
        // 2. 调用视频生成API
        const videoResult = await callVideoGenerationAPI(optimizedPrompt.trim(), segment, modelId);
        return {
            videoUrl: videoResult.videoUrl,
            thumbnailUrl: videoResult.thumbnailUrl,
            optimizedPrompt: optimizedPrompt.trim(),
            duration: videoResult.duration || segment.duration || 15,
            status: videoResult.status || 'completed'
        };
    } catch (error) {
        console.error('单个视频片段生成失败:', error);
        return {
            videoUrl: null,
            thumbnailUrl: null,
            optimizedPrompt: segment.prompt,
            duration: segment.duration || 15,
            status: 'failed',
            error: error.message
        };
    }
}
// 调用视频生成API
async function callVideoGenerationAPI(prompt, segment, modelId) {
    try {
        // 检查是否有角色参考图像（用于一致性判断）
        const hasCharacterImages = segment.metadata?.characters?.some((char)=>char.imageUrl || char.referenceImages?.length > 0);
        // 获取用户选择的视频生成模型配置
        let videoModel;
        if (modelId) {
            // 使用用户指定的模型
            videoModel = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findUnique({
                where: {
                    id: modelId
                }
            });
            if (!videoModel) {
                throw new Error(`未找到指定的模型配置: ${modelId}`);
            }
            if (!videoModel.enabled) {
                throw new Error(`指定的模型未启用: ${videoModel.name}`);
            }
            console.log('🎯 使用用户指定的模型:');
            console.log(`   模型: ${videoModel.name} (${videoModel.model})`);
            console.log(`   提供商: ${videoModel.provider}`);
            // 检查角色一致性
            const hasCharacterImages = segment.metadata?.characters?.some((char)=>char.imageUrl || char.referenceImages?.length > 0);
            if (hasCharacterImages) {
                if (videoModel.supportsImageToVideo) {
                    console.log('✅ 检测到角色参考图像，使用I2V模型确保人物一致性');
                } else {
                    console.log('💡 提醒: 检测到角色参考图像，建议使用I2V模型以确保人物一致性');
                }
            }
            // 人物一致性提醒
            if (hasCharacterImages) {
                if (videoModel.supportsImageToVideo) {
                    console.log('✅ 检测到角色参考图像，使用I2V模型确保人物一致性');
                } else {
                    console.log('💡 提醒: 检测到角色参考图像，建议使用I2V模型以确保人物一致性');
                }
            }
        } else {
            // 如果没有指定模型，使用智能选择器（保持向后兼容）
            const videoModels = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findMany({
                where: {
                    enabled: true,
                    OR: [
                        {
                            supportsVideo: true
                        },
                        {
                            supportsImageToVideo: true
                        }
                    ]
                }
            });
            if (videoModels.length === 0) {
                throw new Error('未找到可用的视频生成模型');
            }
            // 使用智能模型选择器（增强版：优先考虑角色一致性）
            const modelSelector = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$smartModelSelector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SmartModelSelector"](videoModels);
            const characterImages = await getCharacterReferenceImages(segment);
            const segmentInfo = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$smartModelSelector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SmartModelSelector"].analyzeSegment(segment, characterImages);
            // 如果有角色图像，优先选择I2V模型
            if (characterImages.length > 0) {
                const i2vModel = videoModels.find((model)=>model.provider === 'doubao' && model.model === 'ep-20250624195026-qjsmk' && model.supportsImageToVideo);
                if (i2vModel) {
                    videoModel = i2vModel;
                    console.log('🎨 智能选择结果: 检测到角色图像，自动选择I2V模型确保人物一致性');
                    console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`);
                    console.log(`   角色图像数量: ${characterImages.length}`);
                } else {
                    const modelSelection = modelSelector.selectBestModel(segmentInfo);
                    videoModel = modelSelection.selectedModel;
                    console.log('⚠️ 未找到可用的I2V模型，使用智能选择器');
                    console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`);
                    console.log(`   选择理由: ${modelSelection.reason}`);
                }
            } else {
                const modelSelection = modelSelector.selectBestModel(segmentInfo);
                videoModel = modelSelection.selectedModel;
                console.log('🤖 智能模型选择结果:');
                console.log(`   选择模型: ${videoModel.name} (${videoModel.model})`);
                console.log(`   选择理由: ${modelSelection.reason}`);
            }
        }
        // 根据不同的视频生成服务调用相应的API
        if (videoModel.provider === 'doubao') {
            // 检查是否为图生视频模型
            if (videoModel.model === 'ep-20250624195026-qjsmk') {
                // Doubao-Seedance-1.0-lite-i2v - 图生视频
                return await callDoubaoImageToVideoAPI(videoModel, prompt, segment);
            } else if (videoModel.model === 'ep-20250624192345-5ccwj') {
                // Doubao-Seedance-1.0-lite-t2v - 文生视频
                return await callDoubaoTextToVideoAPI(videoModel, prompt, segment);
            } else if (videoModel.model === 'ep-20250624192235-zttm6') {
                // Doubao-Seedance-1.0-pro - 专业版视频
                return await callDoubaoVideoAPI(videoModel, prompt, segment);
            } else {
                // 其他豆包模型，使用原有的API
                return await callDoubaoVideoAPI(videoModel, prompt, segment);
            }
        } else if (videoModel.provider === 'minimax') {
            return await callMinimaxHailuoAPI(videoModel, prompt, segment);
        } else if (videoModel.provider === 'tongyi') {
            return await callTongyiVideoAPI(videoModel, prompt, segment);
        } else if (videoModel.provider === 'skyreels') {
            return await callSkyReelsVideoAPI(videoModel, prompt, segment);
        }
        // 如果没有配置真实的视频生成API，返回占位符
        console.warn('未配置真实的视频生成API，返回占位符数据');
        return {
            videoUrl: null,
            thumbnailUrl: null,
            duration: segment.duration || 15,
            status: 'pending'
        };
    } catch (error) {
        console.error('调用视频生成API失败:', error);
        throw error;
    }
}
// 轮询通义万相任务状态
async function pollTongyiTaskStatus(config, taskId, segment) {
    const maxAttempts = 120 // 最多轮询120次（10分钟）
    ;
    let pollInterval = 3000 // 初始3秒轮询一次
    ;
    const maxPollInterval = 10000 // 最大10秒间隔
    ;
    const intervalIncrement = 1000 // 每次增加1秒
    ;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            const startTime = Date.now();
            console.log(`轮询通义万相任务状态 (${attempt}/${maxAttempts}): ${taskId} [间隔: ${pollInterval}ms]`);
            const response = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const responseTime = Date.now() - startTime;
            if (!response.ok) {
                console.error(`轮询请求失败: ${response.status} ${response.statusText} [响应时间: ${responseTime}ms]`);
                await new Promise((resolve)=>setTimeout(resolve, pollInterval));
                // 智能调整轮询间隔
                if (pollInterval < maxPollInterval) {
                    pollInterval = Math.min(pollInterval + intervalIncrement, maxPollInterval);
                }
                continue;
            }
            const result = await response.json();
            console.log(`任务状态响应 [响应时间: ${responseTime}ms]:`, JSON.stringify(result, null, 2));
            if (result.output) {
                const status = result.output.task_status;
                if (status === 'SUCCEEDED') {
                    // 任务完成，更新数据库
                    // 通义万相API返回的字段是 video_url，不是 results 数组
                    const videoUrl = result.output.video_url || result.output.results?.[0]?.url;
                    const videoSize = result.output.video_size || result.output.results?.[0]?.size || 'unknown';
                    const duration = result.output.video_duration || result.output.results?.[0]?.duration || 5;
                    console.log('🔍 解析视频URL:', {
                        video_url: result.output.video_url,
                        results: result.output.results,
                        finalUrl: videoUrl
                    });
                    if (videoUrl) {
                        const completedAt = new Date().toISOString();
                        // 立即自动下载视频到本地存储
                        console.log(`📥 开始自动下载视频到本地存储...`);
                        const localVideoPath = await downloadVideoToLocal(videoUrl, segment.id, segment.title);
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                            where: {
                                id: segment.id
                            },
                            data: {
                                videoUrl: localVideoPath || videoUrl,
                                status: 'completed',
                                duration: duration,
                                metadata: JSON.stringify({
                                    taskId: taskId,
                                    provider: 'tongyi',
                                    completedAt: completedAt,
                                    videoSize: videoSize,
                                    responseTime: responseTime,
                                    totalAttempts: attempt,
                                    generationTime: Date.now() - startTime,
                                    originalUrl: videoUrl,
                                    localPath: localVideoPath,
                                    autoDownloaded: !!localVideoPath
                                })
                            }
                        });
                        console.log(`🎉 片段 ${segment.segmentIndex} 通义万相视频生成完成!`);
                        console.log(`   原始URL: ${videoUrl}`);
                        console.log(`   本地路径: ${localVideoPath || '下载失败，使用原始URL'}`);
                        console.log(`   文件大小: ${videoSize}`);
                        console.log(`   生成耗时: ${attempt} 次轮询`);
                        console.log(`   完成时间: ${completedAt}`);
                    } else {
                        console.error('❌ 未找到视频URL，完整响应:', JSON.stringify(result.output, null, 2));
                    }
                    return;
                } else if (status === 'FAILED') {
                    // 任务失败
                    const errorMessage = result.output.message || result.output.code || '任务失败';
                    const errorDetails = {
                        taskId: taskId,
                        provider: 'tongyi',
                        error: errorMessage,
                        failedAt: new Date().toISOString(),
                        totalAttempts: attempt,
                        responseTime: responseTime,
                        fullResponse: result.output
                    };
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                        where: {
                            id: segment.id
                        },
                        data: {
                            status: 'failed',
                            metadata: JSON.stringify(errorDetails)
                        }
                    });
                    console.error(`❌ 片段 ${segment.segmentIndex} 通义万相视频生成失败:`);
                    console.error(`   错误信息: ${errorMessage}`);
                    console.error(`   完整响应:`, result.output);
                    return;
                }
                // 状态为 PENDING 或 RUNNING，继续轮询
                console.log(`⏳ 任务状态: ${status}, 继续轮询...`);
            }
            // 智能调整轮询间隔：前10次保持快速，之后逐渐增加间隔
            if (attempt > 10 && pollInterval < maxPollInterval) {
                pollInterval = Math.min(pollInterval + intervalIncrement, maxPollInterval);
            }
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        } catch (error) {
            console.error(`轮询通义万相任务状态失败 (${attempt}/${maxAttempts}):`, error);
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        }
    }
    // 超时处理
    console.error(`通义万相任务 ${taskId} 轮询超时`);
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
        where: {
            id: segment.id
        },
        data: {
            status: 'failed',
            metadata: JSON.stringify({
                taskId: taskId,
                provider: 'tongyi',
                error: '轮询超时',
                timeoutAt: new Date().toISOString()
            })
        }
    });
}
// 通义万相文生视频API调用
async function callTongyiVideoAPI(config, prompt, segment) {
    try {
        // 内容安全检查
        const validation = validatePromptContent(prompt);
        if (!validation.isValid) {
            console.error(`片段 ${segment.segmentIndex} 内容检查失败:`, validation.reason);
            throw new Error(`内容检查失败: ${validation.reason}`);
        }
        // 优化提示词
        const optimizedPrompt = optimizePrompt(prompt);
        console.log(`片段 ${segment.segmentIndex} 提示词优化:`);
        console.log(`  原始: ${prompt.substring(0, 100)}...`);
        console.log(`  优化: ${optimizedPrompt.substring(0, 100)}...`);
        // 创建视频生成任务
        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json',
                'X-DashScope-Async': 'enable'
            },
            body: JSON.stringify({
                model: config.model,
                input: {
                    prompt: optimizedPrompt
                },
                parameters: {
                    size: '1280*720',
                    duration: segment.duration || 5,
                    prompt_extend: true // 开启智能改写
                }
            })
        });
        if (!response.ok) {
            throw new Error(`通义万相API调用失败: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        console.log('通义万相API响应:', JSON.stringify(result, null, 2));
        // 检查任务创建是否成功
        if (result.output && result.output.task_id) {
            // 启动轮询任务状态
            const taskId = result.output.task_id;
            console.log(`通义万相任务创建成功，任务ID: ${taskId}，开始轮询状态...`);
            // 异步轮询任务状态
            pollTongyiTaskStatus(config, taskId, segment);
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: 5,
                status: 'generating',
                taskId: taskId,
                provider: 'tongyi'
            };
        } else {
            console.error('通义万相API返回格式异常，完整响应:', result);
            throw new Error(`通义万相API返回格式异常: ${JSON.stringify(result)}`);
        }
    } catch (error) {
        console.error('通义万相文生视频API调用失败:', error);
        throw error;
    }
}
// 豆包API直接使用endpoint ID作为模型名称
function getDoubaoModelName(endpointId) {
    // 直接返回endpoint ID，不需要映射
    return endpointId;
}
// 豆包文生视频API调用 (Seedance 1.0 Lite T2V)
async function callDoubaoTextToVideoAPI(config, prompt, segment) {
    try {
        console.log(`🎬 调用豆包文生视频API (T2V)，片段: ${segment.segmentIndex}`);
        // 构建文生视频请求内容
        const content = [
            {
                type: "text",
                text: prompt
            }
        ];
        console.log(`📝 文生视频生成，提示词长度: ${prompt.length}`);
        // 豆包文生视频API调用
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: getDoubaoModelName(config.model),
                content: content
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`豆包文生视频API调用失败: ${response.status} ${errorText}`);
            throw new Error(`豆包文生视频API调用失败: ${response.status} ${errorText}`);
        }
        const result = await response.json();
        console.log('豆包文生视频API响应:', JSON.stringify(result, null, 2));
        // 处理任务ID
        let taskId = null;
        if (result.data && result.data.task_id) {
            taskId = result.data.task_id;
        } else if (result.task_id) {
            taskId = result.task_id;
        } else if (result.id) {
            taskId = result.id;
        } else if (result.data && result.data.id) {
            taskId = result.data.id;
        }
        if (taskId) {
            console.log(`豆包文生视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`);
            // 启动轮询任务状态
            pollDoubaoTaskStatus(config, taskId, segment);
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 5,
                status: 'generating',
                taskId: taskId,
                provider: 'doubao-t2v'
            };
        }
        console.error('豆包文生视频API返回格式异常，完整响应:', result);
        throw new Error(`豆包文生视频API返回格式异常: ${JSON.stringify(result)}`);
    } catch (error) {
        console.error('豆包文生视频API调用失败:', error);
        throw error;
    }
}
// 豆包图生视频API调用 (Seedance 1.0 Lite I2V)
async function callDoubaoImageToVideoAPI(config, prompt, segment) {
    try {
        console.log(`🎬 调用豆包图生视频API，片段: ${segment.segmentIndex}`);
        // 获取角色参考图像作为输入图像
        const characterImages = await getCharacterReferenceImages(segment);
        if (characterImages.length === 0) {
            console.warn('⚠️ 图生视频需要输入图像，但未找到角色参考图像，回退到文生视频');
            // 获取T2V模型配置进行回退
            const t2vConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
                where: {
                    provider: 'doubao',
                    model: 'ep-20250624192345-5ccwj',
                    enabled: true
                }
            });
            if (!t2vConfig) {
                throw new Error('未找到可用的豆包T2V模型配置');
            }
            return await callDoubaoTextToVideoAPI(t2vConfig, prompt, segment);
        }
        // 验证图像URL是否有效
        const validImageUrl = await validateImageUrl(characterImages[0]);
        if (!validImageUrl) {
            console.warn('⚠️ 角色参考图像URL无效，回退到文生视频');
            // 获取T2V模型配置进行回退
            const t2vConfig = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].aIConfig.findFirst({
                where: {
                    provider: 'doubao',
                    model: 'ep-20250624192345-5ccwj',
                    enabled: true
                }
            });
            if (!t2vConfig) {
                throw new Error('未找到可用的豆包T2V模型配置');
            }
            return await callDoubaoTextToVideoAPI(t2vConfig, prompt, segment);
        }
        console.log(`🖼️ 使用参考图像进行图生视频: ${validImageUrl.substring(0, 100)}...`);
        // 构建图生视频请求内容 - 豆包I2V正确格式
        const content = [
            {
                type: "text",
                text: `${prompt} --ratio adaptive --dur ${segment.duration || 5}`
            },
            {
                type: "image_url",
                image_url: {
                    url: validImageUrl
                }
            }
        ];
        console.log(`🔄 图生视频生成，提示词长度: ${prompt.length}，输入图像: ${characterImages[0].substring(0, 100)}...`);
        // 豆包图生视频API调用
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: getDoubaoModelName(config.model),
                content: content
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`豆包图生视频API调用失败: ${response.status} ${errorText}`);
            throw new Error(`豆包图生视频API调用失败: ${response.status} ${errorText}`);
        }
        const result = await response.json();
        console.log('豆包图生视频API响应:', JSON.stringify(result, null, 2));
        // 处理任务ID
        let taskId = null;
        if (result.data && result.data.task_id) {
            taskId = result.data.task_id;
        } else if (result.task_id) {
            taskId = result.task_id;
        } else if (result.id) {
            taskId = result.id;
        } else if (result.data && result.data.id) {
            taskId = result.data.id;
        }
        if (taskId) {
            console.log(`豆包图生视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`);
            // 启动轮询任务状态（复用现有的轮询函数）
            pollDoubaoTaskStatus(config, taskId, segment);
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 5,
                status: 'generating',
                taskId: taskId,
                provider: 'doubao-i2v',
                inputImage: validImageUrl
            };
        }
        console.error('豆包图生视频API返回格式异常，完整响应:', result);
        throw new Error(`豆包图生视频API返回格式异常: ${JSON.stringify(result)}`);
    } catch (error) {
        console.error('豆包图生视频API调用失败:', error);
        throw error;
    }
}
// 豆包视频API调用
async function callDoubaoVideoAPI(config, prompt, segment) {
    try {
        console.log(`🎬 调用豆包视频API，片段: ${segment.segmentIndex}`);
        // 恢复视觉约束功能 - 双重约束系统核心
        const characterImages = await getCharacterReferenceImages(segment);
        // 构建内容数组（文本+图像双重约束）
        const content = [
            {
                type: "text",
                text: `${prompt}` // 文本约束
            }
        ];
        // 添加视觉约束（参考图像）
        if (characterImages.length > 0) {
            console.log(`🖼️ 应用视觉约束：${characterImages.length} 个角色参考图像`);
            content.push({
                type: "image_url",
                image_url: {
                    url: characterImages[0] // 使用主要角色的参考图像
                }
            });
        } else {
            console.log(`📝 仅使用文本约束（未找到角色参考图像）`);
        }
        console.log(`🔒 双重约束生成视频，提示词长度: ${prompt.length}，参考图像: ${characterImages.length}个`);
        // 豆包视频生成API调用 - 使用正确的视频生成端点
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: getDoubaoModelName(config.model),
                content: content
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`豆包API调用失败: ${response.status} ${errorText}`);
            throw new Error(`豆包API调用失败: ${response.status} ${errorText}`);
        }
        const result = await response.json();
        console.log('豆包API响应:', JSON.stringify(result, null, 2));
        // 豆包视频生成API返回任务ID - 支持多种格式
        let taskId = null;
        if (result.data && result.data.task_id) {
            taskId = result.data.task_id;
        } else if (result.task_id) {
            taskId = result.task_id;
        } else if (result.id) {
            // 豆包API返回的是id字段
            taskId = result.id;
        } else if (result.data && result.data.id) {
            taskId = result.data.id;
        }
        if (taskId) {
            console.log(`豆包视频任务创建成功，任务ID: ${taskId}，开始轮询状态...`);
            // 启动轮询任务状态
            pollDoubaoTaskStatus(config, taskId, segment);
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 5,
                status: 'generating',
                taskId: taskId,
                provider: 'doubao'
            };
        }
        console.error('豆包API返回格式异常，完整响应:', result);
        throw new Error(`豆包API返回格式异常: ${JSON.stringify(result)}`);
    } catch (error) {
        console.error('豆包视频API调用失败:', error);
        throw error;
    }
}
// 轮询豆包任务状态
async function pollDoubaoTaskStatus(config, taskId, segment) {
    const maxAttempts = 120;
    let pollInterval = 3000;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            console.log(`轮询豆包任务状态 (${attempt}/${maxAttempts}): ${taskId}`);
            // 查询豆包任务状态
            const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${config.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                console.error(`轮询请求失败: ${response.status} ${response.statusText}`);
                await new Promise((resolve)=>setTimeout(resolve, pollInterval));
                continue;
            }
            const result = await response.json();
            console.log(`豆包任务状态响应:`, JSON.stringify(result, null, 2));
            // 豆包API实际响应格式：{ "id": "xxx", "status": "succeeded", "content": { "video_url": "xxx" } }
            const status = result.status;
            if (status === 'succeeded' || status === 'completed') {
                // 任务完成
                const videoUrl = result.content?.video_url || result.video_url;
                if (videoUrl) {
                    // 尝试获取视频的实际时长
                    let actualDuration = segment.duration || 5;
                    try {
                        actualDuration = await getVideoActualDuration(videoUrl);
                        console.log(`📏 获取到视频实际时长: ${actualDuration}秒 (预期: ${segment.duration}秒)`);
                    } catch (error) {
                        console.warn('获取视频实际时长失败，使用预设时长:', error.message);
                    }
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                        where: {
                            id: segment.id
                        },
                        data: {
                            videoUrl: videoUrl,
                            status: 'completed',
                            duration: actualDuration,
                            metadata: JSON.stringify({
                                taskId: taskId,
                                provider: 'doubao',
                                completedAt: new Date().toISOString(),
                                totalAttempts: attempt,
                                responseFormat: 'direct',
                                expectedDuration: segment.duration,
                                actualDuration: actualDuration,
                                durationMatch: Math.abs(actualDuration - (segment.duration || 5)) <= 2
                            })
                        }
                    });
                    console.log(`🎉 片段 ${segment.segmentIndex} 豆包视频生成完成: ${videoUrl} (时长: ${actualDuration}秒)`);
                    return;
                } else {
                    console.error('❌ 任务成功但未找到视频URL，完整响应:', result);
                }
            } else if (status === 'failed' || status === 'error') {
                // 任务失败
                const errorMessage = result.message || result.error || '任务失败';
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
                    where: {
                        id: segment.id
                    },
                    data: {
                        status: 'failed',
                        metadata: JSON.stringify({
                            taskId: taskId,
                            provider: 'doubao',
                            error: errorMessage,
                            failedAt: new Date().toISOString(),
                            totalAttempts: attempt,
                            fullResponse: result
                        })
                    }
                });
                console.error(`❌ 片段 ${segment.segmentIndex} 豆包视频生成失败: ${errorMessage}`);
                return;
            } else if (status === 'pending' || status === 'running' || status === 'processing') {
                // 状态为进行中，继续轮询
                console.log(`⏳ 任务状态: ${status}, 继续轮询...`);
            } else {
                console.log(`❓ 未知任务状态: ${status}, 继续轮询...`);
            }
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        } catch (error) {
            console.error(`轮询豆包任务状态失败 (${attempt}/${maxAttempts}):`, error);
            await new Promise((resolve)=>setTimeout(resolve, pollInterval));
        }
    }
    // 超时处理
    console.error(`豆包任务 ${taskId} 轮询超时`);
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
        where: {
            id: segment.id
        },
        data: {
            status: 'failed',
            metadata: JSON.stringify({
                taskId: taskId,
                provider: 'doubao',
                error: '轮询超时',
                timeoutAt: new Date().toISOString()
            })
        }
    });
}
// MiniMax Hailuo API调用
async function callMinimaxHailuoAPI(config, prompt, segment) {
    try {
        const response = await fetch('https://api.minimaxi.com/v1/video_generation', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: config.model,
                prompt: prompt,
                video_setting: {
                    video_duration: Math.min(segment.duration || 6, 6),
                    video_aspect_ratio: '16:9'
                }
            })
        });
        if (!response.ok) {
            throw new Error(`MiniMax API调用失败: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        console.log('MiniMax API响应:', JSON.stringify(result, null, 2));
        // 根据MiniMax API的响应格式处理结果
        if (result.task_id) {
            // 异步生成，需要轮询状态
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 15,
                status: 'generating',
                taskId: result.task_id
            };
        } else if (result.video_url) {
            // 同步生成完成
            return {
                videoUrl: result.video_url,
                thumbnailUrl: result.thumbnail_url || null,
                duration: result.duration || segment.duration || 15,
                status: 'completed'
            };
        } else if (result.data && result.data.task_id) {
            // 可能的嵌套格式
            return {
                videoUrl: null,
                thumbnailUrl: null,
                duration: segment.duration || 15,
                status: 'generating',
                taskId: result.data.task_id
            };
        } else {
            console.error('MiniMax API返回格式异常，完整响应:', result);
            throw new Error(`MiniMax API返回格式异常: ${JSON.stringify(result)}`);
        }
    } catch (error) {
        console.error('MiniMax Hailuo API调用失败:', error);
        throw error;
    }
}
// 检查角色图像先决条件
async function checkCharacterImagePrerequisites(projectId) {
    try {
        console.log(`🔍 检查项目 ${projectId} 的角色图像先决条件`);
        // 获取项目的所有角色
        const characters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
            where: {
                projectId
            },
            select: {
                id: true,
                name: true,
                generatedImages: true
            }
        });
        if (characters.length === 0) {
            return {
                success: false,
                error: '项目中没有角色信息，请先创建角色',
                missingCharacters: []
            };
        }
        console.log(`📋 找到 ${characters.length} 个角色`);
        // 检查每个角色是否有参考图像
        const missingCharacters = [];
        for (const character of characters){
            console.log(`🎭 检查角色: ${character.name}`);
            if (!character.generatedImages) {
                console.log(`❌ 角色 ${character.name} 没有生成图像`);
                missingCharacters.push(character.name);
                continue;
            }
            try {
                const images = JSON.parse(character.generatedImages);
                const hasAllImages = images.front && images.side && images.back;
                if (!hasAllImages) {
                    console.log(`❌ 角色 ${character.name} 的图像不完整`);
                    missingCharacters.push(character.name);
                } else {
                    console.log(`✅ 角色 ${character.name} 有完整的参考图像`);
                }
            } catch (e) {
                console.log(`❌ 角色 ${character.name} 的图像数据解析失败`);
                missingCharacters.push(character.name);
            }
        }
        if (missingCharacters.length > 0) {
            const errorMessage = `以下角色缺少参考图像，请先生成角色形象：${missingCharacters.join('、')}`;
            console.log(`❌ 先决条件检查失败: ${errorMessage}`);
            return {
                success: false,
                error: errorMessage,
                missingCharacters
            };
        }
        console.log('✅ 所有角色都有完整的参考图像');
        return {
            success: true
        };
    } catch (error) {
        console.error('检查角色图像先决条件失败:', error);
        return {
            success: false,
            error: '检查角色图像时发生错误，请稍后重试'
        };
    }
}
// 获取角色参考图像（视觉约束）- 增强版
async function getCharacterReferenceImages(segment) {
    try {
        console.log(`🔍 开始获取片段 ${segment.segmentIndex || 'unknown'} 的角色参考图像`);
        const characterImages = [];
        let characterNames = [];
        // 1. 从segment的metadata中获取角色名称
        if (segment.metadata) {
            try {
                const metadata = typeof segment.metadata === 'string' ? JSON.parse(segment.metadata) : segment.metadata;
                characterNames = metadata.characters || [];
                console.log(`📋 从metadata获取角色: ${characterNames.join(', ')}`);
            } catch (e) {
                console.warn('解析segment metadata失败:', e);
            }
        }
        // 2. 智能角色名称提取（从文本中识别）
        if (characterNames.length === 0) {
            characterNames = await extractCharacterNamesFromText(segment);
            console.log(`🤖 智能提取角色: ${characterNames.join(', ')}`);
        }
        // 3. 如果仍然没有角色，尝试获取项目的所有主要角色
        if (characterNames.length === 0) {
            characterNames = await getProjectMainCharacters(segment);
            console.log(`📚 获取项目主要角色: ${characterNames.join(', ')}`);
        }
        console.log(`🎭 最终确定角色列表: ${characterNames.join(', ')}`);
        // 4. 获取角色的最佳参考图像
        if (characterNames.length > 0) {
            const characters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
                where: {
                    name: {
                        in: characterNames
                    }
                }
            });
            for (const character of characters){
                const bestImage = await selectBestReferenceImage(character, segment);
                if (bestImage) {
                    characterImages.push(bestImage);
                    console.log(`✅ 为角色 ${character.name} 选择最佳参考图像`);
                } else {
                    console.warn(`⚠️ 角色 ${character.name} 没有可用的参考图像`);
                }
            }
        }
        console.log(`🖼️ 总共获取 ${characterImages.length} 个角色参考图像`);
        return characterImages;
    } catch (error) {
        console.error('获取角色参考图像失败:', error);
        return [];
    }
}
// 智能提取角色名称
async function extractCharacterNamesFromText(segment) {
    const text = `${segment.title || ''} ${segment.description || ''} ${segment.prompt || ''}`.toLowerCase();
    const characterNames = [];
    // 获取数据库中所有角色名称进行匹配
    try {
        const allCharacters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
            select: {
                name: true
            }
        });
        for (const character of allCharacters){
            if (text.includes(character.name.toLowerCase())) {
                characterNames.push(character.name);
            }
        }
    } catch (error) {
        console.warn('获取角色列表失败:', error);
        // 降级方案：硬编码常见角色名称
        const commonNames = [
            '张三',
            '李四',
            '王五',
            '赵六',
            '小明',
            '小红'
        ];
        for (const name of commonNames){
            if (text.includes(name.toLowerCase())) {
                characterNames.push(name);
            }
        }
    }
    return [
        ...new Set(characterNames)
    ] // 去重
    ;
}
// 获取项目主要角色
async function getProjectMainCharacters(segment) {
    try {
        // 尝试从segment中获取项目信息
        // 这里需要根据实际的数据结构调整
        const characters = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].character.findMany({
            take: 3,
            orderBy: {
                createdAt: 'asc'
            } // 按创建时间排序，假设先创建的是主要角色
        });
        return characters.map((c)=>c.name);
    } catch (error) {
        console.warn('获取项目主要角色失败:', error);
        return [];
    }
}
// 选择最佳参考图像
async function selectBestReferenceImage(character, segment) {
    try {
        if (!character.generatedImages) {
            return null;
        }
        const images = JSON.parse(character.generatedImages);
        // 解析参考图像集合
        const referenceSet = {
            standardPortrait: images.front,
            profileView: images.side,
            fullBody: images.back,
            expressions: {
                neutral: images.front
            },
            angles: {
                front: images.front,
                leftProfile: images.side,
                rightProfile: images.side,
                threeQuarter: images.front
            }
        };
        // 根据场景类型选择最佳图像
        const sceneType = segment.type || 'scene';
        const cameraAngle = segment.metadata?.cameraAngle || 'medium shot';
        // 选择策略
        if (cameraAngle.includes('close') || sceneType === 'dialogue') {
            // 特写镜头优先使用正面头像
            return referenceSet.standardPortrait || referenceSet.angles?.front;
        } else if (cameraAngle.includes('profile') || sceneType === 'profile') {
            // 侧面镜头使用侧面图像
            return referenceSet.profileView || referenceSet.angles?.leftProfile;
        } else if (cameraAngle.includes('wide') || sceneType === 'action') {
            // 广角镜头可以使用全身图像
            return referenceSet.fullBody || referenceSet.standardPortrait;
        } else {
            // 默认使用标准头像
            return referenceSet.standardPortrait || referenceSet.angles?.front || referenceSet.profileView;
        }
    } catch (error) {
        console.warn(`选择角色 ${character.name} 最佳参考图像失败:`, error);
        return null;
    }
}
// 等待片段完成生成
async function waitForSegmentCompletion(segmentId, taskId, maxWaitTime = 600000) {
    const startTime = Date.now();
    const checkInterval = 5000 // 每5秒检查一次
    ;
    console.log(`⏳ 开始等待片段 ${segmentId} 完成，任务ID: ${taskId}`);
    while(Date.now() - startTime < maxWaitTime){
        try {
            // 检查数据库中的片段状态
            const segment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.findUnique({
                where: {
                    id: segmentId
                }
            });
            if (!segment) {
                console.error(`片段 ${segmentId} 不存在`);
                return false;
            }
            if (segment.status === 'completed') {
                console.log(`✅ 片段 ${segmentId} 已完成生成`);
                return true;
            } else if (segment.status === 'failed') {
                console.log(`❌ 片段 ${segmentId} 生成失败`);
                return false;
            }
            // 继续等待
            console.log(`⏳ 片段 ${segmentId} 仍在生成中，继续等待... (已等待 ${Math.round((Date.now() - startTime) / 1000)}s)`);
            await new Promise((resolve)=>setTimeout(resolve, checkInterval));
        } catch (error) {
            console.error(`检查片段 ${segmentId} 状态失败:`, error);
            await new Promise((resolve)=>setTimeout(resolve, checkInterval));
        }
    }
    console.log(`⏰ 片段 ${segmentId} 等待超时 (${maxWaitTime / 1000}s)`);
    return false;
}
// 基础一致性验证
async function validateBasicConsistency(generatedImageUrl, characterInfo, segmentInfo) {
    try {
        console.log(`🔍 开始一致性验证，角色: ${characterInfo.name}`);
        const result = {
            textConsistency: 0,
            imageConsistency: 0,
            overallConsistency: 0,
            passed: false,
            issues: [],
            suggestions: []
        };
        // 1. 文本一致性验证
        result.textConsistency = await validateTextConsistency(characterInfo, segmentInfo);
        // 2. 图像一致性验证（简化版）
        result.imageConsistency = await validateImageConsistency(generatedImageUrl, characterInfo);
        // 3. 计算综合评分
        const textWeight = 0.6 // 文本约束权重
        ;
        const imageWeight = 0.4 // 图像约束权重
        ;
        result.overallConsistency = result.textConsistency * textWeight + result.imageConsistency * imageWeight;
        // 4. 判断是否通过验证
        const threshold = 0.7 // 一致性阈值
        ;
        result.passed = result.overallConsistency >= threshold;
        // 5. 生成问题和建议
        if (!result.passed) {
            if (result.textConsistency < 0.6) {
                result.issues.push('文本约束一致性不足');
                result.suggestions.push('增强角色DNA描述的详细程度');
            }
            if (result.imageConsistency < 0.6) {
                result.issues.push('图像约束一致性不足');
                result.suggestions.push('提供更清晰的角色参考图像');
            }
        }
        console.log(`📊 一致性验证结果: 文本${result.textConsistency.toFixed(2)}, 图像${result.imageConsistency.toFixed(2)}, 综合${result.overallConsistency.toFixed(2)}`);
        return result;
    } catch (error) {
        console.error('一致性验证失败:', error);
        return {
            textConsistency: 0,
            imageConsistency: 0,
            overallConsistency: 0,
            passed: false,
            issues: [
                '验证过程出错'
            ],
            suggestions: [
                '请检查输入数据的完整性'
            ]
        };
    }
}
// 文本一致性验证
async function validateTextConsistency(characterInfo, segmentInfo) {
    try {
        let score = 0.5 // 基础分数
        ;
        // 检查角色DNA完整性
        const dna = parseCharacterDNA(characterInfo);
        if (dna) {
            score += 0.2 // 有详细DNA加分
            ;
            // 检查关键特征是否在提示词中体现
            const prompt = segmentInfo.prompt || '';
            const lowerPrompt = prompt.toLowerCase();
            // 检查面部特征
            if (lowerPrompt.includes(dna.facial.faceShape.toLowerCase())) score += 0.1;
            if (lowerPrompt.includes(dna.facial.eyeColor.toLowerCase())) score += 0.1;
            // 检查独特标识
            for (const identifier of dna.uniqueIdentifiers){
                if (lowerPrompt.includes(identifier.toLowerCase())) {
                    score += 0.05;
                }
            }
        }
        // 检查角色名称是否正确
        if (characterInfo.name && segmentInfo.prompt?.includes(characterInfo.name)) {
            score += 0.1;
        }
        return Math.min(score, 1.0) // 最高1.0分
        ;
    } catch (error) {
        console.warn('文本一致性验证失败:', error);
        return 0.3 // 默认低分
        ;
    }
}
// 图像一致性验证（简化版）
async function validateImageConsistency(generatedImageUrl, characterInfo) {
    try {
        // 简化版：基于角色是否有参考图像来评分
        if (!characterInfo.generatedImages) {
            return 0.3 // 没有参考图像，低分
            ;
        }
        try {
            const images = JSON.parse(characterInfo.generatedImages);
            let score = 0.5 // 基础分数
            ;
            // 检查参考图像的完整性
            if (images.front) score += 0.2;
            if (images.side) score += 0.1;
            if (images.back) score += 0.1;
            // 如果有生成的图像URL，假设一致性较好
            if (generatedImageUrl && generatedImageUrl.length > 0) {
                score += 0.1;
            }
            return Math.min(score, 1.0);
        } catch (e) {
            console.warn('解析角色参考图像失败:', e);
            return 0.4;
        }
    } catch (error) {
        console.warn('图像一致性验证失败:', error);
        return 0.3 // 默认低分
        ;
    }
}
// 保存一致性验证记录
async function saveConsistencyValidation(characterId, episodeId1, result, generatedImageUrl) {
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].consistencyValidation.create({
            data: {
                characterId,
                episodeId: episodeId1,
                textConsistencyScore: result.textConsistency,
                imageConsistencyScore: result.imageConsistency,
                overallScore: result.overallConsistency,
                validationDetails: JSON.stringify({
                    passed: result.passed,
                    issues: result.issues,
                    suggestions: result.suggestions
                }),
                issuesFound: JSON.stringify(result.issues),
                textWeight: 0.6,
                imageWeight: 0.4,
                generatedImageUrl: generatedImageUrl
            }
        });
        console.log(`💾 已保存角色 ${characterId} 的一致性验证记录`);
    } catch (error) {
        console.error('保存一致性验证记录失败:', error);
    }
}
// 获取视频实际时长
async function getVideoActualDuration(videoUrl) {
    try {
        console.log(`📏 尝试获取视频实际时长: ${videoUrl.substring(0, 100)}...`);
        // 方法1: 尝试通过HEAD请求获取Content-Range或其他元数据
        const headResponse = await fetch(videoUrl, {
            method: 'HEAD',
            headers: {
                'Range': 'bytes=0-1' // 请求很小的范围来获取元数据
            }
        });
        // 检查响应头中是否有时长信息
        const contentRange = headResponse.headers.get('content-range');
        const contentLength = headResponse.headers.get('content-length');
        if (contentRange) {
            console.log(`📋 Content-Range: ${contentRange}`);
        }
        if (contentLength) {
            const fileSizeBytes = parseInt(contentLength);
            console.log(`📋 文件大小: ${(fileSizeBytes / 1024 / 1024).toFixed(2)} MB`);
            // 基于文件大小估算时长（粗略估算）
            // 假设1MB约等于1秒的高质量视频
            const estimatedDuration = Math.round(fileSizeBytes / (1024 * 1024));
            if (estimatedDuration > 0 && estimatedDuration <= 60) {
                console.log(`📏 基于文件大小估算时长: ${estimatedDuration}秒`);
                return estimatedDuration;
            }
        }
        // 方法2: 如果无法从HTTP头获取，尝试下载一小部分文件分析
        // 这里暂时返回默认值，避免下载整个文件
        console.log('📏 无法从HTTP头获取时长信息，使用默认估算');
        // 基于URL模式的启发式估算
        if (videoUrl.includes('doubao')) {
            // 豆包生成的视频通常是5-10秒
            return 8 // 默认8秒
            ;
        }
        return 5 // 默认5秒
        ;
    } catch (error) {
        console.warn('获取视频实际时长失败:', error.message);
        return 5 // 默认5秒
        ;
    }
}
// 高级视频时长检测（可选，需要额外依赖）
async function getVideoActualDurationAdvanced(videoUrl) {
    try {
        // 这个函数可以在未来实现更高级的视频分析
        // 例如使用ffprobe或其他视频分析工具
        // 目前返回基础估算
        console.log('📏 使用高级时长检测（暂未实现）');
        return 5;
    } catch (error) {
        console.warn('高级视频时长检测失败:', error.message);
        return 5;
    }
}
// SkyReels-V2 API调用
async function callSkyReelsVideoAPI(config, prompt, segment) {
    try {
        console.log(`🎬 开始SkyReels-V2视频生成，片段 ${segment.segmentIndex}`);
        console.log(`📝 提示词: ${prompt.substring(0, 200)}...`);
        // 导入SkyReels客户端
        const { SkyReelsClient } = await __turbopack_context__.r("[project]/src/lib/skyreels.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        // 创建SkyReels客户端
        const skyreelsClient = new SkyReelsClient({
            id: 'skyreels-temp',
            provider: 'skyreels',
            apiKey: config.apiKey,
            model: config.model,
            temperature: 0.7,
            maxTokens: 4000,
            topP: 0.9,
            status: 'connected',
            createdAt: new Date(),
            updatedAt: new Date()
        });
        // 计算视频参数
        const duration = Math.min(segment.duration || 10, 30) // SkyReels支持最长30秒
        ;
        const numFrames = duration <= 4 ? 97 : duration <= 10 ? 257 : duration <= 15 ? 377 : 737;
        console.log(`⏱️ 视频时长: ${duration}秒, 帧数: ${numFrames}`);
        // 生成视频
        const videoPath = await skyreelsClient.generateAndWait({
            prompt: prompt,
            num_frames: numFrames,
            guidance_scale: 6.0,
            fps: 24,
            resolution: '540P'
        }, 1800000, 5000) // 30分钟超时，5秒轮询间隔
        ;
        if (!videoPath) {
            throw new Error('SkyReels视频生成失败：未返回视频路径');
        }
        console.log(`✅ SkyReels视频生成完成: ${videoPath}`);
        // 更新片段状态
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segment.id
            },
            data: {
                videoUrl: videoPath,
                status: 'completed',
                duration: duration,
                metadata: JSON.stringify({
                    provider: 'skyreels',
                    model: config.model,
                    completedAt: new Date().toISOString(),
                    numFrames: numFrames,
                    guidanceScale: 6.0,
                    fps: 24,
                    resolution: '540P',
                    localGeneration: true
                })
            }
        });
        console.log(`💾 片段 ${segment.segmentIndex} SkyReels视频生成记录已更新`);
        return {
            videoUrl: videoPath,
            duration: duration,
            status: 'completed',
            provider: 'skyreels'
        };
    } catch (error) {
        console.error(`❌ SkyReels视频生成失败，片段 ${segment.segmentIndex}:`, error);
        // 更新片段状态为失败
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].videoSegment.update({
            where: {
                id: segment.id
            },
            data: {
                status: 'failed',
                metadata: JSON.stringify({
                    provider: 'skyreels',
                    error: error.message,
                    failedAt: new Date().toISOString()
                })
            }
        });
        throw error;
    }
}
// 验证图像URL是否有效
async function validateImageUrl(imageUrl) {
    try {
        if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim() === '') {
            return false;
        }
        // 检查URL格式
        try {
            new URL(imageUrl);
        } catch (e) {
            console.warn('图像URL格式无效:', imageUrl);
            return false;
        }
        // 发送HEAD请求检查图像是否存在
        const response = await fetch(imageUrl, {
            method: 'HEAD',
            timeout: 10000 // 10秒超时
        });
        if (!response.ok) {
            console.warn(`图像URL返回错误状态: ${response.status}`);
            return false;
        }
        // 检查内容类型是否为图像
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.startsWith('image/')) {
            console.warn(`URL内容类型不是图像: ${contentType}`);
            return false;
        }
        return true;
    } catch (error) {
        console.warn('验证图像URL失败:', error.message);
        return false;
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9b30f393._.js.map