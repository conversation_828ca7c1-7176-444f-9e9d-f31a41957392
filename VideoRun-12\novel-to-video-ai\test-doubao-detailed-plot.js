// 测试豆包模型在详细剧情分析中的应用
async function testDoubaoDetailedPlot() {
  try {
    console.log('🎭 测试豆包模型详细剧情分析...');
    
    // 1. 首先确保有豆包模型配置
    console.log('\n📝 1. 检查豆包模型配置...');
    
    const modelsResponse = await fetch('http://localhost:3000/api/models');
    if (!modelsResponse.ok) {
      throw new Error('无法获取模型列表');
    }
    
    const modelsResult = await modelsResponse.json();
    const doubaoModels = modelsResult.data.filter(model => 
      model.provider === 'doubao' && model.enabled
    );
    
    if (doubaoModels.length === 0) {
      console.log('❌ 没有找到启用的豆包模型，请先配置豆包模型');
      return;
    }
    
    console.log('✅ 找到启用的豆包模型:', doubaoModels.length, '个');
    doubaoModels.forEach(model => {
      console.log(`   - ${model.name} (${model.model})`);
    });
    
    // 2. 获取测试项目和剧集
    console.log('\n📚 2. 获取测试项目和剧集...');
    
    const projectsResponse = await fetch('http://localhost:3000/api/projects');
    if (!projectsResponse.ok) {
      throw new Error('无法获取项目列表');
    }
    
    const projectsResult = await projectsResponse.json();
    if (!projectsResult.success || projectsResult.data.length === 0) {
      console.log('❌ 没有找到测试项目，请先创建项目');
      return;
    }
    
    const testProject = projectsResult.data[0];
    console.log('✅ 使用测试项目:', testProject.name);
    
    // 获取项目的剧集
    const episodesResponse = await fetch(`http://localhost:3000/api/projects/${testProject.id}/episodes`);
    if (!episodesResponse.ok) {
      throw new Error('无法获取剧集列表');
    }
    
    const episodesResult = await episodesResponse.json();
    if (!episodesResult.success || episodesResult.data.length === 0) {
      console.log('❌ 项目中没有剧集，请先分析小说内容');
      return;
    }
    
    const testEpisode = episodesResult.data[0];
    console.log('✅ 使用测试剧集:', testEpisode.title);
    
    // 3. 测试豆包模型的详细剧情分析
    console.log('\n🔍 3. 测试豆包模型详细剧情分析...');
    
    const analysisResponse = await fetch(`http://localhost:3000/api/ai/analyze-detailed-plot`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        episodeId: testEpisode.id,
        customPrompt: '请特别关注角色的一致性特征和场景的视觉元素'
      })
    });
    
    console.log('📊 分析响应状态:', analysisResponse.status);
    
    if (!analysisResponse.ok) {
      const errorText = await analysisResponse.text();
      console.log('❌ 详细剧情分析失败:', errorText);
      return;
    }
    
    const analysisResult = await analysisResponse.json();
    
    if (analysisResult.success) {
      console.log('✅ 豆包模型详细剧情分析成功！');
      
      const data = analysisResult.data;
      
      // 显示分析结果摘要
      console.log('\n📋 分析结果摘要:');
      console.log('🎭 角色数量:', data.detailedCharacters ? JSON.parse(data.detailedCharacters).length : 0);
      console.log('🏞️ 场景数量:', data.detailedScenes ? JSON.parse(data.detailedScenes).length : 0);
      console.log('📖 情节序列数量:', data.plotSequences ? JSON.parse(data.plotSequences).length : 0);
      console.log('💭 情感弧线:', data.emotionalArc ? '已生成' : '未生成');
      console.log('📝 生成提示词:', data.generatedPrompt ? '已生成' : '未生成');
      
      // 显示一致性增强功能
      if (data.detailedCharacters) {
        const characters = JSON.parse(data.detailedCharacters);
        console.log('\n🎯 一致性增强功能验证:');
        
        characters.forEach((char, index) => {
          console.log(`  ${index + 1}. ${char.name}`);
          if (char.isKnownCharacter !== undefined) {
            console.log(`     已知角色: ${char.isKnownCharacter ? '是' : '否'}`);
          }
          if (char.consistencyInfo) {
            console.log(`     一致性匹配度: ${(char.consistencyInfo.consistencyMatch * 100).toFixed(1)}%`);
            if (char.consistencyInfo.differences && char.consistencyInfo.differences.length > 0) {
              console.log(`     发现差异: ${char.consistencyInfo.differences.join(', ')}`);
            }
          }
        });
      }
      
      // 显示生成的提示词预览
      if (data.generatedPrompt) {
        console.log('\n📝 生成的视频提示词预览:');
        const promptPreview = data.generatedPrompt.substring(0, 200);
        console.log(`   "${promptPreview}${data.generatedPrompt.length > 200 ? '...' : ''}"`);
        console.log(`   总长度: ${data.generatedPrompt.length} 字符`);
      }
      
    } else {
      console.log('❌ 详细剧情分析失败:', analysisResult.error);
    }
    
    // 4. 测试一致性视频生成（如果有豆包视频模型）
    const videoModel = doubaoModels.find(model => 
      model.model.includes('seedance') || model.model.includes('video')
    );
    
    if (videoModel) {
      console.log('\n🎬 4. 测试豆包一致性视频生成...');
      
      const videoResponse = await fetch(`http://localhost:3000/api/ai/generate-video-with-consistency`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId: testEpisode.id,
          consistencyMode: 'balanced',
          includeReferenceImages: false,
          style: 'cinematic',
          quality: 'high'
        })
      });
      
      console.log('📊 视频生成响应状态:', videoResponse.status);
      
      if (videoResponse.ok) {
        const videoResult = await videoResponse.json();
        if (videoResult.success) {
          console.log('✅ 豆包一致性视频生成配置成功！');
          console.log('🆔 生成ID:', videoResult.data.generationId);
          console.log('🎯 一致性模式:', videoResult.data.consistencyMode);
          console.log('👥 角色数量:', videoResult.data.charactersCount);
        } else {
          console.log('❌ 视频生成失败:', videoResult.error);
        }
      } else {
        const errorText = await videoResponse.text();
        console.log('❌ 视频生成请求失败:', errorText);
      }
    } else {
      console.log('\n⏭️ 4. 跳过视频生成测试（没有找到视频生成模型）');
    }
    
    console.log('\n🎉 豆包模型详细剧情分析测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoDetailedPlot();
