// Generated by dts-bundle-generator v9.3.1

import { <PERSON><PERSON><PERSON> } from 'node:buffer';

export declare const Personalities: string[];
export declare const Categories: string[];
export type Personality = typeof Personalities[number];
export type Category = typeof Categories[number];
export interface Voice {
	Name: string;
	ShortName: string;
	FriendlyName: string;
	Gender: "Male" | "Female";
	Locale: string;
	VoiceTag: {
		ContentCategories: Category[];
		VoicePersonalities: Personality[];
	};
}
export declare function getVoices(): Promise<Voice[]>;
export type options = Partial<{
	voice: string;
	volume: string;
	rate: string;
	pitch: string;
}>;
export declare function tts(text: string, options?: options): Promise<Buffer>;
export declare function ttsSave(text: string, file: fs.PathOrFileDescriptor, options?: options): Promise<void>;

export {};
