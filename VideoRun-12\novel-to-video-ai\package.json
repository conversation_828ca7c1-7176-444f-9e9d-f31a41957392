{"name": "novel-to-video-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@headlessui/react": "^2.2.4", "@prisma/client": "^6.9.0", "fluent-ffmpeg": "^2.1.3", "lucide-react": "^0.515.0", "mammoth": "^1.9.1", "next": "15.3.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}