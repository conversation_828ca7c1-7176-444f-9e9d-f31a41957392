'use client'

import { useState } from 'react'
import { Settings, CheckCircle, AlertCircle } from 'lucide-react'
import Layout from '@/components/Layout'
import AIConfigPanel from '@/components/AIConfigPanel'
import { AIConfig } from '@/types'

export default function ModelsPage() {
  const [savedConfig, setSavedConfig] = useState<AIConfig | null>(null)
  const [showSuccess, setShowSuccess] = useState(false)

  // 处理配置保存
  const handleConfigSaved = (config: AIConfig) => {
    setSavedConfig(config)
    setShowSuccess(true)
    
    // 3秒后隐藏成功提示
    setTimeout(() => {
      setShowSuccess(false)
    }, 3000)
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* 页面头部 */}
        <div>
          <div className="flex items-center mb-4">
            <Settings className="text-purple-600 mr-3" size={32} />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">模型配置</h1>
              <p className="mt-1 text-sm text-gray-600">
                配置AI大模型，用于小说分析和视频脚本生成
              </p>
            </div>
          </div>
        </div>

        {/* 成功提示 */}
        {showSuccess && (
          <div className="rounded-md bg-green-50 p-4 border border-green-200">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  配置保存成功！
                </h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>AI模型配置已保存，现在可以开始创建项目并分析小说了。</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* AI配置面板 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              大模型配置
            </h2>
            <p className="mt-1 text-sm text-gray-600">
              选择并配置您要使用的AI模型
            </p>
          </div>
          
          <div className="p-6">
            <AIConfigPanel onConfigSaved={handleConfigSaved} />
          </div>
        </div>

        {/* 配置说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                配置说明
              </h3>
              <div className="text-sm text-blue-700 space-y-2">
                <p><strong>DeepSeek模型选择：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><strong>DeepSeek Reasoner</strong> - 专业推理模型，适合小说分析、角色提取和剧情分析</li>
                </ul>
                <p className="mt-3"><strong>豆包模型选择：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><strong>豆包 Seedance 1.0 Pro</strong> - 专业视频生成模型，支持高质量文生视频</li>
                </ul>
                <p className="mt-3"><strong>参数调整：</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li><strong>温度(Temperature)</strong> - 控制输出的随机性，0.7为推荐值</li>
                  <li><strong>最大Token数</strong> - 控制单次输出长度，4000为推荐值</li>
                  <li><strong>Top P</strong> - 控制输出的多样性，0.9为推荐值</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 使用流程 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              使用流程
            </h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium">
                    1
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">配置AI模型</h3>
                  <p className="text-sm text-gray-600">选择模型提供商，输入API密钥，测试连接</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium">
                    2
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">创建项目</h3>
                  <p className="text-sm text-gray-600">在项目页面创建新项目，上传小说文件</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium">
                    3
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">AI分析</h3>
                  <p className="text-sm text-gray-600">AI自动分析小说，提取角色和剧集信息</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium">
                    4
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">生成视频脚本</h3>
                  <p className="text-sm text-gray-600">基于多维度信息生成专业视频脚本</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 当前配置状态 */}
        {savedConfig && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                当前配置
              </h2>
            </div>
            
            <div className="p-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">提供商</dt>
                  <dd className="mt-1 text-sm text-gray-900">{savedConfig.provider}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">模型</dt>
                  <dd className="mt-1 text-sm text-gray-900">{savedConfig.model}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">温度</dt>
                  <dd className="mt-1 text-sm text-gray-900">{savedConfig.temperature}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">最大Token数</dt>
                  <dd className="mt-1 text-sm text-gray-900">{savedConfig.maxTokens}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">状态</dt>
                  <dd className="mt-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      savedConfig.status === 'connected' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {savedConfig.status === 'connected' ? '已连接' : '未测试'}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">最后更新</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(savedConfig.updatedAt).toLocaleString('zh-CN')}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
