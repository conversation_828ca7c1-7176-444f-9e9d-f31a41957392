// 角色一致性相关类型定义

// 详细角色DNA结构
export interface DetailedCharacterDNA {
  facial: {
    faceShape: string      // 脸型：圆脸/方脸/瓜子脸等
    eyeShape: string       // 眼型：丹凤眼/杏眼/桃花眼等
    eyeColor: string       // 眼色：黑色/棕色等
    noseShape: string      // 鼻型：高挺/小巧等
    mouthShape: string     // 嘴型：薄唇/厚唇等
    skinTone: string       // 肤色：白皙/小麦色等
    facialFeatures: string // 其他特征：酒窝/雀斑/痣等
  }
  physique: {
    height: string         // 身高描述
    build: string          // 体型：纤细/健壮等
    posture: string        // 体态特征
  }
  clothing: {
    style: string          // 服装风格
    colors: string[]       // 常用颜色
    accessories: string[]  // 配饰特征
  }
  uniqueIdentifiers: string[] // 独特标识符
  standardPrompt: string      // 标准化提示词
}

// 参考图像集合
export interface ReferenceImageSet {
  standardPortrait?: string    // 标准正面头像
  profileView?: string        // 侧面头像
  fullBody?: string          // 全身图
  expressions?: {
    neutral?: string         // 中性表情
    happy?: string          // 开心表情
    sad?: string            // 悲伤表情
    angry?: string          // 愤怒表情
  }
  angles?: {
    front?: string          // 正面
    leftProfile?: string    // 左侧面
    rightProfile?: string   // 右侧面
    threeQuarter?: string   // 四分之三角度
  }
  consistencyScore?: number   // 图像集一致性评分
}

// 一致性设置
export interface ConsistencySettings {
  textWeight: number          // 文本约束权重 (0.0-1.0)
  imageWeight: number         // 图像约束权重 (0.0-1.0)
  consistencyThreshold: number // 一致性阈值 (0.0-1.0)
  maxRetries: number          // 最大重试次数
  mode: 'plot' | 'consistency' | 'hybrid' // 一致性模式
}

// 增强的角色接口
export interface EnhancedCharacter {
  id: string
  projectId: string
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
  generatedImages?: string    // JSON格式的三视图

  // 一致性相关字段
  detailedDNA?: string        // JSON格式的DetailedCharacterDNA
  consistencyScore?: number   // 一致性评分
  consistencySettings?: string // JSON格式的ConsistencySettings
  referenceImages?: string    // JSON格式的ReferenceImageSet
  globalCharacterId?: string  // 全局角色ID
  isGlobalCharacter?: boolean // 是否为全局角色

  createdAt: Date
  updatedAt: Date
}

// 一致性验证结果
export interface ConsistencyResult {
  textConsistency: number     // 文本一致性评分
  imageConsistency: number    // 图像一致性评分
  overallConsistency: number  // 综合一致性评分
  passed: boolean            // 是否通过验证
  issues: string[]           // 发现的问题列表
  suggestions?: string[]     // 改进建议
}

// 一致性验证记录
export interface ConsistencyValidation {
  id: string
  characterId: string
  episodeId?: string
  textConsistencyScore?: number
  imageConsistencyScore?: number
  overallScore?: number
  validationDetails?: string  // JSON格式
  issuesFound?: string       // JSON格式
  textWeight?: number
  imageWeight?: number
  generatedImageUrl?: string
  createdAt: Date
}

// 场景上下文
export interface SceneContext {
  location: string           // 场景地点
  description: string        // 场景描述
  atmosphere: string         // 氛围
  timeOfDay?: string        // 时间
  lighting?: string         // 光线
  action: string            // 动作
  emotion: string           // 情感
}

// 视频帧
export interface VideoFrame {
  imageUrl: string          // 生成的图像URL
  duration: number          // 持续时间
  metadata?: any           // 元数据
}

// 生成策略
export interface GenerationStrategy {
  textWeight: number        // 文本约束权重
  imageWeight: number       // 图像约束权重
  consistencyThreshold: number // 一致性阈值
}

// 提取的特征
export interface ExtractedFeatures {
  faceShape: string
  eyeShape: string
  eyeColor: string
  noseShape: string
  mouthShape: string
  skinTone: string
  description: string       // 完整描述
}

// 一致性分析结果
export interface ConsistencyAnalysis {
  characterCount: number    // 角色数量
  averageScore: number     // 平均一致性评分
  issues: string[]         // 发现的问题
  recommendations: string[] // 建议
}

// 双重约束生成参数
export interface DualConstraintParams {
  characterDNA: DetailedCharacterDNA
  referenceImages: string[]
  sceneContext: SceneContext
  consistencySettings: ConsistencySettings
}

// API响应类型
export interface ConsistencyAPIResponse {
  success: boolean
  data?: any
  error?: string
  consistencyScore?: number
}
