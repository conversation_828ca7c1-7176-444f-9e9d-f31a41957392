// 测试三视图提示词完全分离
async function testThreeViewSeparation() {
  try {
    console.log('🎯 测试三视图提示词完全分离...');
    
    console.log('\n📝 用户要求:');
    console.log('   ✅ 正面：能看到完整的脸部');
    console.log('   ✅ 侧面：90度侧脸轮廓');
    console.log('   ✅ 背面：看不到脸，只看到后脑勺和背部');
    console.log('   ✅ 每个视图单独写提示词');
    console.log('   ✅ 只保持身材、服饰、颜色一致');
    
    // 模拟角色数据
    const testCharacter = {
      identity: '年轻战士',
      physique: '身材匀称，中等身高',
      hairstyle: '短发，黑色',
      clothing: '银色盔甲，蓝色披风',
      facial: '英俊的脸庞，坚毅的眼神'
    };
    
    // 模拟 extractConsistentFeatures 函数
    const extractConsistentFeatures = (character) => {
      const features = [];
      
      if (character.physique) features.push(character.physique);
      if (character.hairstyle) features.push(character.hairstyle);
      if (character.clothing) features.push(character.clothing);
      if (character.identity) features.push(character.identity);
      
      features.push('相同角色', '一致的特征', '同一人物');
      
      return features.join('，');
    };
    
    // 生成一致性特征
    const consistentFeatures = extractConsistentFeatures(testCharacter);
    console.log('\n🔧 一致性特征:', consistentFeatures);
    
    // 生成新的三视图提示词
    const newPrompts = {
      front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，${consistentFeatures}，角色设计图，白色背景，全身图，高质量，动漫风格`,
      
      side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，${consistentFeatures}，角色设计图，白色背景，全身图，高质量，动漫风格`,
      
      back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，${consistentFeatures}，角色设计图，白色背景，全身图，高质量，动漫风格`
    };
    
    console.log('\n✨ 新的三视图提示词:');
    console.log('\n📋 正面视图:');
    console.log(`   ${newPrompts.front}`);
    console.log('\n📋 侧面视图:');
    console.log(`   ${newPrompts.side}`);
    console.log('\n📋 背面视图:');
    console.log(`   ${newPrompts.back}`);
    
    // 分析关键差异
    console.log('\n🎯 关键差异分析:');
    
    const analyzePrompt = (viewType, prompt) => {
      const viewKeywords = {
        front: ['正面视图', '面向镜头', '完整的脸部', '清晰可见', '直视前方'],
        side: ['侧面视图', '90度侧身', '侧脸轮廓', '侧面剪影', '面向左侧或右侧'],
        back: ['背面视图', '背对镜头', '看不到脸部', '后脑勺', '背部', '背影']
      };
      
      const keywords = viewKeywords[viewType] || [];
      const matchedKeywords = keywords.filter(keyword => prompt.includes(keyword));
      
      return {
        viewType,
        uniqueKeywords: matchedKeywords,
        uniqueCount: matchedKeywords.length
      };
    };
    
    Object.entries(newPrompts).forEach(([viewType, prompt]) => {
      const analysis = analyzePrompt(viewType, prompt);
      console.log(`   ${viewType.toUpperCase()}: ${analysis.uniqueCount} 个独特关键词`);
      console.log(`     关键词: ${analysis.uniqueKeywords.join(', ')}`);
    });
    
    // 检查一致性特征
    console.log('\n🔄 一致性特征检查:');
    const consistentKeywords = ['年轻战士', '身材匀称', '短发，黑色', '银色盔甲', '相同角色'];
    
    Object.entries(newPrompts).forEach(([viewType, prompt]) => {
      const matchedConsistent = consistentKeywords.filter(keyword => prompt.includes(keyword));
      console.log(`   ${viewType.toUpperCase()}: ${matchedConsistent.length}/${consistentKeywords.length} 一致性特征`);
    });
    
    // 验证脸部可见性
    console.log('\n👤 脸部可见性验证:');
    
    const faceVisibility = {
      front: newPrompts.front.includes('完整的脸部') && newPrompts.front.includes('清晰可见'),
      side: newPrompts.side.includes('侧脸轮廓') && !newPrompts.side.includes('看不到脸'),
      back: newPrompts.back.includes('看不到脸部') && newPrompts.back.includes('背影')
    };
    
    Object.entries(faceVisibility).forEach(([viewType, isCorrect]) => {
      const status = isCorrect ? '✅' : '❌';
      const description = {
        front: '完整脸部可见',
        side: '侧脸轮廓可见',
        back: '脸部不可见'
      };
      console.log(`   ${status} ${viewType.toUpperCase()}: ${description[viewType]}`);
    });
    
    console.log('\n🎉 修复效果预期:');
    console.log('   ✅ 三个提示词完全不同');
    console.log('   ✅ 正面：能看到完整脸部');
    console.log('   ✅ 侧面：90度侧脸轮廓');
    console.log('   ✅ 背面：完全看不到脸');
    console.log('   ✅ 身材、服饰、发型保持一致');
    
    console.log('\n📋 使用说明:');
    console.log('   1. 重启开发服务器或刷新页面');
    console.log('   2. 重新生成三视图');
    console.log('   3. 观察三个视图的明显差异');
    
    console.log('\n✅ 三视图提示词完全分离修复完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testThreeViewSeparation();
