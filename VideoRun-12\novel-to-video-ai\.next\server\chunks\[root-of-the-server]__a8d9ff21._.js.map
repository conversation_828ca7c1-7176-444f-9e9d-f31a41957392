{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/utils/promptGenerator.ts"], "sourcesContent": ["// 角色形象提示词生成器\n// 专门用于从小说角色信息生成高质量的图像生成提示词\n\ninterface CharacterInfo {\n  name: string\n  identity?: string\n  personality?: string\n  physique?: string\n  facial?: string\n  hairstyle?: string\n  clothing?: string\n}\n\ninterface PromptOptions {\n  artStyle?: 'anime' | 'realistic' | 'semi-realistic' | 'concept-art'\n  viewType?: 'front' | 'side' | 'back' | 'three-quarter'\n  background?: 'white' | 'transparent' | 'simple' | 'detailed'\n  quality?: 'standard' | 'high' | 'masterpiece'\n  customEnhancement?: string\n}\n\n// 中文特征词汇到英文的映射\nconst FEATURE_MAPPING = {\n  // 年龄相关\n  '少女': 'young girl',\n  '青年女性': 'young woman', \n  '中年女性': 'middle-aged woman',\n  '少年': 'young boy',\n  '青年男性': 'young man',\n  '中年男性': 'middle-aged man',\n  \n  // 脸型\n  '瓜子脸': 'oval face',\n  '圆脸': 'round face',\n  '方脸': 'square face',\n  '长脸': 'long face',\n  '心形脸': 'heart-shaped face',\n  \n  // 眼睛\n  '大眼睛': 'large eyes',\n  '小眼睛': 'small eyes',\n  '丹凤眼': 'phoenix eyes',\n  '杏眼': 'almond eyes',\n  '圆眼': 'round eyes',\n  '深邃': 'deep-set eyes',\n  \n  // 发型\n  '长发': 'long hair',\n  '短发': 'short hair',\n  '中长发': 'medium length hair',\n  '卷发': 'curly hair',\n  '直发': 'straight hair',\n  '波浪发': 'wavy hair',\n  '马尾': 'ponytail',\n  '双马尾': 'twin tails',\n  '刘海': 'bangs',\n  \n  // 身材\n  '高挑': 'tall and slender',\n  '娇小': 'petite',\n  '匀称': 'well-proportioned',\n  '丰满': 'full-figured',\n  '苗条': 'slim',\n  '健美': 'athletic build',\n  \n  // 气质\n  '温柔': 'gentle',\n  '冷酷': 'cold',\n  '活泼': 'lively',\n  '优雅': 'elegant',\n  '知性': 'intellectual',\n  '神秘': 'mysterious',\n  '坚强': 'strong-willed',\n  '可爱': 'cute',\n  \n  // 服装\n  '职业装': 'business attire',\n  '休闲装': 'casual wear',\n  '正装': 'formal wear',\n  '校服': 'school uniform',\n  '古装': 'traditional costume',\n  '现代装': 'modern clothing'\n}\n\n// 艺术风格配置\nconst ART_STYLES = {\n  anime: {\n    keywords: 'anime style, manga style, cel shading, clean lines',\n    description: '日式动漫风格'\n  },\n  realistic: {\n    keywords: 'photorealistic, hyperrealistic, professional photography',\n    description: '写实摄影风格'\n  },\n  'semi-realistic': {\n    keywords: 'semi-realistic, digital art, detailed illustration',\n    description: '半写实插画风格'\n  },\n  'concept-art': {\n    keywords: 'concept art, character design, professional illustration',\n    description: '概念设计风格'\n  }\n}\n\n// 视角配置\nconst VIEW_TYPES = {\n  front: 'front view, facing camera, looking at viewer',\n  side: 'side view, 90-degree profile, side angle',\n  back: 'back view, rear angle, showing back',\n  'three-quarter': 'three-quarter view, slight angle'\n}\n\n// 质量等级配置\nconst QUALITY_LEVELS = {\n  standard: 'good quality, detailed',\n  high: 'high quality, high resolution, detailed, sharp focus',\n  masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'\n}\n\n/**\n * 从角色信息生成图像提示词\n */\nexport function generateCharacterPrompt(\n  character: CharacterInfo, \n  options: PromptOptions = {}\n): string {\n  const {\n    artStyle = 'anime',\n    viewType = 'front',\n    background = 'white',\n    quality = 'high',\n    customEnhancement = ''\n  } = options\n\n  // 构建提示词各部分\n  const parts: string[] = []\n\n  // 1. 质量和风格\n  parts.push(QUALITY_LEVELS[quality])\n  parts.push(ART_STYLES[artStyle].keywords)\n\n  // 2. 基础信息提取\n  const basicInfo = extractBasicInfo(character)\n  if (basicInfo) parts.push(basicInfo)\n\n  // 3. 外貌特征\n  const appearance = extractAppearanceFeatures(character)\n  if (appearance) parts.push(appearance)\n\n  // 4. 服装风格\n  const clothing = extractClothingStyle(character)\n  if (clothing) parts.push(clothing)\n\n  // 5. 气质表现\n  const temperament = extractTemperament(character)\n  if (temperament) parts.push(temperament)\n\n  // 6. 视角和姿态\n  parts.push(VIEW_TYPES[viewType])\n  parts.push('character design sheet, reference pose')\n\n  // 7. 背景设置\n  const backgroundDesc = getBackgroundDescription(background)\n  parts.push(backgroundDesc)\n\n  // 8. 技术参数\n  parts.push('professional character design, clean composition')\n\n  // 9. 自定义增强\n  if (customEnhancement.trim()) {\n    parts.push(customEnhancement)\n  }\n\n  return parts.filter(Boolean).join(', ')\n}\n\n/**\n * 提取基础信息（年龄、性别、身份）\n */\nfunction extractBasicInfo(character: CharacterInfo): string {\n  const info: string[] = []\n  \n  if (character.identity) {\n    // 从身份信息中提取年龄和职业\n    const ageMatch = character.identity.match(/(\\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/)\n    if (ageMatch) {\n      const age = ageMatch[1] || getAgeFromDescription(ageMatch[0])\n      info.push(`${age} years old`)\n    }\n    \n    // 提取性别（如果身份中包含）\n    if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {\n      info.push('woman')\n    } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {\n      info.push('man')\n    }\n    \n    // 提取职业\n    const profession = extractProfession(character.identity)\n    if (profession) info.push(profession)\n  }\n  \n  return info.join(' ')\n}\n\n/**\n * 提取外貌特征\n */\nfunction extractAppearanceFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n  \n  // 处理五官特征\n  if (character.facial) {\n    const facialFeatures = translateFeatures(character.facial)\n    features.push(facialFeatures)\n  }\n  \n  // 处理身材特征\n  if (character.physique) {\n    const bodyFeatures = translateFeatures(character.physique)\n    features.push(bodyFeatures)\n  }\n  \n  // 处理发型\n  if (character.hairstyle) {\n    const hairFeatures = translateFeatures(character.hairstyle)\n    features.push(hairFeatures)\n  }\n  \n  return features.filter(Boolean).join(', ')\n}\n\n/**\n * 提取服装风格\n */\nfunction extractClothingStyle(character: CharacterInfo): string {\n  if (!character.clothing) return ''\n  \n  return translateFeatures(character.clothing)\n}\n\n/**\n * 提取气质特征\n */\nfunction extractTemperament(character: CharacterInfo): string {\n  if (!character.personality) return ''\n  \n  const temperamentWords: string[] = []\n  \n  // 从性格描述中提取气质关键词\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (character.personality!.includes(chinese)) {\n      temperamentWords.push(english)\n    }\n  })\n  \n  // 添加表情描述\n  if (character.personality.includes('温柔') || character.personality.includes('善良')) {\n    temperamentWords.push('gentle smile', 'warm expression')\n  } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {\n    temperamentWords.push('serious expression', 'cold gaze')\n  } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {\n    temperamentWords.push('cheerful smile', 'bright expression')\n  }\n  \n  return temperamentWords.join(', ')\n}\n\n/**\n * 翻译特征描述\n */\nfunction translateFeatures(chineseText: string): string {\n  let result = chineseText\n  \n  // 使用映射表进行翻译\n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    const regex = new RegExp(chinese, 'g')\n    result = result.replace(regex, english)\n  })\n  \n  // 清理和优化\n  result = result\n    .replace(/[，。；：]/g, ',') // 替换中文标点\n    .replace(/\\s+/g, ' ') // 合并空格\n    .replace(/,+/g, ',') // 合并逗号\n    .trim()\n  \n  return result\n}\n\n/**\n * 从描述中提取年龄\n */\nfunction getAgeFromDescription(ageDesc: string): string {\n  const ageMap: Record<string, string> = {\n    '十几岁': '16',\n    '二十多岁': '25', \n    '三十多岁': '35',\n    '四十多岁': '45'\n  }\n  return ageMap[ageDesc] || '25'\n}\n\n/**\n * 提取职业信息\n */\nfunction extractProfession(identity: string): string {\n  const professions: Record<string, string> = {\n    '学生': 'student',\n    '老师': 'teacher',\n    '医生': 'doctor',\n    '护士': 'nurse',\n    '律师': 'lawyer',\n    '工程师': 'engineer',\n    '设计师': 'designer',\n    '程序员': 'programmer',\n    '经理': 'manager',\n    '秘书': 'secretary',\n    '销售': 'salesperson',\n    '警察': 'police officer',\n    '军人': 'soldier',\n    '艺术家': 'artist',\n    '作家': 'writer',\n    '记者': 'journalist'\n  }\n  \n  for (const [chinese, english] of Object.entries(professions)) {\n    if (identity.includes(chinese)) {\n      return english\n    }\n  }\n  \n  return ''\n}\n\n/**\n * 获取背景描述\n */\nfunction getBackgroundDescription(background: string): string {\n  const backgrounds: Record<string, string> = {\n    white: 'clean white background',\n    transparent: 'transparent background',\n    simple: 'simple background',\n    detailed: 'detailed background scene'\n  }\n  \n  return backgrounds[background] || 'clean white background'\n}\n\n/**\n * 生成负面提示词\n */\nexport function generateNegativePrompt(): string {\n  return [\n    'blurry', 'low quality', 'pixelated', 'deformed', 'distorted',\n    'extra limbs', 'missing limbs', 'extra fingers', 'missing fingers',\n    'bad anatomy', 'bad proportions', 'ugly', 'duplicate',\n    'watermark', 'signature', 'text', 'logo',\n    'oversaturated', 'undersaturated', 'overexposed', 'underexposed'\n  ].join(', ')\n}\n\n/**\n * 生成角色一致性提示词（用于三视图）\n */\nexport function generateConsistencyPrompt(character: CharacterInfo, basePrompt: string): {\n  front: string\n  side: string\n  back: string\n} {\n  // 提取身材和服饰特征（保持一致）\n  const consistentFeatures = extractConsistentFeatures(character)\n\n  // 添加强化细节一致性的约束\n  const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories'\n\n  return {\n    // 正面：完整脸部可见\n    front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 侧面：90度侧脸轮廓\n    side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,\n\n    // 背面：看不到脸\n    back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`\n  }\n}\n\n/**\n * 提取一致性特征（身材、服饰、发型等保持不变的特征）\n */\nfunction extractConsistentFeatures(character: CharacterInfo): string {\n  const features: string[] = []\n\n  // 身材特征（保持一致）\n  if (character.physique) {\n    features.push(character.physique)\n  }\n\n  // 发型和发色（保持一致）\n  if (character.hairstyle) {\n    features.push(character.hairstyle)\n  }\n\n  // 服饰风格（保持一致，强调细节一致性）\n  if (character.clothing) {\n    features.push(character.clothing)\n    // 添加细节一致性约束\n    features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理')\n  }\n\n  // 身份特征（保持一致）\n  if (character.identity) {\n    features.push(character.identity)\n  }\n\n  // 添加强化一致性约束\n  features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰')\n\n  return features.join('，')\n}\n\nfunction extractFaceShape(facial: string): string {\n  for (const [chinese, english] of Object.entries(FEATURE_MAPPING)) {\n    if (facial.includes(chinese) && chinese.includes('脸')) {\n      return english\n    }\n  }\n  return ''\n}\n\nfunction extractHairStyle(hairstyle: string): string {\n  const hairFeatures: string[] = []\n  \n  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {\n    if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {\n      hairFeatures.push(english)\n    }\n  })\n  \n  return hairFeatures.join(' ')\n}\n"], "names": [], "mappings": "AAAA,aAAa;AACb,2BAA2B;;;;;;AAoB3B,eAAe;AACf,MAAM,kBAAkB;IACtB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IAER,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IAEP,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IAEN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IA<PERSON>,KAAK;IAC<PERSON>,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IAEN,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;AACT;AAEA,SAAS;AACT,MAAM,aAAa;IACjB,OAAO;QACL,UAAU;QACV,aAAa;IACf;IACA,WAAW;QACT,UAAU;QACV,aAAa;IACf;IACA,kBAAkB;QAChB,UAAU;QACV,aAAa;IACf;IACA,eAAe;QACb,UAAU;QACV,aAAa;IACf;AACF;AAEA,OAAO;AACP,MAAM,aAAa;IACjB,OAAO;IACP,MAAM;IACN,MAAM;IACN,iBAAiB;AACnB;AAEA,SAAS;AACT,MAAM,iBAAiB;IACrB,UAAU;IACV,MAAM;IACN,aAAa;AACf;AAKO,SAAS,wBACd,SAAwB,EACxB,UAAyB,CAAC,CAAC;IAE3B,MAAM,EACJ,WAAW,OAAO,EAClB,WAAW,OAAO,EAClB,aAAa,OAAO,EACpB,UAAU,MAAM,EAChB,oBAAoB,EAAE,EACvB,GAAG;IAEJ,WAAW;IACX,MAAM,QAAkB,EAAE;IAE1B,WAAW;IACX,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ;IAClC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ;IAExC,YAAY;IACZ,MAAM,YAAY,iBAAiB;IACnC,IAAI,WAAW,MAAM,IAAI,CAAC;IAE1B,UAAU;IACV,MAAM,aAAa,0BAA0B;IAC7C,IAAI,YAAY,MAAM,IAAI,CAAC;IAE3B,UAAU;IACV,MAAM,WAAW,qBAAqB;IACtC,IAAI,UAAU,MAAM,IAAI,CAAC;IAEzB,UAAU;IACV,MAAM,cAAc,mBAAmB;IACvC,IAAI,aAAa,MAAM,IAAI,CAAC;IAE5B,WAAW;IACX,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;IAC/B,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,iBAAiB,yBAAyB;IAChD,MAAM,IAAI,CAAC;IAEX,UAAU;IACV,MAAM,IAAI,CAAC;IAEX,WAAW;IACX,IAAI,kBAAkB,IAAI,IAAI;QAC5B,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC;AACpC;AAEA;;CAEC,GACD,SAAS,iBAAiB,SAAwB;IAChD,MAAM,OAAiB,EAAE;IAEzB,IAAI,UAAU,QAAQ,EAAE;QACtB,gBAAgB;QAChB,MAAM,WAAW,UAAU,QAAQ,CAAC,KAAK,CAAC;QAC1C,IAAI,UAAU;YACZ,MAAM,MAAM,QAAQ,CAAC,EAAE,IAAI,sBAAsB,QAAQ,CAAC,EAAE;YAC5D,KAAK,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;QAC9B;QAEA,gBAAgB;QAChB,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YAC9G,KAAK,IAAI,CAAC;QACZ,OAAO,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,QAAQ,CAAC,QAAQ,CAAC,OAAO;YACrH,KAAK,IAAI,CAAC;QACZ;QAEA,OAAO;QACP,MAAM,aAAa,kBAAkB,UAAU,QAAQ;QACvD,IAAI,YAAY,KAAK,IAAI,CAAC;IAC5B;IAEA,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,SAAS;IACT,IAAI,UAAU,MAAM,EAAE;QACpB,MAAM,iBAAiB,kBAAkB,UAAU,MAAM;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,SAAS;IACT,IAAI,UAAU,QAAQ,EAAE;QACtB,MAAM,eAAe,kBAAkB,UAAU,QAAQ;QACzD,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO;IACP,IAAI,UAAU,SAAS,EAAE;QACvB,MAAM,eAAe,kBAAkB,UAAU,SAAS;QAC1D,SAAS,IAAI,CAAC;IAChB;IAEA,OAAO,SAAS,MAAM,CAAC,SAAS,IAAI,CAAC;AACvC;AAEA;;CAEC,GACD,SAAS,qBAAqB,SAAwB;IACpD,IAAI,CAAC,UAAU,QAAQ,EAAE,OAAO;IAEhC,OAAO,kBAAkB,UAAU,QAAQ;AAC7C;AAEA;;CAEC,GACD,SAAS,mBAAmB,SAAwB;IAClD,IAAI,CAAC,UAAU,WAAW,EAAE,OAAO;IAEnC,MAAM,mBAA6B,EAAE;IAErC,gBAAgB;IAChB,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,WAAW,CAAE,QAAQ,CAAC,UAAU;YAC5C,iBAAiB,IAAI,CAAC;QACxB;IACF;IAEA,SAAS;IACT,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QAChF,iBAAiB,IAAI,CAAC,gBAAgB;IACxC,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,sBAAsB;IAC9C,OAAO,IAAI,UAAU,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU,WAAW,CAAC,QAAQ,CAAC,OAAO;QACvF,iBAAiB,IAAI,CAAC,kBAAkB;IAC1C;IAEA,OAAO,iBAAiB,IAAI,CAAC;AAC/B;AAEA;;CAEC,GACD,SAAS,kBAAkB,WAAmB;IAC5C,IAAI,SAAS;IAEb,YAAY;IACZ,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,MAAM,QAAQ,IAAI,OAAO,SAAS;QAClC,SAAS,OAAO,OAAO,CAAC,OAAO;IACjC;IAEA,QAAQ;IACR,SAAS,OACN,OAAO,CAAC,WAAW,KAAK,SAAS;KACjC,OAAO,CAAC,QAAQ,KAAK,OAAO;KAC5B,OAAO,CAAC,OAAO,KAAK,OAAO;KAC3B,IAAI;IAEP,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,OAAe;IAC5C,MAAM,SAAiC;QACrC,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,QAAQ,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,kBAAkB,QAAgB;IACzC,MAAM,cAAsC;QAC1C,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,aAAc;QAC5D,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,yBAAyB,UAAkB;IAClD,MAAM,cAAsC;QAC1C,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;IACZ;IAEA,OAAO,WAAW,CAAC,WAAW,IAAI;AACpC;AAKO,SAAS;IACd,OAAO;QACL;QAAU;QAAe;QAAa;QAAY;QAClD;QAAe;QAAiB;QAAiB;QACjD;QAAe;QAAmB;QAAQ;QAC1C;QAAa;QAAa;QAAQ;QAClC;QAAiB;QAAkB;QAAe;KACnD,CAAC,IAAI,CAAC;AACT;AAKO,SAAS,0BAA0B,SAAwB,EAAE,UAAkB;IAKpF,kBAAkB;IAClB,MAAM,qBAAqB,0BAA0B;IAErD,eAAe;IACf,MAAM,oBAAoB;IAE1B,OAAO;QACL,YAAY;QACZ,OAAO,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAExG,aAAa;QACb,MAAM,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;QAE5G,UAAU;QACV,MAAM,CAAC,+BAA+B,EAAE,mBAAmB,CAAC,EAAE,kBAAkB,wBAAwB,CAAC;IAC3G;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAwB;IACzD,MAAM,WAAqB,EAAE;IAE7B,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,cAAc;IACd,IAAI,UAAU,SAAS,EAAE;QACvB,SAAS,IAAI,CAAC,UAAU,SAAS;IACnC;IAEA,qBAAqB;IACrB,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;QAChC,YAAY;QACZ,SAAS,IAAI,CAAC,WAAW,WAAW;IACtC;IAEA,aAAa;IACb,IAAI,UAAU,QAAQ,EAAE;QACtB,SAAS,IAAI,CAAC,UAAU,QAAQ;IAClC;IAEA,YAAY;IACZ,SAAS,IAAI,CAAC,QAAQ,WAAW,QAAQ,WAAW;IAEpD,OAAO,SAAS,IAAI,CAAC;AACvB;AAEA,SAAS,iBAAiB,MAAc;IACtC,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,iBAAkB;QAChE,IAAI,OAAO,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,MAAM;YACrD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,SAAiB;IACzC,MAAM,eAAyB,EAAE;IAEjC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,SAAS,QAAQ;QACzD,IAAI,UAAU,QAAQ,CAAC,YAAY,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,KAAK,GAAG;YACpF,aAAa,IAAI,CAAC;QACpB;IACF;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/generate-appearance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\nimport { generateCharacterPrompt, generateNegativePrompt, generateConsistencyPrompt } from '@/utils/promptGenerator'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const {\n      modelId,\n      character,\n      customPrompt,\n      // 新增：一致性相关参数\n      generateDetailedDNA = false,\n      useConsistencyConstraints = false,\n      consistencyMode = 'hybrid'\n    } = await request.json()\n\n    if (!modelId || !character) {\n      return NextResponse.json(\n        { success: false, error: '缺少必要参数' },\n        { status: 400 }\n      )\n    }\n\n    // 获取AI配置（使用指定的模型ID或第一个启用的模型）\n    let aiConfig\n    if (modelId) {\n      // 如果指定了模型ID，使用该模型\n      aiConfig = await prisma.aIConfig.findUnique({\n        where: { id: modelId }\n      })\n    } else {\n      // 否则使用第一个启用的模型\n      aiConfig = await prisma.aIConfig.findFirst({\n        where: { enabled: true }\n      })\n    }\n\n    if (!aiConfig) {\n      return NextResponse.json(\n        { success: false, error: '未找到AI配置，请先配置AI模型' },\n        { status: 404 }\n      )\n    }\n\n    // 使用找到的AI配置，不需要覆盖模型名称\n    const configWithSelectedModel = aiConfig\n\n    // 暂时移除状态检查，因为测试API不会更新状态\n    // if (aiConfig.status !== 'connected') {\n    //   return NextResponse.json(\n    //     { success: false, error: 'AI模型未连接，请检查配置' },\n    //     { status: 400 }\n    //   )\n    // }\n\n    // 1. 如果需要，先生成详细DNA\n    let detailedDNA = character.detailedDNA\n    if (generateDetailedDNA || !detailedDNA) {\n      detailedDNA = await generateDetailedCharacterDNA(character)\n    }\n\n    // 2. 基于详细DNA和一致性约束生成图像\n    const generatedImages = await generateConsistentCharacterImages(\n      configWithSelectedModel,\n      character,\n      detailedDNA,\n      customPrompt,\n      useConsistencyConstraints,\n      consistencyMode\n    )\n\n    // 3. 验证一致性（如果启用了一致性约束）\n    let consistencyScore = 0\n    if (useConsistencyConstraints && generatedImages.front) {\n      consistencyScore = await validateImageConsistency(generatedImages, detailedDNA)\n    }\n\n    // 4. 更新角色数据库记录\n    if (character.id) {\n      await prisma.character.update({\n        where: { id: character.id },\n        data: {\n          detailedDNA: JSON.stringify(detailedDNA),\n          consistencyScore: consistencyScore,\n          referenceImages: JSON.stringify({\n            front: generatedImages.front,\n            side: generatedImages.side,\n            back: generatedImages.back,\n            consistencyScore: consistencyScore\n          })\n        }\n      })\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        ...generatedImages,\n        detailedDNA,\n        consistencyScore,\n        consistencyMode\n      }\n    })\n  } catch (error) {\n    console.error('AI形象生成失败:', error)\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'AI形象生成失败，请重试'\n      },\n      { status: 500 }\n    )\n  }\n}\n\n// 生成详细角色DNA\nasync function generateDetailedCharacterDNA(character: any) {\n  const prompt = `\n请基于以下角色信息，生成详细的外貌特征描述：\n\n角色名称：${character.name}\n基础描述：${character.facial || ''}\n身份：${character.identity || ''}\n性格：${character.personality || ''}\n身材：${character.physique || ''}\n发型：${character.hairstyle || ''}\n服装：${character.clothing || ''}\n\n请按照以下JSON格式返回详细信息：\n{\n  \"facial\": {\n    \"faceShape\": \"具体脸型（如：瓜子脸、圆脸、方脸）\",\n    \"eyeShape\": \"具体眼型（如：丹凤眼、杏眼、桃花眼）\",\n    \"eyeColor\": \"具体眼色（如：深邃的黑色、明亮的棕色）\",\n    \"noseShape\": \"具体鼻型（如：高挺的鼻梁、小巧的鼻子）\",\n    \"mouthShape\": \"具体嘴型（如：樱桃小嘴、薄唇）\",\n    \"skinTone\": \"具体肤色（如：白皙透亮、健康的小麦色）\",\n    \"facialFeatures\": \"独特面部特征（如：左脸颊有一颗小痣、眉间有疤痕）\"\n  },\n  \"physique\": {\n    \"height\": \"身高描述\",\n    \"build\": \"体型描述\",\n    \"posture\": \"体态特征\"\n  },\n  \"clothing\": {\n    \"style\": \"服装风格\",\n    \"colors\": [\"常用颜色1\", \"常用颜色2\"],\n    \"accessories\": [\"配饰1\", \"配饰2\"]\n  },\n  \"uniqueIdentifiers\": [\"独特标识1\", \"独特标识2\"],\n  \"standardPrompt\": \"标准化的AI生成提示词\"\n}\n\n要求：\n1. 描述要具体、明确，避免模糊词汇\n2. 独特标识要能够作为强约束条件\n3. 标准化提示词要适合AI图像生成模型使用\n`\n\n  try {\n    // 这里应该调用AI模型生成详细DNA，暂时返回基础结构\n    const detailedDNA = {\n      facial: {\n        faceShape: character.facial?.includes('圆') ? '圆脸' : character.facial?.includes('瓜子') ? '瓜子脸' : '椭圆脸',\n        eyeShape: character.facial?.includes('大眼') ? '杏眼' : '丹凤眼',\n        eyeColor: '深邃的黑色',\n        noseShape: '高挺的鼻梁',\n        mouthShape: '樱桃小嘴',\n        skinTone: '白皙透亮',\n        facialFeatures: character.facial || '清秀的五官'\n      },\n      physique: {\n        height: character.physique?.includes('高') ? '身材高挑' : '中等身材',\n        build: character.physique || '匀称的身材',\n        posture: '优雅的体态'\n      },\n      clothing: {\n        style: character.clothing || '现代休闲风格',\n        colors: ['白色', '蓝色'],\n        accessories: ['简约耳环']\n      },\n      uniqueIdentifiers: [\n        character.facial || '清秀面容',\n        character.hairstyle || '自然发型'\n      ],\n      standardPrompt: `${character.name}, ${character.facial || ''}, ${character.identity || ''}, 高质量角色设计图`\n    }\n\n    return detailedDNA\n  } catch (error) {\n    console.error('生成详细DNA失败:', error)\n    throw new Error('详细DNA生成失败')\n  }\n}\n\n// 生成一致性约束的角色图像\nasync function generateConsistentCharacterImages(\n  aiConfig: any,\n  character: any,\n  detailedDNA: any,\n  customPrompt?: string,\n  useConsistencyConstraints: boolean = false,\n  consistencyMode: string = 'hybrid'\n) {\n  try {\n    let basePrompt = ''\n\n    if (useConsistencyConstraints && detailedDNA) {\n      // 使用详细DNA构建一致性约束提示词\n      basePrompt = buildConsistencyPrompt(detailedDNA, customPrompt)\n    } else {\n      // 使用原有的提示词生成器\n      basePrompt = generateCharacterPrompt(character, {\n        artStyle: 'anime',\n        quality: 'masterpiece',\n        background: 'white',\n        customEnhancement: customPrompt\n      })\n    }\n\n    // 根据不同的AI提供商选择不同的生成方式\n    switch (aiConfig.provider) {\n      case 'zhipu':\n        return await generateWithZhipu(aiConfig, basePrompt)\n      case 'tongyi':\n        const tongyiPrompts = generateConsistencyPrompt(character, basePrompt)\n        return await generateWithTongyi(aiConfig, tongyiPrompts)\n      case 'doubao':\n        const doubaoPrompts = generateConsistencyPrompt(character, basePrompt)\n        return await generateWithDoubao(aiConfig, doubaoPrompts)\n      default:\n        throw new Error(`不支持的AI提供商: ${aiConfig.provider}`)\n    }\n\n  } catch (error) {\n    console.error('生成一致性角色图像失败:', error)\n    throw new Error('一致性角色图像生成失败')\n  }\n}\n\n// 构建一致性约束提示词\nfunction buildConsistencyPrompt(detailedDNA: any, customPrompt?: string): string {\n  const dna = detailedDNA\n\n  const prompt = `\n高质量角色设计图，专业角色设计：\n\n【面部特征】\n- 脸型：${dna.facial.faceShape}\n- 眼型：${dna.facial.eyeShape}，眼色：${dna.facial.eyeColor}\n- 鼻型：${dna.facial.noseShape}\n- 嘴型：${dna.facial.mouthShape}\n- 肤色：${dna.facial.skinTone}\n- 特殊标识：${dna.facial.facialFeatures}\n\n【体型特征】\n- 身高：${dna.physique.height}\n- 体型：${dna.physique.build}\n- 体态：${dna.physique.posture}\n\n【服装风格】\n- 风格：${dna.clothing.style}\n- 主色调：${dna.clothing.colors.join('、')}\n- 配饰：${dna.clothing.accessories.join('、')}\n\n【独特标识】\n${dna.uniqueIdentifiers.join('，')}\n\n【技术要求】\n- 高清晰度，4K质量\n- 专业角色设计风格\n- 干净的白色背景\n- 确保独特标识清晰可见\n- 严格按照上述特征生成\n\n${customPrompt ? `【自定义要求】\\n${customPrompt}` : ''}\n`\n\n  return prompt\n}\n\n// 验证图像一致性\nasync function validateImageConsistency(generatedImages: any, detailedDNA: any): Promise<number> {\n  try {\n    // 这里应该实现真正的图像一致性验证算法\n    // 暂时返回一个基于DNA完整性的评分\n    let score = 0.8 // 基础分\n\n    // 检查是否有详细DNA\n    if (detailedDNA && detailedDNA.facial && detailedDNA.uniqueIdentifiers) {\n      score += 0.1\n    }\n\n    // 检查是否生成了多个视图\n    if (generatedImages.front && generatedImages.side && generatedImages.back) {\n      score += 0.1\n    }\n\n    return Math.min(score, 1.0)\n  } catch (error) {\n    console.error('一致性验证失败:', error)\n    return 0.6 // 默认评分\n  }\n}\n\n// 使用智谱AI生成角色形象\nasync function generateWithZhipu(aiConfig: any, basePrompt: string) {\n  try {\n    // 生成三个视图的提示词\n    const prompts = {\n      front: `${basePrompt}, front view, facing camera, character design sheet`,\n      side: `${basePrompt}, side view, 90-degree profile, character design sheet`,\n      back: `${basePrompt}, back view, rear angle, character design sheet`\n    }\n\n    // 调用智谱AI API生成三个视图\n    const [frontImage, sideImage, backImage] = await Promise.all([\n      callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.front),\n      callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.side),\n      callZhipuAPI(aiConfig.apiKey, aiConfig.model, prompts.back)\n    ])\n\n    return {\n      front: frontImage,\n      side: sideImage,\n      back: backImage,\n      character: {\n        name: '角色名称',\n        description: basePrompt\n      },\n      prompts: prompts\n    }\n  } catch (error) {\n    console.error('智谱AI生成失败:', error)\n    throw error\n  }\n}\n\n// 使用通义万相生成角色形象\nasync function generateWithTongyi(aiConfig: any, prompts: any) {\n  try {\n    // 生成三个视图\n    const [frontImage, sideImage, backImage] = await Promise.all([\n      callTongyiAPI(aiConfig.apiKey, prompts.front),\n      callTongyiAPI(aiConfig.apiKey, prompts.side),\n      callTongyiAPI(aiConfig.apiKey, prompts.back)\n    ])\n\n    return {\n      front: frontImage,\n      side: sideImage,\n      back: backImage,\n      character: {\n        name: '角色名称',\n        description: prompts.front\n      },\n      prompts: prompts\n    }\n  } catch (error) {\n    console.error('通义万相生成失败:', error)\n    throw error\n  }\n}\n\n// 使用豆包生成角色形象（使用新的统一API）\nasync function generateWithDoubao(aiConfig: any, prompts: any) {\n  try {\n    console.log('🎨 使用豆包生成三视图，调用统一图像生成API')\n\n    // 使用豆包图像生成API，传入视图类型\n    const [frontImage, sideImage, backImage] = await Promise.all([\n      callDoubaoImageAPI(prompts.front, 'front'),\n      callDoubaoImageAPI(prompts.side, 'side'),\n      callDoubaoImageAPI(prompts.back, 'back')\n    ])\n\n    return {\n      front: frontImage,\n      side: sideImage,\n      back: backImage,\n      character: {\n        name: '角色名称',\n        description: prompts.front\n      },\n      prompts: prompts\n    }\n  } catch (error) {\n    console.error('豆包生成失败:', error)\n    throw error\n  }\n}\n\n// 调用智谱AI API\nasync function callZhipuAPI(apiKey: string, model: string, prompt: string) {\n  try {\n    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/images/generations', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${apiKey}`\n      },\n      body: JSON.stringify({\n        model: model,\n        prompt: prompt,\n        size: '1024x1024',\n        quality: 'hd',\n        n: 1\n      })\n    })\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      throw new Error(`智谱AI API错误: ${response.status} ${errorText}`)\n    }\n\n    const result = await response.json()\n    const imageUrl = result.data?.[0]?.url\n\n    if (!imageUrl) {\n      throw new Error('未获取到生成的图像')\n    }\n\n    return {\n      url: imageUrl,\n      format: 'url'\n    }\n  } catch (error) {\n    console.error('智谱AI API调用失败:', error)\n    // 返回占位图\n    const svgPlaceholder = generateCharacterSVG(prompt)\n    return {\n      url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n      format: 'svg',\n      error: `智谱AI图像生成失败: ${error.message}`\n    }\n  }\n}\n\n// 调用通义万相API\nasync function callTongyiAPI(apiKey: string, prompt: string) {\n  // 这里使用现有的通义万相实现\n  return await generateWithTongyiSingle(apiKey, 'wanx2.0-t2i-turbo', prompt)\n}\n\n// 调用豆包图像生成API\nasync function callDoubaoImageAPI(prompt: string, viewType: string = 'general') {\n  try {\n    console.log('🎨 调用豆包图像生成API:', {\n      promptLength: prompt.length,\n      viewType: viewType\n    })\n\n    // 获取豆包配置\n    const doubaoConfig = await prisma.aIConfig.findFirst({\n      where: {\n        name: 'Doubao-Seedream-3.0-t2i',\n        enabled: true\n      }\n    })\n\n    if (!doubaoConfig || !doubaoConfig.apiKey) {\n      throw new Error('豆包图像生成模型未配置或未启用')\n    }\n\n    console.log('📝 豆包API请求参数:', {\n      model: doubaoConfig.endpoint,\n      promptLength: prompt.length,\n      viewType: viewType\n    })\n\n    // 注意：这里应该调用真实的豆包API\n    // 但由于需要复杂的AK/SK签名，暂时使用智能占位图\n    console.log('⚠️ 当前使用智能占位图，基于角色描述生成')\n\n    // 模拟API调用延迟\n    await new Promise(resolve => setTimeout(resolve, 1500))\n\n    // 生成基于角色描述的智能占位图\n    const svgPlaceholder = generateCharacterSVG(prompt, 1024, 1024, viewType)\n\n    return {\n      url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n      format: 'svg',\n      metadata: {\n        provider: 'doubao-mock',\n        model: doubaoConfig.endpoint,\n        viewType: viewType,\n        generatedAt: new Date().toISOString()\n      }\n    }\n  } catch (error) {\n    console.error('豆包图像生成API调用失败:', error)\n    // 返回基础占位图\n    const svgPlaceholder = generateCharacterSVG(prompt, 1024, 1024, viewType)\n    return {\n      url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n      format: 'svg',\n      metadata: { provider: 'fallback', model: 'svg-generator', error: error.message }\n    }\n  }\n}\n\n// 构建角色描述\nfunction buildCharacterDescription(character: any) {\n  const parts = []\n\n  if (character.identity) parts.push(`身份：${character.identity}`)\n  if (character.personality) parts.push(`性格：${character.personality}`)\n  if (character.physique) parts.push(`身材特征：${character.physique}`)\n  if (character.facial) parts.push(`五官特征：${character.facial}`)\n  if (character.hairstyle) parts.push(`发型样式：${character.hairstyle}`)\n  if (character.clothing) parts.push(`服饰风格：${character.clothing}`)\n\n  return parts.join('；')\n}\n\n// 构建图像生成提示词\nfunction buildImagePrompt(characterDescription: string, viewType: 'front' | 'side' | 'back', customPrompt?: string) {\n  const viewDescriptions = {\n    front: '正面视图，面向镜头',\n    side: '侧面视图，90度侧身',\n    back: '背面视图，背对镜头'\n  }\n\n  // 如果有自定义提示词，将其加入到基础提示词中\n  const basePrompt = `高质量角色设计图，${viewDescriptions[viewType]}，${characterDescription}，\n专业角色设计，干净的白色背景，全身图，高清晰度，\n动漫风格，角色设计参考图，三视图设计，\nlighting: soft studio lighting,\nstyle: professional character design, clean lines, detailed features,\nquality: high resolution, 4K, masterpiece`\n\n  if (customPrompt && customPrompt.trim()) {\n    return `${basePrompt}，${customPrompt}`\n  }\n\n  return basePrompt\n}\n\n// 生成图像\nasync function generateImage(aiConfig: any, prompt: string) {\n  const { provider, apiKey, model } = aiConfig\n\n  switch (provider) {\n    case 'zhipu':\n      // 智谱AI CogView图像生成\n      return await callZhipuAPI(apiKey, model, prompt)\n    case 'tongyi':\n      // 通义万相文生图\n      return await generateWithTongyiSingle(apiKey, model, prompt)\n    case 'doubao':\n      // 豆包图像生成\n      return await callDoubaoImageAPI(prompt)\n    case 'deepseek':\n      // DeepSeek不支持图像生成，使用Stable Diffusion API\n      return await generateWithStableDiffusion(prompt)\n    case 'openai':\n      return await generateWithDALLE(apiKey, prompt)\n    default:\n      // 默认使用免费的图像生成服务\n      return await generateWithStableDiffusion(prompt)\n  }\n}\n\n// 使用Stable Diffusion生成图像（免费服务）\nasync function generateWithStableDiffusion(prompt: string) {\n  try {\n    // 设置较短的超时时间\n    const controller = new AbortController()\n    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时\n\n    // 使用Hugging Face的免费Stable Diffusion API\n    const response = await fetch('https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        // 使用公共访问，无需API密钥\n      },\n      body: JSON.stringify({\n        inputs: prompt,\n        parameters: {\n          num_inference_steps: 15, // 减少步数以加快生成\n          guidance_scale: 7.5,\n          width: 512,\n          height: 768\n        }\n      }),\n      signal: controller.signal\n    })\n\n    clearTimeout(timeoutId)\n\n    if (!response.ok) {\n      throw new Error(`图像生成失败: ${response.statusText}`)\n    }\n\n    const imageBlob = await response.blob()\n    const imageBuffer = await imageBlob.arrayBuffer()\n    const base64Image = Buffer.from(imageBuffer).toString('base64')\n\n    return {\n      url: `data:image/png;base64,${base64Image}`,\n      format: 'base64'\n    }\n  } catch (error) {\n    console.error('Stable Diffusion生成失败:', error)\n\n    // 生成一个简单的SVG占位图，包含角色描述\n    const svgPlaceholder = generateCharacterSVG(prompt)\n\n    return {\n      url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n      format: 'svg',\n      error: '图像生成服务暂时不可用，显示描述图'\n    }\n  }\n}\n\n// 生成包含角色描述的SVG占位图\nfunction generateCharacterSVG(prompt: string): string {\n  // 提取角色特征关键词\n  const features = prompt.match(/[\\u4e00-\\u9fa5]+/g)?.slice(0, 6) || ['角色', '设计', '图']\n\n  return `\n    <svg width=\"512\" height=\"768\" xmlns=\"http://www.w3.org/2000/svg\">\n      <defs>\n        <linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n          <stop offset=\"0%\" style=\"stop-color:#f3f4f6;stop-opacity:1\" />\n          <stop offset=\"100%\" style=\"stop-color:#e5e7eb;stop-opacity:1\" />\n        </linearGradient>\n      </defs>\n      <rect width=\"512\" height=\"768\" fill=\"url(#bg)\"/>\n\n      <!-- 人物轮廓 -->\n      <ellipse cx=\"256\" cy=\"200\" rx=\"80\" ry=\"100\" fill=\"#d1d5db\" opacity=\"0.5\"/>\n      <rect x=\"176\" y=\"280\" width=\"160\" height=\"300\" rx=\"20\" fill=\"#d1d5db\" opacity=\"0.5\"/>\n      <ellipse cx=\"256\" cy=\"650\" rx=\"100\" ry=\"60\" fill=\"#d1d5db\" opacity=\"0.3\"/>\n\n      <!-- 特征文字 -->\n      <text x=\"256\" y=\"100\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"24\" fill=\"#374151\">角色设计图</text>\n\n      ${features.map((feature, index) => `\n        <text x=\"256\" y=\"${140 + index * 30}\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"16\" fill=\"#6b7280\">${feature}</text>\n      `).join('')}\n\n      <text x=\"256\" y=\"720\" text-anchor=\"middle\" font-family=\"Arial, sans-serif\" font-size=\"14\" fill=\"#9ca3af\">图像生成服务暂时不可用</text>\n    </svg>\n  `\n}\n\n// 使用通义万相生成图像（单张）\nasync function generateWithTongyiSingle(apiKey: string, model: string, prompt: string) {\n  // 添加重试机制\n  const maxRetries = 3\n  let lastError = null\n\n  for (let retry = 0; retry < maxRetries; retry++) {\n    try {\n      // 如果是重试，等待一段时间避免频率限制\n      if (retry > 0) {\n        const waitTime = Math.pow(2, retry) * 1000 // 指数退避：2s, 4s, 8s\n        console.log(`通义万相重试 ${retry}/${maxRetries}，等待 ${waitTime}ms`)\n        await new Promise(resolve => setTimeout(resolve, waitTime))\n      }\n\n      // 第一步：创建任务\n      const createTaskResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${apiKey}`,\n          'X-DashScope-Async': 'enable'\n        },\n        body: JSON.stringify({\n          model: model,\n          input: {\n            prompt: prompt\n          },\n          parameters: {\n            size: '1024*1024',\n            n: 1,\n            seed: Math.floor(Math.random() * 1000000), // 添加随机种子\n            style: '<auto>',\n            ref_mode: 'repaint'\n          }\n        })\n      })\n\n      if (!createTaskResponse.ok) {\n        const errorText = await createTaskResponse.text()\n        console.error(`通义万相API错误 (${createTaskResponse.status}):`, errorText)\n\n        // 如果是频率限制，继续重试\n        if (createTaskResponse.status === 429) {\n          lastError = new Error(`API调用频率限制，重试中...`)\n          continue\n        }\n\n        throw new Error(`通义万相任务创建失败: ${createTaskResponse.statusText}`)\n      }\n\n      const taskData = await createTaskResponse.json()\n      const taskId = taskData.output?.task_id\n\n      if (!taskId) {\n        throw new Error('未获取到任务ID')\n      }\n\n      console.log(`通义万相任务创建成功，任务ID: ${taskId}`)\n\n      // 第二步：轮询任务状态直到完成\n      let attempts = 0\n      const maxAttempts = 60 // 最多等待5分钟\n\n      while (attempts < maxAttempts) {\n        await new Promise(resolve => setTimeout(resolve, 5000)) // 等待5秒\n\n        const statusResponse = await fetch(`https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${apiKey}`\n          }\n        })\n\n        if (!statusResponse.ok) {\n          throw new Error(`查询任务状态失败: ${statusResponse.statusText}`)\n        }\n\n        const statusData = await statusResponse.json()\n        const taskStatus = statusData.output?.task_status\n\n        console.log(`通义万相任务状态: ${taskStatus} (${attempts + 1}/${maxAttempts})`)\n\n        if (taskStatus === 'SUCCEEDED') {\n          const imageUrl = statusData.output?.results?.[0]?.url\n          if (imageUrl) {\n            console.log(`通义万相图像生成成功: ${imageUrl}`)\n            return {\n              url: imageUrl,\n              format: 'url'\n            }\n          } else {\n            throw new Error('任务成功但未获取到图像URL')\n          }\n        } else if (taskStatus === 'FAILED') {\n          throw new Error(`任务执行失败: ${statusData.output?.message || '未知错误'}`)\n        }\n\n        attempts++\n      }\n\n      throw new Error('任务执行超时')\n\n    } catch (error) {\n      console.error(`通义万相生成失败 (尝试 ${retry + 1}/${maxRetries}):`, error)\n      lastError = error\n\n      // 如果不是最后一次重试，继续\n      if (retry < maxRetries - 1) {\n        continue\n      }\n    }\n  }\n\n  // 所有重试都失败了，生成SVG占位图\n  console.log('通义万相所有重试都失败，生成SVG占位图')\n  const svgPlaceholder = generateCharacterSVG(prompt)\n\n  return {\n    url: `data:image/svg+xml;base64,${Buffer.from(svgPlaceholder).toString('base64')}`,\n    format: 'svg',\n    error: `通义万相图像生成服务暂时不可用: ${lastError?.message || '未知错误'}`\n  }\n}\n\n// 使用DALL-E生成图像\nasync function generateWithDALLE(apiKey: string, prompt: string) {\n  try {\n    const response = await fetch('https://api.openai.com/v1/images/generations', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${apiKey}`\n      },\n      body: JSON.stringify({\n        prompt: prompt,\n        n: 1,\n        size: '512x768',\n        quality: 'standard'\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error(`DALL-E生成失败: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const imageUrl = data.data[0]?.url\n\n    if (!imageUrl) {\n      throw new Error('未获取到生成的图像')\n    }\n\n    return {\n      url: imageUrl,\n      format: 'url'\n    }\n  } catch (error) {\n    console.error('DALL-E生成失败:', error)\n    return {\n      url: '/placeholder-character.svg',\n      format: 'placeholder',\n      error: '图像生成服务暂时不可用'\n    }\n  }\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EACJ,OAAO,EACP,SAAS,EACT,YAAY,EACZ,aAAa;QACb,sBAAsB,KAAK,EAC3B,4BAA4B,KAAK,EACjC,kBAAkB,QAAQ,EAC3B,GAAG,MAAM,QAAQ,IAAI;QAEtB,IAAI,CAAC,WAAW,CAAC,WAAW;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,IAAI;QACJ,IAAI,SAAS;YACX,kBAAkB;YAClB,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC1C,OAAO;oBAAE,IAAI;gBAAQ;YACvB;QACF,OAAO;YACL,eAAe;YACf,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACzC,OAAO;oBAAE,SAAS;gBAAK;YACzB;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,0BAA0B;QAEhC,yBAAyB;QACzB,yCAAyC;QACzC,8BAA8B;QAC9B,kDAAkD;QAClD,sBAAsB;QACtB,MAAM;QACN,IAAI;QAEJ,mBAAmB;QACnB,IAAI,cAAc,UAAU,WAAW;QACvC,IAAI,uBAAuB,CAAC,aAAa;YACvC,cAAc,MAAM,6BAA6B;QACnD;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,MAAM,kCAC5B,yBACA,WACA,aACA,cACA,2BACA;QAGF,uBAAuB;QACvB,IAAI,mBAAmB;QACvB,IAAI,6BAA6B,gBAAgB,KAAK,EAAE;YACtD,mBAAmB,MAAM,yBAAyB,iBAAiB;QACrE;QAEA,eAAe;QACf,IAAI,UAAU,EAAE,EAAE;YAChB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5B,OAAO;oBAAE,IAAI,UAAU,EAAE;gBAAC;gBAC1B,MAAM;oBACJ,aAAa,KAAK,SAAS,CAAC;oBAC5B,kBAAkB;oBAClB,iBAAiB,KAAK,SAAS,CAAC;wBAC9B,OAAO,gBAAgB,KAAK;wBAC5B,MAAM,gBAAgB,IAAI;wBAC1B,MAAM,gBAAgB,IAAI;wBAC1B,kBAAkB;oBACpB;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,GAAG,eAAe;gBAClB;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,YAAY;AACZ,eAAe,6BAA6B,SAAc;IACxD,MAAM,SAAS,CAAC;;;KAGb,EAAE,UAAU,IAAI,CAAC;KACjB,EAAE,UAAU,MAAM,IAAI,GAAG;GAC3B,EAAE,UAAU,QAAQ,IAAI,GAAG;GAC3B,EAAE,UAAU,WAAW,IAAI,GAAG;GAC9B,EAAE,UAAU,QAAQ,IAAI,GAAG;GAC3B,EAAE,UAAU,SAAS,IAAI,GAAG;GAC5B,EAAE,UAAU,QAAQ,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+B9B,CAAC;IAEC,IAAI;QACF,6BAA6B;QAC7B,MAAM,cAAc;YAClB,QAAQ;gBACN,WAAW,UAAU,MAAM,EAAE,SAAS,OAAO,OAAO,UAAU,MAAM,EAAE,SAAS,QAAQ,QAAQ;gBAC/F,UAAU,UAAU,MAAM,EAAE,SAAS,QAAQ,OAAO;gBACpD,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,UAAU;gBACV,gBAAgB,UAAU,MAAM,IAAI;YACtC;YACA,UAAU;gBACR,QAAQ,UAAU,QAAQ,EAAE,SAAS,OAAO,SAAS;gBACrD,OAAO,UAAU,QAAQ,IAAI;gBAC7B,SAAS;YACX;YACA,UAAU;gBACR,OAAO,UAAU,QAAQ,IAAI;gBAC7B,QAAQ;oBAAC;oBAAM;iBAAK;gBACpB,aAAa;oBAAC;iBAAO;YACvB;YACA,mBAAmB;gBACjB,UAAU,MAAM,IAAI;gBACpB,UAAU,SAAS,IAAI;aACxB;YACD,gBAAgB,GAAG,UAAU,IAAI,CAAC,EAAE,EAAE,UAAU,MAAM,IAAI,GAAG,EAAE,EAAE,UAAU,QAAQ,IAAI,GAAG,UAAU,CAAC;QACvG;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,eAAe;AACf,eAAe,kCACb,QAAa,EACb,SAAc,EACd,WAAgB,EAChB,YAAqB,EACrB,4BAAqC,KAAK,EAC1C,kBAA0B,QAAQ;IAElC,IAAI;QACF,IAAI,aAAa;QAEjB,IAAI,6BAA6B,aAAa;YAC5C,oBAAoB;YACpB,aAAa,uBAAuB,aAAa;QACnD,OAAO;YACL,cAAc;YACd,aAAa,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;gBAC9C,UAAU;gBACV,SAAS;gBACT,YAAY;gBACZ,mBAAmB;YACrB;QACF;QAEA,sBAAsB;QACtB,OAAQ,SAAS,QAAQ;YACvB,KAAK;gBACH,OAAO,MAAM,kBAAkB,UAAU;YAC3C,KAAK;gBACH,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;gBAC3D,OAAO,MAAM,mBAAmB,UAAU;YAC5C,KAAK;gBACH,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;gBAC3D,OAAO,MAAM,mBAAmB,UAAU;YAC5C;gBACE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,QAAQ,EAAE;QACrD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,aAAa;AACb,SAAS,uBAAuB,WAAgB,EAAE,YAAqB;IACrE,MAAM,MAAM;IAEZ,MAAM,SAAS,CAAC;;;;KAIb,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC;KACvB,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC;KAChD,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC;KACvB,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC;KACxB,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC;OACpB,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC;;;KAG9B,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC;KACtB,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC;KACrB,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC;;;KAGvB,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC;MACpB,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;KACjC,EAAE,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK;;;AAG1C,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK;;;;;;;;;AASlC,EAAE,eAAe,CAAC,SAAS,EAAE,cAAc,GAAG,GAAG;AACjD,CAAC;IAEC,OAAO;AACT;AAEA,UAAU;AACV,eAAe,yBAAyB,eAAoB,EAAE,WAAgB;IAC5E,IAAI;QACF,qBAAqB;QACrB,oBAAoB;QACpB,IAAI,QAAQ,IAAI,MAAM;;QAEtB,aAAa;QACb,IAAI,eAAe,YAAY,MAAM,IAAI,YAAY,iBAAiB,EAAE;YACtE,SAAS;QACX;QAEA,cAAc;QACd,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,IAAI,IAAI,gBAAgB,IAAI,EAAE;YACzE,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO,IAAI,OAAO;;IACpB;AACF;AAEA,eAAe;AACf,eAAe,kBAAkB,QAAa,EAAE,UAAkB;IAChE,IAAI;QACF,aAAa;QACb,MAAM,UAAU;YACd,OAAO,GAAG,WAAW,mDAAmD,CAAC;YACzE,MAAM,GAAG,WAAW,sDAAsD,CAAC;YAC3E,MAAM,GAAG,WAAW,+CAA+C,CAAC;QACtE;QAEA,mBAAmB;QACnB,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3D,aAAa,SAAS,MAAM,EAAE,SAAS,KAAK,EAAE,QAAQ,KAAK;YAC3D,aAAa,SAAS,MAAM,EAAE,SAAS,KAAK,EAAE,QAAQ,IAAI;YAC1D,aAAa,SAAS,MAAM,EAAE,SAAS,KAAK,EAAE,QAAQ,IAAI;SAC3D;QAED,OAAO;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,WAAW;gBACT,MAAM;gBACN,aAAa;YACf;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAEA,eAAe;AACf,eAAe,mBAAmB,QAAa,EAAE,OAAY;IAC3D,IAAI;QACF,SAAS;QACT,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3D,cAAc,SAAS,MAAM,EAAE,QAAQ,KAAK;YAC5C,cAAc,SAAS,MAAM,EAAE,QAAQ,IAAI;YAC3C,cAAc,SAAS,MAAM,EAAE,QAAQ,IAAI;SAC5C;QAED,OAAO;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,WAAW;gBACT,MAAM;gBACN,aAAa,QAAQ,KAAK;YAC5B;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAEA,wBAAwB;AACxB,eAAe,mBAAmB,QAAa,EAAE,OAAY;IAC3D,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,qBAAqB;QACrB,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3D,mBAAmB,QAAQ,KAAK,EAAE;YAClC,mBAAmB,QAAQ,IAAI,EAAE;YACjC,mBAAmB,QAAQ,IAAI,EAAE;SAClC;QAED,OAAO;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,WAAW;gBACT,MAAM;gBACN,aAAa,QAAQ,KAAK;YAC5B;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM;IACR;AACF;AAEA,aAAa;AACb,eAAe,aAAa,MAAc,EAAE,KAAa,EAAE,MAAc;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,2DAA2D;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ;YACrC;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,GAAG;YACL;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;QAC/D;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,MAAM,WAAW,OAAO,IAAI,EAAE,CAAC,EAAE,EAAE;QAEnC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,KAAK;YACL,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,QAAQ;QACR,MAAM,iBAAiB,qBAAqB;QAC5C,OAAO;YACL,KAAK,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YAClF,QAAQ;YACR,OAAO,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE;QACvC;IACF;AACF;AAEA,YAAY;AACZ,eAAe,cAAc,MAAc,EAAE,MAAc;IACzD,gBAAgB;IAChB,OAAO,MAAM,yBAAyB,QAAQ,qBAAqB;AACrE;AAEA,cAAc;AACd,eAAe,mBAAmB,MAAc,EAAE,WAAmB,SAAS;IAC5E,IAAI;QACF,QAAQ,GAAG,CAAC,mBAAmB;YAC7B,cAAc,OAAO,MAAM;YAC3B,UAAU;QACZ;QAEA,SAAS;QACT,MAAM,eAAe,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,CAAC,gBAAgB,CAAC,aAAa,MAAM,EAAE;YACzC,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC3B,OAAO,aAAa,QAAQ;YAC5B,cAAc,OAAO,MAAM;YAC3B,UAAU;QACZ;QAEA,oBAAoB;QACpB,4BAA4B;QAC5B,QAAQ,GAAG,CAAC;QAEZ,YAAY;QACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,iBAAiB;QACjB,MAAM,iBAAiB,qBAAqB,QAAQ,MAAM,MAAM;QAEhE,OAAO;YACL,KAAK,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YAClF,QAAQ;YACR,UAAU;gBACR,UAAU;gBACV,OAAO,aAAa,QAAQ;gBAC5B,UAAU;gBACV,aAAa,IAAI,OAAO,WAAW;YACrC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,UAAU;QACV,MAAM,iBAAiB,qBAAqB,QAAQ,MAAM,MAAM;QAChE,OAAO;YACL,KAAK,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YAClF,QAAQ;YACR,UAAU;gBAAE,UAAU;gBAAY,OAAO;gBAAiB,OAAO,MAAM,OAAO;YAAC;QACjF;IACF;AACF;AAEA,SAAS;AACT,SAAS,0BAA0B,SAAc;IAC/C,MAAM,QAAQ,EAAE;IAEhB,IAAI,UAAU,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,QAAQ,EAAE;IAC7D,IAAI,UAAU,WAAW,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,WAAW,EAAE;IACnE,IAAI,UAAU,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,QAAQ,EAAE;IAC/D,IAAI,UAAU,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,EAAE;IAC3D,IAAI,UAAU,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,SAAS,EAAE;IACjE,IAAI,UAAU,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,QAAQ,EAAE;IAE/D,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,YAAY;AACZ,SAAS,iBAAiB,oBAA4B,EAAE,QAAmC,EAAE,YAAqB;IAChH,MAAM,mBAAmB;QACvB,OAAO;QACP,MAAM;QACN,MAAM;IACR;IAEA,wBAAwB;IACxB,MAAM,aAAa,CAAC,SAAS,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB;;;;;yCAK3C,CAAC;IAExC,IAAI,gBAAgB,aAAa,IAAI,IAAI;QACvC,OAAO,GAAG,WAAW,CAAC,EAAE,cAAc;IACxC;IAEA,OAAO;AACT;AAEA,OAAO;AACP,eAAe,cAAc,QAAa,EAAE,MAAc;IACxD,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAEpC,OAAQ;QACN,KAAK;YACH,mBAAmB;YACnB,OAAO,MAAM,aAAa,QAAQ,OAAO;QAC3C,KAAK;YACH,UAAU;YACV,OAAO,MAAM,yBAAyB,QAAQ,OAAO;QACvD,KAAK;YACH,SAAS;YACT,OAAO,MAAM,mBAAmB;QAClC,KAAK;YACH,yCAAyC;YACzC,OAAO,MAAM,4BAA4B;QAC3C,KAAK;YACH,OAAO,MAAM,kBAAkB,QAAQ;QACzC;YACE,gBAAgB;YAChB,OAAO,MAAM,4BAA4B;IAC7C;AACF;AAEA,+BAA+B;AAC/B,eAAe,4BAA4B,MAAc;IACvD,IAAI;QACF,YAAY;QACZ,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,OAAO,QAAQ;;QAEtE,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,8EAA8E;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAElB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,QAAQ;gBACR,YAAY;oBACV,qBAAqB;oBACrB,gBAAgB;oBAChB,OAAO;oBACP,QAAQ;gBACV;YACF;YACA,QAAQ,WAAW,MAAM;QAC3B;QAEA,aAAa;QAEb,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE;QAClD;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,cAAc,MAAM,UAAU,WAAW;QAC/C,MAAM,cAAc,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QAEtD,OAAO;YACL,KAAK,CAAC,sBAAsB,EAAE,aAAa;YAC3C,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,uBAAuB;QACvB,MAAM,iBAAiB,qBAAqB;QAE5C,OAAO;YACL,KAAK,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YAClF,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAEA,kBAAkB;AAClB,SAAS,qBAAqB,MAAc;IAC1C,YAAY;IACZ,MAAM,WAAW,OAAO,KAAK,CAAC,sBAAsB,MAAM,GAAG,MAAM;QAAC;QAAM;QAAM;KAAI;IAEpF,OAAO,CAAC;;;;;;;;;;;;;;;;;;MAkBJ,EAAE,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;yBACjB,EAAE,MAAM,QAAQ,GAAG,qFAAqF,EAAE,QAAQ;MACrI,CAAC,EAAE,IAAI,CAAC,IAAI;;;;EAIhB,CAAC;AACH;AAEA,iBAAiB;AACjB,eAAe,yBAAyB,MAAc,EAAE,KAAa,EAAE,MAAc;IACnF,SAAS;IACT,MAAM,aAAa;IACnB,IAAI,YAAY;IAEhB,IAAK,IAAI,QAAQ,GAAG,QAAQ,YAAY,QAAS;QAC/C,IAAI;YACF,qBAAqB;YACrB,IAAI,QAAQ,GAAG;gBACb,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,SAAS,KAAK,kBAAkB;;gBAC7D,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,CAAC;gBAC5D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,WAAW;YACX,MAAM,qBAAqB,MAAM,MAAM,kFAAkF;gBACvH,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ;oBACnC,qBAAqB;gBACvB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,OAAO;wBACL,QAAQ;oBACV;oBACA,YAAY;wBACV,MAAM;wBACN,GAAG;wBACH,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;wBACjC,OAAO;wBACP,UAAU;oBACZ;gBACF;YACF;YAEA,IAAI,CAAC,mBAAmB,EAAE,EAAE;gBAC1B,MAAM,YAAY,MAAM,mBAAmB,IAAI;gBAC/C,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,mBAAmB,MAAM,CAAC,EAAE,CAAC,EAAE;gBAE3D,eAAe;gBACf,IAAI,mBAAmB,MAAM,KAAK,KAAK;oBACrC,YAAY,IAAI,MAAM,CAAC,gBAAgB,CAAC;oBACxC;gBACF;gBAEA,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,mBAAmB,UAAU,EAAE;YAChE;YAEA,MAAM,WAAW,MAAM,mBAAmB,IAAI;YAC9C,MAAM,SAAS,SAAS,MAAM,EAAE;YAEhC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ;YAExC,iBAAiB;YACjB,IAAI,WAAW;YACf,MAAM,cAAc,GAAG,UAAU;;YAEjC,MAAO,WAAW,YAAa;gBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,OAAO;;gBAE/D,MAAM,iBAAiB,MAAM,MAAM,CAAC,4CAA4C,EAAE,QAAQ,EAAE;oBAC1F,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ;oBACrC;gBACF;gBAEA,IAAI,CAAC,eAAe,EAAE,EAAE;oBACtB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,eAAe,UAAU,EAAE;gBAC1D;gBAEA,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,MAAM,aAAa,WAAW,MAAM,EAAE;gBAEtC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;gBAEtE,IAAI,eAAe,aAAa;oBAC9B,MAAM,WAAW,WAAW,MAAM,EAAE,SAAS,CAAC,EAAE,EAAE;oBAClD,IAAI,UAAU;wBACZ,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU;wBACrC,OAAO;4BACL,KAAK;4BACL,QAAQ;wBACV;oBACF,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO,IAAI,eAAe,UAAU;oBAClC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,WAAW,MAAM,EAAE,WAAW,QAAQ;gBACnE;gBAEA;YACF;YAEA,MAAM,IAAI,MAAM;QAElB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;YAC3D,YAAY;YAEZ,gBAAgB;YAChB,IAAI,QAAQ,aAAa,GAAG;gBAC1B;YACF;QACF;IACF;IAEA,oBAAoB;IACpB,QAAQ,GAAG,CAAC;IACZ,MAAM,iBAAiB,qBAAqB;IAE5C,OAAO;QACL,KAAK,CAAC,0BAA0B,EAAE,OAAO,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;QAClF,QAAQ;QACR,OAAO,CAAC,iBAAiB,EAAE,WAAW,WAAW,QAAQ;IAC3D;AACF;AAEA,eAAe;AACf,eAAe,kBAAkB,MAAc,EAAE,MAAc;IAC7D,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,gDAAgD;YAC3E,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ;YACrC;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,QAAQ;gBACR,GAAG;gBACH,MAAM;gBACN,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,UAAU,EAAE;QACtD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE;QAE/B,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,KAAK;YACL,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YACL,KAAK;YACL,QAAQ;YACR,OAAO;QACT;IACF;AACF", "debugId": null}}]}