import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const filename = params.filename
    
    // 安全检查：防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json({
        success: false,
        error: '无效的文件名'
      }, { status: 400 })
    }
    
    // 构建文件路径
    const filePath = path.join(process.cwd(), 'temp', 'video-merge', filename)
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({
        success: false,
        error: '文件不存在'
      }, { status: 404 })
    }
    
    // 读取文件
    const fileBuffer = fs.readFileSync(filePath)
    
    // 获取文件扩展名来设置正确的MIME类型
    const ext = path.extname(filename).toLowerCase()
    let mimeType = 'application/octet-stream'
    
    switch (ext) {
      case '.mp4':
        mimeType = 'video/mp4'
        break
      case '.webm':
        mimeType = 'video/webm'
        break
      case '.avi':
        mimeType = 'video/x-msvideo'
        break
      case '.mov':
        mimeType = 'video/quicktime'
        break
    }
    
    // 返回文件
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileBuffer.length.toString(),
        'Content-Disposition': `inline; filename="${filename}"`,
        'Cache-Control': 'public, max-age=3600' // 缓存1小时
      }
    })
    
  } catch (error) {
    console.error('文件下载失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '文件下载失败'
    }, { status: 500 })
  }
}
