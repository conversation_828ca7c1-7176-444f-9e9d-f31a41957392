const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTTSAPI() {
  try {
    console.log('🎵 测试TTS API...');
    
    // 1. 检查TTS配置
    const ttsConfigs = await prisma.aIConfig.findMany({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'asc' }
      ]
    });
    
    if (ttsConfigs.length === 0) {
      console.error('❌ 未找到可用的TTS配置');
      console.log('请先运行: node setup-edge-tts.js');
      return;
    }
    
    console.log('✅ 找到TTS配置:');
    ttsConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.name} (${config.provider})`);
    });
    
    // 2. 测试获取声音列表
    console.log('\n🎤 测试获取声音列表...');
    
    try {
      const voicesResponse = await fetch('http://localhost:3000/api/ai/generate-tts', {
        method: 'GET'
      });
      
      if (voicesResponse.ok) {
        const voicesResult = await voicesResponse.json();
        if (voicesResult.success) {
          console.log('✅ 成功获取声音列表');
          console.log(`可用声音数量: ${voicesResult.data.length}`);
          
          if (voicesResult.data.length > 0) {
            console.log('前5个声音:');
            voicesResult.data.slice(0, 5).forEach((voice, index) => {
              console.log(`  ${index + 1}. ${voice.name} (${voice.id})`);
            });
          }
        } else {
          console.error('❌ 获取声音列表失败:', voicesResult.error);
        }
      } else {
        console.error('❌ API调用失败:', voicesResponse.status);
      }
    } catch (error) {
      console.error('❌ 获取声音列表异常:', error.message);
    }
    
    // 3. 测试语音生成
    console.log('\n🎵 测试语音生成...');
    
    const testText = '你好，这是语音合成的测试。今天天气很好，适合出去走走。';
    
    const ttsRequest = {
      text: testText,
      voiceId: 'zh-CN-XiaoxiaoNeural',
      emotion: 'neutral',
      speed: 1.0,
      pitch: 0,
      volume: 80,
      format: 'mp3'
    };
    
    console.log('测试文本:', testText);
    console.log('请求参数:', {
      textLength: testText.length,
      voice: ttsRequest.voiceId,
      emotion: ttsRequest.emotion,
      speed: ttsRequest.speed
    });
    
    try {
      const ttsResponse = await fetch('http://localhost:3000/api/ai/generate-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ttsRequest)
      });
      
      if (ttsResponse.ok) {
        const ttsResult = await ttsResponse.json();
        if (ttsResult.success) {
          console.log('✅ 语音生成成功');
          console.log('生成结果:', {
            duration: ttsResult.data.duration.toFixed(1) + '秒',
            size: (ttsResult.data.size / 1024).toFixed(1) + 'KB',
            format: ttsResult.data.format,
            audioUrlLength: ttsResult.data.audioUrl.length
          });
          
          if (ttsResult.data.metadata) {
            console.log('元数据:', ttsResult.data.metadata);
          }
        } else {
          console.error('❌ 语音生成失败:', ttsResult.error);
        }
      } else {
        const errorText = await ttsResponse.text();
        console.error('❌ API调用失败:', ttsResponse.status, errorText);
      }
    } catch (error) {
      console.error('❌ 语音生成异常:', error.message);
    }
    
    // 4. 测试角色声音配置
    console.log('\n🎭 测试角色声音配置...');
    
    const characters = await prisma.character.findMany({
      take: 3,
      include: {
        voiceConfigs: {
          include: {
            ttsConfig: {
              select: {
                name: true,
                provider: true
              }
            }
          }
        }
      }
    });
    
    if (characters.length > 0) {
      console.log('角色声音配置:');
      characters.forEach((character, index) => {
        console.log(`  ${index + 1}. ${character.name}:`);
        if (character.voiceConfigs.length > 0) {
          character.voiceConfigs.forEach((voiceConfig, vIndex) => {
            console.log(`    - ${voiceConfig.voiceName} (${voiceConfig.voiceId})`);
            console.log(`      TTS服务: ${voiceConfig.ttsConfig.name}`);
            console.log(`      参数: 语速=${voiceConfig.baseSpeed}, 音调=${voiceConfig.basePitch}, 音量=${voiceConfig.baseVolume}`);
          });
        } else {
          console.log('    - 未配置声音');
        }
      });
      
      // 测试使用角色声音生成语音
      const testCharacter = characters[0];
      if (testCharacter.voiceConfigs.length > 0) {
        console.log(`\n🎤 测试角色 "${testCharacter.name}" 的声音...`);
        
        const characterTTSRequest = {
          text: `我是${testCharacter.name}，很高兴见到大家。`,
          characterId: testCharacter.id,
          emotion: 'happy',
          format: 'mp3'
        };
        
        try {
          const characterTTSResponse = await fetch('http://localhost:3000/api/ai/generate-tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(characterTTSRequest)
          });
          
          if (characterTTSResponse.ok) {
            const characterTTSResult = await characterTTSResponse.json();
            if (characterTTSResult.success) {
              console.log('✅ 角色语音生成成功');
              console.log('角色语音结果:', {
                duration: characterTTSResult.data.duration.toFixed(1) + '秒',
                size: (characterTTSResult.data.size / 1024).toFixed(1) + 'KB'
              });
            } else {
              console.error('❌ 角色语音生成失败:', characterTTSResult.error);
            }
          } else {
            console.error('❌ 角色语音API调用失败:', characterTTSResponse.status);
          }
        } catch (error) {
          console.error('❌ 角色语音生成异常:', error.message);
        }
      }
    } else {
      console.log('⚠️ 未找到角色');
    }
    
    console.log('\n🎉 TTS API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'GET'
    });
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 检查开发服务器...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ 开发服务器未运行');
    console.log('请先启动开发服务器: npm run dev');
    return;
  }
  
  console.log('✅ 开发服务器正在运行');
  await testTTSAPI();
}

main();
