const { spawn } = require('child_process');

console.log('🔍 开始监控视频生成日志...');
console.log('按 Ctrl+C 停止监控\n');

// 启动开发服务器并监控日志
const server = spawn('npm', ['run', 'dev'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

// 监控标准输出
server.stdout.on('data', (data) => {
  const output = data.toString();
  
  // 过滤视频生成相关的日志
  if (output.includes('🎬') || 
      output.includes('豆包') || 
      output.includes('视频') || 
      output.includes('API') ||
      output.includes('生成') ||
      output.includes('片段') ||
      output.includes('错误') ||
      output.includes('失败')) {
    console.log('📺 [VIDEO LOG]:', output.trim());
  }
});

// 监控标准错误
server.stderr.on('data', (data) => {
  const output = data.toString();
  console.log('❌ [ERROR]:', output.trim());
});

// 处理进程退出
server.on('close', (code) => {
  console.log(`\n服务器进程退出，代码: ${code}`);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n停止监控...');
  server.kill();
  process.exit(0);
});
