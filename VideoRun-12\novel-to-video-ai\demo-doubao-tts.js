async function demoDoubaoTTS() {
  try {
    console.log('🎵 豆包TTS功能演示');
    console.log('='.repeat(50));
    
    // 演示文本列表
    const demoTexts = [
      {
        text: '欢迎使用豆包语音合成大模型！这是一个高质量的中文语音合成服务。',
        description: '欢迎语音',
        emotion: 'happy'
      },
      {
        text: '在一个遥远的古代王国里，住着一位美丽善良的公主。她有着如星辰般闪亮的眼睛。',
        description: '故事叙述',
        emotion: 'neutral'
      },
      {
        text: '什么？这怎么可能！我不敢相信这是真的！',
        description: '惊讶表达',
        emotion: 'surprised'
      },
      {
        text: '今天的天气非常好，阳光明媚，微风轻拂，是个出门游玩的好日子。',
        description: '天气描述',
        emotion: 'cheerful'
      },
      {
        text: '人工智能技术正在快速发展，语音合成技术已经达到了接近真人的水平。',
        description: '技术介绍',
        emotion: 'professional'
      }
    ];
    
    console.log(`📝 准备生成 ${demoTexts.length} 个演示音频...\n`);
    
    for (let i = 0; i < demoTexts.length; i++) {
      const demo = demoTexts[i];
      console.log(`🎤 演示 ${i + 1}/${demoTexts.length}: ${demo.description}`);
      console.log(`📄 文本: ${demo.text}`);
      console.log(`😊 情感: ${demo.emotion}`);
      
      try {
        const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: demo.text,
            emotion: demo.emotion,
            speed: 1.0,
            format: 'mp3'
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log(`✅ 生成成功!`);
            console.log(`   时长: ${result.data.duration.toFixed(1)}秒`);
            console.log(`   大小: ${(result.data.size / 1024).toFixed(1)}KB`);
            console.log(`   提供商: ${result.data.metadata.provider}`);
            
            // 保存音频文件
            const fs = require('fs');
            const base64Data = result.data.audioUrl.split(',')[1];
            const audioBuffer = Buffer.from(base64Data, 'base64');
            const filename = `demo_${i + 1}_${demo.description.replace(/[^a-zA-Z0-9]/g, '_')}.mp3`;
            fs.writeFileSync(filename, audioBuffer);
            console.log(`   文件: ${filename}`);
            
          } else {
            console.log(`❌ 生成失败: ${result.error}`);
          }
        } else {
          console.log(`❌ API调用失败: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ 请求异常: ${error.message}`);
      }
      
      console.log(''); // 空行分隔
      
      // 避免请求过快
      if (i < demoTexts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 演示不同语速
    console.log('🎛️ 演示不同语速效果...\n');
    
    const speedDemo = {
      text: '这是语速测试，我们来听听不同语速的效果。',
      speeds: [0.8, 1.0, 1.2, 1.5]
    };
    
    for (const speed of speedDemo.speeds) {
      console.log(`🎵 语速 ${speed}x 测试...`);
      
      try {
        const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: speedDemo.text,
            speed: speed,
            format: 'mp3'
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log(`✅ 语速 ${speed}x 生成成功`);
            console.log(`   时长: ${result.data.duration.toFixed(1)}秒`);
            
            // 保存音频文件
            const fs = require('fs');
            const base64Data = result.data.audioUrl.split(',')[1];
            const audioBuffer = Buffer.from(base64Data, 'base64');
            const filename = `speed_${speed}x.mp3`;
            fs.writeFileSync(filename, audioBuffer);
            console.log(`   文件: ${filename}`);
          }
        }
      } catch (error) {
        console.log(`❌ 语速 ${speed}x 测试失败: ${error.message}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 800));
    }
    
    // 演示长文本处理
    console.log('\n📚 演示长文本处理...\n');
    
    const longText = `
在遥远的东方，有一个美丽的国度，那里山清水秀，鸟语花香。
国度里住着勤劳善良的人民，他们日出而作，日落而息，过着平静祥和的生活。
这个国度的国王是一位英明的君主，他深爱着自己的人民，总是为了国家的繁荣昌盛而努力。
有一天，一位神秘的旅者来到了这个国度，他带来了一个古老的传说。
传说中提到，在国度的最高山峰上，隐藏着一颗能够实现任何愿望的神奇宝石。
这个消息很快传遍了整个国度，人们开始议论纷纷。
    `.trim();
    
    console.log(`📄 长文本 (${longText.length} 字符):`);
    console.log(longText.substring(0, 100) + '...');
    
    try {
      const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: longText,
          speed: 1.0,
          format: 'mp3'
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log(`✅ 长文本处理成功!`);
          console.log(`   时长: ${result.data.duration.toFixed(1)}秒`);
          console.log(`   大小: ${(result.data.size / 1024).toFixed(1)}KB`);
          console.log(`   平均语速: ${(longText.length / result.data.duration * 60).toFixed(0)} 字/分钟`);
          
          // 保存音频文件
          const fs = require('fs');
          const base64Data = result.data.audioUrl.split(',')[1];
          const audioBuffer = Buffer.from(base64Data, 'base64');
          fs.writeFileSync('long_text_demo.mp3', audioBuffer);
          console.log(`   文件: long_text_demo.mp3`);
        }
      }
    } catch (error) {
      console.log(`❌ 长文本处理失败: ${error.message}`);
    }
    
    console.log('\n🎉 豆包TTS功能演示完成！');
    console.log('='.repeat(50));
    console.log('📁 生成的音频文件:');
    
    // 列出生成的文件
    const fs = require('fs');
    const files = fs.readdirSync('.').filter(file => file.endsWith('.mp3') && file.includes('demo'));
    files.forEach((file, index) => {
      const stats = fs.statSync(file);
      console.log(`   ${index + 1}. ${file} (${(stats.size / 1024).toFixed(1)}KB)`);
    });
    
    console.log('\n💡 提示: 您可以播放这些MP3文件来听取豆包TTS的效果！');
    
  } catch (error) {
    console.error('❌ 演示失败:', error);
  }
}

// 检查服务器状态
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'GET'
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 检查开发服务器状态...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ 开发服务器未运行');
    console.log('请先启动开发服务器: npm run dev');
    return;
  }
  
  console.log('✅ 开发服务器正在运行\n');
  await demoDoubaoTTS();
}

main();
