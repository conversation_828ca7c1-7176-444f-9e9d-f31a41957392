'use client'

import { useState, useEffect } from 'react'
import { Switch } from '@headlessui/react'
import { Settings, Check, X, AlertCircle, Loader, Sliders } from 'lucide-react'

interface ModelConfig {
  id: string
  provider: string
  model: string
  apiKey: string
  enabled: boolean
  name: string
  description: string
  status?: 'connected' | 'disconnected' | 'testing' | 'error'
}

interface AIConfigPanelProps {
  onConfigSaved?: () => void
}

export default function AIConfigPanel({ onConfigSaved }: AIConfigPanelProps) {
  const [models, setModels] = useState<ModelConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showParameterSettings, setShowParameterSettings] = useState<string | null>(null)

  // 预定义的模型配置
  const predefinedModels = [
    // DeepSeek 系列
    {
      id: 'deepseek-reasoner',
      provider: 'deepseek',
      model: 'deepseek-reasoner',
      name: 'DeepSeek Reasoner',
      description: '专业推理模型，适合复杂逻辑分析',
      apiKey: '',
      enabled: false
    },
    // 豆包系列
    {
      id: 'doubao-seedance-endpoint',
      provider: 'doubao',
      model: 'ep-20250622184757-q77k7',
      name: '豆包 Seedance 1.0 Pro',
      description: '豆包视频生成模型，支持高质量文生视频，已通过API连接测试',
      apiKey: '',
      enabled: false
    },
    {
      id: 'doubao-seedream-3-0-t2i',
      provider: 'doubao',
      model: 'ep-20250623162000-p9zzw',
      name: '豆包 Seedream 3.0 T2I',
      description: '豆包图像生成模型，支持高质量文生图，适合角色形象和场景生成',
      apiKey: '',
      enabled: false
    },
    {
      id: 'doubao-seedance-1-0-lite-i2v-250428',
      provider: 'doubao',
      model: 'ep-20250624013749-dbrbn',
      name: '豆包 Seedance 1.0 Lite I2V',
      description: '豆包图生视频模型，支持基于图像生成5-8秒视频，适合角色动作和场景动画',
      apiKey: '',
      enabled: false,
      supportsVideo: true,
      supportsImageToVideo: true
    },
    {
      id: 'doubao-seedance-1-0-lite-t2v-250428',
      provider: 'doubao',
      model: 'ep-20250624013223-bwdtj',
      name: '豆包 Seedance 1.0 Lite T2V',
      description: '豆包文生视频模型，支持基于文本生成5-8秒视频，适合创意场景和环境建立',
      apiKey: '',
      enabled: false,
      supportsVideo: true,
      supportsImageToVideo: false
    },
    // SkyReels-V2 本地模型
    {
      id: 'skyreels-v2-df-1-3b-540p',
      provider: 'skyreels',
      model: 'SkyReels-V2-DF-1.3B-540P',
      name: 'SkyReels-V2 本地模型',
      description: '本地部署的SkyReels-V2文生视频模型，支持无限长度视频生成，540P分辨率',
      apiKey: 'http://localhost:8000',
      enabled: false,
      supportsVideo: true,
      supportsImageToVideo: false
    }
  ]

  // 加载模型配置
  useEffect(() => {
    loadModels()
  }, [])

  const loadModels = async () => {
    try {
      const response = await fetch('/api/models')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // 合并预定义模型和已保存的配置
          const savedModels = data.data || []
          const mergedModels = predefinedModels.map(predefined => {
            // 通过 provider + model 匹配，而不是通过ID
            const saved = savedModels.find((s: ModelConfig) =>
              s.provider === predefined.provider && s.model === predefined.model
            )
            return saved ? {
              ...predefined,
              ...saved,
              // 保持预定义的描述和名称，但使用数据库的ID和状态
              name: predefined.name,
              description: predefined.description
            } : predefined
          })
          setModels(mergedModels)
        } else {
          setModels(predefinedModels)
        }
      } else {
        setModels(predefinedModels)
      }
    } catch (error) {
      console.error('加载模型配置失败:', error)
      setModels(predefinedModels)
    } finally {
      setIsLoading(false)
    }
  }

  // 更新模型配置
  const updateModel = (id: string, updates: Partial<ModelConfig>) => {
    setModels(prev => prev.map(model => {
      if (model.id === id) {
        const updated = { ...model, ...updates }
        // 如果更新包含新的ID，需要特殊处理
        if (updates.id && updates.id !== id) {
          // ID发生了变化，这通常发生在保存新模型时
          return updated
        }
        return updated
      }
      return model
    }))
  }

  // 保存模型配置
  const saveModel = async (model: ModelConfig) => {
    try {
      // 检查是否为新模型
      const isNewModel = !model.id.startsWith('cmc')

      const response = await fetch('/api/models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...model,
          id: isNewModel ? undefined : model.id // 新模型不传ID
        }),
      })

      const data = await response.json()

      if (data.success) {
        if (isNewModel) {
          // 新模型：更新ID并设置状态
          const oldId = model.id
          const dbId = data.data.id
          setModels(prev => prev.map(m =>
            m.id === oldId ? { ...m, id: dbId, status: 'connected' } : m
          ))
        } else {
          // 现有模型：只更新状态
          updateModel(model.id, { status: 'connected' })
        }
        onConfigSaved?.()
      } else {
        updateModel(model.id, { status: 'error' })
        setError(data.error || '保存配置失败')
      }
    } catch (error) {
      updateModel(model.id, { status: 'error' })
      setError('保存配置失败，请重试')
    }
  }

  // 测试模型连接
  const testModel = async (model: ModelConfig) => {
    if (!model.apiKey) {
      setError('请先输入API密钥')
      return
    }

    updateModel(model.id, { status: 'testing' })
    setError(null)

    try {
      // 检查模型是否已保存到数据库（通过ID格式判断）
      const isNewModel = !model.id.startsWith('cmc')

      if (isNewModel) {
        // 这是新模型，需要先保存
        const saveResponse = await fetch('/api/models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...model,
            id: undefined // 不传ID让数据库生成
          }),
        })

        const saveData = await saveResponse.json()
        if (!saveData.success) {
          updateModel(model.id, { status: 'error' })
          setError(saveData.error || '保存配置失败')
          return
        }

        // 更新本地模型，使用数据库返回的ID
        const dbId = saveData.data.id
        const oldId = model.id
        setModels(prev => prev.map(m =>
          m.id === oldId ? { ...m, id: dbId } : m
        ))
        model = { ...model, id: dbId }
      }

      const response = await fetch('/api/models/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(model),
      })

      const data = await response.json()

      if (data.success) {
        updateModel(model.id, { status: 'connected' })
      } else {
        updateModel(model.id, { status: 'error' })
        setError(data.error || '连接测试失败')
      }
    } catch (error) {
      updateModel(model.id, { status: 'error' })
      setError('连接测试失败，请检查网络')
    }
  }

  // 按提供商分组模型
  const groupedModels = models.reduce((groups, model) => {
    const provider = model.provider
    if (!groups[provider]) {
      groups[provider] = []
    }
    groups[provider].push(model)
    return groups
  }, {} as Record<string, ModelConfig[]>)

  // 获取提供商显示名称
  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'deepseek': return 'DeepSeek'
      case 'doubao': return '豆包 (火山引擎)'
      case 'skyreels': return 'SkyReels-V2 (本地模型)'
      default: return provider
    }
  }

  // 获取状态图标
  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'connected': return <Check className="text-green-500" size={16} />
      case 'testing': return <Loader className="text-blue-500 animate-spin" size={16} />
      case 'error': return <X className="text-red-500" size={16} />
      default: return <AlertCircle className="text-gray-400" size={16} />
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center justify-center py-8">
          <Loader className="animate-spin mr-2" size={20} />
          <span>加载模型配置中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold mb-6 flex items-center">
        <Settings className="mr-2" size={24} />
        大模型配置
      </h2>

      {/* 错误信息 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* 按提供商分组显示模型 */}
      <div className="space-y-6">
        {Object.entries(groupedModels).map(([provider, providerModels]) => (
          <div key={provider} className="border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4 text-gray-900">
              {getProviderName(provider)}
            </h3>

            <div className="space-y-4">
              {providerModels.map((model) => (
                <div key={model.id} className="border border-gray-100 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(model.status)}
                        <h4 className="font-medium text-gray-900">{model.name}</h4>
                        <button
                          onClick={() => setShowParameterSettings(showParameterSettings === model.id ? null : model.id)}
                          className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                          title="API参数设置"
                        >
                          <Sliders size={16} />
                        </button>
                      </div>
                      <Switch
                        checked={model.enabled}
                        onChange={(enabled) => {
                          updateModel(model.id, { enabled })
                          if (enabled && model.apiKey) {
                            saveModel({ ...model, enabled })
                          }
                        }}
                        className={`${
                          model.enabled ? 'bg-purple-600' : 'bg-gray-200'
                        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`}
                      >
                        <span
                          className={`${
                            model.enabled ? 'translate-x-6' : 'translate-x-1'
                          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                        />
                      </Switch>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-3">{model.description}</p>

                  <div className="flex space-x-2">
                    <input
                      type={model.provider === 'skyreels' ? 'text' : 'password'}
                      value={model.apiKey}
                      onChange={(e) => updateModel(model.id, { apiKey: e.target.value })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                      placeholder={model.provider === 'skyreels' ? '输入API服务器地址 (如: http://localhost:8000)' : '输入API密钥...'}
                    />
                    <button
                      onClick={() => testModel(model)}
                      disabled={!model.apiKey || model.status === 'testing'}
                      className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                      title="测试API连接"
                    >
                      {model.status === 'testing' ? '测试中' : '测试'}
                    </button>
                    <button
                      onClick={() => saveModel(model)}
                      disabled={!model.apiKey}
                      className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                      title="保存模型配置"
                    >
                      保存
                    </button>
                  </div>

                  {/* API参数设置面板 */}
                  {showParameterSettings === model.id && (
                    <div className="mt-4 p-4 bg-white border border-gray-200 rounded-lg">
                      <h5 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                        <Sliders className="mr-2" size={18} />
                        API调用参数设置
                      </h5>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Temperature 设置 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Temperature (创造性)
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={model.temperature || 0.7}
                            onChange={(e) => updateModel(model.id, { temperature: parseFloat(e.target.value) })}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0 (保守)</span>
                            <span className="font-medium">{model.temperature || 0.7}</span>
                            <span>2 (创新)</span>
                          </div>
                        </div>

                        {/* Max Tokens 设置 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Max Tokens (最大输出)
                          </label>
                          <input
                            type="number"
                            min="100"
                            max="8000"
                            step="100"
                            value={model.maxTokens || 4000}
                            onChange={(e) => updateModel(model.id, { maxTokens: parseInt(e.target.value) })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                          />
                          <div className="text-xs text-gray-500 mt-1">
                            当前: {model.maxTokens || 4000} tokens
                          </div>
                        </div>

                        {/* Top P 设置 */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Top P (核心采样)
                          </label>
                          <input
                            type="range"
                            min="0.1"
                            max="1"
                            step="0.05"
                            value={model.topP || 0.9}
                            onChange={(e) => updateModel(model.id, { topP: parseFloat(e.target.value) })}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.1 (聚焦)</span>
                            <span className="font-medium">{model.topP || 0.9}</span>
                            <span>1.0 (多样)</span>
                          </div>
                        </div>
                      </div>

                      {/* 参数说明 */}
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <h6 className="text-sm font-medium text-blue-900 mb-2">参数说明：</h6>
                        <ul className="text-xs text-blue-800 space-y-1">
                          <li><strong>Temperature:</strong> 控制输出的随机性，值越高越有创造性</li>
                          <li><strong>Max Tokens:</strong> 限制模型单次输出的最大长度</li>
                          <li><strong>Top P:</strong> 控制词汇选择范围，值越小输出越聚焦</li>
                        </ul>
                      </div>

                      {/* 保存参数按钮 */}
                      <div className="mt-4 flex justify-end">
                        <button
                          onClick={() => {
                            saveModel(model)
                            setShowParameterSettings(null)
                          }}
                          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
                          title="保存API参数设置"
                        >
                          保存参数设置
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
