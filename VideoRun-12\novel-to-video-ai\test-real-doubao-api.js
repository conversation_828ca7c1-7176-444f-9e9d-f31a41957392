const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRealDoubaoAPI() {
  try {
    console.log('🎬 测试真实豆包视频生成API');
    console.log('=' .repeat(50));

    // 获取 Doubao-Seedance-1.0-pro 配置
    const config = await prisma.aIConfig.findFirst({
      where: {
        name: 'Doubao-Seedance-1.0-pro',
        enabled: true
      }
    });

    if (!config) {
      console.log('❌ 未找到 Doubao-Seedance-1.0-pro 配置');
      return;
    }

    console.log('✅ 找到模型配置:');
    console.log('   名称:', config.name);
    console.log('   模型ID:', config.model);
    console.log('   API密钥长度:', config.apiKey.length);
    console.log('   API密钥前8位:', config.apiKey.substring(0, 8));

    // 测试API调用 - 使用正确的豆包API端点
    const testPrompt = "一个年轻人在雪山上行走，阳光明媚，画面清晰";
    
    console.log('\n🚀 开始测试API调用...');
    console.log('📝 测试提示词:', testPrompt);

    const requestBody = {
      model: config.model,
      content: [
        {
          type: "text",
          text: testPrompt
        }
      ]
    };

    console.log('\n📡 发送请求到豆包API...');
    console.log('🔗 URL: https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks');
    console.log('🔑 Authorization: Bearer', config.apiKey.substring(0, 8) + '...');

    const startTime = Date.now();
    
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const endTime = Date.now();
    console.log(`⏱️ 请求耗时: ${endTime - startTime}ms`);

    console.log('\n📊 API响应状态:', response.status);
    console.log('📊 响应状态文本:', response.statusText);

    // 获取响应内容
    const responseText = await response.text();
    console.log('\n📄 原始响应内容:');
    console.log(responseText);

    if (!response.ok) {
      console.log('\n❌ API调用失败:');
      console.log('   状态码:', response.status);
      console.log('   状态文本:', response.statusText);
      
      // 尝试解析错误信息
      try {
        const errorJson = JSON.parse(responseText);
        console.log('   解析后的错误:', JSON.stringify(errorJson, null, 2));
        
        // 分析具体错误类型
        if (errorJson.error) {
          console.log('\n🔍 错误分析:');
          if (errorJson.error.code === 'invalid_api_key') {
            console.log('   ❌ API密钥无效');
            console.log('   💡 请检查火山引擎控制台中的API密钥是否正确');
          } else if (errorJson.error.code === 'model_not_found') {
            console.log('   ❌ 模型未找到');
            console.log('   💡 请检查模型ID是否正确:', config.model);
          } else if (errorJson.error.code === 'insufficient_quota') {
            console.log('   ❌ 配额不足');
            console.log('   💡 请检查账户余额或配额');
          } else {
            console.log('   ❓ 其他错误:', errorJson.error.message);
          }
        }
      } catch (e) {
        console.log('   原始错误文本:', responseText);
      }
      return;
    }

    // 解析成功响应
    try {
      const result = JSON.parse(responseText);
      console.log('\n✅ API调用成功!');
      console.log('📋 响应结果:', JSON.stringify(result, null, 2));

      if (result.task_id) {
        console.log('\n🎯 获得任务ID:', result.task_id);
        console.log('📊 任务状态:', result.status || '未知');
        
        // 保存任务信息用于后续测试
        console.log('\n💾 任务信息已记录，可用于后续状态查询测试');
        console.log('   任务ID:', result.task_id);
        console.log('   创建时间:', new Date().toISOString());
      }
    } catch (e) {
      console.log('❌ 解析响应失败:', e.message);
      console.log('原始响应:', responseText);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.log('💡 网络连接问题，请检查网络连接');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('💡 连接被拒绝，请检查API端点是否正确');
    } else if (error.name === 'AbortError') {
      console.log('💡 请求超时，请稍后重试');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testRealDoubaoAPI();
