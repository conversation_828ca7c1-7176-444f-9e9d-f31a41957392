{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/support.js"], "sourcesContent": ["\"use strict\";\n\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\n\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n}\nelse {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([buffer], {\n            type: \"application/zip\"\n        }).size === 0;\n    }\n    catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        }\n        catch (e) {\n            exports.blob = false;\n        }\n    }\n}\n\ntry {\n    exports.nodestream = !!require(\"readable-stream\").Readable;\n} catch(e) {\n    exports.nodestream = false;\n}\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,MAAM,GAAG;AACjB,QAAQ,KAAK,GAAG;AAChB,QAAQ,MAAM,GAAG;AACjB,QAAQ,WAAW,GAAG,OAAO,gBAAgB,eAAe,OAAO,eAAe;AAClF,QAAQ,UAAU,GAAG,OAAO,WAAW;AACvC,wEAAwE;AACxE,QAAQ,UAAU,GAAG,OAAO,eAAe;AAE3C,IAAI,OAAO,gBAAgB,aAAa;IACpC,QAAQ,IAAI,GAAG;AACnB,OACK;IACD,IAAI,SAAS,IAAI,YAAY;IAC7B,IAAI;QACA,QAAQ,IAAI,GAAG,IAAI,KAAK;YAAC;SAAO,EAAE;YAC9B,MAAM;QACV,GAAG,IAAI,KAAK;IAChB,EACA,OAAO,GAAG;QACN,IAAI;YACA,IAAI,UAAU,KAAK,WAAW,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,IAAI,KAAK,aAAa;YACrG,IAAI,UAAU,IAAI;YAClB,QAAQ,MAAM,CAAC;YACf,QAAQ,IAAI,GAAG,QAAQ,OAAO,CAAC,mBAAmB,IAAI,KAAK;QAC/D,EACA,OAAO,GAAG;YACN,QAAQ,IAAI,GAAG;QACnB;IACJ;AACJ;AAEA,IAAI;IACA,QAAQ,UAAU,GAAG,CAAC,CAAC,qGAA2B,QAAQ;AAC9D,EAAE,OAAM,GAAG;IACP,QAAQ,UAAU,GAAG;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/base64.js"], "sourcesContent": ["\"use strict\";\nvar utils = require(\"./utils\");\nvar support = require(\"./support\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while (i < input.length) {\n        remainingBytes = len - i;\n\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n\n        enc1 = chr1 >> 2;\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        enc3 = remainingBytes > 1 ? (((chr2 & 15) << 2) | (chr3 >> 6)) : 64;\n        enc4 = remainingBytes > 2 ? (chr3 & 63) : 64;\n\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n\n    }\n\n    return output.join(\"\");\n};\n\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n\n    var dataUrlPrefix = \"data:\";\n\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n\n    var totalLength = input.length * 3 / 4;\n    if(input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if(input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength|0);\n    } else {\n        output = new Array(totalLength|0);\n    }\n\n    while (i < input.length) {\n\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n\n        output[resultIndex++] = chr1;\n\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n\n    }\n\n    return output;\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AACJ,mBAAmB;AACnB,IAAI,UAAU;AAGd,6BAA6B;AAC7B,QAAQ,MAAM,GAAG,SAAS,KAAK;IAC3B,IAAI,SAAS,EAAE;IACf,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;IACxC,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,iBAAiB;IAEhD,IAAI,UAAU,MAAM,SAAS,CAAC,WAAW;IACzC,MAAO,IAAI,MAAM,MAAM,CAAE;QACrB,iBAAiB,MAAM;QAEvB,IAAI,CAAC,SAAS;YACV,OAAO,MAAM,UAAU,CAAC;YACxB,OAAO,IAAI,MAAM,MAAM,UAAU,CAAC,OAAO;YACzC,OAAO,IAAI,MAAM,MAAM,UAAU,CAAC,OAAO;QAC7C,OAAO;YACH,OAAO,KAAK,CAAC,IAAI;YACjB,OAAO,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG;YAC9B,OAAO,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG;QAClC;QAEA,OAAO,QAAQ;QACf,OAAO,AAAC,CAAC,OAAO,CAAC,KAAK,IAAM,QAAQ;QACpC,OAAO,iBAAiB,IAAK,AAAC,CAAC,OAAO,EAAE,KAAK,IAAM,QAAQ,IAAM;QACjE,OAAO,iBAAiB,IAAK,OAAO,KAAM;QAE1C,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;IAEpG;IAEA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,6BAA6B;AAC7B,QAAQ,MAAM,GAAG,SAAS,KAAK;IAC3B,IAAI,MAAM,MAAM;IAChB,IAAI,MAAM,MAAM,MAAM;IACtB,IAAI,IAAI,GAAG,cAAc;IAEzB,IAAI,gBAAgB;IAEpB,IAAI,MAAM,MAAM,CAAC,GAAG,cAAc,MAAM,MAAM,eAAe;QACzD,iDAAiD;QACjD,6DAA6D;QAC7D,iCAAiC;QACjC,mEAAmE;QACnE,uEAAuE;QACvE,oBAAoB;QACpB,MAAM,IAAI,MAAM;IACpB;IAEA,QAAQ,MAAM,OAAO,CAAC,oBAAoB;IAE1C,IAAI,cAAc,MAAM,MAAM,GAAG,IAAI;IACrC,IAAG,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,QAAQ,MAAM,CAAC,KAAK;QACtD;IACJ;IACA,IAAG,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,QAAQ,MAAM,CAAC,KAAK;QACtD;IACJ;IACA,IAAI,cAAc,MAAM,GAAG;QACvB,mEAAmE;QACnE,sCAAsC;QACtC,sCAAsC;QACtC,sEAAsE;QACtE,4BAA4B;QAC5B,4DAA4D;QAC5D,MAAM,IAAI,MAAM;IACpB;IACA,IAAI;IACJ,IAAI,QAAQ,UAAU,EAAE;QACpB,SAAS,IAAI,WAAW,cAAY;IACxC,OAAO;QACH,SAAS,IAAI,MAAM,cAAY;IACnC;IAEA,MAAO,IAAI,MAAM,MAAM,CAAE;QAErB,OAAO,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;QACpC,OAAO,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;QACpC,OAAO,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;QACpC,OAAO,QAAQ,OAAO,CAAC,MAAM,MAAM,CAAC;QAEpC,OAAO,AAAC,QAAQ,IAAM,QAAQ;QAC9B,OAAO,AAAC,CAAC,OAAO,EAAE,KAAK,IAAM,QAAQ;QACrC,OAAO,AAAC,CAAC,OAAO,CAAC,KAAK,IAAK;QAE3B,MAAM,CAAC,cAAc,GAAG;QAExB,IAAI,SAAS,IAAI;YACb,MAAM,CAAC,cAAc,GAAG;QAC5B;QACA,IAAI,SAAS,IAAI;YACb,MAAM,CAAC,cAAc,GAAG;QAC5B;IAEJ;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/nodejsUtils.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */\n    isNode : typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */\n    newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error(\"The \\\"data\\\" argument must not be a number\");\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */\n    allocBuffer: function (size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */\n    isBuffer : function(b){\n        return Buffer.isBuffer(b);\n    },\n\n    isStream : function (obj) {\n        return obj &&\n            typeof obj.on === \"function\" &&\n            typeof obj.pause === \"function\" &&\n            typeof obj.resume === \"function\";\n    }\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;IACb;;;;KAIC,GACD,QAAS,OAAO,WAAW;IAC3B;;;;;KAKC,GACD,eAAe,SAAS,IAAI,EAAE,QAAQ;QAClC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,EAAE;YAChD,OAAO,OAAO,IAAI,CAAC,MAAM;QAC7B,OAAO;YACH,IAAI,OAAO,SAAS,UAAU;gBAC1B,yDAAyD;gBACzD,gEAAgE;gBAChE,MAAM,IAAI,MAAM;YACpB;YACA,OAAO,IAAI,OAAO,MAAM;QAC5B;IACJ;IACA;;;;KAIC,GACD,aAAa,SAAU,IAAI;QACvB,IAAI,OAAO,KAAK,EAAE;YACd,OAAO,OAAO,KAAK,CAAC;QACxB,OAAO;YACH,IAAI,MAAM,IAAI,OAAO;YACrB,IAAI,IAAI,CAAC;YACT,OAAO;QACX;IACJ;IACA;;;;KAIC,GACD,UAAW,SAAS,CAAC;QACjB,OAAO,OAAO,QAAQ,CAAC;IAC3B;IAEA,UAAW,SAAU,GAAG;QACpB,OAAO,OACH,OAAO,IAAI,EAAE,KAAK,cAClB,OAAO,IAAI,KAAK,KAAK,cACrB,OAAO,IAAI,MAAM,KAAK;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/external.js"], "sourcesContent": ["\"use strict\";\n\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = require(\"lie\");\n}\n\n/**\n * Let the user use/change some implementations.\n */\nmodule.exports = {\n    Promise: ES6Promise\n};\n"], "names": [], "mappings": "AAAA;AAEA,gCAAgC;AAChC,8EAA8E;AAC9E,2EAA2E;AAC3E,IAAI,aAAa;AACjB,IAAI,OAAO,YAAY,aAAa;IAChC,aAAa;AACjB,OAAO;IACH;AACJ;AAEA;;CAEC,GACD,OAAO,OAAO,GAAG;IACb,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/utils.js"], "sourcesContent": ["\"use strict\";\n\nvar support = require(\"./support\");\nvar base64 = require(\"./base64\");\nvar nodejsUtils = require(\"./nodejsUtils\");\nvar external = require(\"./external\");\nrequire(\"setimmediate\");\n\n\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */\nfunction string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */\nexports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n\n    try {\n        // Blob constructor\n        return new Blob([part], {\n            type: type\n        });\n    }\n    catch (e) {\n\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        }\n        catch (e) {\n\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n\n\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n    return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n    for (var i = 0; i < str.length; ++i) {\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */\nvar arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */\n    stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while (k < len) {\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            }\n            else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */\n    stringifyByChar: function(array){\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++) {\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed : {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */\n        uint8array : (function () {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */\n        nodebuffer : (function () {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })()\n    }\n};\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536,\n        type = exports.getTypeOf(array),\n        canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n\n    if (canUseApply) {\n        while (chunk > 1) {\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\n\nexports.applyFromCharCode = arrayLikeToString;\n\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for (var i = 0; i < arrayFrom.length; i++) {\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return (new Uint8Array(input)).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */\nexports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for (var index = 0; index < parts.length; index++) {\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || (part === \"\" && index !== 0 && index !== parts.length - 1)) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\n\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function(str) {\n    var res = \"\",\n        code, i;\n    for (i = 0; i < (str || \"\").length; i++) {\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */\nexports.delay = function(callback, args, self) {\n    setImmediate(function () {\n        callback.apply(self || null, args || []);\n    });\n};\n\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */\nexports.inherits = function (ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function() {\n    var result = {}, i, attr;\n    for (i = 0; i < arguments.length; i++) { // arguments is not enumerable in some browsers\n        for (attr in arguments[i]) {\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */\nexports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n\n\n        var isBlob = support.blob && (data instanceof Blob || [\"[object File]\", \"[object Blob]\"].indexOf(Object.prototype.toString.call(data)) !== -1);\n\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function (resolve, reject) {\n                var reader = new FileReader();\n\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n\n        if (!dataType) {\n            return external.Promise.reject(\n                new Error(\"Can't read the data of '\" + name + \"'. Is it \" +\n                          \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\")\n            );\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            }\n            else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;;AAIJ;;;;;;CAMC,GACD,SAAS,cAAc,GAAG;IACtB,IAAI,SAAS;IACb,IAAI,QAAQ,UAAU,EAAE;QACpB,SAAS,IAAI,WAAW,IAAI,MAAM;IACtC,OAAO;QACH,SAAS,IAAI,MAAM,IAAI,MAAM;IACjC;IACA,OAAO,kBAAkB,KAAK;AAClC;AAEA;;;;;;;;;;;;CAYC,GACD,QAAQ,OAAO,GAAG,SAAS,IAAI,EAAE,IAAI;IACjC,QAAQ,YAAY,CAAC;IAErB,IAAI;QACA,mBAAmB;QACnB,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE;YACpB,MAAM;QACV;IACJ,EACA,OAAO,GAAG;QAEN,IAAI;YACA,oCAAoC;YACpC,IAAI,UAAU,KAAK,WAAW,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,IAAI,KAAK,aAAa;YACrG,IAAI,UAAU,IAAI;YAClB,QAAQ,MAAM,CAAC;YACf,OAAO,QAAQ,OAAO,CAAC;QAC3B,EACA,OAAO,GAAG;YAEN,gBAAgB;YAChB,MAAM,IAAI,MAAM;QACpB;IACJ;AAGJ;AACA;;;;CAIC,GACD,SAAS,SAAS,KAAK;IACnB,OAAO;AACX;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,GAAG,EAAE,KAAK;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAAG;QACjC,KAAK,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC,KAAK;IACnC;IACA,OAAO;AACX;AAEA;;;;CAIC,GACD,IAAI,sBAAsB;IACtB;;;;;;;;KAQC,GACD,kBAAkB,SAAS,KAAK,EAAE,IAAI,EAAE,KAAK;QACzC,IAAI,SAAS,EAAE,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM;QAC1C,WAAW;QACX,IAAI,OAAO,OAAO;YACd,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM;QAC3C;QACA,MAAO,IAAI,IAAK;YACZ,IAAI,SAAS,WAAW,SAAS,cAAc;gBAC3C,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,OAAO;YACnF,OACK;gBACD,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,OAAO;YACtF;YACA,KAAK;QACT;QACA,OAAO,OAAO,IAAI,CAAC;IACvB;IACA;;;;;;KAMC,GACD,iBAAiB,SAAS,KAAK;QAC3B,IAAI,YAAY;QAChB,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAClC,aAAa,OAAO,YAAY,CAAC,KAAK,CAAC,EAAE;QAC7C;QACA,OAAO;IACX;IACA,gBAAiB;QACb;;SAEC,GACD,YAAa,AAAC;YACV,IAAI;gBACA,OAAO,QAAQ,UAAU,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,WAAW,IAAI,MAAM,KAAK;YAC/F,EAAE,OAAO,GAAG;gBACR,OAAO;YACX;QACJ;QACA;;SAEC,GACD,YAAa,AAAC;YACV,IAAI;gBACA,OAAO,QAAQ,UAAU,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,YAAY,WAAW,CAAC,IAAI,MAAM,KAAK;YACxG,EAAE,OAAO,GAAG;gBACR,OAAO;YACX;QACJ;IACJ;AACJ;AAEA;;;;CAIC,GACD,SAAS,kBAAkB,KAAK;IAC5B,uBAAuB;IACvB,uBAAuB;IACvB,6DAA6D;IAC7D,8DAA8D;IAC9D,2DAA2D;IAC3D,EAAE;IACF,uEAAuE;IACvE,EAAE;IACF,uFAAuF;IACvF,0EAA0E;IAC1E,IAAI,QAAQ,OACR,OAAO,QAAQ,SAAS,CAAC,QACzB,cAAc;IAClB,IAAI,SAAS,cAAc;QACvB,cAAc,oBAAoB,cAAc,CAAC,UAAU;IAC/D,OAAO,IAAI,SAAS,cAAc;QAC9B,cAAc,oBAAoB,cAAc,CAAC,UAAU;IAC/D;IAEA,IAAI,aAAa;QACb,MAAO,QAAQ,EAAG;YACd,IAAI;gBACA,OAAO,oBAAoB,gBAAgB,CAAC,OAAO,MAAM;YAC7D,EAAE,OAAO,GAAG;gBACR,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAC/B;QACJ;IACJ;IAEA,uDAAuD;IACvD,iCAAiC;IACjC,OAAO,oBAAoB,eAAe,CAAC;AAC/C;AAEA,QAAQ,iBAAiB,GAAG;AAG5B;;;;;CAKC,GACD,SAAS,qBAAqB,SAAS,EAAE,OAAO;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;IAC7B;IACA,OAAO;AACX;AAEA,yEAAyE;AACzE,IAAI,YAAY,CAAC;AAEjB,cAAc;AACd,SAAS,CAAC,SAAS,GAAG;IAClB,UAAU;IACV,SAAS,SAAS,KAAK;QACnB,OAAO,kBAAkB,OAAO,IAAI,MAAM,MAAM,MAAM;IAC1D;IACA,eAAe,SAAS,KAAK;QACzB,OAAO,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,MAAM;IAC1D;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,kBAAkB,OAAO,IAAI,WAAW,MAAM,MAAM;IAC/D;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,kBAAkB,OAAO,YAAY,WAAW,CAAC,MAAM,MAAM;IACxE;AACJ;AAEA,aAAa;AACb,SAAS,CAAC,QAAQ,GAAG;IACjB,UAAU;IACV,SAAS;IACT,eAAe,SAAS,KAAK;QACzB,OAAO,AAAC,IAAI,WAAW,OAAQ,MAAM;IACzC;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,IAAI,WAAW;IAC1B;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,YAAY,aAAa,CAAC;IACrC;AACJ;AAEA,mBAAmB;AACnB,SAAS,CAAC,cAAc,GAAG;IACvB,UAAU,SAAS,KAAK;QACpB,OAAO,kBAAkB,IAAI,WAAW;IAC5C;IACA,SAAS,SAAS,KAAK;QACnB,OAAO,qBAAqB,IAAI,WAAW,QAAQ,IAAI,MAAM,MAAM,UAAU;IACjF;IACA,eAAe;IACf,cAAc,SAAS,KAAK;QACxB,OAAO,IAAI,WAAW;IAC1B;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,YAAY,aAAa,CAAC,IAAI,WAAW;IACpD;AACJ;AAEA,kBAAkB;AAClB,SAAS,CAAC,aAAa,GAAG;IACtB,UAAU;IACV,SAAS,SAAS,KAAK;QACnB,OAAO,qBAAqB,OAAO,IAAI,MAAM,MAAM,MAAM;IAC7D;IACA,eAAe,SAAS,KAAK;QACzB,OAAO,MAAM,MAAM;IACvB;IACA,cAAc;IACd,cAAc,SAAS,KAAK;QACxB,OAAO,YAAY,aAAa,CAAC;IACrC;AACJ;AAEA,kBAAkB;AAClB,SAAS,CAAC,aAAa,GAAG;IACtB,UAAU;IACV,SAAS,SAAS,KAAK;QACnB,OAAO,qBAAqB,OAAO,IAAI,MAAM,MAAM,MAAM;IAC7D;IACA,eAAe,SAAS,KAAK;QACzB,OAAO,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,MAAM;IAC9D;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,qBAAqB,OAAO,IAAI,WAAW,MAAM,MAAM;IAClE;IACA,cAAc;AAClB;AAEA;;;;;;;CAOC,GACD,QAAQ,WAAW,GAAG,SAAS,UAAU,EAAE,KAAK;IAC5C,IAAI,CAAC,OAAO;QACR,uBAAuB;QACvB,8BAA8B;QAC9B,QAAQ;IACZ;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,QAAQ,YAAY,CAAC;IACrB,IAAI,YAAY,QAAQ,SAAS,CAAC;IAClC,IAAI,SAAS,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC;IAC9C,OAAO;AACX;AAEA;;;;;;;CAOC,GACD,QAAQ,OAAO,GAAG,SAAS,IAAI;IAC3B,IAAI,QAAQ,KAAK,KAAK,CAAC;IACvB,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,MAAM,EAAE,QAAS;QAC/C,IAAI,OAAO,KAAK,CAAC,MAAM;QACvB,uEAAuE;QACvE,IAAI,SAAS,OAAQ,SAAS,MAAM,UAAU,KAAK,UAAU,MAAM,MAAM,GAAG,GAAI;YAC5E;QACJ,OAAO,IAAI,SAAS,MAAM;YACtB,OAAO,GAAG;QACd,OAAO;YACH,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA;;;;;CAKC,GACD,QAAQ,SAAS,GAAG,SAAS,KAAK;IAC9B,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO;IACX;IACA,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,kBAAkB;QAC5D,OAAO;IACX;IACA,IAAI,QAAQ,UAAU,IAAI,YAAY,QAAQ,CAAC,QAAQ;QACnD,OAAO;IACX;IACA,IAAI,QAAQ,UAAU,IAAI,iBAAiB,YAAY;QACnD,OAAO;IACX;IACA,IAAI,QAAQ,WAAW,IAAI,iBAAiB,aAAa;QACrD,OAAO;IACX;AACJ;AAEA;;;;CAIC,GACD,QAAQ,YAAY,GAAG,SAAS,IAAI;IAChC,IAAI,YAAY,OAAO,CAAC,KAAK,WAAW,GAAG;IAC3C,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,OAAO;IAC3B;AACJ;AAEA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,gBAAgB,GAAG,CAAC,GAAG,2DAA2D;AAE1F;;;;CAIC,GACD,QAAQ,MAAM,GAAG,SAAS,GAAG;IACzB,IAAI,MAAM,IACN,MAAM;IACV,IAAK,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAK;QACrC,OAAO,IAAI,UAAU,CAAC;QACtB,OAAO,QAAQ,CAAC,OAAO,KAAK,MAAM,EAAE,IAAI,KAAK,QAAQ,CAAC,IAAI,WAAW;IACzE;IACA,OAAO;AACX;AAEA;;;;CAIC,GACD,QAAQ,KAAK,GAAG,SAAS,QAAQ,EAAE,IAAI,EAAE,KAAI;IACzC,aAAa;QACT,SAAS,KAAK,CAAC,SAAQ,MAAM,QAAQ,EAAE;IAC3C;AACJ;AAEA;;;;;CAKC,GACD,QAAQ,QAAQ,GAAG,SAAU,IAAI,EAAE,SAAS;IACxC,IAAI,MAAM,YAAY;IACtB,IAAI,SAAS,GAAG,UAAU,SAAS;IACnC,KAAK,SAAS,GAAG,IAAI;AACzB;AAEA;;;;;CAKC,GACD,QAAQ,MAAM,GAAG;IACb,IAAI,SAAS,CAAC,GAAG,GAAG;IACpB,IAAK,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACnC,IAAK,QAAQ,SAAS,CAAC,EAAE,CAAE;YACvB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,OAAO,MAAM,CAAC,KAAK,KAAK,aAAa;gBACjG,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK;YACrC;QACJ;IACJ;IACA,OAAO;AACX;AAEA;;;;;;;;CAQC,GACD,QAAQ,cAAc,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,uBAAuB,EAAE,QAAQ;IAE1F,sDAAsD;IACtD,IAAI,UAAU,SAAS,OAAO,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,SAAS,IAAI;QAGhE,IAAI,SAAS,QAAQ,IAAI,IAAI,CAAC,gBAAgB,QAAQ;YAAC;YAAiB;SAAgB,CAAC,OAAO,CAAC,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7I,IAAI,UAAU,OAAO,eAAe,aAAa;YAC7C,OAAO,IAAI,SAAS,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM;gBACjD,IAAI,SAAS,IAAI;gBAEjB,OAAO,MAAM,GAAG,SAAS,CAAC;oBACtB,QAAQ,EAAE,MAAM,CAAC,MAAM;gBAC3B;gBACA,OAAO,OAAO,GAAG,SAAS,CAAC;oBACvB,OAAO,EAAE,MAAM,CAAC,KAAK;gBACzB;gBACA,OAAO,iBAAiB,CAAC;YAC7B;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IAEA,OAAO,QAAQ,IAAI,CAAC,SAAS,IAAI;QAC7B,IAAI,WAAW,QAAQ,SAAS,CAAC;QAEjC,IAAI,CAAC,UAAU;YACX,OAAO,SAAS,OAAO,CAAC,MAAM,CAC1B,IAAI,MAAM,6BAA6B,OAAO,cACpC;QAElB;QACA,+EAA+E;QAC/E,IAAI,aAAa,eAAe;YAC5B,OAAO,QAAQ,WAAW,CAAC,cAAc;QAC7C,OAAO,IAAI,aAAa,UAAU;YAC9B,IAAI,UAAU;gBACV,OAAO,OAAO,MAAM,CAAC;YACzB,OACK,IAAI,UAAU;gBACf,gGAAgG;gBAChG,IAAI,4BAA4B,MAAM;oBAClC,4CAA4C;oBAC5C,iDAAiD;oBACjD,OAAO,cAAc;gBACzB;YACJ;QACJ;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/GenericWorker.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */\nfunction GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\":[],\n        \"end\":[],\n        \"error\":[]\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\n\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */\n    push : function (chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */\n    end : function () {\n        if (this.isFinished) {\n            return false;\n        }\n\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */\n    error : function (e) {\n        if (this.isFinished) {\n            return false;\n        }\n\n        if(this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n\n            this.emit(\"error\", e);\n\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if(this.previous) {\n                this.previous.error(e);\n            }\n\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */\n    on : function (name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */\n    cleanUp : function () {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */\n    emit : function (name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++) {\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */\n    pipe : function (next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */\n    registerPrevious : function (previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous =  previous;\n        var self = this;\n        previous.on(\"data\", function (chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function () {\n            self.end();\n        });\n        previous.on(\"error\", function (e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */\n    pause : function () {\n        if(this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n\n        if(this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */\n    resume : function () {\n        if(!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if(this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if(this.previous) {\n            this.previous.resume();\n        }\n\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */\n    flush : function () {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */\n    processChunk : function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */\n    withStreamInfo : function (key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */\n    mergeStreamInfo : function () {\n        for(var key in this.extraStreamInfo) {\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */\n    lock: function () {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n\n    /**\n     *\n     * Pretty print the workers chain.\n     */\n    toString : function () {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\n\nmodule.exports = GenericWorker;\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,cAAc,IAAI;IACvB,yBAAyB;IACzB,IAAI,CAAC,IAAI,GAAG,QAAQ;IACpB,wDAAwD;IACxD,IAAI,CAAC,UAAU,GAAG,CAAC;IACnB,qDAAqD;IACrD,IAAI,CAAC,cAAc,GAAG;IACtB,sFAAsF;IACtF,IAAI,CAAC,eAAe,GAAG,CAAC;IACxB,6EAA6E;IAC7E,IAAI,CAAC,QAAQ,GAAG;IAChB,+EAA+E;IAC/E,IAAI,CAAC,UAAU,GAAG;IAClB,4FAA4F;IAC5F,IAAI,CAAC,QAAQ,GAAG;IAChB,sBAAsB;IACtB,IAAI,CAAC,UAAU,GAAG;QACd,QAAO,EAAE;QACT,OAAM,EAAE;QACR,SAAQ,EAAE;IACd;IACA,8BAA8B;IAC9B,IAAI,CAAC,QAAQ,GAAG;AACpB;AAEA,cAAc,SAAS,GAAG;IACtB;;;KAGC,GACD,MAAO,SAAU,KAAK;QAClB,IAAI,CAAC,IAAI,CAAC,QAAQ;IACtB;IACA;;;KAGC,GACD,KAAM;QACF,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;QACX;QAEA,IAAI,CAAC,KAAK;QACV,IAAI;YACA,IAAI,CAAC,IAAI,CAAC;YACV,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,UAAU,GAAG;QACtB,EAAE,OAAO,GAAG;YACR,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,OAAQ,SAAU,CAAC;QACf,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;QACX;QAEA,IAAG,IAAI,CAAC,QAAQ,EAAE;YACd,IAAI,CAAC,cAAc,GAAG;QAC1B,OAAO;YACH,IAAI,CAAC,UAAU,GAAG;YAElB,IAAI,CAAC,IAAI,CAAC,SAAS;YAEnB,4DAA4D;YAC5D,8DAA8D;YAC9D,+CAA+C;YAC/C,IAAG,IAAI,CAAC,QAAQ,EAAE;gBACd,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACxB;YAEA,IAAI,CAAC,OAAO;QAChB;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,IAAK,SAAU,IAAI,EAAE,QAAQ;QACzB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,OAAO,IAAI;IACf;IACA;;KAEC,GACD,SAAU;QACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,GAAG;QAC/D,IAAI,CAAC,UAAU,GAAG,EAAE;IACxB;IACA;;;;KAIC,GACD,MAAO,SAAU,IAAI,EAAE,GAAG;QACtB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACvB,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;gBAClD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;YACxC;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAO,SAAU,IAAI;QACjB,OAAO,KAAK,gBAAgB,CAAC,IAAI;IACrC;IACA;;;;;;;KAOC,GACD,kBAAmB,SAAU,QAAQ;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,IAAI,MAAM,iBAAiB,IAAI,GAAG;QAC5C;QAEA,4BAA4B;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,UAAU;QACrC,8BAA8B;QAC9B,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,QAAQ,GAAI;QACjB,IAAI,OAAO,IAAI;QACf,SAAS,EAAE,CAAC,QAAQ,SAAU,KAAK;YAC/B,KAAK,YAAY,CAAC;QACtB;QACA,SAAS,EAAE,CAAC,OAAO;YACf,KAAK,GAAG;QACZ;QACA,SAAS,EAAE,CAAC,SAAS,SAAU,CAAC;YAC5B,KAAK,KAAK,CAAC;QACf;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,OAAQ;QACJ,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACjC,OAAO;QACX;QACA,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAG,IAAI,CAAC,QAAQ,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,KAAK;QACvB;QACA,OAAO;IACX;IACA;;;KAGC,GACD,QAAS;QACL,IAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YAClC,OAAO;QACX;QACA,IAAI,CAAC,QAAQ,GAAG;QAEhB,iDAAiD;QACjD,IAAI,YAAY;QAChB,IAAG,IAAI,CAAC,cAAc,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc;YAC9B,YAAY;QAChB;QACA,IAAG,IAAI,CAAC,QAAQ,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,MAAM;QACxB;QAEA,OAAO,CAAC;IACZ;IACA;;KAEC,GACD,OAAQ,YAAa;IACrB;;;KAGC,GACD,cAAe,SAAS,KAAK;QACzB,IAAI,CAAC,IAAI,CAAC;IACd;IACA;;;;;KAKC,GACD,gBAAiB,SAAU,GAAG,EAAE,KAAK;QACjC,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG;QAC5B,IAAI,CAAC,eAAe;QACpB,OAAO,IAAI;IACf;IACA;;KAEC,GACD,iBAAkB;QACd,IAAI,IAAI,OAAO,IAAI,CAAC,eAAe,CAAE;YACjC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM;gBAClE;YACJ;YACA,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI;QACpD;IACJ;IAEA;;;KAGC,GACD,MAAM;QACF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,IAAI,MAAM,iBAAiB,IAAI,GAAG;QAC5C;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI;QACtB;IACJ;IAEA;;;KAGC,GACD,UAAW;QACP,IAAI,KAAK,YAAY,IAAI,CAAC,IAAI;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,IAAI,CAAC,QAAQ,GAAG,SAAS;QACpC,OAAO;YACH,OAAO;QACX;IACJ;AACJ;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/utf8.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils\");\nvar support = require(\"./support\");\nvar nodejsUtils = require(\"./nodejsUtils\");\nvar GenericWorker = require(\"./stream/GenericWorker\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i=0; i<256; i++) {\n    _utf8len[i] = (i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nvar string2buf = function (str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n    // count binary size\n    for (m_pos = 0; m_pos < str_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n\n    // convert\n    for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */\n            buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */\n            buf[i++] = 0xC0 | (c >>> 6);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else if (c < 0x10000) {\n            /* three bytes */\n            buf[i++] = 0xE0 | (c >>> 12);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else {\n            /* four bytes */\n            buf[i++] = 0xf0 | (c >>> 18);\n            buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        }\n    }\n\n    return buf;\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n\n    max = max || buf.length;\n    if (max > buf.length) { max = buf.length; }\n\n    // go back from last position, until start of sequence found\n    pos = max-1;\n    while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) { return max; }\n\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) { return max; }\n\n    return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n\n// convert array to string\nvar buf2string = function (buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len*2);\n\n    for (out=0, i=0; i<len;) {\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while (c_len > 1 && i < len) {\n            c = (c << 6) | (buf[i++] & 0x3f);\n            c_len--;\n        }\n\n        // terminated by end of string?\n        if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n            utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n        }\n    }\n\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if(utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n\n\n// That's all for the pako functions.\n\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n\n    return string2buf(str);\n};\n\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n    return buf2string(buf);\n};\n\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */\nfunction Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8DecodeWorker.prototype.processChunk = function (chunk) {\n\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if(support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n\n    this.push({\n        data : exports.utf8decode(usableData),\n        meta : chunk.meta\n    });\n};\n\n/**\n * @see GenericWorker.flush\n */\nUtf8DecodeWorker.prototype.flush = function () {\n    if(this.leftOver && this.leftOver.length) {\n        this.push({\n            data : exports.utf8decode(this.leftOver),\n            meta : {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */\nfunction Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8EncodeWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : exports.utf8encode(chunk.data),\n        meta : chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;CAGC,GAED,iEAAiE;AACjE,oFAAoF;AACpF,6CAA6C;AAC7C,IAAI,WAAW,IAAI,MAAM;AACzB,IAAK,IAAI,IAAE,GAAG,IAAE,KAAK,IAAK;IACtB,QAAQ,CAAC,EAAE,GAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI;AAC9F;AACA,QAAQ,CAAC,IAAI,GAAC,QAAQ,CAAC,IAAI,GAAC,GAAG,yBAAyB;AAExD,iDAAiD;AACjD,IAAI,aAAa,SAAU,GAAG;IAC1B,IAAI,KAAK,GAAG,IAAI,OAAO,GAAG,UAAU,IAAI,MAAM,EAAE,UAAU;IAE1D,oBAAoB;IACpB,IAAK,QAAQ,GAAG,QAAQ,SAAS,QAAS;QACtC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,CAAC,IAAI,MAAM,MAAM,UAAW,QAAM,IAAI,SAAU;YAChD,KAAK,IAAI,UAAU,CAAC,QAAM;YAC1B,IAAI,CAAC,KAAK,MAAM,MAAM,QAAQ;gBAC1B,IAAI,UAAU,CAAC,AAAC,IAAI,UAAW,EAAE,IAAI,CAAC,KAAK,MAAM;gBACjD;YACJ;QACJ;QACA,WAAW,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,UAAU,IAAI;IAChE;IAEA,kBAAkB;IAClB,IAAI,QAAQ,UAAU,EAAE;QACpB,MAAM,IAAI,WAAW;IACzB,OAAO;QACH,MAAM,IAAI,MAAM;IACpB;IAEA,UAAU;IACV,IAAK,IAAE,GAAG,QAAQ,GAAG,IAAI,SAAS,QAAS;QACvC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,CAAC,IAAI,MAAM,MAAM,UAAW,QAAM,IAAI,SAAU;YAChD,KAAK,IAAI,UAAU,CAAC,QAAM;YAC1B,IAAI,CAAC,KAAK,MAAM,MAAM,QAAQ;gBAC1B,IAAI,UAAU,CAAC,AAAC,IAAI,UAAW,EAAE,IAAI,CAAC,KAAK,MAAM;gBACjD;YACJ;QACJ;QACA,IAAI,IAAI,MAAM;YACV,YAAY,GACZ,GAAG,CAAC,IAAI,GAAG;QACf,OAAO,IAAI,IAAI,OAAO;YAClB,aAAa,GACb,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QAC3B,OAAO,IAAI,IAAI,SAAS;YACpB,eAAe,GACf,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,IAAI;YAC7B,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QAC3B,OAAO;YACH,cAAc,GACd,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,KAAK;YAC9B,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,IAAI;YAC7B,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QAC3B;IACJ;IAEA,OAAO;AACX;AAEA,kDAAkD;AAClD,uDAAuD;AACvD,+CAA+C;AAC/C,EAAE;AACF,2BAA2B;AAC3B,oCAAoC;AACpC,IAAI,aAAa,SAAS,GAAG,EAAE,GAAG;IAC9B,IAAI;IAEJ,MAAM,OAAO,IAAI,MAAM;IACvB,IAAI,MAAM,IAAI,MAAM,EAAE;QAAE,MAAM,IAAI,MAAM;IAAE;IAE1C,4DAA4D;IAC5D,MAAM,MAAI;IACV,MAAO,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,KAAM;QAAE;IAAO;IAExD,2CAA2C;IAC3C,yDAAyD;IACzD,IAAI,MAAM,GAAG;QAAE,OAAO;IAAK;IAE3B,kEAAkE;IAClE,kBAAkB;IAClB,IAAI,QAAQ,GAAG;QAAE,OAAO;IAAK;IAE7B,OAAO,AAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAO,MAAM;AACpD;AAEA,0BAA0B;AAC1B,IAAI,aAAa,SAAU,GAAG;IAC1B,IAAI,GAAG,KAAK,GAAG;IACf,IAAI,MAAM,IAAI,MAAM;IAEpB,iDAAiD;IACjD,4DAA4D;IAC5D,kDAAkD;IAClD,IAAI,WAAW,IAAI,MAAM,MAAI;IAE7B,IAAK,MAAI,GAAG,IAAE,GAAG,IAAE,KAAM;QACrB,IAAI,GAAG,CAAC,IAAI;QACZ,sBAAsB;QACtB,IAAI,IAAI,MAAM;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAG;QAAU;QAE/C,QAAQ,QAAQ,CAAC,EAAE;QACnB,wBAAwB;QACxB,IAAI,QAAQ,GAAG;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAQ,KAAK,QAAM;YAAG;QAAU;QAEnE,2BAA2B;QAC3B,KAAK,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO;QAC/C,gBAAgB;QAChB,MAAO,QAAQ,KAAK,IAAI,IAAK;YACzB,IAAI,AAAC,KAAK,IAAM,GAAG,CAAC,IAAI,GAAG;YAC3B;QACJ;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,GAAG;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAQ;QAAU;QAErD,IAAI,IAAI,SAAS;YACb,QAAQ,CAAC,MAAM,GAAG;QACtB,OAAO;YACH,KAAK;YACL,QAAQ,CAAC,MAAM,GAAG,SAAU,AAAC,KAAK,KAAM;YACxC,QAAQ,CAAC,MAAM,GAAG,SAAU,IAAI;QACpC;IACJ;IAEA,2BAA2B;IAC3B,IAAI,SAAS,MAAM,KAAK,KAAK;QACzB,IAAG,SAAS,QAAQ,EAAE;YAClB,WAAW,SAAS,QAAQ,CAAC,GAAG;QACpC,OAAO;YACH,SAAS,MAAM,GAAG;QACtB;IACJ;IAEA,oDAAoD;IACpD,OAAO,MAAM,iBAAiB,CAAC;AACnC;AAGA,qCAAqC;AAGrC;;;;;CAKC,GACD,QAAQ,UAAU,GAAG,SAAS,WAAW,GAAG;IACxC,IAAI,QAAQ,UAAU,EAAE;QACpB,OAAO,YAAY,aAAa,CAAC,KAAK;IAC1C;IAEA,OAAO,WAAW;AACtB;AAGA;;;;;CAKC,GACD,QAAQ,UAAU,GAAG,SAAS,WAAW,GAAG;IACxC,IAAI,QAAQ,UAAU,EAAE;QACpB,OAAO,MAAM,WAAW,CAAC,cAAc,KAAK,QAAQ,CAAC;IACzD;IAEA,MAAM,MAAM,WAAW,CAAC,QAAQ,UAAU,GAAG,eAAe,SAAS;IAErE,OAAO,WAAW;AACtB;AAEA;;;CAGC,GACD,SAAS;IACL,cAAc,IAAI,CAAC,IAAI,EAAE;IACzB,kEAAkE;IAClE,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,MAAM,QAAQ,CAAC,kBAAkB;AAEjC;;CAEC,GACD,iBAAiB,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IAErD,IAAI,OAAO,MAAM,WAAW,CAAC,QAAQ,UAAU,GAAG,eAAe,SAAS,MAAM,IAAI;IAEpF,qDAAqD;IACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACvC,IAAG,QAAQ,UAAU,EAAE;YACnB,IAAI,eAAe;YACnB,OAAO,IAAI,WAAW,aAAa,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;YAChE,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxB,KAAK,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC,MAAM;QAC/C,OAAO;YACH,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAChC;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA,IAAI,eAAe,WAAW;IAC9B,IAAI,aAAa;IACjB,IAAI,iBAAiB,KAAK,MAAM,EAAE;QAC9B,IAAI,QAAQ,UAAU,EAAE;YACpB,aAAa,KAAK,QAAQ,CAAC,GAAG;YAC9B,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ,CAAC,cAAc,KAAK,MAAM;QAC3D,OAAO;YACH,aAAa,KAAK,KAAK,CAAC,GAAG;YAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,cAAc,KAAK,MAAM;QACxD;IACJ;IAEA,IAAI,CAAC,IAAI,CAAC;QACN,MAAO,QAAQ,UAAU,CAAC;QAC1B,MAAO,MAAM,IAAI;IACrB;AACJ;AAEA;;CAEC,GACD,iBAAiB,SAAS,CAAC,KAAK,GAAG;IAC/B,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACtC,IAAI,CAAC,IAAI,CAAC;YACN,MAAO,QAAQ,UAAU,CAAC,IAAI,CAAC,QAAQ;YACvC,MAAO,CAAC;QACZ;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;AACJ;AACA,QAAQ,gBAAgB,GAAG;AAE3B;;;CAGC,GACD,SAAS;IACL,cAAc,IAAI,CAAC,IAAI,EAAE;AAC7B;AACA,MAAM,QAAQ,CAAC,kBAAkB;AAEjC;;CAEC,GACD,iBAAiB,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IACrD,IAAI,CAAC,IAAI,CAAC;QACN,MAAO,QAAQ,UAAU,CAAC,MAAM,IAAI;QACpC,MAAO,MAAM,IAAI;IACrB;AACJ;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/ConvertWorker.js"], "sourcesContent": ["\"use strict\";\n\nvar GenericWorker = require(\"./GenericWorker\");\nvar utils = require(\"../utils\");\n\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */\nfunction ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nConvertWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : utils.transformTo(this.destType, chunk.data),\n        meta : chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ;;;;CAIC,GACD,SAAS,cAAc,QAAQ;IAC3B,cAAc,IAAI,CAAC,IAAI,EAAE,sBAAsB;IAC/C,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,MAAM,QAAQ,CAAC,eAAe;AAE9B;;CAEC,GACD,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IAClD,IAAI,CAAC,IAAI,CAAC;QACN,MAAO,MAAM,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI;QAClD,MAAO,MAAM,IAAI;IACrB;AACJ;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js"], "sourcesContent": ["\"use strict\";\n\nvar Readable = require(\"readable-stream\").Readable;\n\nvar utils = require(\"../utils\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/\nfunction NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n\n    var self = this;\n    helper.on(\"data\", function (data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if(updateCb) {\n            updateCb(meta);\n        }\n    })\n        .on(\"error\", function(e) {\n            self.emit(\"error\", e);\n        })\n        .on(\"end\", function () {\n            self.push(null);\n        });\n}\n\n\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\n\nmodule.exports = NodejsStreamOutputAdapter;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,WAAW,qGAA2B,QAAQ;AAElD,IAAI;AACJ,MAAM,QAAQ,CAAC,2BAA2B;AAE1C;;;;;;;AAOA,GACA,SAAS,0BAA0B,MAAM,EAAE,OAAO,EAAE,QAAQ;IACxD,SAAS,IAAI,CAAC,IAAI,EAAE;IACpB,IAAI,CAAC,OAAO,GAAG;IAEf,IAAI,OAAO,IAAI;IACf,OAAO,EAAE,CAAC,QAAQ,SAAU,IAAI,EAAE,IAAI;QAClC,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO;YAClB,KAAK,OAAO,CAAC,KAAK;QACtB;QACA,IAAG,UAAU;YACT,SAAS;QACb;IACJ,GACK,EAAE,CAAC,SAAS,SAAS,CAAC;QACnB,KAAK,IAAI,CAAC,SAAS;IACvB,GACC,EAAE,CAAC,OAAO;QACP,KAAK,IAAI,CAAC;IACd;AACR;AAGA,0BAA0B,SAAS,CAAC,KAAK,GAAG;IACxC,IAAI,CAAC,OAAO,CAAC,MAAM;AACvB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/StreamHelper.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar ConvertWorker = require(\"./ConvertWorker\");\nvar GenericWorker = require(\"./GenericWorker\");\nvar base64 = require(\"../base64\");\nvar support = require(\"../support\");\nvar external = require(\"../external\");\n\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = require(\"../nodejs/NodejsStreamOutputAdapter\");\n    } catch(e) {\n        // ignore\n    }\n}\n\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */\nfunction transformZipOutput(type, content, mimeType) {\n    switch(type) {\n    case \"blob\" :\n        return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n    case \"base64\" :\n        return base64.encode(content);\n    default :\n        return utils.transformTo(type, content);\n    }\n}\n\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */\nfunction concat (type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++) {\n        totalLength += dataArray[i].length;\n    }\n    switch(type) {\n    case \"string\":\n        return dataArray.join(\"\");\n    case \"array\":\n        return Array.prototype.concat.apply([], dataArray);\n    case \"uint8array\":\n        res = new Uint8Array(totalLength);\n        for(i = 0; i < dataArray.length; i++) {\n            res.set(dataArray[i], index);\n            index += dataArray[i].length;\n        }\n        return res;\n    case \"nodebuffer\":\n        return Buffer.concat(dataArray);\n    default:\n        throw new Error(\"concat : unsupported type '\"  + type + \"'\");\n    }\n}\n\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */\nfunction accumulate(helper, updateCallback) {\n    return new external.Promise(function (resolve, reject){\n        var dataArray = [];\n        var chunkType = helper._internalType,\n            resultType = helper._outputType,\n            mimeType = helper._mimeType;\n        helper\n            .on(\"data\", function (data, meta) {\n                dataArray.push(data);\n                if(updateCallback) {\n                    updateCallback(meta);\n                }\n            })\n            .on(\"error\", function(err) {\n                dataArray = [];\n                reject(err);\n            })\n            .on(\"end\", function (){\n                try {\n                    var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                    resolve(result);\n                } catch (e) {\n                    reject(e);\n                }\n                dataArray = [];\n            })\n            .resume();\n    });\n}\n\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */\nfunction StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType) {\n    case \"blob\":\n    case \"arraybuffer\":\n        internalType = \"uint8array\";\n        break;\n    case \"base64\":\n        internalType = \"string\";\n        break;\n    }\n\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch(e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\n\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */\n    accumulate : function (updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */\n    on : function (evt, fn) {\n        var self = this;\n\n        if(evt === \"data\") {\n            this._worker.on(evt, function (chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function () {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    resume : function () {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    pause : function () {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */\n    toNodejsStream : function (updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode : this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\n\n\nmodule.exports = StreamHelper;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,4BAA4B;AAChC,IAAI,QAAQ,UAAU,EAAE;IACpB,IAAI;QACA;IACJ,EAAE,OAAM,GAAG;IACP,SAAS;IACb;AACJ;AAEA;;;;;;;;CAQC,GACD,SAAS,mBAAmB,IAAI,EAAE,OAAO,EAAE,QAAQ;IAC/C,OAAO;QACP,KAAK;YACD,OAAO,MAAM,OAAO,CAAC,MAAM,WAAW,CAAC,eAAe,UAAU;QACpE,KAAK;YACD,OAAO,OAAO,MAAM,CAAC;QACzB;YACI,OAAO,MAAM,WAAW,CAAC,MAAM;IACnC;AACJ;AAEA;;;;;;CAMC,GACD,SAAS,OAAQ,IAAI,EAAE,SAAS;IAC5B,IAAI,GAAG,QAAQ,GAAG,MAAM,MAAM,cAAc;IAC5C,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAClC,eAAe,SAAS,CAAC,EAAE,CAAC,MAAM;IACtC;IACA,OAAO;QACP,KAAK;YACD,OAAO,UAAU,IAAI,CAAC;QAC1B,KAAK;YACD,OAAO,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;QAC5C,KAAK;YACD,MAAM,IAAI,WAAW;YACrB,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBAClC,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE;gBACtB,SAAS,SAAS,CAAC,EAAE,CAAC,MAAM;YAChC;YACA,OAAO;QACX,KAAK;YACD,OAAO,OAAO,MAAM,CAAC;QACzB;YACI,MAAM,IAAI,MAAM,gCAAiC,OAAO;IAC5D;AACJ;AAEA;;;;;;;;CAQC,GACD,SAAS,WAAW,MAAM,EAAE,cAAc;IACtC,OAAO,IAAI,SAAS,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM;QACjD,IAAI,YAAY,EAAE;QAClB,IAAI,YAAY,OAAO,aAAa,EAChC,aAAa,OAAO,WAAW,EAC/B,WAAW,OAAO,SAAS;QAC/B,OACK,EAAE,CAAC,QAAQ,SAAU,IAAI,EAAE,IAAI;YAC5B,UAAU,IAAI,CAAC;YACf,IAAG,gBAAgB;gBACf,eAAe;YACnB;QACJ,GACC,EAAE,CAAC,SAAS,SAAS,GAAG;YACrB,YAAY,EAAE;YACd,OAAO;QACX,GACC,EAAE,CAAC,OAAO;YACP,IAAI;gBACA,IAAI,SAAS,mBAAmB,YAAY,OAAO,WAAW,YAAY;gBAC1E,QAAQ;YACZ,EAAE,OAAO,GAAG;gBACR,OAAO;YACX;YACA,YAAY,EAAE;QAClB,GACC,MAAM;IACf;AACJ;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC9C,IAAI,eAAe;IACnB,OAAO;QACP,KAAK;QACL,KAAK;YACD,eAAe;YACf;QACJ,KAAK;YACD,eAAe;YACf;IACJ;IAEA,IAAI;QACA,2BAA2B;QAC3B,IAAI,CAAC,aAAa,GAAG;QACrB,kCAAkC;QAClC,IAAI,CAAC,WAAW,GAAG;QACnB,gBAAgB;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,MAAM,YAAY,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,IAAI,cAAc;QAC7C,gEAAgE;QAChE,2CAA2C;QAC3C,OAAO,IAAI;IACf,EAAE,OAAM,GAAG;QACP,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc;QACjC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IACvB;AACJ;AAEA,aAAa,SAAS,GAAG;IACrB;;;;;KAKC,GACD,YAAa,SAAU,QAAQ;QAC3B,OAAO,WAAW,IAAI,EAAE;IAC5B;IACA;;;;;KAKC,GACD,IAAK,SAAU,GAAG,EAAE,EAAE;QAClB,IAAI,OAAO,IAAI;QAEf,IAAG,QAAQ,QAAQ;YACf,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,SAAU,KAAK;gBAChC,GAAG,IAAI,CAAC,MAAM,MAAM,IAAI,EAAE,MAAM,IAAI;YACxC;QACJ,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK;gBACjB,MAAM,KAAK,CAAC,IAAI,WAAW;YAC/B;QACJ;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,QAAS;QACL,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO;QACjD,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,OAAQ;QACJ,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,gBAAiB,SAAU,QAAQ;QAC/B,MAAM,YAAY,CAAC;QACnB,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;YACnC,iEAAiE;YACjE,qDAAqD;YACrD,iEAAiE;YACjE,eAAe;YACf,MAAM,IAAI,MAAM,IAAI,CAAC,WAAW,GAAG;QACvC;QAEA,OAAO,IAAI,0BAA0B,IAAI,EAAE;YACvC,YAAa,IAAI,CAAC,WAAW,KAAK;QACtC,GAAG;IACP;AACJ;AAGA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/defaults.js"], "sourcesContent": ["\"use strict\";\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n"], "names": [], "mappings": "AAAA;AACA,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AACjB,QAAQ,GAAG,GAAG;AACd,QAAQ,aAAa,GAAG;AACxB,QAAQ,IAAI,GAAG;AACf,QAAQ,WAAW,GAAG;AACtB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,OAAO,GAAG;AAClB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/DataWorker.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar GenericWorker = require(\"./GenericWorker\");\n\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */\nfunction DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n\n    this._tickScheduled = false;\n\n    dataP.then(function (data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if(!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function (e) {\n        self.error(e);\n    });\n}\n\nutils.inherits(<PERSON><PERSON><PERSON><PERSON>, GenericWorker);\n\n/**\n * @see GenericWorker.cleanUp\n */\nDataWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n\n/**\n * @see GenericWorker.resume\n */\nDataWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n\n/**\n * Trigger a tick a schedule an other call to this function.\n */\nDataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if(this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if(!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n\n/**\n * Read and push a chunk.\n */\nDataWorker.prototype._tick = function() {\n\n    if(this.isPaused || this.isFinished) {\n        return false;\n    }\n\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type) {\n        case \"string\":\n            data = this.data.substring(this.index, nextIndex);\n            break;\n        case \"uint8array\":\n            data = this.data.subarray(this.index, nextIndex);\n            break;\n        case \"array\":\n        case \"nodebuffer\":\n            data = this.data.slice(this.index, nextIndex);\n            break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data : data,\n            meta : {\n                percent : this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\n\nmodule.exports = DataWorker;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ,mCAAmC;AACnC,wCAAwC;AACxC,IAAI,qBAAqB,KAAK;AAE9B;;;;CAIC,GACD,SAAS,WAAW,KAAK;IACrB,cAAc,IAAI,CAAC,IAAI,EAAE;IACzB,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,GAAG;IAEZ,IAAI,CAAC,cAAc,GAAG;IAEtB,MAAM,IAAI,CAAC,SAAU,IAAI;QACrB,KAAK,WAAW,GAAG;QACnB,KAAK,IAAI,GAAG;QACZ,KAAK,GAAG,GAAG,QAAQ,KAAK,MAAM,IAAI;QAClC,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC;QAC5B,IAAG,CAAC,KAAK,QAAQ,EAAE;YACf,KAAK,cAAc;QACvB;IACJ,GAAG,SAAU,CAAC;QACV,KAAK,KAAK,CAAC;IACf;AACJ;AAEA,MAAM,QAAQ,CAAC,YAAY;AAE3B;;CAEC,GACD,WAAW,SAAS,CAAC,OAAO,GAAG;IAC3B,cAAc,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IACzC,IAAI,CAAC,IAAI,GAAG;AAChB;AAEA;;CAEC,GACD,WAAW,SAAS,CAAC,MAAM,GAAG;IAC1B,IAAG,CAAC,cAAc,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG;QAC3C,OAAO;IACX;IAEA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE;QAC1C,IAAI,CAAC,cAAc,GAAG;QACtB,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,IAAI;IAC7C;IACA,OAAO;AACX;AAEA;;CAEC,GACD,WAAW,SAAS,CAAC,cAAc,GAAG;IAClC,IAAI,CAAC,cAAc,GAAG;IACtB,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;QACjC;IACJ;IACA,IAAI,CAAC,KAAK;IACV,IAAG,CAAC,IAAI,CAAC,UAAU,EAAE;QACjB,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,IAAI;QACzC,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ;AAEA;;CAEC,GACD,WAAW,SAAS,CAAC,KAAK,GAAG;IAEzB,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;QACjC,OAAO;IACX;IAEA,IAAI,OAAO;IACX,IAAI,OAAO,MAAM,YAAY,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG;IAC7D,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE;QACxB,MAAM;QACN,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;QACH,OAAO,IAAI,CAAC,IAAI;YAChB,KAAK;gBACD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE;gBACvC;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;gBACtC;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gBACnC;QACJ;QACA,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,IAAI,CAAC,IAAI,CAAC;YACb,MAAO;YACP,MAAO;gBACH,SAAU,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM;YACvD;QACJ;IACJ;AACJ;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/crc32.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils\");\n\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n\n    for(var n =0; n < 256; n++){\n        c = n;\n        for(var k =0; k < 8; k++){\n            c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n        }\n        table[n] = c;\n    }\n\n    return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\n// That's all for the pako functions.\n\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */\nfunction crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n\n    if(isArray) {\n        return crc32(crc|0, input, input.length, 0);\n    } else {\n        return crc32str(crc|0, input, input.length, 0);\n    }\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ;;;CAGC,GAED,wDAAwD;AACxD,SAAS;IACL,IAAI,GAAG,QAAQ,EAAE;IAEjB,IAAI,IAAI,IAAG,GAAG,IAAI,KAAK,IAAI;QACvB,IAAI;QACJ,IAAI,IAAI,IAAG,GAAG,IAAI,GAAG,IAAI;YACrB,IAAK,AAAC,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;QACnD;QACA,KAAK,CAAC,EAAE,GAAG;IACf;IAEA,OAAO;AACX;AAEA,8DAA8D;AAC9D,IAAI,WAAW;AAGf,SAAS,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC7B,IAAI,IAAI,UAAU,MAAM,MAAM;IAE9B,MAAM,MAAO,CAAC;IAEd,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAM;QAC7B,MAAM,AAAC,QAAQ,IAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,KAAK;IAChD;IAEA,OAAQ,MAAO,CAAC,GAAK,SAAS;AAClC;AAEA,qCAAqC;AAErC;;;;;;;;;CASC,GACD,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAChC,IAAI,IAAI,UAAU,MAAM,MAAM;IAE9B,MAAM,MAAO,CAAC;IAEd,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAM;QAC7B,MAAM,AAAC,QAAQ,IAAK,CAAC,CAAC,CAAC,MAAM,IAAI,UAAU,CAAC,EAAE,IAAI,KAAK;IAC3D;IAEA,OAAQ,MAAO,CAAC,GAAK,SAAS;AAClC;AAEA,OAAO,OAAO,GAAG,SAAS,aAAa,KAAK,EAAE,GAAG;IAC7C,IAAI,OAAO,UAAU,eAAe,CAAC,MAAM,MAAM,EAAE;QAC/C,OAAO;IACX;IAEA,IAAI,UAAU,MAAM,SAAS,CAAC,WAAW;IAEzC,IAAG,SAAS;QACR,OAAO,MAAM,MAAI,GAAG,OAAO,MAAM,MAAM,EAAE;IAC7C,OAAO;QACH,OAAO,SAAS,MAAI,GAAG,OAAO,MAAM,MAAM,EAAE;IAChD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/Crc32Probe.js"], "sourcesContent": ["\"use strict\";\n\nvar GenericWorker = require(\"./GenericWorker\");\nvar crc32 = require(\"../crc32\");\nvar utils = require(\"../utils\");\n\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */\nfunction Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nCrc32Probe.prototype.processChunk = function (chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;CAGC,GACD,SAAS;IACL,cAAc,IAAI,CAAC,IAAI,EAAE;IACzB,IAAI,CAAC,cAAc,CAAC,SAAS;AACjC;AACA,MAAM,QAAQ,CAAC,YAAY;AAE3B;;CAEC,GACD,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IAC/C,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,MAAM,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;IACnE,IAAI,CAAC,IAAI,CAAC;AACd;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1590, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/stream/DataLengthProbe.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar GenericWorker = require(\"./GenericWorker\");\n\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */\nfunction DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nDataLengthProbe.prototype.processChunk = function (chunk) {\n    if(chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ;;;;CAIC,GACD,SAAS,gBAAgB,QAAQ;IAC7B,cAAc,IAAI,CAAC,IAAI,EAAE,yBAAyB;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,cAAc,CAAC,UAAU;AAClC;AACA,MAAM,QAAQ,CAAC,iBAAiB;AAEhC;;CAEC,GACD,gBAAgB,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IACpD,IAAG,OAAO;QACN,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC/C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,MAAM,IAAI,CAAC,MAAM;IAC/D;IACA,cAAc,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;AACpD;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/compressedObject.js"], "sourcesContent": ["\"use strict\";\n\nvar external = require(\"./external\");\nvar DataWorker = require(\"./stream/DataWorker\");\nvar Crc32Probe = require(\"./stream/Crc32Probe\");\nvar DataLengthProbe = require(\"./stream/DataLengthProbe\");\n\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */\nfunction CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\n\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getContentWorker: function () {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent))\n            .pipe(this.compression.uncompressWorker())\n            .pipe(new DataLengthProbe(\"data_length\"));\n\n        var that = this;\n        worker.on(\"end\", function () {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getCompressedWorker: function () {\n        return new DataWorker(external.Promise.resolve(this.compressedContent))\n            .withStreamInfo(\"compressedSize\", this.compressedSize)\n            .withStreamInfo(\"uncompressedSize\", this.uncompressedSize)\n            .withStreamInfo(\"crc32\", this.crc32)\n            .withStreamInfo(\"compression\", this.compression)\n        ;\n    }\n};\n\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */\nCompressedObject.createWorkerFrom = function (uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker\n        .pipe(new Crc32Probe())\n        .pipe(new DataLengthProbe(\"uncompressedSize\"))\n        .pipe(compression.compressWorker(compressionOptions))\n        .pipe(new DataLengthProbe(\"compressedSize\"))\n        .withStreamInfo(\"compression\", compression);\n};\n\nmodule.exports = CompressedObject;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;;;;;CAQC,GACD,SAAS,iBAAiB,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI;IAChF,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,gBAAgB,GAAG;IACxB,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,iBAAiB,GAAG;AAC7B;AAEA,iBAAiB,SAAS,GAAG;IACzB;;;KAGC,GACD,kBAAkB;QACd,IAAI,SAAS,IAAI,WAAW,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,GACtE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,IACtC,IAAI,CAAC,IAAI,gBAAgB;QAE9B,IAAI,OAAO,IAAI;QACf,OAAO,EAAE,CAAC,OAAO;YACb,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,KAAK,KAAK,gBAAgB,EAAE;gBAC1D,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,qBAAqB;QACjB,OAAO,IAAI,WAAW,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAChE,cAAc,CAAC,kBAAkB,IAAI,CAAC,cAAc,EACpD,cAAc,CAAC,oBAAoB,IAAI,CAAC,gBAAgB,EACxD,cAAc,CAAC,SAAS,IAAI,CAAC,KAAK,EAClC,cAAc,CAAC,eAAe,IAAI,CAAC,WAAW;IAEvD;AACJ;AAEA;;;;;;;CAOC,GACD,iBAAiB,gBAAgB,GAAG,SAAU,kBAAkB,EAAE,WAAW,EAAE,kBAAkB;IAC7F,OAAO,mBACF,IAAI,CAAC,IAAI,cACT,IAAI,CAAC,IAAI,gBAAgB,qBACzB,IAAI,CAAC,YAAY,cAAc,CAAC,qBAChC,IAAI,CAAC,IAAI,gBAAgB,mBACzB,cAAc,CAAC,eAAe;AACvC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/zipObject.js"], "sourcesContent": ["\"use strict\";\n\nvar StreamHelper = require(\"./stream/StreamHelper\");\nvar DataWorker = require(\"./stream/DataWorker\");\nvar utf8 = require(\"./utf8\");\nvar CompressedObject = require(\"./compressedObject\");\nvar GenericWorker = require(\"./stream/GenericWorker\");\n\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */\nvar ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression : options.compression,\n        compressionOptions : options.compressionOptions\n    };\n};\n\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */\n    internalStream: function (type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n\n            var isUnicodeString = !this._dataBinary;\n\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n\n        return new StreamHelper(result, outputType, \"\");\n    },\n\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */\n    async: function (type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */\n    nodeStream: function (type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */\n    _compressWorker: function (compression, compressionOptions) {\n        if (\n            this._data instanceof CompressedObject &&\n            this._data.compression.magic === compression.magic\n        ) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if(!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */\n    _decompressWorker : function () {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\n\nvar removedMethods = [\"asText\", \"asBinary\", \"asNodeBuffer\", \"asUint8Array\", \"asArrayBuffer\"];\nvar removedFn = function () {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\n\nfor(var i = 0; i < removedMethods.length; i++) {\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;;;CAMC,GACD,IAAI,YAAY,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO;IACxC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG;IACtB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;IACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;IAC9B,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe;IAC9C,IAAI,CAAC,cAAc,GAAG,QAAQ,cAAc;IAE5C,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,WAAW,GAAG,QAAQ,MAAM;IACjC,4BAA4B;IAC5B,IAAI,CAAC,OAAO,GAAG;QACX,aAAc,QAAQ,WAAW;QACjC,oBAAqB,QAAQ,kBAAkB;IACnD;AACJ;AAEA,UAAU,SAAS,GAAG;IAClB;;;;KAIC,GACD,gBAAgB,SAAU,IAAI;QAC1B,IAAI,SAAS,MAAM,aAAa;QAChC,IAAI;YACA,IAAI,CAAC,MAAM;gBACP,MAAM,IAAI,MAAM;YACpB;YACA,aAAa,KAAK,WAAW;YAC7B,IAAI,mBAAmB,eAAe,YAAY,eAAe;YACjE,IAAI,eAAe,kBAAkB,eAAe,QAAQ;gBACxD,aAAa;YACjB;YACA,SAAS,IAAI,CAAC,iBAAiB;YAE/B,IAAI,kBAAkB,CAAC,IAAI,CAAC,WAAW;YAEvC,IAAI,mBAAmB,CAAC,kBAAkB;gBACtC,SAAS,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAgB;YAClD;YACA,IAAI,CAAC,mBAAmB,kBAAkB;gBACtC,SAAS,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAgB;YAClD;QACJ,EAAE,OAAO,GAAG;YACR,SAAS,IAAI,cAAc;YAC3B,OAAO,KAAK,CAAC;QACjB;QAEA,OAAO,IAAI,aAAa,QAAQ,YAAY;IAChD;IAEA;;;;;KAKC,GACD,OAAO,SAAU,IAAI,EAAE,QAAQ;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,UAAU,CAAC;IAChD;IAEA;;;;;KAKC,GACD,YAAY,SAAU,IAAI,EAAE,QAAQ;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,cAAc,cAAc,CAAC;IACpE;IAEA;;;;;;KAMC,GACD,iBAAiB,SAAU,WAAW,EAAE,kBAAkB;QACtD,IACI,IAAI,CAAC,KAAK,YAAY,oBACtB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,YAAY,KAAK,EACpD;YACE,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB;QACzC,OAAO;YACH,IAAI,SAAS,IAAI,CAAC,iBAAiB;YACnC,IAAG,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClB,SAAS,OAAO,IAAI,CAAC,IAAI,KAAK,gBAAgB;YAClD;YACA,OAAO,iBAAiB,gBAAgB,CAAC,QAAQ,aAAa;QAClE;IACJ;IACA;;;;KAIC,GACD,mBAAoB;QAChB,IAAI,IAAI,CAAC,KAAK,YAAY,kBAAkB;YACxC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB;QACtC,OAAO,IAAI,IAAI,CAAC,KAAK,YAAY,eAAe;YAC5C,OAAO,IAAI,CAAC,KAAK;QACrB,OAAO;YACH,OAAO,IAAI,WAAW,IAAI,CAAC,KAAK;QACpC;IACJ;AACJ;AAEA,IAAI,iBAAiB;IAAC;IAAU;IAAY;IAAgB;IAAgB;CAAgB;AAC5F,IAAI,YAAY;IACZ,MAAM,IAAI,MAAM;AACpB;AAEA,IAAI,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;IAC3C,UAAU,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG;AAC7C;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/flate.js"], "sourcesContent": ["\"use strict\";\nvar USE_TYPEDARRAY = (typeof Uint8Array !== \"undefined\") && (typeof Uint16Array !== \"undefined\") && (typeof Uint32Array !== \"undefined\");\n\nvar pako = require(\"pako\");\nvar utils = require(\"./utils\");\nvar GenericWorker = require(\"./stream/GenericWorker\");\n\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\n\nexports.magic = \"\\x08\\x00\";\n\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */\nfunction FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\n\nutils.inherits(FlateWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nFlateWorker.prototype.processChunk = function (chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n\n/**\n * @see GenericWorker.flush\n */\nFlateWorker.prototype.flush = function () {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */\nFlateWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */\nFlateWorker.prototype._createPako = function () {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data : data,\n            meta : self.meta\n        });\n    };\n};\n\nexports.compressWorker = function (compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function () {\n    return new FlateWorker(\"Inflate\", {});\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI,iBAAiB,AAAC,OAAO,eAAe,eAAiB,OAAO,gBAAgB,eAAiB,OAAO,gBAAgB;AAE5H,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,aAAa,iBAAiB,eAAe;AAEjD,QAAQ,KAAK,GAAG;AAEhB;;;;;CAKC,GACD,SAAS,YAAY,MAAM,EAAE,OAAO;IAChC,cAAc,IAAI,CAAC,IAAI,EAAE,iBAAiB;IAE1C,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,YAAY,GAAG;IACpB,iDAAiD;IACjD,iDAAiD;IACjD,IAAI,CAAC,IAAI,GAAG,CAAC;AACjB;AAEA,MAAM,QAAQ,CAAC,aAAa;AAE5B;;CAEC,GACD,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;IAChD,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;IACtB,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;QACrB,IAAI,CAAC,WAAW;IACpB;IACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC,YAAY,MAAM,IAAI,GAAG;AAC/D;AAEA;;CAEC,GACD,YAAY,SAAS,CAAC,KAAK,GAAG;IAC1B,cAAc,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;IACvC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;QACrB,IAAI,CAAC,WAAW;IACpB;IACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;AACxB;AACA;;CAEC,GACD,YAAY,SAAS,CAAC,OAAO,GAAG;IAC5B,cAAc,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IACzC,IAAI,CAAC,KAAK,GAAG;AACjB;AAEA;;;;;CAKC,GACD,YAAY,SAAS,CAAC,WAAW,GAAG;IAChC,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpC,KAAK;QACL,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,EAAE,sBAAsB;IAC/D;IACA,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,IAAI;QAC7B,KAAK,IAAI,CAAC;YACN,MAAO;YACP,MAAO,KAAK,IAAI;QACpB;IACJ;AACJ;AAEA,QAAQ,cAAc,GAAG,SAAU,kBAAkB;IACjD,OAAO,IAAI,YAAY,WAAW;AACtC;AACA,QAAQ,gBAAgB,GAAG;IACvB,OAAO,IAAI,YAAY,WAAW,CAAC;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/compressions.js"], "sourcesContent": ["\"use strict\";\n\nvar GenericWorker = require(\"./stream/GenericWorker\");\n\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker : function () {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker : function () {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = require(\"./flate\");\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AAEJ,QAAQ,KAAK,GAAG;IACZ,OAAO;IACP,gBAAiB;QACb,OAAO,IAAI,cAAc;IAC7B;IACA,kBAAmB;QACf,OAAO,IAAI,cAAc;IAC7B;AACJ;AACA,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/signature.js"], "sourcesContent": ["\"use strict\";\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\x08\";\n"], "names": [], "mappings": "AAAA;AACA,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,qBAAqB,GAAG;AAChC,QAAQ,+BAA+B,GAAG;AAC1C,QAAQ,2BAA2B,GAAG;AACtC,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1902, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/generate/ZipFileWorker.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar GenericWorker = require(\"../stream/GenericWorker\");\nvar utf8 = require(\"../utf8\");\nvar crc32 = require(\"../crc32\");\nvar signature = require(\"../signature\");\n\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */\nvar decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for (i = 0; i < bytes; i++) {\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */\nvar generateUnixExternalFileAttr = function (unixPermissions, isDir) {\n\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */\nvar generateDosExternalFileAttr = function (dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0)  & 0x3F;\n};\n\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */\nvar generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"],\n        compression = streamInfo[\"compression\"],\n        useCustomEncoding = encodeFileName !== utf8.utf8encode,\n        encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)),\n        utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)),\n        comment = file.comment,\n        encodedComment = utils.transformTo(\"string\", encodeFileName(comment)),\n        utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)),\n        useUTF8ForFileName = utfEncodedFileName.length !== file.name.length,\n        useUTF8ForComment = utfEncodedComment.length !== comment.length,\n        dosTime,\n        dosDate,\n        extraFields = \"\",\n        unicodePathExtraField = \"\",\n        unicodeCommentExtraField = \"\",\n        dir = file.dir,\n        date = file.date;\n\n\n    var dataInfo = {\n        crc32 : 0,\n        compressedSize : 0,\n        uncompressedSize : 0\n    };\n\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n\n\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if(platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else { // DOS or other, fallback to DOS\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | (date.getUTCMonth() + 1);\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField =\n            // Version\n            decToHex(1, 1) +\n            // NameCRC32\n            decToHex(crc32(encodedFileName), 4) +\n            // UnicodeName\n            utfEncodedFileName;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x70\" +\n            // size\n            decToHex(unicodePathExtraField.length, 2) +\n            // content\n            unicodePathExtraField;\n    }\n\n    if(useUTF8ForComment) {\n\n        unicodeCommentExtraField =\n            // Version\n            decToHex(1, 1) +\n            // CommentCRC32\n            decToHex(crc32(encodedComment), 4) +\n            // UnicodeName\n            utfEncodedComment;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x63\" +\n            // size\n            decToHex(unicodeCommentExtraField.length, 2) +\n            // content\n            unicodeCommentExtraField;\n    }\n\n    var header = \"\";\n\n    // version needed to extract\n    header += \"\\x0A\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n\n\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n\n    var dirRecord = signature.CENTRAL_FILE_HEADER +\n        // version made by (00: DOS)\n        decToHex(versionMadeBy, 2) +\n        // file header (common to file and central directory)\n        header +\n        // file comment length\n        decToHex(encodedComment.length, 2) +\n        // disk number start\n        \"\\x00\\x00\" +\n        // internal file attributes TODO\n        \"\\x00\\x00\" +\n        // external file attributes\n        decToHex(extFileAttr, 4) +\n        // relative offset of local header\n        decToHex(offset, 4) +\n        // file name\n        encodedFileName +\n        // extra field\n        extraFields +\n        // file comment\n        encodedComment;\n\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */\nvar generateCentralDirectoryEnd = function (entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END +\n        // number of this disk\n        \"\\x00\\x00\" +\n        // number of the disk with the start of the central directory\n        \"\\x00\\x00\" +\n        // total number of entries in the central directory on this disk\n        decToHex(entriesCount, 2) +\n        // total number of entries in the central directory\n        decToHex(entriesCount, 2) +\n        // size of the central directory   4 bytes\n        decToHex(centralDirLength, 4) +\n        // offset of start of central directory with respect to the starting disk number\n        decToHex(localDirLength, 4) +\n        // .ZIP file comment length\n        decToHex(encodedComment.length, 2) +\n        // .ZIP file comment\n        encodedComment;\n\n    return dirEnd;\n};\n\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */\nvar generateDataDescriptors = function (streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR +\n        // crc-32                          4 bytes\n        decToHex(streamInfo[\"crc32\"], 4) +\n        // compressed size                 4 bytes\n        decToHex(streamInfo[\"compressedSize\"], 4) +\n        // uncompressed size               4 bytes\n        decToHex(streamInfo[\"uncompressedSize\"], 4);\n\n    return descriptor;\n};\n\n\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */\nfunction ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n\n\n\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n\n/**\n * @see GenericWorker.push\n */\nZipFileWorker.prototype.push = function (chunk) {\n\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n\n    if(this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n\n        GenericWorker.prototype.push.call(this, {\n            data : chunk.data,\n            meta : {\n                currentFile : this.currentFile,\n                percent : entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */\nZipFileWorker.prototype.openedSource = function (streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n\n    // don't stream folders (because they don't have any content)\n    if(streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */\nZipFileWorker.prototype.closedSource = function (streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n\n    this.dirRecords.push(record.dirRecord);\n    if(streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data : generateDataDescriptors(streamInfo),\n            meta : {percent:100}\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n        while(this.contentBuffer.length) {\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n\n/**\n * @see GenericWorker.flush\n */\nZipFileWorker.prototype.flush = function () {\n\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++) {\n        this.push({\n            data : this.dirRecords[i],\n            meta : {percent:100}\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n\n    this.push({\n        data : dirEnd,\n        meta : {percent:100}\n    });\n};\n\n/**\n * Prepare the next source to be read.\n */\nZipFileWorker.prototype.prepareNextSource = function () {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n\n/**\n * @see GenericWorker.registerPrevious\n */\nZipFileWorker.prototype.registerPrevious = function (previous) {\n    this._sources.push(previous);\n    var self = this;\n\n    previous.on(\"data\", function (chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function () {\n        self.closedSource(self.previous.streamInfo);\n        if(self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function (e) {\n        self.error(e);\n    });\n    return this;\n};\n\n/**\n * @see GenericWorker.resume\n */\nZipFileWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n\n/**\n * @see GenericWorker.error\n */\nZipFileWorker.prototype.error = function (e) {\n    var sources = this._sources;\n    if(!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++) {\n        try {\n            sources[i].error(e);\n        } catch(e) {\n            // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n\n/**\n * @see GenericWorker.lock\n */\nZipFileWorker.prototype.lock = function () {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++) {\n        sources[i].lock();\n    }\n};\n\nmodule.exports = ZipFileWorker;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;;;CAMC,GACD,IAAI,WAAW,SAAS,GAAG,EAAE,KAAK;IAC9B,IAAI,MAAM,IAAI;IACd,IAAK,IAAI,GAAG,IAAI,OAAO,IAAK;QACxB,OAAO,OAAO,YAAY,CAAC,MAAM;QACjC,MAAM,QAAQ;IAClB;IACA,OAAO;AACX;AAEA;;;;;;;;;;;;;;CAcC,GACD,IAAI,+BAA+B,SAAU,eAAe,EAAE,KAAK;IAE/D,IAAI,SAAS;IACb,IAAI,CAAC,iBAAiB;QAClB,2DAA2D;QAC3D,oBAAoB;QACpB,oBAAoB;QACpB,SAAS,QAAQ,SAAS;IAC9B;IACA,OAAO,CAAC,SAAS,MAAM,KAAK;AAChC;AAEA;;;;;;;;;;;;CAYC,GACD,IAAI,8BAA8B,SAAU,cAAc;IACtD,gDAAgD;IAChD,OAAO,CAAC,kBAAkB,CAAC,IAAK;AACpC;AAEA;;;;;;;;;CASC,GACD,IAAI,mBAAmB,SAAS,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc;IACzG,IAAI,OAAO,UAAU,CAAC,OAAO,EACzB,cAAc,UAAU,CAAC,cAAc,EACvC,oBAAoB,mBAAmB,KAAK,UAAU,EACtD,kBAAkB,MAAM,WAAW,CAAC,UAAU,eAAe,KAAK,IAAI,IACtE,qBAAqB,MAAM,WAAW,CAAC,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,IAC1E,UAAU,KAAK,OAAO,EACtB,iBAAiB,MAAM,WAAW,CAAC,UAAU,eAAe,WAC5D,oBAAoB,MAAM,WAAW,CAAC,UAAU,KAAK,UAAU,CAAC,WAChE,qBAAqB,mBAAmB,MAAM,KAAK,KAAK,IAAI,CAAC,MAAM,EACnE,oBAAoB,kBAAkB,MAAM,KAAK,QAAQ,MAAM,EAC/D,SACA,SACA,cAAc,IACd,wBAAwB,IACxB,2BAA2B,IAC3B,MAAM,KAAK,GAAG,EACd,OAAO,KAAK,IAAI;IAGpB,IAAI,WAAW;QACX,OAAQ;QACR,gBAAiB;QACjB,kBAAmB;IACvB;IAEA,uEAAuE;IACvE,yBAAyB;IACzB,IAAI,CAAC,mBAAmB,gBAAgB;QACpC,SAAS,KAAK,GAAG,UAAU,CAAC,QAAQ;QACpC,SAAS,cAAc,GAAG,UAAU,CAAC,iBAAiB;QACtD,SAAS,gBAAgB,GAAG,UAAU,CAAC,mBAAmB;IAC9D;IAEA,IAAI,UAAU;IACd,IAAI,iBAAiB;QACjB,8DAA8D;QAC9D,gEAAgE;QAChE,iCAAiC;QACjC,WAAW;IACf;IACA,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,iBAAiB,GAAG;QACjE,wCAAwC;QACxC,WAAW;IACf;IAGA,IAAI,cAAc;IAClB,IAAI,gBAAgB;IACpB,IAAI,KAAK;QACL,uCAAuC;QACvC,eAAe;IACnB;IACA,IAAG,aAAa,QAAQ;QACpB,gBAAgB,QAAQ,oBAAoB;QAC5C,eAAe,6BAA6B,KAAK,eAAe,EAAE;IACtE,OAAO;QACH,gBAAgB,QAAQ,mBAAmB;QAC3C,eAAe,4BAA4B,KAAK,cAAc,EAAE;IACpE;IAEA,OAAO;IACP,8DAA8D;IAC9D,8DAA8D;IAC9D,8DAA8D;IAE9D,UAAU,KAAK,WAAW;IAC1B,UAAU,WAAW;IACrB,UAAU,UAAU,KAAK,aAAa;IACtC,UAAU,WAAW;IACrB,UAAU,UAAU,KAAK,aAAa,KAAK;IAE3C,UAAU,KAAK,cAAc,KAAK;IAClC,UAAU,WAAW;IACrB,UAAU,UAAW,KAAK,WAAW,KAAK;IAC1C,UAAU,WAAW;IACrB,UAAU,UAAU,KAAK,UAAU;IAEnC,IAAI,oBAAoB;QACpB,mEAAmE;QACnE,uEAAuE;QACvE,kEAAkE;QAClE,8BAA8B;QAC9B,+DAA+D;QAC/D,kEAAkE;QAClE,iEAAiE;QACjE,mEAAmE;QACnE,iCAAiC;QACjC,wBACI,UAAU;QACV,SAAS,GAAG,KACZ,YAAY;QACZ,SAAS,MAAM,kBAAkB,KACjC,cAAc;QACd;QAEJ,eACI,oCAAoC;QACpC,aACA,OAAO;QACP,SAAS,sBAAsB,MAAM,EAAE,KACvC,UAAU;QACV;IACR;IAEA,IAAG,mBAAmB;QAElB,2BACI,UAAU;QACV,SAAS,GAAG,KACZ,eAAe;QACf,SAAS,MAAM,iBAAiB,KAChC,cAAc;QACd;QAEJ,eACI,oCAAoC;QACpC,aACA,OAAO;QACP,SAAS,yBAAyB,MAAM,EAAE,KAC1C,UAAU;QACV;IACR;IAEA,IAAI,SAAS;IAEb,4BAA4B;IAC5B,UAAU;IACV,2BAA2B;IAC3B,UAAU,SAAS,SAAS;IAC5B,qBAAqB;IACrB,UAAU,YAAY,KAAK;IAC3B,qBAAqB;IACrB,UAAU,SAAS,SAAS;IAC5B,qBAAqB;IACrB,UAAU,SAAS,SAAS;IAC5B,SAAS;IACT,UAAU,SAAS,SAAS,KAAK,EAAE;IACnC,kBAAkB;IAClB,UAAU,SAAS,SAAS,cAAc,EAAE;IAC5C,oBAAoB;IACpB,UAAU,SAAS,SAAS,gBAAgB,EAAE;IAC9C,mBAAmB;IACnB,UAAU,SAAS,gBAAgB,MAAM,EAAE;IAC3C,qBAAqB;IACrB,UAAU,SAAS,YAAY,MAAM,EAAE;IAGvC,IAAI,aAAa,UAAU,iBAAiB,GAAG,SAAS,kBAAkB;IAE1E,IAAI,YAAY,UAAU,mBAAmB,GACzC,4BAA4B;IAC5B,SAAS,eAAe,KACxB,qDAAqD;IACrD,SACA,sBAAsB;IACtB,SAAS,eAAe,MAAM,EAAE,KAChC,oBAAoB;IACpB,aACA,gCAAgC;IAChC,aACA,2BAA2B;IAC3B,SAAS,aAAa,KACtB,kCAAkC;IAClC,SAAS,QAAQ,KACjB,YAAY;IACZ,kBACA,cAAc;IACd,cACA,eAAe;IACf;IAEJ,OAAO;QACH,YAAY;QACZ,WAAW;IACf;AACJ;AAEA;;;;;;;;CAQC,GACD,IAAI,8BAA8B,SAAU,YAAY,EAAE,gBAAgB,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc;IAC/G,IAAI,SAAS;IACb,IAAI,iBAAiB,MAAM,WAAW,CAAC,UAAU,eAAe;IAEhE,+BAA+B;IAC/B,SAAS,UAAU,qBAAqB,GACpC,sBAAsB;IACtB,aACA,6DAA6D;IAC7D,aACA,gEAAgE;IAChE,SAAS,cAAc,KACvB,mDAAmD;IACnD,SAAS,cAAc,KACvB,0CAA0C;IAC1C,SAAS,kBAAkB,KAC3B,gFAAgF;IAChF,SAAS,gBAAgB,KACzB,2BAA2B;IAC3B,SAAS,eAAe,MAAM,EAAE,KAChC,oBAAoB;IACpB;IAEJ,OAAO;AACX;AAEA;;;;;CAKC,GACD,IAAI,0BAA0B,SAAU,UAAU;IAC9C,IAAI,aAAa;IACjB,aAAa,UAAU,eAAe,GAClC,0CAA0C;IAC1C,SAAS,UAAU,CAAC,QAAQ,EAAE,KAC9B,0CAA0C;IAC1C,SAAS,UAAU,CAAC,iBAAiB,EAAE,KACvC,0CAA0C;IAC1C,SAAS,UAAU,CAAC,mBAAmB,EAAE;IAE7C,OAAO;AACX;AAGA;;;;;;;CAOC,GACD,SAAS,cAAc,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc;IACjE,cAAc,IAAI,CAAC,IAAI,EAAE;IACzB,6EAA6E;IAC7E,IAAI,CAAC,YAAY,GAAG;IACpB,8BAA8B;IAC9B,IAAI,CAAC,UAAU,GAAG;IAClB,0CAA0C;IAC1C,IAAI,CAAC,WAAW,GAAG;IACnB,kDAAkD;IAClD,IAAI,CAAC,cAAc,GAAG;IACtB,8CAA8C;IAC9C,IAAI,CAAC,WAAW,GAAG;IACnB,2EAA2E;IAC3E,0EAA0E;IAC1E,6EAA6E;IAC7E,uCAAuC;IACvC,IAAI,CAAC,UAAU,GAAG;IAClB,yDAAyD;IACzD,IAAI,CAAC,aAAa,GAAG,EAAE;IACvB,2CAA2C;IAC3C,IAAI,CAAC,UAAU,GAAG,EAAE;IACpB,mFAAmF;IACnF,IAAI,CAAC,mBAAmB,GAAG;IAC3B,gDAAgD;IAChD,IAAI,CAAC,YAAY,GAAG;IACpB,0FAA0F;IAC1F,iCAAiC;IACjC,IAAI,CAAC,WAAW,GAAG;IAInB,IAAI,CAAC,QAAQ,GAAG,EAAE;AACtB;AACA,MAAM,QAAQ,CAAC,eAAe;AAE9B;;CAEC,GACD,cAAc,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK;IAE1C,IAAI,qBAAqB,MAAM,IAAI,CAAC,OAAO,IAAI;IAC/C,IAAI,eAAe,IAAI,CAAC,YAAY;IACpC,IAAI,iBAAiB,IAAI,CAAC,QAAQ,CAAC,MAAM;IAEzC,IAAG,IAAI,CAAC,UAAU,EAAE;QAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IAC5B,OAAO;QACH,IAAI,CAAC,YAAY,IAAI,MAAM,IAAI,CAAC,MAAM;QAEtC,cAAc,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACpC,MAAO,MAAM,IAAI;YACjB,MAAO;gBACH,aAAc,IAAI,CAAC,WAAW;gBAC9B,SAAU,eAAe,CAAC,qBAAqB,MAAM,CAAC,eAAe,iBAAiB,CAAC,CAAC,IAAI,eAAe;YAC/G;QACJ;IACJ;AACJ;AAEA;;;CAGC,GACD,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,UAAU;IACvD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY;IAC5C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI;IAE1C,IAAI,kBAAkB,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG;IAEjE,6DAA6D;IAC7D,IAAG,iBAAiB;QAChB,IAAI,SAAS,iBAAiB,YAAY,iBAAiB,OAAO,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc;QACjI,IAAI,CAAC,IAAI,CAAC;YACN,MAAO,OAAO,UAAU;YACxB,MAAO;gBAAC,SAAQ;YAAC;QACrB;IACJ,OAAO;QACH,6DAA6D;QAC7D,IAAI,CAAC,UAAU,GAAG;IACtB;AACJ;AAEA;;;CAGC,GACD,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,UAAU;IACvD,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,kBAAkB,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG;IACjE,IAAI,SAAS,iBAAiB,YAAY,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc;IAEhI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,SAAS;IACrC,IAAG,iBAAiB;QAChB,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC;YACN,MAAO,wBAAwB;YAC/B,MAAO;gBAAC,SAAQ;YAAG;QACvB;IACJ,OAAO;QACH,8DAA8D;QAC9D,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC;YACN,MAAO,OAAO,UAAU;YACxB,MAAO;gBAAC,SAAQ;YAAC;QACrB;QACA,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK;QACtC;IACJ;IACA,IAAI,CAAC,WAAW,GAAG;AACvB;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,KAAK,GAAG;IAE5B,IAAI,iBAAiB,IAAI,CAAC,YAAY;IACtC,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;QAC5C,IAAI,CAAC,IAAI,CAAC;YACN,MAAO,IAAI,CAAC,UAAU,CAAC,EAAE;YACzB,MAAO;gBAAC,SAAQ;YAAG;QACvB;IACJ;IACA,IAAI,mBAAmB,IAAI,CAAC,YAAY,GAAG;IAE3C,IAAI,SAAS,4BAA4B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,kBAAkB,gBAAgB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc;IAEvI,IAAI,CAAC,IAAI,CAAC;QACN,MAAO;QACP,MAAO;YAAC,SAAQ;QAAG;IACvB;AACJ;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,iBAAiB,GAAG;IACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;IACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;IAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK;IACvB,OAAO;QACH,IAAI,CAAC,QAAQ,CAAC,MAAM;IACxB;AACJ;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,gBAAgB,GAAG,SAAU,QAAQ;IACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACnB,IAAI,OAAO,IAAI;IAEf,SAAS,EAAE,CAAC,QAAQ,SAAU,KAAK;QAC/B,KAAK,YAAY,CAAC;IACtB;IACA,SAAS,EAAE,CAAC,OAAO;QACf,KAAK,YAAY,CAAC,KAAK,QAAQ,CAAC,UAAU;QAC1C,IAAG,KAAK,QAAQ,CAAC,MAAM,EAAE;YACrB,KAAK,iBAAiB;QAC1B,OAAO;YACH,KAAK,GAAG;QACZ;IACJ;IACA,SAAS,EAAE,CAAC,SAAS,SAAU,CAAC;QAC5B,KAAK,KAAK,CAAC;IACf;IACA,OAAO,IAAI;AACf;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,MAAM,GAAG;IAC7B,IAAG,CAAC,cAAc,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG;QAC3C,OAAO;IACX;IAEA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACxC,IAAI,CAAC,iBAAiB;QACtB,OAAO;IACX;IACA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACjE,IAAI,CAAC,GAAG;QACR,OAAO;IACX;AACJ;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACvC,IAAI,UAAU,IAAI,CAAC,QAAQ;IAC3B,IAAG,CAAC,cAAc,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;QAC7C,OAAO;IACX;IACA,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACpC,IAAI;YACA,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;QACrB,EAAE,OAAM,GAAG;QACP,sCAAsC;QAC1C;IACJ;IACA,OAAO;AACX;AAEA;;CAEC,GACD,cAAc,SAAS,CAAC,IAAI,GAAG;IAC3B,cAAc,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IACtC,IAAI,UAAU,IAAI,CAAC,QAAQ;IAC3B,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACpC,OAAO,CAAC,EAAE,CAAC,IAAI;IACnB;AACJ;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/generate/index.js"], "sourcesContent": ["\"use strict\";\n\nvar compressions = require(\"../compressions\");\nvar ZipFileWorker = require(\"./ZipFileWorker\");\n\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */\nvar getCompression = function (fileCompression, zipCompression) {\n\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */\nexports.generateWorker = function (zip, options, comment) {\n\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n\n        zip.forEach(function (relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n\n            file._compressWorker(compression, compressionOptions)\n                .withStreamInfo(\"file\", {\n                    name : relativePath,\n                    dir : dir,\n                    date : date,\n                    comment : file.comment || \"\",\n                    unixPermissions : file.unixPermissions,\n                    dosPermissions : file.dosPermissions\n                })\n                .pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n\n    return zipFileWorker;\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ;;;;;CAKC,GACD,IAAI,iBAAiB,SAAU,eAAe,EAAE,cAAc;IAE1D,IAAI,kBAAkB,mBAAmB;IACzC,IAAI,cAAc,YAAY,CAAC,gBAAgB;IAC/C,IAAI,CAAC,aAAa;QACd,MAAM,IAAI,MAAM,kBAAkB;IACtC;IACA,OAAO;AACX;AAEA;;;;;CAKC,GACD,QAAQ,cAAc,GAAG,SAAU,GAAG,EAAE,OAAO,EAAE,OAAO;IAEpD,IAAI,gBAAgB,IAAI,cAAc,QAAQ,WAAW,EAAE,SAAS,QAAQ,QAAQ,EAAE,QAAQ,cAAc;IAC5G,IAAI,eAAe;IACnB,IAAI;QAEA,IAAI,OAAO,CAAC,SAAU,YAAY,EAAE,IAAI;YACpC;YACA,IAAI,cAAc,eAAe,KAAK,OAAO,CAAC,WAAW,EAAE,QAAQ,WAAW;YAC9E,IAAI,qBAAqB,KAAK,OAAO,CAAC,kBAAkB,IAAI,QAAQ,kBAAkB,IAAI,CAAC;YAC3F,IAAI,MAAM,KAAK,GAAG,EAAE,OAAO,KAAK,IAAI;YAEpC,KAAK,eAAe,CAAC,aAAa,oBAC7B,cAAc,CAAC,QAAQ;gBACpB,MAAO;gBACP,KAAM;gBACN,MAAO;gBACP,SAAU,KAAK,OAAO,IAAI;gBAC1B,iBAAkB,KAAK,eAAe;gBACtC,gBAAiB,KAAK,cAAc;YACxC,GACC,IAAI,CAAC;QACd;QACA,cAAc,YAAY,GAAG;IACjC,EAAE,OAAO,GAAG;QACR,cAAc,KAAK,CAAC;IACxB;IAEA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2384, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar GenericWorker = require(\"../stream/GenericWorker\");\n\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */\nfunction NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\n\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */\nNodejsStreamInputAdapter.prototype._bindStream = function (stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream\n        .on(\"data\", function (chunk) {\n            self.push({\n                data: chunk,\n                meta : {\n                    percent : 0\n                }\n            });\n        })\n        .on(\"error\", function (e) {\n            if(self.isPaused) {\n                this.generatedError = e;\n            } else {\n                self.error(e);\n            }\n        })\n        .on(\"end\", function () {\n            if(self.isPaused) {\n                self._upstreamEnded = true;\n            } else {\n                self.end();\n            }\n        });\n};\nNodejsStreamInputAdapter.prototype.pause = function () {\n    if(!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if(this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n\n    return true;\n};\n\nmodule.exports = NodejsStreamInputAdapter;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ;;;;;CAKC,GACD,SAAS,yBAAyB,QAAQ,EAAE,MAAM;IAC9C,cAAc,IAAI,CAAC,IAAI,EAAE,qCAAqC;IAC9D,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,WAAW,CAAC;AACrB;AAEA,MAAM,QAAQ,CAAC,0BAA0B;AAEzC;;;;CAIC,GACD,yBAAyB,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;IAC7D,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,OAAO,GAAG;IACf,OAAO,KAAK;IACZ,OACK,EAAE,CAAC,QAAQ,SAAU,KAAK;QACvB,KAAK,IAAI,CAAC;YACN,MAAM;YACN,MAAO;gBACH,SAAU;YACd;QACJ;IACJ,GACC,EAAE,CAAC,SAAS,SAAU,CAAC;QACpB,IAAG,KAAK,QAAQ,EAAE;YACd,IAAI,CAAC,cAAc,GAAG;QAC1B,OAAO;YACH,KAAK,KAAK,CAAC;QACf;IACJ,GACC,EAAE,CAAC,OAAO;QACP,IAAG,KAAK,QAAQ,EAAE;YACd,KAAK,cAAc,GAAG;QAC1B,OAAO;YACH,KAAK,GAAG;QACZ;IACJ;AACR;AACA,yBAAyB,SAAS,CAAC,KAAK,GAAG;IACvC,IAAG,CAAC,cAAc,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG;QAC1C,OAAO;IACX;IACA,IAAI,CAAC,OAAO,CAAC,KAAK;IAClB,OAAO;AACX;AACA,yBAAyB,SAAS,CAAC,MAAM,GAAG;IACxC,IAAG,CAAC,cAAc,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG;QAC3C,OAAO;IACX;IAEA,IAAG,IAAI,CAAC,cAAc,EAAE;QACpB,IAAI,CAAC,GAAG;IACZ,OAAO;QACH,IAAI,CAAC,OAAO,CAAC,MAAM;IACvB;IAEA,OAAO;AACX;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2451, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/object.js"], "sourcesContent": ["\"use strict\";\nvar utf8 = require(\"./utf8\");\nvar utils = require(\"./utils\");\nvar GenericWorker = require(\"./stream/GenericWorker\");\nvar StreamHelper = require(\"./stream/StreamHelper\");\nvar defaults = require(\"./defaults\");\nvar CompressedObject = require(\"./compressedObject\");\nvar ZipObject = require(\"./zipObject\");\nvar generate = require(\"./generate\");\nvar nodejsUtils = require(\"./nodejsUtils\");\nvar NodejsStreamInputAdapter = require(\"./nodejs/NodejsStreamInputAdapter\");\n\n\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */\nvar fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data),\n        parent;\n\n\n    /*\n     * Correct options.\n     */\n\n    var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && (o.unixPermissions & 0x4000)) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && (o.dosPermissions & 0x0010)) {\n        o.dir = true;\n    }\n\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n\n\n    var isCompressedEmpty = (data instanceof CompressedObject) && data.uncompressedSize === 0;\n\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n\n    /*\n     * Convert content to fit.\n     */\n\n    var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n    /*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */\n};\n\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */\nvar parentFolder = function (path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return (lastSlash > 0) ? path.substring(0, lastSlash) : \"\";\n};\n\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */\nvar forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */\nvar folderAdd = function(name, createFolders) {\n    createFolders = (typeof createFolders !== \"undefined\") ? createFolders : defaults.createFolders;\n\n    name = forceTrailingSlash(name);\n\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/\nfunction isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */\n    load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */\n    forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */\n        for (filename in this.files) {\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) { // the file is in the current root\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */\n    filter: function(search) {\n        var result = [];\n        this.forEach(function (relativePath, entry) {\n            if (search(relativePath, entry)) { // the file matches the function\n                result.push(entry);\n            }\n\n        });\n        return result;\n    },\n\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */\n    file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            }\n            else { // text\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        }\n        else { // more than one argument : we have data !\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */\n    folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */\n    remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for (var i = 0; i < kids.length; i++) {\n                delete this.files[kids[i].name];\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */\n    generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */\n    generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions : null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if(opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n\n            utils.checkSupport(opts.type);\n\n            // accept nodejs `process.platform`\n            if(\n                opts.platform === \"darwin\" ||\n                opts.platform === \"freebsd\" ||\n                opts.platform === \"linux\" ||\n                opts.platform === \"sunos\"\n            ) {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAGJ;;;;;;;CAOC,GACD,IAAI,UAAU,SAAS,IAAI,EAAE,IAAI,EAAE,eAAe;IAC9C,4BAA4B;IAC5B,IAAI,WAAW,MAAM,SAAS,CAAC,OAC3B;IAGJ;;KAEC,GAED,IAAI,IAAI,MAAM,MAAM,CAAC,mBAAmB,CAAC,GAAG;IAC5C,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI;IACvB,IAAI,EAAE,WAAW,KAAK,MAAM;QACxB,EAAE,WAAW,GAAG,EAAE,WAAW,CAAC,WAAW;IAC7C;IAEA,IAAI,OAAO,EAAE,eAAe,KAAK,UAAU;QACvC,EAAE,eAAe,GAAG,SAAS,EAAE,eAAe,EAAE;IACpD;IAEA,mCAAmC;IACnC,IAAI,EAAE,eAAe,IAAK,EAAE,eAAe,GAAG,QAAS;QACnD,EAAE,GAAG,GAAG;IACZ;IACA,qBAAqB;IACrB,IAAI,EAAE,cAAc,IAAK,EAAE,cAAc,GAAG,QAAS;QACjD,EAAE,GAAG,GAAG;IACZ;IAEA,IAAI,EAAE,GAAG,EAAE;QACP,OAAO,mBAAmB;IAC9B;IACA,IAAI,EAAE,aAAa,IAAI,CAAC,SAAS,aAAa,KAAK,GAAG;QAClD,UAAU,IAAI,CAAC,IAAI,EAAE,QAAQ;IACjC;IAEA,IAAI,kBAAkB,aAAa,YAAY,EAAE,MAAM,KAAK,SAAS,EAAE,MAAM,KAAK;IAClF,IAAI,CAAC,mBAAmB,OAAO,gBAAgB,MAAM,KAAK,aAAa;QACnE,EAAE,MAAM,GAAG,CAAC;IAChB;IAGA,IAAI,oBAAoB,AAAC,gBAAgB,oBAAqB,KAAK,gBAAgB,KAAK;IAExF,IAAI,qBAAqB,EAAE,GAAG,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC1D,EAAE,MAAM,GAAG;QACX,EAAE,MAAM,GAAG;QACX,OAAO;QACP,EAAE,WAAW,GAAG;QAChB,WAAW;IACf;IAEA;;KAEC,GAED,IAAI,mBAAmB;IACvB,IAAI,gBAAgB,oBAAoB,gBAAgB,eAAe;QACnE,mBAAmB;IACvB,OAAO,IAAI,YAAY,MAAM,IAAI,YAAY,QAAQ,CAAC,OAAO;QACzD,mBAAmB,IAAI,yBAAyB,MAAM;IAC1D,OAAO;QACH,mBAAmB,MAAM,cAAc,CAAC,MAAM,MAAM,EAAE,MAAM,EAAE,EAAE,qBAAqB,EAAE,EAAE,MAAM;IACnG;IAEA,IAAI,SAAS,IAAI,UAAU,MAAM,kBAAkB;IACnD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;AACnB;;;;;;;;;;;IAWA,GACJ;AAEA;;;;;CAKC,GACD,IAAI,eAAe,SAAU,IAAI;IAC7B,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK;QACxB,OAAO,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG;IAC3C;IACA,IAAI,YAAY,KAAK,WAAW,CAAC;IACjC,OAAO,AAAC,YAAY,IAAK,KAAK,SAAS,CAAC,GAAG,aAAa;AAC5D;AAEA;;;;;CAKC,GACD,IAAI,qBAAqB,SAAS,IAAI;IAClC,+BAA+B;IAC/B,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK;QACxB,QAAQ,KAAK,6BAA6B;IAC9C;IACA,OAAO;AACX;AAEA;;;;;;;CAOC,GACD,IAAI,YAAY,SAAS,IAAI,EAAE,aAAa;IACxC,gBAAgB,AAAC,OAAO,kBAAkB,cAAe,gBAAgB,SAAS,aAAa;IAE/F,OAAO,mBAAmB;IAE1B,kCAAkC;IAClC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QACnB,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM,MAAM;YAC3B,KAAK;YACL,eAAe;QACnB;IACJ;IACA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;AAC3B;AAEA;;;;;AAKA,GACA,SAAS,SAAS,MAAM;IACpB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY;AACtD;AAEA,uCAAuC;AACvC,IAAI,MAAM;IACN;;KAEC,GACD,MAAM;QACF,MAAM,IAAI,MAAM;IACpB;IAGA;;;;;KAKC,GACD,SAAS,SAAS,EAAE;QAChB,IAAI,UAAU,cAAc;QAC5B,yFAAyF;QACzF,yCAAyC,GACzC,IAAK,YAAY,IAAI,CAAC,KAAK,CAAE;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;YAC3B,eAAe,SAAS,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM;YAC/D,IAAI,gBAAgB,SAAS,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE;gBACnE,GAAG,cAAc,OAAO,6FAA6F;YACzH;QACJ;IACJ;IAEA;;;;;;KAMC,GACD,QAAQ,SAAS,MAAM;QACnB,IAAI,SAAS,EAAE;QACf,IAAI,CAAC,OAAO,CAAC,SAAU,YAAY,EAAE,KAAK;YACtC,IAAI,OAAO,cAAc,QAAQ;gBAC7B,OAAO,IAAI,CAAC;YAChB;QAEJ;QACA,OAAO;IACX;IAEA;;;;;;;;KAQC,GACD,MAAM,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC;QACxB,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,IAAI,SAAS,OAAO;gBAChB,IAAI,SAAS;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,YAAY,EAAE,IAAI;oBAC1C,OAAO,CAAC,KAAK,GAAG,IAAI,OAAO,IAAI,CAAC;gBACpC;YACJ,OACK;gBACD,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK;gBACtC,IAAI,OAAO,CAAC,IAAI,GAAG,EAAE;oBACjB,OAAO;gBACX,OAAO;oBACH,OAAO;gBACX;YACJ;QACJ,OACK;YACD,OAAO,IAAI,CAAC,IAAI,GAAG;YACnB,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM,MAAM;QACnC;QACA,OAAO,IAAI;IACf;IAEA;;;;KAIC,GACD,QAAQ,SAAS,GAAG;QAChB,IAAI,CAAC,KAAK;YACN,OAAO,IAAI;QACf;QAEA,IAAI,SAAS,MAAM;YACf,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,YAAY,EAAE,IAAI;gBAC1C,OAAO,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC;YAChC;QACJ;QAEA,6BAA6B;QAC7B,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;QACvB,IAAI,YAAY,UAAU,IAAI,CAAC,IAAI,EAAE;QAErC,wEAAwE;QACxE,IAAI,MAAM,IAAI,CAAC,KAAK;QACpB,IAAI,IAAI,GAAG,UAAU,IAAI;QACzB,OAAO;IACX;IAEA;;;;KAIC,GACD,QAAQ,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,GAAG;QACnB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;QAC3B,IAAI,CAAC,MAAM;YACP,uBAAuB;YACvB,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK;gBACxB,QAAQ;YACZ;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;QAC3B;QAEA,IAAI,QAAQ,CAAC,KAAK,GAAG,EAAE;YACnB,OAAO;YACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;QAC3B,OAAO;YACH,qCAAqC;YACrC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,YAAY,EAAE,IAAI;gBAC9C,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,MAAM;YAC/C;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBAClC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YACnC;QACJ;QAEA,OAAO,IAAI;IACf;IAEA;;KAEC,GACD,UAAU;QACN,MAAM,IAAI,MAAM;IACpB;IAEA;;;;;;KAMC,GACD,wBAAwB,SAAS,OAAO;QACpC,IAAI,QAAQ,OAAO,CAAC;QACpB,IAAI;YACA,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG;gBAC/B,aAAa;gBACb,aAAa;gBACb,oBAAqB;gBACrB,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,gBAAgB,KAAK,UAAU;YACnC;YAEA,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,WAAW;YACjC,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC,WAAW;YAE/C,8DAA8D;YAC9D,IAAG,KAAK,IAAI,KAAK,gBAAgB;gBAC7B,KAAK,IAAI,GAAG;YAChB;YAEA,IAAI,CAAC,KAAK,IAAI,EAAE;gBACZ,MAAM,IAAI,MAAM;YACpB;YAEA,MAAM,YAAY,CAAC,KAAK,IAAI;YAE5B,mCAAmC;YACnC,IACI,KAAK,QAAQ,KAAK,YAClB,KAAK,QAAQ,KAAK,aAClB,KAAK,QAAQ,KAAK,WAClB,KAAK,QAAQ,KAAK,SACpB;gBACE,KAAK,QAAQ,GAAG;YACpB;YACA,IAAI,KAAK,QAAQ,KAAK,SAAS;gBAC3B,KAAK,QAAQ,GAAG;YACpB;YAEA,IAAI,UAAU,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI;YAC9C,SAAS,SAAS,cAAc,CAAC,IAAI,EAAE,MAAM;QACjD,EAAE,OAAO,GAAG;YACR,SAAS,IAAI,cAAc;YAC3B,OAAO,KAAK,CAAC;QACjB;QACA,OAAO,IAAI,aAAa,QAAQ,KAAK,IAAI,IAAI,UAAU,KAAK,QAAQ;IACxE;IACA;;;KAGC,GACD,eAAe,SAAS,OAAO,EAAE,QAAQ;QACrC,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,UAAU,CAAC;IAC3D;IACA;;;KAGC,GACD,oBAAoB,SAAS,OAAO,EAAE,QAAQ;QAC1C,UAAU,WAAW,CAAC;QACtB,IAAI,CAAC,QAAQ,IAAI,EAAE;YACf,QAAQ,IAAI,GAAG;QACnB;QACA,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,cAAc,CAAC;IAC/D;AACJ;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/DataReader.js"], "sourcesContent": ["\"use strict\";\nvar utils = require(\"../utils\");\n\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */\n    checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */\n    checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + (newIndex) + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */\n    setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */\n    skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */\n    byteAt: function() {\n        // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */\n    readInt: function(size) {\n        var result = 0,\n            i;\n        this.checkOffset(size);\n        for (i = this.index + size - 1; i >= this.index; i--) {\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */\n    readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */\n    readData: function() {\n        // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */\n    lastIndexOfSignature: function() {\n        // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */\n    readAndCheckSignature: function() {\n        // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */\n    readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC(\n            ((dostime >> 25) & 0x7f) + 1980, // year\n            ((dostime >> 21) & 0x0f) - 1, // month\n            (dostime >> 16) & 0x1f, // day\n            (dostime >> 11) & 0x1f, // hour\n            (dostime >> 5) & 0x3f, // minute\n            (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AAEJ,SAAS,WAAW,IAAI;IACpB,IAAI,CAAC,IAAI,GAAG,MAAM,4BAA4B;IAC9C,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IACzB,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AAChB;AACA,WAAW,SAAS,GAAG;IACnB;;;;KAIC,GACD,aAAa,SAAS,MAAM;QACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG;IACjC;IACA;;;;KAIC,GACD,YAAY,SAAS,QAAQ;QACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,WAAW,GAAG;YACpD,MAAM,IAAI,MAAM,wCAAwC,IAAI,CAAC,MAAM,GAAG,qBAAsB,WAAY;QAC5G;IACJ;IACA;;;;KAIC,GACD,UAAU,SAAS,QAAQ;QACvB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;;KAIC,GACD,MAAM,SAAS,CAAC;QACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG;IAC/B;IACA;;;;KAIC,GACD,QAAQ;IACJ,sBAAsB;IAC1B;IACA;;;;KAIC,GACD,SAAS,SAAS,IAAI;QAClB,IAAI,SAAS,GACT;QACJ,IAAI,CAAC,WAAW,CAAC;QACjB,IAAK,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,KAAK,IAAI,CAAC,KAAK,EAAE,IAAK;YAClD,SAAS,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;QACzC;QACA,IAAI,CAAC,KAAK,IAAI;QACd,OAAO;IACX;IACA;;;;KAIC,GACD,YAAY,SAAS,IAAI;QACrB,OAAO,MAAM,WAAW,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC;IACrD;IACA;;;;KAIC,GACD,UAAU;IACN,sBAAsB;IAC1B;IACA;;;;KAIC,GACD,sBAAsB;IAClB,sBAAsB;IAC1B;IACA;;;;KAIC,GACD,uBAAuB;IACnB,sBAAsB;IAC1B;IACA;;;KAGC,GACD,UAAU;QACN,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC;QAC3B,OAAO,IAAI,KAAK,KAAK,GAAG,CACpB,CAAC,AAAC,WAAW,KAAM,IAAI,IAAI,MAC3B,CAAC,AAAC,WAAW,KAAM,IAAI,IAAI,GAC3B,AAAC,WAAW,KAAM,MAClB,AAAC,WAAW,KAAM,MAClB,AAAC,WAAW,IAAK,MACjB,CAAC,UAAU,IAAI,KAAK,KAAK,SAAS;IAC1C;AACJ;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/ArrayReader.js"], "sourcesContent": ["\"use strict\";\nvar DataReader = require(\"./DataReader\");\nvar utils = require(\"../utils\");\n\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++) {\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DataReader);\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3);\n    for (var i = this.length - 4; i >= 0; --i) {\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nArrayReader.prototype.readAndCheckSignature = function (sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3),\n        data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AAEJ,SAAS,YAAY,IAAI;IACrB,WAAW,IAAI,CAAC,IAAI,EAAE;IACtB,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;QACtC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG;IACxB;AACJ;AACA,MAAM,QAAQ,CAAC,aAAa;AAC5B;;CAEC,GACD,YAAY,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;IACrC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnC;AACA;;CAEC,GACD,YAAY,SAAS,CAAC,oBAAoB,GAAG,SAAS,GAAG;IACrD,IAAI,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC;IAC1B,IAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;QACvC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM;YAC9G,OAAO,IAAI,IAAI,CAAC,IAAI;QACxB;IACJ;IAEA,OAAO,CAAC;AACZ;AACA;;CAEC,GACD,YAAY,SAAS,CAAC,qBAAqB,GAAG,SAAU,GAAG;IACvD,IAAI,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,UAAU,CAAC,IACtB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,OAAO,SAAS,IAAI,CAAC,EAAE,IAAI,SAAS,IAAI,CAAC,EAAE,IAAI,SAAS,IAAI,CAAC,EAAE,IAAI,SAAS,IAAI,CAAC,EAAE;AACvF;AACA;;CAEC,GACD,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC1C,IAAI,CAAC,WAAW,CAAC;IACjB,IAAG,SAAS,GAAG;QACX,OAAO,EAAE;IACb;IACA,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;IAC9E,IAAI,CAAC,KAAK,IAAI;IACd,OAAO;AACX;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/StringReader.js"], "sourcesContent": ["\"use strict\";\nvar DataReader = require(\"./DataReader\");\nvar utils = require(\"../utils\");\n\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nStringReader.prototype.readAndCheckSignature = function (sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AAEJ,SAAS,aAAa,IAAI;IACtB,WAAW,IAAI,CAAC,IAAI,EAAE;AAC1B;AACA,MAAM,QAAQ,CAAC,cAAc;AAC7B;;CAEC,GACD,aAAa,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;IACtC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG;AAC5C;AACA;;CAEC,GACD,aAAa,SAAS,CAAC,oBAAoB,GAAG,SAAS,GAAG;IACtD,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI;AACjD;AACA;;CAEC,GACD,aAAa,SAAS,CAAC,qBAAqB,GAAG,SAAU,GAAG;IACxD,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,OAAO,QAAQ;AACnB;AACA;;CAEC,GACD,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC3C,IAAI,CAAC,WAAW,CAAC;IACjB,oEAAoE;IACpE,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;IAC9E,IAAI,CAAC,KAAK,IAAI;IACd,OAAO;AACX;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/Uint8ArrayReader.js"], "sourcesContent": ["\"use strict\";\nvar ArrayReader = require(\"./ArrayReader\");\nvar utils = require(\"../utils\");\n\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8<PERSON><PERSON>yReader, ArrayReader);\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AAEJ,SAAS,iBAAiB,IAAI;IAC1B,YAAY,IAAI,CAAC,IAAI,EAAE;AAC3B;AACA,MAAM,QAAQ,CAAC,kBAAkB;AACjC;;CAEC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC/C,IAAI,CAAC,WAAW,CAAC;IACjB,IAAG,SAAS,GAAG;QACX,iFAAiF;QACjF,OAAO,IAAI,WAAW;IAC1B;IACA,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;IACjF,IAAI,CAAC,KAAK,IAAI;IACd,OAAO;AACX;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2985, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/NodeBufferReader.js"], "sourcesContent": ["\"use strict\";\nvar Uint8ArrayReader = require(\"./Uint8ArrayReader\");\nvar utils = require(\"../utils\");\n\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Uint8ArrayReader);\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AAEJ,SAAS,iBAAiB,IAAI;IAC1B,iBAAiB,IAAI,CAAC,IAAI,EAAE;AAChC;AACA,MAAM,QAAQ,CAAC,kBAAkB;AAEjC;;CAEC,GACD,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC/C,IAAI,CAAC,WAAW,CAAC;IACjB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;IAC9E,IAAI,CAAC,KAAK,IAAI;IACd,OAAO;AACX;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3006, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/reader/readerFor.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"../utils\");\nvar support = require(\"../support\");\nvar ArrayReader = require(\"./ArrayReader\");\nvar StringReader = require(\"./StringReader\");\nvar NodeBufferReader = require(\"./NodeBufferReader\");\nvar Uint8ArrayReader = require(\"./Uint8ArrayReader\");\n\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */\nmodule.exports = function (data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;CAIC,GACD,OAAO,OAAO,GAAG,SAAU,IAAI;IAC3B,IAAI,OAAO,MAAM,SAAS,CAAC;IAC3B,MAAM,YAAY,CAAC;IACnB,IAAI,SAAS,YAAY,CAAC,QAAQ,UAAU,EAAE;QAC1C,OAAO,IAAI,aAAa;IAC5B;IACA,IAAI,SAAS,cAAc;QACvB,OAAO,IAAI,iBAAiB;IAChC;IACA,IAAI,QAAQ,UAAU,EAAE;QACpB,OAAO,IAAI,iBAAiB,MAAM,WAAW,CAAC,cAAc;IAChE;IACA,OAAO,IAAI,YAAY,MAAM,WAAW,CAAC,SAAS;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/zipEntry.js"], "sourcesContent": ["\"use strict\";\nvar readerFor = require(\"./reader/readerFor\");\nvar utils = require(\"./utils\");\nvar CompressedObject = require(\"./compressedObject\");\nvar crc32fn = require(\"./crc32\");\nvar utf8 = require(\"./utf8\");\nvar compressions = require(\"./compressions\");\nvar support = require(\"./support\");\n\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */\nvar findCompression = function(compressionMethod) {\n    for (var method in compressions) {\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */\n    isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */\n    useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) { // no compression found\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */\n    processAttributes: function () {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n\n        if(madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n\n        if(madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = (this.externalFileAttributes >> 16) & 0xFFFF;\n            // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */\n    parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n\n        while (reader.index + 4 < end) {\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */\n    handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray =  utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray =  utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */\n    findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */\n    findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,cAAc;AAClB,IAAI,eAAe;AAEnB;;;;CAIC,GACD,IAAI,kBAAkB,SAAS,iBAAiB;IAC5C,IAAK,IAAI,UAAU,aAAc;QAC7B,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,SAAS;YAC7D;QACJ;QACA,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,mBAAmB;YAClD,OAAO,YAAY,CAAC,OAAO;QAC/B;IACJ;IACA,OAAO;AACX;AAEA,qBAAqB;AACrB;;;;;CAKC,GACD,SAAS,SAAS,OAAO,EAAE,WAAW;IAClC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,WAAW,GAAG;AACvB;AACA,SAAS,SAAS,GAAG;IACjB;;;KAGC,GACD,aAAa;QACT,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM;IACvC;IACA;;;KAGC,GACD,SAAS;QACL,gBAAgB;QAChB,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM;IACvC;IACA;;;KAGC,GACD,eAAe,SAAS,MAAM;QAC1B,IAAI,aAAa;QAEjB,oDAAoD;QACpD,oDAAoD;QACpD,qFAAqF;QACrF,+DAA+D;QAC/D,qDAAqD;QACrD,OAAO,IAAI,CAAC;QACZ,kGAAkG;QAClG,sCAAsC;QACtC,uGAAuG;QACvG,+GAA+G;QAC/G,4FAA4F;QAC5F,gBAAgB;QAChB,EAAE;QACF,0EAA0E;QAC1E,+EAA+E;QAC/E,iFAAiF;QACjF,iGAAiG;QACjG,IAAI,CAAC,cAAc,GAAG,OAAO,OAAO,CAAC;QACrC,yBAAyB,OAAO,OAAO,CAAC,IAAI,yDAAyD;QACrG,+FAA+F;QAC/F,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc;QACnD,OAAO,IAAI,CAAC;QAEZ,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,KAAK,IAAI,CAAC,gBAAgB,KAAK,CAAC,GAAG;YAC5D,MAAM,IAAI,MAAM,qFAAqF;QACzG;QAEA,cAAc,gBAAgB,IAAI,CAAC,iBAAiB;QACpD,IAAI,gBAAgB,MAAM;YACtB,MAAM,IAAI,MAAM,iCAAiC,MAAM,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,4BAA4B,MAAM,WAAW,CAAC,UAAU,IAAI,CAAC,QAAQ,IAAI;QACrK;QACA,IAAI,CAAC,YAAY,GAAG,IAAI,iBAAiB,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,EAAE,aAAa,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc;IACrJ;IAEA;;;KAGC,GACD,iBAAiB,SAAS,MAAM;QAC5B,IAAI,CAAC,aAAa,GAAG,OAAO,OAAO,CAAC;QACpC,OAAO,IAAI,CAAC;QACZ,0CAA0C;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG,OAAO,UAAU,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,OAAO,QAAQ;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,OAAO,OAAO,CAAC;QACrC,IAAI,CAAC,gBAAgB,GAAG,OAAO,OAAO,CAAC;QACvC,IAAI,iBAAiB,OAAO,OAAO,CAAC;QACpC,IAAI,CAAC,iBAAiB,GAAG,OAAO,OAAO,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,OAAO,OAAO,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,OAAO,OAAO,CAAC;QACtC,IAAI,CAAC,sBAAsB,GAAG,OAAO,OAAO,CAAC;QAC7C,IAAI,CAAC,sBAAsB,GAAG,OAAO,OAAO,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,OAAO,OAAO,CAAC;QAExC,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,MAAM,IAAI,MAAM;QACpB;QAEA,yDAAyD;QACzD,OAAO,IAAI,CAAC;QACZ,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,oBAAoB,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,iBAAiB;IAC7D;IAEA;;KAEC,GACD,mBAAmB;QACf,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,SAAS,IAAI,CAAC,aAAa,IAAI;QAEnC,+CAA+C;QAC/C,iDAAiD;QACjD,kEAAkE;QAClE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,sBAAsB,GAAG,SAAS,OAAO;QAEzD,IAAG,WAAW,aAAa;YACvB,wBAAwB;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,GAAG;QACxD;QAEA,IAAG,WAAW,cAAc;YACxB,IAAI,CAAC,eAAe,GAAG,AAAC,IAAI,CAAC,sBAAsB,IAAI,KAAM;QAC7D,4EAA4E;QAChF;QAEA,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;YACjD,IAAI,CAAC,GAAG,GAAG;QACf;IACJ;IAEA;;;KAGC,GACD,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC3B;QACJ;QAEA,kDAAkD;QAClD,IAAI,cAAc,UAAU,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK;QAE1D,iFAAiF;QACjF,0BAA0B;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM,gBAAgB,EAAE;YAClD,IAAI,CAAC,gBAAgB,GAAG,YAAY,OAAO,CAAC;QAChD;QACA,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM,gBAAgB,EAAE;YAChD,IAAI,CAAC,cAAc,GAAG,YAAY,OAAO,CAAC;QAC9C;QACA,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM,gBAAgB,EAAE;YACnD,IAAI,CAAC,iBAAiB,GAAG,YAAY,OAAO,CAAC;QACjD;QACA,IAAI,IAAI,CAAC,eAAe,KAAK,MAAM,gBAAgB,EAAE;YACjD,IAAI,CAAC,eAAe,GAAG,YAAY,OAAO,CAAC;QAC/C;IACJ;IACA;;;KAGC,GACD,iBAAiB,SAAS,MAAM;QAC5B,IAAI,MAAM,OAAO,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAC3C,cACA,kBACA;QAEJ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,CAAC;QACxB;QAEA,MAAO,OAAO,KAAK,GAAG,IAAI,IAAK;YAC3B,eAAe,OAAO,OAAO,CAAC;YAC9B,mBAAmB,OAAO,OAAO,CAAC;YAClC,kBAAkB,OAAO,QAAQ,CAAC;YAElC,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG;gBAC7B,IAAI;gBACJ,QAAQ;gBACR,OAAO;YACX;QACJ;QAEA,OAAO,QAAQ,CAAC;IACpB;IACA;;KAEC,GACD,YAAY;QACR,IAAI,kBAAkB,QAAQ,UAAU,GAAG,eAAe;QAC1D,IAAI,IAAI,CAAC,OAAO,IAAI;YAChB,IAAI,CAAC,WAAW,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;YAChD,IAAI,CAAC,cAAc,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,WAAW;QAC1D,OAAO;YACH,IAAI,QAAQ,IAAI,CAAC,yBAAyB;YAC1C,IAAI,UAAU,MAAM;gBAChB,IAAI,CAAC,WAAW,GAAG;YACvB,OAAO;gBACH,sCAAsC;gBACtC,IAAI,oBAAqB,MAAM,WAAW,CAAC,iBAAiB,IAAI,CAAC,QAAQ;gBACzE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;YACvD;YAEA,IAAI,WAAW,IAAI,CAAC,4BAA4B;YAChD,IAAI,aAAa,MAAM;gBACnB,IAAI,CAAC,cAAc,GAAG;YAC1B,OAAO;gBACH,sCAAsC;gBACtC,IAAI,mBAAoB,MAAM,WAAW,CAAC,iBAAiB,IAAI,CAAC,WAAW;gBAC3E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;YAC1D;QACJ;IACJ;IAEA;;;KAGC,GACD,2BAA2B;QACvB,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC,OAAO;QACzC,IAAI,YAAY;YACZ,IAAI,cAAc,UAAU,WAAW,KAAK;YAE5C,gBAAgB;YAChB,IAAI,YAAY,OAAO,CAAC,OAAO,GAAG;gBAC9B,OAAO;YACX;YAEA,8DAA8D;YAC9D,IAAI,QAAQ,IAAI,CAAC,QAAQ,MAAM,YAAY,OAAO,CAAC,IAAI;gBACnD,OAAO;YACX;YAEA,OAAO,KAAK,UAAU,CAAC,YAAY,QAAQ,CAAC,WAAW,MAAM,GAAG;QACpE;QACA,OAAO;IACX;IAEA;;;KAGC,GACD,8BAA8B;QAC1B,IAAI,gBAAgB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5C,IAAI,eAAe;YACf,IAAI,cAAc,UAAU,cAAc,KAAK;YAE/C,gBAAgB;YAChB,IAAI,YAAY,OAAO,CAAC,OAAO,GAAG;gBAC9B,OAAO;YACX;YAEA,6DAA6D;YAC7D,IAAI,QAAQ,IAAI,CAAC,WAAW,MAAM,YAAY,OAAO,CAAC,IAAI;gBACtD,OAAO;YACX;YAEA,OAAO,KAAK,UAAU,CAAC,YAAY,QAAQ,CAAC,cAAc,MAAM,GAAG;QACvE;QACA,OAAO;IACX;AACJ;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/zipEntries.js"], "sourcesContent": ["\"use strict\";\nvar readerFor = require(\"./reader/readerFor\");\nvar utils = require(\"./utils\");\nvar sig = require(\"./signature\");\nvar ZipEntry = require(\"./zipEntry\");\nvar support = require(\"./support\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */\n    checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */\n    isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */\n    readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */\n    readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44,\n            index = 0,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n        while (index < extraDataSize) {\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */\n    readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */\n    readLocalFiles: function() {\n        var i, file;\n        for (i = 0; i < this.files.length; i++) {\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */\n    readCentralDir: function() {\n        var file;\n\n        this.reader.setIndex(this.centralDirOffset);\n        while (this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)) {\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n                // We found some records but not all.\n                // Something is wrong but we got something for the user: no error here.\n                // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */\n    readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" +\n                                \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n\n\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */\n        if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */\n\n            // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n        }\n\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n                // The offsets seem wrong, but we have something at the specified offset.\n                // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */\n    load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,wBAAwB;AACxB;;;;CAIC,GACD,SAAS,WAAW,WAAW;IAC3B,IAAI,CAAC,KAAK,GAAG,EAAE;IACf,IAAI,CAAC,WAAW,GAAG;AACvB;AACA,WAAW,SAAS,GAAG;IACnB;;;;KAIC,GACD,gBAAgB,SAAS,iBAAiB;QACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,oBAAoB;YACvD,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;YACrB,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACvC,MAAM,IAAI,MAAM,gDAAgD,MAAM,MAAM,MAAM,CAAC,aAAa,gBAAgB,MAAM,MAAM,CAAC,qBAAqB;QACtJ;IACJ;IACA;;;;;KAKC,GACD,aAAa,SAAS,UAAU,EAAE,iBAAiB;QAC/C,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,KAAK;QACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACrB,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,SAAS,cAAc;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACrB,OAAO;IACX;IACA;;KAEC,GACD,uBAAuB;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC5C,sDAAsD;QACtD,uEAAuE;QACvE,oFAAoF;QACpF,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB;QAC3D,IAAI,kBAAkB,QAAQ,UAAU,GAAG,eAAe;QAC1D,2EAA2E;QAC3E,mDAAmD;QACnD,IAAI,gBAAgB,MAAM,WAAW,CAAC,iBAAiB;QACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;IACtD;IACA;;;;;KAKC,GACD,4BAA4B;QACxB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,kDAAkD;QAClD,+CAA+C;QAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,mBAAmB,GAAG,CAAC;QAC5B,IAAI,gBAAgB,IAAI,CAAC,qBAAqB,GAAG,IAC7C,QAAQ,GACR,cACA,kBACA;QACJ,MAAO,QAAQ,cAAe;YAC1B,eAAe,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACnC,mBAAmB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YACvC,kBAAkB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvC,IAAI,CAAC,mBAAmB,CAAC,aAAa,GAAG;gBACrC,IAAI;gBACJ,QAAQ;gBACR,OAAO;YACX;QACJ;IACJ;IACA;;KAEC,GACD,mCAAmC;QAC/B,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACxD,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC9D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtC,IAAI,IAAI,CAAC,UAAU,GAAG,GAAG;YACrB,MAAM,IAAI,MAAM;QACpB;IACJ;IACA;;KAEC,GACD,gBAAgB;QACZ,IAAI,GAAG;QACP,IAAK,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACpC,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,iBAAiB;YAC3C,IAAI,CAAC,cAAc,CAAC,IAAI,iBAAiB;YACzC,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM;YAC9B,KAAK,UAAU;YACf,KAAK,iBAAiB;QAC1B;IACJ;IACA;;KAEC,GACD,gBAAgB;QACZ,IAAI;QAEJ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB;QAC1C,MAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,mBAAmB,EAAG;YAC/D,OAAO,IAAI,SAAS;gBAChB,OAAO,IAAI,CAAC,KAAK;YACrB,GAAG,IAAI,CAAC,WAAW;YACnB,KAAK,eAAe,CAAC,IAAI,CAAC,MAAM;YAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACpB;QAEA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAC9C,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gBACzD,kDAAkD;gBAClD,yDAAyD;gBACzD,MAAM,IAAI,MAAM,oCAAoC,IAAI,CAAC,iBAAiB,GAAG,kCAAkC,IAAI,CAAC,KAAK,CAAC,MAAM;YACpI,OAAO;YACH,qCAAqC;YACrC,uEAAuE;YACvE,sGAAsG;YAC1G;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB;QACd,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,qBAAqB;QACvE,IAAI,SAAS,GAAG;YACZ,+DAA+D;YAC/D,+DAA+D;YAC/D,4DAA4D;YAC5D,iEAAiE;YACjE,uBAAuB;YACvB,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,iBAAiB;YAE1D,IAAI,WAAW;gBACX,MAAM,IAAI,MAAM,gEACA;YACpB,OAAO;gBACH,MAAM,IAAI,MAAM;YACpB;QAEJ;QACA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACrB,IAAI,wBAAwB;QAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,qBAAqB;QAC7C,IAAI,CAAC,qBAAqB;QAG1B;;;;;;;;;SASC,GACD,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,uBAAuB,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,2BAA2B,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM,gBAAgB,EAAE;YACjU,IAAI,CAAC,KAAK,GAAG;YAEb;;;;;;;YAOA,GAEA,uCAAuC;YACvC,SAAS,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,+BAA+B;YAC7E,IAAI,SAAS,GAAG;gBACZ,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,+BAA+B;YACvD,IAAI,CAAC,iCAAiC;YAEtC,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kCAAkC,EAAE,IAAI,2BAA2B,GAAG;gBAC7F,sEAAsE;gBACtE,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,2BAA2B;gBAC1G,IAAI,IAAI,CAAC,kCAAkC,GAAG,GAAG;oBAC7C,MAAM,IAAI,MAAM;gBACpB;YACJ;YACA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kCAAkC;YAC5D,IAAI,CAAC,cAAc,CAAC,IAAI,2BAA2B;YACnD,IAAI,CAAC,0BAA0B;QACnC;QAEA,IAAI,gCAAgC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc;QAC/E,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,iCAAiC,IAAI,gCAAgC;YACrE,iCAAiC,GAAG,2CAA2C,MAAK,IAAI,CAAC,qBAAqB;QAClH;QAEA,IAAI,aAAa,wBAAwB;QAEzC,IAAI,aAAa,GAAG;YAChB,0EAA0E;YAC1E,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,IAAI,mBAAmB,GAAG;YAClE,yEAAyE;YACzE,kBAAkB;YACtB,OAAO;gBACH,uDAAuD;gBACvD,kEAAkE;gBAClE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;YACvB;QACJ,OAAO,IAAI,aAAa,GAAG;YACvB,MAAM,IAAI,MAAM,4BAA4B,KAAK,GAAG,CAAC,cAAc;QACvE;IACJ;IACA,eAAe,SAAS,IAAI;QACxB,IAAI,CAAC,MAAM,GAAG,UAAU;IAC5B;IACA;;;KAGC,GACD,MAAM,SAAS,IAAI;QACf,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,cAAc;IACvB;AACJ;AACA,wBAAwB;AACxB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/load.js"], "sourcesContent": ["\"use strict\";\nvar utils = require(\"./utils\");\nvar external = require(\"./external\");\nvar utf8 = require(\"./utf8\");\nvar ZipEntries = require(\"./zipEntries\");\nvar Crc32Probe = require(\"./stream/Crc32Probe\");\nvar nodejsUtils = require(\"./nodejsUtils\");\n\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */\nfunction checkEntryCRC32(zipEntry) {\n    return new external.Promise(function (resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function (e) {\n            reject(e);\n        })\n            .on(\"end\", function () {\n                if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                    reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n                } else {\n                    resolve();\n                }\n            })\n            .resume();\n    });\n}\n\nmodule.exports = function (data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64)\n        .then(function (data) {\n            var zipEntries = new ZipEntries(options);\n            zipEntries.load(data);\n            return zipEntries;\n        }).then(function checkCRC32(zipEntries) {\n            var promises = [external.Promise.resolve(zipEntries)];\n            var files = zipEntries.files;\n            if (options.checkCRC32) {\n                for (var i = 0; i < files.length; i++) {\n                    promises.push(checkEntryCRC32(files[i]));\n                }\n            }\n            return external.Promise.all(promises);\n        }).then(function addFiles(results) {\n            var zipEntries = results.shift();\n            var files = zipEntries.files;\n            for (var i = 0; i < files.length; i++) {\n                var input = files[i];\n\n                var unsafeName = input.fileNameStr;\n                var safeName = utils.resolve(input.fileNameStr);\n\n                zip.file(safeName, input.decompressed, {\n                    binary: true,\n                    optimizedBinaryString: true,\n                    date: input.date,\n                    dir: input.dir,\n                    comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                    unixPermissions: input.unixPermissions,\n                    dosPermissions: input.dosPermissions,\n                    createFolders: options.createFolders\n                });\n                if (!input.dir) {\n                    zip.file(safeName).unsafeOriginalName = unsafeName;\n                }\n            }\n            if (zipEntries.zipComment.length) {\n                zip.comment = zipEntries.zipComment;\n            }\n\n            return zip;\n        });\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;;CAIC,GACD,SAAS,gBAAgB,QAAQ;IAC7B,OAAO,IAAI,SAAS,OAAO,CAAC,SAAU,OAAO,EAAE,MAAM;QACjD,IAAI,SAAS,SAAS,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI;QAC/D,OAAO,EAAE,CAAC,SAAS,SAAU,CAAC;YAC1B,OAAO;QACX,GACK,EAAE,CAAC,OAAO;YACP,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,SAAS,YAAY,CAAC,KAAK,EAAE;gBACzD,OAAO,IAAI,MAAM;YACrB,OAAO;gBACH;YACJ;QACJ,GACC,MAAM;IACf;AACJ;AAEA,OAAO,OAAO,GAAG,SAAU,IAAI,EAAE,OAAO;IACpC,IAAI,MAAM,IAAI;IACd,UAAU,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG;QAClC,QAAQ;QACR,YAAY;QACZ,uBAAuB;QACvB,eAAe;QACf,gBAAgB,KAAK,UAAU;IACnC;IAEA,IAAI,YAAY,MAAM,IAAI,YAAY,QAAQ,CAAC,OAAO;QAClD,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM;IAC7C;IAEA,OAAO,MAAM,cAAc,CAAC,uBAAuB,MAAM,MAAM,QAAQ,qBAAqB,EAAE,QAAQ,MAAM,EACvG,IAAI,CAAC,SAAU,IAAI;QAChB,IAAI,aAAa,IAAI,WAAW;QAChC,WAAW,IAAI,CAAC;QAChB,OAAO;IACX,GAAG,IAAI,CAAC,SAAS,WAAW,UAAU;QAClC,IAAI,WAAW;YAAC,SAAS,OAAO,CAAC,OAAO,CAAC;SAAY;QACrD,IAAI,QAAQ,WAAW,KAAK;QAC5B,IAAI,QAAQ,UAAU,EAAE;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,SAAS,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE;YAC1C;QACJ;QACA,OAAO,SAAS,OAAO,CAAC,GAAG,CAAC;IAChC,GAAG,IAAI,CAAC,SAAS,SAAS,OAAO;QAC7B,IAAI,aAAa,QAAQ,KAAK;QAC9B,IAAI,QAAQ,WAAW,KAAK;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,QAAQ,KAAK,CAAC,EAAE;YAEpB,IAAI,aAAa,MAAM,WAAW;YAClC,IAAI,WAAW,MAAM,OAAO,CAAC,MAAM,WAAW;YAE9C,IAAI,IAAI,CAAC,UAAU,MAAM,YAAY,EAAE;gBACnC,QAAQ;gBACR,uBAAuB;gBACvB,MAAM,MAAM,IAAI;gBAChB,KAAK,MAAM,GAAG;gBACd,SAAS,MAAM,cAAc,CAAC,MAAM,GAAG,MAAM,cAAc,GAAG;gBAC9D,iBAAiB,MAAM,eAAe;gBACtC,gBAAgB,MAAM,cAAc;gBACpC,eAAe,QAAQ,aAAa;YACxC;YACA,IAAI,CAAC,MAAM,GAAG,EAAE;gBACZ,IAAI,IAAI,CAAC,UAAU,kBAAkB,GAAG;YAC5C;QACJ;QACA,IAAI,WAAW,UAAU,CAAC,MAAM,EAAE;YAC9B,IAAI,OAAO,GAAG,WAAW,UAAU;QACvC;QAEA,OAAO;IACX;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/jszip/lib/index.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * Representation a of zip file in js\n * @constructor\n */\nfunction JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if(!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n\n    if(arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n\n    this.comment = null;\n\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for (var i in this) {\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = require(\"./object\");\nJSZip.prototype.loadAsync = require(\"./load\");\nJSZip.support = require(\"./support\");\nJSZip.defaults = require(\"./defaults\");\n\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\n\nJSZip.loadAsync = function (content, options) {\n    return new JSZip().loadAsync(content, options);\n};\n\nJSZip.external = require(\"./external\");\nmodule.exports = JSZip;\n"], "names": [], "mappings": "AAAA;AAEA;;;CAGC,GACD,SAAS;IACL,0EAA0E;IAC1E,IAAG,CAAC,CAAC,IAAI,YAAY,KAAK,GAAG;QACzB,OAAO,IAAI;IACf;IAEA,IAAG,UAAU,MAAM,EAAE;QACjB,MAAM,IAAI,MAAM;IACpB;IAEA,gCAAgC;IAChC,IAAI;IACJ,uBAAuB;IACvB,8BAA8B;IAC9B,IAAI;IACJ,kDAAkD;IAClD,wDAAwD;IACxD,0DAA0D;IAC1D,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC;IAE3B,IAAI,CAAC,OAAO,GAAG;IAEf,gCAAgC;IAChC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;QACT,IAAI,SAAS,IAAI;QACjB,IAAK,IAAI,KAAK,IAAI,CAAE;YAChB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY;gBAC/B,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;YACvB;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,SAAS;AACf,MAAM,SAAS,CAAC,SAAS;AACzB,MAAM,OAAO;AACb,MAAM,QAAQ;AAEd,iDAAiD;AACjD,wEAAwE;AACxE,MAAM,OAAO,GAAG;AAEhB,MAAM,SAAS,GAAG,SAAU,OAAO,EAAE,OAAO;IACxC,OAAO,IAAI,QAAQ,SAAS,CAAC,SAAS;AAC1C;AAEA,MAAM,QAAQ;AACd,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}