# 小说转视频AI生成器需求文档

## 项目概述

开发一个网页版的小说转视频AI生成器，通过AI技术将小说文本转换为结构化的视频内容。系统支持小说上传、角色分析、剧集拆分、剧情信息提取和视频生成等核心功能。

## 核心功能需求

### 1. 大模型配置功能
- **功能描述**: 系统核心驱动，所有AI分析和生成功能都依赖大模型
- **默认模型**: DeepSeek
- **配置选项**:
  - 模型选择（DeepSeek、通义千问、通义万相、MiniMax海螺、智谱AI等）
  - API密钥配置
  - 模型选项：
    - DeepSeek系列：DeepSeek chat、DeepSeek r1、DeepSeek v3等
    - 通义系列：通义千问-Turbo-Latest、wanx2.0-t2i turbo
    - MiniMax系列：Hailuo 02（专业视频生成模型）
    - 智谱AI系列：CogView-3-Flash（快速图像生成）、CogView-4（高质量图像生成）
- **界面要求**:
  - 设置页面或配置面板
  - 连接状态检测
  - 模型切换功能
- **重要性**: 大模型是整个系统的核心驱动力，负责：
  - 角色信息提取
  - 剧集智能拆分
  - 剧情信息分析提取
  - 角色形象生成（通义万相）
  - 生成视频脚本
  - 驱动生成视频
- **增强功能**: 所有AI按钮支持增强提示词功能，用户可自定义优化AI效果

### 2. 文件上传功能
- **功能描述**: 用户可以上传小说文件
- **支持格式**: txt、doc、docx等文本格式
- **界面要求**: 
  - 拖拽上传区域
  - 上传进度显示
  - 文件预览功能


### 3. 自动分析人物剧情功能
- **触发方式**: 上传完成后显示"自动分析人物剧情"按钮
- **AI驱动**: 使用配置的大模型（默认DeepSeek）进行智能分析
- **处理流程**:
  1. 调用大模型API
  2. 传入完整小说文本
  3. 使用专门的提示词进行分析
  4. 返回结构化数据
- **功能包含两个子任务**:

#### 3.1 角色信息提取
- **提取内容**:
  - 人物名称
  - 角色信息（五官、身份、外貌、性格、隐线）
- **存储位置**: 角色一栏
- **展示方式**: 结构化列表展示所有角色及其详细信息
- **新增功能**: AI一键生成角色形象
  - 基于提取的角色信息生成正、侧、背三视图
  - 智能提示词生成器，确保形象符合小说描述
  - 支持提示词预览和自定义优化
  - 支持多种图像生成模型：通义万相、智谱AI CogView系列

#### 3.2 剧集拆分
- **拆分规则**: 按原小说章节划分
- **存储格式**:
  - 故事标题（一级目录）
  - 详细剧情（二级目录）
- **存储位置**: 剧集一栏
- **排序**: 按原章节顺序排列

### 4. 剧集管理功能
- **剧集列表**: 显示所有故事按原顺序排列
- **故事查看**: 点击每个故事可查看详细剧情
- **层级结构**: 
  - 一级：故事标题
  - 二级：详细剧情内容

### 5. 剧情信息提取功能
- **触发方式**: 在每个故事详情页面有"提取剧情信息"按钮
- **AI驱动**: 使用配置的大模型（默认DeepSeek）对单个剧集进行深度分析
- **提取内容**: 三大信息维度
  
#### 5.1 本集人物
- 当前剧集中出场的所有角色
- 关联角色库中的详细信息

#### 5.2 场景信息
- 故事发生的地点
- 环境描述
- 氛围设定

#### 5.3 事件三要素
- **结构**: 正常 → 矛盾冲突 → 升级事件
- **定义**: 哪几个人在什么地方做什么
- **分析维度**:
  - 参与人物
  - 发生地点
  - 具体行为/事件

### 6. 视频生成功能
- **触发位置**: 第一集后面的"生成视频"按钮
- **AI驱动**: 智能选择最适合的模型进行视频生成
  - 优先使用MiniMax Hailuo 02（专业视频生成模型）
  - 备选其他配置的大模型生成视频脚本
- **生成依据**: 多维度信息整合
  - 角色信息（外貌、性格等）
  - 场景信息
  - 事件三要素
- **生成单位**: 以一集为单位进行视频生成
- **AI创作流程**:
  1. 整合多维度信息
  2. 构建专业视频生成提示词
  3. 调用MiniMax视频生成API
  4. 返回视频文件或生成任务ID
- **支持增强提示词**: 用户可自定义优化视频生成效果
- **新增功能**: 视频一致性管理
  - 角色DNA系统：确保同一角色在不同场景中保持外貌一致性
  - 场景模板库：确保同一场景在不同时间线上保持环境一致性
  - 剧情视频提示词生成器：专门优化小说剧情的视频生成效果
  - 一致性检查工具：帮助用户管理和验证视频的连贯性

## 系统架构设计

### 前端界面布局
```
┌─────────────────────────────────────┐
│           小说转视频AI生成器          │
├─────────────────────────────────────┤
│  大模型配置区域                      │
│  [DeepSeek ▼] [API密钥] [连接测试]   │
├─────────────────────────────────────┤
│  文件上传区域                        │
│  [拖拽上传] [自动分析人物剧情]        │
├─────────────────┬───────────────────┤
│   角色一栏       │    剧集一栏        │
│                │                   │
│ • 角色A         │ 1. 第一章标题      │
│   - 五官描述    │    - 详细剧情      │
│   - 身份信息    │    [提取剧情信息]   │
│   - 外貌特征    │    [生成视频]      │
│   - 性格特点    │                   │
│   - 隐线伏笔    │ 2. 第二章标题      │
│                │    - 详细剧情      │
│ • 角色B         │    [提取剧情信息]   │
│   - ...        │                   │
└─────────────────┴───────────────────┘
```

### 数据结构设计

#### 大模型配置数据结构
```json
{
  "aiConfig": {
    "provider": "deepseek",
    "apiKey": "sk-xxx",
    "model": "deepseek-chat",
    "parameters": {
      "temperature": 0.7,
      "maxTokens": 4000,
      "topP": 0.9
    },
    "prompts": {
      "characterExtraction": "请分析以下小说文本，提取所有角色信息...",
      "episodeSplit": "请将以下小说按章节拆分...",
      "plotAnalysis": "请分析以下剧集的三大信息..."
    },
    "status": "connected",
    "lastTest": "2024-01-01T00:00:00Z"
  }
}
```

#### 角色数据结构
```json
{
  "characters": [
    {
      "id": "char_001",
      "name": "角色名称",
      "appearance": {
        "face": "五官描述",
        "body": "外貌特征",
        "clothing": "服装描述"
      },
      "identity": "身份信息",
      "personality": "性格特点",
      "hiddenLines": "隐线伏笔"
    }
  ]
}
```

#### 剧集数据结构
```json
{
  "episodes": [
    {
      "id": "ep_001",
      "title": "故事标题",
      "content": "详细剧情",
      "order": 1,
      "plotInfo": {
        "characters": ["角色ID列表"],
        "scenes": [
          {
            "location": "场景地点",
            "description": "环境描述",
            "atmosphere": "氛围设定"
          }
        ],
        "events": [
          {
            "normal": "正常状态描述",
            "conflict": "矛盾冲突描述",
            "escalation": "升级事件描述",
            "participants": ["参与角色"],
            "location": "发生地点",
            "actions": ["具体行为"]
          }
        ]
      }
    }
  ]
}
```

## 技术实现要求

### AI模型集成（核心）
- **主要模型**: DeepSeek作为默认大语言模型
  - 优势：中文理解能力强，性价比高，API稳定
  - 备选：GPT-4、Claude、文心一言等
- **文本分析**: 使用大语言模型进行文本理解和信息提取
- **角色识别**: 基于大模型的NLP技术识别人物及其属性
- **情节分析**: 智能分析故事结构和事件关系
- **视频生成**: 集成视频生成AI模型（如Runway、Stable Video等）
- **API管理**:
  - 支持多种大模型提供商
  - API密钥安全存储
  - 请求限流和错误重试

### 用户体验要求
- **响应式设计**: 适配不同屏幕尺寸
- **加载状态**: 所有AI处理过程显示进度
- **错误处理**: 友好的错误提示和重试机制
- **数据持久化**: 用户数据本地存储或云端保存

### 性能要求
- **文件上传**: 支持大文件上传（最大50MB）
- **处理速度**: AI分析结果在合理时间内返回
- **并发处理**: 支持多个剧集同时分析
- **缓存机制**: 已分析内容缓存避免重复处理

## 开发优先级

### 第一阶段（基础架构）
1. 大模型配置功能（DeepSeek集成）
2. 文件上传和预览
3. 基础界面布局（大模型配置区、角色栏、剧集栏）
4. 模拟数据展示

### 第二阶段（AI集成）
1. 角色信息提取功能
2. 剧集自动拆分功能
3. 剧情信息提取功能

### 第三阶段（视频生成）
1. 视频生成接口集成
2. 多维度信息整合
3. 生成结果展示

### 第四阶段（优化完善）
1. 用户体验优化
2. 性能优化
3. 错误处理完善

## 验收标准

1. **功能完整性**: 所有描述的功能都能正常工作
2. **数据准确性**: AI提取的信息准确度达到可用标准
3. **界面友好性**: 用户操作流程清晰直观
4. **系统稳定性**: 处理各种异常情况不崩溃
5. **性能表现**: 响应时间在用户可接受范围内

## 后续扩展计划

1. **批量处理**: 支持多本小说同时处理
2. **模板定制**: 用户可自定义视频风格模板
3. **协作功能**: 多用户协作编辑和审核
4. **导出功能**: 支持多种格式的内容导出
5. **API接口**: 提供开放API供第三方集成
