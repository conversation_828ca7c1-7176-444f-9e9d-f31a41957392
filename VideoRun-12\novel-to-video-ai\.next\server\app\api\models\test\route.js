/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/models/test/route";
exports.ids = ["app/api/models/test/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Ftest%2Froute&page=%2Fapi%2Fmodels%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Ftest%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Ftest%2Froute&page=%2Fapi%2Fmodels%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Ftest%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_models_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/models/test/route.ts */ \"(rsc)/./src/app/api/models/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/models/test/route\",\n        pathname: \"/api/models/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/models/test/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\models\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_models_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Ftest%2Froute&page=%2Fapi%2Fmodels%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Ftest%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/models/test/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/models/test/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n// 测试模型连接\nasync function testModelConnection(config) {\n    try {\n        // 根据不同的提供商进行测试\n        switch(config.provider){\n            case 'deepseek':\n                return await testDeepSeekConnection(config);\n            case 'tongyi':\n                return await testTongyiConnection(config);\n            case 'minimax':\n                return await testMinimaxConnection(config);\n            case 'zhipu':\n                return await testZhipuConnection(config);\n            case 'openai':\n                return await testOpenAIConnection(config);\n            case 'claude':\n                return await testClaudeConnection(config);\n            case 'doubao':\n                return await testDoubaoConnection(config);\n            case 'skyreels':\n                return await testSkyReelsConnection(config);\n            default:\n                return {\n                    success: false,\n                    error: '不支持的模型提供商'\n                };\n        }\n    } catch (error) {\n        console.error('测试模型连接失败:', error);\n        return {\n            success: false,\n            error: '连接测试失败'\n        };\n    }\n}\n// 测试DeepSeek连接\nasync function testDeepSeekConnection(config) {\n    try {\n        const response = await fetch('https://api.deepseek.com/v1/chat/completions', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${config.apiKey}`\n            },\n            body: JSON.stringify({\n                model: config.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: '测试连接'\n                    }\n                ],\n                max_tokens: 10\n            })\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const error = await response.text();\n            return {\n                success: false,\n                error: `DeepSeek API错误: ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: `DeepSeek连接失败: ${error}`\n        };\n    }\n}\n// 测试通义连接\nasync function testTongyiConnection(config) {\n    try {\n        // 通义的API测试逻辑\n        // 支持通义千问和通义万相模型\n        let requestBody = {\n            model: config.model,\n            input: {\n                messages: [\n                    {\n                        role: 'user',\n                        content: '测试连接'\n                    }\n                ]\n            },\n            parameters: {\n                max_tokens: 10\n            }\n        };\n        // 对于turbo模型，添加result_format参数\n        if (config.model.includes('turbo')) {\n            requestBody.parameters.result_format = 'message';\n        }\n        console.log(`🔍 测试通义API连接: ${config.model}`);\n        console.log(`🔑 API密钥前缀: ${config.apiKey.substring(0, 8)}...`);\n        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${config.apiKey}`,\n                'X-DashScope-SSE': 'disable'\n            },\n            body: JSON.stringify(requestBody)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            // 检查响应中的错误码\n            if (data.code && data.code !== 'Success') {\n                if (data.code === 'Arrearage') {\n                    return {\n                        success: false,\n                        error: '通义API账户欠费，请前往阿里云控制台充值后重试'\n                    };\n                } else if (data.code === 'InvalidApiKey') {\n                    return {\n                        success: false,\n                        error: '通义API密钥无效，请检查配置'\n                    };\n                } else {\n                    return {\n                        success: false,\n                        error: `通义API错误: ${data.message || data.code}`\n                    };\n                }\n            }\n            return {\n                success: true\n            };\n        } else {\n            const errorText = await response.text();\n            console.error(`❌ 通义API测试失败 (${response.status}):`, errorText);\n            let errorData = {};\n            try {\n                errorData = JSON.parse(errorText);\n            } catch (e) {\n                // 如果不是JSON格式，检查是否包含url error\n                if (errorText.includes('url error')) {\n                    return {\n                        success: false,\n                        error: '通义API URL错误，请检查模型名称是否正确'\n                    };\n                }\n                return {\n                    success: false,\n                    error: `通义API错误: ${errorText || response.statusText}`\n                };\n            }\n            if (errorData.code === 'Arrearage') {\n                return {\n                    success: false,\n                    error: '通义API账户欠费，请前往阿里云控制台充值后重试'\n                };\n            } else if (errorData.code === 'InvalidApiKey') {\n                return {\n                    success: false,\n                    error: '通义API密钥无效，请检查配置'\n                };\n            } else if (errorData.message && errorData.message.includes('url error')) {\n                return {\n                    success: false,\n                    error: '通义API URL错误，请检查模型名称是否正确'\n                };\n            } else {\n                return {\n                    success: false,\n                    error: `通义API错误: ${errorData.message || response.statusText}`\n                };\n            }\n        }\n    } catch (error) {\n        console.error('通义API测试异常:', error);\n        return {\n            success: false,\n            error: `通义连接失败: ${error}`\n        };\n    }\n}\n// 测试OpenAI连接\nasync function testOpenAIConnection(config) {\n    try {\n        const response = await fetch('https://api.openai.com/v1/chat/completions', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${config.apiKey}`\n            },\n            body: JSON.stringify({\n                model: config.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: '测试连接'\n                    }\n                ],\n                max_tokens: 10\n            })\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const error = await response.text();\n            return {\n                success: false,\n                error: `OpenAI API错误: ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: `OpenAI连接失败: ${error}`\n        };\n    }\n}\n// 测试MiniMax连接\nasync function testMinimaxConnection(config) {\n    try {\n        // MiniMax Hailuo视频生成API测试\n        const response = await fetch('https://api.minimaxi.com/v1/video_generation', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${config.apiKey}`\n            },\n            body: JSON.stringify({\n                model: config.model,\n                prompt: '测试连接',\n                // 添加基本参数\n                video_setting: {\n                    video_duration: 6,\n                    video_aspect_ratio: '16:9'\n                }\n            })\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const error = await response.text();\n            return {\n                success: false,\n                error: `MiniMax API错误: ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: `MiniMax连接失败: ${error}`\n        };\n    }\n}\n// 测试智谱AI连接\nasync function testZhipuConnection(config) {\n    try {\n        // 智谱AI CogView图像生成API测试\n        const response = await fetch('https://open.bigmodel.cn/api/paas/v4/images/generations', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${config.apiKey}`\n            },\n            body: JSON.stringify({\n                model: config.model,\n                prompt: '测试连接',\n                size: '1024x1024',\n                quality: 'standard',\n                n: 1\n            })\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const error = await response.text();\n            return {\n                success: false,\n                error: `智谱AI API错误: ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: `智谱AI连接失败: ${error}`\n        };\n    }\n}\n// 测试Claude连接\nasync function testClaudeConnection(config) {\n    try {\n        const response = await fetch('https://api.anthropic.com/v1/messages', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'x-api-key': config.apiKey,\n                'anthropic-version': '2023-06-01'\n            },\n            body: JSON.stringify({\n                model: config.model,\n                max_tokens: 10,\n                messages: [\n                    {\n                        role: 'user',\n                        content: '测试连接'\n                    }\n                ]\n            })\n        });\n        if (response.ok) {\n            return {\n                success: true\n            };\n        } else {\n            const error = await response.text();\n            return {\n                success: false,\n                error: `Claude API错误: ${error}`\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: `Claude连接失败: ${error}`\n        };\n    }\n}\n// 测试豆包连接（带指数退避重试机制）\nasync function testDoubaoConnection(config) {\n    const maxRetries = 5 // 增加重试次数\n    ;\n    const baseDelay = 1000 // 基础延迟1秒\n    ;\n    console.log(`🔍 测试豆包API连接: ${config.model}`);\n    console.log(`🔑 API密钥前缀: ${config.apiKey.substring(0, 8)}...`);\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000) // 30秒超时\n        ;\n        try {\n            if (attempt > 1) {\n                // 指数退避算法：1s, 2s, 4s, 8s, 16s\n                const delay = baseDelay * Math.pow(2, attempt - 2);\n                console.log(`🔄 第${attempt}次重试（延迟${delay}ms）...`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n            }\n            // 豆包模型类型检测 - 根据endpoint ID映射到正确的模型名称\n            let actualModelName = config.model;\n            let isImageToVideoModel = false;\n            let isTextToVideoModel = false;\n            let isVideoModel = false;\n            let isImageModel = false;\n            // 豆包视频模型直接使用endpoint ID，不需要映射\n            if (config.model === 'ep-20250624195026-qjsmk') {\n                // I2V模型 - 直接使用endpoint ID\n                actualModelName = config.model;\n                isImageToVideoModel = true;\n                isVideoModel = true;\n            } else if (config.model === 'ep-20250624192345-5ccwj') {\n                // T2V模型 - 直接使用endpoint ID\n                actualModelName = config.model;\n                isTextToVideoModel = true;\n                isVideoModel = true;\n            } else if (config.model === 'ep-20250624192235-zttm6') {\n                // Pro模型 - 直接使用endpoint ID\n                actualModelName = config.model;\n                isTextToVideoModel = true;\n                isVideoModel = true;\n            } else if (config.model === 'ep-20250623162000-p9zzw') {\n                // 图像生成模型\n                actualModelName = 'doubao-seedream-3-0-t2i-250415';\n                isImageModel = true;\n            } else {\n                // 其他模型保持原名\n                isVideoModel = config.model.includes('seedance') && !config.model.includes('t2i');\n                isImageModel = config.model.includes('seedream') || config.model.includes('t2i');\n                // 如果是其他豆包模型但不是视频/图像模型，可能是聊天模型\n                // 但根据诊断，当前API密钥可能没有聊天权限，所以跳过测试\n                if (!isVideoModel && !isImageModel && config.model.startsWith('ep-')) {\n                    console.log('🎯 检测到豆包endpoint但非视频/图像模型，跳过测试');\n                    return {\n                        success: true\n                    };\n                }\n            }\n            let apiUrl;\n            let requestBody;\n            if (isVideoModel) {\n                // 视频生成API\n                apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks';\n                if (isImageToVideoModel) {\n                    // 图生视频模型测试：跳过实际API调用，直接返回成功\n                    // 因为图生视频需要有效的图像URL，在测试连接时很难提供\n                    console.log('🎯 检测到图生视频模型，跳过API调用测试');\n                    return {\n                        success: true\n                    };\n                } else {\n                    // 文生视频模型只需要文本\n                    requestBody = {\n                        model: actualModelName,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: \"测试连接\"\n                            }\n                        ]\n                    };\n                }\n            } else if (isImageModel) {\n                // 图像生成API\n                apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/images/generations';\n                requestBody = {\n                    model: actualModelName,\n                    prompt: \"测试连接\",\n                    response_format: \"url\",\n                    size: \"1024x1024\",\n                    guidance_scale: 3,\n                    watermark: true\n                };\n            } else {\n                // 文本生成API\n                apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';\n                requestBody = {\n                    model: actualModelName,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: '测试连接'\n                        }\n                    ],\n                    max_tokens: 10,\n                    temperature: 0.3\n                };\n            }\n            const response = await fetch(apiUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${config.apiKey}`\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                console.log(`✅ 豆包API连接测试成功（第${attempt}次尝试）`);\n                return {\n                    success: true\n                };\n            } else {\n                const errorText = await response.text();\n                console.error(`❌ 豆包API测试失败 (${response.status}):`, errorText);\n                let errorData = {};\n                let errorMessage = '';\n                try {\n                    errorData = JSON.parse(errorText);\n                    errorMessage = errorData.error?.message || response.statusText;\n                } catch (e) {\n                    errorMessage = errorText || response.statusText;\n                }\n                // 检查是否是可重试的错误\n                const isRetryableError = errorMessage.includes('internal error') || errorMessage.includes('service unavailable') || errorMessage.includes('timeout') || response.status >= 500;\n                if (isRetryableError && attempt < maxRetries) {\n                    console.log(`🔄 检测到可重试错误: ${errorMessage}`);\n                    continue; // 继续下一次重试\n                }\n                // 处理特定的错误类型（不可重试）\n                if (errorMessage.includes('API key') || errorMessage.includes('invalid')) {\n                    return {\n                        success: false,\n                        error: '豆包API密钥无效，请检查配置'\n                    };\n                } else if (errorMessage.includes('rate limit')) {\n                    return {\n                        success: false,\n                        error: '豆包API调用频率超限，请稍后重试'\n                    };\n                } else if (errorMessage.includes('quota') || errorMessage.includes('insufficient')) {\n                    return {\n                        success: false,\n                        error: '豆包API配额不足，请检查账户余额'\n                    };\n                } else if (errorMessage.includes('does not exist') || errorMessage.includes('not found')) {\n                    return {\n                        success: false,\n                        error: '豆包模型不存在或无权访问，请在火山方舟控制台开通权限'\n                    };\n                } else if (errorMessage.includes('internal error')) {\n                    return {\n                        success: false,\n                        error: '豆包服务临时不可用（内部错误），请稍后重试或使用其他AI模型'\n                    };\n                } else {\n                    return {\n                        success: false,\n                        error: `豆包API错误: ${errorMessage}`\n                    };\n                }\n            }\n        } catch (error) {\n            clearTimeout(timeoutId);\n            console.error(`豆包API测试异常 (尝试 ${attempt}/${maxRetries}):`, error);\n            if (error.name === 'AbortError') {\n                if (attempt < maxRetries) {\n                    console.log('🔄 连接超时，重试中...');\n                    continue;\n                }\n                return {\n                    success: false,\n                    error: '豆包API连接超时，请检查网络连接'\n                };\n            }\n            // 网络错误等可以重试\n            if (attempt < maxRetries) {\n                console.log(`🔄 网络错误，准备重试...`);\n                continue;\n            }\n            return {\n                success: false,\n                error: `豆包连接失败: ${error}`\n            };\n        }\n    }\n    return {\n        success: false,\n        error: '豆包连接失败，已达到最大重试次数。建议稍后重试或使用其他AI模型。'\n    };\n}\n// 测试SkyReels连接\nasync function testSkyReelsConnection(config) {\n    try {\n        console.log(`🔍 测试SkyReels API连接: ${config.model}`);\n        console.log(`🔗 API服务器地址: ${config.apiKey}`);\n        // SkyReels的apiKey实际上是服务器地址\n        const baseUrl = config.apiKey || 'http://localhost:8000';\n        const response = await fetch(`${baseUrl}/health`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            // 设置较短的超时时间，因为是本地服务\n            signal: AbortSignal.timeout(10000) // 10秒超时\n        });\n        if (response.ok) {\n            const data = await response.json();\n            console.log('✅ SkyReels健康检查响应:', data);\n            // 检查模型是否已加载\n            if (data.status === 'healthy' && data.model_loaded) {\n                return {\n                    success: true\n                };\n            } else if (data.status === 'healthy' && !data.model_loaded) {\n                return {\n                    success: false,\n                    error: 'SkyReels服务器运行正常，但模型尚未加载完成，请稍后重试'\n                };\n            } else {\n                return {\n                    success: false,\n                    error: `SkyReels服务器状态异常: ${data.status}`\n                };\n            }\n        } else {\n            const errorText = await response.text();\n            return {\n                success: false,\n                error: `SkyReels API错误 (${response.status}): ${errorText}`\n            };\n        }\n    } catch (error) {\n        console.error('SkyReels API测试异常:', error);\n        if (error.name === 'TimeoutError') {\n            return {\n                success: false,\n                error: 'SkyReels服务器连接超时，请检查服务器是否启动并运行在正确的端口'\n            };\n        } else if (error.message.includes('fetch')) {\n            return {\n                success: false,\n                error: 'SkyReels服务器连接失败，请确认服务器地址正确且服务器已启动'\n            };\n        } else {\n            return {\n                success: false,\n                error: `SkyReels连接失败: ${error.message}`\n            };\n        }\n    }\n}\n// POST - 测试模型连接\nasync function POST(request) {\n    try {\n        const modelConfig = await request.json();\n        // 验证必需字段\n        if (!modelConfig.provider || !modelConfig.model || !modelConfig.apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少必需字段'\n            }, {\n                status: 400\n            });\n        }\n        // 如果有ID，更新数据库中的测试状态\n        if (modelConfig.id) {\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.update({\n                where: {\n                    id: modelConfig.id\n                },\n                data: {\n                    status: 'testing'\n                }\n            });\n        }\n        // 测试连接\n        const testResult = await testModelConnection(modelConfig);\n        // 更新数据库中的状态\n        if (modelConfig.id) {\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.update({\n                where: {\n                    id: modelConfig.id\n                },\n                data: {\n                    status: testResult.success ? 'connected' : 'error',\n                    lastTest: new Date()\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: testResult.success,\n            error: testResult.error\n        });\n    } catch (error) {\n        console.error('测试模型连接失败:', error);\n        // 如果有ID，更新状态为错误\n        if (request.body) {\n            try {\n                const body = await request.json();\n                if (body.id) {\n                    await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.update({\n                        where: {\n                            id: body.id\n                        },\n                        data: {\n                            status: 'error'\n                        }\n                    });\n                }\n            } catch (e) {\n            // 忽略解析错误\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '测试连接失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/models/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPVxuICBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmodels%2Ftest%2Froute&page=%2Fapi%2Fmodels%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodels%2Ftest%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();