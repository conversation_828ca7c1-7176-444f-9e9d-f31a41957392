const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoLiteModels() {
  try {
    console.log('🎬 测试豆包 Seedance 1.0 Lite 模型集成...\n');
    
    // 1. 检查两个新模型是否已添加到数据库
    console.log('📋 检查豆包 Lite 模型配置...');
    
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    const t2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj'
      }
    });
    
    console.log('📊 模型配置状态:');
    
    if (i2vModel) {
      console.log('✅ 豆包 Seedance 1.0 Lite I2V (图生视频):');
      console.log(`   ID: ${i2vModel.id}`);
      console.log(`   名称: ${i2vModel.name}`);
      console.log(`   模型: ${i2vModel.model}`);
      console.log(`   启用状态: ${i2vModel.enabled}`);
      console.log(`   支持视频: ${i2vModel.supportsVideo}`);
      console.log(`   支持图生视频: ${i2vModel.supportsImageToVideo}`);
      console.log(`   API密钥: ${i2vModel.apiKey ? '已配置' : '未配置'}`);
    } else {
      console.log('❌ 未找到豆包 I2V 模型配置');
    }
    
    if (t2vModel) {
      console.log('✅ 豆包 Seedance 1.0 Lite T2V (文生视频):');
      console.log(`   ID: ${t2vModel.id}`);
      console.log(`   名称: ${t2vModel.name}`);
      console.log(`   模型: ${t2vModel.model}`);
      console.log(`   启用状态: ${t2vModel.enabled}`);
      console.log(`   支持视频: ${t2vModel.supportsVideo}`);
      console.log(`   支持图生视频: ${t2vModel.supportsImageToVideo}`);
      console.log(`   API密钥: ${t2vModel.apiKey ? '已配置' : '未配置'}`);
    } else {
      console.log('❌ 未找到豆包 T2V 模型配置');
    }
    
    if (!i2vModel && !t2vModel) {
      console.log('💡 请在AI配置页面添加这两个模型');
      return;
    }
    
    // 2. 检查角色参考图像
    console.log('\n🖼️ 检查角色参考图像...');
    
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 3
    });
    
    console.log(`📊 找到 ${charactersWithImages.length} 个有参考图像的角色:`);
    
    for (const character of charactersWithImages) {
      console.log(`   - ${character.name}: ${character.generatedImages ? '有图像' : '无图像'}`);
    }
    
    // 3. 智能模型选择测试
    console.log('\n🤖 测试智能模型选择...');
    
    const availableModels = [i2vModel, t2vModel].filter(Boolean);
    
    if (availableModels.length > 0) {
      // 模拟不同类型的片段
      const testSegments = [
        {
          type: 'environment',
          title: '雪夜雁门关环境建立',
          hasCharacterImages: false,
          characterCount: 0,
          sceneComplexity: 'medium',
          emotionalIntensity: 'low'
        },
        {
          type: 'character',
          title: '李四角色登场',
          hasCharacterImages: true,
          characterCount: 1,
          sceneComplexity: 'low',
          emotionalIntensity: 'medium'
        },
        {
          type: 'emotion',
          title: '张三检查虎符表情变化',
          hasCharacterImages: true,
          characterCount: 1,
          sceneComplexity: 'low',
          emotionalIntensity: 'high'
        },
        {
          type: 'action',
          title: '激烈战斗场面',
          hasCharacterImages: false,
          characterCount: 2,
          sceneComplexity: 'high',
          emotionalIntensity: 'high'
        }
      ];
      
      // 导入智能选择器（模拟）
      console.log('📋 模拟智能模型选择结果:');
      
      for (const segment of testSegments) {
        console.log(`\n🎬 片段: ${segment.title}`);
        console.log(`   类型: ${segment.type}`);
        console.log(`   角色图像: ${segment.hasCharacterImages ? '有' : '无'}`);
        console.log(`   场景复杂度: ${segment.sceneComplexity}`);
        console.log(`   情感强度: ${segment.emotionalIntensity}`);
        
        // 简化的选择逻辑
        let recommendedModel = null;
        let reason = '';
        
        if (segment.hasCharacterImages && i2vModel && i2vModel.enabled) {
          recommendedModel = i2vModel;
          reason = '有角色参考图像，推荐使用图生视频模型确保角色一致性';
        } else if (t2vModel && t2vModel.enabled) {
          recommendedModel = t2vModel;
          reason = '无角色参考图像或创意性场景，推荐使用文生视频模型';
        } else if (i2vModel && i2vModel.enabled) {
          recommendedModel = i2vModel;
          reason = '备选：图生视频模型';
        }
        
        if (recommendedModel) {
          console.log(`   ✅ 推荐模型: ${recommendedModel.name}`);
          console.log(`   📋 选择理由: ${reason}`);
        } else {
          console.log(`   ❌ 无可用模型`);
        }
      }
    }
    
    // 4. API调用格式对比
    console.log('\n📡 API调用格式对比:');
    
    console.log('豆包 Seedance 1.0 Lite T2V (文生视频):');
    console.log('   端点ID: ep-20250624013223-bwdtj');
    console.log('   输入格式: 纯文本提示词');
    console.log('   适用场景: 环境建立、创意场景、复杂动作');
    console.log('   优势: 创意自由度高，适应性强');
    
    console.log('\n豆包 Seedance 1.0 Lite I2V (图生视频):');
    console.log('   端点ID: ep-20250624013749-dbrbn');
    console.log('   输入格式: 参考图像 + 文本提示词');
    console.log('   适用场景: 角色特写、情感表达、角色动作');
    console.log('   优势: 角色一致性好，表情细腻');
    
    // 5. 使用建议
    console.log('\n💡 使用建议:');
    
    console.log('🎯 推荐使用策略:');
    console.log('   1. 环境建立片段 → T2V (文生视频)');
    console.log('   2. 角色登场片段 → I2V (图生视频)');
    console.log('   3. 情感表达片段 → I2V (图生视频)');
    console.log('   4. 动作场景片段 → 根据复杂度选择');
    console.log('   5. 对话互动片段 → I2V (图生视频)');
    console.log('   6. 悬念结尾片段 → T2V (文生视频)');
    
    console.log('\n🔄 混合使用优势:');
    console.log('   - 角色一致性: I2V确保主要角色外观统一');
    console.log('   - 创意多样性: T2V提供丰富的场景变化');
    console.log('   - 成本优化: 根据需求选择合适模型');
    console.log('   - 质量提升: 发挥各模型专长');
    
    // 6. 实际测试建议
    console.log('\n🧪 实际测试建议:');
    
    if (i2vModel && i2vModel.apiKey && i2vModel.enabled) {
      console.log('✅ I2V模型可以测试');
    } else {
      console.log('⚠️ I2V模型需要配置API密钥并启用');
    }
    
    if (t2vModel && t2vModel.apiKey && t2vModel.enabled) {
      console.log('✅ T2V模型可以测试');
    } else {
      console.log('⚠️ T2V模型需要配置API密钥并启用');
    }
    
    console.log('\n📋 测试步骤:');
    console.log('   1. 在AI配置页面配置两个模型的API密钥');
    console.log('   2. 启用两个模型');
    console.log('   3. 确保有角色参考图像');
    console.log('   4. 生成测试视频片段');
    console.log('   5. 观察智能模型选择效果');
    
    // 7. 预期效果
    console.log('\n🎯 预期效果:');
    console.log('   - 角色一致性显著提升');
    console.log('   - 场景多样性保持丰富');
    console.log('   - 智能模型选择减少人工干预');
    console.log('   - 整体视频质量提升');
    
    console.log('\n🎬 豆包 Seedance 1.0 Lite 模型集成测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoLiteModels();
