{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'\nimport Link from 'next/link'\nimport {\n  ArrowLeft,\n  Upload,\n  Users,\n  Film,\n  Video,\n  FileText,\n  Calendar,\n  AlertCircle,\n  CheckCircle,\n  Play,\n  BookOpen,\n  ChevronDown,\n  X\n} from 'lucide-react'\nimport ModelSelector from '@/components/ModelSelector'\nimport Layout from '@/components/Layout'\nimport ProjectFileUpload from '@/components/ProjectFileUpload'\nimport CharacterManager from '@/components/CharacterManager'\nimport EpisodeManager from '@/components/EpisodeManager'\nimport DetailedPlotExtraction from '@/components/DetailedPlotExtraction'\nimport VideoSegmentViewer from '@/components/VideoSegmentViewer'\nimport EpisodeVideoCard from '@/components/EpisodeVideoCard'\nimport ConsistencyVideoGenerator from '@/components/ConsistencyVideoGenerator'\nimport { Project, Character, Episode } from '@/types'\n\ntype TabType = 'upload' | 'novel' | 'characters' | 'episodes' | 'videos'\n\nexport default function ProjectDetailPage() {\n  const params = useParams()\n  const router = useRouter()\n  const searchParams = useSearchParams()\n  const projectId = params.id as string\n  \n  const [project, setProject] = useState<Project | null>(null)\n  const [characters, setCharacters] = useState<Character[]>([])\n  const [episodes, setEpisodes] = useState<Episode[]>([])\n\n  // 从URL参数中获取当前标签页，如果没有则默认为'upload'\n  const getInitialTab = (): TabType => {\n    const tab = searchParams.get('tab') as TabType\n    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']\n    return validTabs.includes(tab) ? tab : 'upload'\n  }\n\n  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab())\n  const [loading, setLoading] = useState(true)\n\n  // 更新标签页并同时更新URL\n  const updateActiveTab = (tab: TabType) => {\n    setActiveTab(tab)\n    const newSearchParams = new URLSearchParams(searchParams.toString())\n    newSearchParams.set('tab', tab)\n    router.replace(`/projects/${projectId}?${newSearchParams.toString()}`, { scroll: false })\n  }\n  const [error, setError] = useState<string | null>(null)\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [selectedModel, setSelectedModel] = useState<string>('')\n\n  // 增强提示词相关状态\n  const [showPromptDropdown, setShowPromptDropdown] = useState(false)\n  const [enhancePrompt, setEnhancePrompt] = useState('')\n  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')\n\n  // 详细剧情提取相关状态\n  const [showDetailedPlotExtraction, setShowDetailedPlotExtraction] = useState(false)\n  const [currentEpisodeId, setCurrentEpisodeId] = useState('')\n  const [currentEpisodeTitle, setCurrentEpisodeTitle] = useState('')\n  const [currentEpisodeContent, setCurrentEpisodeContent] = useState('')\n\n  // 视频片段查看器相关状态\n  const [showVideoSegmentViewer, setShowVideoSegmentViewer] = useState(false)\n  const [videoEpisodeId, setVideoEpisodeId] = useState('')\n\n  // 视频生成模式：'auto' | 'manual'，默认为手动模式\n  const [videoGenerationMode, setVideoGenerationMode] = useState<'auto' | 'manual'>('manual')\n\n  // 一致性视频生成器相关状态\n  const [showConsistencyVideoGenerator, setShowConsistencyVideoGenerator] = useState(false)\n  const [consistencyEpisodeId, setConsistencyEpisodeId] = useState('')\n  const [consistencyEpisodeTitle, setConsistencyEpisodeTitle] = useState('')\n\n  // 文件上传成功处理\n  const handleUploadSuccess = (updatedProject: any) => {\n    setProject(updatedProject.project)\n    setCharacters(updatedProject.characters || [])\n    setEpisodes(updatedProject.episodes || [])\n    updateActiveTab('characters')\n  }\n\n  // 文件上传错误处理\n  const handleUploadError = (errorMessage: string) => {\n    setError(errorMessage)\n  }\n\n  // 剧情分析处理\n  const handleAnalyzePlot = async (episodeId: string) => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/analyze-plot`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新剧集状态\n        setEpisodes(prev =>\n          prev.map(ep =>\n            ep.id === episodeId\n              ? { ...ep, plotInfo: data.data }\n              : ep\n          )\n        )\n      } else {\n        throw new Error(data.error || '剧情分析失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '剧情分析失败')\n    }\n  }\n\n  // 视频生成处理\n  const handleGenerateVideo = async (episodeId: string) => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/generate-video`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新剧集状态\n        setEpisodes(prev =>\n          prev.map(ep =>\n            ep.id === episodeId\n              ? { ...ep, status: 'video_generated' }\n              : ep\n          )\n        )\n        // 可以在这里显示视频脚本模态框\n      } else {\n        throw new Error(data.error || '视频生成失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '视频生成失败')\n    }\n  }\n\n  // 提取详细剧情信息处理\n  const handleExtractDetailedPlot = (episodeId: string) => {\n    const episode = episodes.find(ep => ep.id === episodeId)\n    if (episode) {\n      setCurrentEpisodeId(episodeId)\n      setCurrentEpisodeTitle(episode.title)\n      setCurrentEpisodeContent(episode.content)\n      setShowDetailedPlotExtraction(true)\n    }\n  }\n\n  // 打开一致性视频生成器\n  const handleOpenConsistencyVideoGenerator = (episodeId: string) => {\n    const episode = episodes.find(ep => ep.id === episodeId)\n    if (episode) {\n      setConsistencyEpisodeId(episodeId)\n      setConsistencyEpisodeTitle(episode.title)\n      setShowConsistencyVideoGenerator(true)\n    }\n  }\n\n  // 一致性视频生成成功处理\n  const handleConsistencyVideoGenerated = (videoData: any) => {\n    console.log('✅ 一致性视频生成成功:', videoData)\n    // 可以在这里更新UI状态或显示成功消息\n    alert(`一致性视频生成成功！\\n生成ID: ${videoData.generationId}\\n角色数量: ${videoData.consistencyInfo.characterCount}\\n参考图像: ${videoData.consistencyInfo.referenceImageCount} 个`)\n  }\n\n  // 生成剧情视频处理\n  const handleGenerateStoryVideo = async (episodeId: string, prompt: string, modelId?: string) => {\n    try {\n      console.log('🎬 开始生成剧情视频，episodeId:', episodeId)\n\n      // 首先检查是否已有视频生成记录\n      const checkResponse = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`)\n      const checkData = await checkResponse.json()\n\n      console.log('📊 检查现有片段结果:', checkData)\n\n      if (checkData.success && checkData.data && checkData.data.segments.length > 0) {\n        console.log(`⚠️ 发现现有片段 ${checkData.data.segments.length} 个，显示确认对话框`)\n\n        // 如果已有视频记录，显示确认对话框\n        const confirmed = confirm(\n          `检测到该剧集已有 ${checkData.data.segments.length} 个视频片段。\\n\\n` +\n          `重新生成将会：\\n` +\n          `• 删除所有现有的视频片段\\n` +\n          `• 停止正在进行的生成任务\\n` +\n          `• 重新开始生成新的视频片段\\n\\n` +\n          `确定要继续吗？`\n        )\n\n        console.log('👤 用户确认结果:', confirmed)\n\n        if (!confirmed) {\n          console.log('❌ 用户取消操作')\n          return // 用户取消操作\n        }\n\n        console.log('✅ 用户确认继续，开始重新生成')\n      } else {\n        console.log('✨ 没有现有片段，直接开始生成')\n      }\n\n      const response = await fetch('/api/ai/generate-story-video', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          episodeId,\n          prompt,\n          projectId,\n          modelId,\n          generationMode: videoGenerationMode // 传递生成模式\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 显示详细的生成结果\n        const message = data.data.message || '剧情视频片段创建完成！'\n        const modeMessage = videoGenerationMode === 'auto'\n          ? `共${data.data.totalSegments}个片段正在自动生成中...`\n          : `共创建了${data.data.totalSegments}个片段，请在\"剧情视频片段\"页面手动生成每个片段。\\n\\n💡 当前为手动生成模式，您可以选择性地生成需要的片段。`\n        alert(`${message}\\n\\n${modeMessage}`)\n        setShowDetailedPlotExtraction(false)\n\n        // 直接打开视频片段查看器，不切换标签页\n        // 这样用户可以继续在当前标签页查看剧情内容\n        setVideoEpisodeId(episodeId)\n        setShowVideoSegmentViewer(true)\n      } else {\n        // 处理角色图像先决条件错误\n        if (data.requirementType === 'character_images') {\n          const missingChars = data.missingCharacters?.join('、') || '某些角色'\n          const errorMessage = `❌ 视频生成失败\\n\\n${data.error}\\n\\n解决方案：\\n1. 点击\"角色管理\"标签页\\n2. 为 ${missingChars} 生成角色形象\\n3. 确保每个角色都有正面、侧面、背面三视图\\n4. 重新尝试生成视频`\n          alert(errorMessage)\n\n          // 自动切换到角色管理标签页\n          setActiveTab('characters')\n        } else {\n          throw new Error(data.error || '剧情视频生成失败')\n        }\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '剧情视频生成失败')\n    }\n  }\n\n  // 保存增强提示词\n  const handleSaveEnhancePrompt = () => {\n    setSavedEnhancePrompt(enhancePrompt)\n    setShowPromptDropdown(false)\n    // 保存到localStorage\n    const storageKey = `enhance_prompt_reanalyze_${projectId}`\n    localStorage.setItem(storageKey, enhancePrompt)\n  }\n\n  // 从localStorage加载增强提示词\n  useEffect(() => {\n    if (projectId) {\n      const storageKey = `enhance_prompt_reanalyze_${projectId}`\n      const savedPrompt = localStorage.getItem(storageKey)\n      if (savedPrompt) {\n        setSavedEnhancePrompt(savedPrompt)\n        setEnhancePrompt(savedPrompt)\n      }\n    }\n  }, [projectId])\n\n  // 手动分析人物剧情\n  const handleManualAnalyze = async () => {\n    if (!project?.content) {\n      setError('没有小说内容可供分析')\n      return\n    }\n\n    if (!selectedModel) {\n      setError('请先选择分析模型')\n      return\n    }\n\n    setIsAnalyzing(true)\n    setError(null)\n\n    try {\n      const response = await fetch(`/api/projects/${projectId}/analyze`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          modelId: selectedModel,\n          customPrompt: savedEnhancePrompt || undefined\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        // 更新项目数据\n        setProject(data.data.project)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n        // 切换到角色标签页\n        updateActiveTab('characters')\n      } else {\n        throw new Error(data.error || 'AI分析失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'AI分析失败')\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 监听URL参数变化，更新当前标签页\n  useEffect(() => {\n    const tab = searchParams.get('tab') as TabType\n    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']\n    if (tab && validTabs.includes(tab) && tab !== activeTab) {\n      setActiveTab(tab)\n    }\n  }, [searchParams, activeTab])\n\n  // 加载项目详情\n  useEffect(() => {\n    if (projectId) {\n      loadProjectDetail()\n    }\n  }, [projectId])\n\n  const loadProjectDetail = async () => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setProject(data.data)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n\n        // 只有在URL中没有指定标签页时，才根据项目状态设置默认标签页\n        if (!searchParams.get('tab')) {\n          if (data.data.status === 'created') {\n            updateActiveTab('upload')\n          } else if (data.data.status === 'uploaded') {\n            updateActiveTab('novel')\n          } else {\n            updateActiveTab('characters')\n          }\n        }\n      } else {\n        throw new Error(data.error || '加载项目失败')\n      }\n    } catch (error) {\n      setError(error instanceof Error ? error.message : '加载项目失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 重新加载项目数据的简化版本（用于角色管理器回调）\n  const loadProject = async () => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}`)\n      const data = await response.json()\n\n      if (data.success) {\n        setProject(data.data)\n        setCharacters(data.data.characters || [])\n        setEpisodes(data.data.episodes || [])\n      }\n    } catch (error) {\n      console.error('重新加载项目失败:', error)\n    }\n  }\n\n  // 获取状态显示\n  const getStatusDisplay = (status: string) => {\n    const statusMap = {\n      created: { text: '已创建', color: 'bg-gray-100 text-gray-800', icon: FileText },\n      uploaded: { text: '已上传', color: 'bg-blue-100 text-blue-800', icon: Upload },\n      analyzing: { text: '分析中', color: 'bg-yellow-100 text-yellow-800', icon: Play },\n      completed: { text: '已完成', color: 'bg-green-100 text-green-800', icon: CheckCircle }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.created\n  }\n\n  // 标签页配置\n  const tabs = [\n    {\n      id: 'upload' as TabType,\n      name: '上传文件',\n      icon: Upload,\n      description: '上传小说文件进行AI分析',\n      disabled: false\n    },\n    {\n      id: 'novel' as TabType,\n      name: '小说',\n      icon: BookOpen,\n      description: '查看已上传的小说内容',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'characters' as TabType,\n      name: '角色',\n      icon: Users,\n      description: '查看和管理提取的角色信息',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'episodes' as TabType,\n      name: '剧集',\n      icon: Film,\n      description: '查看和管理章节剧集',\n      disabled: !project || project.status === 'created'\n    },\n    {\n      id: 'videos' as TabType,\n      name: '视频',\n      icon: Video,\n      description: '生成和管理视频脚本',\n      disabled: !project || ['created', 'uploaded'].includes(project.status)\n    }\n  ]\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">加载项目中...</p>\n          </div>\n        </div>\n      </Layout>\n    )\n  }\n\n  if (error || !project) {\n    return (\n      <Layout>\n        <div className=\"text-center py-12\">\n          <AlertCircle className=\"mx-auto h-12 w-12 text-red-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">加载失败</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            {error || '项目不存在'}\n          </p>\n          <div className=\"mt-6\">\n            <Link\n              href=\"/projects\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700\"\n            >\n              <ArrowLeft className=\"mr-2\" size={16} />\n              返回项目列表\n            </Link>\n          </div>\n        </div>\n      </Layout>\n    )\n  }\n\n  const statusDisplay = getStatusDisplay(project.status)\n\n  return (\n    <Layout>\n      <div className=\"space-y-6\">\n        {/* 页面头部 */}\n        <div>\n          <Link\n            href=\"/projects\"\n            className=\"inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4\"\n          >\n            <ArrowLeft className=\"mr-2\" size={16} />\n            返回项目列表\n          </Link>\n          \n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">{project.name}</h1>\n              {project.description && (\n                <p className=\"mt-1 text-sm text-gray-600\">{project.description}</p>\n              )}\n              <div className=\"mt-2 flex items-center space-x-4\">\n                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>\n                  <statusDisplay.icon className=\"mr-1\" size={12} />\n                  {statusDisplay.text}\n                </span>\n                <div className=\"flex items-center text-sm text-gray-500\">\n                  <Calendar size={14} className=\"mr-1\" />\n                  创建于 {new Date(project.createdAt).toLocaleDateString('zh-CN')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 标签页导航 */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {tabs.map((tab) => {\n              const isActive = activeTab === tab.id\n              const isDisabled = tab.disabled\n              \n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => !isDisabled && updateActiveTab(tab.id)}\n                  disabled={isDisabled}\n                  className={`\n                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm\n                    ${isActive\n                      ? 'border-purple-500 text-purple-600'\n                      : isDisabled\n                      ? 'border-transparent text-gray-400 cursor-not-allowed'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }\n                  `}\n                >\n                  <tab.icon \n                    className={`mr-2 ${isActive ? 'text-purple-500' : isDisabled ? 'text-gray-400' : 'text-gray-400 group-hover:text-gray-500'}`} \n                    size={16} \n                  />\n                  {tab.name}\n                  {tab.id === 'characters' && characters.length > 0 && (\n                    <span className=\"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs\">\n                      {characters.length}\n                    </span>\n                  )}\n                  {tab.id === 'episodes' && episodes.length > 0 && (\n                    <span className=\"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs\">\n                      {episodes.length}\n                    </span>\n                  )}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 标签页内容 */}\n        <div className=\"mt-6\">\n          {activeTab === 'upload' && (\n            <ProjectFileUpload\n              projectId={projectId}\n              project={project}\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {activeTab === 'novel' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">小说内容</h3>\n                    <div className=\"text-sm text-gray-500\">\n                      字数：{project?.content?.length || 0} 字\n                    </div>\n                  </div>\n\n                  {project?.content ? (\n                    <>\n                      <div className=\"prose max-w-none\">\n                        <div className=\"bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto\">\n                          <pre className=\"whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed\">\n                            {project.content}\n                          </pre>\n                        </div>\n                      </div>\n\n                      {/* 分析区域 */}\n                      <div className=\"mt-6 space-y-4\">\n                        {/* 模型选择器 */}\n                        <div className=\"flex items-center justify-center space-x-3\">\n                          <span className=\"text-sm font-medium text-gray-700\">分析模型:</span>\n                          <ModelSelector\n                            selectedModel={selectedModel}\n                            onModelChange={setSelectedModel}\n                            className=\"w-64\"\n                          />\n                        </div>\n\n                        {/* 分析按钮 */}\n                        <div className=\"flex justify-center space-x-3\">\n                          <div className=\"relative flex\">\n                            <button\n                              onClick={handleManualAnalyze}\n                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}\n                              className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-l-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                              {isAnalyzing || project.status === 'analyzing' ? (\n                                <>\n                                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                                  AI分析中...\n                                </>\n                              ) : (\n                                <>\n                                  <Users className=\"mr-2\" size={20} />\n                                  {project.status === 'completed' ? 'AI重新分析人物剧情' : '自动分析人物剧情'}\n                                </>\n                              )}\n                            </button>\n\n                            <button\n                              onClick={() => setShowPromptDropdown(!showPromptDropdown)}\n                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}\n                              className=\"px-3 py-3 border border-transparent rounded-r-md border-l border-purple-500 text-base font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                            >\n                              <ChevronDown size={16} />\n                            </button>\n\n                            {/* 增强提示词下拉框 */}\n                            {showPromptDropdown && (\n                              <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10\">\n                                <div className=\"p-4\">\n                                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                    增强提示词设置\n                                  </label>\n                                  <textarea\n                                    value={enhancePrompt}\n                                    onChange={(e) => setEnhancePrompt(e.target.value)}\n                                    placeholder=\"输入增强提示词，用于优化AI分析效果...\"\n                                    className=\"w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none\"\n                                    rows={4}\n                                  />\n\n                                  {/* 保存按钮 */}\n                                  <div className=\"mt-3\">\n                                    <button\n                                      onClick={handleSaveEnhancePrompt}\n                                      className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2\"\n                                    >\n                                      保存增强提示词\n                                    </button>\n                                  </div>\n\n                                  {/* 操作按钮 */}\n                                  <div className=\"flex justify-end mt-3\">\n                                    <button\n                                      onClick={() => setShowPromptDropdown(false)}\n                                      className=\"text-sm text-gray-600 hover:text-gray-800\"\n                                    >\n                                      关闭\n                                    </button>\n                                  </div>\n\n                                  {/* 当前保存的提示词预览 */}\n                                  {savedEnhancePrompt && (\n                                    <div className=\"mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600\">\n                                      <div className=\"font-medium mb-1\">当前已保存的增强提示词：</div>\n                                      <div className=\"max-h-16 overflow-y-auto\">\n                                        {savedEnhancePrompt}\n                                      </div>\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n\n                          {project.status === 'completed' && (\n                            <div className=\"flex items-center text-sm text-green-600\">\n                              <CheckCircle className=\"mr-1\" size={16} />\n                              已完成分析，可重新分析\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <BookOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无小说内容</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        请先上传小说文件\n                      </p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'characters' && (\n            <CharacterManager\n              projectId={params.id}\n              characters={project.characters || []}\n              onCharactersUpdate={loadProject}\n            />\n          )}\n\n          {activeTab === 'episodes' && (\n            <EpisodeManager\n              episodes={episodes}\n              onAnalyzePlot={handleAnalyzePlot}\n              onGenerateVideo={handleGenerateVideo}\n              onExtractDetailedPlot={handleExtractDetailedPlot}\n            />\n          )}\n\n          {activeTab === 'videos' && (\n            <div className=\"space-y-6\">\n              {/* 视频管理头部 */}\n              <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <Video className=\"text-purple-600 mr-2\" size={20} />\n                    <h3 className=\"text-lg font-medium text-gray-900\">剧情视频管理</h3>\n                  </div>\n                  <span className=\"bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n                    共 {episodes.length} 集\n                  </span>\n                </div>\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  支持传统分段视频生成和新的一致性约束视频生成\n                </p>\n              </div>\n\n              {/* 剧集视频列表 */}\n              {episodes.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {episodes.map((episode, index) => (\n                    <EpisodeVideoCard\n                      key={episode.id}\n                      episode={episode}\n                      episodeIndex={index}\n                      projectId={projectId}\n                      onViewSegments={() => {\n                        setVideoEpisodeId(episode.id)\n                        setShowVideoSegmentViewer(true)\n                      }}\n                      onGenerateConsistencyVideo={() => handleOpenConsistencyVideoGenerator(episode.id)}\n                    />\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <Video className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无剧集</h3>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    请先上传小说并分析剧集\n                  </p>\n                </div>\n              )}\n\n              {/* 说明信息 */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h4 className=\"text-sm font-medium text-blue-800 mb-2\">\n                  分段视频生成说明\n                </h4>\n                <div className=\"text-sm text-blue-700 space-y-1\">\n                  <p>• <strong>智能分段：</strong>AI自动将剧情分解为3-8个短视频片段</p>\n                  <p>• <strong>并行生成：</strong>多个片段同时生成，提高效率</p>\n                  <p>• <strong>实时进度：</strong>可查看每个片段的生成状态和进度</p>\n                  <p>• <strong>独立播放：</strong>每个片段可单独播放和下载</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 详细剧情提取模态框 */}\n        <DetailedPlotExtraction\n          episodeId={currentEpisodeId}\n          episodeTitle={currentEpisodeTitle}\n          episodeContent={currentEpisodeContent}\n          isOpen={showDetailedPlotExtraction}\n          onClose={() => setShowDetailedPlotExtraction(false)}\n          onGenerateStoryVideo={handleGenerateStoryVideo}\n          videoGenerationMode={videoGenerationMode}\n          onVideoGenerationModeChange={setVideoGenerationMode}\n        />\n\n        {/* 视频片段查看器 */}\n        <VideoSegmentViewer\n          episodeId={videoEpisodeId}\n          projectId={projectId}\n          isOpen={showVideoSegmentViewer}\n          onClose={() => setShowVideoSegmentViewer(false)}\n        />\n\n        {/* 一致性视频生成器模态框 */}\n        {showConsistencyVideoGenerator && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n            <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">一致性视频生成</h2>\n                  <button\n                    onClick={() => setShowConsistencyVideoGenerator(false)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <X size={24} />\n                  </button>\n                </div>\n\n                <ConsistencyVideoGenerator\n                  episodeId={consistencyEpisodeId}\n                  episodeTitle={consistencyEpisodeTitle}\n                  onVideoGenerated={handleConsistencyVideoGenerated}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA;;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,OAAO,EAAE;IAE3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAEtD,kCAAkC;IAClC,MAAM,gBAAgB;QACpB,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,MAAM,YAAuB;YAAC;YAAU;YAAS;YAAc;YAAY;SAAS;QACpF,OAAO,UAAU,QAAQ,CAAC,OAAO,MAAM;IACzC;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gBAAgB;IAChB,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,MAAM,kBAAkB,IAAI,gBAAgB,aAAa,QAAQ;QACjE,gBAAgB,GAAG,CAAC,OAAO;QAC3B,OAAO,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,gBAAgB,QAAQ,IAAI,EAAE;YAAE,QAAQ;QAAM;IACzF;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,YAAY;IACZ,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,aAAa;IACb,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,cAAc;IACd,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,mCAAmC;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAElF,eAAe;IACf,MAAM,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,WAAW;IACX,MAAM,sBAAsB,CAAC;QAC3B,WAAW,eAAe,OAAO;QACjC,cAAc,eAAe,UAAU,IAAI,EAAE;QAC7C,YAAY,eAAe,QAAQ,IAAI,EAAE;QACzC,gBAAgB;IAClB;IAEA,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,SAAS;IACT,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,UAAU,aAAa,CAAC,EAAE;gBAC5F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,KACP,GAAG,EAAE,KAAK,YACN;4BAAE,GAAG,EAAE;4BAAE,UAAU,KAAK,IAAI;wBAAC,IAC7B;YAGV,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,UAAU,eAAe,CAAC,EAAE;gBAC9F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,KACP,GAAG,EAAE,KAAK,YACN;4BAAE,GAAG,EAAE;4BAAE,QAAQ;wBAAkB,IACnC;YAGR,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,aAAa;IACb,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QAC9C,IAAI,SAAS;YACX,oBAAoB;YACpB,uBAAuB,QAAQ,KAAK;YACpC,yBAAyB,QAAQ,OAAO;YACxC,8BAA8B;QAChC;IACF;IAEA,aAAa;IACb,MAAM,sCAAsC,CAAC;QAC3C,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QAC9C,IAAI,SAAS;YACX,wBAAwB;YACxB,2BAA2B,QAAQ,KAAK;YACxC,iCAAiC;QACnC;IACF;IAEA,cAAc;IACd,MAAM,kCAAkC,CAAC;QACvC,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,qBAAqB;QACrB,MAAM,CAAC,kBAAkB,EAAE,UAAU,YAAY,CAAC,QAAQ,EAAE,UAAU,eAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,eAAe,CAAC,mBAAmB,CAAC,EAAE,CAAC;IAClK;IAEA,WAAW;IACX,MAAM,2BAA2B,OAAO,WAAmB,QAAgB;QACzE,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,iBAAiB;YACjB,MAAM,gBAAgB,MAAM,MAAM,CAAC,iCAAiC,EAAE,WAAW;YACjF,MAAM,YAAY,MAAM,cAAc,IAAI;YAE1C,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,IAAI,UAAU,OAAO,IAAI,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7E,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;gBAEnE,mBAAmB;gBACnB,MAAM,YAAY,QAChB,CAAC,SAAS,EAAE,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,GACvD,CAAC,SAAS,CAAC,GACX,CAAC,eAAe,CAAC,GACjB,CAAC,eAAe,CAAC,GACjB,CAAC,kBAAkB,CAAC,GACpB,CAAC,OAAO,CAAC;gBAGX,QAAQ,GAAG,CAAC,cAAc;gBAE1B,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CAAC;oBACZ,QAAO,SAAS;gBAClB;gBAEA,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;oBACA,gBAAgB,oBAAoB,SAAS;gBAC/C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY;gBACZ,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,IAAI;gBACrC,MAAM,cAAc,wBAAwB,SACxC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,GAC1C,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC,yDAAyD,CAAC;gBAC7F,MAAM,GAAG,QAAQ,IAAI,EAAE,aAAa;gBACpC,8BAA8B;gBAE9B,qBAAqB;gBACrB,uBAAuB;gBACvB,kBAAkB;gBAClB,0BAA0B;YAC5B,OAAO;gBACL,eAAe;gBACf,IAAI,KAAK,eAAe,KAAK,oBAAoB;oBAC/C,MAAM,eAAe,KAAK,iBAAiB,EAAE,KAAK,QAAQ;oBAC1D,MAAM,eAAe,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,gCAAgC,EAAE,aAAa,4CAA4C,CAAC;oBAC3I,MAAM;oBAEN,eAAe;oBACf,aAAa;gBACf,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;IAEA,UAAU;IACV,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,sBAAsB;QACtB,kBAAkB;QAClB,MAAM,aAAa,CAAC,yBAAyB,EAAE,WAAW;QAC1D,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,aAAa,CAAC,yBAAyB,EAAE,WAAW;YAC1D,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,sBAAsB;gBACtB,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;KAAU;IAEd,WAAW;IACX,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,SAAS;YACrB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,eAAe;YAClB,SAAS;YACT;QACF;QAEA,eAAe;QACf,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,QAAQ,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,cAAc,sBAAsB;gBACtC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS;gBACT,WAAW,KAAK,IAAI,CAAC,OAAO;gBAC5B,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;gBACpC,WAAW;gBACX,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,MAAM,YAAuB;YAAC;YAAU;YAAS;YAAc;YAAY;SAAS;QACpF,IAAI,OAAO,UAAU,QAAQ,CAAC,QAAQ,QAAQ,WAAW;YACvD,aAAa;QACf;IACF,GAAG;QAAC;QAAc;KAAU;IAE5B,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;gBACpB,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAEpC,iCAAiC;gBACjC,IAAI,CAAC,aAAa,GAAG,CAAC,QAAQ;oBAC5B,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,WAAW;wBAClC,gBAAgB;oBAClB,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,YAAY;wBAC1C,gBAAgB;oBAClB,OAAO;wBACL,gBAAgB;oBAClB;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;gBACpB,cAAc,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;gBACxC,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY;YAChB,SAAS;gBAAE,MAAM;gBAAO,OAAO;gBAA6B,MAAM,8MAAA,CAAA,WAAQ;YAAC;YAC3E,UAAU;gBAAE,MAAM;gBAAO,OAAO;gBAA6B,MAAM,sMAAA,CAAA,SAAM;YAAC;YAC1E,WAAW;gBAAE,MAAM;gBAAO,OAAO;gBAAiC,MAAM,kMAAA,CAAA,OAAI;YAAC;YAC7E,WAAW;gBAAE,MAAM;gBAAO,OAAO;gBAA+B,MAAM,2NAAA,CAAA,cAAW;YAAC;QACpF;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAEA,QAAQ;IACR,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;YACb,UAAU,CAAC,WAAW,QAAQ,MAAM,KAAK;QAC3C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,UAAU,CAAC,WAAW;gBAAC;gBAAW;aAAW,CAAC,QAAQ,CAAC,QAAQ,MAAM;QACvE;KACD;IAED,IAAI,SAAS;QACX,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCACV,SAAS;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,MAAM,gBAAgB,iBAAiB,QAAQ,MAAM;IAErD,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAO,MAAM;;;;;;gCAAM;;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,QAAQ,IAAI;;;;;;oCAC7D,QAAQ,WAAW,kBAClB,8OAAC;wCAAE,WAAU;kDAA8B,QAAQ,WAAW;;;;;;kDAEhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EAAE,cAAc,KAAK,EAAE;;kEAC/G,8OAAC,cAAc,IAAI;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAC1C,cAAc,IAAI;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;oDAClC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,WAAW,cAAc,IAAI,EAAE;4BACrC,MAAM,aAAa,IAAI,QAAQ;4BAE/B,qBACE,8OAAC;gCAEC,SAAS,IAAM,CAAC,cAAc,gBAAgB,IAAI,EAAE;gCACpD,UAAU;gCACV,WAAW,CAAC;;oBAEV,EAAE,WACE,sCACA,aACA,wDACA,6EACH;kBACH,CAAC;;kDAED,8OAAC,IAAI,IAAI;wCACP,WAAW,CAAC,KAAK,EAAE,WAAW,oBAAoB,aAAa,kBAAkB,2CAA2C;wCAC5H,MAAM;;;;;;oCAEP,IAAI,IAAI;oCACR,IAAI,EAAE,KAAK,gBAAgB,WAAW,MAAM,GAAG,mBAC9C,8OAAC;wCAAK,WAAU;kDACb,WAAW,MAAM;;;;;;oCAGrB,IAAI,EAAE,KAAK,cAAc,SAAS,MAAM,GAAG,mBAC1C,8OAAC;wCAAK,WAAU;kDACb,SAAS,MAAM;;;;;;;+BAzBf,IAAI,EAAE;;;;;wBA8BjB;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,0BACb,8OAAC,uIAAA,CAAA,UAAiB;4BAChB,WAAW;4BACX,SAAS;4BACT,iBAAiB;4BACjB,eAAe;;;;;;wBAIlB,cAAc,yBACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;;wDAAwB;wDACjC,SAAS,SAAS,UAAU;wDAAE;;;;;;;;;;;;;wCAIrC,SAAS,wBACR;;8DACE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO;;;;;;;;;;;;;;;;8DAMtB,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,8OAAC,mIAAA,CAAA,UAAa;oEACZ,eAAe;oEACf,eAAe;oEACf,WAAU;;;;;;;;;;;;sEAKd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,SAAS;4EACT,UAAU,eAAe,QAAQ,MAAM,KAAK,eAAe,CAAC;4EAC5D,WAAU;sFAET,eAAe,QAAQ,MAAM,KAAK,4BACjC;;kGACE,8OAAC;wFAAI,WAAU;;;;;;oFAAuE;;6GAIxF;;kGACE,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;wFAAO,MAAM;;;;;;oFAC7B,QAAQ,MAAM,KAAK,cAAc,eAAe;;;;;;;;sFAKvD,8OAAC;4EACC,SAAS,IAAM,sBAAsB,CAAC;4EACtC,UAAU,eAAe,QAAQ,MAAM,KAAK,eAAe,CAAC;4EAC5D,WAAU;sFAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gFAAC,MAAM;;;;;;;;;;;wEAIpB,oCACC,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAM,WAAU;kGAA+C;;;;;;kGAGhE,8OAAC;wFACC,OAAO;wFACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wFAChD,aAAY;wFACZ,WAAU;wFACV,MAAM;;;;;;kGAIR,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FACC,SAAS;4FACT,WAAU;sGACX;;;;;;;;;;;kGAMH,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FACC,SAAS,IAAM,sBAAsB;4FACrC,WAAU;sGACX;;;;;;;;;;;oFAMF,oCACC,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;0GAAmB;;;;;;0GAClC,8OAAC;gGAAI,WAAU;0GACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gEASd,QAAQ,MAAM,KAAK,6BAClB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;4EAAO,MAAM;;;;;;wEAAM;;;;;;;;;;;;;;;;;;;;yEAQpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUrD,cAAc,8BACb,8OAAC,sIAAA,CAAA,UAAgB;4BACf,WAAW,OAAO,EAAE;4BACpB,YAAY,QAAQ,UAAU,IAAI,EAAE;4BACpC,oBAAoB;;;;;;wBAIvB,cAAc,4BACb,8OAAC,oIAAA,CAAA,UAAc;4BACb,UAAU;4BACV,eAAe;4BACf,iBAAiB;4BACjB,uBAAuB;;;;;;wBAI1B,cAAc,0BACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAuB,MAAM;;;;;;sEAC9C,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;8DAEpD,8OAAC;oDAAK,WAAU;;wDAA+E;wDAC1F,SAAS,MAAM;wDAAC;;;;;;;;;;;;;sDAGvB,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;gCAM3C,SAAS,MAAM,GAAG,kBACjB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,sIAAA,CAAA,UAAgB;4CAEf,SAAS;4CACT,cAAc;4CACd,WAAW;4CACX,gBAAgB;gDACd,kBAAkB,QAAQ,EAAE;gDAC5B,0BAA0B;4CAC5B;4CACA,4BAA4B,IAAM,oCAAoC,QAAQ,EAAE;2CAR3E,QAAQ,EAAE;;;;;;;;;yDAarB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAO9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;sEAAE,8OAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,8OAAC;;wDAAE;sEAAE,8OAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,8OAAC;;wDAAE;sEAAE,8OAAC;sEAAO;;;;;;wDAAc;;;;;;;8DAC3B,8OAAC;;wDAAE;sEAAE,8OAAC;sEAAO;;;;;;wDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrC,8OAAC,4IAAA,CAAA,UAAsB;oBACrB,WAAW;oBACX,cAAc;oBACd,gBAAgB;oBAChB,QAAQ;oBACR,SAAS,IAAM,8BAA8B;oBAC7C,sBAAsB;oBACtB,qBAAqB;oBACrB,6BAA6B;;;;;;8BAI/B,8OAAC,wIAAA,CAAA,UAAkB;oBACjB,WAAW;oBACX,WAAW;oBACX,QAAQ;oBACR,SAAS,IAAM,0BAA0B;;;;;;gBAI1C,+CACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CACC,SAAS,IAAM,iCAAiC;4CAChD,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAIb,8OAAC,+IAAA,CAAA,UAAyB;oCACxB,WAAW;oCACX,cAAc;oCACd,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}]}