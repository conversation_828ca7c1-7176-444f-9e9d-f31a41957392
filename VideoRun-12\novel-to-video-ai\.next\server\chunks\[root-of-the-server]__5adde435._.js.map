{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/models/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\ninterface ModelConfig {\n  id: string\n  provider: string\n  model: string\n  apiKey: string\n  enabled: boolean\n  name: string\n  description?: string\n  supportsVideo?: boolean\n  supportsImage?: boolean\n  supportsImageToVideo?: boolean\n  temperature?: number\n  maxTokens?: number\n  topP?: number\n}\n\n// GET - 获取所有模型配置\nexport async function GET() {\n  try {\n    const aiConfigs = await prisma.aIConfig.findMany({\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    console.log('获取到的AI配置:', aiConfigs)\n\n    return NextResponse.json({\n      success: true,\n      data: aiConfigs\n    })\n  } catch (error) {\n    console.error('获取模型列表失败:', error)\n    return NextResponse.json(\n      {\n        success: false,\n        error: '获取模型列表失败'\n      },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - 保存或更新模型配置\nexport async function POST(request: NextRequest) {\n  try {\n    const modelConfig: ModelConfig = await request.json()\n\n    // 验证必需字段\n    if (!modelConfig.provider || !modelConfig.model || !modelConfig.name) {\n      return NextResponse.json(\n        { success: false, error: '缺少必需字段' },\n        { status: 400 }\n      )\n    }\n\n    let savedConfig\n\n    if (modelConfig.id) {\n      // 更新现有配置\n      savedConfig = await prisma.aIConfig.update({\n        where: { id: modelConfig.id },\n        data: {\n          provider: modelConfig.provider,\n          model: modelConfig.model,\n          name: modelConfig.name,\n          description: modelConfig.description,\n          apiKey: modelConfig.apiKey,\n          enabled: modelConfig.enabled,\n          supportsVideo: modelConfig.supportsVideo ?? false,\n          supportsImage: modelConfig.supportsImage ?? false,\n          supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,\n          temperature: modelConfig.temperature ?? 0.7,\n          status: 'disconnected' // 重置状态，需要重新测试\n        }\n      })\n    } else {\n      // 创建新配置\n      savedConfig = await prisma.aIConfig.create({\n        data: {\n          provider: modelConfig.provider,\n          model: modelConfig.model,\n          name: modelConfig.name,\n          description: modelConfig.description || '',\n          apiKey: modelConfig.apiKey,\n          enabled: modelConfig.enabled ?? true,\n          supportsVideo: modelConfig.supportsVideo ?? false,\n          supportsImage: modelConfig.supportsImage ?? false,\n          supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,\n          temperature: modelConfig.temperature ?? 0.7,\n          status: 'disconnected'\n        }\n      })\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: savedConfig\n    })\n  } catch (error) {\n    console.error('保存模型配置失败:', error)\n    return NextResponse.json(\n      { success: false, error: '保存模型配置失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAmBO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,SAAS;gBACP,WAAW;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,aAAa;QAEzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;QACT,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAA2B,MAAM,QAAQ,IAAI;QAEnD,SAAS;QACT,IAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,IAAI,EAAE;YACpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,YAAY,EAAE,EAAE;YAClB,SAAS;YACT,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACzC,OAAO;oBAAE,IAAI,YAAY,EAAE;gBAAC;gBAC5B,MAAM;oBACJ,UAAU,YAAY,QAAQ;oBAC9B,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,aAAa,YAAY,WAAW;oBACpC,QAAQ,YAAY,MAAM;oBAC1B,SAAS,YAAY,OAAO;oBAC5B,eAAe,YAAY,aAAa,IAAI;oBAC5C,eAAe,YAAY,aAAa,IAAI;oBAC5C,sBAAsB,YAAY,oBAAoB,IAAI;oBAC1D,aAAa,YAAY,WAAW,IAAI;oBACxC,QAAQ,eAAe,cAAc;gBACvC;YACF;QACF,OAAO;YACL,QAAQ;YACR,cAAc,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACzC,MAAM;oBACJ,UAAU,YAAY,QAAQ;oBAC9B,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,aAAa,YAAY,WAAW,IAAI;oBACxC,QAAQ,YAAY,MAAM;oBAC1B,SAAS,YAAY,OAAO,IAAI;oBAChC,eAAe,YAAY,aAAa,IAAI;oBAC5C,eAAe,YAAY,aAAa,IAAI;oBAC5C,sBAAsB,YAAY,oBAAoB,IAAI;oBAC1D,aAAa,YAAY,WAAW,IAAI;oBACxC,QAAQ;gBACV;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}