{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/es5.js"], "sourcesContent": ["var isES5 = (function(){\n    \"use strict\";\n    return this === undefined;\n})();\n\nif (isES5) {\n    module.exports = {\n        freeze: Object.freeze,\n        defineProperty: Object.defineProperty,\n        getDescriptor: Object.getOwnPropertyDescriptor,\n        keys: Object.keys,\n        names: Object.getOwnPropertyNames,\n        getPrototypeOf: Object.getPrototypeOf,\n        isArray: Array.isArray,\n        isES5: isES5,\n        propertyIsWritable: function(obj, prop) {\n            var descriptor = Object.getOwnPropertyDescriptor(obj, prop);\n            return !!(!descriptor || descriptor.writable || descriptor.set);\n        }\n    };\n} else {\n    var has = {}.hasOwnProperty;\n    var str = {}.toString;\n    var proto = {}.constructor.prototype;\n\n    var ObjectKeys = function (o) {\n        var ret = [];\n        for (var key in o) {\n            if (has.call(o, key)) {\n                ret.push(key);\n            }\n        }\n        return ret;\n    };\n\n    var ObjectGetDescriptor = function(o, key) {\n        return {value: o[key]};\n    };\n\n    var ObjectDefineProperty = function (o, key, desc) {\n        o[key] = desc.value;\n        return o;\n    };\n\n    var ObjectFreeze = function (obj) {\n        return obj;\n    };\n\n    var ObjectGetPrototypeOf = function (obj) {\n        try {\n            return Object(obj).constructor.prototype;\n        }\n        catch (e) {\n            return proto;\n        }\n    };\n\n    var ArrayIsArray = function (obj) {\n        try {\n            return str.call(obj) === \"[object Array]\";\n        }\n        catch(e) {\n            return false;\n        }\n    };\n\n    module.exports = {\n        isArray: ArrayIsArray,\n        keys: ObjectKeys,\n        names: ObjectKeys,\n        defineProperty: ObjectDefineProperty,\n        getDescriptor: ObjectGetDescriptor,\n        freeze: ObjectFreeze,\n        getPrototypeOf: ObjectGetPrototypeOf,\n        isES5: isES5,\n        propertyIsWritable: function() {\n            return true;\n        }\n    };\n}\n"], "names": [], "mappings": "AAAA,IAAI,QAAQ,AAAC;IACT;IACA,OAAO,IAAI,KAAK;AACpB;AAEA,IAAI,OAAO;IACP,OAAO,OAAO,GAAG;QACb,QAAQ,OAAO,MAAM;QACrB,gBAAgB,OAAO,cAAc;QACrC,eAAe,OAAO,wBAAwB;QAC9C,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,mBAAmB;QACjC,gBAAgB,OAAO,cAAc;QACrC,SAAS,MAAM,OAAO;QACtB,OAAO;QACP,oBAAoB,SAAS,GAAG,EAAE,IAAI;YAClC,IAAI,aAAa,OAAO,wBAAwB,CAAC,KAAK;YACtD,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,WAAW,QAAQ,IAAI,WAAW,GAAG;QAClE;IACJ;AACJ,OAAO;IACH,IAAI,MAAM,CAAC,EAAE,cAAc;IAC3B,IAAI,MAAM,CAAC,EAAE,QAAQ;IACrB,IAAI,QAAQ,CAAC,EAAE,WAAW,CAAC,SAAS;IAEpC,IAAI,aAAa,SAAU,CAAC;QACxB,IAAI,MAAM,EAAE;QACZ,IAAK,IAAI,OAAO,EAAG;YACf,IAAI,IAAI,IAAI,CAAC,GAAG,MAAM;gBAClB,IAAI,IAAI,CAAC;YACb;QACJ;QACA,OAAO;IACX;IAEA,IAAI,sBAAsB,SAAS,CAAC,EAAE,GAAG;QACrC,OAAO;YAAC,OAAO,CAAC,CAAC,IAAI;QAAA;IACzB;IAEA,IAAI,uBAAuB,SAAU,CAAC,EAAE,GAAG,EAAE,IAAI;QAC7C,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK;QACnB,OAAO;IACX;IAEA,IAAI,eAAe,SAAU,GAAG;QAC5B,OAAO;IACX;IAEA,IAAI,uBAAuB,SAAU,GAAG;QACpC,IAAI;YACA,OAAO,OAAO,KAAK,WAAW,CAAC,SAAS;QAC5C,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IAEA,IAAI,eAAe,SAAU,GAAG;QAC5B,IAAI;YACA,OAAO,IAAI,IAAI,CAAC,SAAS;QAC7B,EACA,OAAM,GAAG;YACL,OAAO;QACX;IACJ;IAEA,OAAO,OAAO,GAAG;QACb,SAAS;QACT,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,eAAe;QACf,QAAQ;QACR,gBAAgB;QAChB,OAAO;QACP,oBAAoB;YAChB,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/util.js"], "sourcesContent": ["\"use strict\";\nvar es5 = require(\"./es5\");\nvar canEvaluate = typeof navigator == \"undefined\";\n\nvar errorObj = {e: {}};\nvar tryCatchTarget;\nvar globalObject = typeof self !== \"undefined\" ? self :\n    typeof window !== \"undefined\" ? window :\n    typeof global !== \"undefined\" ? global :\n    this !== undefined ? this : null;\n\nfunction tryCatcher() {\n    try {\n        var target = tryCatchTarget;\n        tryCatchTarget = null;\n        return target.apply(this, arguments);\n    } catch (e) {\n        errorObj.e = e;\n        return errorObj;\n    }\n}\nfunction tryCatch(fn) {\n    tryCatchTarget = fn;\n    return tryCatcher;\n}\n\nvar inherits = function(Child, Parent) {\n    var hasProp = {}.hasOwnProperty;\n\n    function T() {\n        this.constructor = Child;\n        this.constructor$ = Parent;\n        for (var propertyName in Parent.prototype) {\n            if (hasProp.call(Parent.prototype, propertyName) &&\n                propertyName.charAt(propertyName.length-1) !== \"$\"\n           ) {\n                this[propertyName + \"$\"] = Parent.prototype[propertyName];\n            }\n        }\n    }\n    T.prototype = Parent.prototype;\n    Child.prototype = new T();\n    return Child.prototype;\n};\n\n\nfunction isPrimitive(val) {\n    return val == null || val === true || val === false ||\n        typeof val === \"string\" || typeof val === \"number\";\n\n}\n\nfunction isObject(value) {\n    return typeof value === \"function\" ||\n           typeof value === \"object\" && value !== null;\n}\n\nfunction maybeWrapAsError(maybeError) {\n    if (!isPrimitive(maybeError)) return maybeError;\n\n    return new Error(safeToString(maybeError));\n}\n\nfunction withAppended(target, appendee) {\n    var len = target.length;\n    var ret = new Array(len + 1);\n    var i;\n    for (i = 0; i < len; ++i) {\n        ret[i] = target[i];\n    }\n    ret[i] = appendee;\n    return ret;\n}\n\nfunction getDataPropertyOrDefault(obj, key, defaultValue) {\n    if (es5.isES5) {\n        var desc = Object.getOwnPropertyDescriptor(obj, key);\n\n        if (desc != null) {\n            return desc.get == null && desc.set == null\n                    ? desc.value\n                    : defaultValue;\n        }\n    } else {\n        return {}.hasOwnProperty.call(obj, key) ? obj[key] : undefined;\n    }\n}\n\nfunction notEnumerableProp(obj, name, value) {\n    if (isPrimitive(obj)) return obj;\n    var descriptor = {\n        value: value,\n        configurable: true,\n        enumerable: false,\n        writable: true\n    };\n    es5.defineProperty(obj, name, descriptor);\n    return obj;\n}\n\nfunction thrower(r) {\n    throw r;\n}\n\nvar inheritedDataKeys = (function() {\n    var excludedPrototypes = [\n        Array.prototype,\n        Object.prototype,\n        Function.prototype\n    ];\n\n    var isExcludedProto = function(val) {\n        for (var i = 0; i < excludedPrototypes.length; ++i) {\n            if (excludedPrototypes[i] === val) {\n                return true;\n            }\n        }\n        return false;\n    };\n\n    if (es5.isES5) {\n        var getKeys = Object.getOwnPropertyNames;\n        return function(obj) {\n            var ret = [];\n            var visitedKeys = Object.create(null);\n            while (obj != null && !isExcludedProto(obj)) {\n                var keys;\n                try {\n                    keys = getKeys(obj);\n                } catch (e) {\n                    return ret;\n                }\n                for (var i = 0; i < keys.length; ++i) {\n                    var key = keys[i];\n                    if (visitedKeys[key]) continue;\n                    visitedKeys[key] = true;\n                    var desc = Object.getOwnPropertyDescriptor(obj, key);\n                    if (desc != null && desc.get == null && desc.set == null) {\n                        ret.push(key);\n                    }\n                }\n                obj = es5.getPrototypeOf(obj);\n            }\n            return ret;\n        };\n    } else {\n        var hasProp = {}.hasOwnProperty;\n        return function(obj) {\n            if (isExcludedProto(obj)) return [];\n            var ret = [];\n\n            /*jshint forin:false */\n            enumeration: for (var key in obj) {\n                if (hasProp.call(obj, key)) {\n                    ret.push(key);\n                } else {\n                    for (var i = 0; i < excludedPrototypes.length; ++i) {\n                        if (hasProp.call(excludedPrototypes[i], key)) {\n                            continue enumeration;\n                        }\n                    }\n                    ret.push(key);\n                }\n            }\n            return ret;\n        };\n    }\n\n})();\n\nvar thisAssignmentPattern = /this\\s*\\.\\s*\\S+\\s*=/;\nfunction isClass(fn) {\n    try {\n        if (typeof fn === \"function\") {\n            var keys = es5.names(fn.prototype);\n\n            var hasMethods = es5.isES5 && keys.length > 1;\n            var hasMethodsOtherThanConstructor = keys.length > 0 &&\n                !(keys.length === 1 && keys[0] === \"constructor\");\n            var hasThisAssignmentAndStaticMethods =\n                thisAssignmentPattern.test(fn + \"\") && es5.names(fn).length > 0;\n\n            if (hasMethods || hasMethodsOtherThanConstructor ||\n                hasThisAssignmentAndStaticMethods) {\n                return true;\n            }\n        }\n        return false;\n    } catch (e) {\n        return false;\n    }\n}\n\nfunction toFastProperties(obj) {\n    /*jshint -W027,-W055,-W031*/\n    function FakeConstructor() {}\n    FakeConstructor.prototype = obj;\n    var l = 8;\n    while (l--) new FakeConstructor();\n    return obj;\n    eval(obj);\n}\n\nvar rident = /^[a-z$_][a-z$_0-9]*$/i;\nfunction isIdentifier(str) {\n    return rident.test(str);\n}\n\nfunction filledRange(count, prefix, suffix) {\n    var ret = new Array(count);\n    for(var i = 0; i < count; ++i) {\n        ret[i] = prefix + i + suffix;\n    }\n    return ret;\n}\n\nfunction safeToString(obj) {\n    try {\n        return obj + \"\";\n    } catch (e) {\n        return \"[no string representation]\";\n    }\n}\n\nfunction isError(obj) {\n    return obj !== null &&\n           typeof obj === \"object\" &&\n           typeof obj.message === \"string\" &&\n           typeof obj.name === \"string\";\n}\n\nfunction markAsOriginatingFromRejection(e) {\n    try {\n        notEnumerableProp(e, \"isOperational\", true);\n    }\n    catch(ignore) {}\n}\n\nfunction originatesFromRejection(e) {\n    if (e == null) return false;\n    return ((e instanceof Error[\"__BluebirdErrorTypes__\"].OperationalError) ||\n        e[\"isOperational\"] === true);\n}\n\nfunction canAttachTrace(obj) {\n    return isError(obj) && es5.propertyIsWritable(obj, \"stack\");\n}\n\nvar ensureErrorObject = (function() {\n    if (!(\"stack\" in new Error())) {\n        return function(value) {\n            if (canAttachTrace(value)) return value;\n            try {throw new Error(safeToString(value));}\n            catch(err) {return err;}\n        };\n    } else {\n        return function(value) {\n            if (canAttachTrace(value)) return value;\n            return new Error(safeToString(value));\n        };\n    }\n})();\n\nfunction classString(obj) {\n    return {}.toString.call(obj);\n}\n\nfunction copyDescriptors(from, to, filter) {\n    var keys = es5.names(from);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        if (filter(key)) {\n            try {\n                es5.defineProperty(to, key, es5.getDescriptor(from, key));\n            } catch (ignore) {}\n        }\n    }\n}\n\nvar asArray = function(v) {\n    if (es5.isArray(v)) {\n        return v;\n    }\n    return null;\n};\n\nif (typeof Symbol !== \"undefined\" && Symbol.iterator) {\n    var ArrayFrom = typeof Array.from === \"function\" ? function(v) {\n        return Array.from(v);\n    } : function(v) {\n        var ret = [];\n        var it = v[Symbol.iterator]();\n        var itResult;\n        while (!((itResult = it.next()).done)) {\n            ret.push(itResult.value);\n        }\n        return ret;\n    };\n\n    asArray = function(v) {\n        if (es5.isArray(v)) {\n            return v;\n        } else if (v != null && typeof v[Symbol.iterator] === \"function\") {\n            return ArrayFrom(v);\n        }\n        return null;\n    };\n}\n\nvar isNode = typeof process !== \"undefined\" &&\n        classString(process).toLowerCase() === \"[object process]\";\n\nvar hasEnvVariables = typeof process !== \"undefined\" &&\n    typeof process.env !== \"undefined\";\n\nfunction env(key) {\n    return hasEnvVariables ? process.env[key] : undefined;\n}\n\nfunction getNativePromise() {\n    if (typeof Promise === \"function\") {\n        try {\n            var promise = new Promise(function(){});\n            if ({}.toString.call(promise) === \"[object Promise]\") {\n                return Promise;\n            }\n        } catch (e) {}\n    }\n}\n\nfunction domainBind(self, cb) {\n    return self.bind(cb);\n}\n\nvar ret = {\n    isClass: isClass,\n    isIdentifier: isIdentifier,\n    inheritedDataKeys: inheritedDataKeys,\n    getDataPropertyOrDefault: getDataPropertyOrDefault,\n    thrower: thrower,\n    isArray: es5.isArray,\n    asArray: asArray,\n    notEnumerableProp: notEnumerableProp,\n    isPrimitive: isPrimitive,\n    isObject: isObject,\n    isError: isError,\n    canEvaluate: canEvaluate,\n    errorObj: errorObj,\n    tryCatch: tryCatch,\n    inherits: inherits,\n    withAppended: withAppended,\n    maybeWrapAsError: maybeWrapAsError,\n    toFastProperties: toFastProperties,\n    filledRange: filledRange,\n    toString: safeToString,\n    canAttachTrace: canAttachTrace,\n    ensureErrorObject: ensureErrorObject,\n    originatesFromRejection: originatesFromRejection,\n    markAsOriginatingFromRejection: markAsOriginatingFromRejection,\n    classString: classString,\n    copyDescriptors: copyDescriptors,\n    hasDevTools: typeof chrome !== \"undefined\" && chrome &&\n                 typeof chrome.loadTimes === \"function\",\n    isNode: isNode,\n    hasEnvVariables: hasEnvVariables,\n    env: env,\n    global: globalObject,\n    getNativePromise: getNativePromise,\n    domainBind: domainBind\n};\nret.isRecentNode = ret.isNode && (function() {\n    var version = process.versions.node.split(\".\").map(Number);\n    return (version[0] === 0 && version[1] > 10) || (version[0] > 0);\n})();\n\nif (ret.isNode) ret.toFastProperties(process);\n\ntry {throw new Error(); } catch (e) {ret.lastLineError = e;}\nmodule.exports = ret;\n"], "names": [], "mappings": "AAmXkB;AAnXlB;AACA,IAAI;AACJ,IAAI,cAAc,OAAO,aAAa;AAEtC,IAAI,WAAW;IAAC,GAAG,CAAC;AAAC;AACrB,IAAI;AACJ,IAAI,eAAe,OAAO,SAAS,cAAc,OAC7C,OAAO,WAAW,cAAc,SAChC,OAAO,WAAW,cAAc,SAChC,IAAI,KAAK,YAAY,IAAI,GAAG;AAEhC,SAAS;IACL,IAAI;QACA,IAAI,SAAS;QACb,iBAAiB;QACjB,OAAO,OAAO,KAAK,CAAC,IAAI,EAAE;IAC9B,EAAE,OAAO,GAAG;QACR,SAAS,CAAC,GAAG;QACb,OAAO;IACX;AACJ;AACA,SAAS,SAAS,EAAE;IAChB,iBAAiB;IACjB,OAAO;AACX;AAEA,IAAI,WAAW,SAAS,KAAK,EAAE,MAAM;IACjC,IAAI,UAAU,CAAC,EAAE,cAAc;IAE/B,SAAS;QACL,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAK,IAAI,gBAAgB,OAAO,SAAS,CAAE;YACvC,IAAI,QAAQ,IAAI,CAAC,OAAO,SAAS,EAAE,iBAC/B,aAAa,MAAM,CAAC,aAAa,MAAM,GAAC,OAAO,KAClD;gBACG,IAAI,CAAC,eAAe,IAAI,GAAG,OAAO,SAAS,CAAC,aAAa;YAC7D;QACJ;IACJ;IACA,EAAE,SAAS,GAAG,OAAO,SAAS;IAC9B,MAAM,SAAS,GAAG,IAAI;IACtB,OAAO,MAAM,SAAS;AAC1B;AAGA,SAAS,YAAY,GAAG;IACpB,OAAO,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,SAC1C,OAAO,QAAQ,YAAY,OAAO,QAAQ;AAElD;AAEA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,cACjB,OAAO,UAAU,YAAY,UAAU;AAClD;AAEA,SAAS,iBAAiB,UAAU;IAChC,IAAI,CAAC,YAAY,aAAa,OAAO;IAErC,OAAO,IAAI,MAAM,aAAa;AAClC;AAEA,SAAS,aAAa,MAAM,EAAE,QAAQ;IAClC,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,MAAM,IAAI,MAAM,MAAM;IAC1B,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QACtB,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;IACtB;IACA,GAAG,CAAC,EAAE,GAAG;IACT,OAAO;AACX;AAEA,SAAS,yBAAyB,GAAG,EAAE,GAAG,EAAE,YAAY;IACpD,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,OAAO,wBAAwB,CAAC,KAAK;QAEhD,IAAI,QAAQ,MAAM;YACd,OAAO,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,OAC7B,KAAK,KAAK,GACV;QACd;IACJ,OAAO;QACH,OAAO,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,KAAK,OAAO,GAAG,CAAC,IAAI,GAAG;IACzD;AACJ;AAEA,SAAS,kBAAkB,GAAG,EAAE,IAAI,EAAE,KAAK;IACvC,IAAI,YAAY,MAAM,OAAO;IAC7B,IAAI,aAAa;QACb,OAAO;QACP,cAAc;QACd,YAAY;QACZ,UAAU;IACd;IACA,IAAI,cAAc,CAAC,KAAK,MAAM;IAC9B,OAAO;AACX;AAEA,SAAS,QAAQ,CAAC;IACd,MAAM;AACV;AAEA,IAAI,oBAAoB,AAAC;IACrB,IAAI,qBAAqB;QACrB,MAAM,SAAS;QACf,OAAO,SAAS;QAChB,SAAS,SAAS;KACrB;IAED,IAAI,kBAAkB,SAAS,GAAG;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,MAAM,EAAE,EAAE,EAAG;YAChD,IAAI,kBAAkB,CAAC,EAAE,KAAK,KAAK;gBAC/B,OAAO;YACX;QACJ;QACA,OAAO;IACX;IAEA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,UAAU,OAAO,mBAAmB;QACxC,OAAO,SAAS,GAAG;YACf,IAAI,MAAM,EAAE;YACZ,IAAI,cAAc,OAAO,MAAM,CAAC;YAChC,MAAO,OAAO,QAAQ,CAAC,gBAAgB,KAAM;gBACzC,IAAI;gBACJ,IAAI;oBACA,OAAO,QAAQ;gBACnB,EAAE,OAAO,GAAG;oBACR,OAAO;gBACX;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;oBAClC,IAAI,MAAM,IAAI,CAAC,EAAE;oBACjB,IAAI,WAAW,CAAC,IAAI,EAAE;oBACtB,WAAW,CAAC,IAAI,GAAG;oBACnB,IAAI,OAAO,OAAO,wBAAwB,CAAC,KAAK;oBAChD,IAAI,QAAQ,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM;wBACtD,IAAI,IAAI,CAAC;oBACb;gBACJ;gBACA,MAAM,IAAI,cAAc,CAAC;YAC7B;YACA,OAAO;QACX;IACJ,OAAO;QACH,IAAI,UAAU,CAAC,EAAE,cAAc;QAC/B,OAAO,SAAS,GAAG;YACf,IAAI,gBAAgB,MAAM,OAAO,EAAE;YACnC,IAAI,MAAM,EAAE;YAEZ,qBAAqB,GACrB,aAAa,IAAK,IAAI,OAAO,IAAK;gBAC9B,IAAI,QAAQ,IAAI,CAAC,KAAK,MAAM;oBACxB,IAAI,IAAI,CAAC;gBACb,OAAO;oBACH,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,MAAM,EAAE,EAAE,EAAG;wBAChD,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,MAAM;4BAC1C,SAAS;wBACb;oBACJ;oBACA,IAAI,IAAI,CAAC;gBACb;YACJ;YACA,OAAO;QACX;IACJ;AAEJ;AAEA,IAAI,wBAAwB;AAC5B,SAAS,QAAQ,EAAE;IACf,IAAI;QACA,IAAI,OAAO,OAAO,YAAY;YAC1B,IAAI,OAAO,IAAI,KAAK,CAAC,GAAG,SAAS;YAEjC,IAAI,aAAa,IAAI,KAAK,IAAI,KAAK,MAAM,GAAG;YAC5C,IAAI,iCAAiC,KAAK,MAAM,GAAG,KAC/C,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,KAAK,aAAa;YACpD,IAAI,oCACA,sBAAsB,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,MAAM,GAAG;YAElE,IAAI,cAAc,kCACd,mCAAmC;gBACnC,OAAO;YACX;QACJ;QACA,OAAO;IACX,EAAE,OAAO,GAAG;QACR,OAAO;IACX;AACJ;AAEA,SAAS,iBAAiB,GAAG;IACzB,0BAA0B,GAC1B,SAAS,mBAAmB;IAC5B,gBAAgB,SAAS,GAAG;IAC5B,IAAI,IAAI;IACR,MAAO,IAAK,IAAI;IAChB,OAAO;;AAEX;AAEA,IAAI,SAAS;AACb,SAAS,aAAa,GAAG;IACrB,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,MAAM;IACtC,IAAI,MAAM,IAAI,MAAM;IACpB,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;QAC3B,GAAG,CAAC,EAAE,GAAG,SAAS,IAAI;IAC1B;IACA,OAAO;AACX;AAEA,SAAS,aAAa,GAAG;IACrB,IAAI;QACA,OAAO,MAAM;IACjB,EAAE,OAAO,GAAG;QACR,OAAO;IACX;AACJ;AAEA,SAAS,QAAQ,GAAG;IAChB,OAAO,QAAQ,QACR,OAAO,QAAQ,YACf,OAAO,IAAI,OAAO,KAAK,YACvB,OAAO,IAAI,IAAI,KAAK;AAC/B;AAEA,SAAS,+BAA+B,CAAC;IACrC,IAAI;QACA,kBAAkB,GAAG,iBAAiB;IAC1C,EACA,OAAM,QAAQ,CAAC;AACnB;AAEA,SAAS,wBAAwB,CAAC;IAC9B,IAAI,KAAK,MAAM,OAAO;IACtB,OAAQ,AAAC,aAAa,KAAK,CAAC,yBAAyB,CAAC,gBAAgB,IAClE,CAAC,CAAC,gBAAgB,KAAK;AAC/B;AAEA,SAAS,eAAe,GAAG;IACvB,OAAO,QAAQ,QAAQ,IAAI,kBAAkB,CAAC,KAAK;AACvD;AAEA,IAAI,oBAAoB,AAAC;IACrB,IAAI,CAAC,CAAC,WAAW,IAAI,OAAO,GAAG;QAC3B,OAAO,SAAS,KAAK;YACjB,IAAI,eAAe,QAAQ,OAAO;YAClC,IAAI;gBAAC,MAAM,IAAI,MAAM,aAAa;YAAQ,EAC1C,OAAM,KAAK;gBAAC,OAAO;YAAI;QAC3B;IACJ,OAAO;QACH,OAAO,SAAS,KAAK;YACjB,IAAI,eAAe,QAAQ,OAAO;YAClC,OAAO,IAAI,MAAM,aAAa;QAClC;IACJ;AACJ;AAEA,SAAS,YAAY,GAAG;IACpB,OAAO,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC;AAC5B;AAEA,SAAS,gBAAgB,IAAI,EAAE,EAAE,EAAE,MAAM;IACrC,IAAI,OAAO,IAAI,KAAK,CAAC;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAClC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,OAAO,MAAM;YACb,IAAI;gBACA,IAAI,cAAc,CAAC,IAAI,KAAK,IAAI,aAAa,CAAC,MAAM;YACxD,EAAE,OAAO,QAAQ,CAAC;QACtB;IACJ;AACJ;AAEA,IAAI,UAAU,SAAS,CAAC;IACpB,IAAI,IAAI,OAAO,CAAC,IAAI;QAChB,OAAO;IACX;IACA,OAAO;AACX;AAEA,IAAI,OAAO,WAAW,eAAe,OAAO,QAAQ,EAAE;IAClD,IAAI,YAAY,OAAO,MAAM,IAAI,KAAK,aAAa,SAAS,CAAC;QACzD,OAAO,MAAM,IAAI,CAAC;IACtB,IAAI,SAAS,CAAC;QACV,IAAI,MAAM,EAAE;QACZ,IAAI,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC;QAC3B,IAAI;QACJ,MAAO,CAAE,CAAC,WAAW,GAAG,IAAI,EAAE,EAAE,IAAI,CAAG;YACnC,IAAI,IAAI,CAAC,SAAS,KAAK;QAC3B;QACA,OAAO;IACX;IAEA,UAAU,SAAS,CAAC;QAChB,IAAI,IAAI,OAAO,CAAC,IAAI;YAChB,OAAO;QACX,OAAO,IAAI,KAAK,QAAQ,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,YAAY;YAC9D,OAAO,UAAU;QACrB;QACA,OAAO;IACX;AACJ;AAEA,IAAI,SAAS,OAAO,gKAAA,CAAA,UAAO,KAAK,eACxB,YAAY,gKAAA,CAAA,UAAO,EAAE,WAAW,OAAO;AAE/C,IAAI,kBAAkB,OAAO,gKAAA,CAAA,UAAO,KAAK,eACrC,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,KAAK;AAE3B,SAAS,IAAI,GAAG;IACZ,OAAO,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,IAAI,GAAG;AAChD;AAEA,SAAS;IACL,IAAI,OAAO,YAAY,YAAY;QAC/B,IAAI;YACA,IAAI,UAAU,IAAI,QAAQ,YAAW;YACrC,IAAI,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,oBAAoB;gBAClD,OAAO;YACX;QACJ,EAAE,OAAO,GAAG,CAAC;IACjB;AACJ;AAEA,SAAS,WAAW,KAAI,EAAE,EAAE;IACxB,OAAO,MAAK,IAAI,CAAC;AACrB;AAEA,IAAI,MAAM;IACN,SAAS;IACT,cAAc;IACd,mBAAmB;IACnB,0BAA0B;IAC1B,SAAS;IACT,SAAS,IAAI,OAAO;IACpB,SAAS;IACT,mBAAmB;IACnB,aAAa;IACb,UAAU;IACV,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;IACV,UAAU;IACV,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,mBAAmB;IACnB,yBAAyB;IACzB,gCAAgC;IAChC,aAAa;IACb,iBAAiB;IACjB,aAAa,OAAO,WAAW,eAAe,UACjC,OAAO,OAAO,SAAS,KAAK;IACzC,QAAQ;IACR,iBAAiB;IACjB,KAAK;IACL,QAAQ;IACR,kBAAkB;IAClB,YAAY;AAChB;AACA,IAAI,YAAY,GAAG,IAAI,MAAM,IAAI,AAAC;IAC9B,IAAI,UAAU,gKAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IACnD,OAAO,AAAC,OAAO,CAAC,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE,GAAG,MAAQ,OAAO,CAAC,EAAE,GAAG;AAClE;AAEA,IAAI,IAAI,MAAM,EAAE,IAAI,gBAAgB,CAAC,gKAAA,CAAA,UAAO;AAE5C,IAAI;IAAC,MAAM,IAAI;AAAS,EAAE,OAAO,GAAG;IAAC,IAAI,aAAa,GAAG;AAAE;AAC3D,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/schedule.js"], "sourcesContent": ["\"use strict\";\nvar util = require(\"./util\");\nvar schedule;\nvar noAsyncScheduler = function() {\n    throw new Error(\"No async scheduler available\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n};\nvar NativePromise = util.getNativePromise();\nif (util.isNode && typeof MutationObserver === \"undefined\") {\n    var GlobalSetImmediate = global.setImmediate;\n    var ProcessNextTick = process.nextTick;\n    schedule = util.isRecentNode\n                ? function(fn) { GlobalSetImmediate.call(global, fn); }\n                : function(fn) { ProcessNextTick.call(process, fn); };\n} else if (typeof NativePromise === \"function\" &&\n           typeof NativePromise.resolve === \"function\") {\n    var nativePromise = NativePromise.resolve();\n    schedule = function(fn) {\n        nativePromise.then(fn);\n    };\n} else if ((typeof MutationObserver !== \"undefined\") &&\n          !(typeof window !== \"undefined\" &&\n            window.navigator &&\n            (window.navigator.standalone || window.cordova))) {\n    schedule = (function() {\n        var div = document.createElement(\"div\");\n        var opts = {attributes: true};\n        var toggleScheduled = false;\n        var div2 = document.createElement(\"div\");\n        var o2 = new MutationObserver(function() {\n            div.classList.toggle(\"foo\");\n            toggleScheduled = false;\n        });\n        o2.observe(div2, opts);\n\n        var scheduleToggle = function() {\n            if (toggleScheduled) return;\n                toggleScheduled = true;\n                div2.classList.toggle(\"foo\");\n            };\n\n            return function schedule(fn) {\n            var o = new MutationObserver(function() {\n                o.disconnect();\n                fn();\n            });\n            o.observe(div, opts);\n            scheduleToggle();\n        };\n    })();\n} else if (typeof setImmediate !== \"undefined\") {\n    schedule = function (fn) {\n        setImmediate(fn);\n    };\n} else if (typeof setTimeout !== \"undefined\") {\n    schedule = function (fn) {\n        setTimeout(fn, 0);\n    };\n} else {\n    schedule = noAsyncScheduler;\n}\nmodule.exports = schedule;\n"], "names": [], "mappings": "AAS0B;AAT1B;AACA,IAAI;AACJ,IAAI;AACJ,IAAI,mBAAmB;IACnB,MAAM,IAAI,MAAM;AACpB;AACA,IAAI,gBAAgB,KAAK,gBAAgB;AACzC,IAAI,KAAK,MAAM,IAAI,OAAO,qBAAqB,aAAa;IACxD,IAAI,qBAAqB,OAAO,YAAY;IAC5C,IAAI,kBAAkB,gKAAA,CAAA,UAAO,CAAC,QAAQ;IACtC,WAAW,KAAK,YAAY,GACd,SAAS,EAAE;QAAI,mBAAmB,IAAI,CAAC,QAAQ;IAAK,IACpD,SAAS,EAAE;QAAI,gBAAgB,IAAI,CAAC,gKAAA,CAAA,UAAO,EAAE;IAAK;AACpE,OAAO,IAAI,OAAO,kBAAkB,cACzB,OAAO,cAAc,OAAO,KAAK,YAAY;IACpD,IAAI,gBAAgB,cAAc,OAAO;IACzC,WAAW,SAAS,EAAE;QAClB,cAAc,IAAI,CAAC;IACvB;AACJ,OAAO,IAAI,AAAC,OAAO,qBAAqB,eAC9B,CAAC,CAAC,OAAO,WAAW,eAClB,OAAO,SAAS,IAChB,CAAC,OAAO,SAAS,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,GAAG;IAC1D,WAAW,AAAC;QACR,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,OAAO;YAAC,YAAY;QAAI;QAC5B,IAAI,kBAAkB;QACtB,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,IAAI,KAAK,IAAI,iBAAiB;YAC1B,IAAI,SAAS,CAAC,MAAM,CAAC;YACrB,kBAAkB;QACtB;QACA,GAAG,OAAO,CAAC,MAAM;QAEjB,IAAI,iBAAiB;YACjB,IAAI,iBAAiB;YACjB,kBAAkB;YAClB,KAAK,SAAS,CAAC,MAAM,CAAC;QAC1B;QAEA,OAAO,SAAS,SAAS,EAAE;YAC3B,IAAI,IAAI,IAAI,iBAAiB;gBACzB,EAAE,UAAU;gBACZ;YACJ;YACA,EAAE,OAAO,CAAC,KAAK;YACf;QACJ;IACJ;AACJ,OAAO,IAAI,OAAO,iBAAiB,aAAa;IAC5C,WAAW,SAAU,EAAE;QACnB,aAAa;IACjB;AACJ,OAAO,IAAI,OAAO,eAAe,aAAa;IAC1C,WAAW,SAAU,EAAE;QACnB,WAAW,IAAI;IACnB;AACJ,OAAO;IACH,WAAW;AACf;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/queue.js"], "sourcesContent": ["\"use strict\";\nfunction arrayMove(src, srcIndex, dst, dstIndex, len) {\n    for (var j = 0; j < len; ++j) {\n        dst[j + dstIndex] = src[j + srcIndex];\n        src[j + srcIndex] = void 0;\n    }\n}\n\nfunction Queue(capacity) {\n    this._capacity = capacity;\n    this._length = 0;\n    this._front = 0;\n}\n\nQueue.prototype._willBeOverCapacity = function (size) {\n    return this._capacity < size;\n};\n\nQueue.prototype._pushOne = function (arg) {\n    var length = this.length();\n    this._checkCapacity(length + 1);\n    var i = (this._front + length) & (this._capacity - 1);\n    this[i] = arg;\n    this._length = length + 1;\n};\n\nQueue.prototype.push = function (fn, receiver, arg) {\n    var length = this.length() + 3;\n    if (this._willBeOverCapacity(length)) {\n        this._pushOne(fn);\n        this._pushOne(receiver);\n        this._pushOne(arg);\n        return;\n    }\n    var j = this._front + length - 3;\n    this._checkCapacity(length);\n    var wrapMask = this._capacity - 1;\n    this[(j + 0) & wrapMask] = fn;\n    this[(j + 1) & wrapMask] = receiver;\n    this[(j + 2) & wrapMask] = arg;\n    this._length = length;\n};\n\nQueue.prototype.shift = function () {\n    var front = this._front,\n        ret = this[front];\n\n    this[front] = undefined;\n    this._front = (front + 1) & (this._capacity - 1);\n    this._length--;\n    return ret;\n};\n\nQueue.prototype.length = function () {\n    return this._length;\n};\n\nQueue.prototype._checkCapacity = function (size) {\n    if (this._capacity < size) {\n        this._resizeTo(this._capacity << 1);\n    }\n};\n\nQueue.prototype._resizeTo = function (capacity) {\n    var oldCapacity = this._capacity;\n    this._capacity = capacity;\n    var front = this._front;\n    var length = this._length;\n    var moveItemsCount = (front + length) & (oldCapacity - 1);\n    arrayMove(this, 0, this, oldCapacity, moveItemsCount);\n};\n\nmodule.exports = Queue;\n"], "names": [], "mappings": "AAAA;AACA,SAAS,UAAU,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG;IAChD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC1B,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,CAAC,IAAI,SAAS;QACrC,GAAG,CAAC,IAAI,SAAS,GAAG,KAAK;IAC7B;AACJ;AAEA,SAAS,MAAM,QAAQ;IACnB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,MAAM,GAAG;AAClB;AAEA,MAAM,SAAS,CAAC,mBAAmB,GAAG,SAAU,IAAI;IAChD,OAAO,IAAI,CAAC,SAAS,GAAG;AAC5B;AAEA,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG;IACpC,IAAI,SAAS,IAAI,CAAC,MAAM;IACxB,IAAI,CAAC,cAAc,CAAC,SAAS;IAC7B,IAAI,IAAI,AAAC,IAAI,CAAC,MAAM,GAAG,SAAW,IAAI,CAAC,SAAS,GAAG;IACnD,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,OAAO,GAAG,SAAS;AAC5B;AAEA,MAAM,SAAS,CAAC,IAAI,GAAG,SAAU,EAAE,EAAE,QAAQ,EAAE,GAAG;IAC9C,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK;IAC7B,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAClC,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,QAAQ,CAAC;QACd;IACJ;IACA,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS;IAC/B,IAAI,CAAC,cAAc,CAAC;IACpB,IAAI,WAAW,IAAI,CAAC,SAAS,GAAG;IAChC,IAAI,CAAC,AAAC,IAAI,IAAK,SAAS,GAAG;IAC3B,IAAI,CAAC,AAAC,IAAI,IAAK,SAAS,GAAG;IAC3B,IAAI,CAAC,AAAC,IAAI,IAAK,SAAS,GAAG;IAC3B,IAAI,CAAC,OAAO,GAAG;AACnB;AAEA,MAAM,SAAS,CAAC,KAAK,GAAG;IACpB,IAAI,QAAQ,IAAI,CAAC,MAAM,EACnB,MAAM,IAAI,CAAC,MAAM;IAErB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG,AAAC,QAAQ,IAAM,IAAI,CAAC,SAAS,GAAG;IAC9C,IAAI,CAAC,OAAO;IACZ,OAAO;AACX;AAEA,MAAM,SAAS,CAAC,MAAM,GAAG;IACrB,OAAO,IAAI,CAAC,OAAO;AACvB;AAEA,MAAM,SAAS,CAAC,cAAc,GAAG,SAAU,IAAI;IAC3C,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,IAAI;IACrC;AACJ;AAEA,MAAM,SAAS,CAAC,SAAS,GAAG,SAAU,QAAQ;IAC1C,IAAI,cAAc,IAAI,CAAC,SAAS;IAChC,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,QAAQ,IAAI,CAAC,MAAM;IACvB,IAAI,SAAS,IAAI,CAAC,OAAO;IACzB,IAAI,iBAAiB,AAAC,QAAQ,SAAW,cAAc;IACvD,UAAU,IAAI,EAAE,GAAG,IAAI,EAAE,aAAa;AAC1C;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/async.js"], "sourcesContent": ["\"use strict\";\nvar firstLineError;\ntry {throw new Error(); } catch (e) {firstLineError = e;}\nvar schedule = require(\"./schedule\");\nvar Queue = require(\"./queue\");\nvar util = require(\"./util\");\n\nfunction Async() {\n    this._customScheduler = false;\n    this._isTickUsed = false;\n    this._lateQueue = new Queue(16);\n    this._normalQueue = new Queue(16);\n    this._haveDrainedQueues = false;\n    this._trampolineEnabled = true;\n    var self = this;\n    this.drainQueues = function () {\n        self._drainQueues();\n    };\n    this._schedule = schedule;\n}\n\nAsync.prototype.setScheduler = function(fn) {\n    var prev = this._schedule;\n    this._schedule = fn;\n    this._customScheduler = true;\n    return prev;\n};\n\nAsync.prototype.hasCustomScheduler = function() {\n    return this._customScheduler;\n};\n\nAsync.prototype.enableTrampoline = function() {\n    this._trampolineEnabled = true;\n};\n\nAsync.prototype.disableTrampolineIfNecessary = function() {\n    if (util.hasDevTools) {\n        this._trampolineEnabled = false;\n    }\n};\n\nAsync.prototype.haveItemsQueued = function () {\n    return this._isTickUsed || this._haveDrainedQueues;\n};\n\n\nAsync.prototype.fatalError = function(e, isNode) {\n    if (isNode) {\n        process.stderr.write(\"Fatal \" + (e instanceof Error ? e.stack : e) +\n            \"\\n\");\n        process.exit(2);\n    } else {\n        this.throwLater(e);\n    }\n};\n\nAsync.prototype.throwLater = function(fn, arg) {\n    if (arguments.length === 1) {\n        arg = fn;\n        fn = function () { throw arg; };\n    }\n    if (typeof setTimeout !== \"undefined\") {\n        setTimeout(function() {\n            fn(arg);\n        }, 0);\n    } else try {\n        this._schedule(function() {\n            fn(arg);\n        });\n    } catch (e) {\n        throw new Error(\"No async scheduler available\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n};\n\nfunction AsyncInvokeLater(fn, receiver, arg) {\n    this._lateQueue.push(fn, receiver, arg);\n    this._queueTick();\n}\n\nfunction AsyncInvoke(fn, receiver, arg) {\n    this._normalQueue.push(fn, receiver, arg);\n    this._queueTick();\n}\n\nfunction AsyncSettlePromises(promise) {\n    this._normalQueue._pushOne(promise);\n    this._queueTick();\n}\n\nif (!util.hasDevTools) {\n    Async.prototype.invokeLater = AsyncInvokeLater;\n    Async.prototype.invoke = AsyncInvoke;\n    Async.prototype.settlePromises = AsyncSettlePromises;\n} else {\n    Async.prototype.invokeLater = function (fn, receiver, arg) {\n        if (this._trampolineEnabled) {\n            AsyncInvokeLater.call(this, fn, receiver, arg);\n        } else {\n            this._schedule(function() {\n                setTimeout(function() {\n                    fn.call(receiver, arg);\n                }, 100);\n            });\n        }\n    };\n\n    Async.prototype.invoke = function (fn, receiver, arg) {\n        if (this._trampolineEnabled) {\n            AsyncInvoke.call(this, fn, receiver, arg);\n        } else {\n            this._schedule(function() {\n                fn.call(receiver, arg);\n            });\n        }\n    };\n\n    Async.prototype.settlePromises = function(promise) {\n        if (this._trampolineEnabled) {\n            AsyncSettlePromises.call(this, promise);\n        } else {\n            this._schedule(function() {\n                promise._settlePromises();\n            });\n        }\n    };\n}\n\nAsync.prototype._drainQueue = function(queue) {\n    while (queue.length() > 0) {\n        var fn = queue.shift();\n        if (typeof fn !== \"function\") {\n            fn._settlePromises();\n            continue;\n        }\n        var receiver = queue.shift();\n        var arg = queue.shift();\n        fn.call(receiver, arg);\n    }\n};\n\nAsync.prototype._drainQueues = function () {\n    this._drainQueue(this._normalQueue);\n    this._reset();\n    this._haveDrainedQueues = true;\n    this._drainQueue(this._lateQueue);\n};\n\nAsync.prototype._queueTick = function () {\n    if (!this._isTickUsed) {\n        this._isTickUsed = true;\n        this._schedule(this.drainQueues);\n    }\n};\n\nAsync.prototype._reset = function () {\n    this._isTickUsed = false;\n};\n\nmodule.exports = Async;\nmodule.exports.firstLineError = firstLineError;\n"], "names": [], "mappings": "AAiDQ;AAjDR;AACA,IAAI;AACJ,IAAI;IAAC,MAAM,IAAI;AAAS,EAAE,OAAO,GAAG;IAAC,iBAAiB;AAAE;AACxD,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,SAAS;IACL,IAAI,CAAC,gBAAgB,GAAG;IACxB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM;IAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM;IAC9B,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,OAAO,IAAI;IACf,IAAI,CAAC,WAAW,GAAG;QACf,KAAK,YAAY;IACrB;IACA,IAAI,CAAC,SAAS,GAAG;AACrB;AAEA,MAAM,SAAS,CAAC,YAAY,GAAG,SAAS,EAAE;IACtC,IAAI,OAAO,IAAI,CAAC,SAAS;IACzB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,gBAAgB,GAAG;IACxB,OAAO;AACX;AAEA,MAAM,SAAS,CAAC,kBAAkB,GAAG;IACjC,OAAO,IAAI,CAAC,gBAAgB;AAChC;AAEA,MAAM,SAAS,CAAC,gBAAgB,GAAG;IAC/B,IAAI,CAAC,kBAAkB,GAAG;AAC9B;AAEA,MAAM,SAAS,CAAC,4BAA4B,GAAG;IAC3C,IAAI,KAAK,WAAW,EAAE;QAClB,IAAI,CAAC,kBAAkB,GAAG;IAC9B;AACJ;AAEA,MAAM,SAAS,CAAC,eAAe,GAAG;IAC9B,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB;AACtD;AAGA,MAAM,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE,MAAM;IAC3C,IAAI,QAAQ;QACR,gKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,QAAQ,EAAE,KAAK,GAAG,CAAC,IAC7D;QACJ,gKAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACjB,OAAO;QACH,IAAI,CAAC,UAAU,CAAC;IACpB;AACJ;AAEA,MAAM,SAAS,CAAC,UAAU,GAAG,SAAS,EAAE,EAAE,GAAG;IACzC,IAAI,UAAU,MAAM,KAAK,GAAG;QACxB,MAAM;QACN,KAAK;YAAc,MAAM;QAAK;IAClC;IACA,IAAI,OAAO,eAAe,aAAa;QACnC,WAAW;YACP,GAAG;QACP,GAAG;IACP,OAAO,IAAI;QACP,IAAI,CAAC,SAAS,CAAC;YACX,GAAG;QACP;IACJ,EAAE,OAAO,GAAG;QACR,MAAM,IAAI,MAAM;IACpB;AACJ;AAEA,SAAS,iBAAiB,EAAE,EAAE,QAAQ,EAAE,GAAG;IACvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU;IACnC,IAAI,CAAC,UAAU;AACnB;AAEA,SAAS,YAAY,EAAE,EAAE,QAAQ,EAAE,GAAG;IAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU;IACrC,IAAI,CAAC,UAAU;AACnB;AAEA,SAAS,oBAAoB,OAAO;IAChC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IAC3B,IAAI,CAAC,UAAU;AACnB;AAEA,IAAI,CAAC,KAAK,WAAW,EAAE;IACnB,MAAM,SAAS,CAAC,WAAW,GAAG;IAC9B,MAAM,SAAS,CAAC,MAAM,GAAG;IACzB,MAAM,SAAS,CAAC,cAAc,GAAG;AACrC,OAAO;IACH,MAAM,SAAS,CAAC,WAAW,GAAG,SAAU,EAAE,EAAE,QAAQ,EAAE,GAAG;QACrD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,iBAAiB,IAAI,CAAC,IAAI,EAAE,IAAI,UAAU;QAC9C,OAAO;YACH,IAAI,CAAC,SAAS,CAAC;gBACX,WAAW;oBACP,GAAG,IAAI,CAAC,UAAU;gBACtB,GAAG;YACP;QACJ;IACJ;IAEA,MAAM,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,EAAE,QAAQ,EAAE,GAAG;QAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,YAAY,IAAI,CAAC,IAAI,EAAE,IAAI,UAAU;QACzC,OAAO;YACH,IAAI,CAAC,SAAS,CAAC;gBACX,GAAG,IAAI,CAAC,UAAU;YACtB;QACJ;IACJ;IAEA,MAAM,SAAS,CAAC,cAAc,GAAG,SAAS,OAAO;QAC7C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,oBAAoB,IAAI,CAAC,IAAI,EAAE;QACnC,OAAO;YACH,IAAI,CAAC,SAAS,CAAC;gBACX,QAAQ,eAAe;YAC3B;QACJ;IACJ;AACJ;AAEA,MAAM,SAAS,CAAC,WAAW,GAAG,SAAS,KAAK;IACxC,MAAO,MAAM,MAAM,KAAK,EAAG;QACvB,IAAI,KAAK,MAAM,KAAK;QACpB,IAAI,OAAO,OAAO,YAAY;YAC1B,GAAG,eAAe;YAClB;QACJ;QACA,IAAI,WAAW,MAAM,KAAK;QAC1B,IAAI,MAAM,MAAM,KAAK;QACrB,GAAG,IAAI,CAAC,UAAU;IACtB;AACJ;AAEA,MAAM,SAAS,CAAC,YAAY,GAAG;IAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY;IAClC,IAAI,CAAC,MAAM;IACX,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU;AACpC;AAEA,MAAM,SAAS,CAAC,UAAU,GAAG;IACzB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACnB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW;IACnC;AACJ;AAEA,MAAM,SAAS,CAAC,MAAM,GAAG;IACrB,IAAI,CAAC,WAAW,GAAG;AACvB;AAEA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/errors.js"], "sourcesContent": ["\"use strict\";\nvar es5 = require(\"./es5\");\nvar Objectfreeze = es5.freeze;\nvar util = require(\"./util\");\nvar inherits = util.inherits;\nvar notEnumerableProp = util.notEnumerableProp;\n\nfunction subError(nameProperty, defaultMessage) {\n    function SubError(message) {\n        if (!(this instanceof SubError)) return new SubError(message);\n        notEnumerableProp(this, \"message\",\n            typeof message === \"string\" ? message : defaultMessage);\n        notEnumerableProp(this, \"name\", nameProperty);\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        } else {\n            Error.call(this);\n        }\n    }\n    inherits(SubError, Error);\n    return SubError;\n}\n\nvar _TypeError, _RangeError;\nvar Warning = subError(\"Warning\", \"warning\");\nvar CancellationError = subError(\"CancellationError\", \"cancellation error\");\nvar TimeoutError = subError(\"TimeoutError\", \"timeout error\");\nvar AggregateError = subError(\"AggregateError\", \"aggregate error\");\ntry {\n    _TypeError = TypeError;\n    _RangeError = RangeError;\n} catch(e) {\n    _TypeError = subError(\"TypeError\", \"type error\");\n    _RangeError = subError(\"RangeError\", \"range error\");\n}\n\nvar methods = (\"join pop push shift unshift slice filter forEach some \" +\n    \"every map indexOf lastIndexOf reduce reduceRight sort reverse\").split(\" \");\n\nfor (var i = 0; i < methods.length; ++i) {\n    if (typeof Array.prototype[methods[i]] === \"function\") {\n        AggregateError.prototype[methods[i]] = Array.prototype[methods[i]];\n    }\n}\n\nes5.defineProperty(AggregateError.prototype, \"length\", {\n    value: 0,\n    configurable: false,\n    writable: true,\n    enumerable: true\n});\nAggregateError.prototype[\"isOperational\"] = true;\nvar level = 0;\nAggregateError.prototype.toString = function() {\n    var indent = Array(level * 4 + 1).join(\" \");\n    var ret = \"\\n\" + indent + \"AggregateError of:\" + \"\\n\";\n    level++;\n    indent = Array(level * 4 + 1).join(\" \");\n    for (var i = 0; i < this.length; ++i) {\n        var str = this[i] === this ? \"[Circular AggregateError]\" : this[i] + \"\";\n        var lines = str.split(\"\\n\");\n        for (var j = 0; j < lines.length; ++j) {\n            lines[j] = indent + lines[j];\n        }\n        str = lines.join(\"\\n\");\n        ret += str + \"\\n\";\n    }\n    level--;\n    return ret;\n};\n\nfunction OperationalError(message) {\n    if (!(this instanceof OperationalError))\n        return new OperationalError(message);\n    notEnumerableProp(this, \"name\", \"OperationalError\");\n    notEnumerableProp(this, \"message\", message);\n    this.cause = message;\n    this[\"isOperational\"] = true;\n\n    if (message instanceof Error) {\n        notEnumerableProp(this, \"message\", message.message);\n        notEnumerableProp(this, \"stack\", message.stack);\n    } else if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n\n}\ninherits(OperationalError, Error);\n\nvar errorTypes = Error[\"__BluebirdErrorTypes__\"];\nif (!errorTypes) {\n    errorTypes = Objectfreeze({\n        CancellationError: CancellationError,\n        TimeoutError: TimeoutError,\n        OperationalError: OperationalError,\n        RejectionError: OperationalError,\n        AggregateError: AggregateError\n    });\n    es5.defineProperty(Error, \"__BluebirdErrorTypes__\", {\n        value: errorTypes,\n        writable: false,\n        enumerable: false,\n        configurable: false\n    });\n}\n\nmodule.exports = {\n    Error: Error,\n    TypeError: _TypeError,\n    RangeError: _RangeError,\n    CancellationError: errorTypes.CancellationError,\n    OperationalError: errorTypes.OperationalError,\n    TimeoutError: errorTypes.TimeoutError,\n    AggregateError: errorTypes.AggregateError,\n    Warning: Warning\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI,eAAe,IAAI,MAAM;AAC7B,IAAI;AACJ,IAAI,WAAW,KAAK,QAAQ;AAC5B,IAAI,oBAAoB,KAAK,iBAAiB;AAE9C,SAAS,SAAS,YAAY,EAAE,cAAc;IAC1C,SAAS,SAAS,OAAO;QACrB,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG,OAAO,IAAI,SAAS;QACrD,kBAAkB,IAAI,EAAE,WACpB,OAAO,YAAY,WAAW,UAAU;QAC5C,kBAAkB,IAAI,EAAE,QAAQ;QAChC,IAAI,MAAM,iBAAiB,EAAE;YACzB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAClD,OAAO;YACH,MAAM,IAAI,CAAC,IAAI;QACnB;IACJ;IACA,SAAS,UAAU;IACnB,OAAO;AACX;AAEA,IAAI,YAAY;AAChB,IAAI,UAAU,SAAS,WAAW;AAClC,IAAI,oBAAoB,SAAS,qBAAqB;AACtD,IAAI,eAAe,SAAS,gBAAgB;AAC5C,IAAI,iBAAiB,SAAS,kBAAkB;AAChD,IAAI;IACA,aAAa;IACb,cAAc;AAClB,EAAE,OAAM,GAAG;IACP,aAAa,SAAS,aAAa;IACnC,cAAc,SAAS,cAAc;AACzC;AAEA,IAAI,UAAU,CAAC,2DACX,+DAA+D,EAAE,KAAK,CAAC;AAE3E,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;IACrC,IAAI,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,YAAY;QACnD,eAAe,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;IACtE;AACJ;AAEA,IAAI,cAAc,CAAC,eAAe,SAAS,EAAE,UAAU;IACnD,OAAO;IACP,cAAc;IACd,UAAU;IACV,YAAY;AAChB;AACA,eAAe,SAAS,CAAC,gBAAgB,GAAG;AAC5C,IAAI,QAAQ;AACZ,eAAe,SAAS,CAAC,QAAQ,GAAG;IAChC,IAAI,SAAS,MAAM,QAAQ,IAAI,GAAG,IAAI,CAAC;IACvC,IAAI,MAAM,OAAO,SAAS,uBAAuB;IACjD;IACA,SAAS,MAAM,QAAQ,IAAI,GAAG,IAAI,CAAC;IACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,EAAG;QAClC,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,IAAI,GAAG,8BAA8B,IAAI,CAAC,EAAE,GAAG;QACrE,IAAI,QAAQ,IAAI,KAAK,CAAC;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE;QAChC;QACA,MAAM,MAAM,IAAI,CAAC;QACjB,OAAO,MAAM;IACjB;IACA;IACA,OAAO;AACX;AAEA,SAAS,iBAAiB,OAAO;IAC7B,IAAI,CAAC,CAAC,IAAI,YAAY,gBAAgB,GAClC,OAAO,IAAI,iBAAiB;IAChC,kBAAkB,IAAI,EAAE,QAAQ;IAChC,kBAAkB,IAAI,EAAE,WAAW;IACnC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,gBAAgB,GAAG;IAExB,IAAI,mBAAmB,OAAO;QAC1B,kBAAkB,IAAI,EAAE,WAAW,QAAQ,OAAO;QAClD,kBAAkB,IAAI,EAAE,SAAS,QAAQ,KAAK;IAClD,OAAO,IAAI,MAAM,iBAAiB,EAAE;QAChC,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAClD;AAEJ;AACA,SAAS,kBAAkB;AAE3B,IAAI,aAAa,KAAK,CAAC,yBAAyB;AAChD,IAAI,CAAC,YAAY;IACb,aAAa,aAAa;QACtB,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;IACpB;IACA,IAAI,cAAc,CAAC,OAAO,0BAA0B;QAChD,OAAO;QACP,UAAU;QACV,YAAY;QACZ,cAAc;IAClB;AACJ;AAEA,OAAO,OAAO,GAAG;IACb,OAAO;IACP,WAAW;IACX,YAAY;IACZ,mBAAmB,WAAW,iBAAiB;IAC/C,kBAAkB,WAAW,gBAAgB;IAC7C,cAAc,WAAW,YAAY;IACrC,gBAAgB,WAAW,cAAc;IACzC,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/thenables.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL) {\nvar util = require(\"./util\");\nvar errorObj = util.errorObj;\nvar isObject = util.isObject;\n\nfunction tryConvertToPromise(obj, context) {\n    if (isObject(obj)) {\n        if (obj instanceof Promise) return obj;\n        var then = getThen(obj);\n        if (then === errorObj) {\n            if (context) context._pushContext();\n            var ret = Promise.reject(then.e);\n            if (context) context._popContext();\n            return ret;\n        } else if (typeof then === \"function\") {\n            if (isAnyBluebirdPromise(obj)) {\n                var ret = new Promise(INTERNAL);\n                obj._then(\n                    ret._fulfill,\n                    ret._reject,\n                    undefined,\n                    ret,\n                    null\n                );\n                return ret;\n            }\n            return doThenable(obj, then, context);\n        }\n    }\n    return obj;\n}\n\nfunction doGetThen(obj) {\n    return obj.then;\n}\n\nfunction getThen(obj) {\n    try {\n        return doGetThen(obj);\n    } catch (e) {\n        errorObj.e = e;\n        return errorObj;\n    }\n}\n\nvar hasProp = {}.hasOwnProperty;\nfunction isAnyBluebirdPromise(obj) {\n    try {\n        return hasProp.call(obj, \"_promise0\");\n    } catch (e) {\n        return false;\n    }\n}\n\nfunction doThenable(x, then, context) {\n    var promise = new Promise(INTERNAL);\n    var ret = promise;\n    if (context) context._pushContext();\n    promise._captureStackTrace();\n    if (context) context._popContext();\n    var synchronous = true;\n    var result = util.tryCatch(then).call(x, resolve, reject);\n    synchronous = false;\n\n    if (promise && result === errorObj) {\n        promise._rejectCallback(result.e, true, true);\n        promise = null;\n    }\n\n    function resolve(value) {\n        if (!promise) return;\n        promise._resolveCallback(value);\n        promise = null;\n    }\n\n    function reject(reason) {\n        if (!promise) return;\n        promise._rejectCallback(reason, synchronous, true);\n        promise = null;\n    }\n    return ret;\n}\n\nreturn tryConvertToPromise;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ;IAC3C,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAE5B,SAAS,oBAAoB,GAAG,EAAE,OAAO;QACrC,IAAI,SAAS,MAAM;YACf,IAAI,eAAe,SAAS,OAAO;YACnC,IAAI,OAAO,QAAQ;YACnB,IAAI,SAAS,UAAU;gBACnB,IAAI,SAAS,QAAQ,YAAY;gBACjC,IAAI,MAAM,QAAQ,MAAM,CAAC,KAAK,CAAC;gBAC/B,IAAI,SAAS,QAAQ,WAAW;gBAChC,OAAO;YACX,OAAO,IAAI,OAAO,SAAS,YAAY;gBACnC,IAAI,qBAAqB,MAAM;oBAC3B,IAAI,MAAM,IAAI,QAAQ;oBACtB,IAAI,KAAK,CACL,IAAI,QAAQ,EACZ,IAAI,OAAO,EACX,WACA,KACA;oBAEJ,OAAO;gBACX;gBACA,OAAO,WAAW,KAAK,MAAM;YACjC;QACJ;QACA,OAAO;IACX;IAEA,SAAS,UAAU,GAAG;QAClB,OAAO,IAAI,IAAI;IACnB;IAEA,SAAS,QAAQ,GAAG;QAChB,IAAI;YACA,OAAO,UAAU;QACrB,EAAE,OAAO,GAAG;YACR,SAAS,CAAC,GAAG;YACb,OAAO;QACX;IACJ;IAEA,IAAI,UAAU,CAAC,EAAE,cAAc;IAC/B,SAAS,qBAAqB,GAAG;QAC7B,IAAI;YACA,OAAO,QAAQ,IAAI,CAAC,KAAK;QAC7B,EAAE,OAAO,GAAG;YACR,OAAO;QACX;IACJ;IAEA,SAAS,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO;QAChC,IAAI,UAAU,IAAI,QAAQ;QAC1B,IAAI,MAAM;QACV,IAAI,SAAS,QAAQ,YAAY;QACjC,QAAQ,kBAAkB;QAC1B,IAAI,SAAS,QAAQ,WAAW;QAChC,IAAI,cAAc;QAClB,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS;QAClD,cAAc;QAEd,IAAI,WAAW,WAAW,UAAU;YAChC,QAAQ,eAAe,CAAC,OAAO,CAAC,EAAE,MAAM;YACxC,UAAU;QACd;QAEA,SAAS,QAAQ,KAAK;YAClB,IAAI,CAAC,SAAS;YACd,QAAQ,gBAAgB,CAAC;YACzB,UAAU;QACd;QAEA,SAAS,OAAO,MAAM;YAClB,IAAI,CAAC,SAAS;YACd,QAAQ,eAAe,CAAC,QAAQ,aAAa;YAC7C,UAAU;QACd;QACA,OAAO;IACX;IAEA,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/promise_array.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL, tryConvertToPromise,\n    apiRejection, Proxyable) {\nvar util = require(\"./util\");\nvar isArray = util.isArray;\n\nfunction toResolutionValue(val) {\n    switch(val) {\n    case -2: return [];\n    case -3: return {};\n    }\n}\n\nfunction PromiseArray(values) {\n    var promise = this._promise = new Promise(INTERNAL);\n    if (values instanceof Promise) {\n        promise._propagateFrom(values, 3);\n    }\n    promise._setOnCancel(this);\n    this._values = values;\n    this._length = 0;\n    this._totalResolved = 0;\n    this._init(undefined, -2);\n}\nutil.inherits(PromiseArray, Proxyable);\n\nPromiseArray.prototype.length = function () {\n    return this._length;\n};\n\nPromiseArray.prototype.promise = function () {\n    return this._promise;\n};\n\nPromiseArray.prototype._init = function init(_, resolveValueIfEmpty) {\n    var values = tryConvertToPromise(this._values, this._promise);\n    if (values instanceof Promise) {\n        values = values._target();\n        var bitField = values._bitField;\n        ;\n        this._values = values;\n\n        if (((bitField & 50397184) === 0)) {\n            this._promise._setAsyncGuaranteed();\n            return values._then(\n                init,\n                this._reject,\n                undefined,\n                this,\n                resolveValueIfEmpty\n           );\n        } else if (((bitField & 33554432) !== 0)) {\n            values = values._value();\n        } else if (((bitField & 16777216) !== 0)) {\n            return this._reject(values._reason());\n        } else {\n            return this._cancel();\n        }\n    }\n    values = util.asArray(values);\n    if (values === null) {\n        var err = apiRejection(\n            \"expecting an array or an iterable object but got \" + util.classString(values)).reason();\n        this._promise._rejectCallback(err, false);\n        return;\n    }\n\n    if (values.length === 0) {\n        if (resolveValueIfEmpty === -5) {\n            this._resolveEmptyArray();\n        }\n        else {\n            this._resolve(toResolutionValue(resolveValueIfEmpty));\n        }\n        return;\n    }\n    this._iterate(values);\n};\n\nPromiseArray.prototype._iterate = function(values) {\n    var len = this.getActualLength(values.length);\n    this._length = len;\n    this._values = this.shouldCopyValues() ? new Array(len) : this._values;\n    var result = this._promise;\n    var isResolved = false;\n    var bitField = null;\n    for (var i = 0; i < len; ++i) {\n        var maybePromise = tryConvertToPromise(values[i], result);\n\n        if (maybePromise instanceof Promise) {\n            maybePromise = maybePromise._target();\n            bitField = maybePromise._bitField;\n        } else {\n            bitField = null;\n        }\n\n        if (isResolved) {\n            if (bitField !== null) {\n                maybePromise.suppressUnhandledRejections();\n            }\n        } else if (bitField !== null) {\n            if (((bitField & 50397184) === 0)) {\n                maybePromise._proxy(this, i);\n                this._values[i] = maybePromise;\n            } else if (((bitField & 33554432) !== 0)) {\n                isResolved = this._promiseFulfilled(maybePromise._value(), i);\n            } else if (((bitField & 16777216) !== 0)) {\n                isResolved = this._promiseRejected(maybePromise._reason(), i);\n            } else {\n                isResolved = this._promiseCancelled(i);\n            }\n        } else {\n            isResolved = this._promiseFulfilled(maybePromise, i);\n        }\n    }\n    if (!isResolved) result._setAsyncGuaranteed();\n};\n\nPromiseArray.prototype._isResolved = function () {\n    return this._values === null;\n};\n\nPromiseArray.prototype._resolve = function (value) {\n    this._values = null;\n    this._promise._fulfill(value);\n};\n\nPromiseArray.prototype._cancel = function() {\n    if (this._isResolved() || !this._promise._isCancellable()) return;\n    this._values = null;\n    this._promise._cancel();\n};\n\nPromiseArray.prototype._reject = function (reason) {\n    this._values = null;\n    this._promise._rejectCallback(reason, false);\n};\n\nPromiseArray.prototype._promiseFulfilled = function (value, index) {\n    this._values[index] = value;\n    var totalResolved = ++this._totalResolved;\n    if (totalResolved >= this._length) {\n        this._resolve(this._values);\n        return true;\n    }\n    return false;\n};\n\nPromiseArray.prototype._promiseCancelled = function() {\n    this._cancel();\n    return true;\n};\n\nPromiseArray.prototype._promiseRejected = function (reason) {\n    this._totalResolved++;\n    this._reject(reason);\n    return true;\n};\n\nPromiseArray.prototype._resultCancelled = function() {\n    if (this._isResolved()) return;\n    var values = this._values;\n    this._cancel();\n    if (values instanceof Promise) {\n        values.cancel();\n    } else {\n        for (var i = 0; i < values.length; ++i) {\n            if (values[i] instanceof Promise) {\n                values[i].cancel();\n            }\n        }\n    }\n};\n\nPromiseArray.prototype.shouldCopyValues = function () {\n    return true;\n};\n\nPromiseArray.prototype.getActualLength = function (len) {\n    return len;\n};\n\nreturn PromiseArray;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAC5D,YAAY,EAAE,SAAS;IAC3B,IAAI;IACJ,IAAI,UAAU,KAAK,OAAO;IAE1B,SAAS,kBAAkB,GAAG;QAC1B,OAAO;YACP,KAAK,CAAC;gBAAG,OAAO,EAAE;YAClB,KAAK,CAAC;gBAAG,OAAO,CAAC;QACjB;IACJ;IAEA,SAAS,aAAa,MAAM;QACxB,IAAI,UAAU,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QAC1C,IAAI,kBAAkB,SAAS;YAC3B,QAAQ,cAAc,CAAC,QAAQ;QACnC;QACA,QAAQ,YAAY,CAAC,IAAI;QACzB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAC3B;IACA,KAAK,QAAQ,CAAC,cAAc;IAE5B,aAAa,SAAS,CAAC,MAAM,GAAG;QAC5B,OAAO,IAAI,CAAC,OAAO;IACvB;IAEA,aAAa,SAAS,CAAC,OAAO,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ;IACxB;IAEA,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,CAAC,EAAE,mBAAmB;QAC/D,IAAI,SAAS,oBAAoB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ;QAC5D,IAAI,kBAAkB,SAAS;YAC3B,SAAS,OAAO,OAAO;YACvB,IAAI,WAAW,OAAO,SAAS;;YAE/B,IAAI,CAAC,OAAO,GAAG;YAEf,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBAC/B,IAAI,CAAC,QAAQ,CAAC,mBAAmB;gBACjC,OAAO,OAAO,KAAK,CACf,MACA,IAAI,CAAC,OAAO,EACZ,WACA,IAAI,EACJ;YAER,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBACtC,SAAS,OAAO,MAAM;YAC1B,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBACtC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,OAAO;YACtC,OAAO;gBACH,OAAO,IAAI,CAAC,OAAO;YACvB;QACJ;QACA,SAAS,KAAK,OAAO,CAAC;QACtB,IAAI,WAAW,MAAM;YACjB,IAAI,MAAM,aACN,sDAAsD,KAAK,WAAW,CAAC,SAAS,MAAM;YAC1F,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK;YACnC;QACJ;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACrB,IAAI,wBAAwB,CAAC,GAAG;gBAC5B,IAAI,CAAC,kBAAkB;YAC3B,OACK;gBACD,IAAI,CAAC,QAAQ,CAAC,kBAAkB;YACpC;YACA;QACJ;QACA,IAAI,CAAC,QAAQ,CAAC;IAClB;IAEA,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAS,MAAM;QAC7C,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,MAAM;QAC5C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,MAAM,OAAO,IAAI,CAAC,OAAO;QACtE,IAAI,SAAS,IAAI,CAAC,QAAQ;QAC1B,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YAC1B,IAAI,eAAe,oBAAoB,MAAM,CAAC,EAAE,EAAE;YAElD,IAAI,wBAAwB,SAAS;gBACjC,eAAe,aAAa,OAAO;gBACnC,WAAW,aAAa,SAAS;YACrC,OAAO;gBACH,WAAW;YACf;YAEA,IAAI,YAAY;gBACZ,IAAI,aAAa,MAAM;oBACnB,aAAa,2BAA2B;gBAC5C;YACJ,OAAO,IAAI,aAAa,MAAM;gBAC1B,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBAC/B,aAAa,MAAM,CAAC,IAAI,EAAE;oBAC1B,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;gBACtB,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBACtC,aAAa,IAAI,CAAC,iBAAiB,CAAC,aAAa,MAAM,IAAI;gBAC/D,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBACtC,aAAa,IAAI,CAAC,gBAAgB,CAAC,aAAa,OAAO,IAAI;gBAC/D,OAAO;oBACH,aAAa,IAAI,CAAC,iBAAiB,CAAC;gBACxC;YACJ,OAAO;gBACH,aAAa,IAAI,CAAC,iBAAiB,CAAC,cAAc;YACtD;QACJ;QACA,IAAI,CAAC,YAAY,OAAO,mBAAmB;IAC/C;IAEA,aAAa,SAAS,CAAC,WAAW,GAAG;QACjC,OAAO,IAAI,CAAC,OAAO,KAAK;IAC5B;IAEA,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QAC7C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAC3B;IAEA,aAAa,SAAS,CAAC,OAAO,GAAG;QAC7B,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI;QAC3D,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC,OAAO;IACzB;IAEA,aAAa,SAAS,CAAC,OAAO,GAAG,SAAU,MAAM;QAC7C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ;IAC1C;IAEA,aAAa,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,KAAK;QAC7D,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACtB,IAAI,gBAAgB,EAAE,IAAI,CAAC,cAAc;QACzC,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC1B,OAAO;QACX;QACA,OAAO;IACX;IAEA,aAAa,SAAS,CAAC,iBAAiB,GAAG;QACvC,IAAI,CAAC,OAAO;QACZ,OAAO;IACX;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM;QACtD,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,OAAO,CAAC;QACb,OAAO;IACX;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,IAAI,CAAC,WAAW,IAAI;QACxB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,CAAC,OAAO;QACZ,IAAI,kBAAkB,SAAS;YAC3B,OAAO,MAAM;QACjB,OAAO;YACH,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;gBACpC,IAAI,MAAM,CAAC,EAAE,YAAY,SAAS;oBAC9B,MAAM,CAAC,EAAE,CAAC,MAAM;gBACpB;YACJ;QACJ;IACJ;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,OAAO;IACX;IAEA,aAAa,SAAS,CAAC,eAAe,GAAG,SAAU,GAAG;QAClD,OAAO;IACX;IAEA,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/context.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise) {\nvar longStackTraces = false;\nvar contextStack = [];\n\nPromise.prototype._promiseCreated = function() {};\nPromise.prototype._pushContext = function() {};\nPromise.prototype._popContext = function() {return null;};\nPromise._peekContext = Promise.prototype._peekContext = function() {};\n\nfunction Context() {\n    this._trace = new Context.CapturedTrace(peekContext());\n}\nContext.prototype._pushContext = function () {\n    if (this._trace !== undefined) {\n        this._trace._promiseCreated = null;\n        contextStack.push(this._trace);\n    }\n};\n\nContext.prototype._popContext = function () {\n    if (this._trace !== undefined) {\n        var trace = contextStack.pop();\n        var ret = trace._promiseCreated;\n        trace._promiseCreated = null;\n        return ret;\n    }\n    return null;\n};\n\nfunction createContext() {\n    if (longStackTraces) return new Context();\n}\n\nfunction peekContext() {\n    var lastIndex = contextStack.length - 1;\n    if (lastIndex >= 0) {\n        return contextStack[lastIndex];\n    }\n    return undefined;\n}\nContext.CapturedTrace = null;\nContext.create = createContext;\nContext.deactivateLongStackTraces = function() {};\nContext.activateLongStackTraces = function() {\n    var Promise_pushContext = Promise.prototype._pushContext;\n    var Promise_popContext = Promise.prototype._popContext;\n    var Promise_PeekContext = Promise._peekContext;\n    var Promise_peekContext = Promise.prototype._peekContext;\n    var Promise_promiseCreated = Promise.prototype._promiseCreated;\n    Context.deactivateLongStackTraces = function() {\n        Promise.prototype._pushContext = Promise_pushContext;\n        Promise.prototype._popContext = Promise_popContext;\n        Promise._peekContext = Promise_PeekContext;\n        Promise.prototype._peekContext = Promise_peekContext;\n        Promise.prototype._promiseCreated = Promise_promiseCreated;\n        longStackTraces = false;\n    };\n    longStackTraces = true;\n    Promise.prototype._pushContext = Context.prototype._pushContext;\n    Promise.prototype._popContext = Context.prototype._popContext;\n    Promise._peekContext = Promise.prototype._peekContext = peekContext;\n    Promise.prototype._promiseCreated = function() {\n        var ctx = this._peekContext();\n        if (ctx && ctx._promiseCreated == null) ctx._promiseCreated = this;\n    };\n};\nreturn Context;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,IAAI,kBAAkB;IACtB,IAAI,eAAe,EAAE;IAErB,QAAQ,SAAS,CAAC,eAAe,GAAG,YAAY;IAChD,QAAQ,SAAS,CAAC,YAAY,GAAG,YAAY;IAC7C,QAAQ,SAAS,CAAC,WAAW,GAAG;QAAY,OAAO;IAAK;IACxD,QAAQ,YAAY,GAAG,QAAQ,SAAS,CAAC,YAAY,GAAG,YAAY;IAEpE,SAAS;QACL,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,aAAa,CAAC;IAC5C;IACA,QAAQ,SAAS,CAAC,YAAY,GAAG;QAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;YAC3B,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG;YAC9B,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM;QACjC;IACJ;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;YAC3B,IAAI,QAAQ,aAAa,GAAG;YAC5B,IAAI,MAAM,MAAM,eAAe;YAC/B,MAAM,eAAe,GAAG;YACxB,OAAO;QACX;QACA,OAAO;IACX;IAEA,SAAS;QACL,IAAI,iBAAiB,OAAO,IAAI;IACpC;IAEA,SAAS;QACL,IAAI,YAAY,aAAa,MAAM,GAAG;QACtC,IAAI,aAAa,GAAG;YAChB,OAAO,YAAY,CAAC,UAAU;QAClC;QACA,OAAO;IACX;IACA,QAAQ,aAAa,GAAG;IACxB,QAAQ,MAAM,GAAG;IACjB,QAAQ,yBAAyB,GAAG,YAAY;IAChD,QAAQ,uBAAuB,GAAG;QAC9B,IAAI,sBAAsB,QAAQ,SAAS,CAAC,YAAY;QACxD,IAAI,qBAAqB,QAAQ,SAAS,CAAC,WAAW;QACtD,IAAI,sBAAsB,QAAQ,YAAY;QAC9C,IAAI,sBAAsB,QAAQ,SAAS,CAAC,YAAY;QACxD,IAAI,yBAAyB,QAAQ,SAAS,CAAC,eAAe;QAC9D,QAAQ,yBAAyB,GAAG;YAChC,QAAQ,SAAS,CAAC,YAAY,GAAG;YACjC,QAAQ,SAAS,CAAC,WAAW,GAAG;YAChC,QAAQ,YAAY,GAAG;YACvB,QAAQ,SAAS,CAAC,YAAY,GAAG;YACjC,QAAQ,SAAS,CAAC,eAAe,GAAG;YACpC,kBAAkB;QACtB;QACA,kBAAkB;QAClB,QAAQ,SAAS,CAAC,YAAY,GAAG,QAAQ,SAAS,CAAC,YAAY;QAC/D,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,SAAS,CAAC,WAAW;QAC7D,QAAQ,YAAY,GAAG,QAAQ,SAAS,CAAC,YAAY,GAAG;QACxD,QAAQ,SAAS,CAAC,eAAe,GAAG;YAChC,IAAI,MAAM,IAAI,CAAC,YAAY;YAC3B,IAAI,OAAO,IAAI,eAAe,IAAI,MAAM,IAAI,eAAe,GAAG,IAAI;QACtE;IACJ;IACA,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/debuggability.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, Context) {\nvar getDomain = Promise._getDomain;\nvar async = Promise._async;\nvar Warning = require(\"./errors\").Warning;\nvar util = require(\"./util\");\nvar canAttachTrace = util.canAttachTrace;\nvar unhandledRejectionHandled;\nvar possiblyUnhandledRejection;\nvar bluebirdFramePattern =\n    /[\\\\\\/]bluebird[\\\\\\/]js[\\\\\\/](release|debug|instrumented)/;\nvar nodeFramePattern = /\\((?:timers\\.js):\\d+:\\d+\\)/;\nvar parseLinePattern = /[\\/<\\(](.+?):(\\d+):(\\d+)\\)?\\s*$/;\nvar stackFramePattern = null;\nvar formatStack = null;\nvar indentStackFrames = false;\nvar printWarning;\nvar debugging = !!(util.env(\"BLUEBIRD_DEBUG\") != 0 &&\n                        (false ||\n                         util.env(\"BLUEBIRD_DEBUG\") ||\n                         util.env(\"NODE_ENV\") === \"development\"));\n\nvar warnings = !!(util.env(\"BLUEBIRD_WARNINGS\") != 0 &&\n    (debugging || util.env(\"BLUEBIRD_WARNINGS\")));\n\nvar longStackTraces = !!(util.env(\"BLUEBIRD_LONG_STACK_TRACES\") != 0 &&\n    (debugging || util.env(\"BLUEBIRD_LONG_STACK_TRACES\")));\n\nvar wForgottenReturn = util.env(\"BLUEBIRD_W_FORGOTTEN_RETURN\") != 0 &&\n    (warnings || !!util.env(\"BLUEBIRD_W_FORGOTTEN_RETURN\"));\n\nPromise.prototype.suppressUnhandledRejections = function() {\n    var target = this._target();\n    target._bitField = ((target._bitField & (~1048576)) |\n                      524288);\n};\n\nPromise.prototype._ensurePossibleRejectionHandled = function () {\n    if ((this._bitField & 524288) !== 0) return;\n    this._setRejectionIsUnhandled();\n    async.invokeLater(this._notifyUnhandledRejection, this, undefined);\n};\n\nPromise.prototype._notifyUnhandledRejectionIsHandled = function () {\n    fireRejectionEvent(\"rejectionHandled\",\n                                  unhandledRejectionHandled, undefined, this);\n};\n\nPromise.prototype._setReturnedNonUndefined = function() {\n    this._bitField = this._bitField | 268435456;\n};\n\nPromise.prototype._returnedNonUndefined = function() {\n    return (this._bitField & 268435456) !== 0;\n};\n\nPromise.prototype._notifyUnhandledRejection = function () {\n    if (this._isRejectionUnhandled()) {\n        var reason = this._settledValue();\n        this._setUnhandledRejectionIsNotified();\n        fireRejectionEvent(\"unhandledRejection\",\n                                      possiblyUnhandledRejection, reason, this);\n    }\n};\n\nPromise.prototype._setUnhandledRejectionIsNotified = function () {\n    this._bitField = this._bitField | 262144;\n};\n\nPromise.prototype._unsetUnhandledRejectionIsNotified = function () {\n    this._bitField = this._bitField & (~262144);\n};\n\nPromise.prototype._isUnhandledRejectionNotified = function () {\n    return (this._bitField & 262144) > 0;\n};\n\nPromise.prototype._setRejectionIsUnhandled = function () {\n    this._bitField = this._bitField | 1048576;\n};\n\nPromise.prototype._unsetRejectionIsUnhandled = function () {\n    this._bitField = this._bitField & (~1048576);\n    if (this._isUnhandledRejectionNotified()) {\n        this._unsetUnhandledRejectionIsNotified();\n        this._notifyUnhandledRejectionIsHandled();\n    }\n};\n\nPromise.prototype._isRejectionUnhandled = function () {\n    return (this._bitField & 1048576) > 0;\n};\n\nPromise.prototype._warn = function(message, shouldUseOwnTrace, promise) {\n    return warn(message, shouldUseOwnTrace, promise || this);\n};\n\nPromise.onPossiblyUnhandledRejection = function (fn) {\n    var domain = getDomain();\n    possiblyUnhandledRejection =\n        typeof fn === \"function\" ? (domain === null ?\n                                            fn : util.domainBind(domain, fn))\n                                 : undefined;\n};\n\nPromise.onUnhandledRejectionHandled = function (fn) {\n    var domain = getDomain();\n    unhandledRejectionHandled =\n        typeof fn === \"function\" ? (domain === null ?\n                                            fn : util.domainBind(domain, fn))\n                                 : undefined;\n};\n\nvar disableLongStackTraces = function() {};\nPromise.longStackTraces = function () {\n    if (async.haveItemsQueued() && !config.longStackTraces) {\n        throw new Error(\"cannot enable long stack traces after promises have been created\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    if (!config.longStackTraces && longStackTracesIsSupported()) {\n        var Promise_captureStackTrace = Promise.prototype._captureStackTrace;\n        var Promise_attachExtraTrace = Promise.prototype._attachExtraTrace;\n        config.longStackTraces = true;\n        disableLongStackTraces = function() {\n            if (async.haveItemsQueued() && !config.longStackTraces) {\n                throw new Error(\"cannot enable long stack traces after promises have been created\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n            }\n            Promise.prototype._captureStackTrace = Promise_captureStackTrace;\n            Promise.prototype._attachExtraTrace = Promise_attachExtraTrace;\n            Context.deactivateLongStackTraces();\n            async.enableTrampoline();\n            config.longStackTraces = false;\n        };\n        Promise.prototype._captureStackTrace = longStackTracesCaptureStackTrace;\n        Promise.prototype._attachExtraTrace = longStackTracesAttachExtraTrace;\n        Context.activateLongStackTraces();\n        async.disableTrampolineIfNecessary();\n    }\n};\n\nPromise.hasLongStackTraces = function () {\n    return config.longStackTraces && longStackTracesIsSupported();\n};\n\nvar fireDomEvent = (function() {\n    try {\n        if (typeof CustomEvent === \"function\") {\n            var event = new CustomEvent(\"CustomEvent\");\n            util.global.dispatchEvent(event);\n            return function(name, event) {\n                var domEvent = new CustomEvent(name.toLowerCase(), {\n                    detail: event,\n                    cancelable: true\n                });\n                return !util.global.dispatchEvent(domEvent);\n            };\n        } else if (typeof Event === \"function\") {\n            var event = new Event(\"CustomEvent\");\n            util.global.dispatchEvent(event);\n            return function(name, event) {\n                var domEvent = new Event(name.toLowerCase(), {\n                    cancelable: true\n                });\n                domEvent.detail = event;\n                return !util.global.dispatchEvent(domEvent);\n            };\n        } else {\n            var event = document.createEvent(\"CustomEvent\");\n            event.initCustomEvent(\"testingtheevent\", false, true, {});\n            util.global.dispatchEvent(event);\n            return function(name, event) {\n                var domEvent = document.createEvent(\"CustomEvent\");\n                domEvent.initCustomEvent(name.toLowerCase(), false, true,\n                    event);\n                return !util.global.dispatchEvent(domEvent);\n            };\n        }\n    } catch (e) {}\n    return function() {\n        return false;\n    };\n})();\n\nvar fireGlobalEvent = (function() {\n    if (util.isNode) {\n        return function() {\n            return process.emit.apply(process, arguments);\n        };\n    } else {\n        if (!util.global) {\n            return function() {\n                return false;\n            };\n        }\n        return function(name) {\n            var methodName = \"on\" + name.toLowerCase();\n            var method = util.global[methodName];\n            if (!method) return false;\n            method.apply(util.global, [].slice.call(arguments, 1));\n            return true;\n        };\n    }\n})();\n\nfunction generatePromiseLifecycleEventObject(name, promise) {\n    return {promise: promise};\n}\n\nvar eventToObjectGenerator = {\n    promiseCreated: generatePromiseLifecycleEventObject,\n    promiseFulfilled: generatePromiseLifecycleEventObject,\n    promiseRejected: generatePromiseLifecycleEventObject,\n    promiseResolved: generatePromiseLifecycleEventObject,\n    promiseCancelled: generatePromiseLifecycleEventObject,\n    promiseChained: function(name, promise, child) {\n        return {promise: promise, child: child};\n    },\n    warning: function(name, warning) {\n        return {warning: warning};\n    },\n    unhandledRejection: function (name, reason, promise) {\n        return {reason: reason, promise: promise};\n    },\n    rejectionHandled: generatePromiseLifecycleEventObject\n};\n\nvar activeFireEvent = function (name) {\n    var globalEventFired = false;\n    try {\n        globalEventFired = fireGlobalEvent.apply(null, arguments);\n    } catch (e) {\n        async.throwLater(e);\n        globalEventFired = true;\n    }\n\n    var domEventFired = false;\n    try {\n        domEventFired = fireDomEvent(name,\n                    eventToObjectGenerator[name].apply(null, arguments));\n    } catch (e) {\n        async.throwLater(e);\n        domEventFired = true;\n    }\n\n    return domEventFired || globalEventFired;\n};\n\nPromise.config = function(opts) {\n    opts = Object(opts);\n    if (\"longStackTraces\" in opts) {\n        if (opts.longStackTraces) {\n            Promise.longStackTraces();\n        } else if (!opts.longStackTraces && Promise.hasLongStackTraces()) {\n            disableLongStackTraces();\n        }\n    }\n    if (\"warnings\" in opts) {\n        var warningsOption = opts.warnings;\n        config.warnings = !!warningsOption;\n        wForgottenReturn = config.warnings;\n\n        if (util.isObject(warningsOption)) {\n            if (\"wForgottenReturn\" in warningsOption) {\n                wForgottenReturn = !!warningsOption.wForgottenReturn;\n            }\n        }\n    }\n    if (\"cancellation\" in opts && opts.cancellation && !config.cancellation) {\n        if (async.haveItemsQueued()) {\n            throw new Error(\n                \"cannot enable cancellation after promises are in use\");\n        }\n        Promise.prototype._clearCancellationData =\n            cancellationClearCancellationData;\n        Promise.prototype._propagateFrom = cancellationPropagateFrom;\n        Promise.prototype._onCancel = cancellationOnCancel;\n        Promise.prototype._setOnCancel = cancellationSetOnCancel;\n        Promise.prototype._attachCancellationCallback =\n            cancellationAttachCancellationCallback;\n        Promise.prototype._execute = cancellationExecute;\n        propagateFromFunction = cancellationPropagateFrom;\n        config.cancellation = true;\n    }\n    if (\"monitoring\" in opts) {\n        if (opts.monitoring && !config.monitoring) {\n            config.monitoring = true;\n            Promise.prototype._fireEvent = activeFireEvent;\n        } else if (!opts.monitoring && config.monitoring) {\n            config.monitoring = false;\n            Promise.prototype._fireEvent = defaultFireEvent;\n        }\n    }\n    return Promise;\n};\n\nfunction defaultFireEvent() { return false; }\n\nPromise.prototype._fireEvent = defaultFireEvent;\nPromise.prototype._execute = function(executor, resolve, reject) {\n    try {\n        executor(resolve, reject);\n    } catch (e) {\n        return e;\n    }\n};\nPromise.prototype._onCancel = function () {};\nPromise.prototype._setOnCancel = function (handler) { ; };\nPromise.prototype._attachCancellationCallback = function(onCancel) {\n    ;\n};\nPromise.prototype._captureStackTrace = function () {};\nPromise.prototype._attachExtraTrace = function () {};\nPromise.prototype._clearCancellationData = function() {};\nPromise.prototype._propagateFrom = function (parent, flags) {\n    ;\n    ;\n};\n\nfunction cancellationExecute(executor, resolve, reject) {\n    var promise = this;\n    try {\n        executor(resolve, reject, function(onCancel) {\n            if (typeof onCancel !== \"function\") {\n                throw new TypeError(\"onCancel must be a function, got: \" +\n                                    util.toString(onCancel));\n            }\n            promise._attachCancellationCallback(onCancel);\n        });\n    } catch (e) {\n        return e;\n    }\n}\n\nfunction cancellationAttachCancellationCallback(onCancel) {\n    if (!this._isCancellable()) return this;\n\n    var previousOnCancel = this._onCancel();\n    if (previousOnCancel !== undefined) {\n        if (util.isArray(previousOnCancel)) {\n            previousOnCancel.push(onCancel);\n        } else {\n            this._setOnCancel([previousOnCancel, onCancel]);\n        }\n    } else {\n        this._setOnCancel(onCancel);\n    }\n}\n\nfunction cancellationOnCancel() {\n    return this._onCancelField;\n}\n\nfunction cancellationSetOnCancel(onCancel) {\n    this._onCancelField = onCancel;\n}\n\nfunction cancellationClearCancellationData() {\n    this._cancellationParent = undefined;\n    this._onCancelField = undefined;\n}\n\nfunction cancellationPropagateFrom(parent, flags) {\n    if ((flags & 1) !== 0) {\n        this._cancellationParent = parent;\n        var branchesRemainingToCancel = parent._branchesRemainingToCancel;\n        if (branchesRemainingToCancel === undefined) {\n            branchesRemainingToCancel = 0;\n        }\n        parent._branchesRemainingToCancel = branchesRemainingToCancel + 1;\n    }\n    if ((flags & 2) !== 0 && parent._isBound()) {\n        this._setBoundTo(parent._boundTo);\n    }\n}\n\nfunction bindingPropagateFrom(parent, flags) {\n    if ((flags & 2) !== 0 && parent._isBound()) {\n        this._setBoundTo(parent._boundTo);\n    }\n}\nvar propagateFromFunction = bindingPropagateFrom;\n\nfunction boundValueFunction() {\n    var ret = this._boundTo;\n    if (ret !== undefined) {\n        if (ret instanceof Promise) {\n            if (ret.isFulfilled()) {\n                return ret.value();\n            } else {\n                return undefined;\n            }\n        }\n    }\n    return ret;\n}\n\nfunction longStackTracesCaptureStackTrace() {\n    this._trace = new CapturedTrace(this._peekContext());\n}\n\nfunction longStackTracesAttachExtraTrace(error, ignoreSelf) {\n    if (canAttachTrace(error)) {\n        var trace = this._trace;\n        if (trace !== undefined) {\n            if (ignoreSelf) trace = trace._parent;\n        }\n        if (trace !== undefined) {\n            trace.attachExtraTrace(error);\n        } else if (!error.__stackCleaned__) {\n            var parsed = parseStackAndMessage(error);\n            util.notEnumerableProp(error, \"stack\",\n                parsed.message + \"\\n\" + parsed.stack.join(\"\\n\"));\n            util.notEnumerableProp(error, \"__stackCleaned__\", true);\n        }\n    }\n}\n\nfunction checkForgottenReturns(returnValue, promiseCreated, name, promise,\n                               parent) {\n    if (returnValue === undefined && promiseCreated !== null &&\n        wForgottenReturn) {\n        if (parent !== undefined && parent._returnedNonUndefined()) return;\n        if ((promise._bitField & 65535) === 0) return;\n\n        if (name) name = name + \" \";\n        var handlerLine = \"\";\n        var creatorLine = \"\";\n        if (promiseCreated._trace) {\n            var traceLines = promiseCreated._trace.stack.split(\"\\n\");\n            var stack = cleanStack(traceLines);\n            for (var i = stack.length - 1; i >= 0; --i) {\n                var line = stack[i];\n                if (!nodeFramePattern.test(line)) {\n                    var lineMatches = line.match(parseLinePattern);\n                    if (lineMatches) {\n                        handlerLine  = \"at \" + lineMatches[1] +\n                            \":\" + lineMatches[2] + \":\" + lineMatches[3] + \" \";\n                    }\n                    break;\n                }\n            }\n\n            if (stack.length > 0) {\n                var firstUserLine = stack[0];\n                for (var i = 0; i < traceLines.length; ++i) {\n\n                    if (traceLines[i] === firstUserLine) {\n                        if (i > 0) {\n                            creatorLine = \"\\n\" + traceLines[i - 1];\n                        }\n                        break;\n                    }\n                }\n\n            }\n        }\n        var msg = \"a promise was created in a \" + name +\n            \"handler \" + handlerLine + \"but was not returned from it, \" +\n            \"see http://goo.gl/rRqMUw\" +\n            creatorLine;\n        promise._warn(msg, true, promiseCreated);\n    }\n}\n\nfunction deprecated(name, replacement) {\n    var message = name +\n        \" is deprecated and will be removed in a future version.\";\n    if (replacement) message += \" Use \" + replacement + \" instead.\";\n    return warn(message);\n}\n\nfunction warn(message, shouldUseOwnTrace, promise) {\n    if (!config.warnings) return;\n    var warning = new Warning(message);\n    var ctx;\n    if (shouldUseOwnTrace) {\n        promise._attachExtraTrace(warning);\n    } else if (config.longStackTraces && (ctx = Promise._peekContext())) {\n        ctx.attachExtraTrace(warning);\n    } else {\n        var parsed = parseStackAndMessage(warning);\n        warning.stack = parsed.message + \"\\n\" + parsed.stack.join(\"\\n\");\n    }\n\n    if (!activeFireEvent(\"warning\", warning)) {\n        formatAndLogError(warning, \"\", true);\n    }\n}\n\nfunction reconstructStack(message, stacks) {\n    for (var i = 0; i < stacks.length - 1; ++i) {\n        stacks[i].push(\"From previous event:\");\n        stacks[i] = stacks[i].join(\"\\n\");\n    }\n    if (i < stacks.length) {\n        stacks[i] = stacks[i].join(\"\\n\");\n    }\n    return message + \"\\n\" + stacks.join(\"\\n\");\n}\n\nfunction removeDuplicateOrEmptyJumps(stacks) {\n    for (var i = 0; i < stacks.length; ++i) {\n        if (stacks[i].length === 0 ||\n            ((i + 1 < stacks.length) && stacks[i][0] === stacks[i+1][0])) {\n            stacks.splice(i, 1);\n            i--;\n        }\n    }\n}\n\nfunction removeCommonRoots(stacks) {\n    var current = stacks[0];\n    for (var i = 1; i < stacks.length; ++i) {\n        var prev = stacks[i];\n        var currentLastIndex = current.length - 1;\n        var currentLastLine = current[currentLastIndex];\n        var commonRootMeetPoint = -1;\n\n        for (var j = prev.length - 1; j >= 0; --j) {\n            if (prev[j] === currentLastLine) {\n                commonRootMeetPoint = j;\n                break;\n            }\n        }\n\n        for (var j = commonRootMeetPoint; j >= 0; --j) {\n            var line = prev[j];\n            if (current[currentLastIndex] === line) {\n                current.pop();\n                currentLastIndex--;\n            } else {\n                break;\n            }\n        }\n        current = prev;\n    }\n}\n\nfunction cleanStack(stack) {\n    var ret = [];\n    for (var i = 0; i < stack.length; ++i) {\n        var line = stack[i];\n        var isTraceLine = \"    (No stack trace)\" === line ||\n            stackFramePattern.test(line);\n        var isInternalFrame = isTraceLine && shouldIgnore(line);\n        if (isTraceLine && !isInternalFrame) {\n            if (indentStackFrames && line.charAt(0) !== \" \") {\n                line = \"    \" + line;\n            }\n            ret.push(line);\n        }\n    }\n    return ret;\n}\n\nfunction stackFramesAsArray(error) {\n    var stack = error.stack.replace(/\\s+$/g, \"\").split(\"\\n\");\n    for (var i = 0; i < stack.length; ++i) {\n        var line = stack[i];\n        if (\"    (No stack trace)\" === line || stackFramePattern.test(line)) {\n            break;\n        }\n    }\n    if (i > 0 && error.name != \"SyntaxError\") {\n        stack = stack.slice(i);\n    }\n    return stack;\n}\n\nfunction parseStackAndMessage(error) {\n    var stack = error.stack;\n    var message = error.toString();\n    stack = typeof stack === \"string\" && stack.length > 0\n                ? stackFramesAsArray(error) : [\"    (No stack trace)\"];\n    return {\n        message: message,\n        stack: error.name == \"SyntaxError\" ? stack : cleanStack(stack)\n    };\n}\n\nfunction formatAndLogError(error, title, isSoft) {\n    if (typeof console !== \"undefined\") {\n        var message;\n        if (util.isObject(error)) {\n            var stack = error.stack;\n            message = title + formatStack(stack, error);\n        } else {\n            message = title + String(error);\n        }\n        if (typeof printWarning === \"function\") {\n            printWarning(message, isSoft);\n        } else if (typeof console.log === \"function\" ||\n            typeof console.log === \"object\") {\n            console.log(message);\n        }\n    }\n}\n\nfunction fireRejectionEvent(name, localHandler, reason, promise) {\n    var localEventFired = false;\n    try {\n        if (typeof localHandler === \"function\") {\n            localEventFired = true;\n            if (name === \"rejectionHandled\") {\n                localHandler(promise);\n            } else {\n                localHandler(reason, promise);\n            }\n        }\n    } catch (e) {\n        async.throwLater(e);\n    }\n\n    if (name === \"unhandledRejection\") {\n        if (!activeFireEvent(name, reason, promise) && !localEventFired) {\n            formatAndLogError(reason, \"Unhandled rejection \");\n        }\n    } else {\n        activeFireEvent(name, promise);\n    }\n}\n\nfunction formatNonError(obj) {\n    var str;\n    if (typeof obj === \"function\") {\n        str = \"[function \" +\n            (obj.name || \"anonymous\") +\n            \"]\";\n    } else {\n        str = obj && typeof obj.toString === \"function\"\n            ? obj.toString() : util.toString(obj);\n        var ruselessToString = /\\[object [a-zA-Z0-9$_]+\\]/;\n        if (ruselessToString.test(str)) {\n            try {\n                var newStr = JSON.stringify(obj);\n                str = newStr;\n            }\n            catch(e) {\n\n            }\n        }\n        if (str.length === 0) {\n            str = \"(empty array)\";\n        }\n    }\n    return (\"(<\" + snip(str) + \">, no stack trace)\");\n}\n\nfunction snip(str) {\n    var maxChars = 41;\n    if (str.length < maxChars) {\n        return str;\n    }\n    return str.substr(0, maxChars - 3) + \"...\";\n}\n\nfunction longStackTracesIsSupported() {\n    return typeof captureStackTrace === \"function\";\n}\n\nvar shouldIgnore = function() { return false; };\nvar parseLineInfoRegex = /[\\/<\\(]([^:\\/]+):(\\d+):(?:\\d+)\\)?\\s*$/;\nfunction parseLineInfo(line) {\n    var matches = line.match(parseLineInfoRegex);\n    if (matches) {\n        return {\n            fileName: matches[1],\n            line: parseInt(matches[2], 10)\n        };\n    }\n}\n\nfunction setBounds(firstLineError, lastLineError) {\n    if (!longStackTracesIsSupported()) return;\n    var firstStackLines = firstLineError.stack.split(\"\\n\");\n    var lastStackLines = lastLineError.stack.split(\"\\n\");\n    var firstIndex = -1;\n    var lastIndex = -1;\n    var firstFileName;\n    var lastFileName;\n    for (var i = 0; i < firstStackLines.length; ++i) {\n        var result = parseLineInfo(firstStackLines[i]);\n        if (result) {\n            firstFileName = result.fileName;\n            firstIndex = result.line;\n            break;\n        }\n    }\n    for (var i = 0; i < lastStackLines.length; ++i) {\n        var result = parseLineInfo(lastStackLines[i]);\n        if (result) {\n            lastFileName = result.fileName;\n            lastIndex = result.line;\n            break;\n        }\n    }\n    if (firstIndex < 0 || lastIndex < 0 || !firstFileName || !lastFileName ||\n        firstFileName !== lastFileName || firstIndex >= lastIndex) {\n        return;\n    }\n\n    shouldIgnore = function(line) {\n        if (bluebirdFramePattern.test(line)) return true;\n        var info = parseLineInfo(line);\n        if (info) {\n            if (info.fileName === firstFileName &&\n                (firstIndex <= info.line && info.line <= lastIndex)) {\n                return true;\n            }\n        }\n        return false;\n    };\n}\n\nfunction CapturedTrace(parent) {\n    this._parent = parent;\n    this._promisesCreated = 0;\n    var length = this._length = 1 + (parent === undefined ? 0 : parent._length);\n    captureStackTrace(this, CapturedTrace);\n    if (length > 32) this.uncycle();\n}\nutil.inherits(CapturedTrace, Error);\nContext.CapturedTrace = CapturedTrace;\n\nCapturedTrace.prototype.uncycle = function() {\n    var length = this._length;\n    if (length < 2) return;\n    var nodes = [];\n    var stackToIndex = {};\n\n    for (var i = 0, node = this; node !== undefined; ++i) {\n        nodes.push(node);\n        node = node._parent;\n    }\n    length = this._length = i;\n    for (var i = length - 1; i >= 0; --i) {\n        var stack = nodes[i].stack;\n        if (stackToIndex[stack] === undefined) {\n            stackToIndex[stack] = i;\n        }\n    }\n    for (var i = 0; i < length; ++i) {\n        var currentStack = nodes[i].stack;\n        var index = stackToIndex[currentStack];\n        if (index !== undefined && index !== i) {\n            if (index > 0) {\n                nodes[index - 1]._parent = undefined;\n                nodes[index - 1]._length = 1;\n            }\n            nodes[i]._parent = undefined;\n            nodes[i]._length = 1;\n            var cycleEdgeNode = i > 0 ? nodes[i - 1] : this;\n\n            if (index < length - 1) {\n                cycleEdgeNode._parent = nodes[index + 1];\n                cycleEdgeNode._parent.uncycle();\n                cycleEdgeNode._length =\n                    cycleEdgeNode._parent._length + 1;\n            } else {\n                cycleEdgeNode._parent = undefined;\n                cycleEdgeNode._length = 1;\n            }\n            var currentChildLength = cycleEdgeNode._length + 1;\n            for (var j = i - 2; j >= 0; --j) {\n                nodes[j]._length = currentChildLength;\n                currentChildLength++;\n            }\n            return;\n        }\n    }\n};\n\nCapturedTrace.prototype.attachExtraTrace = function(error) {\n    if (error.__stackCleaned__) return;\n    this.uncycle();\n    var parsed = parseStackAndMessage(error);\n    var message = parsed.message;\n    var stacks = [parsed.stack];\n\n    var trace = this;\n    while (trace !== undefined) {\n        stacks.push(cleanStack(trace.stack.split(\"\\n\")));\n        trace = trace._parent;\n    }\n    removeCommonRoots(stacks);\n    removeDuplicateOrEmptyJumps(stacks);\n    util.notEnumerableProp(error, \"stack\", reconstructStack(message, stacks));\n    util.notEnumerableProp(error, \"__stackCleaned__\", true);\n};\n\nvar captureStackTrace = (function stackDetection() {\n    var v8stackFramePattern = /^\\s*at\\s*/;\n    var v8stackFormatter = function(stack, error) {\n        if (typeof stack === \"string\") return stack;\n\n        if (error.name !== undefined &&\n            error.message !== undefined) {\n            return error.toString();\n        }\n        return formatNonError(error);\n    };\n\n    if (typeof Error.stackTraceLimit === \"number\" &&\n        typeof Error.captureStackTrace === \"function\") {\n        Error.stackTraceLimit += 6;\n        stackFramePattern = v8stackFramePattern;\n        formatStack = v8stackFormatter;\n        var captureStackTrace = Error.captureStackTrace;\n\n        shouldIgnore = function(line) {\n            return bluebirdFramePattern.test(line);\n        };\n        return function(receiver, ignoreUntil) {\n            Error.stackTraceLimit += 6;\n            captureStackTrace(receiver, ignoreUntil);\n            Error.stackTraceLimit -= 6;\n        };\n    }\n    var err = new Error();\n\n    if (typeof err.stack === \"string\" &&\n        err.stack.split(\"\\n\")[0].indexOf(\"stackDetection@\") >= 0) {\n        stackFramePattern = /@/;\n        formatStack = v8stackFormatter;\n        indentStackFrames = true;\n        return function captureStackTrace(o) {\n            o.stack = new Error().stack;\n        };\n    }\n\n    var hasStackAfterThrow;\n    try { throw new Error(); }\n    catch(e) {\n        hasStackAfterThrow = (\"stack\" in e);\n    }\n    if (!(\"stack\" in err) && hasStackAfterThrow &&\n        typeof Error.stackTraceLimit === \"number\") {\n        stackFramePattern = v8stackFramePattern;\n        formatStack = v8stackFormatter;\n        return function captureStackTrace(o) {\n            Error.stackTraceLimit += 6;\n            try { throw new Error(); }\n            catch(e) { o.stack = e.stack; }\n            Error.stackTraceLimit -= 6;\n        };\n    }\n\n    formatStack = function(stack, error) {\n        if (typeof stack === \"string\") return stack;\n\n        if ((typeof error === \"object\" ||\n            typeof error === \"function\") &&\n            error.name !== undefined &&\n            error.message !== undefined) {\n            return error.toString();\n        }\n        return formatNonError(error);\n    };\n\n    return null;\n\n})([]);\n\nif (typeof console !== \"undefined\" && typeof console.warn !== \"undefined\") {\n    printWarning = function (message) {\n        console.warn(message);\n    };\n    if (util.isNode && process.stderr.isTTY) {\n        printWarning = function(message, isSoft) {\n            var color = isSoft ? \"\\u001b[33m\" : \"\\u001b[31m\";\n            console.warn(color + message + \"\\u001b[0m\\n\");\n        };\n    } else if (!util.isNode && typeof (new Error().stack) === \"string\") {\n        printWarning = function(message, isSoft) {\n            console.warn(\"%c\" + message,\n                        isSoft ? \"color: darkorange\" : \"color: red\");\n        };\n    }\n}\n\nvar config = {\n    warnings: warnings,\n    longStackTraces: false,\n    cancellation: false,\n    monitoring: false\n};\n\nif (longStackTraces) Promise.longStackTraces();\n\nreturn {\n    longStackTraces: function() {\n        return config.longStackTraces;\n    },\n    warnings: function() {\n        return config.warnings;\n    },\n    cancellation: function() {\n        return config.cancellation;\n    },\n    monitoring: function() {\n        return config.monitoring;\n    },\n    propagateFromFunction: function() {\n        return propagateFromFunction;\n    },\n    boundValueFunction: function() {\n        return boundValueFunction;\n    },\n    checkForgottenReturns: checkForgottenReturns,\n    setBounds: setBounds,\n    warn: warn,\n    deprecated: deprecated,\n    CapturedTrace: CapturedTrace,\n    fireDomEvent: fireDomEvent,\n    fireGlobalEvent: fireGlobalEvent\n};\n};\n"], "names": [], "mappings": "AAk2BuB;AAl2BvB;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,OAAO;IAC1C,IAAI,YAAY,QAAQ,UAAU;IAClC,IAAI,QAAQ,QAAQ,MAAM;IAC1B,IAAI,UAAU,0GAAoB,OAAO;IACzC,IAAI;IACJ,IAAI,iBAAiB,KAAK,cAAc;IACxC,IAAI;IACJ,IAAI;IACJ,IAAI,uBACA;IACJ,IAAI,mBAAmB;IACvB,IAAI,mBAAmB;IACvB,IAAI,oBAAoB;IACxB,IAAI,cAAc;IAClB,IAAI,oBAAoB;IACxB,IAAI;IACJ,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,qBAAqB,KACzB,CAAC,SACA,KAAK,GAAG,CAAC,qBACT,KAAK,GAAG,CAAC,gBAAgB,aAAa,CAAC;IAEhE,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,wBAAwB,KAC/C,CAAC,aAAa,KAAK,GAAG,CAAC,oBAAoB,CAAC;IAEhD,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,iCAAiC,KAC/D,CAAC,aAAa,KAAK,GAAG,CAAC,6BAA6B,CAAC;IAEzD,IAAI,mBAAmB,KAAK,GAAG,CAAC,kCAAkC,KAC9D,CAAC,YAAY,CAAC,CAAC,KAAK,GAAG,CAAC,8BAA8B;IAE1D,QAAQ,SAAS,CAAC,2BAA2B,GAAG;QAC5C,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,SAAS,GAAI,AAAC,OAAO,SAAS,GAAI,CAAC,UACxB;IACtB;IAEA,QAAQ,SAAS,CAAC,+BAA+B,GAAG;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,MAAM,GAAG;QACrC,IAAI,CAAC,wBAAwB;QAC7B,MAAM,WAAW,CAAC,IAAI,CAAC,yBAAyB,EAAE,IAAI,EAAE;IAC5D;IAEA,QAAQ,SAAS,CAAC,kCAAkC,GAAG;QACnD,mBAAmB,oBACW,2BAA2B,WAAW,IAAI;IAC5E;IAEA,QAAQ,SAAS,CAAC,wBAAwB,GAAG;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,qBAAqB,GAAG;QACtC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,MAAM;IAC5C;IAEA,QAAQ,SAAS,CAAC,yBAAyB,GAAG;QAC1C,IAAI,IAAI,CAAC,qBAAqB,IAAI;YAC9B,IAAI,SAAS,IAAI,CAAC,aAAa;YAC/B,IAAI,CAAC,gCAAgC;YACrC,mBAAmB,sBACW,4BAA4B,QAAQ,IAAI;QAC1E;IACJ;IAEA,QAAQ,SAAS,CAAC,gCAAgC,GAAG;QACjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,kCAAkC,GAAG;QACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAI,CAAC;IACxC;IAEA,QAAQ,SAAS,CAAC,6BAA6B,GAAG;QAC9C,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI;IACvC;IAEA,QAAQ,SAAS,CAAC,wBAAwB,GAAG;QACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,0BAA0B,GAAG;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAI,CAAC;QACpC,IAAI,IAAI,CAAC,6BAA6B,IAAI;YACtC,IAAI,CAAC,kCAAkC;YACvC,IAAI,CAAC,kCAAkC;QAC3C;IACJ;IAEA,QAAQ,SAAS,CAAC,qBAAqB,GAAG;QACtC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI;IACxC;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAS,OAAO,EAAE,iBAAiB,EAAE,OAAO;QAClE,OAAO,KAAK,SAAS,mBAAmB,WAAW,IAAI;IAC3D;IAEA,QAAQ,4BAA4B,GAAG,SAAU,EAAE;QAC/C,IAAI,SAAS;QACb,6BACI,OAAO,OAAO,aAAc,WAAW,OACH,KAAK,KAAK,UAAU,CAAC,QAAQ,MACtC;IACnC;IAEA,QAAQ,2BAA2B,GAAG,SAAU,EAAE;QAC9C,IAAI,SAAS;QACb,4BACI,OAAO,OAAO,aAAc,WAAW,OACH,KAAK,KAAK,UAAU,CAAC,QAAQ,MACtC;IACnC;IAEA,IAAI,yBAAyB,YAAY;IACzC,QAAQ,eAAe,GAAG;QACtB,IAAI,MAAM,eAAe,MAAM,CAAC,OAAO,eAAe,EAAE;YACpD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,OAAO,eAAe,IAAI,8BAA8B;YACzD,IAAI,4BAA4B,QAAQ,SAAS,CAAC,kBAAkB;YACpE,IAAI,2BAA2B,QAAQ,SAAS,CAAC,iBAAiB;YAClE,OAAO,eAAe,GAAG;YACzB,yBAAyB;gBACrB,IAAI,MAAM,eAAe,MAAM,CAAC,OAAO,eAAe,EAAE;oBACpD,MAAM,IAAI,MAAM;gBACpB;gBACA,QAAQ,SAAS,CAAC,kBAAkB,GAAG;gBACvC,QAAQ,SAAS,CAAC,iBAAiB,GAAG;gBACtC,QAAQ,yBAAyB;gBACjC,MAAM,gBAAgB;gBACtB,OAAO,eAAe,GAAG;YAC7B;YACA,QAAQ,SAAS,CAAC,kBAAkB,GAAG;YACvC,QAAQ,SAAS,CAAC,iBAAiB,GAAG;YACtC,QAAQ,uBAAuB;YAC/B,MAAM,4BAA4B;QACtC;IACJ;IAEA,QAAQ,kBAAkB,GAAG;QACzB,OAAO,OAAO,eAAe,IAAI;IACrC;IAEA,IAAI,eAAe,AAAC;QAChB,IAAI;YACA,IAAI,OAAO,gBAAgB,YAAY;gBACnC,IAAI,QAAQ,IAAI,YAAY;gBAC5B,KAAK,MAAM,CAAC,aAAa,CAAC;gBAC1B,OAAO,SAAS,IAAI,EAAE,KAAK;oBACvB,IAAI,WAAW,IAAI,YAAY,KAAK,WAAW,IAAI;wBAC/C,QAAQ;wBACR,YAAY;oBAChB;oBACA,OAAO,CAAC,KAAK,MAAM,CAAC,aAAa,CAAC;gBACtC;YACJ,OAAO,IAAI,OAAO,UAAU,YAAY;gBACpC,IAAI,QAAQ,IAAI,MAAM;gBACtB,KAAK,MAAM,CAAC,aAAa,CAAC;gBAC1B,OAAO,SAAS,IAAI,EAAE,KAAK;oBACvB,IAAI,WAAW,IAAI,MAAM,KAAK,WAAW,IAAI;wBACzC,YAAY;oBAChB;oBACA,SAAS,MAAM,GAAG;oBAClB,OAAO,CAAC,KAAK,MAAM,CAAC,aAAa,CAAC;gBACtC;YACJ,OAAO;gBACH,IAAI,QAAQ,SAAS,WAAW,CAAC;gBACjC,MAAM,eAAe,CAAC,mBAAmB,OAAO,MAAM,CAAC;gBACvD,KAAK,MAAM,CAAC,aAAa,CAAC;gBAC1B,OAAO,SAAS,IAAI,EAAE,KAAK;oBACvB,IAAI,WAAW,SAAS,WAAW,CAAC;oBACpC,SAAS,eAAe,CAAC,KAAK,WAAW,IAAI,OAAO,MAChD;oBACJ,OAAO,CAAC,KAAK,MAAM,CAAC,aAAa,CAAC;gBACtC;YACJ;QACJ,EAAE,OAAO,GAAG,CAAC;QACb,OAAO;YACH,OAAO;QACX;IACJ;IAEA,IAAI,kBAAkB,AAAC;QACnB,IAAI,KAAK,MAAM,EAAE;YACb,OAAO;gBACH,OAAO,gKAAA,CAAA,UAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gKAAA,CAAA,UAAO,EAAE;YACvC;QACJ,OAAO;YACH,IAAI,CAAC,KAAK,MAAM,EAAE;gBACd,OAAO;oBACH,OAAO;gBACX;YACJ;YACA,OAAO,SAAS,IAAI;gBAChB,IAAI,aAAa,OAAO,KAAK,WAAW;gBACxC,IAAI,SAAS,KAAK,MAAM,CAAC,WAAW;gBACpC,IAAI,CAAC,QAAQ,OAAO;gBACpB,OAAO,KAAK,CAAC,KAAK,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;gBACnD,OAAO;YACX;QACJ;IACJ;IAEA,SAAS,oCAAoC,IAAI,EAAE,OAAO;QACtD,OAAO;YAAC,SAAS;QAAO;IAC5B;IAEA,IAAI,yBAAyB;QACzB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB,SAAS,IAAI,EAAE,OAAO,EAAE,KAAK;YACzC,OAAO;gBAAC,SAAS;gBAAS,OAAO;YAAK;QAC1C;QACA,SAAS,SAAS,IAAI,EAAE,OAAO;YAC3B,OAAO;gBAAC,SAAS;YAAO;QAC5B;QACA,oBAAoB,SAAU,IAAI,EAAE,MAAM,EAAE,OAAO;YAC/C,OAAO;gBAAC,QAAQ;gBAAQ,SAAS;YAAO;QAC5C;QACA,kBAAkB;IACtB;IAEA,IAAI,kBAAkB,SAAU,IAAI;QAChC,IAAI,mBAAmB;QACvB,IAAI;YACA,mBAAmB,gBAAgB,KAAK,CAAC,MAAM;QACnD,EAAE,OAAO,GAAG;YACR,MAAM,UAAU,CAAC;YACjB,mBAAmB;QACvB;QAEA,IAAI,gBAAgB;QACpB,IAAI;YACA,gBAAgB,aAAa,MACjB,sBAAsB,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;QACzD,EAAE,OAAO,GAAG;YACR,MAAM,UAAU,CAAC;YACjB,gBAAgB;QACpB;QAEA,OAAO,iBAAiB;IAC5B;IAEA,QAAQ,MAAM,GAAG,SAAS,IAAI;QAC1B,OAAO,OAAO;QACd,IAAI,qBAAqB,MAAM;YAC3B,IAAI,KAAK,eAAe,EAAE;gBACtB,QAAQ,eAAe;YAC3B,OAAO,IAAI,CAAC,KAAK,eAAe,IAAI,QAAQ,kBAAkB,IAAI;gBAC9D;YACJ;QACJ;QACA,IAAI,cAAc,MAAM;YACpB,IAAI,iBAAiB,KAAK,QAAQ;YAClC,OAAO,QAAQ,GAAG,CAAC,CAAC;YACpB,mBAAmB,OAAO,QAAQ;YAElC,IAAI,KAAK,QAAQ,CAAC,iBAAiB;gBAC/B,IAAI,sBAAsB,gBAAgB;oBACtC,mBAAmB,CAAC,CAAC,eAAe,gBAAgB;gBACxD;YACJ;QACJ;QACA,IAAI,kBAAkB,QAAQ,KAAK,YAAY,IAAI,CAAC,OAAO,YAAY,EAAE;YACrE,IAAI,MAAM,eAAe,IAAI;gBACzB,MAAM,IAAI,MACN;YACR;YACA,QAAQ,SAAS,CAAC,sBAAsB,GACpC;YACJ,QAAQ,SAAS,CAAC,cAAc,GAAG;YACnC,QAAQ,SAAS,CAAC,SAAS,GAAG;YAC9B,QAAQ,SAAS,CAAC,YAAY,GAAG;YACjC,QAAQ,SAAS,CAAC,2BAA2B,GACzC;YACJ,QAAQ,SAAS,CAAC,QAAQ,GAAG;YAC7B,wBAAwB;YACxB,OAAO,YAAY,GAAG;QAC1B;QACA,IAAI,gBAAgB,MAAM;YACtB,IAAI,KAAK,UAAU,IAAI,CAAC,OAAO,UAAU,EAAE;gBACvC,OAAO,UAAU,GAAG;gBACpB,QAAQ,SAAS,CAAC,UAAU,GAAG;YACnC,OAAO,IAAI,CAAC,KAAK,UAAU,IAAI,OAAO,UAAU,EAAE;gBAC9C,OAAO,UAAU,GAAG;gBACpB,QAAQ,SAAS,CAAC,UAAU,GAAG;YACnC;QACJ;QACA,OAAO;IACX;IAEA,SAAS;QAAqB,OAAO;IAAO;IAE5C,QAAQ,SAAS,CAAC,UAAU,GAAG;IAC/B,QAAQ,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,EAAE,OAAO,EAAE,MAAM;QAC3D,IAAI;YACA,SAAS,SAAS;QACtB,EAAE,OAAO,GAAG;YACR,OAAO;QACX;IACJ;IACA,QAAQ,SAAS,CAAC,SAAS,GAAG,YAAa;IAC3C,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAU,OAAO;;IAAM;IACxD,QAAQ,SAAS,CAAC,2BAA2B,GAAG,SAAS,QAAQ;;IAEjE;IACA,QAAQ,SAAS,CAAC,kBAAkB,GAAG,YAAa;IACpD,QAAQ,SAAS,CAAC,iBAAiB,GAAG,YAAa;IACnD,QAAQ,SAAS,CAAC,sBAAsB,GAAG,YAAY;IACvD,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,EAAE,KAAK;;;IAG1D;IAEA,SAAS,oBAAoB,QAAQ,EAAE,OAAO,EAAE,MAAM;QAClD,IAAI,UAAU,IAAI;QAClB,IAAI;YACA,SAAS,SAAS,QAAQ,SAAS,QAAQ;gBACvC,IAAI,OAAO,aAAa,YAAY;oBAChC,MAAM,IAAI,UAAU,uCACA,KAAK,QAAQ,CAAC;gBACtC;gBACA,QAAQ,2BAA2B,CAAC;YACxC;QACJ,EAAE,OAAO,GAAG;YACR,OAAO;QACX;IACJ;IAEA,SAAS,uCAAuC,QAAQ;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,OAAO,IAAI;QAEvC,IAAI,mBAAmB,IAAI,CAAC,SAAS;QACrC,IAAI,qBAAqB,WAAW;YAChC,IAAI,KAAK,OAAO,CAAC,mBAAmB;gBAChC,iBAAiB,IAAI,CAAC;YAC1B,OAAO;gBACH,IAAI,CAAC,YAAY,CAAC;oBAAC;oBAAkB;iBAAS;YAClD;QACJ,OAAO;YACH,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IAEA,SAAS;QACL,OAAO,IAAI,CAAC,cAAc;IAC9B;IAEA,SAAS,wBAAwB,QAAQ;QACrC,IAAI,CAAC,cAAc,GAAG;IAC1B;IAEA,SAAS;QACL,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,cAAc,GAAG;IAC1B;IAEA,SAAS,0BAA0B,MAAM,EAAE,KAAK;QAC5C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YACnB,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,4BAA4B,OAAO,0BAA0B;YACjE,IAAI,8BAA8B,WAAW;gBACzC,4BAA4B;YAChC;YACA,OAAO,0BAA0B,GAAG,4BAA4B;QACpE;QACA,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,OAAO,QAAQ,IAAI;YACxC,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;QACpC;IACJ;IAEA,SAAS,qBAAqB,MAAM,EAAE,KAAK;QACvC,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,OAAO,QAAQ,IAAI;YACxC,IAAI,CAAC,WAAW,CAAC,OAAO,QAAQ;QACpC;IACJ;IACA,IAAI,wBAAwB;IAE5B,SAAS;QACL,IAAI,MAAM,IAAI,CAAC,QAAQ;QACvB,IAAI,QAAQ,WAAW;YACnB,IAAI,eAAe,SAAS;gBACxB,IAAI,IAAI,WAAW,IAAI;oBACnB,OAAO,IAAI,KAAK;gBACpB,OAAO;oBACH,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IAEA,SAAS;QACL,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,IAAI,CAAC,YAAY;IACrD;IAEA,SAAS,gCAAgC,KAAK,EAAE,UAAU;QACtD,IAAI,eAAe,QAAQ;YACvB,IAAI,QAAQ,IAAI,CAAC,MAAM;YACvB,IAAI,UAAU,WAAW;gBACrB,IAAI,YAAY,QAAQ,MAAM,OAAO;YACzC;YACA,IAAI,UAAU,WAAW;gBACrB,MAAM,gBAAgB,CAAC;YAC3B,OAAO,IAAI,CAAC,MAAM,gBAAgB,EAAE;gBAChC,IAAI,SAAS,qBAAqB;gBAClC,KAAK,iBAAiB,CAAC,OAAO,SAC1B,OAAO,OAAO,GAAG,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC;gBAC9C,KAAK,iBAAiB,CAAC,OAAO,oBAAoB;YACtD;QACJ;IACJ;IAEA,SAAS,sBAAsB,WAAW,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAC1C,MAAM;QACjC,IAAI,gBAAgB,aAAa,mBAAmB,QAChD,kBAAkB;YAClB,IAAI,WAAW,aAAa,OAAO,qBAAqB,IAAI;YAC5D,IAAI,CAAC,QAAQ,SAAS,GAAG,KAAK,MAAM,GAAG;YAEvC,IAAI,MAAM,OAAO,OAAO;YACxB,IAAI,cAAc;YAClB,IAAI,cAAc;YAClB,IAAI,eAAe,MAAM,EAAE;gBACvB,IAAI,aAAa,eAAe,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACnD,IAAI,QAAQ,WAAW;gBACvB,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;oBACxC,IAAI,OAAO,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO;wBAC9B,IAAI,cAAc,KAAK,KAAK,CAAC;wBAC7B,IAAI,aAAa;4BACb,cAAe,QAAQ,WAAW,CAAC,EAAE,GACjC,MAAM,WAAW,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,EAAE,GAAG;wBACtD;wBACA;oBACJ;gBACJ;gBAEA,IAAI,MAAM,MAAM,GAAG,GAAG;oBAClB,IAAI,gBAAgB,KAAK,CAAC,EAAE;oBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,EAAE,EAAG;wBAExC,IAAI,UAAU,CAAC,EAAE,KAAK,eAAe;4BACjC,IAAI,IAAI,GAAG;gCACP,cAAc,OAAO,UAAU,CAAC,IAAI,EAAE;4BAC1C;4BACA;wBACJ;oBACJ;gBAEJ;YACJ;YACA,IAAI,MAAM,gCAAgC,OACtC,aAAa,cAAc,mCAC3B,6BACA;YACJ,QAAQ,KAAK,CAAC,KAAK,MAAM;QAC7B;IACJ;IAEA,SAAS,WAAW,IAAI,EAAE,WAAW;QACjC,IAAI,UAAU,OACV;QACJ,IAAI,aAAa,WAAW,UAAU,cAAc;QACpD,OAAO,KAAK;IAChB;IAEA,SAAS,KAAK,OAAO,EAAE,iBAAiB,EAAE,OAAO;QAC7C,IAAI,CAAC,OAAO,QAAQ,EAAE;QACtB,IAAI,UAAU,IAAI,QAAQ;QAC1B,IAAI;QACJ,IAAI,mBAAmB;YACnB,QAAQ,iBAAiB,CAAC;QAC9B,OAAO,IAAI,OAAO,eAAe,IAAI,CAAC,MAAM,QAAQ,YAAY,EAAE,GAAG;YACjE,IAAI,gBAAgB,CAAC;QACzB,OAAO;YACH,IAAI,SAAS,qBAAqB;YAClC,QAAQ,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC;QAC9D;QAEA,IAAI,CAAC,gBAAgB,WAAW,UAAU;YACtC,kBAAkB,SAAS,IAAI;QACnC;IACJ;IAEA,SAAS,iBAAiB,OAAO,EAAE,MAAM;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,EAAG;YACxC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;YACf,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;QAC/B;QACA,IAAI,IAAI,OAAO,MAAM,EAAE;YACnB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;QAC/B;QACA,OAAO,UAAU,OAAO,OAAO,IAAI,CAAC;IACxC;IAEA,SAAS,4BAA4B,MAAM;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACpC,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,KACpB,AAAC,IAAI,IAAI,OAAO,MAAM,IAAK,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,IAAE,EAAE,CAAC,EAAE,EAAG;gBAC9D,OAAO,MAAM,CAAC,GAAG;gBACjB;YACJ;QACJ;IACJ;IAEA,SAAS,kBAAkB,MAAM;QAC7B,IAAI,UAAU,MAAM,CAAC,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACpC,IAAI,OAAO,MAAM,CAAC,EAAE;YACpB,IAAI,mBAAmB,QAAQ,MAAM,GAAG;YACxC,IAAI,kBAAkB,OAAO,CAAC,iBAAiB;YAC/C,IAAI,sBAAsB,CAAC;YAE3B,IAAK,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACvC,IAAI,IAAI,CAAC,EAAE,KAAK,iBAAiB;oBAC7B,sBAAsB;oBACtB;gBACJ;YACJ;YAEA,IAAK,IAAI,IAAI,qBAAqB,KAAK,GAAG,EAAE,EAAG;gBAC3C,IAAI,OAAO,IAAI,CAAC,EAAE;gBAClB,IAAI,OAAO,CAAC,iBAAiB,KAAK,MAAM;oBACpC,QAAQ,GAAG;oBACX;gBACJ,OAAO;oBACH;gBACJ;YACJ;YACA,UAAU;QACd;IACJ;IAEA,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,IAAI,OAAO,KAAK,CAAC,EAAE;YACnB,IAAI,cAAc,2BAA2B,QACzC,kBAAkB,IAAI,CAAC;YAC3B,IAAI,kBAAkB,eAAe,aAAa;YAClD,IAAI,eAAe,CAAC,iBAAiB;gBACjC,IAAI,qBAAqB,KAAK,MAAM,CAAC,OAAO,KAAK;oBAC7C,OAAO,SAAS;gBACpB;gBACA,IAAI,IAAI,CAAC;YACb;QACJ;QACA,OAAO;IACX;IAEA,SAAS,mBAAmB,KAAK;QAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,IAAI,OAAO,KAAK,CAAC,EAAE;YACnB,IAAI,2BAA2B,QAAQ,kBAAkB,IAAI,CAAC,OAAO;gBACjE;YACJ;QACJ;QACA,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,eAAe;YACtC,QAAQ,MAAM,KAAK,CAAC;QACxB;QACA,OAAO;IACX;IAEA,SAAS,qBAAqB,KAAK;QAC/B,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAI,UAAU,MAAM,QAAQ;QAC5B,QAAQ,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,IACtC,mBAAmB,SAAS;YAAC;SAAuB;QAClE,OAAO;YACH,SAAS;YACT,OAAO,MAAM,IAAI,IAAI,gBAAgB,QAAQ,WAAW;QAC5D;IACJ;IAEA,SAAS,kBAAkB,KAAK,EAAE,KAAK,EAAE,MAAM;QAC3C,IAAI,OAAO,YAAY,aAAa;YAChC,IAAI;YACJ,IAAI,KAAK,QAAQ,CAAC,QAAQ;gBACtB,IAAI,QAAQ,MAAM,KAAK;gBACvB,UAAU,QAAQ,YAAY,OAAO;YACzC,OAAO;gBACH,UAAU,QAAQ,OAAO;YAC7B;YACA,IAAI,OAAO,iBAAiB,YAAY;gBACpC,aAAa,SAAS;YAC1B,OAAO,IAAI,OAAO,QAAQ,GAAG,KAAK,cAC9B,OAAO,QAAQ,GAAG,KAAK,UAAU;gBACjC,QAAQ,GAAG,CAAC;YAChB;QACJ;IACJ;IAEA,SAAS,mBAAmB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO;QAC3D,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAI,OAAO,iBAAiB,YAAY;gBACpC,kBAAkB;gBAClB,IAAI,SAAS,oBAAoB;oBAC7B,aAAa;gBACjB,OAAO;oBACH,aAAa,QAAQ;gBACzB;YACJ;QACJ,EAAE,OAAO,GAAG;YACR,MAAM,UAAU,CAAC;QACrB;QAEA,IAAI,SAAS,sBAAsB;YAC/B,IAAI,CAAC,gBAAgB,MAAM,QAAQ,YAAY,CAAC,iBAAiB;gBAC7D,kBAAkB,QAAQ;YAC9B;QACJ,OAAO;YACH,gBAAgB,MAAM;QAC1B;IACJ;IAEA,SAAS,eAAe,GAAG;QACvB,IAAI;QACJ,IAAI,OAAO,QAAQ,YAAY;YAC3B,MAAM,eACF,CAAC,IAAI,IAAI,IAAI,WAAW,IACxB;QACR,OAAO;YACH,MAAM,OAAO,OAAO,IAAI,QAAQ,KAAK,aAC/B,IAAI,QAAQ,KAAK,KAAK,QAAQ,CAAC;YACrC,IAAI,mBAAmB;YACvB,IAAI,iBAAiB,IAAI,CAAC,MAAM;gBAC5B,IAAI;oBACA,IAAI,SAAS,KAAK,SAAS,CAAC;oBAC5B,MAAM;gBACV,EACA,OAAM,GAAG,CAET;YACJ;YACA,IAAI,IAAI,MAAM,KAAK,GAAG;gBAClB,MAAM;YACV;QACJ;QACA,OAAQ,OAAO,KAAK,OAAO;IAC/B;IAEA,SAAS,KAAK,GAAG;QACb,IAAI,WAAW;QACf,IAAI,IAAI,MAAM,GAAG,UAAU;YACvB,OAAO;QACX;QACA,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK;IACzC;IAEA,SAAS;QACL,OAAO,OAAO,sBAAsB;IACxC;IAEA,IAAI,eAAe;QAAa,OAAO;IAAO;IAC9C,IAAI,qBAAqB;IACzB,SAAS,cAAc,IAAI;QACvB,IAAI,UAAU,KAAK,KAAK,CAAC;QACzB,IAAI,SAAS;YACT,OAAO;gBACH,UAAU,OAAO,CAAC,EAAE;gBACpB,MAAM,SAAS,OAAO,CAAC,EAAE,EAAE;YAC/B;QACJ;IACJ;IAEA,SAAS,UAAU,cAAc,EAAE,aAAa;QAC5C,IAAI,CAAC,8BAA8B;QACnC,IAAI,kBAAkB,eAAe,KAAK,CAAC,KAAK,CAAC;QACjD,IAAI,iBAAiB,cAAc,KAAK,CAAC,KAAK,CAAC;QAC/C,IAAI,aAAa,CAAC;QAClB,IAAI,YAAY,CAAC;QACjB,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,EAAE,EAAG;YAC7C,IAAI,SAAS,cAAc,eAAe,CAAC,EAAE;YAC7C,IAAI,QAAQ;gBACR,gBAAgB,OAAO,QAAQ;gBAC/B,aAAa,OAAO,IAAI;gBACxB;YACJ;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,EAAE,EAAG;YAC5C,IAAI,SAAS,cAAc,cAAc,CAAC,EAAE;YAC5C,IAAI,QAAQ;gBACR,eAAe,OAAO,QAAQ;gBAC9B,YAAY,OAAO,IAAI;gBACvB;YACJ;QACJ;QACA,IAAI,aAAa,KAAK,YAAY,KAAK,CAAC,iBAAiB,CAAC,gBACtD,kBAAkB,gBAAgB,cAAc,WAAW;YAC3D;QACJ;QAEA,eAAe,SAAS,IAAI;YACxB,IAAI,qBAAqB,IAAI,CAAC,OAAO,OAAO;YAC5C,IAAI,OAAO,cAAc;YACzB,IAAI,MAAM;gBACN,IAAI,KAAK,QAAQ,KAAK,iBACjB,cAAc,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,WAAY;oBACrD,OAAO;gBACX;YACJ;YACA,OAAO;QACX;IACJ;IAEA,SAAS,cAAc,MAAM;QACzB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,SAAS,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,YAAY,IAAI,OAAO,OAAO;QAC1E,kBAAkB,IAAI,EAAE;QACxB,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO;IACjC;IACA,KAAK,QAAQ,CAAC,eAAe;IAC7B,QAAQ,aAAa,GAAG;IAExB,cAAc,SAAS,CAAC,OAAO,GAAG;QAC9B,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,EAAE;QACd,IAAI,eAAe,CAAC;QAEpB,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,EAAE,SAAS,WAAW,EAAE,EAAG;YAClD,MAAM,IAAI,CAAC;YACX,OAAO,KAAK,OAAO;QACvB;QACA,SAAS,IAAI,CAAC,OAAO,GAAG;QACxB,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,EAAE,EAAG;YAClC,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK;YAC1B,IAAI,YAAY,CAAC,MAAM,KAAK,WAAW;gBACnC,YAAY,CAAC,MAAM,GAAG;YAC1B;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;YAC7B,IAAI,eAAe,KAAK,CAAC,EAAE,CAAC,KAAK;YACjC,IAAI,QAAQ,YAAY,CAAC,aAAa;YACtC,IAAI,UAAU,aAAa,UAAU,GAAG;gBACpC,IAAI,QAAQ,GAAG;oBACX,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,GAAG;oBAC3B,KAAK,CAAC,QAAQ,EAAE,CAAC,OAAO,GAAG;gBAC/B;gBACA,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG;gBACnB,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG;gBACnB,IAAI,gBAAgB,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI;gBAE/C,IAAI,QAAQ,SAAS,GAAG;oBACpB,cAAc,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE;oBACxC,cAAc,OAAO,CAAC,OAAO;oBAC7B,cAAc,OAAO,GACjB,cAAc,OAAO,CAAC,OAAO,GAAG;gBACxC,OAAO;oBACH,cAAc,OAAO,GAAG;oBACxB,cAAc,OAAO,GAAG;gBAC5B;gBACA,IAAI,qBAAqB,cAAc,OAAO,GAAG;gBACjD,IAAK,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;oBAC7B,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG;oBACnB;gBACJ;gBACA;YACJ;QACJ;IACJ;IAEA,cAAc,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK;QACrD,IAAI,MAAM,gBAAgB,EAAE;QAC5B,IAAI,CAAC,OAAO;QACZ,IAAI,SAAS,qBAAqB;QAClC,IAAI,UAAU,OAAO,OAAO;QAC5B,IAAI,SAAS;YAAC,OAAO,KAAK;SAAC;QAE3B,IAAI,QAAQ,IAAI;QAChB,MAAO,UAAU,UAAW;YACxB,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,CAAC,KAAK,CAAC;YACzC,QAAQ,MAAM,OAAO;QACzB;QACA,kBAAkB;QAClB,4BAA4B;QAC5B,KAAK,iBAAiB,CAAC,OAAO,SAAS,iBAAiB,SAAS;QACjE,KAAK,iBAAiB,CAAC,OAAO,oBAAoB;IACtD;IAEA,IAAI,oBAAoB,AAAC,SAAS;QAC9B,IAAI,sBAAsB;QAC1B,IAAI,mBAAmB,SAAS,KAAK,EAAE,KAAK;YACxC,IAAI,OAAO,UAAU,UAAU,OAAO;YAEtC,IAAI,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,WAAW;gBAC7B,OAAO,MAAM,QAAQ;YACzB;YACA,OAAO,eAAe;QAC1B;QAEA,IAAI,OAAO,MAAM,eAAe,KAAK,YACjC,OAAO,MAAM,iBAAiB,KAAK,YAAY;YAC/C,MAAM,eAAe,IAAI;YACzB,oBAAoB;YACpB,cAAc;YACd,IAAI,oBAAoB,MAAM,iBAAiB;YAE/C,eAAe,SAAS,IAAI;gBACxB,OAAO,qBAAqB,IAAI,CAAC;YACrC;YACA,OAAO,SAAS,QAAQ,EAAE,WAAW;gBACjC,MAAM,eAAe,IAAI;gBACzB,kBAAkB,UAAU;gBAC5B,MAAM,eAAe,IAAI;YAC7B;QACJ;QACA,IAAI,MAAM,IAAI;QAEd,IAAI,OAAO,IAAI,KAAK,KAAK,YACrB,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,sBAAsB,GAAG;YAC1D,oBAAoB;YACpB,cAAc;YACd,oBAAoB;YACpB,OAAO,SAAS,kBAAkB,CAAC;gBAC/B,EAAE,KAAK,GAAG,IAAI,QAAQ,KAAK;YAC/B;QACJ;QAEA,IAAI;QACJ,IAAI;YAAE,MAAM,IAAI;QAAS,EACzB,OAAM,GAAG;YACL,qBAAsB,WAAW;QACrC;QACA,IAAI,CAAC,CAAC,WAAW,GAAG,KAAK,sBACrB,OAAO,MAAM,eAAe,KAAK,UAAU;YAC3C,oBAAoB;YACpB,cAAc;YACd,OAAO,SAAS,kBAAkB,CAAC;gBAC/B,MAAM,eAAe,IAAI;gBACzB,IAAI;oBAAE,MAAM,IAAI;gBAAS,EACzB,OAAM,GAAG;oBAAE,EAAE,KAAK,GAAG,EAAE,KAAK;gBAAE;gBAC9B,MAAM,eAAe,IAAI;YAC7B;QACJ;QAEA,cAAc,SAAS,KAAK,EAAE,KAAK;YAC/B,IAAI,OAAO,UAAU,UAAU,OAAO;YAEtC,IAAI,CAAC,OAAO,UAAU,YAClB,OAAO,UAAU,UAAU,KAC3B,MAAM,IAAI,KAAK,aACf,MAAM,OAAO,KAAK,WAAW;gBAC7B,OAAO,MAAM,QAAQ;YACzB;YACA,OAAO,eAAe;QAC1B;QAEA,OAAO;IAEX,EAAG,EAAE;IAEL,IAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,IAAI,KAAK,aAAa;QACvE,eAAe,SAAU,OAAO;YAC5B,QAAQ,IAAI,CAAC;QACjB;QACA,IAAI,KAAK,MAAM,IAAI,gKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACrC,eAAe,SAAS,OAAO,EAAE,MAAM;gBACnC,IAAI,QAAQ,SAAS,eAAe;gBACpC,QAAQ,IAAI,CAAC,QAAQ,UAAU;YACnC;QACJ,OAAO,IAAI,CAAC,KAAK,MAAM,IAAI,OAAQ,IAAI,QAAQ,KAAK,KAAM,UAAU;YAChE,eAAe,SAAS,OAAO,EAAE,MAAM;gBACnC,QAAQ,IAAI,CAAC,OAAO,SACR,SAAS,sBAAsB;YAC/C;QACJ;IACJ;IAEA,IAAI,SAAS;QACT,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,YAAY;IAChB;IAEA,IAAI,iBAAiB,QAAQ,eAAe;IAE5C,OAAO;QACH,iBAAiB;YACb,OAAO,OAAO,eAAe;QACjC;QACA,UAAU;YACN,OAAO,OAAO,QAAQ;QAC1B;QACA,cAAc;YACV,OAAO,OAAO,YAAY;QAC9B;QACA,YAAY;YACR,OAAO,OAAO,UAAU;QAC5B;QACA,uBAAuB;YACnB,OAAO;QACX;QACA,oBAAoB;YAChB,OAAO;QACX;QACA,uBAAuB;QACvB,WAAW;QACX,MAAM;QACN,YAAY;QACZ,eAAe;QACf,cAAc;QACd,iBAAiB;IACrB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1921, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/finally.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, tryConvertToPromise) {\nvar util = require(\"./util\");\nvar CancellationError = Promise.CancellationError;\nvar errorObj = util.errorObj;\n\nfunction PassThroughHandlerContext(promise, type, handler) {\n    this.promise = promise;\n    this.type = type;\n    this.handler = handler;\n    this.called = false;\n    this.cancelPromise = null;\n}\n\nPassThroughHandlerContext.prototype.isFinallyHandler = function() {\n    return this.type === 0;\n};\n\nfunction FinallyHandlerCancelReaction(finallyHandler) {\n    this.finallyHandler = finallyHandler;\n}\n\nFinallyHandlerCancelReaction.prototype._resultCancelled = function() {\n    checkCancel(this.finallyHandler);\n};\n\nfunction checkCancel(ctx, reason) {\n    if (ctx.cancelPromise != null) {\n        if (arguments.length > 1) {\n            ctx.cancelPromise._reject(reason);\n        } else {\n            ctx.cancelPromise._cancel();\n        }\n        ctx.cancelPromise = null;\n        return true;\n    }\n    return false;\n}\n\nfunction succeed() {\n    return finallyHandler.call(this, this.promise._target()._settledValue());\n}\nfunction fail(reason) {\n    if (checkCancel(this, reason)) return;\n    errorObj.e = reason;\n    return errorObj;\n}\nfunction finallyHandler(reasonOrValue) {\n    var promise = this.promise;\n    var handler = this.handler;\n\n    if (!this.called) {\n        this.called = true;\n        var ret = this.isFinallyHandler()\n            ? handler.call(promise._boundValue())\n            : handler.call(promise._boundValue(), reasonOrValue);\n        if (ret !== undefined) {\n            promise._setReturnedNonUndefined();\n            var maybePromise = tryConvertToPromise(ret, promise);\n            if (maybePromise instanceof Promise) {\n                if (this.cancelPromise != null) {\n                    if (maybePromise._isCancelled()) {\n                        var reason =\n                            new CancellationError(\"late cancellation observer\");\n                        promise._attachExtraTrace(reason);\n                        errorObj.e = reason;\n                        return errorObj;\n                    } else if (maybePromise.isPending()) {\n                        maybePromise._attachCancellationCallback(\n                            new FinallyHandlerCancelReaction(this));\n                    }\n                }\n                return maybePromise._then(\n                    succeed, fail, undefined, this, undefined);\n            }\n        }\n    }\n\n    if (promise.isRejected()) {\n        checkCancel(this);\n        errorObj.e = reasonOrValue;\n        return errorObj;\n    } else {\n        checkCancel(this);\n        return reasonOrValue;\n    }\n}\n\nPromise.prototype._passThrough = function(handler, type, success, fail) {\n    if (typeof handler !== \"function\") return this.then();\n    return this._then(success,\n                      fail,\n                      undefined,\n                      new PassThroughHandlerContext(this, type, handler),\n                      undefined);\n};\n\nPromise.prototype.lastly =\nPromise.prototype[\"finally\"] = function (handler) {\n    return this._passThrough(handler,\n                             0,\n                             finallyHandler,\n                             finallyHandler);\n};\n\nPromise.prototype.tap = function (handler) {\n    return this._passThrough(handler, 1, finallyHandler);\n};\n\nreturn PassThroughHandlerContext;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,mBAAmB;IACtD,IAAI;IACJ,IAAI,oBAAoB,QAAQ,iBAAiB;IACjD,IAAI,WAAW,KAAK,QAAQ;IAE5B,SAAS,0BAA0B,OAAO,EAAE,IAAI,EAAE,OAAO;QACrD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG;IACzB;IAEA,0BAA0B,SAAS,CAAC,gBAAgB,GAAG;QACnD,OAAO,IAAI,CAAC,IAAI,KAAK;IACzB;IAEA,SAAS,6BAA6B,cAAc;QAChD,IAAI,CAAC,cAAc,GAAG;IAC1B;IAEA,6BAA6B,SAAS,CAAC,gBAAgB,GAAG;QACtD,YAAY,IAAI,CAAC,cAAc;IACnC;IAEA,SAAS,YAAY,GAAG,EAAE,MAAM;QAC5B,IAAI,IAAI,aAAa,IAAI,MAAM;YAC3B,IAAI,UAAU,MAAM,GAAG,GAAG;gBACtB,IAAI,aAAa,CAAC,OAAO,CAAC;YAC9B,OAAO;gBACH,IAAI,aAAa,CAAC,OAAO;YAC7B;YACA,IAAI,aAAa,GAAG;YACpB,OAAO;QACX;QACA,OAAO;IACX;IAEA,SAAS;QACL,OAAO,eAAe,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,aAAa;IACzE;IACA,SAAS,KAAK,MAAM;QAChB,IAAI,YAAY,IAAI,EAAE,SAAS;QAC/B,SAAS,CAAC,GAAG;QACb,OAAO;IACX;IACA,SAAS,eAAe,aAAa;QACjC,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,UAAU,IAAI,CAAC,OAAO;QAE1B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,MAAM,IAAI,CAAC,gBAAgB,KACzB,QAAQ,IAAI,CAAC,QAAQ,WAAW,MAChC,QAAQ,IAAI,CAAC,QAAQ,WAAW,IAAI;YAC1C,IAAI,QAAQ,WAAW;gBACnB,QAAQ,wBAAwB;gBAChC,IAAI,eAAe,oBAAoB,KAAK;gBAC5C,IAAI,wBAAwB,SAAS;oBACjC,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM;wBAC5B,IAAI,aAAa,YAAY,IAAI;4BAC7B,IAAI,SACA,IAAI,kBAAkB;4BAC1B,QAAQ,iBAAiB,CAAC;4BAC1B,SAAS,CAAC,GAAG;4BACb,OAAO;wBACX,OAAO,IAAI,aAAa,SAAS,IAAI;4BACjC,aAAa,2BAA2B,CACpC,IAAI,6BAA6B,IAAI;wBAC7C;oBACJ;oBACA,OAAO,aAAa,KAAK,CACrB,SAAS,MAAM,WAAW,IAAI,EAAE;gBACxC;YACJ;QACJ;QAEA,IAAI,QAAQ,UAAU,IAAI;YACtB,YAAY,IAAI;YAChB,SAAS,CAAC,GAAG;YACb,OAAO;QACX,OAAO;YACH,YAAY,IAAI;YAChB,OAAO;QACX;IACJ;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;QAClE,IAAI,OAAO,YAAY,YAAY,OAAO,IAAI,CAAC,IAAI;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,SACA,MACA,WACA,IAAI,0BAA0B,IAAI,EAAE,MAAM,UAC1C;IACtB;IAEA,QAAQ,SAAS,CAAC,MAAM,GACxB,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO;QAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,SACA,GACA,gBACA;IAC7B;IAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAU,OAAO;QACrC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG;IACzC;IAEA,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/catch_filter.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(NEXT_FILTER) {\nvar util = require(\"./util\");\nvar getKeys = require(\"./es5\").keys;\nvar tryCatch = util.tryCatch;\nvar errorObj = util.errorObj;\n\nfunction catchFilter(instances, cb, promise) {\n    return function(e) {\n        var boundTo = promise._boundValue();\n        predicateLoop: for (var i = 0; i < instances.length; ++i) {\n            var item = instances[i];\n\n            if (item === Error ||\n                (item != null && item.prototype instanceof Error)) {\n                if (e instanceof item) {\n                    return tryCatch(cb).call(boundTo, e);\n                }\n            } else if (typeof item === \"function\") {\n                var matchesPredicate = tryCatch(item).call(boundTo, e);\n                if (matchesPredicate === errorObj) {\n                    return matchesPredicate;\n                } else if (matchesPredicate) {\n                    return tryCatch(cb).call(boundTo, e);\n                }\n            } else if (util.isObject(e)) {\n                var keys = getKeys(item);\n                for (var j = 0; j < keys.length; ++j) {\n                    var key = keys[j];\n                    if (item[key] != e[key]) {\n                        continue predicateLoop;\n                    }\n                }\n                return tryCatch(cb).call(boundTo, e);\n            }\n        }\n        return NEXT_FILTER;\n    };\n}\n\nreturn catchFilter;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,WAAW;IACrC,IAAI;IACJ,IAAI,UAAU,uGAAiB,IAAI;IACnC,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAE5B,SAAS,YAAY,SAAS,EAAE,EAAE,EAAE,OAAO;QACvC,OAAO,SAAS,CAAC;YACb,IAAI,UAAU,QAAQ,WAAW;YACjC,eAAe,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,EAAE,EAAG;gBACtD,IAAI,OAAO,SAAS,CAAC,EAAE;gBAEvB,IAAI,SAAS,SACR,QAAQ,QAAQ,KAAK,SAAS,YAAY,OAAQ;oBACnD,IAAI,aAAa,MAAM;wBACnB,OAAO,SAAS,IAAI,IAAI,CAAC,SAAS;oBACtC;gBACJ,OAAO,IAAI,OAAO,SAAS,YAAY;oBACnC,IAAI,mBAAmB,SAAS,MAAM,IAAI,CAAC,SAAS;oBACpD,IAAI,qBAAqB,UAAU;wBAC/B,OAAO;oBACX,OAAO,IAAI,kBAAkB;wBACzB,OAAO,SAAS,IAAI,IAAI,CAAC,SAAS;oBACtC;gBACJ,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI;oBACzB,IAAI,OAAO,QAAQ;oBACnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;wBAClC,IAAI,MAAM,IAAI,CAAC,EAAE;wBACjB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE;4BACrB,SAAS;wBACb;oBACJ;oBACA,OAAO,SAAS,IAAI,IAAI,CAAC,SAAS;gBACtC;YACJ;YACA,OAAO;QACX;IACJ;IAEA,OAAO;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2055, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/nodeback.js"], "sourcesContent": ["\"use strict\";\nvar util = require(\"./util\");\nvar maybeWrapAsError = util.maybeWrapAsError;\nvar errors = require(\"./errors\");\nvar OperationalError = errors.OperationalError;\nvar es5 = require(\"./es5\");\n\nfunction isUntypedError(obj) {\n    return obj instanceof Error &&\n        es5.getPrototypeOf(obj) === Error.prototype;\n}\n\nvar rErrorKey = /^(?:name|message|stack|cause)$/;\nfunction wrapAsOperationalError(obj) {\n    var ret;\n    if (isUntypedError(obj)) {\n        ret = new OperationalError(obj);\n        ret.name = obj.name;\n        ret.message = obj.message;\n        ret.stack = obj.stack;\n        var keys = es5.keys(obj);\n        for (var i = 0; i < keys.length; ++i) {\n            var key = keys[i];\n            if (!rErrorKey.test(key)) {\n                ret[key] = obj[key];\n            }\n        }\n        return ret;\n    }\n    util.markAsOriginatingFromRejection(obj);\n    return obj;\n}\n\nfunction nodebackForPromise(promise, multiArgs) {\n    return function(err, value) {\n        if (promise === null) return;\n        if (err) {\n            var wrapped = wrapAsOperationalError(maybeWrapAsError(err));\n            promise._attachExtraTrace(wrapped);\n            promise._reject(wrapped);\n        } else if (!multiArgs) {\n            promise._fulfill(value);\n        } else {\n            var $_len = arguments.length;var args = new Array(Math.max($_len - 1, 0)); for(var $_i = 1; $_i < $_len; ++$_i) {args[$_i - 1] = arguments[$_i];};\n            promise._fulfill(args);\n        }\n        promise = null;\n    };\n}\n\nmodule.exports = nodebackForPromise;\n"], "names": [], "mappings": "AAAA;AACA,IAAI;AACJ,IAAI,mBAAmB,KAAK,gBAAgB;AAC5C,IAAI;AACJ,IAAI,mBAAmB,OAAO,gBAAgB;AAC9C,IAAI;AAEJ,SAAS,eAAe,GAAG;IACvB,OAAO,eAAe,SAClB,IAAI,cAAc,CAAC,SAAS,MAAM,SAAS;AACnD;AAEA,IAAI,YAAY;AAChB,SAAS,uBAAuB,GAAG;IAC/B,IAAI;IACJ,IAAI,eAAe,MAAM;QACrB,MAAM,IAAI,iBAAiB;QAC3B,IAAI,IAAI,GAAG,IAAI,IAAI;QACnB,IAAI,OAAO,GAAG,IAAI,OAAO;QACzB,IAAI,KAAK,GAAG,IAAI,KAAK;QACrB,IAAI,OAAO,IAAI,IAAI,CAAC;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;YAClC,IAAI,MAAM,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM;gBACtB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YACvB;QACJ;QACA,OAAO;IACX;IACA,KAAK,8BAA8B,CAAC;IACpC,OAAO;AACX;AAEA,SAAS,mBAAmB,OAAO,EAAE,SAAS;IAC1C,OAAO,SAAS,GAAG,EAAE,KAAK;QACtB,IAAI,YAAY,MAAM;QACtB,IAAI,KAAK;YACL,IAAI,UAAU,uBAAuB,iBAAiB;YACtD,QAAQ,iBAAiB,CAAC;YAC1B,QAAQ,OAAO,CAAC;QACpB,OAAO,IAAI,CAAC,WAAW;YACnB,QAAQ,QAAQ,CAAC;QACrB,OAAO;YACH,IAAI,QAAQ,UAAU,MAAM;YAAC,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,CAAC,QAAQ,GAAG;YAAK,IAAI,IAAI,MAAM,GAAG,MAAM,OAAO,EAAE,IAAK;gBAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;YAAC;;YAChJ,QAAQ,QAAQ,CAAC;QACrB;QACA,UAAU;IACd;AACJ;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/method.js"], "sourcesContent": ["\"use strict\";\nmodule.exports =\nfunction(Promise, INTERNAL, tryConvertToPromise, apiRejection, debug) {\nvar util = require(\"./util\");\nvar tryCatch = util.tryCatch;\n\nPromise.method = function (fn) {\n    if (typeof fn !== \"function\") {\n        throw new Promise.TypeError(\"expecting a function but got \" + util.classString(fn));\n    }\n    return function () {\n        var ret = new Promise(INTERNAL);\n        ret._captureStackTrace();\n        ret._pushContext();\n        var value = tryCatch(fn).apply(this, arguments);\n        var promiseCreated = ret._popContext();\n        debug.checkForgottenReturns(\n            value, promiseCreated, \"Promise.method\", ret);\n        ret._resolveFromSyncValue(value);\n        return ret;\n    };\n};\n\nPromise.attempt = Promise[\"try\"] = function (fn) {\n    if (typeof fn !== \"function\") {\n        return apiRejection(\"expecting a function but got \" + util.classString(fn));\n    }\n    var ret = new Promise(INTERNAL);\n    ret._captureStackTrace();\n    ret._pushContext();\n    var value;\n    if (arguments.length > 1) {\n        debug.deprecated(\"calling Promise.try with more than 1 argument\");\n        var arg = arguments[1];\n        var ctx = arguments[2];\n        value = util.isArray(arg) ? tryCatch(fn).apply(ctx, arg)\n                                  : tryCatch(fn).call(ctx, arg);\n    } else {\n        value = tryCatch(fn)();\n    }\n    var promiseCreated = ret._popContext();\n    debug.checkForgottenReturns(\n        value, promiseCreated, \"Promise.try\", ret);\n    ret._resolveFromSyncValue(value);\n    return ret;\n};\n\nPromise.prototype._resolveFromSyncValue = function (value) {\n    if (value === util.errorObj) {\n        this._rejectCallback(value.e, false);\n    } else {\n        this._resolveCallback(value, true);\n    }\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GACd,SAAS,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,YAAY,EAAE,KAAK;IACpE,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAE5B,QAAQ,MAAM,GAAG,SAAU,EAAE;QACzB,IAAI,OAAO,OAAO,YAAY;YAC1B,MAAM,IAAI,QAAQ,SAAS,CAAC,kCAAkC,KAAK,WAAW,CAAC;QACnF;QACA,OAAO;YACH,IAAI,MAAM,IAAI,QAAQ;YACtB,IAAI,kBAAkB;YACtB,IAAI,YAAY;YAChB,IAAI,QAAQ,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE;YACrC,IAAI,iBAAiB,IAAI,WAAW;YACpC,MAAM,qBAAqB,CACvB,OAAO,gBAAgB,kBAAkB;YAC7C,IAAI,qBAAqB,CAAC;YAC1B,OAAO;QACX;IACJ;IAEA,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,SAAU,EAAE;QAC3C,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,aAAa,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,IAAI,MAAM,IAAI,QAAQ;QACtB,IAAI,kBAAkB;QACtB,IAAI,YAAY;QAChB,IAAI;QACJ,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,MAAM,UAAU,CAAC;YACjB,IAAI,MAAM,SAAS,CAAC,EAAE;YACtB,IAAI,MAAM,SAAS,CAAC,EAAE;YACtB,QAAQ,KAAK,OAAO,CAAC,OAAO,SAAS,IAAI,KAAK,CAAC,KAAK,OACxB,SAAS,IAAI,IAAI,CAAC,KAAK;QACvD,OAAO;YACH,QAAQ,SAAS;QACrB;QACA,IAAI,iBAAiB,IAAI,WAAW;QACpC,MAAM,qBAAqB,CACvB,OAAO,gBAAgB,eAAe;QAC1C,IAAI,qBAAqB,CAAC;QAC1B,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK;QACrD,IAAI,UAAU,KAAK,QAAQ,EAAE;YACzB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;QAClC,OAAO;YACH,IAAI,CAAC,gBAAgB,CAAC,OAAO;QACjC;IACJ;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/bind.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL, tryConvertToPromise, debug) {\nvar calledBind = false;\nvar rejectThis = function(_, e) {\n    this._reject(e);\n};\n\nvar targetRejected = function(e, context) {\n    context.promiseRejectionQueued = true;\n    context.bindingPromise._then(rejectThis, rejectThis, null, this, e);\n};\n\nvar bindingResolved = function(thisArg, context) {\n    if (((this._bitField & 50397184) === 0)) {\n        this._resolveCallback(context.target);\n    }\n};\n\nvar bindingRejected = function(e, context) {\n    if (!context.promiseRejectionQueued) this._reject(e);\n};\n\nPromise.prototype.bind = function (thisArg) {\n    if (!calledBind) {\n        calledBind = true;\n        Promise.prototype._propagateFrom = debug.propagateFromFunction();\n        Promise.prototype._boundValue = debug.boundValueFunction();\n    }\n    var maybePromise = tryConvertToPromise(thisArg);\n    var ret = new Promise(INTERNAL);\n    ret._propagateFrom(this, 1);\n    var target = this._target();\n    ret._setBoundTo(maybePromise);\n    if (maybePromise instanceof Promise) {\n        var context = {\n            promiseRejectionQueued: false,\n            promise: ret,\n            target: target,\n            bindingPromise: maybePromise\n        };\n        target._then(INTERNAL, targetRejected, undefined, ret, context);\n        maybePromise._then(\n            bindingResolved, bindingRejected, undefined, ret, context);\n        ret._setOnCancel(maybePromise);\n    } else {\n        ret._resolveCallback(target);\n    }\n    return ret;\n};\n\nPromise.prototype._setBoundTo = function (obj) {\n    if (obj !== undefined) {\n        this._bitField = this._bitField | 2097152;\n        this._boundTo = obj;\n    } else {\n        this._bitField = this._bitField & (~2097152);\n    }\n};\n\nPromise.prototype._isBound = function () {\n    return (this._bitField & 2097152) === 2097152;\n};\n\nPromise.bind = function (thisArg, value) {\n    return Promise.resolve(value).bind(thisArg);\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,KAAK;IACvE,IAAI,aAAa;IACjB,IAAI,aAAa,SAAS,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC;IACjB;IAEA,IAAI,iBAAiB,SAAS,CAAC,EAAE,OAAO;QACpC,QAAQ,sBAAsB,GAAG;QACjC,QAAQ,cAAc,CAAC,KAAK,CAAC,YAAY,YAAY,MAAM,IAAI,EAAE;IACrE;IAEA,IAAI,kBAAkB,SAAS,OAAO,EAAE,OAAO;QAC3C,IAAK,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM,GAAI;YACrC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,MAAM;QACxC;IACJ;IAEA,IAAI,kBAAkB,SAAS,CAAC,EAAE,OAAO;QACrC,IAAI,CAAC,QAAQ,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC;IACtD;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO;QACtC,IAAI,CAAC,YAAY;YACb,aAAa;YACb,QAAQ,SAAS,CAAC,cAAc,GAAG,MAAM,qBAAqB;YAC9D,QAAQ,SAAS,CAAC,WAAW,GAAG,MAAM,kBAAkB;QAC5D;QACA,IAAI,eAAe,oBAAoB;QACvC,IAAI,MAAM,IAAI,QAAQ;QACtB,IAAI,cAAc,CAAC,IAAI,EAAE;QACzB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,WAAW,CAAC;QAChB,IAAI,wBAAwB,SAAS;YACjC,IAAI,UAAU;gBACV,wBAAwB;gBACxB,SAAS;gBACT,QAAQ;gBACR,gBAAgB;YACpB;YACA,OAAO,KAAK,CAAC,UAAU,gBAAgB,WAAW,KAAK;YACvD,aAAa,KAAK,CACd,iBAAiB,iBAAiB,WAAW,KAAK;YACtD,IAAI,YAAY,CAAC;QACrB,OAAO;YACH,IAAI,gBAAgB,CAAC;QACzB;QACA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;QACzC,IAAI,QAAQ,WAAW;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;YAClC,IAAI,CAAC,QAAQ,GAAG;QACpB,OAAO;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAI,CAAC;QACxC;IACJ;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG;QACzB,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM;IAC1C;IAEA,QAAQ,IAAI,GAAG,SAAU,OAAO,EAAE,KAAK;QACnC,OAAO,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC;IACvC;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/cancel.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, PromiseArray, apiRejection, debug) {\nvar util = require(\"./util\");\nvar tryCatch = util.tryCatch;\nvar errorObj = util.errorObj;\nvar async = Promise._async;\n\nPromise.prototype[\"break\"] = Promise.prototype.cancel = function() {\n    if (!debug.cancellation()) return this._warn(\"cancellation is disabled\");\n\n    var promise = this;\n    var child = promise;\n    while (promise._isCancellable()) {\n        if (!promise._cancelBy(child)) {\n            if (child._isFollowing()) {\n                child._followee().cancel();\n            } else {\n                child._cancelBranched();\n            }\n            break;\n        }\n\n        var parent = promise._cancellationParent;\n        if (parent == null || !parent._isCancellable()) {\n            if (promise._isFollowing()) {\n                promise._followee().cancel();\n            } else {\n                promise._cancelBranched();\n            }\n            break;\n        } else {\n            if (promise._isFollowing()) promise._followee().cancel();\n            promise._setWillBeCancelled();\n            child = promise;\n            promise = parent;\n        }\n    }\n};\n\nPromise.prototype._branchHasCancelled = function() {\n    this._branchesRemainingToCancel--;\n};\n\nPromise.prototype._enoughBranchesHaveCancelled = function() {\n    return this._branchesRemainingToCancel === undefined ||\n           this._branchesRemainingToCancel <= 0;\n};\n\nPromise.prototype._cancelBy = function(canceller) {\n    if (canceller === this) {\n        this._branchesRemainingToCancel = 0;\n        this._invokeOnCancel();\n        return true;\n    } else {\n        this._branchHasCancelled();\n        if (this._enoughBranchesHaveCancelled()) {\n            this._invokeOnCancel();\n            return true;\n        }\n    }\n    return false;\n};\n\nPromise.prototype._cancelBranched = function() {\n    if (this._enoughBranchesHaveCancelled()) {\n        this._cancel();\n    }\n};\n\nPromise.prototype._cancel = function() {\n    if (!this._isCancellable()) return;\n    this._setCancelled();\n    async.invoke(this._cancelPromises, this, undefined);\n};\n\nPromise.prototype._cancelPromises = function() {\n    if (this._length() > 0) this._settlePromises();\n};\n\nPromise.prototype._unsetOnCancel = function() {\n    this._onCancelField = undefined;\n};\n\nPromise.prototype._isCancellable = function() {\n    return this.isPending() && !this._isCancelled();\n};\n\nPromise.prototype.isCancellable = function() {\n    return this.isPending() && !this.isCancelled();\n};\n\nPromise.prototype._doInvokeOnCancel = function(onCancelCallback, internalOnly) {\n    if (util.isArray(onCancelCallback)) {\n        for (var i = 0; i < onCancelCallback.length; ++i) {\n            this._doInvokeOnCancel(onCancelCallback[i], internalOnly);\n        }\n    } else if (onCancelCallback !== undefined) {\n        if (typeof onCancelCallback === \"function\") {\n            if (!internalOnly) {\n                var e = tryCatch(onCancelCallback).call(this._boundValue());\n                if (e === errorObj) {\n                    this._attachExtraTrace(e.e);\n                    async.throwLater(e.e);\n                }\n            }\n        } else {\n            onCancelCallback._resultCancelled(this);\n        }\n    }\n};\n\nPromise.prototype._invokeOnCancel = function() {\n    var onCancelCallback = this._onCancel();\n    this._unsetOnCancel();\n    async.invoke(this._doInvokeOnCancel, this, onCancelCallback);\n};\n\nPromise.prototype._invokeInternalOnCancel = function() {\n    if (this._isCancellable()) {\n        this._doInvokeOnCancel(this._onCancel(), true);\n        this._unsetOnCancel();\n    }\n};\n\nPromise.prototype._resultCancelled = function() {\n    this.cancel();\n};\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK;IACpE,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,QAAQ,QAAQ,MAAM;IAE1B,QAAQ,SAAS,CAAC,QAAQ,GAAG,QAAQ,SAAS,CAAC,MAAM,GAAG;QACpD,IAAI,CAAC,MAAM,YAAY,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;QAE7C,IAAI,UAAU,IAAI;QAClB,IAAI,QAAQ;QACZ,MAAO,QAAQ,cAAc,GAAI;YAC7B,IAAI,CAAC,QAAQ,SAAS,CAAC,QAAQ;gBAC3B,IAAI,MAAM,YAAY,IAAI;oBACtB,MAAM,SAAS,GAAG,MAAM;gBAC5B,OAAO;oBACH,MAAM,eAAe;gBACzB;gBACA;YACJ;YAEA,IAAI,SAAS,QAAQ,mBAAmB;YACxC,IAAI,UAAU,QAAQ,CAAC,OAAO,cAAc,IAAI;gBAC5C,IAAI,QAAQ,YAAY,IAAI;oBACxB,QAAQ,SAAS,GAAG,MAAM;gBAC9B,OAAO;oBACH,QAAQ,eAAe;gBAC3B;gBACA;YACJ,OAAO;gBACH,IAAI,QAAQ,YAAY,IAAI,QAAQ,SAAS,GAAG,MAAM;gBACtD,QAAQ,mBAAmB;gBAC3B,QAAQ;gBACR,UAAU;YACd;QACJ;IACJ;IAEA,QAAQ,SAAS,CAAC,mBAAmB,GAAG;QACpC,IAAI,CAAC,0BAA0B;IACnC;IAEA,QAAQ,SAAS,CAAC,4BAA4B,GAAG;QAC7C,OAAO,IAAI,CAAC,0BAA0B,KAAK,aACpC,IAAI,CAAC,0BAA0B,IAAI;IAC9C;IAEA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS;QAC5C,IAAI,cAAc,IAAI,EAAE;YACpB,IAAI,CAAC,0BAA0B,GAAG;YAClC,IAAI,CAAC,eAAe;YACpB,OAAO;QACX,OAAO;YACH,IAAI,CAAC,mBAAmB;YACxB,IAAI,IAAI,CAAC,4BAA4B,IAAI;gBACrC,IAAI,CAAC,eAAe;gBACpB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG;QAChC,IAAI,IAAI,CAAC,4BAA4B,IAAI;YACrC,IAAI,CAAC,OAAO;QAChB;IACJ;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG;QACxB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI;QAC5B,IAAI,CAAC,aAAa;QAClB,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE;IAC7C;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG;QAChC,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,IAAI,CAAC,eAAe;IAChD;IAEA,QAAQ,SAAS,CAAC,cAAc,GAAG;QAC/B,IAAI,CAAC,cAAc,GAAG;IAC1B;IAEA,QAAQ,SAAS,CAAC,cAAc,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,YAAY;IACjD;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAChD;IAEA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAS,gBAAgB,EAAE,YAAY;QACzE,IAAI,KAAK,OAAO,CAAC,mBAAmB;YAChC,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,EAAE,EAAG;gBAC9C,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE;YAChD;QACJ,OAAO,IAAI,qBAAqB,WAAW;YACvC,IAAI,OAAO,qBAAqB,YAAY;gBACxC,IAAI,CAAC,cAAc;oBACf,IAAI,IAAI,SAAS,kBAAkB,IAAI,CAAC,IAAI,CAAC,WAAW;oBACxD,IAAI,MAAM,UAAU;wBAChB,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBAC1B,MAAM,UAAU,CAAC,EAAE,CAAC;oBACxB;gBACJ;YACJ,OAAO;gBACH,iBAAiB,gBAAgB,CAAC,IAAI;YAC1C;QACJ;IACJ;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG;QAChC,IAAI,mBAAmB,IAAI,CAAC,SAAS;QACrC,IAAI,CAAC,cAAc;QACnB,MAAM,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE;IAC/C;IAEA,QAAQ,SAAS,CAAC,uBAAuB,GAAG;QACxC,IAAI,IAAI,CAAC,cAAc,IAAI;YACvB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,IAAI;YACzC,IAAI,CAAC,cAAc;QACvB;IACJ;IAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG;QACjC,IAAI,CAAC,MAAM;IACf;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/direct_resolve.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise) {\nfunction returner() {\n    return this.value;\n}\nfunction thrower() {\n    throw this.reason;\n}\n\nPromise.prototype[\"return\"] =\nPromise.prototype.thenReturn = function (value) {\n    if (value instanceof Promise) value.suppressUnhandledRejections();\n    return this._then(\n        returner, undefined, undefined, {value: value}, undefined);\n};\n\nPromise.prototype[\"throw\"] =\nPromise.prototype.thenThrow = function (reason) {\n    return this._then(\n        thrower, undefined, undefined, {reason: reason}, undefined);\n};\n\nPromise.prototype.catchThrow = function (reason) {\n    if (arguments.length <= 1) {\n        return this._then(\n            undefined, thrower, undefined, {reason: reason}, undefined);\n    } else {\n        var _reason = arguments[1];\n        var handler = function() {throw _reason;};\n        return this.caught(reason, handler);\n    }\n};\n\nPromise.prototype.catchReturn = function (value) {\n    if (arguments.length <= 1) {\n        if (value instanceof Promise) value.suppressUnhandledRejections();\n        return this._then(\n            undefined, returner, undefined, {value: value}, undefined);\n    } else {\n        var _value = arguments[1];\n        if (_value instanceof Promise) _value.suppressUnhandledRejections();\n        var handler = function() {return _value;};\n        return this.caught(value, handler);\n    }\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,SAAS;QACL,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,SAAS;QACL,MAAM,IAAI,CAAC,MAAM;IACrB;IAEA,QAAQ,SAAS,CAAC,SAAS,GAC3B,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK;QAC1C,IAAI,iBAAiB,SAAS,MAAM,2BAA2B;QAC/D,OAAO,IAAI,CAAC,KAAK,CACb,UAAU,WAAW,WAAW;YAAC,OAAO;QAAK,GAAG;IACxD;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAC1B,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QAC1C,OAAO,IAAI,CAAC,KAAK,CACb,SAAS,WAAW,WAAW;YAAC,QAAQ;QAAM,GAAG;IACzD;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,MAAM;QAC3C,IAAI,UAAU,MAAM,IAAI,GAAG;YACvB,OAAO,IAAI,CAAC,KAAK,CACb,WAAW,SAAS,WAAW;gBAAC,QAAQ;YAAM,GAAG;QACzD,OAAO;YACH,IAAI,UAAU,SAAS,CAAC,EAAE;YAC1B,IAAI,UAAU;gBAAY,MAAM;YAAQ;YACxC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;QAC/B;IACJ;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QAC3C,IAAI,UAAU,MAAM,IAAI,GAAG;YACvB,IAAI,iBAAiB,SAAS,MAAM,2BAA2B;YAC/D,OAAO,IAAI,CAAC,KAAK,CACb,WAAW,UAAU,WAAW;gBAAC,OAAO;YAAK,GAAG;QACxD,OAAO;YACH,IAAI,SAAS,SAAS,CAAC,EAAE;YACzB,IAAI,kBAAkB,SAAS,OAAO,2BAA2B;YACjE,IAAI,UAAU;gBAAY,OAAO;YAAO;YACxC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO;QAC9B;IACJ;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/synchronous_inspection.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise) {\nfunction PromiseInspection(promise) {\n    if (promise !== undefined) {\n        promise = promise._target();\n        this._bitField = promise._bitField;\n        this._settledValueField = promise._isFateSealed()\n            ? promise._settledValue() : undefined;\n    }\n    else {\n        this._bitField = 0;\n        this._settledValueField = undefined;\n    }\n}\n\nPromiseInspection.prototype._settledValue = function() {\n    return this._settledValueField;\n};\n\nvar value = PromiseInspection.prototype.value = function () {\n    if (!this.isFulfilled()) {\n        throw new TypeError(\"cannot get fulfillment value of a non-fulfilled promise\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    return this._settledValue();\n};\n\nvar reason = PromiseInspection.prototype.error =\nPromiseInspection.prototype.reason = function () {\n    if (!this.isRejected()) {\n        throw new TypeError(\"cannot get rejection reason of a non-rejected promise\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    return this._settledValue();\n};\n\nvar isFulfilled = PromiseInspection.prototype.isFulfilled = function() {\n    return (this._bitField & 33554432) !== 0;\n};\n\nvar isRejected = PromiseInspection.prototype.isRejected = function () {\n    return (this._bitField & 16777216) !== 0;\n};\n\nvar isPending = PromiseInspection.prototype.isPending = function () {\n    return (this._bitField & 50397184) === 0;\n};\n\nvar isResolved = PromiseInspection.prototype.isResolved = function () {\n    return (this._bitField & 50331648) !== 0;\n};\n\nPromiseInspection.prototype.isCancelled = function() {\n    return (this._bitField & 8454144) !== 0;\n};\n\nPromise.prototype.__isCancelled = function() {\n    return (this._bitField & 65536) === 65536;\n};\n\nPromise.prototype._isCancelled = function() {\n    return this._target().__isCancelled();\n};\n\nPromise.prototype.isCancelled = function() {\n    return (this._target()._bitField & 8454144) !== 0;\n};\n\nPromise.prototype.isPending = function() {\n    return isPending.call(this._target());\n};\n\nPromise.prototype.isRejected = function() {\n    return isRejected.call(this._target());\n};\n\nPromise.prototype.isFulfilled = function() {\n    return isFulfilled.call(this._target());\n};\n\nPromise.prototype.isResolved = function() {\n    return isResolved.call(this._target());\n};\n\nPromise.prototype.value = function() {\n    return value.call(this._target());\n};\n\nPromise.prototype.reason = function() {\n    var target = this._target();\n    target._unsetRejectionIsUnhandled();\n    return reason.call(target);\n};\n\nPromise.prototype._value = function() {\n    return this._settledValue();\n};\n\nPromise.prototype._reason = function() {\n    this._unsetRejectionIsUnhandled();\n    return this._settledValue();\n};\n\nPromise.PromiseInspection = PromiseInspection;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,SAAS,kBAAkB,OAAO;QAC9B,IAAI,YAAY,WAAW;YACvB,UAAU,QAAQ,OAAO;YACzB,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;YAClC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,aAAa,KACzC,QAAQ,aAAa,KAAK;QACpC,OACK;YACD,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,kBAAkB,GAAG;QAC9B;IACJ;IAEA,kBAAkB,SAAS,CAAC,aAAa,GAAG;QACxC,OAAO,IAAI,CAAC,kBAAkB;IAClC;IAEA,IAAI,QAAQ,kBAAkB,SAAS,CAAC,KAAK,GAAG;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;YACrB,MAAM,IAAI,UAAU;QACxB;QACA,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,IAAI,SAAS,kBAAkB,SAAS,CAAC,KAAK,GAC9C,kBAAkB,SAAS,CAAC,MAAM,GAAG;QACjC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;YACpB,MAAM,IAAI,UAAU;QACxB;QACA,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,IAAI,cAAc,kBAAkB,SAAS,CAAC,WAAW,GAAG;QACxD,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM;IAC3C;IAEA,IAAI,aAAa,kBAAkB,SAAS,CAAC,UAAU,GAAG;QACtD,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM;IAC3C;IAEA,IAAI,YAAY,kBAAkB,SAAS,CAAC,SAAS,GAAG;QACpD,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM;IAC3C;IAEA,IAAI,aAAa,kBAAkB,SAAS,CAAC,UAAU,GAAG;QACtD,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM;IAC3C;IAEA,kBAAkB,SAAS,CAAC,WAAW,GAAG;QACtC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM;IAC1C;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,MAAM;IACxC;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG;QAC7B,OAAO,IAAI,CAAC,OAAO,GAAG,aAAa;IACvC;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,GAAG,OAAO,MAAM;IACpD;IAEA,QAAQ,SAAS,CAAC,SAAS,GAAG;QAC1B,OAAO,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO;IACtC;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG;QAC3B,OAAO,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO;IACvC;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC5B,OAAO,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO;IACxC;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG;QAC3B,OAAO,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO;IACvC;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACtB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;IAClC;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG;QACvB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,0BAA0B;QACjC,OAAO,OAAO,IAAI,CAAC;IACvB;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG;QACvB,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG;QACxB,IAAI,CAAC,0BAA0B;QAC/B,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,QAAQ,iBAAiB,GAAG;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/join.js"], "sourcesContent": ["\"use strict\";\nmodule.exports =\nfunction(Promise, PromiseArray, tryConvertToPromise, INTERNAL, async,\n         getDomain) {\nvar util = require(\"./util\");\nvar canEvaluate = util.canEvaluate;\nvar tryCatch = util.tryCatch;\nvar errorObj = util.errorObj;\nvar reject;\n\nif (!false) {\nif (canEvaluate) {\n    var thenCallback = function(i) {\n        return new Function(\"value\", \"holder\", \"                             \\n\\\n            'use strict';                                                    \\n\\\n            holder.pIndex = value;                                           \\n\\\n            holder.checkFulfillment(this);                                   \\n\\\n            \".replace(/Index/g, i));\n    };\n\n    var promiseSetter = function(i) {\n        return new Function(\"promise\", \"holder\", \"                           \\n\\\n            'use strict';                                                    \\n\\\n            holder.pIndex = promise;                                         \\n\\\n            \".replace(/Index/g, i));\n    };\n\n    var generateHolderClass = function(total) {\n        var props = new Array(total);\n        for (var i = 0; i < props.length; ++i) {\n            props[i] = \"this.p\" + (i+1);\n        }\n        var assignment = props.join(\" = \") + \" = null;\";\n        var cancellationCode= \"var promise;\\n\" + props.map(function(prop) {\n            return \"                                                         \\n\\\n                promise = \" + prop + \";                                      \\n\\\n                if (promise instanceof Promise) {                            \\n\\\n                    promise.cancel();                                        \\n\\\n                }                                                            \\n\\\n            \";\n        }).join(\"\\n\");\n        var passedArguments = props.join(\", \");\n        var name = \"Holder$\" + total;\n\n\n        var code = \"return function(tryCatch, errorObj, Promise, async) {    \\n\\\n            'use strict';                                                    \\n\\\n            function [TheName](fn) {                                         \\n\\\n                [TheProperties]                                              \\n\\\n                this.fn = fn;                                                \\n\\\n                this.asyncNeeded = true;                                     \\n\\\n                this.now = 0;                                                \\n\\\n            }                                                                \\n\\\n                                                                             \\n\\\n            [TheName].prototype._callFunction = function(promise) {          \\n\\\n                promise._pushContext();                                      \\n\\\n                var ret = tryCatch(this.fn)([ThePassedArguments]);           \\n\\\n                promise._popContext();                                       \\n\\\n                if (ret === errorObj) {                                      \\n\\\n                    promise._rejectCallback(ret.e, false);                   \\n\\\n                } else {                                                     \\n\\\n                    promise._resolveCallback(ret);                           \\n\\\n                }                                                            \\n\\\n            };                                                               \\n\\\n                                                                             \\n\\\n            [TheName].prototype.checkFulfillment = function(promise) {       \\n\\\n                var now = ++this.now;                                        \\n\\\n                if (now === [TheTotal]) {                                    \\n\\\n                    if (this.asyncNeeded) {                                  \\n\\\n                        async.invoke(this._callFunction, this, promise);     \\n\\\n                    } else {                                                 \\n\\\n                        this._callFunction(promise);                         \\n\\\n                    }                                                        \\n\\\n                                                                             \\n\\\n                }                                                            \\n\\\n            };                                                               \\n\\\n                                                                             \\n\\\n            [TheName].prototype._resultCancelled = function() {              \\n\\\n                [CancellationCode]                                           \\n\\\n            };                                                               \\n\\\n                                                                             \\n\\\n            return [TheName];                                                \\n\\\n        }(tryCatch, errorObj, Promise, async);                               \\n\\\n        \";\n\n        code = code.replace(/\\[TheName\\]/g, name)\n            .replace(/\\[TheTotal\\]/g, total)\n            .replace(/\\[ThePassedArguments\\]/g, passedArguments)\n            .replace(/\\[TheProperties\\]/g, assignment)\n            .replace(/\\[CancellationCode\\]/g, cancellationCode);\n\n        return new Function(\"tryCatch\", \"errorObj\", \"Promise\", \"async\", code)\n                           (tryCatch, errorObj, Promise, async);\n    };\n\n    var holderClasses = [];\n    var thenCallbacks = [];\n    var promiseSetters = [];\n\n    for (var i = 0; i < 8; ++i) {\n        holderClasses.push(generateHolderClass(i + 1));\n        thenCallbacks.push(thenCallback(i + 1));\n        promiseSetters.push(promiseSetter(i + 1));\n    }\n\n    reject = function (reason) {\n        this._reject(reason);\n    };\n}}\n\nPromise.join = function () {\n    var last = arguments.length - 1;\n    var fn;\n    if (last > 0 && typeof arguments[last] === \"function\") {\n        fn = arguments[last];\n        if (!false) {\n            if (last <= 8 && canEvaluate) {\n                var ret = new Promise(INTERNAL);\n                ret._captureStackTrace();\n                var HolderClass = holderClasses[last - 1];\n                var holder = new HolderClass(fn);\n                var callbacks = thenCallbacks;\n\n                for (var i = 0; i < last; ++i) {\n                    var maybePromise = tryConvertToPromise(arguments[i], ret);\n                    if (maybePromise instanceof Promise) {\n                        maybePromise = maybePromise._target();\n                        var bitField = maybePromise._bitField;\n                        ;\n                        if (((bitField & 50397184) === 0)) {\n                            maybePromise._then(callbacks[i], reject,\n                                               undefined, ret, holder);\n                            promiseSetters[i](maybePromise, holder);\n                            holder.asyncNeeded = false;\n                        } else if (((bitField & 33554432) !== 0)) {\n                            callbacks[i].call(ret,\n                                              maybePromise._value(), holder);\n                        } else if (((bitField & 16777216) !== 0)) {\n                            ret._reject(maybePromise._reason());\n                        } else {\n                            ret._cancel();\n                        }\n                    } else {\n                        callbacks[i].call(ret, maybePromise, holder);\n                    }\n                }\n\n                if (!ret._isFateSealed()) {\n                    if (holder.asyncNeeded) {\n                        var domain = getDomain();\n                        if (domain !== null) {\n                            holder.fn = util.domainBind(domain, holder.fn);\n                        }\n                    }\n                    ret._setAsyncGuaranteed();\n                    ret._setOnCancel(holder);\n                }\n                return ret;\n            }\n        }\n    }\n    var $_len = arguments.length;var args = new Array($_len); for(var $_i = 0; $_i < $_len; ++$_i) {args[$_i] = arguments[$_i];};\n    if (fn) args.pop();\n    var ret = new PromiseArray(args).promise();\n    return fn !== undefined ? ret.spread(fn) : ret;\n};\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GACd,SAAS,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAC3D,SAAS;IAClB,IAAI;IACJ,IAAI,cAAc,KAAK,WAAW;IAClC,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI;IAEJ,wCAAY;QACZ,IAAI,aAAa;YACb,IAAI,eAAe,SAAS,CAAC;gBACzB,OAAO,IAAI,SAAS,SAAS,UAAU;;;;cAIjC,OAAO,CAAC,UAAU;YAC5B;YAEA,IAAI,gBAAgB,SAAS,CAAC;gBAC1B,OAAO,IAAI,SAAS,WAAW,UAAU;;;cAGnC,OAAO,CAAC,UAAU;YAC5B;YAEA,IAAI,sBAAsB,SAAS,KAAK;gBACpC,IAAI,QAAQ,IAAI,MAAM;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;oBACnC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,IAAE,CAAC;gBAC9B;gBACA,IAAI,aAAa,MAAM,IAAI,CAAC,SAAS;gBACrC,IAAI,mBAAkB,mBAAmB,MAAM,GAAG,CAAC,SAAS,IAAI;oBAC5D,OAAO;8BACW,OAAO;;;;;gBAK7B,GAAG,IAAI,CAAC;gBACR,IAAI,kBAAkB,MAAM,IAAI,CAAC;gBACjC,IAAI,OAAO,YAAY;gBAGvB,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAwCX,OAAO,KAAK,OAAO,CAAC,gBAAgB,MAC/B,OAAO,CAAC,iBAAiB,OACzB,OAAO,CAAC,2BAA2B,iBACnC,OAAO,CAAC,sBAAsB,YAC9B,OAAO,CAAC,yBAAyB;gBAEtC,OAAO,IAAI,SAAS,YAAY,YAAY,WAAW,SAAS,MAC5C,UAAU,UAAU,SAAS;YACrD;YAEA,IAAI,gBAAgB,EAAE;YACtB,IAAI,gBAAgB,EAAE;YACtB,IAAI,iBAAiB,EAAE;YAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACxB,cAAc,IAAI,CAAC,oBAAoB,IAAI;gBAC3C,cAAc,IAAI,CAAC,aAAa,IAAI;gBACpC,eAAe,IAAI,CAAC,cAAc,IAAI;YAC1C;YAEA,SAAS,SAAU,MAAM;gBACrB,IAAI,CAAC,OAAO,CAAC;YACjB;QACJ;IAAC;IAED,QAAQ,IAAI,GAAG;QACX,IAAI,OAAO,UAAU,MAAM,GAAG;QAC9B,IAAI;QACJ,IAAI,OAAO,KAAK,OAAO,SAAS,CAAC,KAAK,KAAK,YAAY;YACnD,KAAK,SAAS,CAAC,KAAK;YACpB,wCAAY;gBACR,IAAI,QAAQ,KAAK,aAAa;oBAC1B,IAAI,MAAM,IAAI,QAAQ;oBACtB,IAAI,kBAAkB;oBACtB,IAAI,cAAc,aAAa,CAAC,OAAO,EAAE;oBACzC,IAAI,SAAS,IAAI,YAAY;oBAC7B,IAAI,YAAY;oBAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;wBAC3B,IAAI,eAAe,oBAAoB,SAAS,CAAC,EAAE,EAAE;wBACrD,IAAI,wBAAwB,SAAS;4BACjC,eAAe,aAAa,OAAO;4BACnC,IAAI,WAAW,aAAa,SAAS;;4BAErC,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gCAC/B,aAAa,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,QACd,WAAW,KAAK;gCACnC,cAAc,CAAC,EAAE,CAAC,cAAc;gCAChC,OAAO,WAAW,GAAG;4BACzB,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gCACtC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KACA,aAAa,MAAM,IAAI;4BAC7C,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gCACtC,IAAI,OAAO,CAAC,aAAa,OAAO;4BACpC,OAAO;gCACH,IAAI,OAAO;4BACf;wBACJ,OAAO;4BACH,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,cAAc;wBACzC;oBACJ;oBAEA,IAAI,CAAC,IAAI,aAAa,IAAI;wBACtB,IAAI,OAAO,WAAW,EAAE;4BACpB,IAAI,SAAS;4BACb,IAAI,WAAW,MAAM;gCACjB,OAAO,EAAE,GAAG,KAAK,UAAU,CAAC,QAAQ,OAAO,EAAE;4BACjD;wBACJ;wBACA,IAAI,mBAAmB;wBACvB,IAAI,YAAY,CAAC;oBACrB;oBACA,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,QAAQ,UAAU,MAAM;QAAC,IAAI,OAAO,IAAI,MAAM;QAAQ,IAAI,IAAI,MAAM,GAAG,MAAM,OAAO,EAAE,IAAK;YAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;QAAC;;QAC3H,IAAI,IAAI,KAAK,GAAG;QAChB,IAAI,MAAM,IAAI,aAAa,MAAM,OAAO;QACxC,OAAO,OAAO,YAAY,IAAI,MAAM,CAAC,MAAM;IAC/C;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2638, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/map.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise,\n                          PromiseArray,\n                          apiRejection,\n                          tryConvertToPromise,\n                          INTERNAL,\n                          debug) {\nvar getDomain = Promise._getDomain;\nvar util = require(\"./util\");\nvar tryCatch = util.tryCatch;\nvar errorObj = util.errorObj;\nvar async = Promise._async;\n\nfunction MappingPromiseArray(promises, fn, limit, _filter) {\n    this.constructor$(promises);\n    this._promise._captureStackTrace();\n    var domain = getDomain();\n    this._callback = domain === null ? fn : util.domainBind(domain, fn);\n    this._preservedValues = _filter === INTERNAL\n        ? new Array(this.length())\n        : null;\n    this._limit = limit;\n    this._inFlight = 0;\n    this._queue = [];\n    async.invoke(this._asyncInit, this, undefined);\n}\nutil.inherits(MappingPromiseArray, PromiseArray);\n\nMappingPromiseArray.prototype._asyncInit = function() {\n    this._init$(undefined, -2);\n};\n\nMappingPromiseArray.prototype._init = function () {};\n\nMappingPromiseArray.prototype._promiseFulfilled = function (value, index) {\n    var values = this._values;\n    var length = this.length();\n    var preservedValues = this._preservedValues;\n    var limit = this._limit;\n\n    if (index < 0) {\n        index = (index * -1) - 1;\n        values[index] = value;\n        if (limit >= 1) {\n            this._inFlight--;\n            this._drainQueue();\n            if (this._isResolved()) return true;\n        }\n    } else {\n        if (limit >= 1 && this._inFlight >= limit) {\n            values[index] = value;\n            this._queue.push(index);\n            return false;\n        }\n        if (preservedValues !== null) preservedValues[index] = value;\n\n        var promise = this._promise;\n        var callback = this._callback;\n        var receiver = promise._boundValue();\n        promise._pushContext();\n        var ret = tryCatch(callback).call(receiver, value, index, length);\n        var promiseCreated = promise._popContext();\n        debug.checkForgottenReturns(\n            ret,\n            promiseCreated,\n            preservedValues !== null ? \"Promise.filter\" : \"Promise.map\",\n            promise\n        );\n        if (ret === errorObj) {\n            this._reject(ret.e);\n            return true;\n        }\n\n        var maybePromise = tryConvertToPromise(ret, this._promise);\n        if (maybePromise instanceof Promise) {\n            maybePromise = maybePromise._target();\n            var bitField = maybePromise._bitField;\n            ;\n            if (((bitField & 50397184) === 0)) {\n                if (limit >= 1) this._inFlight++;\n                values[index] = maybePromise;\n                maybePromise._proxy(this, (index + 1) * -1);\n                return false;\n            } else if (((bitField & 33554432) !== 0)) {\n                ret = maybePromise._value();\n            } else if (((bitField & 16777216) !== 0)) {\n                this._reject(maybePromise._reason());\n                return true;\n            } else {\n                this._cancel();\n                return true;\n            }\n        }\n        values[index] = ret;\n    }\n    var totalResolved = ++this._totalResolved;\n    if (totalResolved >= length) {\n        if (preservedValues !== null) {\n            this._filter(values, preservedValues);\n        } else {\n            this._resolve(values);\n        }\n        return true;\n    }\n    return false;\n};\n\nMappingPromiseArray.prototype._drainQueue = function () {\n    var queue = this._queue;\n    var limit = this._limit;\n    var values = this._values;\n    while (queue.length > 0 && this._inFlight < limit) {\n        if (this._isResolved()) return;\n        var index = queue.pop();\n        this._promiseFulfilled(values[index], index);\n    }\n};\n\nMappingPromiseArray.prototype._filter = function (booleans, values) {\n    var len = values.length;\n    var ret = new Array(len);\n    var j = 0;\n    for (var i = 0; i < len; ++i) {\n        if (booleans[i]) ret[j++] = values[i];\n    }\n    ret.length = j;\n    this._resolve(ret);\n};\n\nMappingPromiseArray.prototype.preservedValues = function () {\n    return this._preservedValues;\n};\n\nfunction map(promises, fn, options, _filter) {\n    if (typeof fn !== \"function\") {\n        return apiRejection(\"expecting a function but got \" + util.classString(fn));\n    }\n\n    var limit = 0;\n    if (options !== undefined) {\n        if (typeof options === \"object\" && options !== null) {\n            if (typeof options.concurrency !== \"number\") {\n                return Promise.reject(\n                    new TypeError(\"'concurrency' must be a number but it is \" +\n                                    util.classString(options.concurrency)));\n            }\n            limit = options.concurrency;\n        } else {\n            return Promise.reject(new TypeError(\n                            \"options argument must be an object but it is \" +\n                             util.classString(options)));\n        }\n    }\n    limit = typeof limit === \"number\" &&\n        isFinite(limit) && limit >= 1 ? limit : 0;\n    return new MappingPromiseArray(promises, fn, limit, _filter).promise();\n}\n\nPromise.prototype.map = function (fn, options) {\n    return map(this, fn, options, null);\n};\n\nPromise.map = function (promises, fn, options, _filter) {\n    return map(promises, fn, options, _filter);\n};\n\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,QAAQ,EACR,KAAK;IAC/B,IAAI,YAAY,QAAQ,UAAU;IAClC,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,QAAQ,QAAQ,MAAM;IAE1B,SAAS,oBAAoB,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO;QACrD,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,kBAAkB;QAChC,IAAI,SAAS;QACb,IAAI,CAAC,SAAS,GAAG,WAAW,OAAO,KAAK,KAAK,UAAU,CAAC,QAAQ;QAChE,IAAI,CAAC,gBAAgB,GAAG,YAAY,WAC9B,IAAI,MAAM,IAAI,CAAC,MAAM,MACrB;QACN,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE;IACxC;IACA,KAAK,QAAQ,CAAC,qBAAqB;IAEnC,oBAAoB,SAAS,CAAC,UAAU,GAAG;QACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IAC5B;IAEA,oBAAoB,SAAS,CAAC,KAAK,GAAG,YAAa;IAEnD,oBAAoB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,KAAK;QACpE,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,QAAQ,IAAI,CAAC,MAAM;QAEvB,IAAI,QAAQ,GAAG;YACX,QAAQ,AAAC,QAAQ,CAAC,IAAK;YACvB,MAAM,CAAC,MAAM,GAAG;YAChB,IAAI,SAAS,GAAG;gBACZ,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,WAAW;gBAChB,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO;YACnC;QACJ,OAAO;YACH,IAAI,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,OAAO;gBACvC,MAAM,CAAC,MAAM,GAAG;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjB,OAAO;YACX;YACA,IAAI,oBAAoB,MAAM,eAAe,CAAC,MAAM,GAAG;YAEvD,IAAI,UAAU,IAAI,CAAC,QAAQ;YAC3B,IAAI,WAAW,IAAI,CAAC,SAAS;YAC7B,IAAI,WAAW,QAAQ,WAAW;YAClC,QAAQ,YAAY;YACpB,IAAI,MAAM,SAAS,UAAU,IAAI,CAAC,UAAU,OAAO,OAAO;YAC1D,IAAI,iBAAiB,QAAQ,WAAW;YACxC,MAAM,qBAAqB,CACvB,KACA,gBACA,oBAAoB,OAAO,mBAAmB,eAC9C;YAEJ,IAAI,QAAQ,UAAU;gBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClB,OAAO;YACX;YAEA,IAAI,eAAe,oBAAoB,KAAK,IAAI,CAAC,QAAQ;YACzD,IAAI,wBAAwB,SAAS;gBACjC,eAAe,aAAa,OAAO;gBACnC,IAAI,WAAW,aAAa,SAAS;;gBAErC,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBAC/B,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;oBAC9B,MAAM,CAAC,MAAM,GAAG;oBAChB,aAAa,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACzC,OAAO;gBACX,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBACtC,MAAM,aAAa,MAAM;gBAC7B,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBACtC,IAAI,CAAC,OAAO,CAAC,aAAa,OAAO;oBACjC,OAAO;gBACX,OAAO;oBACH,IAAI,CAAC,OAAO;oBACZ,OAAO;gBACX;YACJ;YACA,MAAM,CAAC,MAAM,GAAG;QACpB;QACA,IAAI,gBAAgB,EAAE,IAAI,CAAC,cAAc;QACzC,IAAI,iBAAiB,QAAQ;YACzB,IAAI,oBAAoB,MAAM;gBAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ;YACzB,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC;YAClB;YACA,OAAO;QACX;QACA,OAAO;IACX;IAEA,oBAAoB,SAAS,CAAC,WAAW,GAAG;QACxC,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,MAAO,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,GAAG,MAAO;YAC/C,IAAI,IAAI,CAAC,WAAW,IAAI;YACxB,IAAI,QAAQ,MAAM,GAAG;YACrB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE;QAC1C;IACJ;IAEA,oBAAoB,SAAS,CAAC,OAAO,GAAG,SAAU,QAAQ,EAAE,MAAM;QAC9D,IAAI,MAAM,OAAO,MAAM;QACvB,IAAI,MAAM,IAAI,MAAM;QACpB,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YAC1B,IAAI,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,EAAE;QACzC;QACA,IAAI,MAAM,GAAG;QACb,IAAI,CAAC,QAAQ,CAAC;IAClB;IAEA,oBAAoB,SAAS,CAAC,eAAe,GAAG;QAC5C,OAAO,IAAI,CAAC,gBAAgB;IAChC;IAEA,SAAS,IAAI,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO;QACvC,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,aAAa,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QAEA,IAAI,QAAQ;QACZ,IAAI,YAAY,WAAW;YACvB,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;gBACjD,IAAI,OAAO,QAAQ,WAAW,KAAK,UAAU;oBACzC,OAAO,QAAQ,MAAM,CACjB,IAAI,UAAU,8CACE,KAAK,WAAW,CAAC,QAAQ,WAAW;gBAC5D;gBACA,QAAQ,QAAQ,WAAW;YAC/B,OAAO;gBACH,OAAO,QAAQ,MAAM,CAAC,IAAI,UACV,kDACC,KAAK,WAAW,CAAC;YACtC;QACJ;QACA,QAAQ,OAAO,UAAU,YACrB,SAAS,UAAU,SAAS,IAAI,QAAQ;QAC5C,OAAO,IAAI,oBAAoB,UAAU,IAAI,OAAO,SAAS,OAAO;IACxE;IAEA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAU,EAAE,EAAE,OAAO;QACzC,OAAO,IAAI,IAAI,EAAE,IAAI,SAAS;IAClC;IAEA,QAAQ,GAAG,GAAG,SAAU,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO;QAClD,OAAO,IAAI,UAAU,IAAI,SAAS;IACtC;AAGA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2778, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/call_get.js"], "sourcesContent": ["\"use strict\";\nvar cr = Object.create;\nif (cr) {\n    var callerCache = cr(null);\n    var getterCache = cr(null);\n    callerCache[\" size\"] = getterCache[\" size\"] = 0;\n}\n\nmodule.exports = function(Promise) {\nvar util = require(\"./util\");\nvar canEvaluate = util.canEvaluate;\nvar isIdentifier = util.isIdentifier;\n\nvar getMethodCaller;\nvar getGetter;\nif (!false) {\nvar makeMethodCaller = function (methodName) {\n    return new Function(\"ensureMethod\", \"                                    \\n\\\n        return function(obj) {                                               \\n\\\n            'use strict'                                                     \\n\\\n            var len = this.length;                                           \\n\\\n            ensureMethod(obj, 'methodName');                                 \\n\\\n            switch(len) {                                                    \\n\\\n                case 1: return obj.methodName(this[0]);                      \\n\\\n                case 2: return obj.methodName(this[0], this[1]);             \\n\\\n                case 3: return obj.methodName(this[0], this[1], this[2]);    \\n\\\n                case 0: return obj.methodName();                             \\n\\\n                default:                                                     \\n\\\n                    return obj.methodName.apply(obj, this);                  \\n\\\n            }                                                                \\n\\\n        };                                                                   \\n\\\n        \".replace(/methodName/g, methodName))(ensureMethod);\n};\n\nvar makeGetter = function (propertyName) {\n    return new Function(\"obj\", \"                                             \\n\\\n        'use strict';                                                        \\n\\\n        return obj.propertyName;                                             \\n\\\n        \".replace(\"propertyName\", propertyName));\n};\n\nvar getCompiled = function(name, compiler, cache) {\n    var ret = cache[name];\n    if (typeof ret !== \"function\") {\n        if (!isIdentifier(name)) {\n            return null;\n        }\n        ret = compiler(name);\n        cache[name] = ret;\n        cache[\" size\"]++;\n        if (cache[\" size\"] > 512) {\n            var keys = Object.keys(cache);\n            for (var i = 0; i < 256; ++i) delete cache[keys[i]];\n            cache[\" size\"] = keys.length - 256;\n        }\n    }\n    return ret;\n};\n\ngetMethodCaller = function(name) {\n    return getCompiled(name, makeMethodCaller, callerCache);\n};\n\ngetGetter = function(name) {\n    return getCompiled(name, makeGetter, getterCache);\n};\n}\n\nfunction ensureMethod(obj, methodName) {\n    var fn;\n    if (obj != null) fn = obj[methodName];\n    if (typeof fn !== \"function\") {\n        var message = \"Object \" + util.classString(obj) + \" has no method '\" +\n            util.toString(methodName) + \"'\";\n        throw new Promise.TypeError(message);\n    }\n    return fn;\n}\n\nfunction caller(obj) {\n    var methodName = this.pop();\n    var fn = ensureMethod(obj, methodName);\n    return fn.apply(obj, this);\n}\nPromise.prototype.call = function (methodName) {\n    var $_len = arguments.length;var args = new Array(Math.max($_len - 1, 0)); for(var $_i = 1; $_i < $_len; ++$_i) {args[$_i - 1] = arguments[$_i];};\n    if (!false) {\n        if (canEvaluate) {\n            var maybeCaller = getMethodCaller(methodName);\n            if (maybeCaller !== null) {\n                return this._then(\n                    maybeCaller, undefined, undefined, args, undefined);\n            }\n        }\n    }\n    args.push(methodName);\n    return this._then(caller, undefined, undefined, args, undefined);\n};\n\nfunction namedGetter(obj) {\n    return obj[this];\n}\nfunction indexedGetter(obj) {\n    var index = +this;\n    if (index < 0) index = Math.max(0, index + obj.length);\n    return obj[index];\n}\nPromise.prototype.get = function (propertyName) {\n    var isIndex = (typeof propertyName === \"number\");\n    var getter;\n    if (!isIndex) {\n        if (canEvaluate) {\n            var maybeGetter = getGetter(propertyName);\n            getter = maybeGetter !== null ? maybeGetter : namedGetter;\n        } else {\n            getter = namedGetter;\n        }\n    } else {\n        getter = indexedGetter;\n    }\n    return this._then(getter, undefined, undefined, propertyName, undefined);\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,IAAI,KAAK,OAAO,MAAM;AACtB,IAAI,IAAI;IACJ,IAAI,cAAc,GAAG;IACrB,IAAI,cAAc,GAAG;IACrB,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,GAAG;AAClD;AAEA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,IAAI;IACJ,IAAI,cAAc,KAAK,WAAW;IAClC,IAAI,eAAe,KAAK,YAAY;IAEpC,IAAI;IACJ,IAAI;IACJ,wCAAY;QACZ,IAAI,mBAAmB,SAAU,UAAU;YACvC,OAAO,IAAI,SAAS,gBAAgB;;;;;;;;;;;;;;UAc9B,OAAO,CAAC,eAAe,aAAa;QAC9C;QAEA,IAAI,aAAa,SAAU,YAAY;YACnC,OAAO,IAAI,SAAS,OAAO;;;UAGrB,OAAO,CAAC,gBAAgB;QAClC;QAEA,IAAI,cAAc,SAAS,IAAI,EAAE,QAAQ,EAAE,KAAK;YAC5C,IAAI,MAAM,KAAK,CAAC,KAAK;YACrB,IAAI,OAAO,QAAQ,YAAY;gBAC3B,IAAI,CAAC,aAAa,OAAO;oBACrB,OAAO;gBACX;gBACA,MAAM,SAAS;gBACf,KAAK,CAAC,KAAK,GAAG;gBACd,KAAK,CAAC,QAAQ;gBACd,IAAI,KAAK,CAAC,QAAQ,GAAG,KAAK;oBACtB,IAAI,OAAO,OAAO,IAAI,CAAC;oBACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnD,KAAK,CAAC,QAAQ,GAAG,KAAK,MAAM,GAAG;gBACnC;YACJ;YACA,OAAO;QACX;QAEA,kBAAkB,SAAS,IAAI;YAC3B,OAAO,YAAY,MAAM,kBAAkB;QAC/C;QAEA,YAAY,SAAS,IAAI;YACrB,OAAO,YAAY,MAAM,YAAY;QACzC;IACA;IAEA,SAAS,aAAa,GAAG,EAAE,UAAU;QACjC,IAAI;QACJ,IAAI,OAAO,MAAM,KAAK,GAAG,CAAC,WAAW;QACrC,IAAI,OAAO,OAAO,YAAY;YAC1B,IAAI,UAAU,YAAY,KAAK,WAAW,CAAC,OAAO,qBAC9C,KAAK,QAAQ,CAAC,cAAc;YAChC,MAAM,IAAI,QAAQ,SAAS,CAAC;QAChC;QACA,OAAO;IACX;IAEA,SAAS,OAAO,GAAG;QACf,IAAI,aAAa,IAAI,CAAC,GAAG;QACzB,IAAI,KAAK,aAAa,KAAK;QAC3B,OAAO,GAAG,KAAK,CAAC,KAAK,IAAI;IAC7B;IACA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,UAAU;QACzC,IAAI,QAAQ,UAAU,MAAM;QAAC,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,CAAC,QAAQ,GAAG;QAAK,IAAI,IAAI,MAAM,GAAG,MAAM,OAAO,EAAE,IAAK;YAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAAC;;QAChJ,wCAAY;YACR,IAAI,aAAa;gBACb,IAAI,cAAc,gBAAgB;gBAClC,IAAI,gBAAgB,MAAM;oBACtB,OAAO,IAAI,CAAC,KAAK,CACb,aAAa,WAAW,WAAW,MAAM;gBACjD;YACJ;QACJ;QACA,KAAK,IAAI,CAAC;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,WAAW,WAAW,MAAM;IAC1D;IAEA,SAAS,YAAY,GAAG;QACpB,OAAO,GAAG,CAAC,IAAI,CAAC;IACpB;IACA,SAAS,cAAc,GAAG;QACtB,IAAI,QAAQ,CAAC,IAAI;QACjB,IAAI,QAAQ,GAAG,QAAQ,KAAK,GAAG,CAAC,GAAG,QAAQ,IAAI,MAAM;QACrD,OAAO,GAAG,CAAC,MAAM;IACrB;IACA,QAAQ,SAAS,CAAC,GAAG,GAAG,SAAU,YAAY;QAC1C,IAAI,UAAW,OAAO,iBAAiB;QACvC,IAAI;QACJ,IAAI,CAAC,SAAS;YACV,IAAI,aAAa;gBACb,IAAI,cAAc,UAAU;gBAC5B,SAAS,gBAAgB,OAAO,cAAc;YAClD,OAAO;gBACH,SAAS;YACb;QACJ,OAAO;YACH,SAAS;QACb;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,WAAW,WAAW,cAAc;IAClE;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2900, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/using.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function (Promise, apiRejection, tryConvertToPromise,\n    createContext, INTERNAL, debug) {\n    var util = require(\"./util\");\n    var TypeError = require(\"./errors\").TypeError;\n    var inherits = require(\"./util\").inherits;\n    var errorObj = util.errorObj;\n    var tryCatch = util.tryCatch;\n    var NULL = {};\n\n    function thrower(e) {\n        setTimeout(function(){throw e;}, 0);\n    }\n\n    function castPreservingDisposable(thenable) {\n        var maybePromise = tryConvertToPromise(thenable);\n        if (maybePromise !== thenable &&\n            typeof thenable._isDisposable === \"function\" &&\n            typeof thenable._getDisposer === \"function\" &&\n            thenable._isDisposable()) {\n            maybePromise._setDisposable(thenable._getDisposer());\n        }\n        return maybePromise;\n    }\n    function dispose(resources, inspection) {\n        var i = 0;\n        var len = resources.length;\n        var ret = new Promise(INTERNAL);\n        function iterator() {\n            if (i >= len) return ret._fulfill();\n            var maybePromise = castPreservingDisposable(resources[i++]);\n            if (maybePromise instanceof Promise &&\n                maybePromise._isDisposable()) {\n                try {\n                    maybePromise = tryConvertToPromise(\n                        maybePromise._getDisposer().tryDispose(inspection),\n                        resources.promise);\n                } catch (e) {\n                    return thrower(e);\n                }\n                if (maybePromise instanceof Promise) {\n                    return maybePromise._then(iterator, thrower,\n                                              null, null, null);\n                }\n            }\n            iterator();\n        }\n        iterator();\n        return ret;\n    }\n\n    function Disposer(data, promise, context) {\n        this._data = data;\n        this._promise = promise;\n        this._context = context;\n    }\n\n    Disposer.prototype.data = function () {\n        return this._data;\n    };\n\n    Disposer.prototype.promise = function () {\n        return this._promise;\n    };\n\n    Disposer.prototype.resource = function () {\n        if (this.promise().isFulfilled()) {\n            return this.promise().value();\n        }\n        return NULL;\n    };\n\n    Disposer.prototype.tryDispose = function(inspection) {\n        var resource = this.resource();\n        var context = this._context;\n        if (context !== undefined) context._pushContext();\n        var ret = resource !== NULL\n            ? this.doDispose(resource, inspection) : null;\n        if (context !== undefined) context._popContext();\n        this._promise._unsetDisposable();\n        this._data = null;\n        return ret;\n    };\n\n    Disposer.isDisposer = function (d) {\n        return (d != null &&\n                typeof d.resource === \"function\" &&\n                typeof d.tryDispose === \"function\");\n    };\n\n    function FunctionDisposer(fn, promise, context) {\n        this.constructor$(fn, promise, context);\n    }\n    inherits(FunctionDisposer, Disposer);\n\n    FunctionDisposer.prototype.doDispose = function (resource, inspection) {\n        var fn = this.data();\n        return fn.call(resource, resource, inspection);\n    };\n\n    function maybeUnwrapDisposer(value) {\n        if (Disposer.isDisposer(value)) {\n            this.resources[this.index]._setDisposable(value);\n            return value.promise();\n        }\n        return value;\n    }\n\n    function ResourceList(length) {\n        this.length = length;\n        this.promise = null;\n        this[length-1] = null;\n    }\n\n    ResourceList.prototype._resultCancelled = function() {\n        var len = this.length;\n        for (var i = 0; i < len; ++i) {\n            var item = this[i];\n            if (item instanceof Promise) {\n                item.cancel();\n            }\n        }\n    };\n\n    Promise.using = function () {\n        var len = arguments.length;\n        if (len < 2) return apiRejection(\n                        \"you must pass at least 2 arguments to Promise.using\");\n        var fn = arguments[len - 1];\n        if (typeof fn !== \"function\") {\n            return apiRejection(\"expecting a function but got \" + util.classString(fn));\n        }\n        var input;\n        var spreadArgs = true;\n        if (len === 2 && Array.isArray(arguments[0])) {\n            input = arguments[0];\n            len = input.length;\n            spreadArgs = false;\n        } else {\n            input = arguments;\n            len--;\n        }\n        var resources = new ResourceList(len);\n        for (var i = 0; i < len; ++i) {\n            var resource = input[i];\n            if (Disposer.isDisposer(resource)) {\n                var disposer = resource;\n                resource = resource.promise();\n                resource._setDisposable(disposer);\n            } else {\n                var maybePromise = tryConvertToPromise(resource);\n                if (maybePromise instanceof Promise) {\n                    resource =\n                        maybePromise._then(maybeUnwrapDisposer, null, null, {\n                            resources: resources,\n                            index: i\n                    }, undefined);\n                }\n            }\n            resources[i] = resource;\n        }\n\n        var reflectedResources = new Array(resources.length);\n        for (var i = 0; i < reflectedResources.length; ++i) {\n            reflectedResources[i] = Promise.resolve(resources[i]).reflect();\n        }\n\n        var resultPromise = Promise.all(reflectedResources)\n            .then(function(inspections) {\n                for (var i = 0; i < inspections.length; ++i) {\n                    var inspection = inspections[i];\n                    if (inspection.isRejected()) {\n                        errorObj.e = inspection.error();\n                        return errorObj;\n                    } else if (!inspection.isFulfilled()) {\n                        resultPromise.cancel();\n                        return;\n                    }\n                    inspections[i] = inspection.value();\n                }\n                promise._pushContext();\n\n                fn = tryCatch(fn);\n                var ret = spreadArgs\n                    ? fn.apply(undefined, inspections) : fn(inspections);\n                var promiseCreated = promise._popContext();\n                debug.checkForgottenReturns(\n                    ret, promiseCreated, \"Promise.using\", promise);\n                return ret;\n            });\n\n        var promise = resultPromise.lastly(function() {\n            var inspection = new Promise.PromiseInspection(resultPromise);\n            return dispose(resources, inspection);\n        });\n        resources.promise = promise;\n        promise._setOnCancel(resources);\n        return promise;\n    };\n\n    Promise.prototype._setDisposable = function (disposer) {\n        this._bitField = this._bitField | 131072;\n        this._disposer = disposer;\n    };\n\n    Promise.prototype._isDisposable = function () {\n        return (this._bitField & 131072) > 0;\n    };\n\n    Promise.prototype._getDisposer = function () {\n        return this._disposer;\n    };\n\n    Promise.prototype._unsetDisposable = function () {\n        this._bitField = this._bitField & (~131072);\n        this._disposer = undefined;\n    };\n\n    Promise.prototype.disposer = function (fn) {\n        if (typeof fn === \"function\") {\n            return new FunctionDisposer(fn, this, createContext());\n        }\n        throw new TypeError();\n    };\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAU,OAAO,EAAE,YAAY,EAAE,mBAAmB,EACjE,aAAa,EAAE,QAAQ,EAAE,KAAK;IAC9B,IAAI;IACJ,IAAI,YAAY,0GAAoB,SAAS;IAC7C,IAAI,WAAW,wGAAkB,QAAQ;IACzC,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,OAAO,CAAC;IAEZ,SAAS,QAAQ,CAAC;QACd,WAAW;YAAW,MAAM;QAAE,GAAG;IACrC;IAEA,SAAS,yBAAyB,QAAQ;QACtC,IAAI,eAAe,oBAAoB;QACvC,IAAI,iBAAiB,YACjB,OAAO,SAAS,aAAa,KAAK,cAClC,OAAO,SAAS,YAAY,KAAK,cACjC,SAAS,aAAa,IAAI;YAC1B,aAAa,cAAc,CAAC,SAAS,YAAY;QACrD;QACA,OAAO;IACX;IACA,SAAS,QAAQ,SAAS,EAAE,UAAU;QAClC,IAAI,IAAI;QACR,IAAI,MAAM,UAAU,MAAM;QAC1B,IAAI,MAAM,IAAI,QAAQ;QACtB,SAAS;YACL,IAAI,KAAK,KAAK,OAAO,IAAI,QAAQ;YACjC,IAAI,eAAe,yBAAyB,SAAS,CAAC,IAAI;YAC1D,IAAI,wBAAwB,WACxB,aAAa,aAAa,IAAI;gBAC9B,IAAI;oBACA,eAAe,oBACX,aAAa,YAAY,GAAG,UAAU,CAAC,aACvC,UAAU,OAAO;gBACzB,EAAE,OAAO,GAAG;oBACR,OAAO,QAAQ;gBACnB;gBACA,IAAI,wBAAwB,SAAS;oBACjC,OAAO,aAAa,KAAK,CAAC,UAAU,SACV,MAAM,MAAM;gBAC1C;YACJ;YACA;QACJ;QACA;QACA,OAAO;IACX;IAEA,SAAS,SAAS,IAAI,EAAE,OAAO,EAAE,OAAO;QACpC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA,SAAS,SAAS,CAAC,IAAI,GAAG;QACtB,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,OAAO,IAAI,CAAC,QAAQ;IACxB;IAEA,SAAS,SAAS,CAAC,QAAQ,GAAG;QAC1B,IAAI,IAAI,CAAC,OAAO,GAAG,WAAW,IAAI;YAC9B,OAAO,IAAI,CAAC,OAAO,GAAG,KAAK;QAC/B;QACA,OAAO;IACX;IAEA,SAAS,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU;QAC/C,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,YAAY,WAAW,QAAQ,YAAY;QAC/C,IAAI,MAAM,aAAa,OACjB,IAAI,CAAC,SAAS,CAAC,UAAU,cAAc;QAC7C,IAAI,YAAY,WAAW,QAAQ,WAAW;QAC9C,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAC9B,IAAI,CAAC,KAAK,GAAG;QACb,OAAO;IACX;IAEA,SAAS,UAAU,GAAG,SAAU,CAAC;QAC7B,OAAQ,KAAK,QACL,OAAO,EAAE,QAAQ,KAAK,cACtB,OAAO,EAAE,UAAU,KAAK;IACpC;IAEA,SAAS,iBAAiB,EAAE,EAAE,OAAO,EAAE,OAAO;QAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,SAAS;IACnC;IACA,SAAS,kBAAkB;IAE3B,iBAAiB,SAAS,CAAC,SAAS,GAAG,SAAU,QAAQ,EAAE,UAAU;QACjE,IAAI,KAAK,IAAI,CAAC,IAAI;QAClB,OAAO,GAAG,IAAI,CAAC,UAAU,UAAU;IACvC;IAEA,SAAS,oBAAoB,KAAK;QAC9B,IAAI,SAAS,UAAU,CAAC,QAAQ;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC;YAC1C,OAAO,MAAM,OAAO;QACxB;QACA,OAAO;IACX;IAEA,SAAS,aAAa,MAAM;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAO,EAAE,GAAG;IACrB;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YAC1B,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,gBAAgB,SAAS;gBACzB,KAAK,MAAM;YACf;QACJ;IACJ;IAEA,QAAQ,KAAK,GAAG;QACZ,IAAI,MAAM,UAAU,MAAM;QAC1B,IAAI,MAAM,GAAG,OAAO,aACJ;QAChB,IAAI,KAAK,SAAS,CAAC,MAAM,EAAE;QAC3B,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,aAAa,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,IAAI;QACJ,IAAI,aAAa;QACjB,IAAI,QAAQ,KAAK,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG;YAC1C,QAAQ,SAAS,CAAC,EAAE;YACpB,MAAM,MAAM,MAAM;YAClB,aAAa;QACjB,OAAO;YACH,QAAQ;YACR;QACJ;QACA,IAAI,YAAY,IAAI,aAAa;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YAC1B,IAAI,WAAW,KAAK,CAAC,EAAE;YACvB,IAAI,SAAS,UAAU,CAAC,WAAW;gBAC/B,IAAI,WAAW;gBACf,WAAW,SAAS,OAAO;gBAC3B,SAAS,cAAc,CAAC;YAC5B,OAAO;gBACH,IAAI,eAAe,oBAAoB;gBACvC,IAAI,wBAAwB,SAAS;oBACjC,WACI,aAAa,KAAK,CAAC,qBAAqB,MAAM,MAAM;wBAChD,WAAW;wBACX,OAAO;oBACf,GAAG;gBACP;YACJ;YACA,SAAS,CAAC,EAAE,GAAG;QACnB;QAEA,IAAI,qBAAqB,IAAI,MAAM,UAAU,MAAM;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,MAAM,EAAE,EAAE,EAAG;YAChD,kBAAkB,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO;QACjE;QAEA,IAAI,gBAAgB,QAAQ,GAAG,CAAC,oBAC3B,IAAI,CAAC,SAAS,WAAW;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,EAAE,EAAG;gBACzC,IAAI,aAAa,WAAW,CAAC,EAAE;gBAC/B,IAAI,WAAW,UAAU,IAAI;oBACzB,SAAS,CAAC,GAAG,WAAW,KAAK;oBAC7B,OAAO;gBACX,OAAO,IAAI,CAAC,WAAW,WAAW,IAAI;oBAClC,cAAc,MAAM;oBACpB;gBACJ;gBACA,WAAW,CAAC,EAAE,GAAG,WAAW,KAAK;YACrC;YACA,QAAQ,YAAY;YAEpB,KAAK,SAAS;YACd,IAAI,MAAM,aACJ,GAAG,KAAK,CAAC,WAAW,eAAe,GAAG;YAC5C,IAAI,iBAAiB,QAAQ,WAAW;YACxC,MAAM,qBAAqB,CACvB,KAAK,gBAAgB,iBAAiB;YAC1C,OAAO;QACX;QAEJ,IAAI,UAAU,cAAc,MAAM,CAAC;YAC/B,IAAI,aAAa,IAAI,QAAQ,iBAAiB,CAAC;YAC/C,OAAO,QAAQ,WAAW;QAC9B;QACA,UAAU,OAAO,GAAG;QACpB,QAAQ,YAAY,CAAC;QACrB,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAU,QAAQ;QACjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;QAClC,IAAI,CAAC,SAAS,GAAG;IACrB;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI;IACvC;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG;QAC7B,OAAO,IAAI,CAAC,SAAS;IACzB;IAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG;QACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAI,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG;IACrB;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE;QACrC,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,IAAI,iBAAiB,IAAI,IAAI,EAAE;QAC1C;QACA,MAAM,IAAI;IACd;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3093, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/timers.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL, debug) {\nvar util = require(\"./util\");\nvar TimeoutError = Promise.TimeoutError;\n\nfunction HandleWrapper(handle)  {\n    this.handle = handle;\n}\n\nHandleWrapper.prototype._resultCancelled = function() {\n    clearTimeout(this.handle);\n};\n\nvar afterValue = function(value) { return delay(+this).thenReturn(value); };\nvar delay = Promise.delay = function (ms, value) {\n    var ret;\n    var handle;\n    if (value !== undefined) {\n        ret = Promise.resolve(value)\n                ._then(afterValue, null, null, ms, undefined);\n        if (debug.cancellation() && value instanceof Promise) {\n            ret._setOnCancel(value);\n        }\n    } else {\n        ret = new Promise(INTERNAL);\n        handle = setTimeout(function() { ret._fulfill(); }, +ms);\n        if (debug.cancellation()) {\n            ret._setOnCancel(new HandleWrapper(handle));\n        }\n        ret._captureStackTrace();\n    }\n    ret._setAsyncGuaranteed();\n    return ret;\n};\n\nPromise.prototype.delay = function (ms) {\n    return delay(ms, this);\n};\n\nvar afterTimeout = function (promise, message, parent) {\n    var err;\n    if (typeof message !== \"string\") {\n        if (message instanceof Error) {\n            err = message;\n        } else {\n            err = new TimeoutError(\"operation timed out\");\n        }\n    } else {\n        err = new TimeoutError(message);\n    }\n    util.markAsOriginatingFromRejection(err);\n    promise._attachExtraTrace(err);\n    promise._reject(err);\n\n    if (parent != null) {\n        parent.cancel();\n    }\n};\n\nfunction successClear(value) {\n    clearTimeout(this.handle);\n    return value;\n}\n\nfunction failureClear(reason) {\n    clearTimeout(this.handle);\n    throw reason;\n}\n\nPromise.prototype.timeout = function (ms, message) {\n    ms = +ms;\n    var ret, parent;\n\n    var handleWrapper = new HandleWrapper(setTimeout(function timeoutTimeout() {\n        if (ret.isPending()) {\n            afterTimeout(ret, message, parent);\n        }\n    }, ms));\n\n    if (debug.cancellation()) {\n        parent = this.then();\n        ret = parent._then(successClear, failureClear,\n                            undefined, handleWrapper, undefined);\n        ret._setOnCancel(handleWrapper);\n    } else {\n        ret = this._then(successClear, failureClear,\n                            undefined, handleWrapper, undefined);\n    }\n\n    return ret;\n};\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,KAAK;IAClD,IAAI;IACJ,IAAI,eAAe,QAAQ,YAAY;IAEvC,SAAS,cAAc,MAAM;QACzB,IAAI,CAAC,MAAM,GAAG;IAClB;IAEA,cAAc,SAAS,CAAC,gBAAgB,GAAG;QACvC,aAAa,IAAI,CAAC,MAAM;IAC5B;IAEA,IAAI,aAAa,SAAS,KAAK;QAAI,OAAO,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC;IAAQ;IAC1E,IAAI,QAAQ,QAAQ,KAAK,GAAG,SAAU,EAAE,EAAE,KAAK;QAC3C,IAAI;QACJ,IAAI;QACJ,IAAI,UAAU,WAAW;YACrB,MAAM,QAAQ,OAAO,CAAC,OACb,KAAK,CAAC,YAAY,MAAM,MAAM,IAAI;YAC3C,IAAI,MAAM,YAAY,MAAM,iBAAiB,SAAS;gBAClD,IAAI,YAAY,CAAC;YACrB;QACJ,OAAO;YACH,MAAM,IAAI,QAAQ;YAClB,SAAS,WAAW;gBAAa,IAAI,QAAQ;YAAI,GAAG,CAAC;YACrD,IAAI,MAAM,YAAY,IAAI;gBACtB,IAAI,YAAY,CAAC,IAAI,cAAc;YACvC;YACA,IAAI,kBAAkB;QAC1B;QACA,IAAI,mBAAmB;QACvB,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAU,EAAE;QAClC,OAAO,MAAM,IAAI,IAAI;IACzB;IAEA,IAAI,eAAe,SAAU,OAAO,EAAE,OAAO,EAAE,MAAM;QACjD,IAAI;QACJ,IAAI,OAAO,YAAY,UAAU;YAC7B,IAAI,mBAAmB,OAAO;gBAC1B,MAAM;YACV,OAAO;gBACH,MAAM,IAAI,aAAa;YAC3B;QACJ,OAAO;YACH,MAAM,IAAI,aAAa;QAC3B;QACA,KAAK,8BAA8B,CAAC;QACpC,QAAQ,iBAAiB,CAAC;QAC1B,QAAQ,OAAO,CAAC;QAEhB,IAAI,UAAU,MAAM;YAChB,OAAO,MAAM;QACjB;IACJ;IAEA,SAAS,aAAa,KAAK;QACvB,aAAa,IAAI,CAAC,MAAM;QACxB,OAAO;IACX;IAEA,SAAS,aAAa,MAAM;QACxB,aAAa,IAAI,CAAC,MAAM;QACxB,MAAM;IACV;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAU,EAAE,EAAE,OAAO;QAC7C,KAAK,CAAC;QACN,IAAI,KAAK;QAET,IAAI,gBAAgB,IAAI,cAAc,WAAW,SAAS;YACtD,IAAI,IAAI,SAAS,IAAI;gBACjB,aAAa,KAAK,SAAS;YAC/B;QACJ,GAAG;QAEH,IAAI,MAAM,YAAY,IAAI;YACtB,SAAS,IAAI,CAAC,IAAI;YAClB,MAAM,OAAO,KAAK,CAAC,cAAc,cACb,WAAW,eAAe;YAC9C,IAAI,YAAY,CAAC;QACrB,OAAO;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,cACX,WAAW,eAAe;QAClD;QAEA,OAAO;IACX;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/generators.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise,\n                          apiRejection,\n                          INTERNAL,\n                          tryConvertToPromise,\n                          Proxyable,\n                          debug) {\nvar errors = require(\"./errors\");\nvar TypeError = errors.TypeError;\nvar util = require(\"./util\");\nvar errorObj = util.errorObj;\nvar tryCatch = util.tryCatch;\nvar yieldHandlers = [];\n\nfunction promiseFromYieldHandler(value, yieldHandlers, traceParent) {\n    for (var i = 0; i < yieldHandlers.length; ++i) {\n        traceParent._pushContext();\n        var result = tryCatch(yieldHandlers[i])(value);\n        traceParent._popContext();\n        if (result === errorObj) {\n            traceParent._pushContext();\n            var ret = Promise.reject(errorObj.e);\n            traceParent._popContext();\n            return ret;\n        }\n        var maybePromise = tryConvertToPromise(result, traceParent);\n        if (maybePromise instanceof Promise) return maybePromise;\n    }\n    return null;\n}\n\nfunction PromiseSpawn(generatorFunction, receiver, yieldHandler, stack) {\n    if (debug.cancellation()) {\n        var internal = new Promise(INTERNAL);\n        var _finallyPromise = this._finallyPromise = new Promise(INTERNAL);\n        this._promise = internal.lastly(function() {\n            return _finallyPromise;\n        });\n        internal._captureStackTrace();\n        internal._setOnCancel(this);\n    } else {\n        var promise = this._promise = new Promise(INTERNAL);\n        promise._captureStackTrace();\n    }\n    this._stack = stack;\n    this._generatorFunction = generatorFunction;\n    this._receiver = receiver;\n    this._generator = undefined;\n    this._yieldHandlers = typeof yieldHandler === \"function\"\n        ? [yieldHandler].concat(yieldHandlers)\n        : yieldHandlers;\n    this._yieldedPromise = null;\n    this._cancellationPhase = false;\n}\nutil.inherits(PromiseSpawn, Proxyable);\n\nPromiseSpawn.prototype._isResolved = function() {\n    return this._promise === null;\n};\n\nPromiseSpawn.prototype._cleanup = function() {\n    this._promise = this._generator = null;\n    if (debug.cancellation() && this._finallyPromise !== null) {\n        this._finallyPromise._fulfill();\n        this._finallyPromise = null;\n    }\n};\n\nPromiseSpawn.prototype._promiseCancelled = function() {\n    if (this._isResolved()) return;\n    var implementsReturn = typeof this._generator[\"return\"] !== \"undefined\";\n\n    var result;\n    if (!implementsReturn) {\n        var reason = new Promise.CancellationError(\n            \"generator .return() sentinel\");\n        Promise.coroutine.returnSentinel = reason;\n        this._promise._attachExtraTrace(reason);\n        this._promise._pushContext();\n        result = tryCatch(this._generator[\"throw\"]).call(this._generator,\n                                                         reason);\n        this._promise._popContext();\n    } else {\n        this._promise._pushContext();\n        result = tryCatch(this._generator[\"return\"]).call(this._generator,\n                                                          undefined);\n        this._promise._popContext();\n    }\n    this._cancellationPhase = true;\n    this._yieldedPromise = null;\n    this._continue(result);\n};\n\nPromiseSpawn.prototype._promiseFulfilled = function(value) {\n    this._yieldedPromise = null;\n    this._promise._pushContext();\n    var result = tryCatch(this._generator.next).call(this._generator, value);\n    this._promise._popContext();\n    this._continue(result);\n};\n\nPromiseSpawn.prototype._promiseRejected = function(reason) {\n    this._yieldedPromise = null;\n    this._promise._attachExtraTrace(reason);\n    this._promise._pushContext();\n    var result = tryCatch(this._generator[\"throw\"])\n        .call(this._generator, reason);\n    this._promise._popContext();\n    this._continue(result);\n};\n\nPromiseSpawn.prototype._resultCancelled = function() {\n    if (this._yieldedPromise instanceof Promise) {\n        var promise = this._yieldedPromise;\n        this._yieldedPromise = null;\n        promise.cancel();\n    }\n};\n\nPromiseSpawn.prototype.promise = function () {\n    return this._promise;\n};\n\nPromiseSpawn.prototype._run = function () {\n    this._generator = this._generatorFunction.call(this._receiver);\n    this._receiver =\n        this._generatorFunction = undefined;\n    this._promiseFulfilled(undefined);\n};\n\nPromiseSpawn.prototype._continue = function (result) {\n    var promise = this._promise;\n    if (result === errorObj) {\n        this._cleanup();\n        if (this._cancellationPhase) {\n            return promise.cancel();\n        } else {\n            return promise._rejectCallback(result.e, false);\n        }\n    }\n\n    var value = result.value;\n    if (result.done === true) {\n        this._cleanup();\n        if (this._cancellationPhase) {\n            return promise.cancel();\n        } else {\n            return promise._resolveCallback(value);\n        }\n    } else {\n        var maybePromise = tryConvertToPromise(value, this._promise);\n        if (!(maybePromise instanceof Promise)) {\n            maybePromise =\n                promiseFromYieldHandler(maybePromise,\n                                        this._yieldHandlers,\n                                        this._promise);\n            if (maybePromise === null) {\n                this._promiseRejected(\n                    new TypeError(\n                        \"A value %s was yielded that could not be treated as a promise\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\\u000a\".replace(\"%s\", value) +\n                        \"From coroutine:\\u000a\" +\n                        this._stack.split(\"\\n\").slice(1, -7).join(\"\\n\")\n                    )\n                );\n                return;\n            }\n        }\n        maybePromise = maybePromise._target();\n        var bitField = maybePromise._bitField;\n        ;\n        if (((bitField & 50397184) === 0)) {\n            this._yieldedPromise = maybePromise;\n            maybePromise._proxy(this, null);\n        } else if (((bitField & 33554432) !== 0)) {\n            Promise._async.invoke(\n                this._promiseFulfilled, this, maybePromise._value()\n            );\n        } else if (((bitField & 16777216) !== 0)) {\n            Promise._async.invoke(\n                this._promiseRejected, this, maybePromise._reason()\n            );\n        } else {\n            this._promiseCancelled();\n        }\n    }\n};\n\nPromise.coroutine = function (generatorFunction, options) {\n    if (typeof generatorFunction !== \"function\") {\n        throw new TypeError(\"generatorFunction must be a function\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    var yieldHandler = Object(options).yieldHandler;\n    var PromiseSpawn$ = PromiseSpawn;\n    var stack = new Error().stack;\n    return function () {\n        var generator = generatorFunction.apply(this, arguments);\n        var spawn = new PromiseSpawn$(undefined, undefined, yieldHandler,\n                                      stack);\n        var ret = spawn.promise();\n        spawn._generator = generator;\n        spawn._promiseFulfilled(undefined);\n        return ret;\n    };\n};\n\nPromise.coroutine.addYieldHandler = function(fn) {\n    if (typeof fn !== \"function\") {\n        throw new TypeError(\"expecting a function but got \" + util.classString(fn));\n    }\n    yieldHandlers.push(fn);\n};\n\nPromise.spawn = function (generatorFunction) {\n    debug.deprecated(\"Promise.spawn()\", \"Promise.coroutine()\");\n    if (typeof generatorFunction !== \"function\") {\n        return apiRejection(\"generatorFunction must be a function\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    var spawn = new PromiseSpawn(generatorFunction, this);\n    var ret = spawn.promise();\n    spawn._run(Promise.spawn);\n    return ret;\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,mBAAmB,EACnB,SAAS,EACT,KAAK;IAC/B,IAAI;IACJ,IAAI,YAAY,OAAO,SAAS;IAChC,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,gBAAgB,EAAE;IAEtB,SAAS,wBAAwB,KAAK,EAAE,aAAa,EAAE,WAAW;QAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,EAAE,EAAG;YAC3C,YAAY,YAAY;YACxB,IAAI,SAAS,SAAS,aAAa,CAAC,EAAE,EAAE;YACxC,YAAY,WAAW;YACvB,IAAI,WAAW,UAAU;gBACrB,YAAY,YAAY;gBACxB,IAAI,MAAM,QAAQ,MAAM,CAAC,SAAS,CAAC;gBACnC,YAAY,WAAW;gBACvB,OAAO;YACX;YACA,IAAI,eAAe,oBAAoB,QAAQ;YAC/C,IAAI,wBAAwB,SAAS,OAAO;QAChD;QACA,OAAO;IACX;IAEA,SAAS,aAAa,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK;QAClE,IAAI,MAAM,YAAY,IAAI;YACtB,IAAI,WAAW,IAAI,QAAQ;YAC3B,IAAI,kBAAkB,IAAI,CAAC,eAAe,GAAG,IAAI,QAAQ;YACzD,IAAI,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC;gBAC5B,OAAO;YACX;YACA,SAAS,kBAAkB;YAC3B,SAAS,YAAY,CAAC,IAAI;QAC9B,OAAO;YACH,IAAI,UAAU,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;YAC1C,QAAQ,kBAAkB;QAC9B;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG,OAAO,iBAAiB,aACxC;YAAC;SAAa,CAAC,MAAM,CAAC,iBACtB;QACN,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,KAAK,QAAQ,CAAC,cAAc;IAE5B,aAAa,SAAS,CAAC,WAAW,GAAG;QACjC,OAAO,IAAI,CAAC,QAAQ,KAAK;IAC7B;IAEA,aAAa,SAAS,CAAC,QAAQ,GAAG;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG;QAClC,IAAI,MAAM,YAAY,MAAM,IAAI,CAAC,eAAe,KAAK,MAAM;YACvD,IAAI,CAAC,eAAe,CAAC,QAAQ;YAC7B,IAAI,CAAC,eAAe,GAAG;QAC3B;IACJ;IAEA,aAAa,SAAS,CAAC,iBAAiB,GAAG;QACvC,IAAI,IAAI,CAAC,WAAW,IAAI;QACxB,IAAI,mBAAmB,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK;QAE5D,IAAI;QACJ,IAAI,CAAC,kBAAkB;YACnB,IAAI,SAAS,IAAI,QAAQ,iBAAiB,CACtC;YACJ,QAAQ,SAAS,CAAC,cAAc,GAAG;YACnC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EACf;YACjD,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC7B,OAAO;YACH,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EACf;YAClD,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC7B;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,SAAS,CAAC;IACnB;IAEA,aAAa,SAAS,CAAC,iBAAiB,GAAG,SAAS,KAAK;QACrD,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,QAAQ,CAAC,YAAY;QAC1B,IAAI,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAClE,IAAI,CAAC,QAAQ,CAAC,WAAW;QACzB,IAAI,CAAC,SAAS,CAAC;IACnB;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG,SAAS,MAAM;QACrD,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,YAAY;QAC1B,IAAI,SAAS,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,EACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAC3B,IAAI,CAAC,QAAQ,CAAC,WAAW;QACzB,IAAI,CAAC,SAAS,CAAC;IACnB;IAEA,aAAa,SAAS,CAAC,gBAAgB,GAAG;QACtC,IAAI,IAAI,CAAC,eAAe,YAAY,SAAS;YACzC,IAAI,UAAU,IAAI,CAAC,eAAe;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,MAAM;QAClB;IACJ;IAEA,aAAa,SAAS,CAAC,OAAO,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ;IACxB;IAEA,aAAa,SAAS,CAAC,IAAI,GAAG;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;QAC7D,IAAI,CAAC,SAAS,GACV,IAAI,CAAC,kBAAkB,GAAG;QAC9B,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IAEA,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QAC/C,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,WAAW,UAAU;YACrB,IAAI,CAAC,QAAQ;YACb,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,OAAO,QAAQ,MAAM;YACzB,OAAO;gBACH,OAAO,QAAQ,eAAe,CAAC,OAAO,CAAC,EAAE;YAC7C;QACJ;QAEA,IAAI,QAAQ,OAAO,KAAK;QACxB,IAAI,OAAO,IAAI,KAAK,MAAM;YACtB,IAAI,CAAC,QAAQ;YACb,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,OAAO,QAAQ,MAAM;YACzB,OAAO;gBACH,OAAO,QAAQ,gBAAgB,CAAC;YACpC;QACJ,OAAO;YACH,IAAI,eAAe,oBAAoB,OAAO,IAAI,CAAC,QAAQ;YAC3D,IAAI,CAAC,CAAC,wBAAwB,OAAO,GAAG;gBACpC,eACI,wBAAwB,cACA,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,QAAQ;gBACzC,IAAI,iBAAiB,MAAM;oBACvB,IAAI,CAAC,gBAAgB,CACjB,IAAI,UACA,oHAAoH,OAAO,CAAC,MAAM,SAClI,0BACA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;oBAGlD;gBACJ;YACJ;YACA,eAAe,aAAa,OAAO;YACnC,IAAI,WAAW,aAAa,SAAS;;YAErC,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBAC/B,IAAI,CAAC,eAAe,GAAG;gBACvB,aAAa,MAAM,CAAC,IAAI,EAAE;YAC9B,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBACtC,QAAQ,MAAM,CAAC,MAAM,CACjB,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,aAAa,MAAM;YAEzD,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBACtC,QAAQ,MAAM,CAAC,MAAM,CACjB,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa,OAAO;YAEzD,OAAO;gBACH,IAAI,CAAC,iBAAiB;YAC1B;QACJ;IACJ;IAEA,QAAQ,SAAS,GAAG,SAAU,iBAAiB,EAAE,OAAO;QACpD,IAAI,OAAO,sBAAsB,YAAY;YACzC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,eAAe,OAAO,SAAS,YAAY;QAC/C,IAAI,gBAAgB;QACpB,IAAI,QAAQ,IAAI,QAAQ,KAAK;QAC7B,OAAO;YACH,IAAI,YAAY,kBAAkB,KAAK,CAAC,IAAI,EAAE;YAC9C,IAAI,QAAQ,IAAI,cAAc,WAAW,WAAW,cACtB;YAC9B,IAAI,MAAM,MAAM,OAAO;YACvB,MAAM,UAAU,GAAG;YACnB,MAAM,iBAAiB,CAAC;YACxB,OAAO;QACX;IACJ;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG,SAAS,EAAE;QAC3C,IAAI,OAAO,OAAO,YAAY;YAC1B,MAAM,IAAI,UAAU,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,cAAc,IAAI,CAAC;IACvB;IAEA,QAAQ,KAAK,GAAG,SAAU,iBAAiB;QACvC,MAAM,UAAU,CAAC,mBAAmB;QACpC,IAAI,OAAO,sBAAsB,YAAY;YACzC,OAAO,aAAa;QACxB;QACA,IAAI,QAAQ,IAAI,aAAa,mBAAmB,IAAI;QACpD,IAAI,MAAM,MAAM,OAAO;QACvB,MAAM,IAAI,CAAC,QAAQ,KAAK;QACxB,OAAO;IACX;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/nodeify.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise) {\nvar util = require(\"./util\");\nvar async = Promise._async;\nvar tryCatch = util.tryCatch;\nvar errorObj = util.errorObj;\n\nfunction spreadAdapter(val, nodeback) {\n    var promise = this;\n    if (!util.isArray(val)) return successAdapter.call(promise, val, nodeback);\n    var ret =\n        tryCatch(nodeback).apply(promise._boundValue(), [null].concat(val));\n    if (ret === errorObj) {\n        async.throwLater(ret.e);\n    }\n}\n\nfunction successAdapter(val, nodeback) {\n    var promise = this;\n    var receiver = promise._boundValue();\n    var ret = val === undefined\n        ? tryCatch(nodeback).call(receiver, null)\n        : tryCatch(nodeback).call(receiver, null, val);\n    if (ret === errorObj) {\n        async.throwLater(ret.e);\n    }\n}\nfunction errorAdapter(reason, nodeback) {\n    var promise = this;\n    if (!reason) {\n        var newReason = new Error(reason + \"\");\n        newReason.cause = reason;\n        reason = newReason;\n    }\n    var ret = tryCatch(nodeback).call(promise._boundValue(), reason);\n    if (ret === errorObj) {\n        async.throwLater(ret.e);\n    }\n}\n\nPromise.prototype.asCallback = Promise.prototype.nodeify = function (nodeback,\n                                                                     options) {\n    if (typeof nodeback == \"function\") {\n        var adapter = successAdapter;\n        if (options !== undefined && Object(options).spread) {\n            adapter = spreadAdapter;\n        }\n        this._then(\n            adapter,\n            errorAdapter,\n            undefined,\n            this,\n            nodeback\n        );\n    }\n    return this;\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,IAAI;IACJ,IAAI,QAAQ,QAAQ,MAAM;IAC1B,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAE5B,SAAS,cAAc,GAAG,EAAE,QAAQ;QAChC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,eAAe,IAAI,CAAC,SAAS,KAAK;QACjE,IAAI,MACA,SAAS,UAAU,KAAK,CAAC,QAAQ,WAAW,IAAI;YAAC;SAAK,CAAC,MAAM,CAAC;QAClE,IAAI,QAAQ,UAAU;YAClB,MAAM,UAAU,CAAC,IAAI,CAAC;QAC1B;IACJ;IAEA,SAAS,eAAe,GAAG,EAAE,QAAQ;QACjC,IAAI,UAAU,IAAI;QAClB,IAAI,WAAW,QAAQ,WAAW;QAClC,IAAI,MAAM,QAAQ,YACZ,SAAS,UAAU,IAAI,CAAC,UAAU,QAClC,SAAS,UAAU,IAAI,CAAC,UAAU,MAAM;QAC9C,IAAI,QAAQ,UAAU;YAClB,MAAM,UAAU,CAAC,IAAI,CAAC;QAC1B;IACJ;IACA,SAAS,aAAa,MAAM,EAAE,QAAQ;QAClC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,QAAQ;YACT,IAAI,YAAY,IAAI,MAAM,SAAS;YACnC,UAAU,KAAK,GAAG;YAClB,SAAS;QACb;QACA,IAAI,MAAM,SAAS,UAAU,IAAI,CAAC,QAAQ,WAAW,IAAI;QACzD,IAAI,QAAQ,UAAU;YAClB,MAAM,UAAU,CAAC,IAAI,CAAC;QAC1B;IACJ;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAU,QAAQ,EACR,OAAO;QACxE,IAAI,OAAO,YAAY,YAAY;YAC/B,IAAI,UAAU;YACd,IAAI,YAAY,aAAa,OAAO,SAAS,MAAM,EAAE;gBACjD,UAAU;YACd;YACA,IAAI,CAAC,KAAK,CACN,SACA,cACA,WACA,IAAI,EACJ;QAER;QACA,OAAO,IAAI;IACf;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/promisify.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL) {\nvar THIS = {};\nvar util = require(\"./util\");\nvar nodebackForPromise = require(\"./nodeback\");\nvar withAppended = util.withAppended;\nvar maybeWrapAsError = util.maybeWrapAsError;\nvar canEvaluate = util.canEvaluate;\nvar TypeError = require(\"./errors\").TypeError;\nvar defaultSuffix = \"Async\";\nvar defaultPromisified = {__isPromisified__: true};\nvar noCopyProps = [\n    \"arity\",    \"length\",\n    \"name\",\n    \"arguments\",\n    \"caller\",\n    \"callee\",\n    \"prototype\",\n    \"__isPromisified__\"\n];\nvar noCopyPropsPattern = new RegExp(\"^(?:\" + noCopyProps.join(\"|\") + \")$\");\n\nvar defaultFilter = function(name) {\n    return util.isIdentifier(name) &&\n        name.charAt(0) !== \"_\" &&\n        name !== \"constructor\";\n};\n\nfunction propsFilter(key) {\n    return !noCopyPropsPattern.test(key);\n}\n\nfunction isPromisified(fn) {\n    try {\n        return fn.__isPromisified__ === true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\nfunction hasPromisified(obj, key, suffix) {\n    var val = util.getDataPropertyOrDefault(obj, key + suffix,\n                                            defaultPromisified);\n    return val ? isPromisified(val) : false;\n}\nfunction checkValid(ret, suffix, suffixRegexp) {\n    for (var i = 0; i < ret.length; i += 2) {\n        var key = ret[i];\n        if (suffixRegexp.test(key)) {\n            var keyWithoutAsyncSuffix = key.replace(suffixRegexp, \"\");\n            for (var j = 0; j < ret.length; j += 2) {\n                if (ret[j] === keyWithoutAsyncSuffix) {\n                    throw new TypeError(\"Cannot promisify an API that has normal methods with '%s'-suffix\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\"\n                        .replace(\"%s\", suffix));\n                }\n            }\n        }\n    }\n}\n\nfunction promisifiableMethods(obj, suffix, suffixRegexp, filter) {\n    var keys = util.inheritedDataKeys(obj);\n    var ret = [];\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var value = obj[key];\n        var passesDefaultFilter = filter === defaultFilter\n            ? true : defaultFilter(key, value, obj);\n        if (typeof value === \"function\" &&\n            !isPromisified(value) &&\n            !hasPromisified(obj, key, suffix) &&\n            filter(key, value, obj, passesDefaultFilter)) {\n            ret.push(key, value);\n        }\n    }\n    checkValid(ret, suffix, suffixRegexp);\n    return ret;\n}\n\nvar escapeIdentRegex = function(str) {\n    return str.replace(/([$])/, \"\\\\$\");\n};\n\nvar makeNodePromisifiedEval;\nif (!false) {\nvar switchCaseArgumentOrder = function(likelyArgumentCount) {\n    var ret = [likelyArgumentCount];\n    var min = Math.max(0, likelyArgumentCount - 1 - 3);\n    for(var i = likelyArgumentCount - 1; i >= min; --i) {\n        ret.push(i);\n    }\n    for(var i = likelyArgumentCount + 1; i <= 3; ++i) {\n        ret.push(i);\n    }\n    return ret;\n};\n\nvar argumentSequence = function(argumentCount) {\n    return util.filledRange(argumentCount, \"_arg\", \"\");\n};\n\nvar parameterDeclaration = function(parameterCount) {\n    return util.filledRange(\n        Math.max(parameterCount, 3), \"_arg\", \"\");\n};\n\nvar parameterCount = function(fn) {\n    if (typeof fn.length === \"number\") {\n        return Math.max(Math.min(fn.length, 1023 + 1), 0);\n    }\n    return 0;\n};\n\nmakeNodePromisifiedEval =\nfunction(callback, receiver, originalName, fn, _, multiArgs) {\n    var newParameterCount = Math.max(0, parameterCount(fn) - 1);\n    var argumentOrder = switchCaseArgumentOrder(newParameterCount);\n    var shouldProxyThis = typeof callback === \"string\" || receiver === THIS;\n\n    function generateCallForArgumentCount(count) {\n        var args = argumentSequence(count).join(\", \");\n        var comma = count > 0 ? \", \" : \"\";\n        var ret;\n        if (shouldProxyThis) {\n            ret = \"ret = callback.call(this, {{args}}, nodeback); break;\\n\";\n        } else {\n            ret = receiver === undefined\n                ? \"ret = callback({{args}}, nodeback); break;\\n\"\n                : \"ret = callback.call(receiver, {{args}}, nodeback); break;\\n\";\n        }\n        return ret.replace(\"{{args}}\", args).replace(\", \", comma);\n    }\n\n    function generateArgumentSwitchCase() {\n        var ret = \"\";\n        for (var i = 0; i < argumentOrder.length; ++i) {\n            ret += \"case \" + argumentOrder[i] +\":\" +\n                generateCallForArgumentCount(argumentOrder[i]);\n        }\n\n        ret += \"                                                             \\n\\\n        default:                                                             \\n\\\n            var args = new Array(len + 1);                                   \\n\\\n            var i = 0;                                                       \\n\\\n            for (var i = 0; i < len; ++i) {                                  \\n\\\n               args[i] = arguments[i];                                       \\n\\\n            }                                                                \\n\\\n            args[i] = nodeback;                                              \\n\\\n            [CodeForCall]                                                    \\n\\\n            break;                                                           \\n\\\n        \".replace(\"[CodeForCall]\", (shouldProxyThis\n                                ? \"ret = callback.apply(this, args);\\n\"\n                                : \"ret = callback.apply(receiver, args);\\n\"));\n        return ret;\n    }\n\n    var getFunctionCode = typeof callback === \"string\"\n                                ? (\"this != null ? this['\"+callback+\"'] : fn\")\n                                : \"fn\";\n    var body = \"'use strict';                                                \\n\\\n        var ret = function (Parameters) {                                    \\n\\\n            'use strict';                                                    \\n\\\n            var len = arguments.length;                                      \\n\\\n            var promise = new Promise(INTERNAL);                             \\n\\\n            promise._captureStackTrace();                                    \\n\\\n            var nodeback = nodebackForPromise(promise, \" + multiArgs + \");   \\n\\\n            var ret;                                                         \\n\\\n            var callback = tryCatch([GetFunctionCode]);                      \\n\\\n            switch(len) {                                                    \\n\\\n                [CodeForSwitchCase]                                          \\n\\\n            }                                                                \\n\\\n            if (ret === errorObj) {                                          \\n\\\n                promise._rejectCallback(maybeWrapAsError(ret.e), true, true);\\n\\\n            }                                                                \\n\\\n            if (!promise._isFateSealed()) promise._setAsyncGuaranteed();     \\n\\\n            return promise;                                                  \\n\\\n        };                                                                   \\n\\\n        notEnumerableProp(ret, '__isPromisified__', true);                   \\n\\\n        return ret;                                                          \\n\\\n    \".replace(\"[CodeForSwitchCase]\", generateArgumentSwitchCase())\n        .replace(\"[GetFunctionCode]\", getFunctionCode);\n    body = body.replace(\"Parameters\", parameterDeclaration(newParameterCount));\n    return new Function(\"Promise\",\n                        \"fn\",\n                        \"receiver\",\n                        \"withAppended\",\n                        \"maybeWrapAsError\",\n                        \"nodebackForPromise\",\n                        \"tryCatch\",\n                        \"errorObj\",\n                        \"notEnumerableProp\",\n                        \"INTERNAL\",\n                        body)(\n                    Promise,\n                    fn,\n                    receiver,\n                    withAppended,\n                    maybeWrapAsError,\n                    nodebackForPromise,\n                    util.tryCatch,\n                    util.errorObj,\n                    util.notEnumerableProp,\n                    INTERNAL);\n};\n}\n\nfunction makeNodePromisifiedClosure(callback, receiver, _, fn, __, multiArgs) {\n    var defaultThis = (function() {return this;})();\n    var method = callback;\n    if (typeof method === \"string\") {\n        callback = fn;\n    }\n    function promisified() {\n        var _receiver = receiver;\n        if (receiver === THIS) _receiver = this;\n        var promise = new Promise(INTERNAL);\n        promise._captureStackTrace();\n        var cb = typeof method === \"string\" && this !== defaultThis\n            ? this[method] : callback;\n        var fn = nodebackForPromise(promise, multiArgs);\n        try {\n            cb.apply(_receiver, withAppended(arguments, fn));\n        } catch(e) {\n            promise._rejectCallback(maybeWrapAsError(e), true, true);\n        }\n        if (!promise._isFateSealed()) promise._setAsyncGuaranteed();\n        return promise;\n    }\n    util.notEnumerableProp(promisified, \"__isPromisified__\", true);\n    return promisified;\n}\n\nvar makeNodePromisified = canEvaluate\n    ? makeNodePromisifiedEval\n    : makeNodePromisifiedClosure;\n\nfunction promisifyAll(obj, suffix, filter, promisifier, multiArgs) {\n    var suffixRegexp = new RegExp(escapeIdentRegex(suffix) + \"$\");\n    var methods =\n        promisifiableMethods(obj, suffix, suffixRegexp, filter);\n\n    for (var i = 0, len = methods.length; i < len; i+= 2) {\n        var key = methods[i];\n        var fn = methods[i+1];\n        var promisifiedKey = key + suffix;\n        if (promisifier === makeNodePromisified) {\n            obj[promisifiedKey] =\n                makeNodePromisified(key, THIS, key, fn, suffix, multiArgs);\n        } else {\n            var promisified = promisifier(fn, function() {\n                return makeNodePromisified(key, THIS, key,\n                                           fn, suffix, multiArgs);\n            });\n            util.notEnumerableProp(promisified, \"__isPromisified__\", true);\n            obj[promisifiedKey] = promisified;\n        }\n    }\n    util.toFastProperties(obj);\n    return obj;\n}\n\nfunction promisify(callback, receiver, multiArgs) {\n    return makeNodePromisified(callback, receiver, undefined,\n                                callback, null, multiArgs);\n}\n\nPromise.promisify = function (fn, options) {\n    if (typeof fn !== \"function\") {\n        throw new TypeError(\"expecting a function but got \" + util.classString(fn));\n    }\n    if (isPromisified(fn)) {\n        return fn;\n    }\n    options = Object(options);\n    var receiver = options.context === undefined ? THIS : options.context;\n    var multiArgs = !!options.multiArgs;\n    var ret = promisify(fn, receiver, multiArgs);\n    util.copyDescriptors(fn, ret, propsFilter);\n    return ret;\n};\n\nPromise.promisifyAll = function (target, options) {\n    if (typeof target !== \"function\" && typeof target !== \"object\") {\n        throw new TypeError(\"the target of promisifyAll must be an object or a function\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    options = Object(options);\n    var multiArgs = !!options.multiArgs;\n    var suffix = options.suffix;\n    if (typeof suffix !== \"string\") suffix = defaultSuffix;\n    var filter = options.filter;\n    if (typeof filter !== \"function\") filter = defaultFilter;\n    var promisifier = options.promisifier;\n    if (typeof promisifier !== \"function\") promisifier = makeNodePromisified;\n\n    if (!util.isIdentifier(suffix)) {\n        throw new RangeError(\"suffix must be a valid identifier\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n\n    var keys = util.inheritedDataKeys(target);\n    for (var i = 0; i < keys.length; ++i) {\n        var value = target[keys[i]];\n        if (keys[i] !== \"constructor\" &&\n            util.isClass(value)) {\n            promisifyAll(value.prototype, suffix, filter, promisifier,\n                multiArgs);\n            promisifyAll(value, suffix, filter, promisifier, multiArgs);\n        }\n    }\n\n    return promisifyAll(target, suffix, filter, promisifier, multiArgs);\n};\n};\n\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ;IAC3C,IAAI,OAAO,CAAC;IACZ,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe,KAAK,YAAY;IACpC,IAAI,mBAAmB,KAAK,gBAAgB;IAC5C,IAAI,cAAc,KAAK,WAAW;IAClC,IAAI,YAAY,0GAAoB,SAAS;IAC7C,IAAI,gBAAgB;IACpB,IAAI,qBAAqB;QAAC,mBAAmB;IAAI;IACjD,IAAI,cAAc;QACd;QAAY;QACZ;QACA;QACA;QACA;QACA;QACA;KACH;IACD,IAAI,qBAAqB,IAAI,OAAO,SAAS,YAAY,IAAI,CAAC,OAAO;IAErE,IAAI,gBAAgB,SAAS,IAAI;QAC7B,OAAO,KAAK,YAAY,CAAC,SACrB,KAAK,MAAM,CAAC,OAAO,OACnB,SAAS;IACjB;IAEA,SAAS,YAAY,GAAG;QACpB,OAAO,CAAC,mBAAmB,IAAI,CAAC;IACpC;IAEA,SAAS,cAAc,EAAE;QACrB,IAAI;YACA,OAAO,GAAG,iBAAiB,KAAK;QACpC,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IAEA,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,MAAM;QACpC,IAAI,MAAM,KAAK,wBAAwB,CAAC,KAAK,MAAM,QACX;QACxC,OAAO,MAAM,cAAc,OAAO;IACtC;IACA,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,YAAY;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;YACpC,IAAI,MAAM,GAAG,CAAC,EAAE;YAChB,IAAI,aAAa,IAAI,CAAC,MAAM;gBACxB,IAAI,wBAAwB,IAAI,OAAO,CAAC,cAAc;gBACtD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;oBACpC,IAAI,GAAG,CAAC,EAAE,KAAK,uBAAuB;wBAClC,MAAM,IAAI,UAAU,iHACf,OAAO,CAAC,MAAM;oBACvB;gBACJ;YACJ;QACJ;IACJ;IAEA,SAAS,qBAAqB,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM;QAC3D,IAAI,OAAO,KAAK,iBAAiB,CAAC;QAClC,IAAI,MAAM,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;YAClC,IAAI,MAAM,IAAI,CAAC,EAAE;YACjB,IAAI,QAAQ,GAAG,CAAC,IAAI;YACpB,IAAI,sBAAsB,WAAW,gBAC/B,OAAO,cAAc,KAAK,OAAO;YACvC,IAAI,OAAO,UAAU,cACjB,CAAC,cAAc,UACf,CAAC,eAAe,KAAK,KAAK,WAC1B,OAAO,KAAK,OAAO,KAAK,sBAAsB;gBAC9C,IAAI,IAAI,CAAC,KAAK;YAClB;QACJ;QACA,WAAW,KAAK,QAAQ;QACxB,OAAO;IACX;IAEA,IAAI,mBAAmB,SAAS,GAAG;QAC/B,OAAO,IAAI,OAAO,CAAC,SAAS;IAChC;IAEA,IAAI;IACJ,wCAAY;QACZ,IAAI,0BAA0B,SAAS,mBAAmB;YACtD,IAAI,MAAM;gBAAC;aAAoB;YAC/B,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,sBAAsB,IAAI;YAChD,IAAI,IAAI,IAAI,sBAAsB,GAAG,KAAK,KAAK,EAAE,EAAG;gBAChD,IAAI,IAAI,CAAC;YACb;YACA,IAAI,IAAI,IAAI,sBAAsB,GAAG,KAAK,GAAG,EAAE,EAAG;gBAC9C,IAAI,IAAI,CAAC;YACb;YACA,OAAO;QACX;QAEA,IAAI,mBAAmB,SAAS,aAAa;YACzC,OAAO,KAAK,WAAW,CAAC,eAAe,QAAQ;QACnD;QAEA,IAAI,uBAAuB,SAAS,cAAc;YAC9C,OAAO,KAAK,WAAW,CACnB,KAAK,GAAG,CAAC,gBAAgB,IAAI,QAAQ;QAC7C;QAEA,IAAI,iBAAiB,SAAS,EAAE;YAC5B,IAAI,OAAO,GAAG,MAAM,KAAK,UAAU;gBAC/B,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,OAAO,IAAI;YACnD;YACA,OAAO;QACX;QAEA,0BACA,SAAS,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,EAAE,SAAS;YACvD,IAAI,oBAAoB,KAAK,GAAG,CAAC,GAAG,eAAe,MAAM;YACzD,IAAI,gBAAgB,wBAAwB;YAC5C,IAAI,kBAAkB,OAAO,aAAa,YAAY,aAAa;YAEnE,SAAS,6BAA6B,KAAK;gBACvC,IAAI,OAAO,iBAAiB,OAAO,IAAI,CAAC;gBACxC,IAAI,QAAQ,QAAQ,IAAI,OAAO;gBAC/B,IAAI;gBACJ,IAAI,iBAAiB;oBACjB,MAAM;gBACV,OAAO;oBACH,MAAM,aAAa,YACb,iDACA;gBACV;gBACA,OAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,CAAC,MAAM;YACvD;YAEA,SAAS;gBACL,IAAI,MAAM;gBACV,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,EAAE,EAAG;oBAC3C,OAAO,UAAU,aAAa,CAAC,EAAE,GAAE,MAC/B,6BAA6B,aAAa,CAAC,EAAE;gBACrD;gBAEA,OAAO;;;;;;;;;;UAUL,OAAO,CAAC,iBAAkB,kBACF,wCACA;gBAC1B,OAAO;YACX;YAEA,IAAI,kBAAkB,OAAO,aAAa,WACX,0BAAwB,WAAS,YAClC;YAC9B,IAAI,OAAO;;;;;;2DAM4C,YAAY;;;;;;;;;;;;;;MAcjE,OAAO,CAAC,uBAAuB,8BAC5B,OAAO,CAAC,qBAAqB;YAClC,OAAO,KAAK,OAAO,CAAC,cAAc,qBAAqB;YACvD,OAAO,IAAI,SAAS,WACA,MACA,YACA,gBACA,oBACA,sBACA,YACA,YACA,qBACA,YACA,MACJ,SACA,IACA,UACA,cACA,kBACA,oBACA,KAAK,QAAQ,EACb,KAAK,QAAQ,EACb,KAAK,iBAAiB,EACtB;QACpB;IACA;IAEA,SAAS,2BAA2B,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS;QACxE,IAAI,cAAc,AAAC;YAAY,OAAO,IAAI;QAAC;QAC3C,IAAI,SAAS;QACb,IAAI,OAAO,WAAW,UAAU;YAC5B,WAAW;QACf;QACA,SAAS;YACL,IAAI,YAAY;YAChB,IAAI,aAAa,MAAM,YAAY,IAAI;YACvC,IAAI,UAAU,IAAI,QAAQ;YAC1B,QAAQ,kBAAkB;YAC1B,IAAI,KAAK,OAAO,WAAW,YAAY,IAAI,KAAK,cAC1C,IAAI,CAAC,OAAO,GAAG;YACrB,IAAI,KAAK,mBAAmB,SAAS;YACrC,IAAI;gBACA,GAAG,KAAK,CAAC,WAAW,aAAa,WAAW;YAChD,EAAE,OAAM,GAAG;gBACP,QAAQ,eAAe,CAAC,iBAAiB,IAAI,MAAM;YACvD;YACA,IAAI,CAAC,QAAQ,aAAa,IAAI,QAAQ,mBAAmB;YACzD,OAAO;QACX;QACA,KAAK,iBAAiB,CAAC,aAAa,qBAAqB;QACzD,OAAO;IACX;IAEA,IAAI,sBAAsB,cACpB,0BACA;IAEN,SAAS,aAAa,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS;QAC7D,IAAI,eAAe,IAAI,OAAO,iBAAiB,UAAU;QACzD,IAAI,UACA,qBAAqB,KAAK,QAAQ,cAAc;QAEpD,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,MAAM,EAAE,IAAI,KAAK,KAAI,EAAG;YAClD,IAAI,MAAM,OAAO,CAAC,EAAE;YACpB,IAAI,KAAK,OAAO,CAAC,IAAE,EAAE;YACrB,IAAI,iBAAiB,MAAM;YAC3B,IAAI,gBAAgB,qBAAqB;gBACrC,GAAG,CAAC,eAAe,GACf,oBAAoB,KAAK,MAAM,KAAK,IAAI,QAAQ;YACxD,OAAO;gBACH,IAAI,cAAc,YAAY,IAAI;oBAC9B,OAAO,oBAAoB,KAAK,MAAM,KACX,IAAI,QAAQ;gBAC3C;gBACA,KAAK,iBAAiB,CAAC,aAAa,qBAAqB;gBACzD,GAAG,CAAC,eAAe,GAAG;YAC1B;QACJ;QACA,KAAK,gBAAgB,CAAC;QACtB,OAAO;IACX;IAEA,SAAS,UAAU,QAAQ,EAAE,QAAQ,EAAE,SAAS;QAC5C,OAAO,oBAAoB,UAAU,UAAU,WACnB,UAAU,MAAM;IAChD;IAEA,QAAQ,SAAS,GAAG,SAAU,EAAE,EAAE,OAAO;QACrC,IAAI,OAAO,OAAO,YAAY;YAC1B,MAAM,IAAI,UAAU,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,IAAI,cAAc,KAAK;YACnB,OAAO;QACX;QACA,UAAU,OAAO;QACjB,IAAI,WAAW,QAAQ,OAAO,KAAK,YAAY,OAAO,QAAQ,OAAO;QACrE,IAAI,YAAY,CAAC,CAAC,QAAQ,SAAS;QACnC,IAAI,MAAM,UAAU,IAAI,UAAU;QAClC,KAAK,eAAe,CAAC,IAAI,KAAK;QAC9B,OAAO;IACX;IAEA,QAAQ,YAAY,GAAG,SAAU,MAAM,EAAE,OAAO;QAC5C,IAAI,OAAO,WAAW,cAAc,OAAO,WAAW,UAAU;YAC5D,MAAM,IAAI,UAAU;QACxB;QACA,UAAU,OAAO;QACjB,IAAI,YAAY,CAAC,CAAC,QAAQ,SAAS;QACnC,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,OAAO,WAAW,UAAU,SAAS;QACzC,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,OAAO,WAAW,YAAY,SAAS;QAC3C,IAAI,cAAc,QAAQ,WAAW;QACrC,IAAI,OAAO,gBAAgB,YAAY,cAAc;QAErD,IAAI,CAAC,KAAK,YAAY,CAAC,SAAS;YAC5B,MAAM,IAAI,WAAW;QACzB;QAEA,IAAI,OAAO,KAAK,iBAAiB,CAAC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;YAClC,IAAI,QAAQ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,EAAE,KAAK,iBACZ,KAAK,OAAO,CAAC,QAAQ;gBACrB,aAAa,MAAM,SAAS,EAAE,QAAQ,QAAQ,aAC1C;gBACJ,aAAa,OAAO,QAAQ,QAAQ,aAAa;YACrD;QACJ;QAEA,OAAO,aAAa,QAAQ,QAAQ,QAAQ,aAAa;IAC7D;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3671, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/props.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(\n    Promise, PromiseArray, tryConvertToPromise, apiRejection) {\nvar util = require(\"./util\");\nvar isObject = util.isObject;\nvar es5 = require(\"./es5\");\nvar Es6Map;\nif (typeof Map === \"function\") Es6Map = Map;\n\nvar mapToEntries = (function() {\n    var index = 0;\n    var size = 0;\n\n    function extractEntry(value, key) {\n        this[index] = value;\n        this[index + size] = key;\n        index++;\n    }\n\n    return function mapToEntries(map) {\n        size = map.size;\n        index = 0;\n        var ret = new Array(map.size * 2);\n        map.forEach(extractEntry, ret);\n        return ret;\n    };\n})();\n\nvar entriesToMap = function(entries) {\n    var ret = new Es6Map();\n    var length = entries.length / 2 | 0;\n    for (var i = 0; i < length; ++i) {\n        var key = entries[length + i];\n        var value = entries[i];\n        ret.set(key, value);\n    }\n    return ret;\n};\n\nfunction PropertiesPromiseArray(obj) {\n    var isMap = false;\n    var entries;\n    if (Es6Map !== undefined && obj instanceof Es6Map) {\n        entries = mapToEntries(obj);\n        isMap = true;\n    } else {\n        var keys = es5.keys(obj);\n        var len = keys.length;\n        entries = new Array(len * 2);\n        for (var i = 0; i < len; ++i) {\n            var key = keys[i];\n            entries[i] = obj[key];\n            entries[i + len] = key;\n        }\n    }\n    this.constructor$(entries);\n    this._isMap = isMap;\n    this._init$(undefined, -3);\n}\nutil.inherits(PropertiesPromiseArray, PromiseArray);\n\nPropertiesPromiseArray.prototype._init = function () {};\n\nPropertiesPromiseArray.prototype._promiseFulfilled = function (value, index) {\n    this._values[index] = value;\n    var totalResolved = ++this._totalResolved;\n    if (totalResolved >= this._length) {\n        var val;\n        if (this._isMap) {\n            val = entriesToMap(this._values);\n        } else {\n            val = {};\n            var keyOffset = this.length();\n            for (var i = 0, len = this.length(); i < len; ++i) {\n                val[this._values[i + keyOffset]] = this._values[i];\n            }\n        }\n        this._resolve(val);\n        return true;\n    }\n    return false;\n};\n\nPropertiesPromiseArray.prototype.shouldCopyValues = function () {\n    return false;\n};\n\nPropertiesPromiseArray.prototype.getActualLength = function (len) {\n    return len >> 1;\n};\n\nfunction props(promises) {\n    var ret;\n    var castValue = tryConvertToPromise(promises);\n\n    if (!isObject(castValue)) {\n        return apiRejection(\"cannot await properties of a non-object\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    } else if (castValue instanceof Promise) {\n        ret = castValue._then(\n            Promise.props, undefined, undefined, undefined, undefined);\n    } else {\n        ret = new PropertiesPromiseArray(castValue).promise();\n    }\n\n    if (castValue instanceof Promise) {\n        ret._propagateFrom(castValue, 2);\n    }\n    return ret;\n}\n\nPromise.prototype.props = function () {\n    return props(this);\n};\n\nPromise.props = function (promises) {\n    return props(promises);\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SACb,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,YAAY;IAC5D,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,QAAQ,YAAY,SAAS;IAExC,IAAI,eAAe,AAAC;QAChB,IAAI,QAAQ;QACZ,IAAI,OAAO;QAEX,SAAS,aAAa,KAAK,EAAE,GAAG;YAC5B,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,QAAQ,KAAK,GAAG;YACrB;QACJ;QAEA,OAAO,SAAS,aAAa,GAAG;YAC5B,OAAO,IAAI,IAAI;YACf,QAAQ;YACR,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,GAAG;YAC/B,IAAI,OAAO,CAAC,cAAc;YAC1B,OAAO;QACX;IACJ;IAEA,IAAI,eAAe,SAAS,OAAO;QAC/B,IAAI,MAAM,IAAI;QACd,IAAI,SAAS,QAAQ,MAAM,GAAG,IAAI;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;YAC7B,IAAI,MAAM,OAAO,CAAC,SAAS,EAAE;YAC7B,IAAI,QAAQ,OAAO,CAAC,EAAE;YACtB,IAAI,GAAG,CAAC,KAAK;QACjB;QACA,OAAO;IACX;IAEA,SAAS,uBAAuB,GAAG;QAC/B,IAAI,QAAQ;QACZ,IAAI;QACJ,IAAI,WAAW,aAAa,eAAe,QAAQ;YAC/C,UAAU,aAAa;YACvB,QAAQ;QACZ,OAAO;YACH,IAAI,OAAO,IAAI,IAAI,CAAC;YACpB,IAAI,MAAM,KAAK,MAAM;YACrB,UAAU,IAAI,MAAM,MAAM;YAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;gBAC1B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI;gBACrB,OAAO,CAAC,IAAI,IAAI,GAAG;YACvB;QACJ;QACA,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IAC5B;IACA,KAAK,QAAQ,CAAC,wBAAwB;IAEtC,uBAAuB,SAAS,CAAC,KAAK,GAAG,YAAa;IAEtD,uBAAuB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,KAAK;QACvE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACtB,IAAI,gBAAgB,EAAE,IAAI,CAAC,cAAc;QACzC,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI;YACJ,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,MAAM,aAAa,IAAI,CAAC,OAAO;YACnC,OAAO;gBACH,MAAM,CAAC;gBACP,IAAI,YAAY,IAAI,CAAC,MAAM;gBAC3B,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,EAAE,EAAG;oBAC/C,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;gBACtD;YACJ;YACA,IAAI,CAAC,QAAQ,CAAC;YACd,OAAO;QACX;QACA,OAAO;IACX;IAEA,uBAAuB,SAAS,CAAC,gBAAgB,GAAG;QAChD,OAAO;IACX;IAEA,uBAAuB,SAAS,CAAC,eAAe,GAAG,SAAU,GAAG;QAC5D,OAAO,OAAO;IAClB;IAEA,SAAS,MAAM,QAAQ;QACnB,IAAI;QACJ,IAAI,YAAY,oBAAoB;QAEpC,IAAI,CAAC,SAAS,YAAY;YACtB,OAAO,aAAa;QACxB,OAAO,IAAI,qBAAqB,SAAS;YACrC,MAAM,UAAU,KAAK,CACjB,QAAQ,KAAK,EAAE,WAAW,WAAW,WAAW;QACxD,OAAO;YACH,MAAM,IAAI,uBAAuB,WAAW,OAAO;QACvD;QAEA,IAAI,qBAAqB,SAAS;YAC9B,IAAI,cAAc,CAAC,WAAW;QAClC;QACA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG;QACtB,OAAO,MAAM,IAAI;IACrB;IAEA,QAAQ,KAAK,GAAG,SAAU,QAAQ;QAC9B,OAAO,MAAM;IACjB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3778, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/race.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(\n    Promise, INTERNAL, tryConvertToPromise, apiRejection) {\nvar util = require(\"./util\");\n\nvar raceLater = function (promise) {\n    return promise.then(function(array) {\n        return race(array, promise);\n    });\n};\n\nfunction race(promises, parent) {\n    var maybePromise = tryConvertToPromise(promises);\n\n    if (maybePromise instanceof Promise) {\n        return raceLater(maybePromise);\n    } else {\n        promises = util.asArray(promises);\n        if (promises === null)\n            return apiRejection(\"expecting an array or an iterable object but got \" + util.classString(promises));\n    }\n\n    var ret = new Promise(INTERNAL);\n    if (parent !== undefined) {\n        ret._propagateFrom(parent, 3);\n    }\n    var fulfill = ret._fulfill;\n    var reject = ret._reject;\n    for (var i = 0, len = promises.length; i < len; ++i) {\n        var val = promises[i];\n\n        if (val === undefined && !(i in promises)) {\n            continue;\n        }\n\n        Promise.cast(val)._then(fulfill, reject, undefined, ret, null);\n    }\n    return ret;\n}\n\nPromise.race = function (promises) {\n    return race(promises, undefined);\n};\n\nPromise.prototype.race = function () {\n    return race(this, undefined);\n};\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SACb,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,YAAY;IACxD,IAAI;IAEJ,IAAI,YAAY,SAAU,OAAO;QAC7B,OAAO,QAAQ,IAAI,CAAC,SAAS,KAAK;YAC9B,OAAO,KAAK,OAAO;QACvB;IACJ;IAEA,SAAS,KAAK,QAAQ,EAAE,MAAM;QAC1B,IAAI,eAAe,oBAAoB;QAEvC,IAAI,wBAAwB,SAAS;YACjC,OAAO,UAAU;QACrB,OAAO;YACH,WAAW,KAAK,OAAO,CAAC;YACxB,IAAI,aAAa,MACb,OAAO,aAAa,sDAAsD,KAAK,WAAW,CAAC;QACnG;QAEA,IAAI,MAAM,IAAI,QAAQ;QACtB,IAAI,WAAW,WAAW;YACtB,IAAI,cAAc,CAAC,QAAQ;QAC/B;QACA,IAAI,UAAU,IAAI,QAAQ;QAC1B,IAAI,SAAS,IAAI,OAAO;QACxB,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YACjD,IAAI,MAAM,QAAQ,CAAC,EAAE;YAErB,IAAI,QAAQ,aAAa,CAAC,CAAC,KAAK,QAAQ,GAAG;gBACvC;YACJ;YAEA,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC,SAAS,QAAQ,WAAW,KAAK;QAC7D;QACA,OAAO;IACX;IAEA,QAAQ,IAAI,GAAG,SAAU,QAAQ;QAC7B,OAAO,KAAK,UAAU;IAC1B;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG;QACrB,OAAO,KAAK,IAAI,EAAE;IACtB;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/reduce.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise,\n                          PromiseArray,\n                          apiRejection,\n                          tryConvertToPromise,\n                          INTERNAL,\n                          debug) {\nvar getDomain = Promise._getDomain;\nvar util = require(\"./util\");\nvar tryCatch = util.tryCatch;\n\nfunction ReductionPromiseArray(promises, fn, initialValue, _each) {\n    this.constructor$(promises);\n    var domain = getDomain();\n    this._fn = domain === null ? fn : util.domainBind(domain, fn);\n    if (initialValue !== undefined) {\n        initialValue = Promise.resolve(initialValue);\n        initialValue._attachCancellationCallback(this);\n    }\n    this._initialValue = initialValue;\n    this._currentCancellable = null;\n    if(_each === INTERNAL) {\n        this._eachValues = Array(this._length);\n    } else if (_each === 0) {\n        this._eachValues = null;\n    } else {\n        this._eachValues = undefined;\n    }\n    this._promise._captureStackTrace();\n    this._init$(undefined, -5);\n}\nutil.inherits(ReductionPromiseArray, PromiseArray);\n\nReductionPromiseArray.prototype._gotAccum = function(accum) {\n    if (this._eachValues !== undefined && \n        this._eachValues !== null && \n        accum !== INTERNAL) {\n        this._eachValues.push(accum);\n    }\n};\n\nReductionPromiseArray.prototype._eachComplete = function(value) {\n    if (this._eachValues !== null) {\n        this._eachValues.push(value);\n    }\n    return this._eachValues;\n};\n\nReductionPromiseArray.prototype._init = function() {};\n\nReductionPromiseArray.prototype._resolveEmptyArray = function() {\n    this._resolve(this._eachValues !== undefined ? this._eachValues\n                                                 : this._initialValue);\n};\n\nReductionPromiseArray.prototype.shouldCopyValues = function () {\n    return false;\n};\n\nReductionPromiseArray.prototype._resolve = function(value) {\n    this._promise._resolveCallback(value);\n    this._values = null;\n};\n\nReductionPromiseArray.prototype._resultCancelled = function(sender) {\n    if (sender === this._initialValue) return this._cancel();\n    if (this._isResolved()) return;\n    this._resultCancelled$();\n    if (this._currentCancellable instanceof Promise) {\n        this._currentCancellable.cancel();\n    }\n    if (this._initialValue instanceof Promise) {\n        this._initialValue.cancel();\n    }\n};\n\nReductionPromiseArray.prototype._iterate = function (values) {\n    this._values = values;\n    var value;\n    var i;\n    var length = values.length;\n    if (this._initialValue !== undefined) {\n        value = this._initialValue;\n        i = 0;\n    } else {\n        value = Promise.resolve(values[0]);\n        i = 1;\n    }\n\n    this._currentCancellable = value;\n\n    if (!value.isRejected()) {\n        for (; i < length; ++i) {\n            var ctx = {\n                accum: null,\n                value: values[i],\n                index: i,\n                length: length,\n                array: this\n            };\n            value = value._then(gotAccum, undefined, undefined, ctx, undefined);\n        }\n    }\n\n    if (this._eachValues !== undefined) {\n        value = value\n            ._then(this._eachComplete, undefined, undefined, this, undefined);\n    }\n    value._then(completed, completed, undefined, value, this);\n};\n\nPromise.prototype.reduce = function (fn, initialValue) {\n    return reduce(this, fn, initialValue, null);\n};\n\nPromise.reduce = function (promises, fn, initialValue, _each) {\n    return reduce(promises, fn, initialValue, _each);\n};\n\nfunction completed(valueOrReason, array) {\n    if (this.isFulfilled()) {\n        array._resolve(valueOrReason);\n    } else {\n        array._reject(valueOrReason);\n    }\n}\n\nfunction reduce(promises, fn, initialValue, _each) {\n    if (typeof fn !== \"function\") {\n        return apiRejection(\"expecting a function but got \" + util.classString(fn));\n    }\n    var array = new ReductionPromiseArray(promises, fn, initialValue, _each);\n    return array.promise();\n}\n\nfunction gotAccum(accum) {\n    this.accum = accum;\n    this.array._gotAccum(accum);\n    var value = tryConvertToPromise(this.value, this.array._promise);\n    if (value instanceof Promise) {\n        this.array._currentCancellable = value;\n        return value._then(gotValue, undefined, undefined, this, undefined);\n    } else {\n        return gotValue.call(this, value);\n    }\n}\n\nfunction gotValue(value) {\n    var array = this.array;\n    var promise = array._promise;\n    var fn = tryCatch(array._fn);\n    promise._pushContext();\n    var ret;\n    if (array._eachValues !== undefined) {\n        ret = fn.call(promise._boundValue(), value, this.index, this.length);\n    } else {\n        ret = fn.call(promise._boundValue(),\n                              this.accum, value, this.index, this.length);\n    }\n    if (ret instanceof Promise) {\n        array._currentCancellable = ret;\n    }\n    var promiseCreated = promise._popContext();\n    debug.checkForgottenReturns(\n        ret,\n        promiseCreated,\n        array._eachValues !== undefined ? \"Promise.each\" : \"Promise.reduce\",\n        promise\n    );\n    return ret;\n}\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,QAAQ,EACR,KAAK;IAC/B,IAAI,YAAY,QAAQ,UAAU;IAClC,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAE5B,SAAS,sBAAsB,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,KAAK;QAC5D,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,SAAS;QACb,IAAI,CAAC,GAAG,GAAG,WAAW,OAAO,KAAK,KAAK,UAAU,CAAC,QAAQ;QAC1D,IAAI,iBAAiB,WAAW;YAC5B,eAAe,QAAQ,OAAO,CAAC;YAC/B,aAAa,2BAA2B,CAAC,IAAI;QACjD;QACA,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAG,UAAU,UAAU;YACnB,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO;QACzC,OAAO,IAAI,UAAU,GAAG;YACpB,IAAI,CAAC,WAAW,GAAG;QACvB,OAAO;YACH,IAAI,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,CAAC,QAAQ,CAAC,kBAAkB;QAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IAC5B;IACA,KAAK,QAAQ,CAAC,uBAAuB;IAErC,sBAAsB,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK;QACtD,IAAI,IAAI,CAAC,WAAW,KAAK,aACrB,IAAI,CAAC,WAAW,KAAK,QACrB,UAAU,UAAU;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAC1B;IACJ;IAEA,sBAAsB,SAAS,CAAC,aAAa,GAAG,SAAS,KAAK;QAC1D,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM;YAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAC1B;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IAEA,sBAAsB,SAAS,CAAC,KAAK,GAAG,YAAY;IAEpD,sBAAsB,SAAS,CAAC,kBAAkB,GAAG;QACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,aAAa;IACrE;IAEA,sBAAsB,SAAS,CAAC,gBAAgB,GAAG;QAC/C,OAAO;IACX;IAEA,sBAAsB,SAAS,CAAC,QAAQ,GAAG,SAAS,KAAK;QACrD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG;IACnB;IAEA,sBAAsB,SAAS,CAAC,gBAAgB,GAAG,SAAS,MAAM;QAC9D,IAAI,WAAW,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,OAAO;QACtD,IAAI,IAAI,CAAC,WAAW,IAAI;QACxB,IAAI,CAAC,iBAAiB;QACtB,IAAI,IAAI,CAAC,mBAAmB,YAAY,SAAS;YAC7C,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACnC;QACA,IAAI,IAAI,CAAC,aAAa,YAAY,SAAS;YACvC,IAAI,CAAC,aAAa,CAAC,MAAM;QAC7B;IACJ;IAEA,sBAAsB,SAAS,CAAC,QAAQ,GAAG,SAAU,MAAM;QACvD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,OAAO,MAAM;QAC1B,IAAI,IAAI,CAAC,aAAa,KAAK,WAAW;YAClC,QAAQ,IAAI,CAAC,aAAa;YAC1B,IAAI;QACR,OAAO;YACH,QAAQ,QAAQ,OAAO,CAAC,MAAM,CAAC,EAAE;YACjC,IAAI;QACR;QAEA,IAAI,CAAC,mBAAmB,GAAG;QAE3B,IAAI,CAAC,MAAM,UAAU,IAAI;YACrB,MAAO,IAAI,QAAQ,EAAE,EAAG;gBACpB,IAAI,MAAM;oBACN,OAAO;oBACP,OAAO,MAAM,CAAC,EAAE;oBAChB,OAAO;oBACP,QAAQ;oBACR,OAAO,IAAI;gBACf;gBACA,QAAQ,MAAM,KAAK,CAAC,UAAU,WAAW,WAAW,KAAK;YAC7D;QACJ;QAEA,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW;YAChC,QAAQ,MACH,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,WAAW,IAAI,EAAE;QAC/D;QACA,MAAM,KAAK,CAAC,WAAW,WAAW,WAAW,OAAO,IAAI;IAC5D;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,EAAE,YAAY;QACjD,OAAO,OAAO,IAAI,EAAE,IAAI,cAAc;IAC1C;IAEA,QAAQ,MAAM,GAAG,SAAU,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,KAAK;QACxD,OAAO,OAAO,UAAU,IAAI,cAAc;IAC9C;IAEA,SAAS,UAAU,aAAa,EAAE,KAAK;QACnC,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,MAAM,QAAQ,CAAC;QACnB,OAAO;YACH,MAAM,OAAO,CAAC;QAClB;IACJ;IAEA,SAAS,OAAO,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,KAAK;QAC7C,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,aAAa,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,IAAI,QAAQ,IAAI,sBAAsB,UAAU,IAAI,cAAc;QAClE,OAAO,MAAM,OAAO;IACxB;IAEA,SAAS,SAAS,KAAK;QACnB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QACrB,IAAI,QAAQ,oBAAoB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC/D,IAAI,iBAAiB,SAAS;YAC1B,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG;YACjC,OAAO,MAAM,KAAK,CAAC,UAAU,WAAW,WAAW,IAAI,EAAE;QAC7D,OAAO;YACH,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE;QAC/B;IACJ;IAEA,SAAS,SAAS,KAAK;QACnB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,UAAU,MAAM,QAAQ;QAC5B,IAAI,KAAK,SAAS,MAAM,GAAG;QAC3B,QAAQ,YAAY;QACpB,IAAI;QACJ,IAAI,MAAM,WAAW,KAAK,WAAW;YACjC,MAAM,GAAG,IAAI,CAAC,QAAQ,WAAW,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QACvE,OAAO;YACH,MAAM,GAAG,IAAI,CAAC,QAAQ,WAAW,IACX,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;QACpE;QACA,IAAI,eAAe,SAAS;YACxB,MAAM,mBAAmB,GAAG;QAChC;QACA,IAAI,iBAAiB,QAAQ,WAAW;QACxC,MAAM,qBAAqB,CACvB,KACA,gBACA,MAAM,WAAW,KAAK,YAAY,iBAAiB,kBACnD;QAEJ,OAAO;IACX;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/settle.js"], "sourcesContent": ["\"use strict\";\nmodule.exports =\n    function(Promise, PromiseArray, debug) {\nvar PromiseInspection = Promise.PromiseInspection;\nvar util = require(\"./util\");\n\nfunction SettledPromiseArray(values) {\n    this.constructor$(values);\n}\nutil.inherits(SettledPromiseArray, PromiseArray);\n\nSettledPromiseArray.prototype._promiseResolved = function (index, inspection) {\n    this._values[index] = inspection;\n    var totalResolved = ++this._totalResolved;\n    if (totalResolved >= this._length) {\n        this._resolve(this._values);\n        return true;\n    }\n    return false;\n};\n\nSettledPromiseArray.prototype._promiseFulfilled = function (value, index) {\n    var ret = new PromiseInspection();\n    ret._bitField = 33554432;\n    ret._settledValueField = value;\n    return this._promiseResolved(index, ret);\n};\nSettledPromiseArray.prototype._promiseRejected = function (reason, index) {\n    var ret = new PromiseInspection();\n    ret._bitField = 16777216;\n    ret._settledValueField = reason;\n    return this._promiseResolved(index, ret);\n};\n\nPromise.settle = function (promises) {\n    debug.deprecated(\".settle()\", \".reflect()\");\n    return new SettledPromiseArray(promises).promise();\n};\n\nPromise.prototype.settle = function () {\n    return Promise.settle(this);\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GACV,SAAS,OAAO,EAAE,YAAY,EAAE,KAAK;IACzC,IAAI,oBAAoB,QAAQ,iBAAiB;IACjD,IAAI;IAEJ,SAAS,oBAAoB,MAAM;QAC/B,IAAI,CAAC,YAAY,CAAC;IACtB;IACA,KAAK,QAAQ,CAAC,qBAAqB;IAEnC,oBAAoB,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAK,EAAE,UAAU;QACxE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACtB,IAAI,gBAAgB,EAAE,IAAI,CAAC,cAAc;QACzC,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC1B,OAAO;QACX;QACA,OAAO;IACX;IAEA,oBAAoB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,KAAK;QACpE,IAAI,MAAM,IAAI;QACd,IAAI,SAAS,GAAG;QAChB,IAAI,kBAAkB,GAAG;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO;IACxC;IACA,oBAAoB,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM,EAAE,KAAK;QACpE,IAAI,MAAM,IAAI;QACd,IAAI,SAAS,GAAG;QAChB,IAAI,kBAAkB,GAAG;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO;IACxC;IAEA,QAAQ,MAAM,GAAG,SAAU,QAAQ;QAC/B,MAAM,UAAU,CAAC,aAAa;QAC9B,OAAO,IAAI,oBAAoB,UAAU,OAAO;IACpD;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG;QACvB,OAAO,QAAQ,MAAM,CAAC,IAAI;IAC9B;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4007, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/some.js"], "sourcesContent": ["\"use strict\";\nmodule.exports =\nfunction(Promise, PromiseArray, apiRejection) {\nvar util = require(\"./util\");\nvar RangeError = require(\"./errors\").RangeError;\nvar AggregateError = require(\"./errors\").AggregateError;\nvar isArray = util.isArray;\nvar CANCELLATION = {};\n\n\nfunction SomePromiseArray(values) {\n    this.constructor$(values);\n    this._howMany = 0;\n    this._unwrap = false;\n    this._initialized = false;\n}\nutil.inherits(SomePromiseArray, PromiseArray);\n\nSomePromiseArray.prototype._init = function () {\n    if (!this._initialized) {\n        return;\n    }\n    if (this._howMany === 0) {\n        this._resolve([]);\n        return;\n    }\n    this._init$(undefined, -5);\n    var isArrayResolved = isArray(this._values);\n    if (!this._isResolved() &&\n        isArrayResolved &&\n        this._howMany > this._canPossiblyFulfill()) {\n        this._reject(this._getRangeError(this.length()));\n    }\n};\n\nSomePromiseArray.prototype.init = function () {\n    this._initialized = true;\n    this._init();\n};\n\nSomePromiseArray.prototype.setUnwrap = function () {\n    this._unwrap = true;\n};\n\nSomePromiseArray.prototype.howMany = function () {\n    return this._howMany;\n};\n\nSomePromiseArray.prototype.setHowMany = function (count) {\n    this._howMany = count;\n};\n\nSomePromiseArray.prototype._promiseFulfilled = function (value) {\n    this._addFulfilled(value);\n    if (this._fulfilled() === this.howMany()) {\n        this._values.length = this.howMany();\n        if (this.howMany() === 1 && this._unwrap) {\n            this._resolve(this._values[0]);\n        } else {\n            this._resolve(this._values);\n        }\n        return true;\n    }\n    return false;\n\n};\nSomePromiseArray.prototype._promiseRejected = function (reason) {\n    this._addRejected(reason);\n    return this._checkOutcome();\n};\n\nSomePromiseArray.prototype._promiseCancelled = function () {\n    if (this._values instanceof Promise || this._values == null) {\n        return this._cancel();\n    }\n    this._addRejected(CANCELLATION);\n    return this._checkOutcome();\n};\n\nSomePromiseArray.prototype._checkOutcome = function() {\n    if (this.howMany() > this._canPossiblyFulfill()) {\n        var e = new AggregateError();\n        for (var i = this.length(); i < this._values.length; ++i) {\n            if (this._values[i] !== CANCELLATION) {\n                e.push(this._values[i]);\n            }\n        }\n        if (e.length > 0) {\n            this._reject(e);\n        } else {\n            this._cancel();\n        }\n        return true;\n    }\n    return false;\n};\n\nSomePromiseArray.prototype._fulfilled = function () {\n    return this._totalResolved;\n};\n\nSomePromiseArray.prototype._rejected = function () {\n    return this._values.length - this.length();\n};\n\nSomePromiseArray.prototype._addRejected = function (reason) {\n    this._values.push(reason);\n};\n\nSomePromiseArray.prototype._addFulfilled = function (value) {\n    this._values[this._totalResolved++] = value;\n};\n\nSomePromiseArray.prototype._canPossiblyFulfill = function () {\n    return this.length() - this._rejected();\n};\n\nSomePromiseArray.prototype._getRangeError = function (count) {\n    var message = \"Input array must contain at least \" +\n            this._howMany + \" items but contains only \" + count + \" items\";\n    return new RangeError(message);\n};\n\nSomePromiseArray.prototype._resolveEmptyArray = function () {\n    this._reject(this._getRangeError(0));\n};\n\nfunction some(promises, howMany) {\n    if ((howMany | 0) !== howMany || howMany < 0) {\n        return apiRejection(\"expecting a positive integer\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n    var ret = new SomePromiseArray(promises);\n    var promise = ret.promise();\n    ret.setHowMany(howMany);\n    ret.init();\n    return promise;\n}\n\nPromise.some = function (promises, howMany) {\n    return some(promises, howMany);\n};\n\nPromise.prototype.some = function (howMany) {\n    return some(this, howMany);\n};\n\nPromise._SomePromiseArray = SomePromiseArray;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GACd,SAAS,OAAO,EAAE,YAAY,EAAE,YAAY;IAC5C,IAAI;IACJ,IAAI,aAAa,0GAAoB,UAAU;IAC/C,IAAI,iBAAiB,0GAAoB,cAAc;IACvD,IAAI,UAAU,KAAK,OAAO;IAC1B,IAAI,eAAe,CAAC;IAGpB,SAAS,iBAAiB,MAAM;QAC5B,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,KAAK,QAAQ,CAAC,kBAAkB;IAEhC,iBAAiB,SAAS,CAAC,KAAK,GAAG;QAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB;QACJ;QACA,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG;YACrB,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChB;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACxB,IAAI,kBAAkB,QAAQ,IAAI,CAAC,OAAO;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,MACjB,mBACA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,IAAI;YAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;QAChD;IACJ;IAEA,iBAAiB,SAAS,CAAC,IAAI,GAAG;QAC9B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK;IACd;IAEA,iBAAiB,SAAS,CAAC,SAAS,GAAG;QACnC,IAAI,CAAC,OAAO,GAAG;IACnB;IAEA,iBAAiB,SAAS,CAAC,OAAO,GAAG;QACjC,OAAO,IAAI,CAAC,QAAQ;IACxB;IAEA,iBAAiB,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK;QACnD,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA,iBAAiB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK;QAC1D,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,OAAO,IAAI;YACtC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;YAClC,IAAI,IAAI,CAAC,OAAO,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACjC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC9B;YACA,OAAO;QACX;QACA,OAAO;IAEX;IACA,iBAAiB,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM;QAC1D,IAAI,CAAC,YAAY,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,iBAAiB,SAAS,CAAC,iBAAiB,GAAG;QAC3C,IAAI,IAAI,CAAC,OAAO,YAAY,WAAW,IAAI,CAAC,OAAO,IAAI,MAAM;YACzD,OAAO,IAAI,CAAC,OAAO;QACvB;QACA,IAAI,CAAC,YAAY,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa;IAC7B;IAEA,iBAAiB,SAAS,CAAC,aAAa,GAAG;QACvC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,mBAAmB,IAAI;YAC7C,IAAI,IAAI,IAAI;YACZ,IAAK,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAG;gBACtD,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,cAAc;oBAClC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC1B;YACJ;YACA,IAAI,EAAE,MAAM,GAAG,GAAG;gBACd,IAAI,CAAC,OAAO,CAAC;YACjB,OAAO;gBACH,IAAI,CAAC,OAAO;YAChB;YACA,OAAO;QACX;QACA,OAAO;IACX;IAEA,iBAAiB,SAAS,CAAC,UAAU,GAAG;QACpC,OAAO,IAAI,CAAC,cAAc;IAC9B;IAEA,iBAAiB,SAAS,CAAC,SAAS,GAAG;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;IAC5C;IAEA,iBAAiB,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM;QACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IACtB;IAEA,iBAAiB,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG;IAC1C;IAEA,iBAAiB,SAAS,CAAC,mBAAmB,GAAG;QAC7C,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS;IACzC;IAEA,iBAAiB,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QACvD,IAAI,UAAU,uCACN,IAAI,CAAC,QAAQ,GAAG,8BAA8B,QAAQ;QAC9D,OAAO,IAAI,WAAW;IAC1B;IAEA,iBAAiB,SAAS,CAAC,kBAAkB,GAAG;QAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;IACrC;IAEA,SAAS,KAAK,QAAQ,EAAE,OAAO;QAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,WAAW,UAAU,GAAG;YAC1C,OAAO,aAAa;QACxB;QACA,IAAI,MAAM,IAAI,iBAAiB;QAC/B,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,CAAC;QACf,IAAI,IAAI;QACR,OAAO;IACX;IAEA,QAAQ,IAAI,GAAG,SAAU,QAAQ,EAAE,OAAO;QACtC,OAAO,KAAK,UAAU;IAC1B;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO;QACtC,OAAO,KAAK,IAAI,EAAE;IACtB;IAEA,QAAQ,iBAAiB,GAAG;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4134, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/filter.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL) {\nvar PromiseMap = Promise.map;\n\nPromise.prototype.filter = function (fn, options) {\n    return PromiseMap(this, fn, options, INTERNAL);\n};\n\nPromise.filter = function (promises, fn, options) {\n    return PromiseMap(promises, fn, options, INTERNAL);\n};\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ;IAC3C,IAAI,aAAa,QAAQ,GAAG;IAE5B,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,EAAE,OAAO;QAC5C,OAAO,WAAW,IAAI,EAAE,IAAI,SAAS;IACzC;IAEA,QAAQ,MAAM,GAAG,SAAU,QAAQ,EAAE,EAAE,EAAE,OAAO;QAC5C,OAAO,WAAW,UAAU,IAAI,SAAS;IAC7C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/each.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise, INTERNAL) {\nvar PromiseReduce = Promise.reduce;\nvar PromiseAll = Promise.all;\n\nfunction promiseAllThis() {\n    return PromiseAll(this);\n}\n\nfunction PromiseMapSeries(promises, fn) {\n    return PromiseReduce(promises, fn, INTERNAL, INTERNAL);\n}\n\nPromise.prototype.each = function (fn) {\n    return PromiseReduce(this, fn, INTERNAL, 0)\n              ._then(promiseAllThis, undefined, undefined, this, undefined);\n};\n\nPromise.prototype.mapSeries = function (fn) {\n    return PromiseReduce(this, fn, INTERNAL, INTERNAL);\n};\n\nPromise.each = function (promises, fn) {\n    return PromiseReduce(promises, fn, INTERNAL, 0)\n              ._then(promiseAllThis, undefined, undefined, promises, undefined);\n};\n\nPromise.mapSeries = PromiseMapSeries;\n};\n\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,EAAE,QAAQ;IAC3C,IAAI,gBAAgB,QAAQ,MAAM;IAClC,IAAI,aAAa,QAAQ,GAAG;IAE5B,SAAS;QACL,OAAO,WAAW,IAAI;IAC1B;IAEA,SAAS,iBAAiB,QAAQ,EAAE,EAAE;QAClC,OAAO,cAAc,UAAU,IAAI,UAAU;IACjD;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,EAAE;QACjC,OAAO,cAAc,IAAI,EAAE,IAAI,UAAU,GAC9B,KAAK,CAAC,gBAAgB,WAAW,WAAW,IAAI,EAAE;IACjE;IAEA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,EAAE;QACtC,OAAO,cAAc,IAAI,EAAE,IAAI,UAAU;IAC7C;IAEA,QAAQ,IAAI,GAAG,SAAU,QAAQ,EAAE,EAAE;QACjC,OAAO,cAAc,UAAU,IAAI,UAAU,GAClC,KAAK,CAAC,gBAAgB,WAAW,WAAW,UAAU;IACrE;IAEA,QAAQ,SAAS,GAAG;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/any.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function(Promise) {\nvar SomePromiseArray = Promise._SomePromiseArray;\nfunction any(promises) {\n    var ret = new SomePromiseArray(promises);\n    var promise = ret.promise();\n    ret.setHowMany(1);\n    ret.setUnwrap();\n    ret.init();\n    return promise;\n}\n\nPromise.any = function (promises) {\n    return any(promises);\n};\n\nPromise.prototype.any = function () {\n    return any(this);\n};\n\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,OAAO,GAAG,SAAS,OAAO;IACjC,IAAI,mBAAmB,QAAQ,iBAAiB;IAChD,SAAS,IAAI,QAAQ;QACjB,IAAI,MAAM,IAAI,iBAAiB;QAC/B,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,CAAC;QACf,IAAI,SAAS;QACb,IAAI,IAAI;QACR,OAAO;IACX;IAEA,QAAQ,GAAG,GAAG,SAAU,QAAQ;QAC5B,OAAO,IAAI;IACf;IAEA,QAAQ,SAAS,CAAC,GAAG,GAAG;QACpB,OAAO,IAAI,IAAI;IACnB;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/bluebird/js/release/promise.js"], "sourcesContent": ["\"use strict\";\nmodule.exports = function() {\nvar makeSelfResolutionError = function () {\n    return new TypeError(\"circular promise resolution chain\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n};\nvar reflectHandler = function() {\n    return new Promise.PromiseInspection(this._target());\n};\nvar apiRejection = function(msg) {\n    return Promise.reject(new TypeError(msg));\n};\nfunction Proxyable() {}\nvar UNDEFINED_BINDING = {};\nvar util = require(\"./util\");\n\nvar getDomain;\nif (util.isNode) {\n    getDomain = function() {\n        var ret = process.domain;\n        if (ret === undefined) ret = null;\n        return ret;\n    };\n} else {\n    getDomain = function() {\n        return null;\n    };\n}\nutil.notEnumerableProp(Promise, \"_getDomain\", getDomain);\n\nvar es5 = require(\"./es5\");\nvar Async = require(\"./async\");\nvar async = new Async();\nes5.defineProperty(Promise, \"_async\", {value: async});\nvar errors = require(\"./errors\");\nvar TypeError = Promise.TypeError = errors.TypeError;\nPromise.RangeError = errors.RangeError;\nvar CancellationError = Promise.CancellationError = errors.CancellationError;\nPromise.TimeoutError = errors.TimeoutError;\nPromise.OperationalError = errors.OperationalError;\nPromise.RejectionError = errors.OperationalError;\nPromise.AggregateError = errors.AggregateError;\nvar INTERNAL = function(){};\nvar APPLY = {};\nvar NEXT_FILTER = {};\nvar tryConvertToPromise = require(\"./thenables\")(Promise, INTERNAL);\nvar PromiseArray =\n    require(\"./promise_array\")(Promise, INTERNAL,\n                               tryConvertToPromise, apiRejection, Proxyable);\nvar Context = require(\"./context\")(Promise);\n /*jshint unused:false*/\nvar createContext = Context.create;\nvar debug = require(\"./debuggability\")(Promise, Context);\nvar CapturedTrace = debug.CapturedTrace;\nvar PassThroughHandlerContext =\n    require(\"./finally\")(Promise, tryConvertToPromise);\nvar catchFilter = require(\"./catch_filter\")(NEXT_FILTER);\nvar nodebackForPromise = require(\"./nodeback\");\nvar errorObj = util.errorObj;\nvar tryCatch = util.tryCatch;\nfunction check(self, executor) {\n    if (typeof executor !== \"function\") {\n        throw new TypeError(\"expecting a function but got \" + util.classString(executor));\n    }\n    if (self.constructor !== Promise) {\n        throw new TypeError(\"the promise constructor cannot be invoked directly\\u000a\\u000a    See http://goo.gl/MqrFmX\\u000a\");\n    }\n}\n\nfunction Promise(executor) {\n    this._bitField = 0;\n    this._fulfillmentHandler0 = undefined;\n    this._rejectionHandler0 = undefined;\n    this._promise0 = undefined;\n    this._receiver0 = undefined;\n    if (executor !== INTERNAL) {\n        check(this, executor);\n        this._resolveFromExecutor(executor);\n    }\n    this._promiseCreated();\n    this._fireEvent(\"promiseCreated\", this);\n}\n\nPromise.prototype.toString = function () {\n    return \"[object Promise]\";\n};\n\nPromise.prototype.caught = Promise.prototype[\"catch\"] = function (fn) {\n    var len = arguments.length;\n    if (len > 1) {\n        var catchInstances = new Array(len - 1),\n            j = 0, i;\n        for (i = 0; i < len - 1; ++i) {\n            var item = arguments[i];\n            if (util.isObject(item)) {\n                catchInstances[j++] = item;\n            } else {\n                return apiRejection(\"expecting an object but got \" +\n                    \"A catch statement predicate \" + util.classString(item));\n            }\n        }\n        catchInstances.length = j;\n        fn = arguments[i];\n        return this.then(undefined, catchFilter(catchInstances, fn, this));\n    }\n    return this.then(undefined, fn);\n};\n\nPromise.prototype.reflect = function () {\n    return this._then(reflectHandler,\n        reflectHandler, undefined, this, undefined);\n};\n\nPromise.prototype.then = function (didFulfill, didReject) {\n    if (debug.warnings() && arguments.length > 0 &&\n        typeof didFulfill !== \"function\" &&\n        typeof didReject !== \"function\") {\n        var msg = \".then() only accepts functions but was passed: \" +\n                util.classString(didFulfill);\n        if (arguments.length > 1) {\n            msg += \", \" + util.classString(didReject);\n        }\n        this._warn(msg);\n    }\n    return this._then(didFulfill, didReject, undefined, undefined, undefined);\n};\n\nPromise.prototype.done = function (didFulfill, didReject) {\n    var promise =\n        this._then(didFulfill, didReject, undefined, undefined, undefined);\n    promise._setIsFinal();\n};\n\nPromise.prototype.spread = function (fn) {\n    if (typeof fn !== \"function\") {\n        return apiRejection(\"expecting a function but got \" + util.classString(fn));\n    }\n    return this.all()._then(fn, undefined, undefined, APPLY, undefined);\n};\n\nPromise.prototype.toJSON = function () {\n    var ret = {\n        isFulfilled: false,\n        isRejected: false,\n        fulfillmentValue: undefined,\n        rejectionReason: undefined\n    };\n    if (this.isFulfilled()) {\n        ret.fulfillmentValue = this.value();\n        ret.isFulfilled = true;\n    } else if (this.isRejected()) {\n        ret.rejectionReason = this.reason();\n        ret.isRejected = true;\n    }\n    return ret;\n};\n\nPromise.prototype.all = function () {\n    if (arguments.length > 0) {\n        this._warn(\".all() was passed arguments but it does not take any\");\n    }\n    return new PromiseArray(this).promise();\n};\n\nPromise.prototype.error = function (fn) {\n    return this.caught(util.originatesFromRejection, fn);\n};\n\nPromise.getNewLibraryCopy = module.exports;\n\nPromise.is = function (val) {\n    return val instanceof Promise;\n};\n\nPromise.fromNode = Promise.fromCallback = function(fn) {\n    var ret = new Promise(INTERNAL);\n    ret._captureStackTrace();\n    var multiArgs = arguments.length > 1 ? !!Object(arguments[1]).multiArgs\n                                         : false;\n    var result = tryCatch(fn)(nodebackForPromise(ret, multiArgs));\n    if (result === errorObj) {\n        ret._rejectCallback(result.e, true);\n    }\n    if (!ret._isFateSealed()) ret._setAsyncGuaranteed();\n    return ret;\n};\n\nPromise.all = function (promises) {\n    return new PromiseArray(promises).promise();\n};\n\nPromise.cast = function (obj) {\n    var ret = tryConvertToPromise(obj);\n    if (!(ret instanceof Promise)) {\n        ret = new Promise(INTERNAL);\n        ret._captureStackTrace();\n        ret._setFulfilled();\n        ret._rejectionHandler0 = obj;\n    }\n    return ret;\n};\n\nPromise.resolve = Promise.fulfilled = Promise.cast;\n\nPromise.reject = Promise.rejected = function (reason) {\n    var ret = new Promise(INTERNAL);\n    ret._captureStackTrace();\n    ret._rejectCallback(reason, true);\n    return ret;\n};\n\nPromise.setScheduler = function(fn) {\n    if (typeof fn !== \"function\") {\n        throw new TypeError(\"expecting a function but got \" + util.classString(fn));\n    }\n    return async.setScheduler(fn);\n};\n\nPromise.prototype._then = function (\n    didFulfill,\n    didReject,\n    _,    receiver,\n    internalData\n) {\n    var haveInternalData = internalData !== undefined;\n    var promise = haveInternalData ? internalData : new Promise(INTERNAL);\n    var target = this._target();\n    var bitField = target._bitField;\n\n    if (!haveInternalData) {\n        promise._propagateFrom(this, 3);\n        promise._captureStackTrace();\n        if (receiver === undefined &&\n            ((this._bitField & 2097152) !== 0)) {\n            if (!((bitField & 50397184) === 0)) {\n                receiver = this._boundValue();\n            } else {\n                receiver = target === this ? undefined : this._boundTo;\n            }\n        }\n        this._fireEvent(\"promiseChained\", this, promise);\n    }\n\n    var domain = getDomain();\n    if (!((bitField & 50397184) === 0)) {\n        var handler, value, settler = target._settlePromiseCtx;\n        if (((bitField & 33554432) !== 0)) {\n            value = target._rejectionHandler0;\n            handler = didFulfill;\n        } else if (((bitField & 16777216) !== 0)) {\n            value = target._fulfillmentHandler0;\n            handler = didReject;\n            target._unsetRejectionIsUnhandled();\n        } else {\n            settler = target._settlePromiseLateCancellationObserver;\n            value = new CancellationError(\"late cancellation observer\");\n            target._attachExtraTrace(value);\n            handler = didReject;\n        }\n\n        async.invoke(settler, target, {\n            handler: domain === null ? handler\n                : (typeof handler === \"function\" &&\n                    util.domainBind(domain, handler)),\n            promise: promise,\n            receiver: receiver,\n            value: value\n        });\n    } else {\n        target._addCallbacks(didFulfill, didReject, promise, receiver, domain);\n    }\n\n    return promise;\n};\n\nPromise.prototype._length = function () {\n    return this._bitField & 65535;\n};\n\nPromise.prototype._isFateSealed = function () {\n    return (this._bitField & 117506048) !== 0;\n};\n\nPromise.prototype._isFollowing = function () {\n    return (this._bitField & 67108864) === 67108864;\n};\n\nPromise.prototype._setLength = function (len) {\n    this._bitField = (this._bitField & -65536) |\n        (len & 65535);\n};\n\nPromise.prototype._setFulfilled = function () {\n    this._bitField = this._bitField | 33554432;\n    this._fireEvent(\"promiseFulfilled\", this);\n};\n\nPromise.prototype._setRejected = function () {\n    this._bitField = this._bitField | 16777216;\n    this._fireEvent(\"promiseRejected\", this);\n};\n\nPromise.prototype._setFollowing = function () {\n    this._bitField = this._bitField | 67108864;\n    this._fireEvent(\"promiseResolved\", this);\n};\n\nPromise.prototype._setIsFinal = function () {\n    this._bitField = this._bitField | 4194304;\n};\n\nPromise.prototype._isFinal = function () {\n    return (this._bitField & 4194304) > 0;\n};\n\nPromise.prototype._unsetCancelled = function() {\n    this._bitField = this._bitField & (~65536);\n};\n\nPromise.prototype._setCancelled = function() {\n    this._bitField = this._bitField | 65536;\n    this._fireEvent(\"promiseCancelled\", this);\n};\n\nPromise.prototype._setWillBeCancelled = function() {\n    this._bitField = this._bitField | 8388608;\n};\n\nPromise.prototype._setAsyncGuaranteed = function() {\n    if (async.hasCustomScheduler()) return;\n    this._bitField = this._bitField | 134217728;\n};\n\nPromise.prototype._receiverAt = function (index) {\n    var ret = index === 0 ? this._receiver0 : this[\n            index * 4 - 4 + 3];\n    if (ret === UNDEFINED_BINDING) {\n        return undefined;\n    } else if (ret === undefined && this._isBound()) {\n        return this._boundValue();\n    }\n    return ret;\n};\n\nPromise.prototype._promiseAt = function (index) {\n    return this[\n            index * 4 - 4 + 2];\n};\n\nPromise.prototype._fulfillmentHandlerAt = function (index) {\n    return this[\n            index * 4 - 4 + 0];\n};\n\nPromise.prototype._rejectionHandlerAt = function (index) {\n    return this[\n            index * 4 - 4 + 1];\n};\n\nPromise.prototype._boundValue = function() {};\n\nPromise.prototype._migrateCallback0 = function (follower) {\n    var bitField = follower._bitField;\n    var fulfill = follower._fulfillmentHandler0;\n    var reject = follower._rejectionHandler0;\n    var promise = follower._promise0;\n    var receiver = follower._receiverAt(0);\n    if (receiver === undefined) receiver = UNDEFINED_BINDING;\n    this._addCallbacks(fulfill, reject, promise, receiver, null);\n};\n\nPromise.prototype._migrateCallbackAt = function (follower, index) {\n    var fulfill = follower._fulfillmentHandlerAt(index);\n    var reject = follower._rejectionHandlerAt(index);\n    var promise = follower._promiseAt(index);\n    var receiver = follower._receiverAt(index);\n    if (receiver === undefined) receiver = UNDEFINED_BINDING;\n    this._addCallbacks(fulfill, reject, promise, receiver, null);\n};\n\nPromise.prototype._addCallbacks = function (\n    fulfill,\n    reject,\n    promise,\n    receiver,\n    domain\n) {\n    var index = this._length();\n\n    if (index >= 65535 - 4) {\n        index = 0;\n        this._setLength(0);\n    }\n\n    if (index === 0) {\n        this._promise0 = promise;\n        this._receiver0 = receiver;\n        if (typeof fulfill === \"function\") {\n            this._fulfillmentHandler0 =\n                domain === null ? fulfill : util.domainBind(domain, fulfill);\n        }\n        if (typeof reject === \"function\") {\n            this._rejectionHandler0 =\n                domain === null ? reject : util.domainBind(domain, reject);\n        }\n    } else {\n        var base = index * 4 - 4;\n        this[base + 2] = promise;\n        this[base + 3] = receiver;\n        if (typeof fulfill === \"function\") {\n            this[base + 0] =\n                domain === null ? fulfill : util.domainBind(domain, fulfill);\n        }\n        if (typeof reject === \"function\") {\n            this[base + 1] =\n                domain === null ? reject : util.domainBind(domain, reject);\n        }\n    }\n    this._setLength(index + 1);\n    return index;\n};\n\nPromise.prototype._proxy = function (proxyable, arg) {\n    this._addCallbacks(undefined, undefined, arg, proxyable, null);\n};\n\nPromise.prototype._resolveCallback = function(value, shouldBind) {\n    if (((this._bitField & 117506048) !== 0)) return;\n    if (value === this)\n        return this._rejectCallback(makeSelfResolutionError(), false);\n    var maybePromise = tryConvertToPromise(value, this);\n    if (!(maybePromise instanceof Promise)) return this._fulfill(value);\n\n    if (shouldBind) this._propagateFrom(maybePromise, 2);\n\n    var promise = maybePromise._target();\n\n    if (promise === this) {\n        this._reject(makeSelfResolutionError());\n        return;\n    }\n\n    var bitField = promise._bitField;\n    if (((bitField & 50397184) === 0)) {\n        var len = this._length();\n        if (len > 0) promise._migrateCallback0(this);\n        for (var i = 1; i < len; ++i) {\n            promise._migrateCallbackAt(this, i);\n        }\n        this._setFollowing();\n        this._setLength(0);\n        this._setFollowee(promise);\n    } else if (((bitField & 33554432) !== 0)) {\n        this._fulfill(promise._value());\n    } else if (((bitField & 16777216) !== 0)) {\n        this._reject(promise._reason());\n    } else {\n        var reason = new CancellationError(\"late cancellation observer\");\n        promise._attachExtraTrace(reason);\n        this._reject(reason);\n    }\n};\n\nPromise.prototype._rejectCallback =\nfunction(reason, synchronous, ignoreNonErrorWarnings) {\n    var trace = util.ensureErrorObject(reason);\n    var hasStack = trace === reason;\n    if (!hasStack && !ignoreNonErrorWarnings && debug.warnings()) {\n        var message = \"a promise was rejected with a non-error: \" +\n            util.classString(reason);\n        this._warn(message, true);\n    }\n    this._attachExtraTrace(trace, synchronous ? hasStack : false);\n    this._reject(reason);\n};\n\nPromise.prototype._resolveFromExecutor = function (executor) {\n    var promise = this;\n    this._captureStackTrace();\n    this._pushContext();\n    var synchronous = true;\n    var r = this._execute(executor, function(value) {\n        promise._resolveCallback(value);\n    }, function (reason) {\n        promise._rejectCallback(reason, synchronous);\n    });\n    synchronous = false;\n    this._popContext();\n\n    if (r !== undefined) {\n        promise._rejectCallback(r, true);\n    }\n};\n\nPromise.prototype._settlePromiseFromHandler = function (\n    handler, receiver, value, promise\n) {\n    var bitField = promise._bitField;\n    if (((bitField & 65536) !== 0)) return;\n    promise._pushContext();\n    var x;\n    if (receiver === APPLY) {\n        if (!value || typeof value.length !== \"number\") {\n            x = errorObj;\n            x.e = new TypeError(\"cannot .spread() a non-array: \" +\n                                    util.classString(value));\n        } else {\n            x = tryCatch(handler).apply(this._boundValue(), value);\n        }\n    } else {\n        x = tryCatch(handler).call(receiver, value);\n    }\n    var promiseCreated = promise._popContext();\n    bitField = promise._bitField;\n    if (((bitField & 65536) !== 0)) return;\n\n    if (x === NEXT_FILTER) {\n        promise._reject(value);\n    } else if (x === errorObj) {\n        promise._rejectCallback(x.e, false);\n    } else {\n        debug.checkForgottenReturns(x, promiseCreated, \"\",  promise, this);\n        promise._resolveCallback(x);\n    }\n};\n\nPromise.prototype._target = function() {\n    var ret = this;\n    while (ret._isFollowing()) ret = ret._followee();\n    return ret;\n};\n\nPromise.prototype._followee = function() {\n    return this._rejectionHandler0;\n};\n\nPromise.prototype._setFollowee = function(promise) {\n    this._rejectionHandler0 = promise;\n};\n\nPromise.prototype._settlePromise = function(promise, handler, receiver, value) {\n    var isPromise = promise instanceof Promise;\n    var bitField = this._bitField;\n    var asyncGuaranteed = ((bitField & 134217728) !== 0);\n    if (((bitField & 65536) !== 0)) {\n        if (isPromise) promise._invokeInternalOnCancel();\n\n        if (receiver instanceof PassThroughHandlerContext &&\n            receiver.isFinallyHandler()) {\n            receiver.cancelPromise = promise;\n            if (tryCatch(handler).call(receiver, value) === errorObj) {\n                promise._reject(errorObj.e);\n            }\n        } else if (handler === reflectHandler) {\n            promise._fulfill(reflectHandler.call(receiver));\n        } else if (receiver instanceof Proxyable) {\n            receiver._promiseCancelled(promise);\n        } else if (isPromise || promise instanceof PromiseArray) {\n            promise._cancel();\n        } else {\n            receiver.cancel();\n        }\n    } else if (typeof handler === \"function\") {\n        if (!isPromise) {\n            handler.call(receiver, value, promise);\n        } else {\n            if (asyncGuaranteed) promise._setAsyncGuaranteed();\n            this._settlePromiseFromHandler(handler, receiver, value, promise);\n        }\n    } else if (receiver instanceof Proxyable) {\n        if (!receiver._isResolved()) {\n            if (((bitField & 33554432) !== 0)) {\n                receiver._promiseFulfilled(value, promise);\n            } else {\n                receiver._promiseRejected(value, promise);\n            }\n        }\n    } else if (isPromise) {\n        if (asyncGuaranteed) promise._setAsyncGuaranteed();\n        if (((bitField & 33554432) !== 0)) {\n            promise._fulfill(value);\n        } else {\n            promise._reject(value);\n        }\n    }\n};\n\nPromise.prototype._settlePromiseLateCancellationObserver = function(ctx) {\n    var handler = ctx.handler;\n    var promise = ctx.promise;\n    var receiver = ctx.receiver;\n    var value = ctx.value;\n    if (typeof handler === \"function\") {\n        if (!(promise instanceof Promise)) {\n            handler.call(receiver, value, promise);\n        } else {\n            this._settlePromiseFromHandler(handler, receiver, value, promise);\n        }\n    } else if (promise instanceof Promise) {\n        promise._reject(value);\n    }\n};\n\nPromise.prototype._settlePromiseCtx = function(ctx) {\n    this._settlePromise(ctx.promise, ctx.handler, ctx.receiver, ctx.value);\n};\n\nPromise.prototype._settlePromise0 = function(handler, value, bitField) {\n    var promise = this._promise0;\n    var receiver = this._receiverAt(0);\n    this._promise0 = undefined;\n    this._receiver0 = undefined;\n    this._settlePromise(promise, handler, receiver, value);\n};\n\nPromise.prototype._clearCallbackDataAtIndex = function(index) {\n    var base = index * 4 - 4;\n    this[base + 2] =\n    this[base + 3] =\n    this[base + 0] =\n    this[base + 1] = undefined;\n};\n\nPromise.prototype._fulfill = function (value) {\n    var bitField = this._bitField;\n    if (((bitField & 117506048) >>> 16)) return;\n    if (value === this) {\n        var err = makeSelfResolutionError();\n        this._attachExtraTrace(err);\n        return this._reject(err);\n    }\n    this._setFulfilled();\n    this._rejectionHandler0 = value;\n\n    if ((bitField & 65535) > 0) {\n        if (((bitField & 134217728) !== 0)) {\n            this._settlePromises();\n        } else {\n            async.settlePromises(this);\n        }\n    }\n};\n\nPromise.prototype._reject = function (reason) {\n    var bitField = this._bitField;\n    if (((bitField & 117506048) >>> 16)) return;\n    this._setRejected();\n    this._fulfillmentHandler0 = reason;\n\n    if (this._isFinal()) {\n        return async.fatalError(reason, util.isNode);\n    }\n\n    if ((bitField & 65535) > 0) {\n        async.settlePromises(this);\n    } else {\n        this._ensurePossibleRejectionHandled();\n    }\n};\n\nPromise.prototype._fulfillPromises = function (len, value) {\n    for (var i = 1; i < len; i++) {\n        var handler = this._fulfillmentHandlerAt(i);\n        var promise = this._promiseAt(i);\n        var receiver = this._receiverAt(i);\n        this._clearCallbackDataAtIndex(i);\n        this._settlePromise(promise, handler, receiver, value);\n    }\n};\n\nPromise.prototype._rejectPromises = function (len, reason) {\n    for (var i = 1; i < len; i++) {\n        var handler = this._rejectionHandlerAt(i);\n        var promise = this._promiseAt(i);\n        var receiver = this._receiverAt(i);\n        this._clearCallbackDataAtIndex(i);\n        this._settlePromise(promise, handler, receiver, reason);\n    }\n};\n\nPromise.prototype._settlePromises = function () {\n    var bitField = this._bitField;\n    var len = (bitField & 65535);\n\n    if (len > 0) {\n        if (((bitField & 16842752) !== 0)) {\n            var reason = this._fulfillmentHandler0;\n            this._settlePromise0(this._rejectionHandler0, reason, bitField);\n            this._rejectPromises(len, reason);\n        } else {\n            var value = this._rejectionHandler0;\n            this._settlePromise0(this._fulfillmentHandler0, value, bitField);\n            this._fulfillPromises(len, value);\n        }\n        this._setLength(0);\n    }\n    this._clearCancellationData();\n};\n\nPromise.prototype._settledValue = function() {\n    var bitField = this._bitField;\n    if (((bitField & 33554432) !== 0)) {\n        return this._rejectionHandler0;\n    } else if (((bitField & 16777216) !== 0)) {\n        return this._fulfillmentHandler0;\n    }\n};\n\nfunction deferResolve(v) {this.promise._resolveCallback(v);}\nfunction deferReject(v) {this.promise._rejectCallback(v, false);}\n\nPromise.defer = Promise.pending = function() {\n    debug.deprecated(\"Promise.defer\", \"new Promise\");\n    var promise = new Promise(INTERNAL);\n    return {\n        promise: promise,\n        resolve: deferResolve,\n        reject: deferReject\n    };\n};\n\nutil.notEnumerableProp(Promise,\n                       \"_makeSelfResolutionError\",\n                       makeSelfResolutionError);\n\nrequire(\"./method\")(Promise, INTERNAL, tryConvertToPromise, apiRejection,\n    debug);\nrequire(\"./bind\")(Promise, INTERNAL, tryConvertToPromise, debug);\nrequire(\"./cancel\")(Promise, PromiseArray, apiRejection, debug);\nrequire(\"./direct_resolve\")(Promise);\nrequire(\"./synchronous_inspection\")(Promise);\nrequire(\"./join\")(\n    Promise, PromiseArray, tryConvertToPromise, INTERNAL, async, getDomain);\nPromise.Promise = Promise;\nPromise.version = \"3.4.7\";\nrequire('./map.js')(Promise, PromiseArray, apiRejection, tryConvertToPromise, INTERNAL, debug);\nrequire('./call_get.js')(Promise);\nrequire('./using.js')(Promise, apiRejection, tryConvertToPromise, createContext, INTERNAL, debug);\nrequire('./timers.js')(Promise, INTERNAL, debug);\nrequire('./generators.js')(Promise, apiRejection, INTERNAL, tryConvertToPromise, Proxyable, debug);\nrequire('./nodeify.js')(Promise);\nrequire('./promisify.js')(Promise, INTERNAL);\nrequire('./props.js')(Promise, PromiseArray, tryConvertToPromise, apiRejection);\nrequire('./race.js')(Promise, INTERNAL, tryConvertToPromise, apiRejection);\nrequire('./reduce.js')(Promise, PromiseArray, apiRejection, tryConvertToPromise, INTERNAL, debug);\nrequire('./settle.js')(Promise, PromiseArray, debug);\nrequire('./some.js')(Promise, PromiseArray, apiRejection);\nrequire('./filter.js')(Promise, INTERNAL);\nrequire('./each.js')(Promise, INTERNAL);\nrequire('./any.js')(Promise);\n                                                         \n    util.toFastProperties(Promise);                                          \n    util.toFastProperties(Promise.prototype);                                \n    function fillTypes(value) {                                              \n        var p = new Promise(INTERNAL);                                       \n        p._fulfillmentHandler0 = value;                                      \n        p._rejectionHandler0 = value;                                        \n        p._promise0 = value;                                                 \n        p._receiver0 = value;                                                \n    }                                                                        \n    // Complete slack tracking, opt out of field-type tracking and           \n    // stabilize map                                                         \n    fillTypes({a: 1});                                                       \n    fillTypes({b: 2});                                                       \n    fillTypes({c: 3});                                                       \n    fillTypes(1);                                                            \n    fillTypes(function(){});                                                 \n    fillTypes(undefined);                                                    \n    fillTypes(false);                                                        \n    fillTypes(new Promise(INTERNAL));                                        \n    debug.setBounds(Async.firstLineError, util.lastLineError);               \n    return Promise;                                                          \n\n};\n"], "names": [], "mappings": "AAkBkB;AAlBlB;AACA,OAAO,OAAO,GAAG;IACjB,IAAI,0BAA0B;QAC1B,OAAO,IAAI,UAAU;IACzB;IACA,IAAI,iBAAiB;QACjB,OAAO,IAAI,QAAQ,iBAAiB,CAAC,IAAI,CAAC,OAAO;IACrD;IACA,IAAI,eAAe,SAAS,GAAG;QAC3B,OAAO,QAAQ,MAAM,CAAC,IAAI,UAAU;IACxC;IACA,SAAS,aAAa;IACtB,IAAI,oBAAoB,CAAC;IACzB,IAAI;IAEJ,IAAI;IACJ,IAAI,KAAK,MAAM,EAAE;QACb,YAAY;YACR,IAAI,MAAM,gKAAA,CAAA,UAAO,CAAC,MAAM;YACxB,IAAI,QAAQ,WAAW,MAAM;YAC7B,OAAO;QACX;IACJ,OAAO;QACH,YAAY;YACR,OAAO;QACX;IACJ;IACA,KAAK,iBAAiB,CAAC,SAAS,cAAc;IAE9C,IAAI;IACJ,IAAI;IACJ,IAAI,QAAQ,IAAI;IAChB,IAAI,cAAc,CAAC,SAAS,UAAU;QAAC,OAAO;IAAK;IACnD,IAAI;IACJ,IAAI,YAAY,QAAQ,SAAS,GAAG,OAAO,SAAS;IACpD,QAAQ,UAAU,GAAG,OAAO,UAAU;IACtC,IAAI,oBAAoB,QAAQ,iBAAiB,GAAG,OAAO,iBAAiB;IAC5E,QAAQ,YAAY,GAAG,OAAO,YAAY;IAC1C,QAAQ,gBAAgB,GAAG,OAAO,gBAAgB;IAClD,QAAQ,cAAc,GAAG,OAAO,gBAAgB;IAChD,QAAQ,cAAc,GAAG,OAAO,cAAc;IAC9C,IAAI,WAAW,YAAW;IAC1B,IAAI,QAAQ,CAAC;IACb,IAAI,cAAc,CAAC;IACnB,IAAI,sBAAsB,6GAAuB,SAAS;IAC1D,IAAI,eACA,iHAA2B,SAAS,UACT,qBAAqB,cAAc;IAClE,IAAI,UAAU,2GAAqB;IAClC,qBAAqB,GACtB,IAAI,gBAAgB,QAAQ,MAAM;IAClC,IAAI,QAAQ,iHAA2B,SAAS;IAChD,IAAI,gBAAgB,MAAM,aAAa;IACvC,IAAI,4BACA,2GAAqB,SAAS;IAClC,IAAI,cAAc,gHAA0B;IAC5C,IAAI;IACJ,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAI,WAAW,KAAK,QAAQ;IAC5B,SAAS,MAAM,IAAI,EAAE,QAAQ;QACzB,IAAI,OAAO,aAAa,YAAY;YAChC,MAAM,IAAI,UAAU,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,IAAI,KAAK,WAAW,KAAK,SAAS;YAC9B,MAAM,IAAI,UAAU;QACxB;IACJ;IAEA,SAAS,QAAQ,QAAQ;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,aAAa,UAAU;YACvB,MAAM,IAAI,EAAE;YACZ,IAAI,CAAC,oBAAoB,CAAC;QAC9B;QACA,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI;IAC1C;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG;QACzB,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG,QAAQ,SAAS,CAAC,QAAQ,GAAG,SAAU,EAAE;QAChE,IAAI,MAAM,UAAU,MAAM;QAC1B,IAAI,MAAM,GAAG;YACT,IAAI,iBAAiB,IAAI,MAAM,MAAM,IACjC,IAAI,GAAG;YACX,IAAK,IAAI,GAAG,IAAI,MAAM,GAAG,EAAE,EAAG;gBAC1B,IAAI,OAAO,SAAS,CAAC,EAAE;gBACvB,IAAI,KAAK,QAAQ,CAAC,OAAO;oBACrB,cAAc,CAAC,IAAI,GAAG;gBAC1B,OAAO;oBACH,OAAO,aAAa,iCAChB,iCAAiC,KAAK,WAAW,CAAC;gBAC1D;YACJ;YACA,eAAe,MAAM,GAAG;YACxB,KAAK,SAAS,CAAC,EAAE;YACjB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,YAAY,gBAAgB,IAAI,IAAI;QACpE;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW;IAChC;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,gBACd,gBAAgB,WAAW,IAAI,EAAE;IACzC;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,UAAU,EAAE,SAAS;QACpD,IAAI,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,KACvC,OAAO,eAAe,cACtB,OAAO,cAAc,YAAY;YACjC,IAAI,MAAM,oDACF,KAAK,WAAW,CAAC;YACzB,IAAI,UAAU,MAAM,GAAG,GAAG;gBACtB,OAAO,OAAO,KAAK,WAAW,CAAC;YACnC;YACA,IAAI,CAAC,KAAK,CAAC;QACf;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,WAAW,WAAW,WAAW;IACnE;IAEA,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,UAAU,EAAE,SAAS;QACpD,IAAI,UACA,IAAI,CAAC,KAAK,CAAC,YAAY,WAAW,WAAW,WAAW;QAC5D,QAAQ,WAAW;IACvB;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE;QACnC,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO,aAAa,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,OAAO,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,WAAW,WAAW,OAAO;IAC7D;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG;QACvB,IAAI,MAAM;YACN,aAAa;YACb,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;QACrB;QACA,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK;YACjC,IAAI,WAAW,GAAG;QACtB,OAAO,IAAI,IAAI,CAAC,UAAU,IAAI;YAC1B,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,UAAU,GAAG;QACrB;QACA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,GAAG,GAAG;QACpB,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,IAAI,CAAC,KAAK,CAAC;QACf;QACA,OAAO,IAAI,aAAa,IAAI,EAAE,OAAO;IACzC;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAU,EAAE;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,uBAAuB,EAAE;IACrD;IAEA,QAAQ,iBAAiB,GAAG,OAAO,OAAO;IAE1C,QAAQ,EAAE,GAAG,SAAU,GAAG;QACtB,OAAO,eAAe;IAC1B;IAEA,QAAQ,QAAQ,GAAG,QAAQ,YAAY,GAAG,SAAS,EAAE;QACjD,IAAI,MAAM,IAAI,QAAQ;QACtB,IAAI,kBAAkB;QACtB,IAAI,YAAY,UAAU,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,SAAS,CAAC,EAAE,EAAE,SAAS,GAChC;QACvC,IAAI,SAAS,SAAS,IAAI,mBAAmB,KAAK;QAClD,IAAI,WAAW,UAAU;YACrB,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;QAClC;QACA,IAAI,CAAC,IAAI,aAAa,IAAI,IAAI,mBAAmB;QACjD,OAAO;IACX;IAEA,QAAQ,GAAG,GAAG,SAAU,QAAQ;QAC5B,OAAO,IAAI,aAAa,UAAU,OAAO;IAC7C;IAEA,QAAQ,IAAI,GAAG,SAAU,GAAG;QACxB,IAAI,MAAM,oBAAoB;QAC9B,IAAI,CAAC,CAAC,eAAe,OAAO,GAAG;YAC3B,MAAM,IAAI,QAAQ;YAClB,IAAI,kBAAkB;YACtB,IAAI,aAAa;YACjB,IAAI,kBAAkB,GAAG;QAC7B;QACA,OAAO;IACX;IAEA,QAAQ,OAAO,GAAG,QAAQ,SAAS,GAAG,QAAQ,IAAI;IAElD,QAAQ,MAAM,GAAG,QAAQ,QAAQ,GAAG,SAAU,MAAM;QAChD,IAAI,MAAM,IAAI,QAAQ;QACtB,IAAI,kBAAkB;QACtB,IAAI,eAAe,CAAC,QAAQ;QAC5B,OAAO;IACX;IAEA,QAAQ,YAAY,GAAG,SAAS,EAAE;QAC9B,IAAI,OAAO,OAAO,YAAY;YAC1B,MAAM,IAAI,UAAU,kCAAkC,KAAK,WAAW,CAAC;QAC3E;QACA,OAAO,MAAM,YAAY,CAAC;IAC9B;IAEA,QAAQ,SAAS,CAAC,KAAK,GAAG,SACtB,UAAU,EACV,SAAS,EACT,CAAC,EAAK,QAAQ,EACd,YAAY;QAEZ,IAAI,mBAAmB,iBAAiB;QACxC,IAAI,UAAU,mBAAmB,eAAe,IAAI,QAAQ;QAC5D,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,WAAW,OAAO,SAAS;QAE/B,IAAI,CAAC,kBAAkB;YACnB,QAAQ,cAAc,CAAC,IAAI,EAAE;YAC7B,QAAQ,kBAAkB;YAC1B,IAAI,aAAa,aACZ,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,GAAI;gBACpC,IAAI,CAAC,CAAC,CAAC,WAAW,QAAQ,MAAM,CAAC,GAAG;oBAChC,WAAW,IAAI,CAAC,WAAW;gBAC/B,OAAO;oBACH,WAAW,WAAW,IAAI,GAAG,YAAY,IAAI,CAAC,QAAQ;gBAC1D;YACJ;YACA,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,EAAE;QAC5C;QAEA,IAAI,SAAS;QACb,IAAI,CAAC,CAAC,CAAC,WAAW,QAAQ,MAAM,CAAC,GAAG;YAChC,IAAI,SAAS,OAAO,UAAU,OAAO,iBAAiB;YACtD,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBAC/B,QAAQ,OAAO,kBAAkB;gBACjC,UAAU;YACd,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBACtC,QAAQ,OAAO,oBAAoB;gBACnC,UAAU;gBACV,OAAO,0BAA0B;YACrC,OAAO;gBACH,UAAU,OAAO,sCAAsC;gBACvD,QAAQ,IAAI,kBAAkB;gBAC9B,OAAO,iBAAiB,CAAC;gBACzB,UAAU;YACd;YAEA,MAAM,MAAM,CAAC,SAAS,QAAQ;gBAC1B,SAAS,WAAW,OAAO,UACpB,OAAO,YAAY,cAClB,KAAK,UAAU,CAAC,QAAQ;gBAChC,SAAS;gBACT,UAAU;gBACV,OAAO;YACX;QACJ,OAAO;YACH,OAAO,aAAa,CAAC,YAAY,WAAW,SAAS,UAAU;QACnE;QAEA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG;QACxB,OAAO,IAAI,CAAC,SAAS,GAAG;IAC5B;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,MAAM;IAC5C;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG;QAC7B,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,MAAM;IAC3C;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG;QACxC,IAAI,CAAC,SAAS,GAAG,AAAC,IAAI,CAAC,SAAS,GAAG,CAAC,QAC/B,MAAM;IACf;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;QAClC,IAAI,CAAC,UAAU,CAAC,oBAAoB,IAAI;IAC5C;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;QAClC,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI;IAC3C;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;QAClC,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI;IAC3C;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG;QACzB,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI;IACxC;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAI,CAAC;IACxC;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;QAClC,IAAI,CAAC,UAAU,CAAC,oBAAoB,IAAI;IAC5C;IAEA,QAAQ,SAAS,CAAC,mBAAmB,GAAG;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,mBAAmB,GAAG;QACpC,IAAI,MAAM,kBAAkB,IAAI;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;IACtC;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,SAAU,KAAK;QAC3C,IAAI,MAAM,UAAU,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CACtC,QAAQ,IAAI,IAAI,EAAE;QAC1B,IAAI,QAAQ,mBAAmB;YAC3B,OAAO;QACX,OAAO,IAAI,QAAQ,aAAa,IAAI,CAAC,QAAQ,IAAI;YAC7C,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK;QAC1C,OAAO,IAAI,CACH,QAAQ,IAAI,IAAI,EAAE;IAC9B;IAEA,QAAQ,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK;QACrD,OAAO,IAAI,CACH,QAAQ,IAAI,IAAI,EAAE;IAC9B;IAEA,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAU,KAAK;QACnD,OAAO,IAAI,CACH,QAAQ,IAAI,IAAI,EAAE;IAC9B;IAEA,QAAQ,SAAS,CAAC,WAAW,GAAG,YAAY;IAE5C,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAU,QAAQ;QACpD,IAAI,WAAW,SAAS,SAAS;QACjC,IAAI,UAAU,SAAS,oBAAoB;QAC3C,IAAI,SAAS,SAAS,kBAAkB;QACxC,IAAI,UAAU,SAAS,SAAS;QAChC,IAAI,WAAW,SAAS,WAAW,CAAC;QACpC,IAAI,aAAa,WAAW,WAAW;QACvC,IAAI,CAAC,aAAa,CAAC,SAAS,QAAQ,SAAS,UAAU;IAC3D;IAEA,QAAQ,SAAS,CAAC,kBAAkB,GAAG,SAAU,QAAQ,EAAE,KAAK;QAC5D,IAAI,UAAU,SAAS,qBAAqB,CAAC;QAC7C,IAAI,SAAS,SAAS,mBAAmB,CAAC;QAC1C,IAAI,UAAU,SAAS,UAAU,CAAC;QAClC,IAAI,WAAW,SAAS,WAAW,CAAC;QACpC,IAAI,aAAa,WAAW,WAAW;QACvC,IAAI,CAAC,aAAa,CAAC,SAAS,QAAQ,SAAS,UAAU;IAC3D;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG,SAC9B,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM;QAEN,IAAI,QAAQ,IAAI,CAAC,OAAO;QAExB,IAAI,SAAS,QAAQ,GAAG;YACpB,QAAQ;YACR,IAAI,CAAC,UAAU,CAAC;QACpB;QAEA,IAAI,UAAU,GAAG;YACb,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,OAAO,YAAY,YAAY;gBAC/B,IAAI,CAAC,oBAAoB,GACrB,WAAW,OAAO,UAAU,KAAK,UAAU,CAAC,QAAQ;YAC5D;YACA,IAAI,OAAO,WAAW,YAAY;gBAC9B,IAAI,CAAC,kBAAkB,GACnB,WAAW,OAAO,SAAS,KAAK,UAAU,CAAC,QAAQ;YAC3D;QACJ,OAAO;YACH,IAAI,OAAO,QAAQ,IAAI;YACvB,IAAI,CAAC,OAAO,EAAE,GAAG;YACjB,IAAI,CAAC,OAAO,EAAE,GAAG;YACjB,IAAI,OAAO,YAAY,YAAY;gBAC/B,IAAI,CAAC,OAAO,EAAE,GACV,WAAW,OAAO,UAAU,KAAK,UAAU,CAAC,QAAQ;YAC5D;YACA,IAAI,OAAO,WAAW,YAAY;gBAC9B,IAAI,CAAC,OAAO,EAAE,GACV,WAAW,OAAO,SAAS,KAAK,UAAU,CAAC,QAAQ;YAC3D;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,QAAQ;QACxB,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,GAAG;QAC/C,IAAI,CAAC,aAAa,CAAC,WAAW,WAAW,KAAK,WAAW;IAC7D;IAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,UAAU;QAC3D,IAAK,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,MAAM,GAAI;QAC1C,IAAI,UAAU,IAAI,EACd,OAAO,IAAI,CAAC,eAAe,CAAC,2BAA2B;QAC3D,IAAI,eAAe,oBAAoB,OAAO,IAAI;QAClD,IAAI,CAAC,CAAC,wBAAwB,OAAO,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC;QAE7D,IAAI,YAAY,IAAI,CAAC,cAAc,CAAC,cAAc;QAElD,IAAI,UAAU,aAAa,OAAO;QAElC,IAAI,YAAY,IAAI,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC;YACb;QACJ;QAEA,IAAI,WAAW,QAAQ,SAAS;QAChC,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;YAC/B,IAAI,MAAM,IAAI,CAAC,OAAO;YACtB,IAAI,MAAM,GAAG,QAAQ,iBAAiB,CAAC,IAAI;YAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;gBAC1B,QAAQ,kBAAkB,CAAC,IAAI,EAAE;YACrC;YACA,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,YAAY,CAAC;QACtB,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;YACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM;QAChC,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;YACtC,IAAI,CAAC,OAAO,CAAC,QAAQ,OAAO;QAChC,OAAO;YACH,IAAI,SAAS,IAAI,kBAAkB;YACnC,QAAQ,iBAAiB,CAAC;YAC1B,IAAI,CAAC,OAAO,CAAC;QACjB;IACJ;IAEA,QAAQ,SAAS,CAAC,eAAe,GACjC,SAAS,MAAM,EAAE,WAAW,EAAE,sBAAsB;QAChD,IAAI,QAAQ,KAAK,iBAAiB,CAAC;QACnC,IAAI,WAAW,UAAU;QACzB,IAAI,CAAC,YAAY,CAAC,0BAA0B,MAAM,QAAQ,IAAI;YAC1D,IAAI,UAAU,8CACV,KAAK,WAAW,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,SAAS;QACxB;QACA,IAAI,CAAC,iBAAiB,CAAC,OAAO,cAAc,WAAW;QACvD,IAAI,CAAC,OAAO,CAAC;IACjB;IAEA,QAAQ,SAAS,CAAC,oBAAoB,GAAG,SAAU,QAAQ;QACvD,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,YAAY;QACjB,IAAI,cAAc;QAClB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,SAAS,KAAK;YAC1C,QAAQ,gBAAgB,CAAC;QAC7B,GAAG,SAAU,MAAM;YACf,QAAQ,eAAe,CAAC,QAAQ;QACpC;QACA,cAAc;QACd,IAAI,CAAC,WAAW;QAEhB,IAAI,MAAM,WAAW;YACjB,QAAQ,eAAe,CAAC,GAAG;QAC/B;IACJ;IAEA,QAAQ,SAAS,CAAC,yBAAyB,GAAG,SAC1C,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO;QAEjC,IAAI,WAAW,QAAQ,SAAS;QAChC,IAAK,CAAC,WAAW,KAAK,MAAM,GAAI;QAChC,QAAQ,YAAY;QACpB,IAAI;QACJ,IAAI,aAAa,OAAO;YACpB,IAAI,CAAC,SAAS,OAAO,MAAM,MAAM,KAAK,UAAU;gBAC5C,IAAI;gBACJ,EAAE,CAAC,GAAG,IAAI,UAAU,mCACI,KAAK,WAAW,CAAC;YAC7C,OAAO;gBACH,IAAI,SAAS,SAAS,KAAK,CAAC,IAAI,CAAC,WAAW,IAAI;YACpD;QACJ,OAAO;YACH,IAAI,SAAS,SAAS,IAAI,CAAC,UAAU;QACzC;QACA,IAAI,iBAAiB,QAAQ,WAAW;QACxC,WAAW,QAAQ,SAAS;QAC5B,IAAK,CAAC,WAAW,KAAK,MAAM,GAAI;QAEhC,IAAI,MAAM,aAAa;YACnB,QAAQ,OAAO,CAAC;QACpB,OAAO,IAAI,MAAM,UAAU;YACvB,QAAQ,eAAe,CAAC,EAAE,CAAC,EAAE;QACjC,OAAO;YACH,MAAM,qBAAqB,CAAC,GAAG,gBAAgB,IAAK,SAAS,IAAI;YACjE,QAAQ,gBAAgB,CAAC;QAC7B;IACJ;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG;QACxB,IAAI,MAAM,IAAI;QACd,MAAO,IAAI,YAAY,GAAI,MAAM,IAAI,SAAS;QAC9C,OAAO;IACX;IAEA,QAAQ,SAAS,CAAC,SAAS,GAAG;QAC1B,OAAO,IAAI,CAAC,kBAAkB;IAClC;IAEA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,OAAO;QAC7C,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IAEA,QAAQ,SAAS,CAAC,cAAc,GAAG,SAAS,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK;QACzE,IAAI,YAAY,mBAAmB;QACnC,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,kBAAmB,CAAC,WAAW,SAAS,MAAM;QAClD,IAAK,CAAC,WAAW,KAAK,MAAM,GAAI;YAC5B,IAAI,WAAW,QAAQ,uBAAuB;YAE9C,IAAI,oBAAoB,6BACpB,SAAS,gBAAgB,IAAI;gBAC7B,SAAS,aAAa,GAAG;gBACzB,IAAI,SAAS,SAAS,IAAI,CAAC,UAAU,WAAW,UAAU;oBACtD,QAAQ,OAAO,CAAC,SAAS,CAAC;gBAC9B;YACJ,OAAO,IAAI,YAAY,gBAAgB;gBACnC,QAAQ,QAAQ,CAAC,eAAe,IAAI,CAAC;YACzC,OAAO,IAAI,oBAAoB,WAAW;gBACtC,SAAS,iBAAiB,CAAC;YAC/B,OAAO,IAAI,aAAa,mBAAmB,cAAc;gBACrD,QAAQ,OAAO;YACnB,OAAO;gBACH,SAAS,MAAM;YACnB;QACJ,OAAO,IAAI,OAAO,YAAY,YAAY;YACtC,IAAI,CAAC,WAAW;gBACZ,QAAQ,IAAI,CAAC,UAAU,OAAO;YAClC,OAAO;gBACH,IAAI,iBAAiB,QAAQ,mBAAmB;gBAChD,IAAI,CAAC,yBAAyB,CAAC,SAAS,UAAU,OAAO;YAC7D;QACJ,OAAO,IAAI,oBAAoB,WAAW;YACtC,IAAI,CAAC,SAAS,WAAW,IAAI;gBACzB,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;oBAC/B,SAAS,iBAAiB,CAAC,OAAO;gBACtC,OAAO;oBACH,SAAS,gBAAgB,CAAC,OAAO;gBACrC;YACJ;QACJ,OAAO,IAAI,WAAW;YAClB,IAAI,iBAAiB,QAAQ,mBAAmB;YAChD,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBAC/B,QAAQ,QAAQ,CAAC;YACrB,OAAO;gBACH,QAAQ,OAAO,CAAC;YACpB;QACJ;IACJ;IAEA,QAAQ,SAAS,CAAC,sCAAsC,GAAG,SAAS,GAAG;QACnE,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,WAAW,IAAI,QAAQ;QAC3B,IAAI,QAAQ,IAAI,KAAK;QACrB,IAAI,OAAO,YAAY,YAAY;YAC/B,IAAI,CAAC,CAAC,mBAAmB,OAAO,GAAG;gBAC/B,QAAQ,IAAI,CAAC,UAAU,OAAO;YAClC,OAAO;gBACH,IAAI,CAAC,yBAAyB,CAAC,SAAS,UAAU,OAAO;YAC7D;QACJ,OAAO,IAAI,mBAAmB,SAAS;YACnC,QAAQ,OAAO,CAAC;QACpB;IACJ;IAEA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAS,GAAG;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,IAAI,QAAQ,EAAE,IAAI,KAAK;IACzE;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ;QACjE,IAAI,UAAU,IAAI,CAAC,SAAS;QAC5B,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,CAAC,SAAS,SAAS,UAAU;IACpD;IAEA,QAAQ,SAAS,CAAC,yBAAyB,GAAG,SAAS,KAAK;QACxD,IAAI,OAAO,QAAQ,IAAI;QACvB,IAAI,CAAC,OAAO,EAAE,GACd,IAAI,CAAC,OAAO,EAAE,GACd,IAAI,CAAC,OAAO,EAAE,GACd,IAAI,CAAC,OAAO,EAAE,GAAG;IACrB;IAEA,QAAQ,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;QACxC,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAK,CAAC,WAAW,SAAS,MAAM,IAAK;QACrC,IAAI,UAAU,IAAI,EAAE;YAChB,IAAI,MAAM;YACV,IAAI,CAAC,iBAAiB,CAAC;YACvB,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,kBAAkB,GAAG;QAE1B,IAAI,CAAC,WAAW,KAAK,IAAI,GAAG;YACxB,IAAK,CAAC,WAAW,SAAS,MAAM,GAAI;gBAChC,IAAI,CAAC,eAAe;YACxB,OAAO;gBACH,MAAM,cAAc,CAAC,IAAI;YAC7B;QACJ;IACJ;IAEA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAU,MAAM;QACxC,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAK,CAAC,WAAW,SAAS,MAAM,IAAK;QACrC,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,oBAAoB,GAAG;QAE5B,IAAI,IAAI,CAAC,QAAQ,IAAI;YACjB,OAAO,MAAM,UAAU,CAAC,QAAQ,KAAK,MAAM;QAC/C;QAEA,IAAI,CAAC,WAAW,KAAK,IAAI,GAAG;YACxB,MAAM,cAAc,CAAC,IAAI;QAC7B,OAAO;YACH,IAAI,CAAC,+BAA+B;QACxC;IACJ;IAEA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,KAAK;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,UAAU,IAAI,CAAC,qBAAqB,CAAC;YACzC,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC;YAC9B,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;YAChC,IAAI,CAAC,yBAAyB,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,SAAS,SAAS,UAAU;QACpD;IACJ;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG,SAAU,GAAG,EAAE,MAAM;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC;YACvC,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC;YAC9B,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC;YAChC,IAAI,CAAC,yBAAyB,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,SAAS,SAAS,UAAU;QACpD;IACJ;IAEA,QAAQ,SAAS,CAAC,eAAe,GAAG;QAChC,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAI,MAAO,WAAW;QAEtB,IAAI,MAAM,GAAG;YACT,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;gBAC/B,IAAI,SAAS,IAAI,CAAC,oBAAoB;gBACtC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ;gBACtD,IAAI,CAAC,eAAe,CAAC,KAAK;YAC9B,OAAO;gBACH,IAAI,QAAQ,IAAI,CAAC,kBAAkB;gBACnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO;gBACvD,IAAI,CAAC,gBAAgB,CAAC,KAAK;YAC/B;YACA,IAAI,CAAC,UAAU,CAAC;QACpB;QACA,IAAI,CAAC,sBAAsB;IAC/B;IAEA,QAAQ,SAAS,CAAC,aAAa,GAAG;QAC9B,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;YAC/B,OAAO,IAAI,CAAC,kBAAkB;QAClC,OAAO,IAAK,CAAC,WAAW,QAAQ,MAAM,GAAI;YACtC,OAAO,IAAI,CAAC,oBAAoB;QACpC;IACJ;IAEA,SAAS,aAAa,CAAC;QAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;IAAG;IAC3D,SAAS,YAAY,CAAC;QAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG;IAAO;IAEhE,QAAQ,KAAK,GAAG,QAAQ,OAAO,GAAG;QAC9B,MAAM,UAAU,CAAC,iBAAiB;QAClC,IAAI,UAAU,IAAI,QAAQ;QAC1B,OAAO;YACH,SAAS;YACT,SAAS;YACT,QAAQ;QACZ;IACJ;IAEA,KAAK,iBAAiB,CAAC,SACA,4BACA;IAEvB,0GAAoB,SAAS,UAAU,qBAAqB,cACxD;IACJ,wGAAkB,SAAS,UAAU,qBAAqB;IAC1D,0GAAoB,SAAS,cAAc,cAAc;IACzD,kHAA4B;IAC5B,0HAAoC;IACpC,wGACI,SAAS,cAAc,qBAAqB,UAAU,OAAO;IACjE,QAAQ,OAAO,GAAG;IAClB,QAAQ,OAAO,GAAG;IAClB,uGAAoB,SAAS,cAAc,cAAc,qBAAqB,UAAU;IACxF,4GAAyB;IACzB,yGAAsB,SAAS,cAAc,qBAAqB,eAAe,UAAU;IAC3F,0GAAuB,SAAS,UAAU;IAC1C,8GAA2B,SAAS,cAAc,UAAU,qBAAqB,WAAW;IAC5F,2GAAwB;IACxB,6GAA0B,SAAS;IACnC,yGAAsB,SAAS,cAAc,qBAAqB;IAClE,wGAAqB,SAAS,UAAU,qBAAqB;IAC7D,0GAAuB,SAAS,cAAc,cAAc,qBAAqB,UAAU;IAC3F,0GAAuB,SAAS,cAAc;IAC9C,wGAAqB,SAAS,cAAc;IAC5C,0GAAuB,SAAS;IAChC,wGAAqB,SAAS;IAC9B,uGAAoB;IAEhB,KAAK,gBAAgB,CAAC;IACtB,KAAK,gBAAgB,CAAC,QAAQ,SAAS;IACvC,SAAS,UAAU,KAAK;QACpB,IAAI,IAAI,IAAI,QAAQ;QACpB,EAAE,oBAAoB,GAAG;QACzB,EAAE,kBAAkB,GAAG;QACvB,EAAE,SAAS,GAAG;QACd,EAAE,UAAU,GAAG;IACnB;IACA,yEAAyE;IACzE,yEAAyE;IACzE,UAAU;QAAC,GAAG;IAAC;IACf,UAAU;QAAC,GAAG;IAAC;IACf,UAAU;QAAC,GAAG;IAAC;IACf,UAAU;IACV,UAAU,YAAW;IACrB,UAAU;IACV,UAAU;IACV,UAAU,IAAI,QAAQ;IACtB,MAAM,SAAS,CAAC,MAAM,cAAc,EAAE,KAAK,aAAa;IACxD,OAAO;AAEX", "ignoreList": [0], "debugId": null}}]}