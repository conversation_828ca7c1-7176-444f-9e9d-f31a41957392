const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoI2VFormatFix() {
  try {
    console.log('🔧 测试豆包图生视频API格式修复（参考T2V格式）...\n');
    
    // 1. 检查两个模型的配置
    const t2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013223-bwdtj'
      }
    });
    
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    console.log('📊 模型配置对比:');
    
    if (t2vModel) {
      console.log('✅ T2V模型 (文生视频):');
      console.log(`   端点: ${t2vModel.model}`);
      console.log(`   启用: ${t2vModel.enabled}`);
      console.log(`   API密钥: ${t2vModel.apiKey ? '已配置' : '未配置'}`);
    }
    
    if (i2vModel) {
      console.log('✅ I2V模型 (图生视频):');
      console.log(`   端点: ${i2vModel.model}`);
      console.log(`   启用: ${i2vModel.enabled}`);
      console.log(`   API密钥: ${i2vModel.apiKey ? '已配置' : '未配置'}`);
    }
    
    if (!i2vModel || !i2vModel.enabled || !i2vModel.apiKey) {
      console.log('⚠️ I2V模型未正确配置，无法测试');
      return;
    }
    
    // 2. 获取角色参考图像
    console.log('\n🖼️ 获取角色参考图像...');
    
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 1
    });
    
    if (charactersWithImages.length === 0) {
      console.log('❌ 没有找到有参考图像的角色');
      return;
    }
    
    const character = charactersWithImages[0];
    let referenceImageUrl = null;
    
    try {
      const images = JSON.parse(character.generatedImages);
      referenceImageUrl = images.front || images.side || images.back;
    } catch (e) {
      console.log('❌ 解析角色图像失败');
      return;
    }
    
    if (!referenceImageUrl) {
      console.log('❌ 角色没有可用的参考图像');
      return;
    }
    
    console.log(`✅ 找到角色: ${character.name}`);
    console.log(`📸 参考图像: 有`);
    
    // 3. 对比API调用格式
    console.log('\n📋 API调用格式对比:');
    
    const testPrompt = '角色在雪夜中缓缓转身，表情严肃警惕';
    
    // T2V格式（已知正确）
    const t2vContent = [
      {
        type: "text",
        text: testPrompt
      }
    ];
    
    // I2V格式（修复后）
    const i2vContent = [
      {
        type: "text",
        text: testPrompt
      },
      {
        type: "image_url",
        image_url: {
          url: referenceImageUrl
        }
      }
    ];
    
    console.log('T2V格式 (已知正确):');
    console.log(JSON.stringify(t2vContent, null, 2));
    
    console.log('\nI2V格式 (修复后):');
    console.log(JSON.stringify(i2vContent, null, 2));
    
    // 4. 测试I2V API调用
    console.log('\n📡 测试I2V API调用...');
    
    try {
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${i2vModel.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: i2vModel.model,
          content: i2vContent
        })
      });
      
      console.log(`📊 API响应状态: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ I2V API调用成功!');
        console.log('📋 响应数据:', JSON.stringify(result, null, 2));
        
        const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
        if (taskId) {
          console.log(`🎯 任务ID: ${taskId}`);
          console.log('✅ 图生视频任务创建成功！');
        }
        
      } else {
        const errorText = await response.text();
        console.log('❌ I2V API调用失败:');
        console.log(`   状态码: ${response.status}`);
        console.log(`   错误信息: ${errorText}`);
        
        // 如果还是同样的错误，说明需要其他格式
        if (errorText.includes('image to video models require image in content')) {
          console.log('\n🔍 错误分析: 仍然是内容格式问题');
          console.log('💡 可能需要尝试其他格式组合');
          
          // 尝试其他可能的格式
          console.log('\n🧪 尝试其他可能的格式...');
          
          // 格式1: 只有图像
          const format1 = [
            {
              type: "image_url",
              image_url: {
                url: referenceImageUrl
              }
            }
          ];
          
          console.log('格式1 (只有图像):');
          console.log(JSON.stringify(format1, null, 2));
          
          // 格式2: 图像在前
          const format2 = [
            {
              type: "image_url",
              image_url: {
                url: referenceImageUrl
              }
            },
            {
              type: "text",
              text: testPrompt
            }
          ];
          
          console.log('\n格式2 (图像在前):');
          console.log(JSON.stringify(format2, null, 2));
          
        }
      }
      
    } catch (error) {
      console.error('❌ API调用异常:', error.message);
    }
    
    // 5. 建议下一步
    console.log('\n💡 下一步建议:');
    console.log('   1. 如果仍然失败，可能需要查看豆包官方文档');
    console.log('   2. 或者联系豆包技术支持确认I2V模型的正确格式');
    console.log('   3. 也可以暂时使用T2V模型，它已经可以正常工作');
    console.log('   4. 考虑在T2V模型中添加图像作为参考（如现有的双重约束系统）');
    
    console.log('\n🎬 豆包图生视频格式修复测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoI2VFormatFix();
