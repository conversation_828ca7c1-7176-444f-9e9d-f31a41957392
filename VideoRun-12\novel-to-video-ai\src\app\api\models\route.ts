import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

interface ModelConfig {
  id: string
  provider: string
  model: string
  apiKey: string
  enabled: boolean
  name: string
  description?: string
  supportsVideo?: boolean
  supportsImage?: boolean
  supportsImageToVideo?: boolean
  temperature?: number
  maxTokens?: number
  topP?: number
}

// GET - 获取所有模型配置
export async function GET() {
  try {
    const aiConfigs = await prisma.aIConfig.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log('获取到的AI配置:', aiConfigs)

    return NextResponse.json({
      success: true,
      data: aiConfigs
    })
  } catch (error) {
    console.error('获取模型列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取模型列表失败'
      },
      { status: 500 }
    )
  }
}

// POST - 保存或更新模型配置
export async function POST(request: NextRequest) {
  try {
    const modelConfig: ModelConfig = await request.json()

    // 验证必需字段
    if (!modelConfig.provider || !modelConfig.model || !modelConfig.name) {
      return NextResponse.json(
        { success: false, error: '缺少必需字段' },
        { status: 400 }
      )
    }

    let savedConfig

    if (modelConfig.id) {
      // 更新现有配置
      savedConfig = await prisma.aIConfig.update({
        where: { id: modelConfig.id },
        data: {
          provider: modelConfig.provider,
          model: modelConfig.model,
          name: modelConfig.name,
          description: modelConfig.description,
          apiKey: modelConfig.apiKey,
          enabled: modelConfig.enabled,
          supportsVideo: modelConfig.supportsVideo ?? false,
          supportsImage: modelConfig.supportsImage ?? false,
          supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,
          temperature: modelConfig.temperature ?? 0.7,
          status: 'disconnected' // 重置状态，需要重新测试
        }
      })
    } else {
      // 创建新配置
      savedConfig = await prisma.aIConfig.create({
        data: {
          provider: modelConfig.provider,
          model: modelConfig.model,
          name: modelConfig.name,
          description: modelConfig.description || '',
          apiKey: modelConfig.apiKey,
          enabled: modelConfig.enabled ?? true,
          supportsVideo: modelConfig.supportsVideo ?? false,
          supportsImage: modelConfig.supportsImage ?? false,
          supportsImageToVideo: modelConfig.supportsImageToVideo ?? false,
          temperature: modelConfig.temperature ?? 0.7,
          status: 'disconnected'
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: savedConfig
    })
  } catch (error) {
    console.error('保存模型配置失败:', error)
    return NextResponse.json(
      { success: false, error: '保存模型配置失败' },
      { status: 500 }
    )
  }
}
