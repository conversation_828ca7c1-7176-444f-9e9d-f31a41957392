'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  Upload,
  Users,
  Film,
  Video,
  FileText,
  Calendar,
  AlertCircle,
  CheckCircle,
  Play,
  BookOpen,
  ChevronDown,
  X
} from 'lucide-react'
import ModelSelector from '@/components/ModelSelector'
import Layout from '@/components/Layout'
import ProjectFileUpload from '@/components/ProjectFileUpload'
import CharacterManager from '@/components/CharacterManager'
import EpisodeManager from '@/components/EpisodeManager'
import DetailedPlotExtraction from '@/components/DetailedPlotExtraction'
import VideoSegmentViewer from '@/components/VideoSegmentViewer'
import EpisodeVideoCard from '@/components/EpisodeVideoCard'
import ConsistencyVideoGenerator from '@/components/ConsistencyVideoGenerator'
import { Project, Character, Episode } from '@/types'

type TabType = 'upload' | 'novel' | 'characters' | 'episodes' | 'videos'

export default function ProjectDetailPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const projectId = params.id as string
  
  const [project, setProject] = useState<Project | null>(null)
  const [characters, setCharacters] = useState<Character[]>([])
  const [episodes, setEpisodes] = useState<Episode[]>([])

  // 从URL参数中获取当前标签页，如果没有则默认为'upload'
  const getInitialTab = (): TabType => {
    const tab = searchParams.get('tab') as TabType
    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']
    return validTabs.includes(tab) ? tab : 'upload'
  }

  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab())
  const [loading, setLoading] = useState(true)

  // 更新标签页并同时更新URL
  const updateActiveTab = (tab: TabType) => {
    setActiveTab(tab)
    const newSearchParams = new URLSearchParams(searchParams.toString())
    newSearchParams.set('tab', tab)
    router.replace(`/projects/${projectId}?${newSearchParams.toString()}`, { scroll: false })
  }
  const [error, setError] = useState<string | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [selectedModel, setSelectedModel] = useState<string>('')

  // 增强提示词相关状态
  const [showPromptDropdown, setShowPromptDropdown] = useState(false)
  const [enhancePrompt, setEnhancePrompt] = useState('')
  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')

  // 详细剧情提取相关状态
  const [showDetailedPlotExtraction, setShowDetailedPlotExtraction] = useState(false)
  const [currentEpisodeId, setCurrentEpisodeId] = useState('')
  const [currentEpisodeTitle, setCurrentEpisodeTitle] = useState('')
  const [currentEpisodeContent, setCurrentEpisodeContent] = useState('')

  // 视频片段查看器相关状态
  const [showVideoSegmentViewer, setShowVideoSegmentViewer] = useState(false)
  const [videoEpisodeId, setVideoEpisodeId] = useState('')

  // 视频生成模式：'auto' | 'manual'，默认为手动模式
  const [videoGenerationMode, setVideoGenerationMode] = useState<'auto' | 'manual'>('manual')

  // 一致性视频生成器相关状态
  const [showConsistencyVideoGenerator, setShowConsistencyVideoGenerator] = useState(false)
  const [consistencyEpisodeId, setConsistencyEpisodeId] = useState('')
  const [consistencyEpisodeTitle, setConsistencyEpisodeTitle] = useState('')

  // 文件上传成功处理
  const handleUploadSuccess = (updatedProject: any) => {
    setProject(updatedProject.project)
    setCharacters(updatedProject.characters || [])
    setEpisodes(updatedProject.episodes || [])
    updateActiveTab('characters')
  }

  // 文件上传错误处理
  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage)
  }

  // 剧情分析处理
  const handleAnalyzePlot = async (episodeId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/analyze-plot`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        // 更新剧集状态
        setEpisodes(prev =>
          prev.map(ep =>
            ep.id === episodeId
              ? { ...ep, plotInfo: data.data }
              : ep
          )
        )
      } else {
        throw new Error(data.error || '剧情分析失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '剧情分析失败')
    }
  }

  // 视频生成处理
  const handleGenerateVideo = async (episodeId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/episodes/${episodeId}/generate-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        // 更新剧集状态
        setEpisodes(prev =>
          prev.map(ep =>
            ep.id === episodeId
              ? { ...ep, status: 'video_generated' }
              : ep
          )
        )
        // 可以在这里显示视频脚本模态框
      } else {
        throw new Error(data.error || '视频生成失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '视频生成失败')
    }
  }

  // 提取详细剧情信息处理
  const handleExtractDetailedPlot = (episodeId: string) => {
    const episode = episodes.find(ep => ep.id === episodeId)
    if (episode) {
      setCurrentEpisodeId(episodeId)
      setCurrentEpisodeTitle(episode.title)
      setCurrentEpisodeContent(episode.content)
      setShowDetailedPlotExtraction(true)
    }
  }

  // 打开一致性视频生成器
  const handleOpenConsistencyVideoGenerator = (episodeId: string) => {
    const episode = episodes.find(ep => ep.id === episodeId)
    if (episode) {
      setConsistencyEpisodeId(episodeId)
      setConsistencyEpisodeTitle(episode.title)
      setShowConsistencyVideoGenerator(true)
    }
  }

  // 一致性视频生成成功处理
  const handleConsistencyVideoGenerated = (videoData: any) => {
    console.log('✅ 一致性视频生成成功:', videoData)
    // 可以在这里更新UI状态或显示成功消息
    alert(`一致性视频生成成功！\n生成ID: ${videoData.generationId}\n角色数量: ${videoData.consistencyInfo.characterCount}\n参考图像: ${videoData.consistencyInfo.referenceImageCount} 个`)
  }

  // 生成剧情视频处理
  const handleGenerateStoryVideo = async (episodeId: string, prompt: string, modelId?: string) => {
    try {
      console.log('🎬 开始生成剧情视频，episodeId:', episodeId)

      // 首先检查是否已有视频生成记录
      const checkResponse = await fetch(`/api/ai/video-segments?episodeId=${episodeId}`)
      const checkData = await checkResponse.json()

      console.log('📊 检查现有片段结果:', checkData)

      if (checkData.success && checkData.data && checkData.data.segments.length > 0) {
        console.log(`⚠️ 发现现有片段 ${checkData.data.segments.length} 个，显示确认对话框`)

        // 如果已有视频记录，显示确认对话框
        const confirmed = confirm(
          `检测到该剧集已有 ${checkData.data.segments.length} 个视频片段。\n\n` +
          `重新生成将会：\n` +
          `• 删除所有现有的视频片段\n` +
          `• 停止正在进行的生成任务\n` +
          `• 重新开始生成新的视频片段\n\n` +
          `确定要继续吗？`
        )

        console.log('👤 用户确认结果:', confirmed)

        if (!confirmed) {
          console.log('❌ 用户取消操作')
          return // 用户取消操作
        }

        console.log('✅ 用户确认继续，开始重新生成')
      } else {
        console.log('✨ 没有现有片段，直接开始生成')
      }

      const response = await fetch('/api/ai/generate-story-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          episodeId,
          prompt,
          projectId,
          modelId,
          generationMode: videoGenerationMode // 传递生成模式
        })
      })

      const data = await response.json()

      if (data.success) {
        // 显示详细的生成结果
        const message = data.data.message || '剧情视频片段创建完成！'
        const modeMessage = videoGenerationMode === 'auto'
          ? `共${data.data.totalSegments}个片段正在自动生成中...`
          : `共创建了${data.data.totalSegments}个片段，请在"剧情视频片段"页面手动生成每个片段。\n\n💡 当前为手动生成模式，您可以选择性地生成需要的片段。`
        alert(`${message}\n\n${modeMessage}`)
        setShowDetailedPlotExtraction(false)

        // 直接打开视频片段查看器，不切换标签页
        // 这样用户可以继续在当前标签页查看剧情内容
        setVideoEpisodeId(episodeId)
        setShowVideoSegmentViewer(true)
      } else {
        // 处理角色图像先决条件错误
        if (data.requirementType === 'character_images') {
          const missingChars = data.missingCharacters?.join('、') || '某些角色'
          const errorMessage = `❌ 视频生成失败\n\n${data.error}\n\n解决方案：\n1. 点击"角色管理"标签页\n2. 为 ${missingChars} 生成角色形象\n3. 确保每个角色都有正面、侧面、背面三视图\n4. 重新尝试生成视频`
          alert(errorMessage)

          // 自动切换到角色管理标签页
          setActiveTab('characters')
        } else {
          throw new Error(data.error || '剧情视频生成失败')
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '剧情视频生成失败')
    }
  }

  // 保存增强提示词
  const handleSaveEnhancePrompt = () => {
    setSavedEnhancePrompt(enhancePrompt)
    setShowPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_reanalyze_${projectId}`
    localStorage.setItem(storageKey, enhancePrompt)
  }

  // 从localStorage加载增强提示词
  useEffect(() => {
    if (projectId) {
      const storageKey = `enhance_prompt_reanalyze_${projectId}`
      const savedPrompt = localStorage.getItem(storageKey)
      if (savedPrompt) {
        setSavedEnhancePrompt(savedPrompt)
        setEnhancePrompt(savedPrompt)
      }
    }
  }, [projectId])

  // 手动分析人物剧情
  const handleManualAnalyze = async () => {
    if (!project?.content) {
      setError('没有小说内容可供分析')
      return
    }

    if (!selectedModel) {
      setError('请先选择分析模型')
      return
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const response = await fetch(`/api/projects/${projectId}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId: selectedModel,
          customPrompt: savedEnhancePrompt || undefined
        })
      })

      const data = await response.json()

      if (data.success) {
        // 更新项目数据
        setProject(data.data.project)
        setCharacters(data.data.characters || [])
        setEpisodes(data.data.episodes || [])
        // 切换到角色标签页
        updateActiveTab('characters')
      } else {
        throw new Error(data.error || 'AI分析失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'AI分析失败')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 监听URL参数变化，更新当前标签页
  useEffect(() => {
    const tab = searchParams.get('tab') as TabType
    const validTabs: TabType[] = ['upload', 'novel', 'characters', 'episodes', 'videos']
    if (tab && validTabs.includes(tab) && tab !== activeTab) {
      setActiveTab(tab)
    }
  }, [searchParams, activeTab])

  // 加载项目详情
  useEffect(() => {
    if (projectId) {
      loadProjectDetail()
    }
  }, [projectId])

  const loadProjectDetail = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}`)
      const data = await response.json()

      if (data.success) {
        setProject(data.data)
        setCharacters(data.data.characters || [])
        setEpisodes(data.data.episodes || [])

        // 只有在URL中没有指定标签页时，才根据项目状态设置默认标签页
        if (!searchParams.get('tab')) {
          if (data.data.status === 'created') {
            updateActiveTab('upload')
          } else if (data.data.status === 'uploaded') {
            updateActiveTab('novel')
          } else {
            updateActiveTab('characters')
          }
        }
      } else {
        throw new Error(data.error || '加载项目失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 重新加载项目数据的简化版本（用于角色管理器回调）
  const loadProject = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}`)
      const data = await response.json()

      if (data.success) {
        setProject(data.data)
        setCharacters(data.data.characters || [])
        setEpisodes(data.data.episodes || [])
      }
    } catch (error) {
      console.error('重新加载项目失败:', error)
    }
  }

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    const statusMap = {
      created: { text: '已创建', color: 'bg-gray-100 text-gray-800', icon: FileText },
      uploaded: { text: '已上传', color: 'bg-blue-100 text-blue-800', icon: Upload },
      analyzing: { text: '分析中', color: 'bg-yellow-100 text-yellow-800', icon: Play },
      completed: { text: '已完成', color: 'bg-green-100 text-green-800', icon: CheckCircle }
    }
    return statusMap[status as keyof typeof statusMap] || statusMap.created
  }

  // 标签页配置
  const tabs = [
    {
      id: 'upload' as TabType,
      name: '上传文件',
      icon: Upload,
      description: '上传小说文件进行AI分析',
      disabled: false
    },
    {
      id: 'novel' as TabType,
      name: '小说',
      icon: BookOpen,
      description: '查看已上传的小说内容',
      disabled: !project || project.status === 'created'
    },
    {
      id: 'characters' as TabType,
      name: '角色',
      icon: Users,
      description: '查看和管理提取的角色信息',
      disabled: !project || project.status === 'created'
    },
    {
      id: 'episodes' as TabType,
      name: '剧集',
      icon: Film,
      description: '查看和管理章节剧集',
      disabled: !project || project.status === 'created'
    },
    {
      id: 'videos' as TabType,
      name: '视频',
      icon: Video,
      description: '生成和管理视频脚本',
      disabled: !project || ['created', 'uploaded'].includes(project.status)
    }
  ]

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载项目中...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (error || !project) {
    return (
      <Layout>
        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">加载失败</h3>
          <p className="mt-1 text-sm text-gray-500">
            {error || '项目不存在'}
          </p>
          <div className="mt-6">
            <Link
              href="/projects"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
            >
              <ArrowLeft className="mr-2" size={16} />
              返回项目列表
            </Link>
          </div>
        </div>
      </Layout>
    )
  }

  const statusDisplay = getStatusDisplay(project.status)

  return (
    <Layout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <div>
          <Link
            href="/projects"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="mr-2" size={16} />
            返回项目列表
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
              {project.description && (
                <p className="mt-1 text-sm text-gray-600">{project.description}</p>
              )}
              <div className="mt-2 flex items-center space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                  <statusDisplay.icon className="mr-1" size={12} />
                  {statusDisplay.text}
                </span>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar size={14} className="mr-1" />
                  创建于 {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id
              const isDisabled = tab.disabled
              
              return (
                <button
                  key={tab.id}
                  onClick={() => !isDisabled && updateActiveTab(tab.id)}
                  disabled={isDisabled}
                  className={`
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    ${isActive
                      ? 'border-purple-500 text-purple-600'
                      : isDisabled
                      ? 'border-transparent text-gray-400 cursor-not-allowed'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <tab.icon 
                    className={`mr-2 ${isActive ? 'text-purple-500' : isDisabled ? 'text-gray-400' : 'text-gray-400 group-hover:text-gray-500'}`} 
                    size={16} 
                  />
                  {tab.name}
                  {tab.id === 'characters' && characters.length > 0 && (
                    <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                      {characters.length}
                    </span>
                  )}
                  {tab.id === 'episodes' && episodes.length > 0 && (
                    <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                      {episodes.length}
                    </span>
                  )}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="mt-6">
          {activeTab === 'upload' && (
            <ProjectFileUpload
              projectId={projectId}
              project={project}
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          )}

          {activeTab === 'novel' && (
            <div className="space-y-6">
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">小说内容</h3>
                    <div className="text-sm text-gray-500">
                      字数：{project?.content?.length || 0} 字
                    </div>
                  </div>

                  {project?.content ? (
                    <>
                      <div className="prose max-w-none">
                        <div className="bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto">
                          <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed">
                            {project.content}
                          </pre>
                        </div>
                      </div>

                      {/* 分析区域 */}
                      <div className="mt-6 space-y-4">
                        {/* 模型选择器 */}
                        <div className="flex items-center justify-center space-x-3">
                          <span className="text-sm font-medium text-gray-700">分析模型:</span>
                          <ModelSelector
                            selectedModel={selectedModel}
                            onModelChange={setSelectedModel}
                            className="w-64"
                          />
                        </div>

                        {/* 分析按钮 */}
                        <div className="flex justify-center space-x-3">
                          <div className="relative flex">
                            <button
                              onClick={handleManualAnalyze}
                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}
                              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-l-md text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isAnalyzing || project.status === 'analyzing' ? (
                                <>
                                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                  AI分析中...
                                </>
                              ) : (
                                <>
                                  <Users className="mr-2" size={20} />
                                  {project.status === 'completed' ? 'AI重新分析人物剧情' : '自动分析人物剧情'}
                                </>
                              )}
                            </button>

                            <button
                              onClick={() => setShowPromptDropdown(!showPromptDropdown)}
                              disabled={isAnalyzing || project.status === 'analyzing' || !selectedModel}
                              className="px-3 py-3 border border-transparent rounded-r-md border-l border-purple-500 text-base font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <ChevronDown size={16} />
                            </button>

                            {/* 增强提示词下拉框 */}
                            {showPromptDropdown && (
                              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                                <div className="p-4">
                                  <label className="block text-sm font-medium text-gray-700 mb-2">
                                    增强提示词设置
                                  </label>
                                  <textarea
                                    value={enhancePrompt}
                                    onChange={(e) => setEnhancePrompt(e.target.value)}
                                    placeholder="输入增强提示词，用于优化AI分析效果..."
                                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none"
                                    rows={4}
                                  />

                                  {/* 保存按钮 */}
                                  <div className="mt-3">
                                    <button
                                      onClick={handleSaveEnhancePrompt}
                                      className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                                    >
                                      保存增强提示词
                                    </button>
                                  </div>

                                  {/* 操作按钮 */}
                                  <div className="flex justify-end mt-3">
                                    <button
                                      onClick={() => setShowPromptDropdown(false)}
                                      className="text-sm text-gray-600 hover:text-gray-800"
                                    >
                                      关闭
                                    </button>
                                  </div>

                                  {/* 当前保存的提示词预览 */}
                                  {savedEnhancePrompt && (
                                    <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                                      <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                                      <div className="max-h-16 overflow-y-auto">
                                        {savedEnhancePrompt}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>

                          {project.status === 'completed' && (
                            <div className="flex items-center text-sm text-green-600">
                              <CheckCircle className="mr-1" size={16} />
                              已完成分析，可重新分析
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">暂无小说内容</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        请先上传小说文件
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'characters' && (
            <CharacterManager
              projectId={params.id}
              characters={project.characters || []}
              onCharactersUpdate={loadProject}
            />
          )}

          {activeTab === 'episodes' && (
            <EpisodeManager
              episodes={episodes}
              onAnalyzePlot={handleAnalyzePlot}
              onGenerateVideo={handleGenerateVideo}
              onExtractDetailedPlot={handleExtractDetailedPlot}
            />
          )}

          {activeTab === 'videos' && (
            <div className="space-y-6">
              {/* 视频管理头部 */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Video className="text-purple-600 mr-2" size={20} />
                    <h3 className="text-lg font-medium text-gray-900">剧情视频管理</h3>
                  </div>
                  <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
                    共 {episodes.length} 集
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-600">
                  支持传统分段视频生成和新的一致性约束视频生成
                </p>
              </div>

              {/* 剧集视频列表 */}
              {episodes.length > 0 ? (
                <div className="space-y-4">
                  {episodes.map((episode, index) => (
                    <EpisodeVideoCard
                      key={episode.id}
                      episode={episode}
                      episodeIndex={index}
                      projectId={projectId}
                      onViewSegments={() => {
                        setVideoEpisodeId(episode.id)
                        setShowVideoSegmentViewer(true)
                      }}
                      onGenerateConsistencyVideo={() => handleOpenConsistencyVideoGenerator(episode.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Video className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无剧集</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    请先上传小说并分析剧集
                  </p>
                </div>
              )}

              {/* 说明信息 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-2">
                  分段视频生成说明
                </h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>• <strong>智能分段：</strong>AI自动将剧情分解为3-8个短视频片段</p>
                  <p>• <strong>并行生成：</strong>多个片段同时生成，提高效率</p>
                  <p>• <strong>实时进度：</strong>可查看每个片段的生成状态和进度</p>
                  <p>• <strong>独立播放：</strong>每个片段可单独播放和下载</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 详细剧情提取模态框 */}
        <DetailedPlotExtraction
          episodeId={currentEpisodeId}
          episodeTitle={currentEpisodeTitle}
          episodeContent={currentEpisodeContent}
          isOpen={showDetailedPlotExtraction}
          onClose={() => setShowDetailedPlotExtraction(false)}
          onGenerateStoryVideo={handleGenerateStoryVideo}
          videoGenerationMode={videoGenerationMode}
          onVideoGenerationModeChange={setVideoGenerationMode}
        />

        {/* 视频片段查看器 */}
        <VideoSegmentViewer
          episodeId={videoEpisodeId}
          projectId={projectId}
          isOpen={showVideoSegmentViewer}
          onClose={() => setShowVideoSegmentViewer(false)}
        />

        {/* 一致性视频生成器模态框 */}
        {showConsistencyVideoGenerator && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">一致性视频生成</h2>
                  <button
                    onClick={() => setShowConsistencyVideoGenerator(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X size={24} />
                  </button>
                </div>

                <ConsistencyVideoGenerator
                  episodeId={consistencyEpisodeId}
                  episodeTitle={consistencyEpisodeTitle}
                  onVideoGenerated={handleConsistencyVideoGenerated}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
