import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 对话片段接口
interface DialogueSegment {
  id: string
  type: 'dialogue' | 'narration' | 'thought'
  text: string
  characterId?: string
  characterName?: string
  emotion: string
  startTime: number
  duration: number
  segmentIndex: number
}

// 音频生成结果
interface AudioGenerationResult {
  segmentIndex: number
  dialogueType: string
  characterId?: string
  text: string
  emotion: string
  audioUrl: string
  duration: number
  fileSize: number
  format: string
  status: 'completed' | 'failed'
  error?: string
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const episodeId = params.id
    const body = await request.json()
    const { segments, options = {} } = body

    console.log(`🎵 开始为剧集 ${episodeId} 生成音频`)

    // 验证剧集是否存在
    const episode = await prisma.episode.findUnique({
      where: { id: episodeId }
    })

    if (!episode) {
      return NextResponse.json(
        { error: '剧集不存在' },
        { status: 404 }
      )
    }

    // 获取可用的TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'asc' }
      ]
    })

    if (!ttsConfig) {
      return NextResponse.json(
        { error: '未找到可用的TTS配置' },
        { status: 404 }
      )
    }

    console.log(`📡 使用TTS服务: ${ttsConfig.name}`)

    // 处理每个片段的音频生成
    const results: AudioGenerationResult[] = []
    
    for (const segment of segments) {
      try {
        console.log(`🎤 处理片段 ${segment.segmentIndex}: ${segment.text.substring(0, 50)}...`)
        
        // 提取对话内容
        const dialogues = await extractDialogues(segment.text, segment.segmentIndex)
        
        // 为每个对话生成音频
        for (const dialogue of dialogues) {
          const audioResult = await generateDialogueAudio(dialogue, ttsConfig, episodeId)
          results.push(audioResult)
        }
        
      } catch (error) {
        console.error(`片段 ${segment.segmentIndex} 音频生成失败:`, error)
        results.push({
          segmentIndex: segment.segmentIndex,
          dialogueType: 'narration',
          text: segment.text,
          emotion: 'neutral',
          audioUrl: '',
          duration: 0,
          fileSize: 0,
          format: 'mp3',
          status: 'failed',
          error: error instanceof Error ? error.message : '音频生成失败'
        })
      }
    }

    // 统计结果
    const successCount = results.filter(r => r.status === 'completed').length
    const failedCount = results.filter(r => r.status === 'failed').length

    console.log(`✅ 音频生成完成: 成功 ${successCount}, 失败 ${failedCount}`)

    return NextResponse.json({
      success: true,
      data: {
        episodeId,
        totalSegments: segments.length,
        totalDialogues: results.length,
        successCount,
        failedCount,
        results,
        ttsProvider: ttsConfig.provider
      }
    })

  } catch (error) {
    console.error('剧集音频生成失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '音频生成失败',
        success: false 
      },
      { status: 500 }
    )
  }
}

// 从文本中提取对话
async function extractDialogues(text: string, segmentIndex: number): Promise<DialogueSegment[]> {
  const dialogues: DialogueSegment[] = []
  
  // 简单的对话提取逻辑
  // 在实际应用中，这里应该使用更复杂的NLP技术
  const lines = text.split('\n').filter(line => line.trim())
  
  let currentTime = 0
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue
    
    // 检测对话格式："角色名：对话内容" 或 "「对话内容」"
    const dialogueMatch = line.match(/^([^：:]+)[：:](.+)$/) || line.match(/^「(.+)」$/)
    
    if (dialogueMatch) {
      // 这是对话
      const characterName = dialogueMatch[1]?.trim()
      const dialogueText = dialogueMatch[2]?.trim() || dialogueMatch[1]?.trim()
      
      const dialogue: DialogueSegment = {
        id: `${segmentIndex}-${i}`,
        type: 'dialogue',
        text: dialogueText,
        characterName,
        emotion: detectEmotion(dialogueText),
        startTime: currentTime,
        duration: estimateDialogueDuration(dialogueText),
        segmentIndex
      }
      
      dialogues.push(dialogue)
      currentTime += dialogue.duration
    } else {
      // 这是旁白
      const narration: DialogueSegment = {
        id: `${segmentIndex}-${i}`,
        type: 'narration',
        text: line,
        emotion: 'neutral',
        startTime: currentTime,
        duration: estimateDialogueDuration(line),
        segmentIndex
      }
      
      dialogues.push(narration)
      currentTime += narration.duration
    }
  }
  
  return dialogues
}

// 简单的情感检测
function detectEmotion(text: string): string {
  if (text.includes('！') || text.includes('!')) return 'excited'
  if (text.includes('？') || text.includes('?')) return 'surprised'
  if (text.includes('哭') || text.includes('悲')) return 'sad'
  if (text.includes('笑') || text.includes('开心')) return 'happy'
  if (text.includes('怒') || text.includes('气')) return 'angry'
  return 'neutral'
}

// 估算对话时长
function estimateDialogueDuration(text: string): number {
  // 中文平均每分钟250字
  const wordsPerMinute = 250
  const minutes = text.length / wordsPerMinute
  return Math.max(minutes * 60, 2) // 最少2秒
}

// 为对话生成音频
async function generateDialogueAudio(
  dialogue: DialogueSegment, 
  ttsConfig: any, 
  episodeId: string
): Promise<AudioGenerationResult> {
  try {
    // 查找角色的声音配置
    let voiceConfig = null
    if (dialogue.characterName) {
      // 根据角色名查找角色
      const character = await prisma.character.findFirst({
        where: {
          name: dialogue.characterName
        },
        include: {
          voiceConfigs: {
            where: {
              ttsConfigId: ttsConfig.id,
              enabled: true
            }
          }
        }
      })
      
      if (character && character.voiceConfigs.length > 0) {
        voiceConfig = character.voiceConfigs[0]
        dialogue.characterId = character.id
      }
    }
    
    // 构建TTS请求
    const ttsRequest = {
      text: dialogue.text,
      characterId: dialogue.characterId,
      voiceId: voiceConfig?.voiceId || 'zh-CN-XiaoxiaoNeural',
      emotion: dialogue.emotion,
      speed: voiceConfig?.baseSpeed || 1.0,
      pitch: voiceConfig?.basePitch || 0,
      volume: voiceConfig?.baseVolume || 80,
      format: 'mp3'
    }
    
    // 调用TTS API
    const response = await fetch('http://localhost:3000/api/ai/generate-tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ttsRequest)
    })
    
    if (!response.ok) {
      throw new Error(`TTS API调用失败: ${response.status}`)
    }
    
    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'TTS生成失败')
    }
    
    // 保存音频文件记录到数据库
    const audioFile = await prisma.audioFile.create({
      data: {
        episodeId,
        segmentIndex: dialogue.segmentIndex,
        dialogueType: dialogue.type,
        characterId: dialogue.characterId,
        text: dialogue.text,
        emotion: dialogue.emotion,
        audioUrl: result.data.audioUrl,
        duration: result.data.duration,
        fileSize: result.data.size,
        format: result.data.format,
        status: 'completed',
        metadata: JSON.stringify({
          voice: ttsRequest.voiceId,
          speed: ttsRequest.speed,
          pitch: ttsRequest.pitch,
          volume: ttsRequest.volume,
          characterName: dialogue.characterName
        })
      }
    })
    
    console.log(`✅ 对话音频生成成功: ${dialogue.text.substring(0, 30)}...`)
    
    return {
      segmentIndex: dialogue.segmentIndex,
      dialogueType: dialogue.type,
      characterId: dialogue.characterId,
      text: dialogue.text,
      emotion: dialogue.emotion,
      audioUrl: result.data.audioUrl,
      duration: result.data.duration,
      fileSize: result.data.size,
      format: result.data.format,
      status: 'completed'
    }
    
  } catch (error) {
    console.error('对话音频生成失败:', error)
    
    return {
      segmentIndex: dialogue.segmentIndex,
      dialogueType: dialogue.type,
      characterId: dialogue.characterId,
      text: dialogue.text,
      emotion: dialogue.emotion,
      audioUrl: '',
      duration: 0,
      fileSize: 0,
      format: 'mp3',
      status: 'failed',
      error: error instanceof Error ? error.message : '音频生成失败'
    }
  }
}
