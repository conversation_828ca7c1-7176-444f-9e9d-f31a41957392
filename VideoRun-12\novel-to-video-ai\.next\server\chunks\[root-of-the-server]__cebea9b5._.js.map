{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/ai/save-custom-prompt/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport fs from 'fs'\nimport path from 'path'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { episodeId, customPrompt } = await request.json()\n\n    if (!episodeId) {\n      return NextResponse.json({\n        success: false,\n        error: '缺少剧集ID'\n      }, { status: 400 })\n    }\n\n    // 确保数据目录存在\n    const dataDir = path.join(process.cwd(), 'data', 'custom-prompts')\n    if (!fs.existsSync(dataDir)) {\n      fs.mkdirSync(dataDir, { recursive: true })\n    }\n\n    // 保存自定义提示词到文件\n    const filePath = path.join(dataDir, `${episodeId}.json`)\n    const data = {\n      episodeId,\n      customPrompt,\n      savedAt: new Date().toISOString()\n    }\n\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2))\n\n    return NextResponse.json({\n      success: true,\n      message: '自定义提示词保存成功'\n    })\n\n  } catch (error) {\n    console.error('保存自定义提示词失败:', error)\n    return NextResponse.json({\n      success: false,\n      error: '保存失败'\n    }, { status: 500 })\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const episodeId = searchParams.get('episodeId')\n\n    if (!episodeId) {\n      return NextResponse.json({\n        success: false,\n        error: '缺少剧集ID'\n      }, { status: 400 })\n    }\n\n    const filePath = path.join(process.cwd(), 'data', 'custom-prompts', `${episodeId}.json`)\n    \n    if (!fs.existsSync(filePath)) {\n      return NextResponse.json({\n        success: true,\n        data: {\n          customPrompt: ''\n        }\n      })\n    }\n\n    const data = JSON.parse(fs.readFileSync(filePath, 'utf-8'))\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        customPrompt: data.customPrompt || ''\n      }\n    })\n\n  } catch (error) {\n    console.error('读取自定义提示词失败:', error)\n    return NextResponse.json({\n      success: false,\n      error: '读取失败'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtD,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,WAAW;QACX,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACjD,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;YAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;QAEA,cAAc;QACd,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,KAAK,CAAC;QACvD,MAAM,OAAO;YACX;YACA;YACA,SAAS,IAAI,OAAO,WAAW;QACjC;QAEA,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM;QAEtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,kBAAkB,GAAG,UAAU,KAAK,CAAC;QAEvF,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,cAAc;gBAChB;YACF;QACF;QAEA,MAAM,OAAO,KAAK,KAAK,CAAC,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,cAAc,KAAK,YAAY,IAAI;YACrC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}