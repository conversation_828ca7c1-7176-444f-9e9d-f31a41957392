const { PrismaClient } = require('@prisma/client');

async function diagnoseConfigs() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 详细诊断所有AI配置...\n');
    
    const configs = await prisma.aIConfig.findMany();
    
    if (configs.length === 0) {
      console.log('❌ 没有找到任何AI配置！');
      console.log('\n💡 解决方案:');
      console.log('1. 前往 AI配置 页面添加配置');
      console.log('2. 或者运行 node setup-video-config.js 自动创建');
      return;
    }
    
    configs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.name}`);
      console.log(`   ID: ${config.id}`);
      console.log(`   提供商: ${config.provider}`);
      console.log(`   模型: ${config.model}`);
      
      // 检查API密钥
      if (!config.apiKey) {
        console.log('   API密钥: ❌ 未配置');
      } else if (config.apiKey === 'your-doubao-api-key' || config.apiKey === 'sk-test-key') {
        console.log('   API密钥: ⚠️ 使用占位符密钥');
      } else {
        console.log(`   API密钥: ✅ 已配置 (${config.apiKey.substring(0, 10)}...)`);
      }
      
      console.log(`   启用状态: ${config.enabled ? '✅ 启用' : '❌ 禁用'}`);
      console.log(`   连接状态: ${config.status}`);
      console.log(`   支持视频: ${config.supportsVideo ? '✅ 是' : '❌ 否'}`);
      console.log(`   最后测试: ${config.lastTest || '从未测试'}`);
      console.log('');
    });
    
    // 分析问题
    const enabledConfigs = configs.filter(c => c.enabled);
    const validApiKeys = configs.filter(c => 
      c.apiKey && 
      c.apiKey !== 'your-doubao-api-key' && 
      c.apiKey !== 'sk-test-key' &&
      c.apiKey.length > 10
    );
    const connectedConfigs = configs.filter(c => c.status === 'connected');
    const videoConfigs = configs.filter(c => c.supportsVideo);
    
    console.log('📊 配置统计:');
    console.log(`   总配置数: ${configs.length}`);
    console.log(`   启用的配置: ${enabledConfigs.length}`);
    console.log(`   有效API密钥: ${validApiKeys.length}`);
    console.log(`   已连接的配置: ${connectedConfigs.length}`);
    console.log(`   支持视频的配置: ${videoConfigs.length}`);
    
    // 诊断问题
    console.log('\n🔧 问题诊断:');
    
    if (validApiKeys.length === 0) {
      console.log('❌ 主要问题: 所有配置都使用占位符API密钥！');
      console.log('   解决方案: 需要配置真实的API密钥');
    }
    
    if (enabledConfigs.length === 0) {
      console.log('❌ 问题: 所有配置都被禁用');
      console.log('   解决方案: 启用至少一个配置');
    }
    
    if (videoConfigs.length === 0) {
      console.log('❌ 问题: 没有支持视频生成的配置');
      console.log('   解决方案: 至少需要一个视频生成模型');
    }
    
    if (connectedConfigs.length === 0) {
      console.log('❌ 问题: 没有已连接的配置');
      console.log('   解决方案: 测试连接并确保API密钥正确');
    }
    
    // 提供具体的修复建议
    console.log('\n💡 修复建议:');
    console.log('1. 前往 http://localhost:3000/ai-config 页面');
    console.log('2. 为每个模型配置真实的API密钥:');
    console.log('   - DeepSeek: 需要 DeepSeek API 密钥');
    console.log('   - 豆包: 需要字节跳动豆包 API 密钥');
    console.log('3. 点击"测试连接"确保配置正确');
    console.log('4. 确保至少有一个视频生成模型可用');
    
  } catch (error) {
    console.error('❌ 诊断失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

diagnoseConfigs();
