const { PrismaClient } = require('@prisma/client');

async function testVideoModels() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 测试视频模型查找逻辑...');
    
    // 模拟修复后的查询
    const videoModels = await prisma.aIConfig.findMany({
      where: {
        enabled: true,
        supportsVideo: true
      }
    });
    
    console.log('\n📋 找到的视频模型:');
    videoModels.forEach((model, index) => {
      console.log(`${index + 1}. ${model.name} (${model.provider}): ${model.model}`);
      console.log(`   启用: ${model.enabled}, 支持视频: ${model.supportsVideo}`);
    });
    
    if (videoModels.length > 0) {
      // 模拟选择逻辑
      const videoModel = videoModels.find(m => m.provider === 'doubao') || 
                        videoModels.find(m => m.provider === 'tongyi') || 
                        videoModels[0];
      
      console.log('\n✅ 将使用的视频模型:');
      console.log(`   名称: ${videoModel.name}`);
      console.log(`   提供商: ${videoModel.provider}`);
      console.log(`   模型: ${videoModel.model}`);
      console.log(`   API密钥: ${videoModel.apiKey ? '已配置' : '未配置'}`);
      
      // 判断调用哪个API
      let apiType = 'unknown';
      if (videoModel.provider === 'doubao') {
        apiType = 'callDoubaoVideoAPI';
      } else if (videoModel.provider === 'minimax') {
        apiType = 'callMinimaxHailuoAPI';
      } else if (videoModel.provider === 'tongyi') {
        apiType = 'callTongyiVideoAPI';
      }
      
      console.log(`   将调用: ${apiType}`);
      
    } else {
      console.log('\n❌ 没有找到支持视频的模型');
      console.log('\n💡 解决方案:');
      console.log('1. 确保至少有一个AI配置的 supportsVideo = true');
      console.log('2. 确保该配置的 enabled = true');
      console.log('3. 确保API密钥已正确配置');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testVideoModels();
