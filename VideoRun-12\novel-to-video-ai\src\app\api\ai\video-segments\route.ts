import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET - 获取视频片段状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const episodeId = searchParams.get('episodeId')
    const storyVideoId = searchParams.get('storyVideoId')

    if (!episodeId && !storyVideoId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId或storyVideoId参数' },
        { status: 400 }
      )
    }

    let whereClause: any = {}
    if (storyVideoId) {
      whereClause.storyVideoId = storyVideoId
    } else if (episodeId) {
      whereClause.episodeId = episodeId
    }

    // 获取视频片段信息
    const segments = await prisma.videoSegment.findMany({
      where: whereClause,
      orderBy: { segmentIndex: 'asc' },
      include: {
        storyVideo: {
          select: {
            id: true,
            status: true,
            metadata: true
          }
        }
      }
    })

    if (segments.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          segments: [],
          summary: {
            totalSegments: 0,
            completedSegments: 0,
            generatingSegments: 0,
            failedSegments: 0,
            pendingSegments: 0,
            progress: 0,
            overallStatus: 'none'
          }
        }
      })
    }

    // 统计生成状态
    const statusCounts = segments.reduce((acc, segment) => {
      acc[segment.status] = (acc[segment.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const totalSegments = segments.length
    const completedSegments = statusCounts.completed || 0
    const generatingSegments = statusCounts.generating || 0
    const failedSegments = statusCounts.failed || 0
    const pendingSegments = statusCounts.pending || 0

    return NextResponse.json({
      success: true,
      data: {
        segments: segments.map(segment => ({
          id: segment.id,
          title: segment.title,
          videoUrl: segment.videoUrl || '',
          duration: segment.duration || 0,
          createdAt: segment.createdAt.toISOString(),
          status: segment.status
        })),
        summary: {
          totalSegments,
          completedSegments,
          generatingSegments,
          failedSegments,
          pendingSegments,
          progress: Math.round((completedSegments / totalSegments) * 100),
          overallStatus: segments[0].storyVideo?.status || 'unknown'
        }
      }
    })
  } catch (error) {
    console.error('获取视频片段失败:', error)
    return NextResponse.json(
      { success: false, error: '获取失败，请重试' },
      { status: 500 }
    )
  }
}
