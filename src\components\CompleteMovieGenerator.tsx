'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Play, Download, Eye, Settings, Clock, Film, Music, Volume2 } from 'lucide-react'

interface CompleteMovieGeneratorProps {
  episodeId: string
  episodeTitle: string
  onMovieGenerated?: (movieData: any) => void
}

export default function CompleteMovieGenerator({
  episodeId,
  episodeTitle,
  onMovieGenerated
}: CompleteMovieGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentTask, setCurrentTask] = useState(null)
  const [generationHistory, setGenerationHistory] = useState([])
  const [settings, setSettings] = useState({
    generateMode: 'auto',
    includeBackgroundMusic: true,
    includeSoundEffects: true,
    videoQuality: 'high',
    audioQuality: 'high'
  })

  // 生成完整影视作品
  const handleGenerateMovie = async () => {
    try {
      setIsGenerating(true)
      
      console.log('🎬 开始生成完整影视作品...')
      
      const response = await fetch(`/api/episodes/${episodeId}/generate-complete-movie`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || '生成失败')
      }
      
      if (result.success) {
        setCurrentTask(result.data)
        console.log('✅ 影视生成任务已启动:', result.data)
        
        // 开始轮询任务状态
        pollTaskStatus(result.data.taskId)
      } else {
        throw new Error(result.error || '生成失败')
      }
      
    } catch (error) {
      console.error('生成影视作品失败:', error)
      alert(`生成失败: ${error.message}`)
      setIsGenerating(false)
    }
  }

  // 轮询任务状态
  const pollTaskStatus = async (taskId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/episodes/${episodeId}/generate-complete-movie?taskId=${taskId}`)
        const result = await response.json()
        
        if (result.success) {
          setCurrentTask(result.data)
          
          if (result.data.status === 'completed') {
            clearInterval(pollInterval)
            setIsGenerating(false)
            onMovieGenerated?.(result.data)
            loadGenerationHistory()
            console.log('🎉 影视作品生成完成!')
          } else if (result.data.status === 'failed') {
            clearInterval(pollInterval)
            setIsGenerating(false)
            alert(`生成失败: ${result.data.statusMessage}`)
          }
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
      }
    }, 3000) // 每3秒轮询一次
  }

  // 加载生成历史
  const loadGenerationHistory = async () => {
    try {
      const response = await fetch(`/api/episodes/${episodeId}/generate-complete-movie`)
      const result = await response.json()
      
      if (result.success) {
        setGenerationHistory(result.data)
      }
    } catch (error) {
      console.error('加载生成历史失败:', error)
    }
  }

  // 获取状态显示信息
  const getStatusInfo = (status: string) => {
    const statusMap = {
      'pending': { label: '等待中', color: 'bg-gray-500', icon: Clock },
      'initializing': { label: '初始化', color: 'bg-blue-500', icon: Settings },
      'analyzing': { label: '分析剧情', color: 'bg-purple-500', icon: Eye },
      'generating_video': { label: '生成视频', color: 'bg-orange-500', icon: Film },
      'generating_audio': { label: '生成音频', color: 'bg-green-500', icon: Volume2 },
      'synchronizing': { label: '同步合并', color: 'bg-indigo-500', icon: Play },
      'completed': { label: '已完成', color: 'bg-emerald-500', icon: Download },
      'failed': { label: '失败', color: 'bg-red-500', icon: Clock }
    }
    return statusMap[status] || statusMap['pending']
  }

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  useEffect(() => {
    loadGenerationHistory()
  }, [episodeId])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Film className="h-5 w-5" />
            完整影视作品生成器
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            将剧集 "{episodeTitle}" 生成为完整的影视作品，包含同步的视频、对话、背景音乐和音效
          </p>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="generate" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="generate">生成设置</TabsTrigger>
              <TabsTrigger value="progress">生成进度</TabsTrigger>
              <TabsTrigger value="history">历史记录</TabsTrigger>
            </TabsList>
            
            <TabsContent value="generate" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>生成模式</Label>
                  <Select 
                    value={settings.generateMode} 
                    onValueChange={(value) => setSettings({...settings, generateMode: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动生成</SelectItem>
                      <SelectItem value="manual">手动控制</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>视频质量</Label>
                  <Select 
                    value={settings.videoQuality} 
                    onValueChange={(value) => setSettings({...settings, videoQuality: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">标准</SelectItem>
                      <SelectItem value="high">高清</SelectItem>
                      <SelectItem value="cinematic">电影级</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>背景音乐</Label>
                    <p className="text-sm text-muted-foreground">
                      根据剧情情绪自动生成背景音乐
                    </p>
                  </div>
                  <Switch
                    checked={settings.includeBackgroundMusic}
                    onCheckedChange={(checked) => 
                      setSettings({...settings, includeBackgroundMusic: checked})
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>音效</Label>
                    <p className="text-sm text-muted-foreground">
                      根据场景自动添加环境音效
                    </p>
                  </div>
                  <Switch
                    checked={settings.includeSoundEffects}
                    onCheckedChange={(checked) => 
                      setSettings({...settings, includeSoundEffects: checked})
                    }
                  />
                </div>
              </div>
              
              <Button 
                onClick={handleGenerateMovie}
                disabled={isGenerating}
                className="w-full"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Settings className="mr-2 h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    开始生成完整影视作品
                  </>
                )}
              </Button>
            </TabsContent>
            
            <TabsContent value="progress" className="space-y-4">
              {currentTask ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {(() => {
                        const statusInfo = getStatusInfo(currentTask.status)
                        const Icon = statusInfo.icon
                        return (
                          <>
                            <Icon className="h-4 w-4" />
                            <span className="font-medium">{statusInfo.label}</span>
                          </>
                        )
                      })()}
                    </div>
                    <Badge className={getStatusInfo(currentTask.status).color}>
                      {currentTask.progress}%
                    </Badge>
                  </div>
                  
                  <Progress value={currentTask.progress} className="w-full" />
                  
                  {currentTask.statusMessage && (
                    <p className="text-sm text-muted-foreground">
                      {currentTask.statusMessage}
                    </p>
                  )}
                  
                  {currentTask.status === 'completed' && currentTask.finalMovieUrl && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-4 text-sm">
                        <span>总时长: {formatDuration(currentTask.totalDuration || 0)}</span>
                        <span>状态: 已完成</span>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="mr-2 h-4 w-4" />
                          预览
                        </Button>
                        <Button size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无生成任务
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="history" className="space-y-4">
              {generationHistory.length > 0 ? (
                <div className="space-y-2">
                  {generationHistory.map((task, index) => (
                    <Card key={task.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusInfo(task.status).color}>
                              {getStatusInfo(task.status).label}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(task.createdAt).toLocaleString()}
                            </span>
                          </div>
                          {task.totalDuration && (
                            <p className="text-sm">
                              时长: {formatDuration(task.totalDuration)}
                            </p>
                          )}
                        </div>
                        {task.status === 'completed' && task.finalMovieUrl && (
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Eye className="mr-2 h-4 w-4" />
                              查看
                            </Button>
                            <Button size="sm">
                              <Download className="mr-2 h-4 w-4" />
                              下载
                            </Button>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无生成历史
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
