import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

interface ZhipuImageRequest {
  prompt: string
  model?: string
  size?: string
  quality?: string
  n?: number
  customPrompt?: string
}

interface ZhipuImageResponse {
  created: number
  data: Array<{
    url: string
    b64_json?: string
  }>
}

// POST - 生成图像
export async function POST(request: NextRequest) {
  try {
    const body: ZhipuImageRequest = await request.json()
    const { 
      prompt, 
      model = 'cogview-3-flash', 
      size = '1024x1024',
      quality = 'standard',
      n = 1,
      customPrompt 
    } = body

    // 获取智谱AI配置
    const aiConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'zhipu',
        model: model,
        enabled: true
      }
    })

    if (!aiConfig) {
      return NextResponse.json(
        { error: '智谱AI模型未配置或未启用' },
        { status: 400 }
      )
    }

    // 构建最终提示词
    let finalPrompt = prompt
    if (customPrompt && customPrompt.trim()) {
      finalPrompt = `${prompt}\n\n增强要求：${customPrompt}`
    }

    // 调用智谱AI API
    const imageResult = await generateImageWithZhipu(aiConfig.apiKey, {
      prompt: finalPrompt,
      model,
      size,
      quality,
      n
    })

    return NextResponse.json(imageResult)
  } catch (error) {
    console.error('智谱AI图像生成失败:', error)
    return NextResponse.json(
      { error: '图像生成失败' },
      { status: 500 }
    )
  }
}

// 调用智谱AI图像生成API
async function generateImageWithZhipu(
  apiKey: string,
  params: {
    prompt: string
    model: string
    size: string
    quality: string
    n: number
  }
): Promise<ZhipuImageResponse> {
  try {
    const response = await fetch('https://open.bigmodel.cn/api/paas/v4/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: params.model,
        prompt: params.prompt,
        size: params.size,
        quality: params.quality,
        n: params.n
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`智谱AI API错误: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('智谱AI API调用失败:', error)
    throw error
  }
}

// 批量生成图像（用于角色三视图）
export async function generateMultipleImages(
  apiKey: string,
  imageRequests: Array<{
    prompt: string
    model: string
    size: string
    quality: string
  }>
): Promise<ZhipuImageResponse[]> {
  const results: ZhipuImageResponse[] = []
  
  // 并发生成所有图像
  const promises = imageRequests.map(request => 
    generateImageWithZhipu(apiKey, { ...request, n: 1 })
  )
  
  const imageResults = await Promise.allSettled(promises)
  
  imageResults.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      results.push(result.value)
    } else {
      console.error(`图像生成失败 (${index}):`, result.reason)
      // 生成失败时返回空结果
      results.push({
        created: Date.now(),
        data: []
      })
    }
  })
  
  return results
}

// 生成角色三视图
export async function generateCharacterViews(
  apiKey: string,
  characterPrompt: string,
  model: string = 'cogview-4'
): Promise<{
  front: ZhipuImageResponse
  side: ZhipuImageResponse
  back: ZhipuImageResponse
}> {
  const viewPrompts = [
    `${characterPrompt}, front view, facing camera, character design sheet`,
    `${characterPrompt}, side view, 90-degree profile, character design sheet`,
    `${characterPrompt}, back view, rear angle, character design sheet`
  ]

  const requests = viewPrompts.map(prompt => ({
    prompt,
    model,
    size: '1024x1024',
    quality: 'hd'
  }))

  const results = await generateMultipleImages(apiKey, requests)

  return {
    front: results[0],
    side: results[1],
    back: results[2]
  }
}

// 图像风格转换
export async function convertImageStyle(
  apiKey: string,
  originalPrompt: string,
  targetStyle: string,
  model: string = 'cogview-4'
): Promise<ZhipuImageResponse> {
  const styledPrompt = `${originalPrompt}, ${targetStyle} style, high quality, detailed`
  
  return await generateImageWithZhipu(apiKey, {
    prompt: styledPrompt,
    model,
    size: '1024x1024',
    quality: 'hd',
    n: 1
  })
}

// 图像质量增强
export async function enhanceImageQuality(
  apiKey: string,
  prompt: string,
  model: string = 'cogview-4'
): Promise<ZhipuImageResponse> {
  const enhancedPrompt = `${prompt}, ultra high quality, masterpiece, highly detailed, professional photography, 8K resolution`
  
  return await generateImageWithZhipu(apiKey, {
    prompt: enhancedPrompt,
    model,
    size: '1024x1024',
    quality: 'hd',
    n: 1
  })
}

// 根据场景生成背景图
export async function generateSceneBackground(
  apiKey: string,
  sceneDescription: string,
  model: string = 'cogview-4'
): Promise<ZhipuImageResponse> {
  const backgroundPrompt = `${sceneDescription}, background scene, no characters, environmental art, detailed landscape`
  
  return await generateImageWithZhipu(apiKey, {
    prompt: backgroundPrompt,
    model,
    size: '1024x1024',
    quality: 'hd',
    n: 1
  })
}

// 生成角色表情包
export async function generateCharacterExpressions(
  apiKey: string,
  characterPrompt: string,
  expressions: string[],
  model: string = 'cogview-3-flash'
): Promise<ZhipuImageResponse[]> {
  const requests = expressions.map(expression => ({
    prompt: `${characterPrompt}, ${expression} expression, portrait, character design`,
    model,
    size: '512x512',
    quality: 'standard'
  }))

  return await generateMultipleImages(apiKey, requests)
}

// 智谱AI模型能力映射
export const ZHIPU_MODEL_CAPABILITIES = {
  'cogview-3-flash': {
    name: 'CogView-3-Flash',
    description: '快速图像生成，适合实时应用',
    maxSize: '1024x1024',
    supportedSizes: ['512x512', '768x768', '1024x1024'],
    speed: 'fast',
    quality: 'good'
  },
  'cogview-4': {
    name: 'CogView-4',
    description: '高质量图像生成，支持复杂场景',
    maxSize: '1024x1024',
    supportedSizes: ['512x512', '768x768', '1024x1024'],
    speed: 'medium',
    quality: 'excellent'
  }
}

// 获取模型推荐配置
export function getRecommendedConfig(useCase: string) {
  const configs = {
    'character_design': {
      model: 'cogview-4',
      size: '1024x1024',
      quality: 'hd'
    },
    'quick_preview': {
      model: 'cogview-3-flash',
      size: '512x512',
      quality: 'standard'
    },
    'scene_background': {
      model: 'cogview-4',
      size: '1024x1024',
      quality: 'hd'
    },
    'expression_pack': {
      model: 'cogview-3-flash',
      size: '512x512',
      quality: 'standard'
    }
  }

  return configs[useCase] || configs['character_design']
}
