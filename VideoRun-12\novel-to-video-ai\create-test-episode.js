// 创建测试剧集来验证一致性功能
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestEpisode() {
  try {
    console.log('创建测试剧集...');
    
    // 使用已有的测试项目ID
    const projectId = 'cmc5yxtyl0000vmcsfqkr7qoi';
    
    // 创建一个包含已知角色的测试剧集
    const episode = await prisma.episode.create({
      data: {
        projectId: projectId,
        title: '第二章：重逢',
        content: `张小雅走进咖啡店，看到了正在工作的王美丽。王美丽依然是那个温柔的咖啡店老板，瓜子脸上带着微笑，波浪卷发在灯光下闪闪发光。

"小雅！"王美丽惊喜地叫道，"你怎么来了？"

张小雅露出了她标志性的甜美笑容，马尾辫随着她的动作轻轻摆动。"美丽姐，我想你了。"

这时，李明轩教授也走进了咖啡店。他依然是那副学者的模样，方脸上带着温和的表情，花白的短发整齐地梳着。

"两位美女在聊什么呢？"李明轩温文尔雅地问道。

突然，一个陌生的年轻人走了进来。他看起来大约二十五岁，高高瘦瘦的，戴着黑框眼镜，穿着简单的白色T恤和牛仔裤。

"请问这里有WiFi吗？"陌生人问道，"我是新来的程序员，叫做林志强。"`,
        orderIndex: 2,
        status: 'created'
      }
    });
    
    console.log('✅ 测试剧集创建成功:', episode.id);
    console.log('剧集标题:', episode.title);
    console.log('项目ID:', episode.projectId);
    
    console.log('\n📝 剧集内容包含：');
    console.log('- 已知角色：张小雅（高中生，马尾辫，甜美笑容）');
    console.log('- 已知角色：王美丽（咖啡店老板，瓜子脸，波浪卷发）');
    console.log('- 已知角色：李明轩（大学教授，方脸，花白短发）');
    console.log('- 新角色：林志强（程序员，戴眼镜，白T恤）');
    
    console.log('\n🎯 测试目标：');
    console.log('1. 验证已知角色的一致性匹配');
    console.log('2. 检查一致性评分计算');
    console.log('3. 识别新角色');
    console.log('4. 生成一致性约束建议');
    
    return episode.id;
    
  } catch (error) {
    console.error('❌ 创建测试剧集失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestEpisode();
