/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/[id]/analyze/route";
exports.ids = ["app/api/projects/[id]/analyze/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/[id]/analyze/route.ts */ \"(rsc)/./src/app/api/projects/[id]/analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/[id]/analyze/route\",\n        pathname: \"/api/projects/[id]/analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/[id]/analyze/route\"\n    },\n    resolvedPagePath: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\api\\\\projects\\\\[id]\\\\analyze\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VideoRun_12_VideoRun_12_novel_to_video_ai_src_app_api_projects_id_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/projects/[id]/analyze/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/projects/[id]/analyze/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n// AI分析提示词\nconst CHARACTER_ANALYSIS_PROMPT = `\n你是一个专业的小说分析专家和角色设计师。请仔细分析以下小说内容，提取所有重要角色的详细信息。\n\n重要要求：\n1. 每个字段都必须填写详细内容，不能为空或过于简单\n2. 描述要准确反映小说中的角色特征\n3. 为后续的角色形象生成提供充分的细节支撑\n4. 必须返回有效的JSON格式，不能包含任何其他文字说明\n5. JSON中的字符串不能包含未转义的换行符或引号\n\n请按照以下JSON格式返回角色信息，每个角色包含以下字段：\n\n- name: 角色姓名（从小说中准确提取）\n- identity: 身份信息（至少80字）\n  * 必须包含：职业/身份、社会地位、家庭背景、年龄段、所属势力/组织等\n  * 要体现角色在故事中的重要性和作用\n- personality: 性格特点（至少120字）\n  * 必须包含：核心性格特征、行为习惯、说话方式、处事风格、内心特点、情感倾向等\n  * 要深入分析角色的心理层面和行为动机\n- physique: 身材特征（至少100字）\n  * 必须包含：身高、体型、体态、肌肉线条、整体身材比例、体重感觉、身体姿态等\n  * 要符合角色的身份地位和性格特点，体现角色的身体特征\n- facial: 五官特征（至少100字）\n  * 必须包含：脸型、眼睛形状/颜色/大小、鼻子特征、嘴唇样式、眉毛特征、肤色、面部轮廓等\n  * 基于小说描述，如无明确描述则根据角色身份和性格合理推断\n- hairstyle: 发型样式（至少80字）\n  * 必须包含：发型样式、发色、发长、发质、刘海、发量、发型与身份的匹配度等\n  * 要符合角色的时代背景、身份地位和个人喜好\n- clothing: 服饰风格（至少100字）\n  * 必须包含：服装风格、颜色搭配、材质、配饰、鞋子、整体穿着品味等\n  * 要体现角色的身份地位、经济状况、个人喜好和时代特色\n\n重要：只返回纯JSON，不要任何解释文字。所有字符串内容不能包含换行符、引号等特殊字符。格式如下：\n{\"characters\":[{\"name\":\"角色名称\",\"identity\":\"详细身份信息80字以上\",\"personality\":\"详细性格特点120字以上\",\"physique\":\"详细身材特征100字以上\",\"facial\":\"详细五官特征100字以上\",\"hairstyle\":\"详细发型样式80字以上\",\"clothing\":\"详细服饰风格100字以上\"}]}\n\n小说内容：\n`;\nconst EPISODE_ANALYSIS_PROMPT = `\n你是一个专业的小说分析专家。请仔细分析以下小说内容，按章节拆分成独立的剧集。\n\n重要要求：\n1. 必须返回有效的JSON格式，不能包含任何其他文字说明\n2. JSON中的字符串不能包含未转义的换行符或引号\n3. 每个章节应该是一个完整的故事单元\n4. 标题要简洁明了，体现该章节的核心内容\n5. 内容要保持原文的完整性和连贯性\n6. 按照原文的章节顺序进行拆分\n\n请按照以下JSON格式返回剧集信息：\n- title: 故事标题（一级目录，章节标题）\n- content: 详细剧情（二级目录，完整的章节内容，用\\\\n表示换行）\n\n重要：只返回纯JSON，不要任何解释文字。内容中不能包含引号、换行符等特殊字符，用句号分段。格式如下：\n{\"episodes\":[{\"title\":\"章节标题\",\"content\":\"完整章节内容用句号分段不含特殊字符\"}]}\n\n小说内容：\n`;\n// 项目AI分析\nasync function POST(request, { params }) {\n    try {\n        const { id: projectId } = await params;\n        // 获取请求体中的模型ID\n        let requestBody = {};\n        try {\n            requestBody = await request.json();\n        } catch (e) {\n        // 如果没有请求体，使用默认值\n        }\n        const { modelId, customPrompt } = requestBody;\n        // 获取项目信息\n        const project = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findUnique({\n            where: {\n                id: projectId\n            }\n        });\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目不存在'\n            }, {\n                status: 404\n            });\n        }\n        if (!project.content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '项目中没有小说内容'\n            }, {\n                status: 400\n            });\n        }\n        // 获取AI配置\n        let aiConfig;\n        if (modelId) {\n            // 如果指定了模型ID，使用该模型配置\n            aiConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.findUnique({\n                where: {\n                    id: modelId\n                }\n            });\n            if (!aiConfig) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '指定的模型配置不存在'\n                }, {\n                    status: 400\n                });\n            }\n            if (!aiConfig.enabled) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '指定的模型未启用'\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // 如果没有指定模型ID，使用第一个启用的模型\n            aiConfig = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.aIConfig.findFirst({\n                where: {\n                    enabled: true\n                }\n            });\n            if (!aiConfig) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: '请先配置并启用AI模型'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // 更新项目状态为分析中\n        await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.project.update({\n            where: {\n                id: projectId\n            },\n            data: {\n                status: 'analyzing'\n            }\n        });\n        // 如果是重新分析，先删除旧的角色和剧集数据\n        await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.character.deleteMany({\n            where: {\n                projectId\n            }\n        });\n        await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.episode.deleteMany({\n            where: {\n                projectId\n            }\n        });\n        try {\n            // 构建角色分析提示词（包含增强提示词）\n            let characterPrompt = CHARACTER_ANALYSIS_PROMPT;\n            if (customPrompt && customPrompt.trim()) {\n                characterPrompt = `${CHARACTER_ANALYSIS_PROMPT}\\n\\n增强要求：${customPrompt}\\n\\n`;\n            }\n            characterPrompt += project.content;\n            // 1. 分析角色信息\n            const characterResponse = await callAIModel(aiConfig, characterPrompt);\n            let characters = [];\n            try {\n                const cleanedResponse = cleanJsonResponse(characterResponse);\n                const characterData = JSON.parse(cleanedResponse);\n                characters = characterData.characters || [];\n            } catch (parseError) {\n                console.error('角色信息解析失败:', parseError);\n                console.error('原始响应:', characterResponse);\n                // 如果解析失败，尝试提取部分信息\n                characters = [];\n            }\n            // 构建剧集分析提示词（包含增强提示词）\n            let episodePrompt = EPISODE_ANALYSIS_PROMPT;\n            if (customPrompt && customPrompt.trim()) {\n                episodePrompt = `${EPISODE_ANALYSIS_PROMPT}\\n\\n增强要求：${customPrompt}\\n\\n`;\n            }\n            episodePrompt += project.content;\n            // 2. 分析剧集信息\n            const episodeResponse = await callAIModel(aiConfig, episodePrompt);\n            let episodes = [];\n            try {\n                const cleanedResponse = cleanJsonResponse(episodeResponse);\n                const episodeData = JSON.parse(cleanedResponse);\n                episodes = episodeData.episodes || [];\n            } catch (parseError) {\n                console.error('剧集信息解析失败:', parseError);\n                console.error('原始响应:', episodeResponse);\n                // 如果解析失败，尝试简单拆分\n                episodes = [];\n            }\n            // 3. 保存角色信息到数据库\n            const savedCharacters = [];\n            for (const character of characters){\n                if (character.name) {\n                    const savedCharacter = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.character.create({\n                        data: {\n                            projectId,\n                            name: character.name,\n                            identity: character.identity || null,\n                            personality: character.personality || null,\n                            physique: character.physique || null,\n                            facial: character.facial || null,\n                            hairstyle: character.hairstyle || null,\n                            clothing: character.clothing || null\n                        }\n                    });\n                    savedCharacters.push(savedCharacter);\n                }\n            }\n            // 4. 保存剧集信息到数据库\n            const savedEpisodes = [];\n            for(let i = 0; i < episodes.length; i++){\n                const episode = episodes[i];\n                if (episode.title && episode.content) {\n                    const savedEpisode = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.episode.create({\n                        data: {\n                            projectId,\n                            title: episode.title,\n                            content: episode.content,\n                            orderIndex: i + 1,\n                            status: 'created'\n                        }\n                    });\n                    savedEpisodes.push(savedEpisode);\n                }\n            }\n            // 5. 更新项目状态为完成\n            const updatedProject = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.project.update({\n                where: {\n                    id: projectId\n                },\n                data: {\n                    status: 'completed',\n                    updatedAt: new Date()\n                },\n                include: {\n                    characters: true,\n                    episodes: true\n                }\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    project: updatedProject,\n                    characters: savedCharacters,\n                    episodes: savedEpisodes,\n                    analysis: {\n                        characterCount: savedCharacters.length,\n                        episodeCount: savedEpisodes.length\n                    }\n                },\n                message: 'AI分析完成'\n            });\n        } catch (aiError) {\n            // AI分析失败，恢复项目状态\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.project.update({\n                where: {\n                    id: projectId\n                },\n                data: {\n                    status: 'uploaded'\n                }\n            });\n            console.error('AI分析失败:', aiError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'AI分析失败，请检查模型配置或稍后重试'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('项目分析失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: '项目分析失败'\n        }, {\n            status: 500\n        });\n    }\n}\n// 清理JSON响应，移除可能导致解析失败的字符\nfunction cleanJsonResponse(response) {\n    // 移除可能的前后缀文字，只保留JSON部分\n    let cleaned = response.trim();\n    // 查找JSON开始和结束位置\n    const jsonStart = cleaned.indexOf('{');\n    const jsonEnd = cleaned.lastIndexOf('}');\n    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {\n        cleaned = cleaned.substring(jsonStart, jsonEnd + 1);\n    }\n    // 更安全的JSON清理方法\n    try {\n        // 尝试直接解析，如果成功就返回\n        JSON.parse(cleaned);\n        return cleaned;\n    } catch (error) {\n        // 如果解析失败，进行更激进的清理\n        console.log('JSON解析失败，进行清理:', error.message);\n        // 使用更简单的方法：逐字符处理，正确处理字符串边界\n        let result = '';\n        let inString = false;\n        let escapeNext = false;\n        for(let i = 0; i < cleaned.length; i++){\n            const char = cleaned[i];\n            const prevChar = i > 0 ? cleaned[i - 1] : '';\n            if (escapeNext) {\n                result += char;\n                escapeNext = false;\n                continue;\n            }\n            if (char === '\\\\') {\n                result += char;\n                escapeNext = true;\n                continue;\n            }\n            if (char === '\"' && prevChar !== '\\\\') {\n                inString = !inString;\n                result += char;\n                continue;\n            }\n            if (inString) {\n                // 在字符串内部，转义特殊字符\n                if (char === '\\n') {\n                    result += '\\\\n';\n                } else if (char === '\\r') {\n                    result += '\\\\r';\n                } else if (char === '\\t') {\n                    result += '\\\\t';\n                } else {\n                    result += char;\n                }\n            } else {\n                // 在字符串外部，正常处理\n                result += char;\n            }\n        }\n        // 移除末尾逗号\n        result = result.replace(/,\\s*}/g, '}').replace(/,\\s*]/g, ']');\n        return result;\n    }\n}\n// 调用AI模型生成内容\nasync function callAIModel(aiConfig, prompt) {\n    const { provider, apiKey, model } = aiConfig;\n    switch(provider){\n        case 'deepseek':\n            return await callDeepSeek(apiKey, model, prompt);\n        case 'openai':\n            return await callOpenAI(apiKey, model, prompt);\n        case 'claude':\n            return await callClaude(apiKey, model, prompt);\n        case 'tongyi':\n            return await callTongyi(apiKey, model, prompt);\n        case 'doubao':\n            return await callDoubao(apiKey, model, prompt);\n        default:\n            throw new Error(`不支持的AI提供商: ${provider}`);\n    }\n}\n// DeepSeek API调用\nasync function callDeepSeek(apiKey, model, prompt) {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), 300000) // 5分钟超时\n    ;\n    try {\n        const response = await fetch('https://api.deepseek.com/v1/chat/completions', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model: model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 8000\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            throw new Error(`DeepSeek API调用失败: ${response.statusText}`);\n        }\n        const data = await response.json();\n        const content = data.choices[0]?.message?.content;\n        if (!content) {\n            throw new Error('AI返回内容为空');\n        }\n        return content;\n    } catch (error) {\n        clearTimeout(timeoutId);\n        if (error.name === 'AbortError') {\n            throw new Error('AI调用超时，请稍后重试');\n        }\n        throw error;\n    }\n}\n// OpenAI API调用\nasync function callOpenAI(apiKey, model, prompt) {\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n            model: model,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0.1,\n            max_tokens: 4000\n        })\n    });\n    if (!response.ok) {\n        throw new Error(`OpenAI API调用失败: ${response.statusText}`);\n    }\n    const data = await response.json();\n    const content = data.choices[0]?.message?.content;\n    if (!content) {\n        throw new Error('AI返回内容为空');\n    }\n    return content;\n}\n// Claude API调用\nasync function callClaude(apiKey, model, prompt) {\n    // Claude API实现\n    throw new Error('Claude API暂未实现');\n}\n// 通义AI调用\nasync function callTongyi(apiKey, model, prompt) {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), 300000) // 5分钟超时\n    ;\n    try {\n        // 根据模型选择正确的API端点\n        let apiUrl = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';\n        let requestBody = {\n            model: model,\n            input: {\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            },\n            parameters: {\n                temperature: 0.3,\n                max_tokens: 8000\n            }\n        };\n        // 对于某些模型，可能需要不同的请求格式\n        if (model.includes('turbo')) {\n            // 确保使用正确的请求格式\n            requestBody = {\n                model: model,\n                input: {\n                    messages: [\n                        {\n                            role: 'user',\n                            content: prompt\n                        }\n                    ]\n                },\n                parameters: {\n                    temperature: 0.3,\n                    max_tokens: 8000,\n                    result_format: 'message'\n                }\n            };\n        }\n        console.log(`🔍 调用通义API: ${apiUrl}`);\n        console.log(`📝 模型: ${model}`);\n        console.log(`🔑 API密钥前缀: ${apiKey.substring(0, 8)}...`);\n        const response = await fetch(apiUrl, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${apiKey}`,\n                'X-DashScope-SSE': 'disable'\n            },\n            body: JSON.stringify(requestBody),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`❌ 通义API响应错误 (${response.status}):`, errorText);\n            let errorData = {};\n            try {\n                errorData = JSON.parse(errorText);\n            } catch (e) {\n                // 如果不是JSON格式，直接使用错误文本\n                if (errorText.includes('url error')) {\n                    throw new Error('通义API URL错误，请检查模型名称和API密钥是否正确');\n                }\n                throw new Error(`通义AI API调用失败: ${errorText || response.statusText}`);\n            }\n            // 处理特定的错误类型\n            if (errorData.code === 'Arrearage') {\n                throw new Error('通义API账户欠费，请前往阿里云控制台充值后重试');\n            } else if (errorData.code === 'InvalidApiKey') {\n                throw new Error('通义API密钥无效，请检查配置');\n            } else if (errorData.code === 'RateLimitExceeded') {\n                throw new Error('通义API调用频率超限，请稍后重试');\n            } else if (errorData.message && errorData.message.includes('url error')) {\n                throw new Error('通义API URL错误，请检查模型名称和API密钥是否正确');\n            } else {\n                throw new Error(`通义AI API调用失败: ${errorData.message || response.statusText}`);\n            }\n        }\n        const data = await response.json();\n        // 检查响应中的错误\n        if (data.code && data.code !== 'Success') {\n            if (data.code === 'Arrearage') {\n                throw new Error('通义API账户欠费，请前往阿里云控制台充值后重试');\n            } else {\n                throw new Error(`通义AI错误: ${data.message || data.code}`);\n            }\n        }\n        const content = data.output?.text;\n        if (!content) {\n            throw new Error('通义AI返回内容为空');\n        }\n        return content;\n    } catch (error) {\n        clearTimeout(timeoutId);\n        if (error.name === 'AbortError') {\n            throw new Error('通义AI调用超时，请稍后重试');\n        }\n        throw error;\n    }\n}\n// 豆包 (Doubao) API调用\nasync function callDoubao(apiKey, model, prompt) {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), 300000) // 5分钟超时\n    ;\n    try {\n        console.log(`🔍 调用豆包API: https://ark.cn-beijing.volces.com/api/v3/chat/completions`);\n        console.log(`📝 模型: ${model}`);\n        console.log(`🔑 API密钥前缀: ${apiKey.substring(0, 8)}...`);\n        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model: model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 8000\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`❌ 豆包API响应错误 (${response.status}):`, errorText);\n            let errorData = {};\n            try {\n                errorData = JSON.parse(errorText);\n            } catch (e) {\n                throw new Error(`豆包API调用失败: ${errorText || response.statusText}`);\n            }\n            // 处理特定的错误类型\n            if (errorData.error?.code === 'invalid_api_key') {\n                throw new Error('豆包API密钥无效，请检查配置');\n            } else if (errorData.error?.code === 'rate_limit_exceeded') {\n                throw new Error('豆包API调用频率超限，请稍后重试');\n            } else if (errorData.error?.code === 'insufficient_quota') {\n                throw new Error('豆包API配额不足，请检查账户余额');\n            } else {\n                throw new Error(`豆包API调用失败: ${errorData.error?.message || response.statusText}`);\n            }\n        }\n        const data = await response.json();\n        const content = data.choices[0]?.message?.content;\n        if (!content) {\n            throw new Error('豆包AI返回内容为空');\n        }\n        console.log(`✅ 豆包API调用成功，返回内容长度: ${content.length}`);\n        return content;\n    } catch (error) {\n        clearTimeout(timeoutId);\n        if (error.name === 'AbortError') {\n            throw new Error('豆包AI调用超时，请稍后重试');\n        }\n        console.error('❌ 豆包API调用失败:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/[id]/analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQ1hGLGdCQUFnQkUsTUFBTSxJQUN0QixJQUFJSCx3REFBWUEsQ0FBQztJQUNmSSxLQUFLO1FBQUM7S0FBUTtBQUNoQixHQUFFO0FBRUosSUFBSUMsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGxpYlxcZGIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPVxuICBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&page=%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2F%5Bid%5D%2Fanalyze%2Froute.ts&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();