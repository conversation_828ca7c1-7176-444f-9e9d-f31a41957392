# SkyReels-V2 本地大模型集成说明

## 概述

本文档介绍如何在VideoRun项目中集成和使用本地部署的SkyReels-V2文生视频模型。

## 前置条件

### 1. SkyReels-V2模型准备
确保您的本地SkyReels-V2模型已正确部署在 `D:\SkyworkSkyReels-V2-T2V` 目录下，并且API服务器正在运行。

### 2. 启动SkyReels服务器
```bash
cd D:\SkyworkSkyReels-V2-T2V
python start_server.py
```

服务器将在 `http://localhost:8000` 启动。

### 3. 验证服务器状态
访问 `http://localhost:8000/health` 确认服务器正常运行。

## 集成功能

### 1. 支持的功能
- ✅ 文本到视频生成 (T2V)
- ✅ 多种视频时长 (4秒-30秒)
- ✅ 540P分辨率输出
- ✅ 实时生成进度监控
- ✅ 任务状态查询
- ✅ 视频下载

### 2. 模型特点
- **模型名称**: SkyReels-V2-DF-1.3B-540P
- **类型**: Diffusion Forcing (无限长度视频生成)
- **分辨率**: 544×960 (540P)
- **最大帧数**: 97帧(4秒) 到 1457帧(60秒)
- **特色**: 支持无限长度视频生成、高质量文本到视频转换

## 配置步骤

### 1. 在前端配置SkyReels模型

1. 访问 `http://localhost:3000/models` (模型配置页面)
2. 找到 "SkyReels-V2 (本地模型)" 分组
3. 配置 "SkyReels-V2 本地模型":
   - **API服务器地址**: `http://localhost:8000`
   - **模型名称**: `SkyReels-V2-DF-1.3B-540P`
   - **启用状态**: 开启
4. 点击"测试"按钮验证连接
5. 测试成功后点击"保存"

### 2. 验证配置
运行测试脚本验证集成是否成功：
```bash
cd VideoRun-12/novel-to-video-ai
node test-skyreels-integration.js
```

## 使用方法

### 1. 在视频生成中使用SkyReels

1. 上传小说并完成分析
2. 在剧集页面选择要生成视频的剧集
3. 点击"生成视频"
4. 系统会自动使用配置的SkyReels模型进行视频生成

### 2. 视频生成参数

SkyReels支持以下生成参数：
- **提示词**: 从剧情内容自动生成
- **帧数**: 根据视频时长自动计算
  - 4秒: 97帧
  - 10秒: 257帧
  - 15秒: 377帧
  - 30秒: 737帧
- **引导强度**: 6.0 (默认)
- **帧率**: 24 FPS
- **分辨率**: 540P

### 3. 生成流程

1. **任务创建**: 系统向SkyReels API发送生成请求
2. **状态监控**: 每5秒查询一次生成进度
3. **完成通知**: 生成完成后自动更新数据库
4. **视频存储**: 视频文件保存到本地路径

## API接口

### 1. SkyReels客户端方法

```typescript
// 创建客户端
const client = new SkyReelsClient(config)

// 测试连接
await client.testConnection()

// 生成视频
const task = await client.generateVideo({
  prompt: "视频描述",
  num_frames: 257,
  guidance_scale: 6.0,
  fps: 24,
  resolution: "540P"
})

// 查询状态
const status = await client.getTaskStatus(taskId)

// 等待完成
const videoPath = await client.generateAndWait(params)
```

### 2. 集成到视频生成流程

SkyReels已集成到以下API中：
- `/api/ai/generate-story-video` - 剧情视频生成
- `/api/ai/generate-video-with-consistency` - 一致性视频生成

## 性能优化

### 1. 内存优化
- SkyReels配置中启用 `offload: true` 将模型组件卸载到CPU
- 调整 `base_num_frames` 控制峰值内存使用

### 2. 速度优化
- 启用 `teacache: true` 加速推理
- 减少 `inference_steps` 加快生成速度
- 使用较少的帧数生成更短视频

### 3. 并发控制
- 通过 `max_concurrent_requests` 控制同时处理的请求数量
- 调整 `request_timeout` 设置请求超时时间

## 故障排除

### 1. 常见问题

**连接失败**
- 检查SkyReels服务器是否启动
- 确认端口8000未被占用
- 验证防火墙设置

**生成失败**
- 检查模型文件是否完整
- 确认GPU/CPU资源充足
- 查看SkyReels服务器日志

**内存不足**
- 启用模型卸载 (`offload: true`)
- 减少 `base_num_frames`
- 关闭其他占用GPU内存的程序

### 2. 日志查看

SkyReels服务器日志会显示详细的错误信息，包括：
- 模型加载状态
- 请求处理过程
- 错误堆栈信息

### 3. 测试工具

使用提供的测试脚本进行诊断：
```bash
node test-skyreels-integration.js
```

## 技术架构

### 1. 组件结构
```
VideoRun项目
├── src/lib/skyreels.ts          # SkyReels客户端
├── src/types/index.ts           # 类型定义
├── src/components/AIConfigPanel.tsx # 配置界面
└── src/app/api/ai/generate-story-video/route.ts # 视频生成API
```

### 2. 数据流
```
前端配置 → 数据库存储 → API调用 → SkyReels服务器 → 视频生成 → 结果返回
```

## 扩展功能

### 1. 自定义参数
可以通过修改配置文件 `D:\SkyworkSkyReels-V2-T2V\config.yaml` 调整：
- 生成参数
- 性能设置
- 输出格式

### 2. 批量处理
支持批量视频生成，提高处理效率。

### 3. 质量控制
集成一致性验证功能，确保生成视频质量。

## 总结

SkyReels-V2本地模型已成功集成到VideoRun项目中，提供了高质量的文生视频功能。通过本地部署，您可以：

- 🚀 享受更快的生成速度
- 🔒 保护数据隐私安全
- 💰 节省API调用成本
- 🎛️ 完全控制生成参数

如有问题，请参考故障排除部分或查看相关日志文件。
