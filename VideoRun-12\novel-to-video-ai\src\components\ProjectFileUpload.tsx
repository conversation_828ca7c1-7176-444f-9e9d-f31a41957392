'use client'

import { useCallback, useState, useEffect } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, FileText, X, CheckCircle, AlertCircle, ChevronDown } from 'lucide-react'
import { FileParser, FileParseError } from '@/lib/fileParser'
import ModelSelector from './ModelSelector'

interface ProjectFileUploadProps {
  projectId: string
  project?: any
  onUploadSuccess: (project: any) => void
  onUploadError: (error: string) => void
}

export default function ProjectFileUpload({
  projectId,
  project,
  onUploadSuccess,
  onUploadError
}: ProjectFileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fileContent, setFileContent] = useState<string>('')
  const [isReading, setIsReading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [selectedModel, setSelectedModel] = useState<string>('')

  // 增强提示词相关状态
  const [showPromptDropdown, setShowPromptDropdown] = useState(false)
  const [enhancePrompt, setEnhancePrompt] = useState('')
  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')

  // 文件解析函数
  const parseFileContent = useCallback(async (file: File): Promise<string> => {
    try {
      return await FileParser.parseFile(file)
    } catch (error) {
      if (error instanceof FileParseError) {
        throw new Error(error.message)
      }
      throw new Error('文件解析失败')
    }
  }, [])

  // 文件拖拽处理
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    setError(null)
    setIsReading(true)

    try {
      // 解析文件内容（包含格式验证）
      const content = await parseFileContent(file)

      setSelectedFile(file)
      setFileContent(content)
    } catch (error) {
      setError(error instanceof Error ? error.message : '文件读取失败')
    } finally {
      setIsReading(false)
    }
  }, [parseFileContent])

  // 配置 dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
    },
    multiple: false,
    disabled: isUploading || isReading || isAnalyzing,
  })

  // 检查是否需要确认重新上传
  const handleUploadClick = () => {
    if (!selectedFile || !fileContent) return

    // 如果项目已有内容，显示确认对话框
    if (project?.content && project.content.trim()) {
      setShowConfirmDialog(true)
    } else {
      handleUpload()
    }
  }

  // 确认重新上传
  const handleConfirmUpload = () => {
    setShowConfirmDialog(false)
    handleUpload()
  }

  // 取消重新上传
  const handleCancelUpload = () => {
    setShowConfirmDialog(false)
  }

  // 上传文件
  const handleUpload = async () => {
    if (!selectedFile || !fileContent) return

    setIsUploading(true)
    setError(null)

    try {
      // 1. 上传文件到项目
      const uploadResponse = await fetch(`/api/projects/${projectId}/upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: selectedFile.name,
          content: fileContent,
        }),
      })

      const uploadData = await uploadResponse.json()

      if (!uploadData.success) {
        throw new Error(uploadData.error || '文件上传失败')
      }

      setUploadSuccess(true)
      setIsAnalyzing(true)

      // 2. 启动AI分析
      const analyzeResponse = await fetch(`/api/projects/${projectId}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId: selectedModel,
          customPrompt: savedEnhancePrompt || undefined
        })
      })

      const analyzeData = await analyzeResponse.json()

      if (analyzeData.success) {
        setIsAnalyzing(false)
        onUploadSuccess(analyzeData.data)
      } else {
        throw new Error(analyzeData.error || 'AI分析失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      setError(errorMessage)
      onUploadError(errorMessage)
      setIsAnalyzing(false)
    } finally {
      setIsUploading(false)
    }
  }

  // 清除选择的文件
  const clearFile = () => {
    setSelectedFile(null)
    setFileContent('')
    setError(null)
    setUploadSuccess(false)
  }

  // 保存增强提示词
  const handleSaveEnhancePrompt = () => {
    setSavedEnhancePrompt(enhancePrompt)
    setShowPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_upload_${projectId}`
    localStorage.setItem(storageKey, enhancePrompt)
  }

  // 从localStorage加载增强提示词
  useEffect(() => {
    if (projectId) {
      const storageKey = `enhance_prompt_upload_${projectId}`
      const savedPrompt = localStorage.getItem(storageKey)
      if (savedPrompt) {
        setSavedEnhancePrompt(savedPrompt)
        setEnhancePrompt(savedPrompt)
      }
    }
  }, [projectId])

  // 如果上传成功，显示成功状态
  if (uploadSuccess && !isAnalyzing) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">上传成功！</h3>
        <p className="mt-1 text-sm text-gray-500">
          文件已上传并完成AI分析，请查看角色和剧集信息
        </p>
      </div>
    )
  }

  // 如果正在分析，显示分析状态
  if (isAnalyzing) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
        <h3 className="mt-2 text-lg font-medium text-gray-900">AI分析中...</h3>
        <p className="mt-1 text-sm text-gray-500">
          正在提取角色信息和拆分剧集，请稍候
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 确认重新上传对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-yellow-400" />
              <h3 className="text-lg font-medium text-gray-900 mt-2">确认重新上传</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  小说已上传，如果点击确认重新上传将删掉之前的人物信息、剧情信息。
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <button
                  onClick={handleCancelUpload}
                  className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-400"
                >
                  取消
                </button>
                <button
                  onClick={handleConfirmUpload}
                  className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700"
                >
                  确认重新上传
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">上传失败</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {!selectedFile ? (
        /* 文件选择区域 */
        <div
          {...getRootProps()}
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent cursor-pointer
            ${isDragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'}
            ${(isUploading || isReading) ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input {...getInputProps()} />
          
          {isReading ? (
            <div className="space-y-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
              <p className="text-sm text-gray-600">正在读取文件...</p>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              {isDragActive ? (
                <p className="text-sm text-purple-600">
                  松开鼠标上传文件
                </p>
              ) : (
                <>
                  <p className="text-gray-600 mb-2">
                    拖拽文件到此处，或点击选择文件
                  </p>
                  <p className="text-sm text-gray-500">
                    支持 .txt、.docx 格式，最大 50MB
                  </p>
                </>
              )}
            </div>
          )}
        </div>
      ) : (
        /* 文件信息和上传 */
        <div className="space-y-4">
          {/* 文件信息 */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <FileText className="mr-3 text-blue-500" size={20} />
              <div>
                <p className="font-medium">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {FileParser.getFileTypeDescription(selectedFile)} • {FileParser.formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            <button
              onClick={clearFile}
              className="p-1 text-gray-400 hover:text-gray-600"
              disabled={isUploading}
            >
              <X size={20} />
            </button>
          </div>

          {/* 内容预览 */}
          <div className="bg-white border rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">内容预览</h4>
            <div className="text-sm text-gray-600 max-h-32 overflow-y-auto">
              {fileContent.substring(0, 500)}
              {fileContent.length > 500 && '...'}
            </div>
            <p className="text-xs text-gray-500 mt-2">
              共 {fileContent.length.toLocaleString()} 个字符
            </p>
          </div>

          {/* 模型选择器 */}
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-sm font-medium text-gray-700">分析模型:</span>
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              className="flex-1"
            />
          </div>

          {/* 上传按钮 */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={clearFile}
              disabled={isUploading}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              重新选择
            </button>

            <div className="relative flex">
              <button
                onClick={handleUploadClick}
                disabled={isUploading || !fileContent || !selectedModel}
                className="px-4 py-2 border border-transparent rounded-l-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                    上传中...
                  </>
                ) : (
                  '上传并分析'
                )}
              </button>

              <button
                onClick={() => setShowPromptDropdown(!showPromptDropdown)}
                disabled={isUploading || !fileContent || !selectedModel}
                className="px-2 py-2 border border-transparent rounded-r-md border-l border-purple-500 shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronDown size={14} />
              </button>

              {/* 增强提示词下拉框 */}
              {showPromptDropdown && (
                <div className="absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="p-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      增强提示词设置
                    </label>
                    <textarea
                      value={enhancePrompt}
                      onChange={(e) => setEnhancePrompt(e.target.value)}
                      placeholder="输入增强提示词，用于优化AI分析效果..."
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none"
                      rows={4}
                    />

                    {/* 保存按钮 */}
                    <div className="mt-3">
                      <button
                        onClick={handleSaveEnhancePrompt}
                        className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        保存增强提示词
                      </button>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex justify-end mt-3">
                      <button
                        onClick={() => setShowPromptDropdown(false)}
                        className="text-sm text-gray-600 hover:text-gray-800"
                      >
                        关闭
                      </button>
                    </div>

                    {/* 当前保存的提示词预览 */}
                    {savedEnhancePrompt && (
                      <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                        <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                        <div className="max-h-16 overflow-y-auto">
                          {savedEnhancePrompt}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 上传说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">
          上传后AI将自动：
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 提取角色信息（名称、五官、身份、外貌、性格、隐线）</li>
          <li>• 按章节拆分剧集</li>
          <li>• 生成故事标题和详细剧情</li>
          <li>• 为后续视频生成做准备</li>
        </ul>
      </div>
    </div>
  )
}
