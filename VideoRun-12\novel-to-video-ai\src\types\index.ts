// AI配置相关类型
export interface AIConfig {
  id: string
  provider: 'deepseek' | 'openai' | 'claude' | 'tongyi' | 'minimax' | 'zhipu' | 'doubao' | 'skyreels'
  apiKey: string
  model: string
  temperature: number
  maxTokens: number
  topP: number
  status: 'connected' | 'disconnected' | 'error'
  lastTest?: Date
  createdAt: Date
  updatedAt: Date
}

// 项目相关类型
export interface Project {
  id: string
  name: string
  description?: string
  fileName?: string
  content?: string
  status: 'created' | 'uploaded' | 'analyzing' | 'completed'
  createdAt: Date
  updatedAt: Date
  characters?: Character[]
  episodes?: Episode[]
}

// 角色相关类型（扩展支持一致性功能）
export interface Character {
  id: string
  projectId: string
  name: string
  identity?: string    // 身份信息
  personality?: string // 性格特点
  physique?: string    // 身材特征
  facial?: string      // 五官特征
  hairstyle?: string   // 发型样式
  clothing?: string    // 服饰风格
  // AI生成的三视图
  generatedImages?: {
    front?: string     // 正面视图base64或URL
    side?: string      // 侧面视图base64或URL
    back?: string      // 背面视图base64或URL
  }

  // 新增：详细DNA信息
  detailedDNA?: DetailedCharacterDNA

  // 新增：一致性相关字段
  consistencyScore?: number           // 一致性评分 (0.0-1.0)
  consistencySettings?: ConsistencySettings  // 一致性设置
  referenceImages?: ReferenceImageSet        // 参考图像集合

  // 新增：全局角色标识
  globalCharacterId?: string          // 关联全局角色库的ID
  isGlobalCharacter?: boolean         // 是否为全局角色

  createdAt: Date
  updatedAt: Date
}

// 详细角色DNA类型
export interface DetailedCharacterDNA {
  facial: DetailedFacialFeatures
  physique: DetailedPhysique
  clothing: DetailedClothing
  uniqueIdentifiers: string[]         // 独特标识
  standardPrompt: string              // 标准化提示词
}

// 详细面部特征
export interface DetailedFacialFeatures {
  faceShape: string      // 脸型：圆脸/方脸/瓜子脸等
  eyeShape: string       // 眼型：丹凤眼/杏眼/桃花眼等
  eyeColor: string       // 眼色：黑色/棕色等
  noseShape: string      // 鼻型：高挺/小巧等
  mouthShape: string     // 嘴型：薄唇/厚唇等
  skinTone: string       // 肤色：白皙/小麦色等
  facialFeatures: string // 其他特征：酒窝/雀斑/痣等
}

// 详细体型特征
export interface DetailedPhysique {
  height: string         // 身高描述
  build: string          // 体型：纤细/健壮等
  posture: string        // 体态特征
}

// 详细服装特征
export interface DetailedClothing {
  style: string          // 服装风格
  colors: string[]       // 常用颜色
  accessories: string[]  // 配饰特征
}

// 参考图像集合
export interface ReferenceImageSet {
  standardPortrait?: string    // 标准正面头像
  profileView?: string        // 侧面头像
  fullBody?: string          // 全身图
  expressions?: {
    neutral?: string         // 中性表情
    happy?: string          // 开心表情
    sad?: string            // 悲伤表情
    angry?: string          // 愤怒表情
  }
  angles?: {
    front?: string          // 正面
    leftProfile?: string    // 左侧面
    rightProfile?: string   // 右侧面
    threeQuarter?: string   // 四分之三角度
  }
  consistencyScore?: number   // 参考图像一致性评分
}

// 一致性设置
export interface ConsistencySettings {
  textWeight: number          // 文本约束权重 (0.0-1.0)
  imageWeight: number         // 图像约束权重 (0.0-1.0)
  consistencyThreshold: number // 一致性阈值 (0.0-1.0)
  mode: 'plot' | 'consistency' | 'hybrid'  // 一致性模式
}

// 一致性验证记录
export interface ConsistencyValidation {
  id: string
  characterId: string
  episodeId?: string
  textConsistencyScore?: number
  imageConsistencyScore?: number
  overallScore?: number
  validationDetails?: any
  issuesFound?: string[]
  textWeight?: number
  imageWeight?: number
  generatedImageUrl?: string
  createdAt: Date
}

// 剧集相关类型
export interface Episode {
  id: string
  projectId: string
  title: string
  content: string
  orderIndex: number
  status: 'created' | 'analyzed' | 'video_generated'
  createdAt: Date
  updatedAt: Date
  plotInfo?: PlotInfo
}

// 剧情信息相关类型
export interface PlotInfo {
  id: string
  episodeId: string
  characters?: string   // 本集人物
  scenes?: string       // 场景信息
  events?: {
    normal?: string     // 正常状态
    conflict?: string   // 矛盾冲突
    escalation?: string // 升级事件
  }
  createdAt: Date
  updatedAt: Date
}

// API响应类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 文件上传相关类型
export interface UploadResponse {
  projectId: string
  fileName: string
  content: string
}

// AI分析相关类型
export interface AnalysisRequest {
  projectId: string
  content: string
  type: 'full' | 'characters' | 'episodes' | 'plot'
}

export interface AnalysisResponse {
  characters?: Character[]
  episodes?: Episode[]
  plotInfo?: PlotInfo
}

// 大模型提示词模板
export interface PromptTemplates {
  characterExtraction: string
  episodeSplit: string
  plotAnalysis: string
}

// 应用状态类型
export interface AppState {
  currentProject: Project | null
  aiConfig: AIConfig | null
  characters: Character[]
  episodes: Episode[]
  isAnalyzing: boolean
  isUploading: boolean
  error: string | null
}

// 组件Props类型
export interface FileUploadProps {
  onUpload: (file: File) => Promise<void>
  isUploading: boolean
}

export interface CharacterListProps {
  characters: Character[]
  onEdit?: (character: Character) => void
}

export interface EpisodeListProps {
  episodes: Episode[]
  onSelect?: (episode: Episode) => void
  onAnalyze?: (episodeId: string) => void
  onGenerateVideo?: (episodeId: string) => void
}

export interface AIConfigPanelProps {
  config: AIConfig | null
  onSave: (config: Partial<AIConfig>) => Promise<void>
  onTest: () => Promise<boolean>
}
