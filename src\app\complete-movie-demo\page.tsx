'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import CompleteMovieGenerator from '@/components/CompleteMovieGenerator'
import { Film, BookOpen, Users, Clock, Play } from 'lucide-react'

export default function CompleteMovieDemoPage() {
  const [projects, setProjects] = useState([])
  const [selectedProject, setSelectedProject] = useState(null)
  const [selectedEpisode, setSelectedEpisode] = useState(null)
  const [loading, setLoading] = useState(true)

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/projects')
      const result = await response.json()
      
      if (result.success) {
        setProjects(result.data)
        if (result.data.length > 0) {
          setSelectedProject(result.data[0])
          await loadEpisodes(result.data[0].id)
        }
      }
    } catch (error) {
      console.error('加载项目失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载剧集列表
  const loadEpisodes = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/episodes`)
      const result = await response.json()
      
      if (result.success && result.data.length > 0) {
        const episodesWithPlotInfo = result.data.filter(ep => ep.plotInfo)
        if (episodesWithPlotInfo.length > 0) {
          setSelectedEpisode(episodesWithPlotInfo[0])
        }
      }
    } catch (error) {
      console.error('加载剧集失败:', error)
    }
  }

  // 处理影视生成完成
  const handleMovieGenerated = (movieData: any) => {
    console.log('🎉 影视作品生成完成:', movieData)
    // 这里可以添加成功提示或其他处理
  }

  useEffect(() => {
    loadProjects()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>加载中...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Film className="h-8 w-8" />
          完整影视作品生成演示
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          将小说剧集转换为完整的影视作品，包含同步的视频、角色配音、背景音乐和环境音效
        </p>
      </div>

      {/* 功能特性展示 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Film className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <h3 className="font-semibold">视频生成</h3>
            <p className="text-sm text-muted-foreground">AI生成高质量视频片段</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <h3 className="font-semibold">角色配音</h3>
            <p className="text-sm text-muted-foreground">豆包TTS角色专属声音</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Play className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <h3 className="font-semibold">背景音乐</h3>
            <p className="text-sm text-muted-foreground">情绪匹配的背景音乐</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <h3 className="font-semibold">精确同步</h3>
            <p className="text-sm text-muted-foreground">音视频完美同步</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="generator" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generator">影视生成器</TabsTrigger>
          <TabsTrigger value="projects">项目管理</TabsTrigger>
        </TabsList>
        
        <TabsContent value="generator" className="space-y-6">
          {selectedProject && selectedEpisode ? (
            <div className="space-y-4">
              {/* 当前选择的项目和剧集信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    当前选择
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">项目信息</h4>
                      <p className="text-sm"><strong>名称:</strong> {selectedProject.name}</p>
                      <p className="text-sm"><strong>状态:</strong> 
                        <Badge className="ml-2">{selectedProject.status}</Badge>
                      </p>
                      {selectedProject.description && (
                        <p className="text-sm mt-2"><strong>描述:</strong> {selectedProject.description}</p>
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">剧集信息</h4>
                      <p className="text-sm"><strong>标题:</strong> {selectedEpisode.title}</p>
                      <p className="text-sm"><strong>状态:</strong> 
                        <Badge className="ml-2">{selectedEpisode.status}</Badge>
                      </p>
                      <p className="text-sm"><strong>内容长度:</strong> {selectedEpisode.content.length} 字符</p>
                      {selectedEpisode.plotInfo && (
                        <Badge className="mt-2" variant="outline">已分析剧情</Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 完整影视生成器 */}
              <CompleteMovieGenerator
                episodeId={selectedEpisode.id}
                episodeTitle={selectedEpisode.title}
                onMovieGenerated={handleMovieGenerated}
              />
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">没有可用的剧集</h3>
                <p className="text-muted-foreground mb-4">
                  请先上传小说项目并完成剧情分析，然后才能生成完整影视作品
                </p>
                <Button onClick={() => window.location.href = '/projects'}>
                  前往项目管理
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>项目列表</CardTitle>
              <p className="text-sm text-muted-foreground">
                选择一个已分析剧情的项目来生成完整影视作品
              </p>
            </CardHeader>
            <CardContent>
              {projects.length > 0 ? (
                <div className="space-y-4">
                  {projects.map((project) => (
                    <Card 
                      key={project.id} 
                      className={`cursor-pointer transition-colors ${
                        selectedProject?.id === project.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => {
                        setSelectedProject(project)
                        loadEpisodes(project.id)
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold">{project.name}</h4>
                            {project.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {project.description}
                              </p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              <Badge>{project.status}</Badge>
                              <span className="text-sm text-muted-foreground">
                                {new Date(project.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          {selectedProject?.id === project.id && (
                            <Badge variant="outline">已选择</Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">暂无项目</h3>
                  <p className="text-muted-foreground mb-4">
                    请先上传小说项目
                  </p>
                  <Button onClick={() => window.location.href = '/projects'}>
                    上传项目
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">生成流程</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>选择已分析剧情的项目和剧集</li>
                <li>配置生成设置（质量、音效等）</li>
                <li>点击"开始生成完整影视作品"</li>
                <li>系统自动完成视频、音频生成和同步</li>
                <li>下载或预览最终的影视作品</li>
              </ol>
            </div>
            <div>
              <h4 className="font-semibold mb-2">功能特性</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>自动视频片段生成和时间轴规划</li>
                <li>角色专属声音配音（豆包TTS）</li>
                <li>智能背景音乐匹配</li>
                <li>环境音效自动添加</li>
                <li>精确的音视频同步</li>
                <li>多种质量选项</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
