const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function replaceDoubaoModels() {
  try {
    // 1. 删除所有旧的豆包模型
    console.log('🗑️ 删除所有旧的豆包模型...');
    const deleteResult = await prisma.aIConfig.deleteMany({
      where: { provider: 'doubao' }
    });
    console.log('已删除', deleteResult.count, '个旧模型');

    // 2. 添加新的豆包模型
    console.log('➕ 添加新的豆包模型...');
    
    const newModels = [
      {
        provider: 'doubao',
        model: 'ep-20250624192235-zttm6',
        name: 'Doubao-Seedance-1.0-pro',
        description: '豆包专业版视频生成模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'disconnected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624195026-qjsmk',
        name: 'Doubao-Seedance-1.0-lite-i2v',
        description: '豆包图生视频模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: true,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'disconnected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624192345-5ccwj',
        name: 'Doubao-Seedance-1.0-lite-t2v',
        description: '豆包文生视频模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'disconnected'
      }
    ];

    for (const model of newModels) {
      await prisma.aIConfig.create({ data: model });
      console.log('✅ 已添加:', model.name);
    }

    console.log('🎉 豆包模型配置替换完成!');
    
  } catch (error) {
    console.error('❌ 替换失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

replaceDoubaoModels();
