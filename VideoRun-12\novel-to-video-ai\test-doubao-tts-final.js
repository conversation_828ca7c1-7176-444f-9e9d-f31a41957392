const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 豆包TTS认证信息（真实参数）
const APP_ID = '7920971896';
const ACCESS_TOKEN = 'b3nfCelq9tf4Lfs4HfPd8wSRS-xLwJ5_';
const SECRET_KEY = '_wIm8vP8uqWW_FCEwkrzEJpJj_2pUhGA';
const CLUSTER = 'volcano_tts';

// 生成请求ID
function generateReqId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

async function testDoubaoTTSFinal() {
  try {
    console.log('🎵 测试豆包TTS API（真实认证参数）...');
    
    // 1. 检查豆包TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true,
        enabled: true
      }
    });
    
    if (!ttsConfig) {
      console.error('❌ 未找到豆包TTS配置');
      console.log('请先运行: node setup-doubao-tts.js');
      return;
    }
    
    console.log('✅ 找到豆包TTS配置:', ttsConfig.name);
    
    // 2. 测试豆包TTS API调用
    const testText = '你好，这是豆包语音合成的最终测试。我们使用真实的认证参数来验证API功能。';
    
    console.log('📡 调用豆包TTS API...');
    console.log('测试文本:', testText);
    
    // 构建豆包TTS请求参数（使用真实参数）
    const requestBody = {
      app: {
        appid: APP_ID,
        token: ACCESS_TOKEN,
        cluster: CLUSTER
      },
      user: {
        uid: "test_user_final"
      },
      audio: {
        voice_type: "zh_male_M392_conversation_wvae_bigtts",
        encoding: "mp3",
        speed_ratio: 1.0,
        rate: 24000,
        bitrate: 160
      },
      request: {
        reqid: generateReqId(),
        text: testText,
        operation: "query"
      }
    };
    
    console.log('请求参数:', {
      appid: requestBody.app.appid,
      cluster: requestBody.app.cluster,
      textLength: testText.length,
      voice_type: requestBody.audio.voice_type,
      encoding: requestBody.audio.encoding,
      reqid: requestBody.request.reqid
    });
    
    // 调用豆包TTS API
    console.log('🔗 API端点: https://openspeech.bytedance.com/api/v1/tts');

    const response = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer;${ACCESS_TOKEN}`
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('API响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status}`);
      console.error('错误详情:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.error('错误JSON:', JSON.stringify(errorJson, null, 2));
        
        // 分析错误
        if (errorJson.code === 3001) {
          console.error('💡 可能的原因：');
          console.error('  1. AppID或Token不正确');
          console.error('  2. 服务未开通或配额不足');
          console.error('  3. 请求格式有误');
        }
      } catch (e) {
        console.error('错误文本:', errorText);
      }
      return;
    }
    
    // 处理成功响应
    const result = await response.json();
    console.log('✅ API调用成功');
    console.log('响应结果:', {
      code: result.code,
      message: result.message,
      sequence: result.sequence,
      hasData: !!result.data,
      dataLength: result.data ? result.data.length : 0
    });
    
    if (result.code === 3000) {
      console.log('🎉 语音合成成功！');
      
      if (result.data) {
        // 保存音频文件
        const fs = require('fs');
        const audioBuffer = Buffer.from(result.data, 'base64');
        const audioPath = `doubao_tts_final_${Date.now()}.mp3`;
        fs.writeFileSync(audioPath, audioBuffer);
        console.log('✅ 音频已保存到:', audioPath);
        console.log('音频大小:', audioBuffer.length, '字节');
        
        if (result.addition && result.addition.duration) {
          const duration = parseFloat(result.addition.duration) / 1000;
          console.log('音频时长:', duration.toFixed(1), '秒');
        }
        
        // 验证音频文件
        if (audioBuffer.length > 1000) {
          console.log('🎵 音频文件生成成功，可以播放测试！');
        }
      }
    } else {
      console.error('❌ 语音合成失败');
      console.error('错误码:', result.code);
      console.error('错误信息:', result.message);
      
      // 分析错误码
      switch (result.code) {
        case 3001:
          console.error('💡 建议：检查AppID、Token是否正确');
          break;
        case 3003:
          console.error('💡 建议：降低并发请求数量');
          break;
        case 3005:
          console.error('💡 建议：稍后重试');
          break;
        case 3010:
          console.error('💡 建议：减少文本长度');
          break;
        case 3011:
          console.error('💡 建议：检查文本内容是否有效');
          break;
        case 3050:
          console.error('💡 建议：检查voice_type参数');
          break;
      }
    }
    
    // 3. 如果成功，测试不同的音色
    if (result.code === 3000) {
      console.log('\n🎤 测试不同音色...');
      
      const voiceTypes = [
        { id: 'zh_female_F001_conversation_wvae_bigtts', name: '女声-对话风格' },
        { id: 'zh_male_M001_news_wvae_bigtts', name: '男声-新闻风格' },
        { id: 'zh_female_F002_news_wvae_bigtts', name: '女声-新闻风格' }
      ];
      
      for (const voice of voiceTypes) {
        console.log(`测试音色: ${voice.name} (${voice.id})`);
        
        const voiceTestBody = {
          ...requestBody,
          audio: {
            ...requestBody.audio,
            voice_type: voice.id
          },
          request: {
            ...requestBody.request,
            reqid: generateReqId(),
            text: `这是${voice.name}的测试语音。`
          }
        };
        
        try {
          const voiceResponse = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer;${ACCESS_TOKEN}`
            },
            body: JSON.stringify(voiceTestBody)
          });
          
          if (voiceResponse.ok) {
            const voiceResult = await voiceResponse.json();
            if (voiceResult.code === 3000) {
              console.log(`  ✅ ${voice.name} 测试成功`);
              
              // 保存音色测试音频
              if (voiceResult.data) {
                const fs = require('fs');
                const voiceAudioBuffer = Buffer.from(voiceResult.data, 'base64');
                const voiceAudioPath = `doubao_${voice.id}_${Date.now()}.mp3`;
                fs.writeFileSync(voiceAudioPath, voiceAudioBuffer);
                console.log(`    音频保存: ${voiceAudioPath}`);
              }
            } else {
              console.log(`  ❌ ${voice.name} 测试失败: ${voiceResult.message}`);
            }
          } else {
            console.log(`  ❌ ${voice.name} API调用失败: ${voiceResponse.status}`);
          }
        } catch (error) {
          console.log(`  ❌ ${voice.name} 测试异常:`, error.message);
        }
        
        // 避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // 4. 测试情感表达（如果支持）
    if (result.code === 3000) {
      console.log('\n😊 测试情感表达...');
      
      const emotionTexts = [
        { text: '今天天气真好，我很开心！', emotion: 'happy' },
        { text: '这件事让我感到很难过。', emotion: 'sad' },
        { text: '什么？这怎么可能！', emotion: 'surprised' }
      ];
      
      for (const emotionTest of emotionTexts) {
        console.log(`测试情感: ${emotionTest.emotion}`);
        
        const emotionTestBody = {
          ...requestBody,
          request: {
            ...requestBody.request,
            reqid: generateReqId(),
            text: emotionTest.text
          }
        };
        
        try {
          const emotionResponse = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer;${ACCESS_TOKEN}`
            },
            body: JSON.stringify(emotionTestBody)
          });
          
          if (emotionResponse.ok) {
            const emotionResult = await emotionResponse.json();
            if (emotionResult.code === 3000) {
              console.log(`  ✅ ${emotionTest.emotion} 情感测试成功`);
            }
          }
        } catch (error) {
          console.log(`  ❌ ${emotionTest.emotion} 情感测试异常:`, error.message);
        }
        
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    console.log('\n🎉 豆包TTS最终测试完成！');
    
    if (result.code === 3000) {
      console.log('✅ 豆包TTS API工作正常，可以集成到系统中！');
    } else {
      console.log('⚠️ 豆包TTS API需要进一步调试');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.error('🌐 网络连接失败，请检查网络设置');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔗 连接被拒绝，请检查API端点');
    } else {
      console.error('详细错误:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 显示使用说明
function showUsage() {
  console.log('🔧 豆包TTS最终测试（真实认证参数）');
  console.log('');
  console.log('使用的认证信息:');
  console.log(`AppID: ${APP_ID}`);
  console.log(`Access Token: ${ACCESS_TOKEN.substring(0, 20)}...`);
  console.log(`Secret Key: ${SECRET_KEY.substring(0, 20)}...`);
  console.log(`Cluster: ${CLUSTER}`);
  console.log('');
  console.log('API端点: https://openspeech.bytedance.com/api/v1/tts');
  console.log('');
  console.log('测试内容:');
  console.log('1. 基础语音合成');
  console.log('2. 多种音色测试');
  console.log('3. 情感表达测试');
  console.log('4. 音频文件保存');
  console.log('');
}

async function main() {
  showUsage();
  await testDoubaoTTSFinal();
}

main();
