'use client'

import React, { useState, useEffect } from 'react'
import { Clock, Video, HardDrive, Zap, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

interface VideoProgressProps {
  episodeId: string
  isGenerating: boolean
  onComplete?: () => void
}

interface ProgressData {
  progress: {
    percentage: number
    completed: number
    total: number
    estimatedRemainingTime: number | null
    currentProcessing: Array<{
      index: number
      title: string
      startTime: string
    }>
    queueStatus: {
      total: number
      completed: number
      processing: number
      pending: number
      failed: number
    }
  }
  statistics: {
    totalVideoSize: number
    totalVideoDuration: number
    averageSegmentDuration: number
  }
  segments: Array<{
    id: string
    index: number
    title: string
    status: string
    duration: number
    hasVideo: boolean
    metadata: {
      videoSize?: number
      responseTime?: number
      totalAttempts?: number
      generationTime?: number
      completedAt?: string
      error?: string
    }
  }>
  lastUpdated: string
}

export default function VideoGenerationProgress({ episodeId, isGenerating, onComplete }: VideoProgressProps) {
  const [progressData, setProgressData] = useState<ProgressData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProgress = async () => {
    try {
      const response = await fetch(`/api/ai/video-progress?episodeId=${episodeId}`)
      const data = await response.json()
      
      if (data.success) {
        setProgressData(data)
        setError(null)
        
        // 检查是否全部完成
        if (data.progress.percentage === 100 && onComplete) {
          onComplete()
        }
      } else {
        setError(data.error || '获取进度失败')
      }
    } catch (err) {
      setError('网络请求失败')
      console.error('获取进度失败:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProgress()
    
    // 如果正在生成，设置定时刷新
    let interval: NodeJS.Timeout | null = null
    if (isGenerating) {
      interval = setInterval(fetchProgress, 3000) // 每3秒刷新一次
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [episodeId, isGenerating])

  const formatTime = (seconds: number | null) => {
    if (!seconds) return '计算中...'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}分${secs}秒`
  }

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'generating':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'generating':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-center">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>加载进度信息...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center text-red-600">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      </div>
    )
  }

  if (!progressData) return null

  const { progress, statistics, segments } = progressData

  return (
    <div className="space-y-4">
      {/* 总体进度 */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2">
            <Video className="w-5 h-5" />
            视频生成进度
          </h3>
        </div>
        <div className="p-6 pt-0 space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>总体进度</span>
              <span>{progress.percentage}%</span>
            </div>
            <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200">
              <div
                className="h-full w-full flex-1 bg-blue-600 transition-all"
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>{progress.completed}/{progress.total} 片段完成</span>
              {progress.estimatedRemainingTime && (
                <span>预计剩余: {formatTime(progress.estimatedRemainingTime)}</span>
              )}
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">{progress.queueStatus.completed}</div>
              <div className="text-xs text-gray-500">已完成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{progress.queueStatus.processing}</div>
              <div className="text-xs text-gray-500">生成中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-600">{progress.queueStatus.pending}</div>
              <div className="text-xs text-gray-500">等待中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-red-600">{progress.queueStatus.failed}</div>
              <div className="text-xs text-gray-500">失败</div>
            </div>
          </div>
        </div>
      </div>

      {/* 当前处理状态 */}
      {progress.currentProcessing.length > 0 && (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="flex flex-col space-y-1.5 p-6">
            <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2">
              <Zap className="w-5 h-5" />
              正在处理
            </h3>
          </div>
          <div className="p-6 pt-0">
            <div className="space-y-2">
              {progress.currentProcessing.map((item) => (
                <div key={item.index} className="flex items-center gap-2 p-2 bg-blue-50 rounded">
                  <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                  <span className="text-sm">片段 {item.index}: {item.title}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 详细片段列表 */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="flex flex-col space-y-1.5 p-6">
          <h3 className="text-2xl font-semibold leading-none tracking-tight">片段详情</h3>
        </div>
        <div className="p-6 pt-0">
          <div className="space-y-2">
            {segments.map((segment) => (
              <div key={segment.id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  {getStatusIcon(segment.status)}
                  <div>
                    <div className="font-medium">片段 {segment.index}</div>
                    <div className="text-sm text-gray-500 truncate max-w-md">{segment.title}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${getStatusColor(segment.status)}`}>
                    {segment.status === 'completed' ? '已完成' :
                     segment.status === 'generating' ? '生成中' :
                     segment.status === 'failed' ? '失败' : '等待中'}
                  </span>
                  {segment.metadata.videoSize && (
                    <span className="text-xs text-gray-500">
                      {formatSize(segment.metadata.videoSize)}
                    </span>
                  )}
                  {segment.duration > 0 && (
                    <span className="text-xs text-gray-500">
                      {segment.duration}s
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      {statistics.totalVideoSize > 0 && (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="flex flex-col space-y-1.5 p-6">
            <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center gap-2">
              <HardDrive className="w-5 h-5" />
              生成统计
            </h3>
          </div>
          <div className="p-6 pt-0">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-gray-500">总文件大小</div>
                <div className="font-semibold">{formatSize(statistics.totalVideoSize)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">总时长</div>
                <div className="font-semibold">{statistics.totalVideoDuration}秒</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">平均时长</div>
                <div className="font-semibold">{statistics.averageSegmentDuration.toFixed(1)}秒</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
