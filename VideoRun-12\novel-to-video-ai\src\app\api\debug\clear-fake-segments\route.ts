import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { episodeId } = await request.json()

    if (!episodeId) {
      return NextResponse.json(
        { success: false, error: '缺少episodeId参数' },
        { status: 400 }
      )
    }

    console.log(`开始清理剧集 ${episodeId} 的假视频片段...`)

    // 删除该剧集的所有视频片段
    const deleteResult = await prisma.videoSegment.deleteMany({
      where: {
        episodeId: episodeId
      }
    })

    console.log(`已删除 ${deleteResult.count} 个视频片段`)

    // 同时删除相关的story_videos记录
    const storyVideos = await prisma.storyVideo.findMany({
      where: {
        episodeId: episodeId
      }
    })

    if (storyVideos.length > 0) {
      const deleteStoryVideos = await prisma.storyVideo.deleteMany({
        where: {
          episodeId: episodeId
        }
      })
      console.log(`已删除 ${deleteStoryVideos.count} 个故事视频记录`)
    }

    return NextResponse.json({
      success: true,
      message: `成功清理剧集 ${episodeId} 的假数据`,
      deletedSegments: deleteResult.count,
      deletedStoryVideos: storyVideos.length
    })

  } catch (error) {
    console.error('清理假数据失败:', error)
    return NextResponse.json(
      { success: false, error: '清理假数据失败' },
      { status: 500 }
    )
  }
}
