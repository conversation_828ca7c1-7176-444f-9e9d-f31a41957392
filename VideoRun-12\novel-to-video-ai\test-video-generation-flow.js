const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testVideoGenerationFlow() {
  try {
    console.log('🧪 测试完整视频生成流程...\n');
    
    // 1. 检查豆包配置
    console.log('1️⃣ 检查豆包配置...');
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        enabled: true,
        supportsVideo: true
      }
    });
    
    if (!doubaoConfig) {
      console.log('❌ 未找到豆包配置');
      return;
    }
    
    console.log(`✅ 豆包配置: ${doubaoConfig.model}`);
    
    // 2. 检查剧集信息
    console.log('\n2️⃣ 检查剧集信息...');
    const episode = await prisma.episode.findFirst({
      where: { title: '雪夜初逢' },
      include: { project: true }
    });
    
    if (!episode) {
      console.log('❌ 未找到"雪夜初逢"剧集');
      return;
    }
    
    console.log(`✅ 剧集: ${episode.title}`);
    console.log(`   项目: ${episode.project?.title || '未知'}`);
    console.log(`   剧情信息: ${episode.plotInfo ? '✅ 有' : '❌ 无'}`);
    
    // 3. 模拟创建视频片段
    console.log('\n3️⃣ 模拟创建视频片段...');
    
    const testSegment = {
      title: "测试片段",
      description: "测试描述", 
      prompt: "张三在雪夜中巡逻，身穿军装，表情严肃。角色：张三(脸型方正轮廓分明，眼睛中等大小深褐色，身材高大魁梧，短发齐耳，军装实用)。高质量电影级画面，细节丰富，光影效果佳。",
      duration: 20,
      segmentIndex: 1
    };
    
    console.log(`测试提示词: ${testSegment.prompt.substring(0, 100)}...`);
    console.log(`测试时长: ${testSegment.duration}秒`);
    
    // 4. 测试豆包API调用
    console.log('\n4️⃣ 测试豆包API调用...');
    
    const content = [
      {
        type: "text",
        text: testSegment.prompt
      }
    ];
    
    console.log('📡 发送API请求...');
    
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${doubaoConfig.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: doubaoConfig.model,
        content: content
      })
    });
    
    console.log(`API响应状态: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status}`);
      console.error(`错误详情: ${errorText.substring(0, 500)}...`);
      return;
    }
    
    const result = await response.json();
    console.log('✅ API调用成功!');
    console.log('📋 响应数据:', JSON.stringify(result, null, 2));
    
    // 5. 解析任务ID
    console.log('\n5️⃣ 解析任务ID...');
    
    let taskId = null;
    if (result.data && result.data.task_id) {
      taskId = result.data.task_id;
    } else if (result.task_id) {
      taskId = result.task_id;
    } else if (result.id) {
      taskId = result.id;
    } else if (result.data && result.data.id) {
      taskId = result.data.id;
    }
    
    if (taskId) {
      console.log(`✅ 任务ID: ${taskId}`);
      
      // 6. 测试状态查询
      console.log('\n6️⃣ 测试状态查询...');
      
      const statusResponse = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${doubaoConfig.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`状态查询响应: ${statusResponse.status}`);
      
      if (statusResponse.ok) {
        const statusResult = await statusResponse.json();
        console.log('✅ 状态查询成功!');
        console.log('📋 状态数据:', JSON.stringify(statusResult, null, 2));
        
        console.log(`\n📊 任务状态: ${statusResult.status || '未知'}`);
        
        if (statusResult.status === 'succeeded' || statusResult.status === 'completed') {
          const videoUrl = statusResult.content?.video_url || statusResult.video_url;
          console.log(`🎬 视频URL: ${videoUrl || '未找到'}`);
        } else if (statusResult.status === 'failed' || statusResult.status === 'error') {
          const errorMessage = statusResult.message || statusResult.error || '任务失败';
          console.log(`❌ 任务失败: ${errorMessage}`);
        } else {
          console.log(`⏳ 任务进行中，状态: ${statusResult.status}`);
        }
      } else {
        const errorText = await statusResponse.text();
        console.error(`❌ 状态查询失败: ${statusResponse.status}`);
        console.error(`错误详情: ${errorText.substring(0, 200)}...`);
      }
    } else {
      console.log('❌ 未找到任务ID');
      console.log('响应结构:', Object.keys(result));
    }
    
    console.log('\n✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testVideoGenerationFlow();
