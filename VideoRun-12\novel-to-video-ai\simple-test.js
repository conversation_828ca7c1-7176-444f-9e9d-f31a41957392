// 简单的API测试
async function testAPI() {
  try {
    console.log('测试服务器连接...');
    
    const response = await fetch('http://localhost:3000/api/ai/generate-appearance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        modelId: 'cmc5ntxj00000vmwk0s2y2zv0', // 使用刚创建的测试AI配置ID
        character: {
          name: '测试角色',
          facial: '圆脸大眼睛'
        },
        generateDetailedDNA: true,
        useConsistencyConstraints: true,
        consistencyMode: 'hybrid'
      })
    });
    
    console.log('响应状态:', response.status);
    console.log('响应头:', response.headers);
    
    const text = await response.text();
    console.log('响应内容:', text);
    
  } catch (error) {
    console.error('错误:', error);
  }
}

testAPI();
