{"version": 3, "sources": ["../index.ts"], "sourcesContent": ["import { Buffer } from 'node:buffer'\nimport fs from 'node:fs'\nimport { WebSocket } from 'ws'\n\nconst baseUrl = `speech.platform.bing.com/consumer/speech/synthesize/readaloud`\nconst token = '6A5AA1D4EAFF4E9FB37E23D68491D6F4'\nconst webSocketURL = `wss://${baseUrl}/edge/v1?TrustedClientToken=${token}`\nconst voiceListUrl = `https://${baseUrl}/voices/list?trustedclienttoken=${token}`\n\nexport const Personalities = ['Approachable', 'Authentic', 'Authority', 'Bright', 'Caring', 'Casual', 'Cheerful', 'Clear', 'Comfort', 'Confident', 'Considerate', 'Conversational', 'Cute', 'Expressive', 'Friendly', 'Honest', 'Humorous', 'Lively', 'Passion', 'Pleasant', 'Positive', 'Professional', 'Rational', 'Reliable', 'Sincere', 'Sunshine', 'Warm']\nexport const Categories = ['Novel', 'Cartoon', 'Conversation', 'Copilot', 'Dialect', 'General', 'News', 'Novel', 'Sports']\nexport type Personality = typeof Personalities[number]\nexport type Category = typeof Categories[number]\n\nexport interface Voice {\n  Name: string\n  ShortName: string\n  FriendlyName: string\n  Gender: 'Male' | 'Female'\n  Locale: string\n  VoiceTag: {\n    ContentCategories: Category[]\n    VoicePersonalities: Personality[]\n  }\n}\n\nexport async function getVoices(): Promise<Voice[]> {\n  const resp = await fetch(voiceListUrl)\n  return (await resp.json()) as Voice[]\n}\n\nfunction uuid() {\n  return crypto.randomUUID().replaceAll('-', '')\n}\n\ntype options = Partial<{\n  voice: string,\n  volume: string,\n  rate: string,\n  pitch: string\n}>\n\nexport function tts(text: string, options: options = {}): Promise<Buffer> {\n  const { voice = 'en-GB-SoniaNeural', volume = '+0%', rate = '+0%', pitch = '+0Hz' } = options\n\n  return new Promise<Buffer>((resolve, reject) => {\n    const ws = new WebSocket(`${webSocketURL}&ConnectionId=${uuid()}`, {\n      host: 'speech.platform.bing.com',\n      origin: 'chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold',\n      headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.66 Safari/537.36 Edg/103.0.1264.44' },\n    })\n    const audioData: Buffer[] = []\n    ws.on('message', (rawData, isBinary) => {\n      if (!isBinary) {\n        const data = rawData.toString('utf8')\n        if (data.includes('turn.end')) {\n          resolve(Buffer.concat(audioData))\n          ws.close()\n        }\n        return\n      }\n      const data = rawData as Buffer\n      const separator = 'Path:audio\\r\\n'\n      const content = data.subarray(data.indexOf(separator) + separator.length)\n      audioData.push(content)\n    })\n    ws.on('error', reject)\n\n    const speechConfig = JSON.stringify({ context: { synthesis: { audio: {\n      metadataoptions: { sentenceBoundaryEnabled: false, wordBoundaryEnabled: false },\n      outputFormat: 'audio-24khz-48kbitrate-mono-mp3',\n    } } } })\n    const configMessage = `X-Timestamp:${Date()}\\r\\nContent-Type:application/json; charset=utf-8\\r\\nPath:speech.config\\r\\n\\r\\n${speechConfig}`\n    ws.on('open', () => ws.send(configMessage, { compress: true }, (configError) => {\n      if (configError)\n        reject(configError)\n\n      const ssmlMessage = `X-RequestId:${uuid()}\\r\\nContent-Type:application/ssml+xml\\r\\n`\n        + `X-Timestamp:${Date()}Z\\r\\nPath:ssml\\r\\n\\r\\n`\n        + `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>`\n        + `<voice name='${voice}'><prosody pitch='${pitch}' rate='${rate}' volume='${volume}'>`\n        + `${text}</prosody></voice></speak>`\n      ws.send(ssmlMessage, { compress: true }, (ssmlError) => {\n        if (ssmlError)\n          reject(ssmlError)\n      })\n    }))\n  })\n}\n\nexport async function ttsSave(text: string, file: fs.PathOrFileDescriptor, options: options = {}) {\n  fs.writeFileSync(file, await tts(text, options))\n}\n"], "mappings": ";AAAA;AACA;AACA;AAwBA,eAAsB,SAAS,GAAqB;AAClD,QAAM,OAAO,MAAM,MAAM,YAAY;AACrC,SAAQ,MAAM,KAAK,KAAK;AAAA;AAG1B,IAAS,eAAI,GAAG;AACd,SAAO,OAAO,WAAW,EAAE,WAAW,KAAK,EAAE;AAAA;AAUxC,SAAS,GAAG,CAAC,MAAc,UAAmB,CAAC,GAAoB;AACxE,UAAQ,QAAQ,qBAAqB,SAAS,OAAO,OAAO,OAAO,QAAQ,WAAW;AAEtF,SAAO,IAAI,QAAgB,CAAC,SAAS,WAAW;AAC9C,UAAM,KAAK,IAAI,UAAU,GAAG,6BAA6B,KAAK,KAAK;AAAA,MACjE,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS,EAAE,cAAc,wIAAwI;AAAA,IACnK,CAAC;AACD,UAAM,YAAsB,CAAC;AAC7B,OAAG,GAAG,WAAW,CAAC,SAAS,aAAa;AACtC,WAAK,UAAU;AACb,cAAM,QAAO,QAAQ,SAAS,MAAM;AACpC,YAAI,MAAK,SAAS,UAAU,GAAG;AAC7B,kBAAQ,OAAO,OAAO,SAAS,CAAC;AAChC,aAAG,MAAM;AAAA,QACX;AACA;AAAA,MACF;AACA,YAAM,OAAO;AACb,YAAM,YAAY;AAClB,YAAM,UAAU,KAAK,SAAS,KAAK,QAAQ,SAAS,IAAI,UAAU,MAAM;AACxE,gBAAU,KAAK,OAAO;AAAA,KACvB;AACD,OAAG,GAAG,SAAS,MAAM;AAErB,UAAM,eAAe,KAAK,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO;AAAA,MACnE,iBAAiB,EAAE,yBAAyB,OAAO,qBAAqB,MAAM;AAAA,MAC9E,cAAc;AAAA,IAChB,EAAE,EAAE,EAAE,CAAC;AACP,UAAM,gBAAgB,eAAe,KAAK,kFAAkF;AAC5H,OAAG,GAAG,QAAQ,MAAM,GAAG,KAAK,eAAe,EAAE,UAAU,KAAK,GAAG,CAAC,gBAAgB;AAC9E,UAAI;AACF,eAAO,WAAW;AAEpB,YAAM,cAAc,eAAe,KAAK,+CACpC,eAAe,KAAK,4BACpB,uFACA,gBAAgB,0BAA0B,gBAAgB,iBAAiB,aAC3E,GAAG;AACP,SAAG,KAAK,aAAa,EAAE,UAAU,KAAK,GAAG,CAAC,cAAc;AACtD,YAAI;AACF,iBAAO,SAAS;AAAA,OACnB;AAAA,KACF,CAAC;AAAA,GACH;AAAA;AAGH,eAAsB,OAAO,CAAC,MAAc,MAA+B,UAAmB,CAAC,GAAG;AAChG,KAAG,cAAc,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC;AAAA;AAvFjD,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,eAAe,SAAS,sCAAsC;AACpE,IAAM,eAAe,WAAW,0CAA0C;AAEnE,IAAM,gBAAgB,CAAC,gBAAgB,aAAa,aAAa,UAAU,UAAU,UAAU,YAAY,SAAS,WAAW,aAAa,eAAe,kBAAkB,QAAQ,cAAc,YAAY,UAAU,YAAY,UAAU,WAAW,YAAY,YAAY,gBAAgB,YAAY,YAAY,WAAW,YAAY,MAAM;AACvV,IAAM,aAAa,CAAC,SAAS,WAAW,gBAAgB,WAAW,WAAW,WAAW,QAAQ,SAAS,QAAQ;", "debugId": "EA7BA9497FADA67464756e2164756e21", "names": []}