// 测试简单的API端点
async function testSimpleAPI() {
  try {
    console.log('🔍 测试简单API端点...');
    
    const response = await fetch('http://localhost:3001/api/ai/analyze-detailed-plot?episodeId=test', {
      method: 'GET'
    });

    console.log('📊 响应状态:', response.status);
    
    const text = await response.text();
    console.log('📊 响应内容:', text);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testSimpleAPI();
