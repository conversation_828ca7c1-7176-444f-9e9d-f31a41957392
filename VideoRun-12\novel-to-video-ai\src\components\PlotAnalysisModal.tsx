'use client'

import { useState, useEffect } from 'react'
import { X, Users, MapPin, Zap, ChevronRight, Clock, Eye, Target } from 'lucide-react'

interface PlotAnalysisModalProps {
  episodeId: string
  episodeTitle: string
  isOpen: boolean
  onClose: () => void
  plotInfo?: any
}

interface PlotInfo {
  characters: string[]
  scenes: Array<{
    location: string
    description: string
    atmosphere: string
    time: string
    significance: string
  }>
  events: Array<{
    normal: string
    conflict: string
    escalation: string
    participants: string[]
    location: string
    actions: string[]
    significance: string
  }>
}

export default function PlotAnalysisModal({ 
  episodeId, 
  episodeTitle, 
  isOpen, 
  onClose,
  plotInfo: initialPlotInfo 
}: PlotAnalysisModalProps) {
  const [plotInfo, setPlotInfo] = useState<PlotInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'characters' | 'scenes' | 'events'>('characters')

  useEffect(() => {
    if (isOpen && initialPlotInfo) {
      try {
        // 解析传入的剧情信息
        const parsedInfo: PlotInfo = {
          characters: typeof initialPlotInfo.characters === 'string' 
            ? JSON.parse(initialPlotInfo.characters) 
            : initialPlotInfo.characters || [],
          scenes: typeof initialPlotInfo.scenes === 'string' 
            ? JSON.parse(initialPlotInfo.scenes) 
            : initialPlotInfo.scenes || [],
          events: typeof initialPlotInfo.events === 'string' 
            ? JSON.parse(initialPlotInfo.events) 
            : initialPlotInfo.events || []
        }
        setPlotInfo(parsedInfo)
      } catch (error) {
        console.error('解析剧情信息失败:', error)
        setError('剧情信息格式错误')
      }
    }
  }, [isOpen, initialPlotInfo])

  if (!isOpen) return null

  const tabs = [
    {
      id: 'characters' as const,
      name: '本集人物',
      icon: Users,
      description: '当前剧集中出场的所有角色'
    },
    {
      id: 'scenes' as const,
      name: '场景信息',
      icon: MapPin,
      description: '故事发生的地点、环境描述、氛围设定'
    },
    {
      id: 'events' as const,
      name: '事件三要素',
      icon: Zap,
      description: '正常状态 → 矛盾冲突 → 升级事件的结构分析'
    }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-purple-500 to-blue-500 text-white">
          <div>
            <h2 className="text-xl font-semibold flex items-center">
              <Zap className="mr-2" size={24} />
              {episodeTitle} - 剧情信息分析
            </h2>
            <p className="text-purple-100 text-sm mt-1">
              基于DeepSeek Reasoner的专业剧情分析结果
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 bg-gray-50">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    isActive
                      ? 'border-purple-500 text-purple-600 bg-white'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="inline mr-2" size={16} />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-2">
                <X size={48} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">解析失败</h3>
              <p className="text-gray-500">{error}</p>
            </div>
          ) : !plotInfo ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <Eye size={48} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无剧情信息</h3>
              <p className="text-gray-500">请先提取剧情信息</p>
            </div>
          ) : (
            <>
              {/* 本集人物 */}
              {activeTab === 'characters' && (
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-800 mb-2 flex items-center">
                      <Users className="mr-2" size={20} />
                      角色识别结果
                    </h3>
                    <p className="text-blue-700 text-sm">
                      AI识别出本集中出场的 <strong>{plotInfo.characters.length}</strong> 个角色
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {plotInfo.characters.map((character, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center mb-2">
                          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <Users className="text-purple-600" size={20} />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{character}</h4>
                            <p className="text-xs text-gray-500">第 {index + 1} 个角色</p>
                          </div>
                        </div>
                        <div className="text-sm text-gray-600">
                          在本集中出场的角色，参与了重要剧情发展
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 场景信息 */}
              {activeTab === 'scenes' && (
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-2 flex items-center">
                      <MapPin className="mr-2" size={20} />
                      场景分析结果
                    </h3>
                    <p className="text-green-700 text-sm">
                      AI分析出本集涉及 <strong>{plotInfo.scenes.length}</strong> 个主要场景
                    </p>
                  </div>

                  <div className="space-y-4">
                    {plotInfo.scenes.map((scene, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                              <MapPin className="text-green-600" size={24} />
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900">{scene.location}</h4>
                              <div className="flex items-center text-sm text-gray-500 mt-1">
                                <Clock className="mr-1" size={14} />
                                {scene.time}
                              </div>
                            </div>
                          </div>
                          <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                            场景 {index + 1}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="text-sm font-medium text-gray-700 mb-2">环境描述</h5>
                            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                              {scene.description}
                            </p>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-gray-700 mb-2">氛围设定</h5>
                            <p className="text-sm text-gray-600 bg-purple-50 p-3 rounded">
                              {scene.atmosphere}
                            </p>
                          </div>
                        </div>

                        <div className="mt-4">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">剧情意义</h5>
                          <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
                            {scene.significance}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 事件三要素 */}
              {activeTab === 'events' && (
                <div className="space-y-6">
                  <div className="bg-orange-50 rounded-lg p-4">
                    <h3 className="font-semibold text-orange-800 mb-2 flex items-center">
                      <Zap className="mr-2" size={20} />
                      事件结构分析
                    </h3>
                    <p className="text-orange-700 text-sm">
                      基于"正常状态 → 矛盾冲突 → 升级事件"的三要素结构分析
                    </p>
                  </div>

                  <div className="space-y-6">
                    {plotInfo.events.map((event, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-6">
                          <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                            <Target className="mr-2 text-orange-600" size={20} />
                            事件 {index + 1}
                          </h4>
                        </div>

                        {/* 三要素结构 */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h5 className="text-sm font-semibold text-blue-800 mb-2 flex items-center">
                              <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold">1</div>
                              正常状态
                            </h5>
                            <p className="text-sm text-blue-700">{event.normal}</p>
                          </div>
                          
                          <div className="bg-yellow-50 p-4 rounded-lg">
                            <h5 className="text-sm font-semibold text-yellow-800 mb-2 flex items-center">
                              <div className="w-6 h-6 bg-yellow-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold">2</div>
                              矛盾冲突
                            </h5>
                            <p className="text-sm text-yellow-700">{event.conflict}</p>
                          </div>
                          
                          <div className="bg-red-50 p-4 rounded-lg">
                            <h5 className="text-sm font-semibold text-red-800 mb-2 flex items-center">
                              <div className="w-6 h-6 bg-red-200 rounded-full flex items-center justify-center mr-2 text-xs font-bold">3</div>
                              升级事件
                            </h5>
                            <p className="text-sm text-red-700">{event.escalation}</p>
                          </div>
                        </div>

                        {/* 详细信息 */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                          <div>
                            <h5 className="text-sm font-medium text-gray-700 mb-2">参与人物</h5>
                            <div className="flex flex-wrap gap-1">
                              {event.participants.map((participant, pIndex) => (
                                <span key={pIndex} className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded">
                                  {participant}
                                </span>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <h5 className="text-sm font-medium text-gray-700 mb-2">发生地点</h5>
                            <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">{event.location}</p>
                          </div>
                          
                          <div>
                            <h5 className="text-sm font-medium text-gray-700 mb-2">具体行为</h5>
                            <div className="space-y-1">
                              {event.actions.map((action, aIndex) => (
                                <div key={aIndex} className="text-xs text-gray-600 bg-gray-50 p-1 rounded flex items-center">
                                  <ChevronRight size={12} className="mr-1 text-gray-400" />
                                  {action}
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* 事件意义 */}
                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">事件重要性和影响</h5>
                          <p className="text-sm text-gray-600 bg-indigo-50 p-3 rounded">
                            {event.significance}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* 底部说明 */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="text-xs text-gray-500 text-center">
            <p>本分析结果由 DeepSeek Reasoner 模型生成，基于专业的剧情分析算法</p>
          </div>
        </div>
      </div>
    </div>
  )
}
