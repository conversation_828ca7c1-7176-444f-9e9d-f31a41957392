'use client'

import { useState } from 'react'
import { Users, User } from 'lucide-react'
import { Character } from '@/types'
import CharacterDetail from './CharacterDetail'

interface CharacterManagerProps {
  projectId: string
  characters: Character[]
  onCharactersUpdate?: () => void
  onCharacterUpdate?: (character: Character) => void
}

export default function CharacterManager({ 
  projectId, 
  characters, 
  onCharactersUpdate, 
  onCharacterUpdate 
}: CharacterManagerProps) {
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  const openCharacterDetail = (character: Character) => {
    setSelectedCharacter(character)
    setIsDetailOpen(true)
  }

  const closeCharacterDetail = () => {
    setIsDetailOpen(false)
    setSelectedCharacter(null)
  }

  const handleCharacterUpdate = (updatedCharacter: Character) => {
    onCharacterUpdate?.(updatedCharacter)
    onCharactersUpdate?.()
    setSelectedCharacter(updatedCharacter)
  }

  if (characters.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">暂无角色信息</h3>
        <p className="mt-1 text-sm text-gray-500">
          上传小说文件后，AI将自动提取角色信息
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="text-purple-600 mr-2" size={20} />
            <h3 className="text-lg font-medium text-gray-900">角色信息</h3>
          </div>
          <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
            共 {characters.length} 个角色
          </span>
        </div>
        <p className="mt-2 text-sm text-gray-600">
          AI自动提取的角色信息，包含五官、身份、外貌、性格、隐线等维度
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {characters.map((character) => (
          <div
            key={character.id}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => openCharacterDetail(character)}
          >
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full mb-4 shadow-lg overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                  <User className="text-white" size={28} />
                </div>
              </div>

              <h4 className="text-lg font-medium text-gray-900 mb-2 hover:text-purple-600 transition-colors">
                {character.name}
              </h4>

              {character.identity && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mb-3">
                  {character.identity}
                </span>
              )}

              <div className="text-xs text-gray-500 space-y-1">
                {character.personality && (
                  <p className="overflow-hidden line-clamp-2">
                    {character.personality}
                  </p>
                )}
                
                <p className="text-gray-400">
                  点击查看详细信息
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedCharacter && (
        <CharacterDetail
          character={selectedCharacter}
          isOpen={isDetailOpen}
          onClose={closeCharacterDetail}
          onUpdate={handleCharacterUpdate}
        />
      )}
    </div>
  )
}
