import { NextRequest, NextResponse } from 'next/server'
import { DeepSeekClient, DoubaoClient, AIServiceError } from '@/lib/ai'
import { SkyReelsClient } from '@/lib/skyreels'
import { AIConfig } from '@/types'

// 测试AI配置连接
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      provider = 'deepseek',
      apiKey,
      model = 'deepseek-chat',
      temperature = 0.7,
      maxTokens = 4000,
      topP = 0.9,
    } = body

    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API密钥不能为空' },
        { status: 400 }
      )
    }

    // 构建配置对象
    const config: AIConfig = {
      id: 'test',
      projectId: 'test',
      provider,
      apiKey,
      model,
      temperature,
      maxTokens,
      topP,
      status: 'disconnected',
    }

    // 根据提供商创建客户端并测试
    let testResult = false
    let errorMessage = ''

    try {
      if (provider === 'deepseek') {
        const client = new DeepSeekClient(config)
        testResult = await client.testConnection()
      } else if (provider === 'doubao') {
        const client = new DoubaoClient(config)
        testResult = await client.testConnection()
      } else if (provider === 'skyreels') {
        const client = new SkyReelsClient(config)
        testResult = await client.testConnection()
      } else {
        // 其他提供商的测试逻辑可以在这里添加
        errorMessage = `暂不支持 ${provider} 提供商的测试`
      }
    } catch (error) {
      if (error instanceof AIServiceError) {
        errorMessage = error.message
      } else {
        errorMessage = '连接测试失败，请检查配置'
      }
    }

    if (testResult) {
      return NextResponse.json({
        success: true,
        message: 'AI服务连接成功',
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: errorMessage || 'AI服务连接失败，请检查API密钥和网络连接',
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('AI配置测试失败:', error)
    return NextResponse.json(
      { success: false, error: '测试连接时发生错误' },
      { status: 500 }
    )
  }
}
