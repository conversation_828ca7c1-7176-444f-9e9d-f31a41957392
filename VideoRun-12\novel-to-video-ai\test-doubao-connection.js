// 测试豆包模型连接功能
async function testDoubaoConnection() {
  try {
    console.log('🔌 测试豆包模型连接功能...');
    
    // 1. 获取现有的豆包模型配置
    console.log('\n📋 1. 获取豆包模型配置...');
    
    const modelsResponse = await fetch('http://localhost:3000/api/models');
    if (!modelsResponse.ok) {
      throw new Error('无法获取模型列表');
    }
    
    const modelsResult = await modelsResponse.json();
    const doubaoModels = modelsResult.data.filter(model => model.provider === 'doubao');
    
    if (doubaoModels.length === 0) {
      console.log('❌ 没有找到豆包模型配置');
      return;
    }
    
    console.log('✅ 找到豆包模型:', doubaoModels.length, '个');
    doubaoModels.forEach((model, index) => {
      console.log(`   ${index + 1}. ${model.name} (${model.model})`);
      console.log(`      状态: ${model.enabled ? '启用' : '禁用'}`);
      console.log(`      API密钥: ${model.apiKey ? '已设置' : '未设置'}`);
    });
    
    // 2. 测试豆包模型连接
    console.log('\n🧪 2. 测试豆包模型连接...');
    
    for (const model of doubaoModels) {
      console.log(`\n🔍 测试模型: ${model.name}`);
      
      if (!model.apiKey || model.apiKey === 'test-api-key-placeholder') {
        console.log('⏭️ 跳过测试（使用测试密钥）');
        continue;
      }
      
      try {
        const testResponse = await fetch('http://localhost:3000/api/models/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: model.id,
            provider: model.provider,
            model: model.model,
            apiKey: model.apiKey
          })
        });
        
        console.log('📊 测试响应状态:', testResponse.status);
        
        if (testResponse.ok) {
          const testResult = await testResponse.json();
          if (testResult.success) {
            console.log('✅ 连接测试成功！');
          } else {
            console.log('❌ 连接测试失败:', testResult.error);
          }
        } else {
          const errorText = await testResponse.text();
          console.log('❌ 测试请求失败:', errorText);
        }
      } catch (error) {
        console.log('❌ 测试过程中发生错误:', error.message);
      }
    }
    
    // 3. 测试豆包API格式验证
    console.log('\n📝 3. 测试豆包API格式验证...');
    
    const testConfig = {
      provider: 'doubao',
      model: 'doubao-lite-32k',
      apiKey: 'test-placeholder-key'
    };
    
    console.log('✅ 豆包API请求格式:');
    console.log('   端点: https://ark.cn-beijing.volces.com/api/v3/chat/completions');
    console.log('   方法: POST');
    console.log('   认证: Bearer Token');
    console.log('   格式: OpenAI兼容');
    
    const requestBody = {
      model: testConfig.model,
      messages: [
        { role: 'user', content: '测试连接' }
      ],
      max_tokens: 10,
      temperature: 0.3
    };
    
    console.log('✅ 请求体格式验证通过');
    console.log('   模型:', requestBody.model);
    console.log('   消息数量:', requestBody.messages.length);
    console.log('   最大令牌:', requestBody.max_tokens);
    console.log('   温度:', requestBody.temperature);
    
    // 4. 测试错误处理场景
    console.log('\n🛡️ 4. 测试错误处理场景...');
    
    const errorScenarios = [
      {
        name: 'API密钥无效',
        config: { ...testConfig, apiKey: 'invalid-key' },
        expectedError: '豆包API密钥无效'
      },
      {
        name: '模型不存在',
        config: { ...testConfig, model: 'non-existent-model' },
        expectedError: '豆包模型不存在'
      },
      {
        name: '空API密钥',
        config: { ...testConfig, apiKey: '' },
        expectedError: '缺少必需字段'
      }
    ];
    
    for (const scenario of errorScenarios) {
      console.log(`\n🧪 测试场景: ${scenario.name}`);
      
      try {
        const testResponse = await fetch('http://localhost:3000/api/models/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(scenario.config)
        });
        
        const testResult = await testResponse.json();
        
        if (!testResult.success && testResult.error) {
          console.log('✅ 错误处理正确:', testResult.error);
        } else {
          console.log('⚠️ 未预期的结果:', testResult);
        }
      } catch (error) {
        console.log('✅ 捕获到错误:', error.message);
      }
    }
    
    // 5. 显示使用指南
    console.log('\n📖 5. 豆包模型使用指南...');
    
    console.log('\n🔑 获取API密钥:');
    console.log('   1. 访问火山引擎控制台: https://console.volcengine.com/');
    console.log('   2. 进入火山方舟大模型服务平台');
    console.log('   3. 在API管理中创建API密钥');
    console.log('   4. 复制生成的API密钥');
    
    console.log('\n⚙️ 配置步骤:');
    console.log('   1. 在系统中访问"模型配置"页面');
    console.log('   2. 找到"豆包 (火山引擎)"分组');
    console.log('   3. 选择要使用的豆包模型');
    console.log('   4. 粘贴API密钥到输入框');
    console.log('   5. 点击"测试"按钮验证连接');
    console.log('   6. 测试成功后启用模型');
    console.log('   7. 点击"保存"保存配置');
    
    console.log('\n🎯 支持的豆包模型:');
    console.log('   • doubao-pro-4k: 专业版4K上下文模型');
    console.log('   • doubao-pro-32k: 专业版32K长文本模型');
    console.log('   • doubao-lite-4k: 轻量版4K模型');
    console.log('   • doubao-lite-32k: 轻量版32K模型');
    console.log('   • doubao-lite-128k: 轻量版128K超长文本模型');
    
    console.log('\n🔧 故障排除:');
    console.log('   • API密钥无效: 检查密钥是否正确复制');
    console.log('   • 连接超时: 检查网络连接和防火墙设置');
    console.log('   • 模型不存在: 确认模型名称拼写正确');
    console.log('   • 配额不足: 检查火山引擎账户余额');
    
    console.log('\n🎉 豆包模型连接测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoConnection();
