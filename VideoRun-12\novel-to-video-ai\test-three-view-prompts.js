// 测试三视图提示词差异化
async function testThreeViewPrompts() {
  try {
    console.log('🎨 测试三视图提示词差异化...');
    
    console.log('\n📝 问题分析:');
    console.log('   问题: 生成的三视图几乎一样');
    console.log('   原因: 三个视图的提示词差异不够明显');
    console.log('   解决方案: 增强视图特定的描述词');
    
    // 1. 模拟 generateConsistencyPrompt 函数
    console.log('\n🔧 1. 模拟提示词生成...');
    
    const testCharacter = {
      identity: '年轻战士',
      facial: '英俊的脸庞，坚毅的眼神',
      hairstyle: '短发，黑色',
      clothing: '银色盔甲，战斗装备'
    };
    
    const basePrompt = '高质量角色设计图，专业角色设计，干净的白色背景，全身图，高清晰度，动漫风格';
    
    // 提取核心特征（简化版）
    const baseFeatures = 'same character design, consistent features, 英俊的脸庞, 坚毅的眼神, 短发黑色, 银色盔甲';
    
    // 生成增强的三视图提示词
    const enhancedPrompts = {
      front: `${basePrompt}, FRONT VIEW: character facing directly towards camera, full frontal pose, looking straight ahead, face clearly visible, front-facing stance, ${baseFeatures}, character design sheet, reference image`,
      side: `${basePrompt}, SIDE VIEW: character in perfect 90-degree profile, side silhouette, facing left or right, profile view, side angle, lateral view, ${baseFeatures}, character design sheet, reference image`,
      back: `${basePrompt}, BACK VIEW: character facing away from camera, rear view, back turned, showing back of head and body, posterior angle, ${baseFeatures}, character design sheet, reference image`
    };
    
    console.log('✅ 增强的三视图提示词生成完成');
    
    // 2. 对比原始和增强的提示词
    console.log('\n📊 2. 提示词对比分析...');
    
    const originalPrompts = {
      front: `${basePrompt}, front view, facing camera, ${baseFeatures}`,
      side: `${basePrompt}, side view, 90-degree profile, same character, ${baseFeatures}`,
      back: `${basePrompt}, back view, rear angle, same character design, ${baseFeatures}`
    };
    
    console.log('🔍 原始提示词（问题版本）:');
    Object.entries(originalPrompts).forEach(([view, prompt]) => {
      const viewSpecificWords = prompt.match(/(front view|side view|back view|facing camera|90-degree profile|rear angle)/g) || [];
      console.log(`   ${view.toUpperCase()}: ${viewSpecificWords.length} 个视图特定词汇`);
      console.log(`     特定词汇: ${viewSpecificWords.join(', ')}`);
    });
    
    console.log('\n✨ 增强提示词（修复版本）:');
    Object.entries(enhancedPrompts).forEach(([view, prompt]) => {
      const viewSpecificWords = prompt.match(/(FRONT VIEW|SIDE VIEW|BACK VIEW|facing directly towards camera|full frontal pose|looking straight ahead|face clearly visible|front-facing stance|perfect 90-degree profile|side silhouette|facing left or right|profile view|side angle|lateral view|facing away from camera|rear view|back turned|showing back of head and body|posterior angle)/g) || [];
      console.log(`   ${view.toUpperCase()}: ${viewSpecificWords.length} 个视图特定词汇`);
      console.log(`     特定词汇: ${viewSpecificWords.slice(0, 3).join(', ')}...`);
    });
    
    // 3. 分析关键差异
    console.log('\n🎯 3. 关键差异分析...');
    
    const keyDifferences = {
      '正面视图': [
        '原始: "front view, facing camera"',
        '增强: "FRONT VIEW: character facing directly towards camera, full frontal pose, looking straight ahead, face clearly visible, front-facing stance"'
      ],
      '侧面视图': [
        '原始: "side view, 90-degree profile"',
        '增强: "SIDE VIEW: character in perfect 90-degree profile, side silhouette, facing left or right, profile view, side angle, lateral view"'
      ],
      '背面视图': [
        '原始: "back view, rear angle"',
        '增强: "BACK VIEW: character facing away from camera, rear view, back turned, showing back of head and body, posterior angle"'
      ]
    };
    
    Object.entries(keyDifferences).forEach(([view, [original, enhanced]]) => {
      console.log(`📋 ${view}:`);
      console.log(`   ${original}`);
      console.log(`   ${enhanced}`);
    });
    
    // 4. 测试实际API调用（模拟）
    console.log('\n🧪 4. 模拟API调用测试...');
    
    const testApiCall = async (viewType, prompt) => {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 分析提示词中的视图特定词汇
      const viewKeywords = {
        front: ['front', 'facing', 'camera', 'frontal', 'straight', 'ahead'],
        side: ['side', 'profile', '90-degree', 'lateral', 'silhouette'],
        back: ['back', 'rear', 'away', 'turned', 'posterior']
      };
      
      const keywords = viewKeywords[viewType] || [];
      const matchedKeywords = keywords.filter(keyword => 
        prompt.toLowerCase().includes(keyword.toLowerCase())
      );
      
      return {
        viewType,
        matchedKeywords: matchedKeywords.length,
        totalKeywords: keywords.length,
        score: (matchedKeywords.length / keywords.length * 100).toFixed(1)
      };
    };
    
    console.log('🔍 视图特异性评分:');
    
    for (const [viewType, prompt] of Object.entries(enhancedPrompts)) {
      const result = await testApiCall(viewType, prompt);
      console.log(`   ${viewType.toUpperCase()}: ${result.score}% (${result.matchedKeywords}/${result.totalKeywords} 关键词匹配)`);
    }
    
    // 5. 显示修复效果预期
    console.log('\n🎉 5. 修复效果预期...');
    
    console.log('📈 预期改进:');
    console.log('   ✅ 正面视图: 角色直视镜头，面部特征清晰可见');
    console.log('   ✅ 侧面视图: 完美的90度侧身轮廓，展示侧面特征');
    console.log('   ✅ 背面视图: 角色背对镜头，展示后脑勺和背部');
    console.log('   ✅ 三个视图将有明显的视觉差异');
    console.log('   ✅ 保持角色特征的一致性');
    
    console.log('\n🛠️ 技术改进:');
    console.log('   - 使用大写标签强调视图类型');
    console.log('   - 增加多个同义词描述同一视图');
    console.log('   - 明确指定角色朝向和姿态');
    console.log('   - 保持角色特征词汇的一致性');
    
    // 6. 用户操作指南
    console.log('\n📋 6. 用户操作指南...');
    
    console.log('🔄 重新生成三视图:');
    console.log('   1. 刷新页面或重新打开三视图生成功能');
    console.log('   2. 选择豆包 Seedream 3.0 T2I 模型');
    console.log('   3. 输入角色信息和自定义提示词');
    console.log('   4. 点击"生成三视图"');
    console.log('   5. 观察三个视图是否有明显差异');
    
    console.log('\n✅ 三视图提示词差异化修复完成！');
    console.log('\n📝 总结:');
    console.log('   ✅ 大幅增强了视图特定的描述词汇');
    console.log('   ✅ 使用大写标签突出视图类型');
    console.log('   ✅ 添加了多个同义词确保AI理解');
    console.log('   ✅ 保持了角色特征的一致性');
    console.log('   ✅ 现在三个视图应该有明显的视觉差异');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testThreeViewPrompts();
