const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTTSConfig() {
  try {
    console.log('🔍 检查TTS配置...');
    
    // 获取所有TTS配置
    const allTTSConfigs = await prisma.aIConfig.findMany({
      where: {
        supportsTTS: true
      },
      orderBy: [
        { provider: 'desc' }
      ]
    });
    
    console.log('所有TTS配置:');
    allTTSConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.name}`);
      console.log(`     Provider: ${config.provider}`);
      console.log(`     Model: ${config.model}`);
      console.log(`     API Key: ${config.apiKey.substring(0, 20)}...`);
      console.log(`     Enabled: ${config.enabled}`);
      console.log(`     Status: ${config.status}`);
      console.log('');
    });
    
    // 获取当前选择的TTS配置
    const selectedTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        supportsTTS: true,
        enabled: true
      },
      orderBy: [
        { provider: 'desc' }
      ]
    });
    
    if (selectedTTSConfig) {
      console.log('✅ 当前选择的TTS配置:');
      console.log(`   名称: ${selectedTTSConfig.name}`);
      console.log(`   提供商: ${selectedTTSConfig.provider}`);
      console.log(`   模型: ${selectedTTSConfig.model}`);
      console.log(`   启用状态: ${selectedTTSConfig.enabled}`);
    } else {
      console.log('❌ 未找到可用的TTS配置');
    }
    
    // 检查豆包TTS配置
    const doubaoTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true
      }
    });
    
    if (doubaoTTSConfig) {
      console.log('\n🎵 豆包TTS配置详情:');
      console.log(`   ID: ${doubaoTTSConfig.id}`);
      console.log(`   名称: ${doubaoTTSConfig.name}`);
      console.log(`   API Key: ${doubaoTTSConfig.apiKey}`);
      console.log(`   模型: ${doubaoTTSConfig.model}`);
      console.log(`   启用状态: ${doubaoTTSConfig.enabled}`);
      console.log(`   支持TTS: ${doubaoTTSConfig.supportsTTS}`);
      console.log(`   状态: ${doubaoTTSConfig.status}`);
    } else {
      console.log('\n❌ 未找到豆包TTS配置');
    }
    
    // 检查Edge TTS配置
    const edgeTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'edge-tts',
        supportsTTS: true
      }
    });
    
    if (edgeTTSConfig) {
      console.log('\n🎤 Edge TTS配置详情:');
      console.log(`   ID: ${edgeTTSConfig.id}`);
      console.log(`   名称: ${edgeTTSConfig.name}`);
      console.log(`   启用状态: ${edgeTTSConfig.enabled}`);
      console.log(`   支持TTS: ${edgeTTSConfig.supportsTTS}`);
      console.log(`   状态: ${edgeTTSConfig.status}`);
    } else {
      console.log('\n❌ 未找到Edge TTS配置');
    }
    
  } catch (error) {
    console.error('❌ 检查TTS配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTTSConfig();
