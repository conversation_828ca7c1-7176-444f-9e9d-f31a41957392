const { PrismaClient } = require('@prisma/client');

async function cleanDoubaoConfig() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧹 清理豆包配置...');
    
    // 1. 删除所有旧的豆包配置
    const deleteResult = await prisma.aIConfig.deleteMany({
      where: {
        provider: 'doubao'
      }
    });
    
    console.log(`🗑️ 已删除 ${deleteResult.count} 个旧配置`);
    
    // 2. 只添加正确的新配置（与您控制台完全匹配）
    const correctModels = [
      {
        provider: 'doubao',
        model: 'ep-20250624192235-zttm6',
        name: 'Doubao-Seedance-1.0-pro',
        description: '豆包专业版视频生成模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624195026-qjsmk',
        name: 'Doubao-Seedance-1.0-lite-i2v',
        description: '豆包图生视频模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: true,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      },
      {
        provider: 'doubao',
        model: 'ep-20250624192345-5ccwj',
        name: 'Doubao-Seedance-1.0-lite-t2v',
        description: '豆包文生视频模型',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c',
        enabled: true,
        supportsVideo: true,
        supportsImage: false,
        supportsImageToVideo: false,
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      }
    ];
    
    // 3. 添加正确的配置
    for (const model of correctModels) {
      await prisma.aIConfig.create({ data: model });
      console.log(`✅ 已添加: ${model.name} (${model.model})`);
    }
    
    console.log('\n🎉 豆包配置清理完成！');
    console.log('现在只有与您控制台完全匹配的配置。');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanDoubaoConfig();
