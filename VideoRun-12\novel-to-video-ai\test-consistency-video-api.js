// 测试一致性视频生成API
async function testConsistencyVideoAPI() {
  try {
    console.log('🎬 测试一致性视频生成API...');
    
    const episodeId = 'cmc60kmf30001vm3cw6lvt6ki'; // 测试剧集ID
    
    console.log('📤 发送一致性视频生成请求...');
    
    const response = await fetch('http://localhost:3001/api/ai/generate-video-with-consistency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        episodeId,
        consistencyMode: 'balanced',
        includeReferenceImages: true,
        style: 'cinematic',
        quality: 'high',
        customEnhancement: '强调角色表情和动作的一致性'
      })
    });

    console.log('📊 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API响应失败:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 一致性视频生成成功！');
      console.log('\n📋 生成结果：');
      
      console.log('🆔 生成ID:', result.data.generationId);
      console.log('🎭 一致性模式:', result.data.consistencyInfo.mode);
      console.log('👥 角色数量:', result.data.consistencyInfo.characterCount);
      console.log('🖼️ 参考图像数量:', result.data.consistencyInfo.referenceImageCount);
      console.log('⏱️ 预计处理时间:', result.data.estimatedProcessingTime);
      
      console.log('\n🎯 一致性特性:');
      Object.entries(result.data.consistencyInfo.consistencyFeatures).forEach(([key, enabled]) => {
        if (enabled) {
          console.log(`  ✓ ${key}`);
        }
      });
      
      console.log('\n👤 角色信息:');
      result.data.characters.forEach((char, index) => {
        console.log(`  ${index + 1}. ${char.name}`);
        console.log(`     - 参考图像: ${char.hasReferenceImages ? '有' : '无'}`);
        console.log(`     - 一致性评分: ${char.consistencyScore || '未设置'}`);
      });
      
      console.log('\n📝 生成的提示词长度:', result.data.prompt.length, '字符');
      console.log('📝 提示词预览:', result.data.prompt.substring(0, 300) + '...');
      
    } else {
      console.log('❌ 生成失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testConsistencyVideoAPI();
