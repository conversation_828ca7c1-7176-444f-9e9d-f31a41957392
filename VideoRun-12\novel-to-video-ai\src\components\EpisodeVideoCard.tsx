'use client'

import { useState, useEffect } from 'react'
import { Video, Play, Clock, Download, Eye, RotateCcw, Shield } from 'lucide-react'

interface Episode {
  id: string
  title: string
  content: string
}

interface VideoSegment {
  id: string
  title: string
  videoUrl: string
  duration: number
  createdAt: string
  status: 'generating' | 'completed' | 'failed'
}

interface EpisodeVideoCardProps {
  episode: Episode
  episodeIndex: number
  projectId: string
  onViewSegments: () => void
  onGenerateConsistencyVideo?: () => void
}

export default function EpisodeVideoCard({ episode, episodeIndex, projectId, onViewSegments, onGenerateConsistencyVideo }: EpisodeVideoCardProps) {
  const [videoSegments, setVideoSegments] = useState<VideoSegment[]>([])
  const [loading, setLoading] = useState(true)
  const [totalDuration, setTotalDuration] = useState(0)

  // 加载视频片段
  useEffect(() => {
    loadVideoSegments()
  }, [episode.id])

  const loadVideoSegments = async () => {
    try {
      setLoading(true)
      // 调用API获取该剧集的视频片段
      const response = await fetch(`/api/ai/video-segments?episodeId=${episode.id}`)
      if (response.ok) {
        const data = await response.json()
        console.log('API返回的完整数据:', data) // 调试日志

        // 修复数据结构访问
        const segments = data.data?.segments || data.segments || []
        setVideoSegments(segments)

        // 调试信息
        console.log(`Episode ${episode.title} segments:`, segments)
        console.log(`Generating segments count:`, segments.filter((s: VideoSegment) => s.status === 'generating').length)

        // 计算总时长
        const total = segments.reduce((sum: number, segment: VideoSegment) =>
          sum + (segment.duration || 0), 0)
        setTotalDuration(total)
      } else {
        console.error('Failed to load video segments:', response.status)
        setVideoSegments([])
        setTotalDuration(0)
      }
    } catch (error) {
      console.error('加载视频片段失败:', error)
      setVideoSegments([])
      setTotalDuration(0)
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 重试失败的片段
  const retryFailedSegment = async (segmentId: string) => {
    try {
      console.log(`🔄 开始重试片段: ${segmentId}`)

      // 调用专门的重试API
      const response = await fetch('/api/ai/retry-failed-segment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          segmentId: segmentId
        })
      })

      const data = await response.json()
      if (data.success) {
        console.log('✅ 重试请求成功提交')
        alert('重试已启动，片段正在重新生成中...')
        loadVideoSegments() // 刷新数据
      } else {
        console.error('❌ 重试请求失败:', data.error)
        alert(`重试失败: ${data.error || '请稍后再试'}`)
      }
    } catch (error) {
      console.error('❌ 重试请求异常:', error)
      alert('重试失败，请检查网络连接后再试')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100'
      case 'generating': return 'text-yellow-600 bg-yellow-100'
      case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'generating': return '生成中'
      case 'failed': return '生成失败'
      default: return '未知状态'
    }
  }

  const completedSegments = videoSegments.filter(segment => segment.status === 'completed')
  const generatingSegments = videoSegments.filter(segment => segment.status === 'generating')

  // 确保数据一致性：如果没有任何片段，则生成中的片段数量应该为0
  const actualGeneratingCount = videoSegments.length === 0 ? 0 : generatingSegments.length

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      {/* 剧集标题 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <span className="bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full mr-3">
            第{episodeIndex + 1}集
          </span>
          <h4 className="text-lg font-semibold text-gray-900">{episode.title}</h4>
        </div>
        <div className="flex space-x-2">
          {onGenerateConsistencyVideo && (
            <button
              onClick={onGenerateConsistencyVideo}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors"
            >
              <Shield className="mr-2" size={16} />
              一致性视频
            </button>
          )}
          <button
            onClick={onViewSegments}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 transition-colors"
          >
            <Eye className="mr-2" size={16} />
            查看所有片段
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-2 text-gray-600">加载视频片段...</span>
        </div>
      ) : (
        <>
          {/* 视频统计信息 */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{videoSegments.length}</div>
              <div className="text-sm text-gray-600">总片段数</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{completedSegments.length}</div>
              <div className="text-sm text-gray-600">已完成</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{formatDuration(totalDuration)}</div>
              <div className="text-sm text-gray-600">总时长</div>
            </div>
          </div>

          {/* 视频片段预览 */}
          {videoSegments.length > 0 ? (
            <div className="space-y-3">
              <h5 className="text-sm font-medium text-gray-700 mb-2">视频片段预览</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {videoSegments.slice(0, 6).map((segment, index) => (
                  <div key={segment.id} className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-gray-500">片段 {index + 1}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(segment.status)}`}>
                        {getStatusText(segment.status)}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900 mb-1 truncate">
                      {segment.title}
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        {formatDuration(segment.duration)}
                      </span>
                      {segment.status === 'completed' && segment.videoUrl && (
                        <button
                          onClick={() => {
                            try {
                              const videoUrl = segment.videoUrl

                              // 如果是本地路径，直接在新窗口中播放
                              if (videoUrl.startsWith('/downloads/')) {
                                window.open(videoUrl, '_blank')
                              } else {
                                // 如果是远程URL，下载文件
                                const cleanUrl = videoUrl.replace(/&amp;/g, '&')
                                const link = document.createElement('a')
                                link.href = cleanUrl
                                link.download = `${segment.title}.mp4`
                                document.body.appendChild(link)
                                link.click()
                                document.body.removeChild(link)
                              }
                            } catch (error) {
                              console.error('播放/下载视频失败:', error)
                              alert('播放/下载视频失败，请稍后重试')
                            }
                          }}
                          className="flex items-center text-purple-600 hover:text-purple-800"
                        >
                          {segment.videoUrl.startsWith('/downloads/') ? (
                            <>
                              <Play size={12} className="mr-1" />
                              播放
                            </>
                          ) : (
                            <>
                              <Download size={12} className="mr-1" />
                              下载
                            </>
                          )}
                        </button>
                      )}
                      {segment.status === 'failed' && (
                        <button
                          onClick={() => retryFailedSegment(segment.id)}
                          className="flex items-center text-red-600 hover:text-red-800"
                          title="重新生成"
                        >
                          <RotateCcw size={12} className="mr-1" />
                          重试
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              {videoSegments.length > 6 && (
                <div className="text-center">
                  <button
                    onClick={onViewSegments}
                    className="text-sm text-purple-600 hover:text-purple-800 font-medium"
                  >
                    查看全部 {videoSegments.length} 个片段 →
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Video size={48} className="mx-auto mb-2 text-gray-300" />
              <p>暂无视频片段</p>
              <p className="text-sm">请先生成视频内容</p>
            </div>
          )}

          {/* 生成中的提示 - 只有在真正有生成中的片段时才显示 */}
          {actualGeneratingCount > 0 && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                <span className="text-sm text-yellow-800">
                  正在生成 {actualGeneratingCount} 个视频片段...
                </span>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
