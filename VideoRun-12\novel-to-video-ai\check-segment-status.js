const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkSegmentStatus() {
  try {
    const segments = await prisma.videoSegment.findMany({
      where: { episodeId: 'cmccxomfg000avmi0yknpfrue' },
      orderBy: { segmentIndex: 'asc' },
      select: {
        id: true,
        segmentIndex: true,
        title: true,
        status: true,
        videoUrl: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    console.log('📊 当前片段状态:');
    segments.forEach(segment => {
      console.log(`片段 ${segment.segmentIndex}: ${segment.status} - ${segment.title.substring(0, 30)}...`);
      console.log(`   ID: ${segment.id}`);
      console.log(`   视频URL: ${segment.videoUrl || '无'}`);
      console.log(`   更新时间: ${segment.updatedAt}`);
      console.log('');
    });
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSegmentStatus();
