{"pages": {"/api/ai/generate-tts/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/ai/generate-tts/route.js"], "/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/api/projects/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/projects/route.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/projects/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/projects/page.js"], "/projects/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/projects/[id]/page.js"], "/api/projects/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/projects/[id]/route.js"], "/api/models/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/models/route.js"], "/api/ai/video-segments/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/ai/video-segments/route.js"], "/projects/new/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/projects/new/page.js"], "/api/projects/[id]/upload/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/projects/[id]/upload/route.js"], "/api/projects/[id]/analyze/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/projects/[id]/analyze/route.js"], "/models/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/models/page.js"], "/api/models/test/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/models/test/route.js"], "/api/ai/generate-appearance/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/ai/generate-appearance/route.js"]}}