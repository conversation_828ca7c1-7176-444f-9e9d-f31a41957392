// 测试豆包endpoint ID调用
async function testDoubaoEndpoint() {
  try {
    console.log('🔗 测试豆包endpoint ID调用...');
    
    const realApiKey = '6c29371e-9766-46bb-9d24-f436c8038c6a';
    
    // 尝试不同的模型名称格式
    const modelVariants = [
      'doubao-seedance-1-0-pro-250528',
      'ep-20250622213553-xxxxx', // endpoint ID格式
      'doubao-seedance-1.0-pro',
      'doubao-seedance-pro',
      'seedance-1.0-pro'
    ];
    
    for (const modelName of modelVariants) {
      console.log(`\n🧪 测试模型名称: ${modelName}`);
      
      try {
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${realApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: modelName,
            messages: [
              { role: 'user', content: '测试' }
            ],
            max_tokens: 10
          })
        });
        
        console.log('📊 状态码:', response.status);
        
        if (response.ok) {
          console.log('✅ 成功！');
          const data = await response.json();
          console.log('📦 响应:', JSON.stringify(data, null, 2));
          break;
        } else {
          const errorText = await response.text();
          console.log('❌ 失败:', errorText.substring(0, 200));
          
          // 分析错误类型
          if (errorText.includes('model') && errorText.includes('not found')) {
            console.log('💡 模型名称不正确');
          } else if (errorText.includes('InternalServiceError')) {
            console.log('💡 服务内部错误');
          } else if (errorText.includes('endpoint')) {
            console.log('💡 可能需要使用endpoint ID');
          }
        }
      } catch (error) {
        console.log('💥 请求异常:', error.message);
      }
    }
    
    // 检查是否需要创建endpoint
    console.log('\n📋 火山方舟使用说明:');
    console.log('   1. 在火山方舟控制台中，某些模型需要先创建"推理接入点"');
    console.log('   2. 创建接入点后会得到一个endpoint ID（如：ep-20250622213553-xxxxx）');
    console.log('   3. 使用endpoint ID作为model参数进行API调用');
    console.log('   4. 豆包视频生成模型可能需要这种方式');
    
    console.log('\n🔧 解决步骤:');
    console.log('   1. 登录火山方舟控制台');
    console.log('   2. 找到豆包视频生成模型');
    console.log('   3. 点击"创建推理接入点"');
    console.log('   4. 复制生成的endpoint ID');
    console.log('   5. 在系统中使用endpoint ID替换模型名称');
    
    console.log('\n⚠️ 当前状态:');
    console.log('   • API密钥有效');
    console.log('   • 服务端返回内部错误');
    console.log('   • 可能需要endpoint ID而不是模型名称');
    console.log('   • 或者豆包视频生成服务暂时不可用');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoEndpoint();
