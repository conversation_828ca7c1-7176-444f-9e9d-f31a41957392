const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCharacterImages() {
  try {
    console.log('🔍 检查角色参考图像...');
    
    // 查询张三的角色信息
    const zhangsan = await prisma.character.findFirst({
      where: { name: '张三' }
    });
    
    if (!zhangsan) {
      console.log('❌ 未找到张三角色');
      return;
    }
    
    console.log(`✅ 找到张三角色: ${zhangsan.id}`);
    console.log(`外貌描述: ${zhangsan.facial?.substring(0, 100)}...`);
    
    if (zhangsan.generatedImages) {
      try {
        const images = JSON.parse(zhangsan.generatedImages);
        console.log('\n🖼️ 张三的参考图像:');
        console.log(`- 正面图: ${images.front ? '✅ 有' : '❌ 无'}`);
        console.log(`- 侧面图: ${images.side ? '✅ 有' : '❌ 无'}`);
        console.log(`- 背面图: ${images.back ? '✅ 有' : '❌ 无'}`);
        
        if (images.front) {
          console.log(`\n正面图URL: ${images.front.substring(0, 100)}...`);
        }
      } catch (e) {
        console.log('❌ 解析图像数据失败:', e.message);
      }
    } else {
      console.log('❌ 张三没有生成的参考图像');
    }
    
    // 查询李四的角色信息
    const lisi = await prisma.character.findFirst({
      where: { name: '李四' }
    });
    
    if (lisi) {
      console.log(`\n✅ 找到李四角色: ${lisi.id}`);
      if (lisi.generatedImages) {
        try {
          const images = JSON.parse(lisi.generatedImages);
          console.log('🖼️ 李四的参考图像:');
          console.log(`- 正面图: ${images.front ? '✅ 有' : '❌ 无'}`);
          console.log(`- 侧面图: ${images.side ? '✅ 有' : '❌ 无'}`);
          console.log(`- 背面图: ${images.back ? '✅ 有' : '❌ 无'}`);
        } catch (e) {
          console.log('❌ 解析李四图像数据失败:', e.message);
        }
      } else {
        console.log('❌ 李四没有生成的参考图像');
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCharacterImages();
