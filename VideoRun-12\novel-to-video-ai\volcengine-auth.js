const crypto = require('crypto');

// 火山引擎V4签名算法实现
class VolcengineAuth {
  constructor(accessKeyId, secretAccessKey, region = 'cn-north-1', service = 'tts') {
    this.accessKeyId = accessKeyId;
    this.secretAccessKey = secretAccessKey;
    this.region = region;
    this.service = service;
  }

  // 生成签名
  generateSignature(method, url, headers, body, timestamp) {
    const urlObj = new URL(url);
    const host = urlObj.host;
    const path = urlObj.pathname;
    const query = urlObj.search.slice(1); // 去掉?

    // 1. 创建规范请求
    const canonicalRequest = this.createCanonicalRequest(method, path, query, headers, body);
    
    // 2. 创建待签名字符串
    const stringToSign = this.createStringToSign(timestamp, canonicalRequest);
    
    // 3. 计算签名
    const signature = this.calculateSignature(timestamp, stringToSign);
    
    // 4. 创建Authorization头
    const authorization = this.createAuthorizationHeader(timestamp, signature, headers);
    
    return {
      authorization,
      signature,
      canonicalRequest,
      stringToSign
    };
  }

  // 创建规范请求
  createCanonicalRequest(method, path, query, headers, body) {
    // 规范化URI
    const canonicalUri = path || '/';
    
    // 规范化查询字符串
    const canonicalQueryString = this.canonicalizeQueryString(query);
    
    // 规范化头部
    const { canonicalHeaders, signedHeaders } = this.canonicalizeHeaders(headers);
    
    // 计算payload哈希
    const payloadHash = this.hashSHA256(body || '');
    
    return [
      method,
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash
    ].join('\n');
  }

  // 规范化查询字符串
  canonicalizeQueryString(query) {
    if (!query) return '';
    
    const params = new URLSearchParams(query);
    const sortedParams = Array.from(params.entries()).sort();
    
    return sortedParams
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
  }

  // 规范化头部
  canonicalizeHeaders(headers) {
    const lowerCaseHeaders = {};
    const signedHeaderNames = [];
    
    // 转换为小写并排序
    Object.keys(headers).forEach(key => {
      const lowerKey = key.toLowerCase();
      lowerCaseHeaders[lowerKey] = headers[key].trim();
      signedHeaderNames.push(lowerKey);
    });
    
    signedHeaderNames.sort();
    
    const canonicalHeaders = signedHeaderNames
      .map(name => `${name}:${lowerCaseHeaders[name]}`)
      .join('\n') + '\n';
    
    const signedHeaders = signedHeaderNames.join(';');
    
    return { canonicalHeaders, signedHeaders };
  }

  // 创建待签名字符串
  createStringToSign(timestamp, canonicalRequest) {
    const algorithm = 'HMAC-SHA256';
    const credentialScope = this.getCredentialScope(timestamp);
    const hashedCanonicalRequest = this.hashSHA256(canonicalRequest);
    
    return [
      algorithm,
      timestamp,
      credentialScope,
      hashedCanonicalRequest
    ].join('\n');
  }

  // 获取凭证范围
  getCredentialScope(timestamp) {
    const dateStamp = timestamp.slice(0, 8);
    return `${dateStamp}/${this.region}/${this.service}/request`;
  }

  // 计算签名
  calculateSignature(timestamp, stringToSign) {
    const dateStamp = timestamp.slice(0, 8);
    
    const kDate = this.hmacSHA256(`HMAC-SHA256${this.secretAccessKey}`, dateStamp);
    const kRegion = this.hmacSHA256(kDate, this.region);
    const kService = this.hmacSHA256(kRegion, this.service);
    const kSigning = this.hmacSHA256(kService, 'request');
    
    return this.hmacSHA256(kSigning, stringToSign).toString('hex');
  }

  // 创建Authorization头
  createAuthorizationHeader(timestamp, signature, headers) {
    const { signedHeaders } = this.canonicalizeHeaders(headers);
    const credentialScope = this.getCredentialScope(timestamp);
    const credential = `${this.accessKeyId}/${credentialScope}`;
    
    return `HMAC-SHA256 Credential=${credential}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  }

  // SHA256哈希
  hashSHA256(data) {
    return crypto.createHash('sha256').update(data, 'utf8').digest('hex');
  }

  // HMAC-SHA256
  hmacSHA256(key, data) {
    return crypto.createHmac('sha256', key).update(data, 'utf8').digest();
  }

  // 生成时间戳
  static generateTimestamp() {
    const now = new Date();
    return now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
  }

  // 为TTS API生成完整的请求头
  generateTTSHeaders(body) {
    const timestamp = VolcengineAuth.generateTimestamp();
    const url = 'https://openspeech.bytedance.com/api/v1/tts';
    
    const headers = {
      'Content-Type': 'application/json',
      'Host': 'openspeech.bytedance.com',
      'X-Date': timestamp
    };
    
    const { authorization } = this.generateSignature('POST', url, headers, body, timestamp);
    
    return {
      ...headers,
      'Authorization': authorization
    };
  }
}

module.exports = VolcengineAuth;
