{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_setup.js"], "sourcesContent": ["// Current version.\nexport var VERSION = '1.13.7';\n\n// Establish the root object, `window` (`self`) in the browser, `global`\n// on the server, or `this` in some virtual machines. We use `self`\n// instead of `window` for `WebWorker` support.\nexport var root = (typeof self == 'object' && self.self === self && self) ||\n          (typeof global == 'object' && global.global === global && global) ||\n          Function('return this')() ||\n          {};\n\n// Save bytes in the minified (but not gzipped) version:\nexport var ArrayProto = Array.prototype, ObjProto = Object.prototype;\nexport var SymbolProto = typeof Symbol !== 'undefined' ? Symbol.prototype : null;\n\n// Create quick reference variables for speed access to core prototypes.\nexport var push = ArrayProto.push,\n    slice = ArrayProto.slice,\n    toString = ObjProto.toString,\n    hasOwnProperty = ObjProto.hasOwnProperty;\n\n// Modern feature detection.\nexport var supportsArrayBuffer = typeof ArrayBuffer !== 'undefined',\n    supportsDataView = typeof DataView !== 'undefined';\n\n// All **ECMAScript 5+** native function implementations that we hope to use\n// are declared here.\nexport var nativeIsArray = Array.isArray,\n    nativeKeys = Object.keys,\n    nativeCreate = Object.create,\n    nativeIsView = supportsArrayBuffer && ArrayBuffer.isView;\n\n// Create references to these builtin functions because we override them.\nexport var _isNaN = isNaN,\n    _isFinite = isFinite;\n\n// Keys in IE < 9 that won't be iterated by `for key in ...` and thus missed.\nexport var hasEnumBug = !{toString: null}.propertyIsEnumerable('toString');\nexport var nonEnumerableProps = ['valueOf', 'isPrototypeOf', 'toString',\n  'propertyIsEnumerable', 'hasOwnProperty', 'toLocaleString'];\n\n// The largest integer that can be represented exactly.\nexport var MAX_ARRAY_INDEX = Math.pow(2, 53) - 1;\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;AACZ,IAAI,UAAU;AAKd,IAAI,OAAO,AAAC,OAAO,QAAQ,YAAY,KAAK,IAAI,KAAK,QAAQ,QACzD,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,UAAU,UAC1D,SAAS,oBACT,CAAC;AAGJ,IAAI,aAAa,MAAM,SAAS,EAAE,WAAW,OAAO,SAAS;AAC7D,IAAI,cAAc,OAAO,WAAW,cAAc,OAAO,SAAS,GAAG;AAGrE,IAAI,OAAO,WAAW,IAAI,EAC7B,QAAQ,WAAW,KAAK,EACxB,WAAW,SAAS,QAAQ,EAC5B,iBAAiB,SAAS,cAAc;AAGrC,IAAI,sBAAsB,OAAO,gBAAgB,aACpD,mBAAmB,OAAO,aAAa;AAIpC,IAAI,gBAAgB,MAAM,OAAO,EACpC,aAAa,OAAO,IAAI,EACxB,eAAe,OAAO,MAAM,EAC5B,eAAe,uBAAuB,YAAY,MAAM;AAGrD,IAAI,SAAS,OAChB,YAAY;AAGT,IAAI,aAAa,CAAC,CAAA;IAAC,UAAU;AAAI,CAAA,EAAE,oBAAoB,CAAC;AACxD,IAAI,qBAAqB;IAAC;IAAW;IAAiB;IAC3D;IAAwB;IAAkB;CAAiB;AAGtD,IAAI,kBAAkB,KAAK,GAAG,CAAC,GAAG,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/restArguments.js"], "sourcesContent": ["// Some functions take a variable number of arguments, or a few expected\n// arguments at the beginning and then a variable number of values to operate\n// on. This helper accumulates all remaining arguments past the function’s\n// argument length (or an explicit `startIndex`), into an array that becomes\n// the last argument. Similar to ES6’s \"rest parameter\".\nexport default function restArguments(func, startIndex) {\n  startIndex = startIndex == null ? func.length - 1 : +startIndex;\n  return function() {\n    var length = Math.max(arguments.length - startIndex, 0),\n        rest = Array(length),\n        index = 0;\n    for (; index < length; index++) {\n      rest[index] = arguments[index + startIndex];\n    }\n    switch (startIndex) {\n      case 0: return func.call(this, rest);\n      case 1: return func.call(this, arguments[0], rest);\n      case 2: return func.call(this, arguments[0], arguments[1], rest);\n    }\n    var args = Array(startIndex + 1);\n    for (index = 0; index < startIndex; index++) {\n      args[index] = arguments[index];\n    }\n    args[startIndex] = rest;\n    return func.apply(this, args);\n  };\n}\n"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,6EAA6E;AAC7E,0EAA0E;AAC1E,4EAA4E;AAC5E,wDAAwD;;;;AACzC,SAAS,cAAc,IAAI,EAAE,UAAU;IACpD,aAAa,cAAc,OAAO,KAAK,MAAM,GAAG,IAAI,CAAC;IACrD,OAAO;QACL,IAAI,SAAS,KAAK,GAAG,CAAC,UAAU,MAAM,GAAG,YAAY,IACjD,OAAO,MAAM,SACb,QAAQ;QACZ,MAAO,QAAQ,QAAQ,QAAS;YAC9B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,WAAW;QAC7C;QACA,OAAQ;YACN,KAAK;gBAAG,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;YAC/B,KAAK;gBAAG,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE;YAC7C,KAAK;gBAAG,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE;QAC7D;QACA,IAAI,OAAO,MAAM,aAAa;QAC9B,IAAK,QAAQ,GAAG,QAAQ,YAAY,QAAS;YAC3C,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isObject.js"], "sourcesContent": ["// Is a given variable an object?\nexport default function isObject(obj) {\n  var type = typeof obj;\n  return type === 'function' || (type === 'object' && !!obj);\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;AAClB,SAAS,SAAS,GAAG;IAClC,IAAI,OAAO,OAAO;IAClB,OAAO,SAAS,cAAe,SAAS,YAAY,CAAC,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isNull.js"], "sourcesContent": ["// Is a given value equal to null?\nexport default function isNull(obj) {\n  return obj === null;\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;AACnB,SAAS,OAAO,GAAG;IAChC,OAAO,QAAQ;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isUndefined.js"], "sourcesContent": ["// Is a given variable undefined?\nexport default function isUndefined(obj) {\n  return obj === void 0;\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;AAClB,SAAS,YAAY,GAAG;IACrC,OAAO,QAAQ,KAAK;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isBoolean.js"], "sourcesContent": ["import { toString } from './_setup.js';\n\n// Is a given value a boolean?\nexport default function isBoolean(obj) {\n  return obj === true || obj === false || toString.call(obj) === '[object Boolean]';\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,UAAU,GAAG;IACnC,OAAO,QAAQ,QAAQ,QAAQ,SAAS,+IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isElement.js"], "sourcesContent": ["// Is a given value a DOM element?\nexport default function isElement(obj) {\n  return !!(obj && obj.nodeType === 1);\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;AACnB,SAAS,UAAU,GAAG;IACnC,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI,QAAQ,KAAK,CAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_tagTester.js"], "sourcesContent": ["import { toString } from './_setup.js';\n\n// Internal function for creating a `toString`-based type tester.\nexport default function tagTester(name) {\n  var tag = '[object ' + name + ']';\n  return function(obj) {\n    return toString.call(obj) === tag;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,UAAU,IAAI;IACpC,IAAI,MAAM,aAAa,OAAO;IAC9B,OAAO,SAAS,GAAG;QACjB,OAAO,+IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS;IAChC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isString.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('String');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isNumber.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('Number');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isDate.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('Date');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isRegExp.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('RegExp');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isError.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('Error');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isSymbol.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('Symbol');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isArrayBuffer.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('ArrayBuffer');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isFunction.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport { root } from './_setup.js';\n\nvar isFunction = tagTester('Function');\n\n// Optimize `isFunction` if appropriate. Work around some `typeof` bugs in old\n// v8, IE 11 (#1621), Safari 8 (#1929), and PhantomJS (#2236).\nvar nodelist = root.document && root.document.childNodes;\nif (typeof /./ != 'function' && typeof Int8Array != 'object' && typeof nodelist != 'function') {\n  isFunction = function(obj) {\n    return typeof obj == 'function' || false;\n  };\n}\n\nexport default isFunction;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;AAE3B,8EAA8E;AAC9E,8DAA8D;AAC9D,IAAI,WAAW,+IAAA,CAAA,OAAI,CAAC,QAAQ,IAAI,+IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,UAAU;AACxD,IAAI,OAAO,OAAO,cAAc,OAAO,aAAa,YAAY,OAAO,YAAY,YAAY;IAC7F,aAAa,SAAS,GAAG;QACvB,OAAO,OAAO,OAAO,cAAc;IACrC;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_hasObjectTag.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('Object');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_stringTagBug.js"], "sourcesContent": ["import { supportsDataView } from './_setup.js';\nimport hasObjectTag from './_hasObjectTag.js';\n\n// In IE 10 - Edge 13, `DataView` has string tag `'[object Object]'`.\n// In IE 11, the most common among them, this problem also applies to\n// `Map`, `WeakMap` and `Set`.\n// Also, there are cases where an application can override the native\n// `DataView` object, in cases like that we can't use the constructor\n// safely and should just rely on alternate `DataView` checks\nexport var hasDataViewBug = (\n      supportsDataView && (!/\\[native code\\]/.test(String(DataView)) || hasObjectTag(new DataView(new ArrayBuffer(8))))\n    ),\n    isIE11 = (typeof Map !== 'undefined' && hasObjectTag(new Map));\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,IAAI,iBACL,+IAAA,CAAA,mBAAgB,IAAI,CAAC,CAAC,kBAAkB,IAAI,CAAC,OAAO,cAAc,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,IAAI,SAAS,IAAI,YAAY,IAAI,GAElH,SAAU,OAAO,QAAQ,eAAe,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isDataView.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport isFunction from './isFunction.js';\nimport isArrayBuffer from './isArrayBuffer.js';\nimport { hasDataViewBug } from './_stringTagBug.js';\n\nvar isDataView = tagTester('DataView');\n\n// In IE 10 - Edge 13, we need a different heuristic\n// to determine whether an object is a `DataView`.\n// Also, in cases where the native `DataView` is\n// overridden we can't rely on the tag itself.\nfunction alternateIsDataView(obj) {\n  return obj != null && isFunction(obj.getInt8) && isArrayBuffer(obj.buffer);\n}\n\nexport default (hasDataViewBug ? alternateIsDataView : isDataView);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;AAE3B,oDAAoD;AACpD,kDAAkD;AAClD,gDAAgD;AAChD,8CAA8C;AAC9C,SAAS,oBAAoB,GAAG;IAC9B,OAAO,OAAO,QAAQ,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,IAAI,OAAO,KAAK,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,IAAI,MAAM;AAC3E;uCAEgB,sJAAA,CAAA,iBAAc,GAAG,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isArray.js"], "sourcesContent": ["import { nativeIsArray } from './_setup.js';\nimport tagTester from './_tagTester.js';\n\n// Is a given value an array?\n// Delegates to ECMA5's native `Array.isArray`.\nexport default nativeIsArray || tagTester('Array');\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,+IAAA,CAAA,gBAAa,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_has.js"], "sourcesContent": ["import { hasOwnProperty } from './_setup.js';\n\n// Internal function to check whether `key` is an own property name of `obj`.\nexport default function has(obj, key) {\n  return obj != null && hasOwnProperty.call(obj, key);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,IAAI,GAAG,EAAE,GAAG;IAClC,OAAO,OAAO,QAAQ,+IAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,KAAK;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isArguments.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport has from './_has.js';\n\nvar isArguments = tagTester('Arguments');\n\n// Define a fallback version of the method in browsers (ahem, IE < 9), where\n// there isn't any inspectable \"Arguments\" type.\n(function() {\n  if (!isArguments(arguments)) {\n    isArguments = function(obj) {\n      return has(obj, 'callee');\n    };\n  }\n}());\n\nexport default isArguments;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,cAAc,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;AAE5B,4EAA4E;AAC5E,gDAAgD;AAC/C,CAAA;IACC,IAAI,CAAC,YAAY,YAAY;QAC3B,cAAc,SAAS,GAAG;YACxB,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,KAAK;QAClB;IACF;AACF,CAAA;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isFinite.js"], "sourcesContent": ["import { _isFinite } from './_setup.js';\nimport isSymbol from './isSymbol.js';\n\n// Is a given object a finite number?\nexport default function isFinite(obj) {\n  return !isSymbol(obj) && _isFinite(obj) && !isNaN(parseFloat(obj));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,SAAS,GAAG;IAClC,OAAO,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,MAAM,WAAW;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isNaN.js"], "sourcesContent": ["import { _isNaN } from './_setup.js';\nimport isNumber from './isNumber.js';\n\n// Is the given value `NaN`?\nexport default function isNaN(obj) {\n  return isNumber(obj) && _isNaN(obj);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,MAAM,GAAG;IAC/B,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,CAAA,GAAA,+IAAA,CAAA,SAAM,AAAD,EAAE;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/constant.js"], "sourcesContent": ["// Predicate-generating function. Often useful outside of Underscore.\nexport default function constant(value) {\n  return function() {\n    return value;\n  };\n}\n"], "names": [], "mappings": "AAAA,qEAAqE;;;;AACtD,SAAS,SAAS,KAAK;IACpC,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createSizePropertyCheck.js"], "sourcesContent": ["import { MAX_ARRAY_INDEX } from './_setup.js';\n\n// Common internal logic for `isArrayLike` and `isBufferLike`.\nexport default function createSizePropertyCheck(getSizeProperty) {\n  return function(collection) {\n    var sizeProperty = getSizeProperty(collection);\n    return typeof sizeProperty == 'number' && sizeProperty >= 0 && sizeProperty <= MAX_ARRAY_INDEX;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,wBAAwB,eAAe;IAC7D,OAAO,SAAS,UAAU;QACxB,IAAI,eAAe,gBAAgB;QACnC,OAAO,OAAO,gBAAgB,YAAY,gBAAgB,KAAK,gBAAgB,+IAAA,CAAA,kBAAe;IAChG;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_shallowProperty.js"], "sourcesContent": ["// Internal helper to generate a function to obtain property `key` from `obj`.\nexport default function shallowProperty(key) {\n  return function(obj) {\n    return obj == null ? void 0 : obj[key];\n  };\n}\n"], "names": [], "mappings": "AAAA,8EAA8E;;;;AAC/D,SAAS,gBAAgB,GAAG;IACzC,OAAO,SAAS,GAAG;QACjB,OAAO,OAAO,OAAO,KAAK,IAAI,GAAG,CAAC,IAAI;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_getByteLength.js"], "sourcesContent": ["import shallowProperty from './_shallowProperty.js';\n\n// Internal helper to obtain the `byteLength` property of an object.\nexport default shallowProperty('byteLength');\n"], "names": [], "mappings": ";;;AAAA;;uCAGe,CAAA,GAAA,yJAAA,CAAA,UAAe,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_isBufferLike.js"], "sourcesContent": ["import createSizePropertyCheck from './_createSizePropertyCheck.js';\nimport getByteLength from './_getByteLength.js';\n\n// Internal helper to determine whether we should spend extensive checks against\n// `ArrayBuffer` et al.\nexport default createSizePropertyCheck(getByteLength);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,CAAA,GAAA,iKAAA,CAAA,UAAuB,AAAD,EAAE,uJAAA,CAAA,UAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isTypedArray.js"], "sourcesContent": ["import { supportsArrayBuffer, nativeIsView, toString } from './_setup.js';\nimport isDataView from './isDataView.js';\nimport constant from './constant.js';\nimport isBufferLike from './_isBufferLike.js';\n\n// Is a given value a typed array?\nvar typedArrayPattern = /\\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\\]/;\nfunction isTypedArray(obj) {\n  // `ArrayBuffer.isView` is the most future-proof, so use it when available.\n  // Otherwise, fall back on the above regular expression.\n  return nativeIsView ? (nativeIsView(obj) && !isDataView(obj)) :\n                isBufferLike(obj) && typedArrayPattern.test(toString.call(obj));\n}\n\nexport default supportsArrayBuffer ? isTypedArray : constant(false);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,kCAAkC;AAClC,IAAI,oBAAoB;AACxB,SAAS,aAAa,GAAG;IACvB,2EAA2E;IAC3E,wDAAwD;IACxD,OAAO,+IAAA,CAAA,eAAY,GAAI,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,OAC1C,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,QAAQ,kBAAkB,IAAI,CAAC,+IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC;AAC1E;uCAEe,+IAAA,CAAA,sBAAmB,GAAG,eAAe,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_getLength.js"], "sourcesContent": ["import shallowProperty from './_shallowProperty.js';\n\n// Internal helper to obtain the `length` property of an object.\nexport default shallowProperty('length');\n"], "names": [], "mappings": ";;;AAAA;;uCAGe,CAAA,GAAA,yJAAA,CAAA,UAAe,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_collectNonEnumProps.js"], "sourcesContent": ["import { nonEnumerableProps, ObjProto } from './_setup.js';\nimport isFunction from './isFunction.js';\nimport has from './_has.js';\n\n// Internal helper to create a simple lookup structure.\n// `collectNonEnumProps` used to depend on `_.contains`, but this led to\n// circular imports. `emulatedSet` is a one-off solution that only works for\n// arrays of strings.\nfunction emulatedSet(keys) {\n  var hash = {};\n  for (var l = keys.length, i = 0; i < l; ++i) hash[keys[i]] = true;\n  return {\n    contains: function(key) { return hash[key] === true; },\n    push: function(key) {\n      hash[key] = true;\n      return keys.push(key);\n    }\n  };\n}\n\n// Internal helper. Checks `keys` for the presence of keys in IE < 9 that won't\n// be iterated by `for key in ...` and thus missed. Extends `keys` in place if\n// needed.\nexport default function collectNonEnumProps(obj, keys) {\n  keys = emulatedSet(keys);\n  var nonEnumIdx = nonEnumerableProps.length;\n  var constructor = obj.constructor;\n  var proto = (isFunction(constructor) && constructor.prototype) || ObjProto;\n\n  // Constructor is a special case.\n  var prop = 'constructor';\n  if (has(obj, prop) && !keys.contains(prop)) keys.push(prop);\n\n  while (nonEnumIdx--) {\n    prop = nonEnumerableProps[nonEnumIdx];\n    if (prop in obj && obj[prop] !== proto[prop] && !keys.contains(prop)) {\n      keys.push(prop);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,uDAAuD;AACvD,wEAAwE;AACxE,4EAA4E;AAC5E,qBAAqB;AACrB,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,CAAC;IACZ,IAAK,IAAI,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;IAC7D,OAAO;QACL,UAAU,SAAS,GAAG;YAAI,OAAO,IAAI,CAAC,IAAI,KAAK;QAAM;QACrD,MAAM,SAAS,GAAG;YAChB,IAAI,CAAC,IAAI,GAAG;YACZ,OAAO,KAAK,IAAI,CAAC;QACnB;IACF;AACF;AAKe,SAAS,oBAAoB,GAAG,EAAE,IAAI;IACnD,OAAO,YAAY;IACnB,IAAI,aAAa,+IAAA,CAAA,qBAAkB,CAAC,MAAM;IAC1C,IAAI,cAAc,IAAI,WAAW;IACjC,IAAI,QAAQ,AAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,gBAAgB,YAAY,SAAS,IAAK,+IAAA,CAAA,WAAQ;IAE1E,iCAAiC;IACjC,IAAI,OAAO;IACX,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,SAAS,CAAC,KAAK,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC;IAEtD,MAAO,aAAc;QACnB,OAAO,+IAAA,CAAA,qBAAkB,CAAC,WAAW;QACrC,IAAI,QAAQ,OAAO,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,QAAQ,CAAC,OAAO;YACpE,KAAK,IAAI,CAAC;QACZ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/keys.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport { nativeKeys, hasEnumBug } from './_setup.js';\nimport has from './_has.js';\nimport collectNonEnumProps from './_collectNonEnumProps.js';\n\n// Retrieve the names of an object's own properties.\n// Delegates to **ECMAScript 5**'s native `Object.keys`.\nexport default function keys(obj) {\n  if (!isObject(obj)) return [];\n  if (nativeKeys) return nativeKeys(obj);\n  var keys = [];\n  for (var key in obj) if (has(obj, key)) keys.push(key);\n  // Ahem, IE < 9.\n  if (hasEnumBug) collectNonEnumProps(obj, keys);\n  return keys;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAIe,SAAS,KAAK,GAAG;IAC9B,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,OAAO,EAAE;IAC7B,IAAI,+IAAA,CAAA,aAAU,EAAE,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAU,AAAD,EAAE;IAClC,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,OAAO,IAAK,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,MAAM,KAAK,IAAI,CAAC;IAClD,gBAAgB;IAChB,IAAI,+IAAA,CAAA,aAAU,EAAE,CAAA,GAAA,6JAAA,CAAA,UAAmB,AAAD,EAAE,KAAK;IACzC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isEmpty.js"], "sourcesContent": ["import getLength from './_getLength.js';\nimport isArray from './isArray.js';\nimport isString from './isString.js';\nimport isArguments from './isArguments.js';\nimport keys from './keys.js';\n\n// Is a given array, string, or object empty?\n// An \"empty\" object has no enumerable own-properties.\nexport default function isEmpty(obj) {\n  if (obj == null) return true;\n  // Skip the more expensive `toString`-based type checks if `obj` has no\n  // `.length`.\n  var length = getLength(obj);\n  if (typeof length == 'number' && (\n    isArray(obj) || isString(obj) || isArguments(obj)\n  )) return length === 0;\n  return getLength(keys(obj)) === 0;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAIe,SAAS,QAAQ,GAAG;IACjC,IAAI,OAAO,MAAM,OAAO;IACxB,uEAAuE;IACvE,aAAa;IACb,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;IACvB,IAAI,OAAO,UAAU,YAAY,CAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,CAAA,GAAA,oJAAA,CAAA,UAAW,AAAD,EAAE,IAC/C,GAAG,OAAO,WAAW;IACrB,OAAO,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,UAAU;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isMatch.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Returns whether an object has a given set of `key:value` pairs.\nexport default function isMatch(object, attrs) {\n  var _keys = keys(attrs), length = _keys.length;\n  if (object == null) return !length;\n  var obj = Object(object);\n  for (var i = 0; i < length; i++) {\n    var key = _keys[i];\n    if (attrs[key] !== obj[key] || !(key in obj)) return false;\n  }\n  return true;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,QAAQ,MAAM,EAAE,KAAK;IAC3C,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,SAAS,MAAM,MAAM;IAC9C,IAAI,UAAU,MAAM,OAAO,CAAC;IAC5B,IAAI,MAAM,OAAO;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,IAAI,MAAM,KAAK,CAAC,EAAE;QAClB,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,GAAG,GAAG,OAAO;IACvD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/underscore.js"], "sourcesContent": ["import { VERSION } from './_setup.js';\n\n// If Underscore is called as a function, it returns a wrapped object that can\n// be used OO-style. This wrapper holds altered versions of all functions added\n// through `_.mixin`. Wrapped objects may be chained.\nexport default function _(obj) {\n  if (obj instanceof _) return obj;\n  if (!(this instanceof _)) return new _(obj);\n  this._wrapped = obj;\n}\n\n_.VERSION = VERSION;\n\n// Extracts the result from a wrapped and chained object.\n_.prototype.value = function() {\n  return this._wrapped;\n};\n\n// Provide unwrapping proxies for some methods used in engine operations\n// such as arithmetic and JSON stringification.\n_.prototype.valueOf = _.prototype.toJSON = _.prototype.value;\n\n_.prototype.toString = function() {\n  return String(this._wrapped);\n};\n"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,EAAE,GAAG;IAC3B,IAAI,eAAe,GAAG,OAAO;IAC7B,IAAI,CAAC,CAAC,IAAI,YAAY,CAAC,GAAG,OAAO,IAAI,EAAE;IACvC,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,EAAE,OAAO,GAAG,+IAAA,CAAA,UAAO;AAEnB,yDAAyD;AACzD,EAAE,SAAS,CAAC,KAAK,GAAG;IAClB,OAAO,IAAI,CAAC,QAAQ;AACtB;AAEA,wEAAwE;AACxE,+CAA+C;AAC/C,EAAE,SAAS,CAAC,OAAO,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,EAAE,SAAS,CAAC,KAAK;AAE5D,EAAE,SAAS,CAAC,QAAQ,GAAG;IACrB,OAAO,OAAO,IAAI,CAAC,QAAQ;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_toBufferView.js"], "sourcesContent": ["import getByteLength from './_getByteLength.js';\n\n// Internal function to wrap or shallow-copy an ArrayBuffer,\n// typed array or DataView to a new view, reusing the buffer.\nexport default function toBufferView(bufferSource) {\n  return new Uint8Array(\n    bufferSource.buffer || bufferSource,\n    bufferSource.byteOffset || 0,\n    getByteLength(bufferSource)\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,aAAa,YAAY;IAC/C,OAAO,IAAI,WACT,aAAa,MAAM,IAAI,cACvB,aAAa,UAAU,IAAI,GAC3B,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isEqual.js"], "sourcesContent": ["import _ from './underscore.js';\nimport { toString, SymbolProto } from './_setup.js';\nimport getByteLength from './_getByteLength.js';\nimport isTypedArray from './isTypedArray.js';\nimport isFunction from './isFunction.js';\nimport { hasDataViewBug }  from './_stringTagBug.js';\nimport isDataView from './isDataView.js';\nimport keys from './keys.js';\nimport has from './_has.js';\nimport toBufferView from './_toBufferView.js';\n\n// We use this string twice, so give it a name for minification.\nvar tagDataView = '[object DataView]';\n\n// Internal recursive comparison function for `_.isEqual`.\nfunction eq(a, b, aStack, bStack) {\n  // Identical objects are equal. `0 === -0`, but they aren't identical.\n  // See the [Harmony `egal` proposal](https://wiki.ecmascript.org/doku.php?id=harmony:egal).\n  if (a === b) return a !== 0 || 1 / a === 1 / b;\n  // `null` or `undefined` only equal to itself (strict comparison).\n  if (a == null || b == null) return false;\n  // `NaN`s are equivalent, but non-reflexive.\n  if (a !== a) return b !== b;\n  // Exhaust primitive checks\n  var type = typeof a;\n  if (type !== 'function' && type !== 'object' && typeof b != 'object') return false;\n  return deepEq(a, b, aStack, bStack);\n}\n\n// Internal recursive comparison function for `_.isEqual`.\nfunction deepEq(a, b, aStack, bStack) {\n  // Unwrap any wrapped objects.\n  if (a instanceof _) a = a._wrapped;\n  if (b instanceof _) b = b._wrapped;\n  // Compare `[[Class]]` names.\n  var className = toString.call(a);\n  if (className !== toString.call(b)) return false;\n  // Work around a bug in IE 10 - Edge 13.\n  if (hasDataViewBug && className == '[object Object]' && isDataView(a)) {\n    if (!isDataView(b)) return false;\n    className = tagDataView;\n  }\n  switch (className) {\n    // These types are compared by value.\n    case '[object RegExp]':\n      // RegExps are coerced to strings for comparison (Note: '' + /a/i === '/a/i')\n    case '[object String]':\n      // Primitives and their corresponding object wrappers are equivalent; thus, `\"5\"` is\n      // equivalent to `new String(\"5\")`.\n      return '' + a === '' + b;\n    case '[object Number]':\n      // `NaN`s are equivalent, but non-reflexive.\n      // Object(NaN) is equivalent to NaN.\n      if (+a !== +a) return +b !== +b;\n      // An `egal` comparison is performed for other numeric values.\n      return +a === 0 ? 1 / +a === 1 / b : +a === +b;\n    case '[object Date]':\n    case '[object Boolean]':\n      // Coerce dates and booleans to numeric primitive values. Dates are compared by their\n      // millisecond representations. Note that invalid dates with millisecond representations\n      // of `NaN` are not equivalent.\n      return +a === +b;\n    case '[object Symbol]':\n      return SymbolProto.valueOf.call(a) === SymbolProto.valueOf.call(b);\n    case '[object ArrayBuffer]':\n    case tagDataView:\n      // Coerce to typed array so we can fall through.\n      return deepEq(toBufferView(a), toBufferView(b), aStack, bStack);\n  }\n\n  var areArrays = className === '[object Array]';\n  if (!areArrays && isTypedArray(a)) {\n      var byteLength = getByteLength(a);\n      if (byteLength !== getByteLength(b)) return false;\n      if (a.buffer === b.buffer && a.byteOffset === b.byteOffset) return true;\n      areArrays = true;\n  }\n  if (!areArrays) {\n    if (typeof a != 'object' || typeof b != 'object') return false;\n\n    // Objects with different constructors are not equivalent, but `Object`s or `Array`s\n    // from different frames are.\n    var aCtor = a.constructor, bCtor = b.constructor;\n    if (aCtor !== bCtor && !(isFunction(aCtor) && aCtor instanceof aCtor &&\n                             isFunction(bCtor) && bCtor instanceof bCtor)\n                        && ('constructor' in a && 'constructor' in b)) {\n      return false;\n    }\n  }\n  // Assume equality for cyclic structures. The algorithm for detecting cyclic\n  // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.\n\n  // Initializing stack of traversed objects.\n  // It's done here since we only need them for objects and arrays comparison.\n  aStack = aStack || [];\n  bStack = bStack || [];\n  var length = aStack.length;\n  while (length--) {\n    // Linear search. Performance is inversely proportional to the number of\n    // unique nested structures.\n    if (aStack[length] === a) return bStack[length] === b;\n  }\n\n  // Add the first object to the stack of traversed objects.\n  aStack.push(a);\n  bStack.push(b);\n\n  // Recursively compare objects and arrays.\n  if (areArrays) {\n    // Compare array lengths to determine if a deep comparison is necessary.\n    length = a.length;\n    if (length !== b.length) return false;\n    // Deep compare the contents, ignoring non-numeric properties.\n    while (length--) {\n      if (!eq(a[length], b[length], aStack, bStack)) return false;\n    }\n  } else {\n    // Deep compare objects.\n    var _keys = keys(a), key;\n    length = _keys.length;\n    // Ensure that both objects contain the same number of properties before comparing deep equality.\n    if (keys(b).length !== length) return false;\n    while (length--) {\n      // Deep compare each member\n      key = _keys[length];\n      if (!(has(b, key) && eq(a[key], b[key], aStack, bStack))) return false;\n    }\n  }\n  // Remove the first object from the stack of traversed objects.\n  aStack.pop();\n  bStack.pop();\n  return true;\n}\n\n// Perform a deep comparison to check if two objects are equal.\nexport default function isEqual(a, b) {\n  return eq(a, b);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,gEAAgE;AAChE,IAAI,cAAc;AAElB,0DAA0D;AAC1D,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM;IAC9B,sEAAsE;IACtE,2FAA2F;IAC3F,IAAI,MAAM,GAAG,OAAO,MAAM,KAAK,IAAI,MAAM,IAAI;IAC7C,kEAAkE;IAClE,IAAI,KAAK,QAAQ,KAAK,MAAM,OAAO;IACnC,4CAA4C;IAC5C,IAAI,MAAM,GAAG,OAAO,MAAM;IAC1B,2BAA2B;IAC3B,IAAI,OAAO,OAAO;IAClB,IAAI,SAAS,cAAc,SAAS,YAAY,OAAO,KAAK,UAAU,OAAO;IAC7E,OAAO,OAAO,GAAG,GAAG,QAAQ;AAC9B;AAEA,0DAA0D;AAC1D,SAAS,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM;IAClC,8BAA8B;IAC9B,IAAI,aAAa,mJAAA,CAAA,UAAC,EAAE,IAAI,EAAE,QAAQ;IAClC,IAAI,aAAa,mJAAA,CAAA,UAAC,EAAE,IAAI,EAAE,QAAQ;IAClC,6BAA6B;IAC7B,IAAI,YAAY,+IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC;IAC9B,IAAI,cAAc,+IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,IAAI,OAAO;IAC3C,wCAAwC;IACxC,IAAI,sJAAA,CAAA,iBAAc,IAAI,aAAa,qBAAqB,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,IAAI;QACrE,IAAI,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,IAAI,OAAO;QAC3B,YAAY;IACd;IACA,OAAQ;QACN,qCAAqC;QACrC,KAAK;QACH,6EAA6E;QAC/E,KAAK;YACH,oFAAoF;YACpF,mCAAmC;YACnC,OAAO,KAAK,MAAM,KAAK;QACzB,KAAK;YACH,4CAA4C;YAC5C,oCAAoC;YACpC,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YAC9B,8DAA8D;YAC9D,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAC/C,KAAK;QACL,KAAK;YACH,qFAAqF;YACrF,wFAAwF;YACxF,+BAA+B;YAC/B,OAAO,CAAC,MAAM,CAAC;QACjB,KAAK;YACH,OAAO,+IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,+IAAA,CAAA,cAAW,CAAC,OAAO,CAAC,IAAI,CAAC;QAClE,KAAK;QACL,KAAK;YACH,gDAAgD;YAChD,OAAO,OAAO,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,IAAI,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,IAAI,QAAQ;IAC5D;IAEA,IAAI,YAAY,cAAc;IAC9B,IAAI,CAAC,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAY,AAAD,EAAE,IAAI;QAC/B,IAAI,aAAa,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE;QAC/B,IAAI,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE,IAAI,OAAO;QAC5C,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE,OAAO;QACnE,YAAY;IAChB;IACA,IAAI,CAAC,WAAW;QACd,IAAI,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU,OAAO;QAEzD,oFAAoF;QACpF,6BAA6B;QAC7B,IAAI,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW;QAChD,IAAI,UAAU,SAAS,CAAC,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,UAAU,iBAAiB,SACtC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,UAAU,iBAAiB,KAAK,KAC5C,iBAAiB,KAAK,iBAAiB,GAAI;YACjE,OAAO;QACT;IACF;IACA,4EAA4E;IAC5E,8EAA8E;IAE9E,2CAA2C;IAC3C,4EAA4E;IAC5E,SAAS,UAAU,EAAE;IACrB,SAAS,UAAU,EAAE;IACrB,IAAI,SAAS,OAAO,MAAM;IAC1B,MAAO,SAAU;QACf,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,MAAM,CAAC,OAAO,KAAK,GAAG,OAAO,MAAM,CAAC,OAAO,KAAK;IACtD;IAEA,0DAA0D;IAC1D,OAAO,IAAI,CAAC;IACZ,OAAO,IAAI,CAAC;IAEZ,0CAA0C;IAC1C,IAAI,WAAW;QACb,wEAAwE;QACxE,SAAS,EAAE,MAAM;QACjB,IAAI,WAAW,EAAE,MAAM,EAAE,OAAO;QAChC,8DAA8D;QAC9D,MAAO,SAAU;YACf,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,QAAQ,SAAS,OAAO;QACxD;IACF,OAAO;QACL,wBAAwB;QACxB,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,IAAI;QACrB,SAAS,MAAM,MAAM;QACrB,iGAAiG;QACjG,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,GAAG,MAAM,KAAK,QAAQ,OAAO;QACtC,MAAO,SAAU;YACf,2BAA2B;YAC3B,MAAM,KAAK,CAAC,OAAO;YACnB,IAAI,CAAC,CAAC,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,GAAG,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,OAAO,GAAG,OAAO;QACnE;IACF;IACA,+DAA+D;IAC/D,OAAO,GAAG;IACV,OAAO,GAAG;IACV,OAAO;AACT;AAGe,SAAS,QAAQ,CAAC,EAAE,CAAC;IAClC,OAAO,GAAG,GAAG;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/allKeys.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport { hasEnumBug } from './_setup.js';\nimport collectNonEnumProps from './_collectNonEnumProps.js';\n\n// Retrieve all the enumerable property names of an object.\nexport default function allKeys(obj) {\n  if (!isObject(obj)) return [];\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  // Ahem, IE < 9.\n  if (hasEnumBug) collectNonEnumProps(obj, keys);\n  return keys;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,QAAQ,GAAG;IACjC,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,OAAO,EAAE;IAC7B,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,OAAO,IAAK,KAAK,IAAI,CAAC;IAC/B,gBAAgB;IAChB,IAAI,+IAAA,CAAA,aAAU,EAAE,CAAA,GAAA,6JAAA,CAAA,UAAmB,AAAD,EAAE,KAAK;IACzC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_methodFingerprint.js"], "sourcesContent": ["import getLength from './_getLength.js';\nimport isFunction from './isFunction.js';\nimport allKeys from './allKeys.js';\n\n// Since the regular `Object.prototype.toString` type tests don't work for\n// some types in IE 11, we use a fingerprinting heuristic instead, based\n// on the methods. It's not great, but it's the best we got.\n// The fingerprint method lists are defined below.\nexport function ie11fingerprint(methods) {\n  var length = getLength(methods);\n  return function(obj) {\n    if (obj == null) return false;\n    // `Map`, `WeakMap` and `Set` have no enumerable keys.\n    var keys = allKeys(obj);\n    if (getLength(keys)) return false;\n    for (var i = 0; i < length; i++) {\n      if (!isFunction(obj[methods[i]])) return false;\n    }\n    // If we are testing against `WeakMap`, we need to ensure that\n    // `obj` doesn't have a `forEach` method in order to distinguish\n    // it from a regular `Map`.\n    return methods !== weakMapMethods || !isFunction(obj[forEachName]);\n  };\n}\n\n// In the interest of compact minification, we write\n// each string in the fingerprints only once.\nvar forEachName = 'forEach',\n    hasName = 'has',\n    commonInit = ['clear', 'delete'],\n    mapTail = ['get', hasName, 'set'];\n\n// `Map`, `WeakMap` and `Set` each have slightly different\n// combinations of the above sublists.\nexport var mapMethods = commonInit.concat(forEachName, mapTail),\n    weakMapMethods = commonInit.concat(mapTail),\n    setMethods = ['add'].concat(commonInit, forEachName, hasName);\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAMO,SAAS,gBAAgB,OAAO;IACrC,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;IACvB,OAAO,SAAS,GAAG;QACjB,IAAI,OAAO,MAAM,OAAO;QACxB,sDAAsD;QACtD,IAAI,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;QACnB,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,OAAO,OAAO;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAI,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO;QAC3C;QACA,8DAA8D;QAC9D,gEAAgE;QAChE,2BAA2B;QAC3B,OAAO,YAAY,kBAAkB,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,GAAG,CAAC,YAAY;IACnE;AACF;AAEA,oDAAoD;AACpD,6CAA6C;AAC7C,IAAI,cAAc,WACd,UAAU,OACV,aAAa;IAAC;IAAS;CAAS,EAChC,UAAU;IAAC;IAAO;IAAS;CAAM;AAI9B,IAAI,aAAa,WAAW,MAAM,CAAC,aAAa,UACnD,iBAAiB,WAAW,MAAM,CAAC,UACnC,aAAa;IAAC;CAAM,CAAC,MAAM,CAAC,YAAY,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isMap.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport { isIE11 } from './_stringTagBug.js';\nimport { ie11fingerprint, mapMethods }  from './_methodFingerprint.js';\n\nexport default isIE11 ? ie11fingerprint(mapMethods) : tagTester('Map');\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAEe,sJAAA,CAAA,SAAM,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,2JAAA,CAAA,aAAU,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isWeakMap.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport { isIE11 } from './_stringTagBug.js';\nimport { ie11fingerprint, weakMapMethods }  from './_methodFingerprint.js';\n\nexport default isIE11 ? ie11fingerprint(weakMapMethods) : tagTester('WeakMap');\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAEe,sJAAA,CAAA,SAAM,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,2JAAA,CAAA,iBAAc,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isSet.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\nimport { isIE11 } from './_stringTagBug.js';\nimport { ie11fingerprint, setMethods }  from './_methodFingerprint.js';\n\nexport default isIE11 ? ie11fingerprint(setMethods) : tagTester('Set');\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAEe,sJAAA,CAAA,SAAM,GAAG,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,2JAAA,CAAA,aAAU,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/isWeakSet.js"], "sourcesContent": ["import tagTester from './_tagTester.js';\n\nexport default tagTester('WeakSet');\n"], "names": [], "mappings": ";;;AAAA;;uCAEe,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/values.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Retrieve the values of an object's properties.\nexport default function values(obj) {\n  var _keys = keys(obj);\n  var length = _keys.length;\n  var values = Array(length);\n  for (var i = 0; i < length; i++) {\n    values[i] = obj[_keys[i]];\n  }\n  return values;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,OAAO,GAAG;IAChC,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;IACjB,IAAI,SAAS,MAAM,MAAM;IACzB,IAAI,SAAS,MAAM;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;IAC3B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/pairs.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Convert an object into a list of `[key, value]` pairs.\n// The opposite of `_.object` with one argument.\nexport default function pairs(obj) {\n  var _keys = keys(obj);\n  var length = _keys.length;\n  var pairs = Array(length);\n  for (var i = 0; i < length; i++) {\n    pairs[i] = [_keys[i], obj[_keys[i]]];\n  }\n  return pairs;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,MAAM,GAAG;IAC/B,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;IACjB,IAAI,SAAS,MAAM,MAAM;IACzB,IAAI,QAAQ,MAAM;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,KAAK,CAAC,EAAE,GAAG;YAAC,KAAK,CAAC,EAAE;YAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;SAAC;IACtC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/invert.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Invert the keys and values of an object. The values must be serializable.\nexport default function invert(obj) {\n  var result = {};\n  var _keys = keys(obj);\n  for (var i = 0, length = _keys.length; i < length; i++) {\n    result[obj[_keys[i]]] = _keys[i];\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,OAAO,GAAG;IAChC,IAAI,SAAS,CAAC;IACd,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,QAAQ,IAAK;QACtD,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE;IAClC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/functions.js"], "sourcesContent": ["import isFunction from './isFunction.js';\n\n// Return a sorted list of the function names available on the object.\nexport default function functions(obj) {\n  var names = [];\n  for (var key in obj) {\n    if (isFunction(obj[key])) names.push(key);\n  }\n  return names.sort();\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,UAAU,GAAG;IACnC,IAAI,QAAQ,EAAE;IACd,IAAK,IAAI,OAAO,IAAK;QACnB,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,GAAG,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC;IACvC;IACA,OAAO,MAAM,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createAssigner.js"], "sourcesContent": ["// An internal function for creating assigner functions.\nexport default function createAssigner(keysFunc, defaults) {\n  return function(obj) {\n    var length = arguments.length;\n    if (defaults) obj = Object(obj);\n    if (length < 2 || obj == null) return obj;\n    for (var index = 1; index < length; index++) {\n      var source = arguments[index],\n          keys = keysFunc(source),\n          l = keys.length;\n      for (var i = 0; i < l; i++) {\n        var key = keys[i];\n        if (!defaults || obj[key] === void 0) obj[key] = source[key];\n      }\n    }\n    return obj;\n  };\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACzC,SAAS,eAAe,QAAQ,EAAE,QAAQ;IACvD,OAAO,SAAS,GAAG;QACjB,IAAI,SAAS,UAAU,MAAM;QAC7B,IAAI,UAAU,MAAM,OAAO;QAC3B,IAAI,SAAS,KAAK,OAAO,MAAM,OAAO;QACtC,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;YAC3C,IAAI,SAAS,SAAS,CAAC,MAAM,EACzB,OAAO,SAAS,SAChB,IAAI,KAAK,MAAM;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;YAC9D;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/extend.js"], "sourcesContent": ["import createAssigner from './_createAssigner.js';\nimport allKeys from './allKeys.js';\n\n// Extend a given object with all the properties in passed-in object(s).\nexport default createAssigner(allKeys);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,wJAAA,CAAA,UAAc,AAAD,EAAE,gJAAA,CAAA,UAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/extendOwn.js"], "sourcesContent": ["import createAssigner from './_createAssigner.js';\nimport keys from './keys.js';\n\n// Assigns a given object with all the own properties in the passed-in\n// object(s).\n// (https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object/assign)\nexport default createAssigner(keys);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAKe,CAAA,GAAA,wJAAA,CAAA,UAAc,AAAD,EAAE,6IAAA,CAAA,UAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/defaults.js"], "sourcesContent": ["import createAssigner from './_createAssigner.js';\nimport allKeys from './allKeys.js';\n\n// Fill in a given object with default properties.\nexport default createAssigner(allKeys, true);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,wJAAA,CAAA,UAAc,AAAD,EAAE,gJAAA,CAAA,UAAO,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_baseCreate.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport { nativeCreate } from './_setup.js';\n\n// Create a naked function reference for surrogate-prototype-swapping.\nfunction ctor() {\n  return function(){};\n}\n\n// An internal function for creating a new object that inherits from another.\nexport default function baseCreate(prototype) {\n  if (!isObject(prototype)) return {};\n  if (nativeCreate) return nativeCreate(prototype);\n  var Ctor = ctor();\n  Ctor.prototype = prototype;\n  var result = new Ctor;\n  Ctor.prototype = null;\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,sEAAsE;AACtE,SAAS;IACP,OAAO,YAAW;AACpB;AAGe,SAAS,WAAW,SAAS;IAC1C,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,YAAY,OAAO,CAAC;IAClC,IAAI,+IAAA,CAAA,eAAY,EAAE,OAAO,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD,EAAE;IACtC,IAAI,OAAO;IACX,KAAK,SAAS,GAAG;IACjB,IAAI,SAAS,IAAI;IACjB,KAAK,SAAS,GAAG;IACjB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/create.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport extendOwn from './extendOwn.js';\n\n// Creates an object that inherits from the given prototype object.\n// If additional properties are provided then they will be added to the\n// created object.\nexport default function create(prototype, props) {\n  var result = baseCreate(prototype);\n  if (props) extendOwn(result, props);\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKe,SAAS,OAAO,SAAS,EAAE,KAAK;IAC7C,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE;IACxB,IAAI,OAAO,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ;IAC7B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/clone.js"], "sourcesContent": ["import isObject from './isObject.js';\nimport isArray from './isArray.js';\nimport extend from './extend.js';\n\n// Create a (shallow-cloned) duplicate of an object.\nexport default function clone(obj) {\n  if (!isObject(obj)) return obj;\n  return isArray(obj) ? obj.slice() : extend({}, obj);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,MAAM,GAAG;IAC/B,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,OAAO;IAC3B,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,OAAO,IAAI,KAAK,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,CAAC,GAAG;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/tap.js"], "sourcesContent": ["// Invokes `interceptor` with the `obj` and then returns `obj`.\n// The primary purpose of this method is to \"tap into\" a method chain, in\n// order to perform operations on intermediate results within the chain.\nexport default function tap(obj, interceptor) {\n  interceptor(obj);\n  return obj;\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,yEAAyE;AACzE,wEAAwE;;;;AACzD,SAAS,IAAI,GAAG,EAAE,WAAW;IAC1C,YAAY;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/toPath.js"], "sourcesContent": ["import _ from './underscore.js';\nimport isArray from './isArray.js';\n\n// Normalize a (deep) property `path` to array.\n// Like `_.iteratee`, this function can be customized.\nexport default function toPath(path) {\n  return isArray(path) ? path : [path];\n}\n_.toPath = toPath;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,OAAO,IAAI;IACjC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;QAAC;KAAK;AACtC;AACA,mJAAA,CAAA,UAAC,CAAC,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_toPath.js"], "sourcesContent": ["import _ from './underscore.js';\nimport './toPath.js';\n\n// Internal wrapper for `_.toPath` to enable minification.\n// Similar to `cb` for `_.iteratee`.\nexport default function toPath(path) {\n  return _.toPath(path);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,OAAO,IAAI;IACjC,OAAO,mJAAA,CAAA,UAAC,CAAC,MAAM,CAAC;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_deepGet.js"], "sourcesContent": ["// Internal function to obtain a nested property in `obj` along `path`.\nexport default function deepGet(obj, path) {\n  var length = path.length;\n  for (var i = 0; i < length; i++) {\n    if (obj == null) return void 0;\n    obj = obj[path[i]];\n  }\n  return length ? obj : void 0;\n}\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;AACxD,SAAS,QAAQ,GAAG,EAAE,IAAI;IACvC,IAAI,SAAS,KAAK,MAAM;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,IAAI,OAAO,MAAM,OAAO,KAAK;QAC7B,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;IACpB;IACA,OAAO,SAAS,MAAM,KAAK;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/get.js"], "sourcesContent": ["import toPath from './_toPath.js';\nimport deepGet from './_deepGet.js';\nimport isUndefined from './isUndefined.js';\n\n// Get the value of the (deep) property on `path` from `object`.\n// If any property in `path` does not exist or if the value is\n// `undefined`, return `defaultValue` instead.\n// The `path` is normalized through `_.toPath`.\nexport default function get(object, path, defaultValue) {\n  var value = deepGet(object, toPath(path));\n  return isUndefined(value) ? defaultValue : value;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMe,SAAS,IAAI,MAAM,EAAE,IAAI,EAAE,YAAY;IACpD,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAA,GAAA,gJAAA,CAAA,UAAM,AAAD,EAAE;IACnC,OAAO,CAAA,GAAA,oJAAA,CAAA,UAAW,AAAD,EAAE,SAAS,eAAe;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/has.js"], "sourcesContent": ["import _has from './_has.js';\nimport toPath from './_toPath.js';\n\n// Shortcut function for checking if an object has a given property directly on\n// itself (in other words, not on a prototype). Unlike the internal `has`\n// function, this public version can also traverse nested properties.\nexport default function has(obj, path) {\n  path = toPath(path);\n  var length = path.length;\n  for (var i = 0; i < length; i++) {\n    var key = path[i];\n    if (!_has(obj, key)) return false;\n    obj = obj[key];\n  }\n  return !!length;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKe,SAAS,IAAI,GAAG,EAAE,IAAI;IACnC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAM,AAAD,EAAE;IACd,IAAI,SAAS,KAAK,MAAM;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,MAAM,OAAO;QAC5B,MAAM,GAAG,CAAC,IAAI;IAChB;IACA,OAAO,CAAC,CAAC;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/identity.js"], "sourcesContent": ["// Keep the identity function around for default iteratees.\nexport default function identity(value) {\n  return value;\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;AAC5C,SAAS,SAAS,KAAK;IACpC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/matcher.js"], "sourcesContent": ["import extendOwn from './extendOwn.js';\nimport isMatch from './isMatch.js';\n\n// Returns a predicate for checking whether an object has a given set of\n// `key:value` pairs.\nexport default function matcher(attrs) {\n  attrs = extendOwn({}, attrs);\n  return function(obj) {\n    return isMatch(obj, attrs);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,QAAQ,KAAK;IACnC,QAAQ,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE,CAAC,GAAG;IACtB,OAAO,SAAS,GAAG;QACjB,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,KAAK;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/property.js"], "sourcesContent": ["import deepGet from './_deepGet.js';\nimport toPath from './_toPath.js';\n\n// Creates a function that, when passed an object, will traverse that object’s\n// properties down the given `path`, specified as an array of keys or indices.\nexport default function property(path) {\n  path = toPath(path);\n  return function(obj) {\n    return deepGet(obj, path);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,SAAS,IAAI;IACnC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAM,AAAD,EAAE;IACd,OAAO,SAAS,GAAG;QACjB,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,KAAK;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_optimizeCb.js"], "sourcesContent": ["// Internal function that returns an efficient (for current engines) version\n// of the passed-in callback, to be repeatedly applied in other Underscore\n// functions.\nexport default function optimizeCb(func, context, argCount) {\n  if (context === void 0) return func;\n  switch (argCount == null ? 3 : argCount) {\n    case 1: return function(value) {\n      return func.call(context, value);\n    };\n    // The 2-argument case is omitted because we’re not using it.\n    case 3: return function(value, index, collection) {\n      return func.call(context, value, index, collection);\n    };\n    case 4: return function(accumulator, value, index, collection) {\n      return func.call(context, accumulator, value, index, collection);\n    };\n  }\n  return function() {\n    return func.apply(context, arguments);\n  };\n}\n"], "names": [], "mappings": "AAAA,4EAA4E;AAC5E,0EAA0E;AAC1E,aAAa;;;;AACE,SAAS,WAAW,IAAI,EAAE,OAAO,EAAE,QAAQ;IACxD,IAAI,YAAY,KAAK,GAAG,OAAO;IAC/B,OAAQ,YAAY,OAAO,IAAI;QAC7B,KAAK;YAAG,OAAO,SAAS,KAAK;gBAC3B,OAAO,KAAK,IAAI,CAAC,SAAS;YAC5B;QACA,6DAA6D;QAC7D,KAAK;YAAG,OAAO,SAAS,KAAK,EAAE,KAAK,EAAE,UAAU;gBAC9C,OAAO,KAAK,IAAI,CAAC,SAAS,OAAO,OAAO;YAC1C;QACA,KAAK;YAAG,OAAO,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;gBAC3D,OAAO,KAAK,IAAI,CAAC,SAAS,aAAa,OAAO,OAAO;YACvD;IACF;IACA,OAAO;QACL,OAAO,KAAK,KAAK,CAAC,SAAS;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_baseIteratee.js"], "sourcesContent": ["import identity from './identity.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport isArray from './isArray.js';\nimport matcher from './matcher.js';\nimport property from './property.js';\nimport optimizeCb from './_optimizeCb.js';\n\n// An internal function to generate callbacks that can be applied to each\n// element in a collection, returning the desired result — either `_.identity`,\n// an arbitrary callback, a property matcher, or a property accessor.\nexport default function baseIteratee(value, context, argCount) {\n  if (value == null) return identity;\n  if (isFunction(value)) return optimizeCb(value, context, argCount);\n  if (isObject(value) && !isArray(value)) return matcher(value);\n  return property(value);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAKe,SAAS,aAAa,KAAK,EAAE,OAAO,EAAE,QAAQ;IAC3D,IAAI,SAAS,MAAM,OAAO,iJAAA,CAAA,UAAQ;IAClC,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,OAAO,SAAS;IACzD,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;IACvD,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/iteratee.js"], "sourcesContent": ["import _ from './underscore.js';\nimport baseIteratee from './_baseIteratee.js';\n\n// External wrapper for our callback generator. Users may customize\n// `_.iteratee` if they want additional predicate/iteratee shorthand styles.\n// This abstraction hides the internal-only `argCount` argument.\nexport default function iteratee(value, context) {\n  return baseIteratee(value, context, Infinity);\n}\n_.iteratee = iteratee;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKe,SAAS,SAAS,KAAK,EAAE,OAAO;IAC7C,OAAO,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,OAAO,SAAS;AACtC;AACA,mJAAA,CAAA,UAAC,CAAC,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_cb.js"], "sourcesContent": ["import _ from './underscore.js';\nimport baseIteratee from './_baseIteratee.js';\nimport iteratee from './iteratee.js';\n\n// The function we call internally to generate a callback. It invokes\n// `_.iteratee` if overridden, otherwise `baseIteratee`.\nexport default function cb(value, context, argCount) {\n  if (_.iteratee !== iteratee) return _.iteratee(value, context);\n  return baseIteratee(value, context, argCount);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIe,SAAS,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ;IACjD,IAAI,mJAAA,CAAA,UAAC,CAAC,QAAQ,KAAK,iJAAA,CAAA,UAAQ,EAAE,OAAO,mJAAA,CAAA,UAAC,CAAC,QAAQ,CAAC,OAAO;IACtD,OAAO,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,OAAO,SAAS;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/mapObject.js"], "sourcesContent": ["import cb from './_cb.js';\nimport keys from './keys.js';\n\n// Returns the results of applying the `iteratee` to each element of `obj`.\n// In contrast to `_.map` it returns an object.\nexport default function mapObject(obj, iteratee, context) {\n  iteratee = cb(iteratee, context);\n  var _keys = keys(obj),\n      length = _keys.length,\n      results = {};\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys[index];\n    results[currentKey] = iteratee(obj[currentKey], currentKey, obj);\n  }\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,UAAU,GAAG,EAAE,QAAQ,EAAE,OAAO;IACtD,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;IACxB,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MACb,SAAS,MAAM,MAAM,EACrB,UAAU,CAAC;IACf,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;QAC3C,IAAI,aAAa,KAAK,CAAC,MAAM;QAC7B,OAAO,CAAC,WAAW,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY;IAC9D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/noop.js"], "sourcesContent": ["// Predicate-generating function. Often useful outside of Underscore.\nexport default function noop(){}\n"], "names": [], "mappings": "AAAA,qEAAqE;;;;AACtD,SAAS,QAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/propertyOf.js"], "sourcesContent": ["import noop from './noop.js';\nimport get from './get.js';\n\n// Generates a function for a given object that returns a given property.\nexport default function propertyOf(obj) {\n  if (obj == null) return noop;\n  return function(path) {\n    return get(obj, path);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,WAAW,GAAG;IACpC,IAAI,OAAO,MAAM,OAAO,6IAAA,CAAA,UAAI;IAC5B,OAAO,SAAS,IAAI;QAClB,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,KAAK;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/times.js"], "sourcesContent": ["import optimizeCb from './_optimizeCb.js';\n\n// Run a function **n** times.\nexport default function times(n, iteratee, context) {\n  var accum = Array(Math.max(0, n));\n  iteratee = optimizeCb(iteratee, context, 1);\n  for (var i = 0; i < n; i++) accum[i] = iteratee(i);\n  return accum;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,MAAM,CAAC,EAAE,QAAQ,EAAE,OAAO;IAChD,IAAI,QAAQ,MAAM,KAAK,GAAG,CAAC,GAAG;IAC9B,WAAW,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,UAAU,SAAS;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK,KAAK,CAAC,EAAE,GAAG,SAAS;IAChD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/random.js"], "sourcesContent": ["// Return a random integer between `min` and `max` (inclusive).\nexport default function random(min, max) {\n  if (max == null) {\n    max = min;\n    min = 0;\n  }\n  return min + Math.floor(Math.random() * (max - min + 1));\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;AAChD,SAAS,OAAO,GAAG,EAAE,GAAG;IACrC,IAAI,OAAO,MAAM;QACf,MAAM;QACN,MAAM;IACR;IACA,OAAO,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/now.js"], "sourcesContent": ["// A (possibly faster) way to get the current timestamp as an integer.\nexport default Date.now || function() {\n  return new Date().getTime();\n};\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;uCACvD,KAAK,GAAG,IAAI;IACzB,OAAO,IAAI,OAAO,OAAO;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createEscaper.js"], "sourcesContent": ["import keys from './keys.js';\n\n// Internal helper to generate functions for escaping and unescaping strings\n// to/from HTML interpolation.\nexport default function createEscaper(map) {\n  var escaper = function(match) {\n    return map[match];\n  };\n  // Regexes for identifying a key that needs to be escaped.\n  var source = '(?:' + keys(map).join('|') + ')';\n  var testRegexp = RegExp(source);\n  var replaceRegexp = RegExp(source, 'g');\n  return function(string) {\n    string = string == null ? '' : '' + string;\n    return testRegexp.test(string) ? string.replace(replaceRegexp, escaper) : string;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,cAAc,GAAG;IACvC,IAAI,UAAU,SAAS,KAAK;QAC1B,OAAO,GAAG,CAAC,MAAM;IACnB;IACA,0DAA0D;IAC1D,IAAI,SAAS,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,IAAI,CAAC,OAAO;IAC3C,IAAI,aAAa,OAAO;IACxB,IAAI,gBAAgB,OAAO,QAAQ;IACnC,OAAO,SAAS,MAAM;QACpB,SAAS,UAAU,OAAO,KAAK,KAAK;QACpC,OAAO,WAAW,IAAI,CAAC,UAAU,OAAO,OAAO,CAAC,eAAe,WAAW;IAC5E;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_escapeMap.js"], "sourcesContent": ["// Internal list of HTML entities for escaping.\nexport default {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#x27;',\n  '`': '&#x60;'\n};\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;uCAChC;IACb,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/escape.js"], "sourcesContent": ["import createEscaper from './_createEscaper.js';\nimport escapeMap from './_escapeMap.js';\n\n// Function for escaping strings to HTML interpolation.\nexport default createEscaper(escapeMap);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE,mJAAA,CAAA,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_unescapeMap.js"], "sourcesContent": ["import invert from './invert.js';\nimport escapeMap from './_escapeMap.js';\n\n// Internal list of HTML entities for unescaping.\nexport default invert(escapeMap);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,mJAAA,CAAA,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/unescape.js"], "sourcesContent": ["import createEscaper from './_createEscaper.js';\nimport unescapeMap from './_unescapeMap.js';\n\n// Function for unescaping strings from HTML interpolation.\nexport default createEscaper(unescapeMap);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,uJAAA,CAAA,UAAa,AAAD,EAAE,qJAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/templateSettings.js"], "sourcesContent": ["import _ from './underscore.js';\n\n// By default, Underscore uses ERB-style template delimiters. Change the\n// following template settings to use alternative delimiters.\nexport default _.templateSettings = {\n  evaluate: /<%([\\s\\S]+?)%>/g,\n  interpolate: /<%=([\\s\\S]+?)%>/g,\n  escape: /<%-([\\s\\S]+?)%>/g\n};\n"], "names": [], "mappings": ";;;AAAA;;uCAIe,mJAAA,CAAA,UAAC,CAAC,gBAAgB,GAAG;IAClC,UAAU;IACV,aAAa;IACb,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1637, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/template.js"], "sourcesContent": ["import defaults from './defaults.js';\nimport _ from './underscore.js';\nimport './templateSettings.js';\n\n// When customizing `_.templateSettings`, if you don't want to define an\n// interpolation, evaluation or escaping regex, we need one that is\n// guaranteed not to match.\nvar noMatch = /(.)^/;\n\n// Certain characters need to be escaped so that they can be put into a\n// string literal.\nvar escapes = {\n  \"'\": \"'\",\n  '\\\\': '\\\\',\n  '\\r': 'r',\n  '\\n': 'n',\n  '\\u2028': 'u2028',\n  '\\u2029': 'u2029'\n};\n\nvar escapeRegExp = /\\\\|'|\\r|\\n|\\u2028|\\u2029/g;\n\nfunction escapeChar(match) {\n  return '\\\\' + escapes[match];\n}\n\n// In order to prevent third-party code injection through\n// `_.templateSettings.variable`, we test it against the following regular\n// expression. It is intentionally a bit more liberal than just matching valid\n// identifiers, but still prevents possible loopholes through defaults or\n// destructuring assignment.\nvar bareIdentifier = /^\\s*(\\w|\\$)+\\s*$/;\n\n// JavaScript micro-templating, similar to <PERSON>'s implementation.\n// Underscore templating handles arbitrary delimiters, preserves whitespace,\n// and correctly escapes quotes within interpolated code.\n// NB: `oldSettings` only exists for backwards compatibility.\nexport default function template(text, settings, oldSettings) {\n  if (!settings && oldSettings) settings = oldSettings;\n  settings = defaults({}, settings, _.templateSettings);\n\n  // Combine delimiters into one regular expression via alternation.\n  var matcher = RegExp([\n    (settings.escape || noMatch).source,\n    (settings.interpolate || noMatch).source,\n    (settings.evaluate || noMatch).source\n  ].join('|') + '|$', 'g');\n\n  // Compile the template source, escaping string literals appropriately.\n  var index = 0;\n  var source = \"__p+='\";\n  text.replace(matcher, function(match, escape, interpolate, evaluate, offset) {\n    source += text.slice(index, offset).replace(escapeRegExp, escapeChar);\n    index = offset + match.length;\n\n    if (escape) {\n      source += \"'+\\n((__t=(\" + escape + \"))==null?'':_.escape(__t))+\\n'\";\n    } else if (interpolate) {\n      source += \"'+\\n((__t=(\" + interpolate + \"))==null?'':__t)+\\n'\";\n    } else if (evaluate) {\n      source += \"';\\n\" + evaluate + \"\\n__p+='\";\n    }\n\n    // Adobe VMs need the match returned to produce the correct offset.\n    return match;\n  });\n  source += \"';\\n\";\n\n  var argument = settings.variable;\n  if (argument) {\n    // Insure against third-party code injection. (CVE-2021-23358)\n    if (!bareIdentifier.test(argument)) throw new Error(\n      'variable is not a bare identifier: ' + argument\n    );\n  } else {\n    // If a variable is not specified, place data values in local scope.\n    source = 'with(obj||{}){\\n' + source + '}\\n';\n    argument = 'obj';\n  }\n\n  source = \"var __t,__p='',__j=Array.prototype.join,\" +\n    \"print=function(){__p+=__j.call(arguments,'');};\\n\" +\n    source + 'return __p;\\n';\n\n  var render;\n  try {\n    render = new Function(argument, '_', source);\n  } catch (e) {\n    e.source = source;\n    throw e;\n  }\n\n  var template = function(data) {\n    return render.call(this, data, _);\n  };\n\n  // Provide the compiled source as a convenience for precompilation.\n  template.source = 'function(' + argument + '){\\n' + source + '}';\n\n  return template;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,mEAAmE;AACnE,2BAA2B;AAC3B,IAAI,UAAU;AAEd,uEAAuE;AACvE,kBAAkB;AAClB,IAAI,UAAU;IACZ,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,UAAU;AACZ;AAEA,IAAI,eAAe;AAEnB,SAAS,WAAW,KAAK;IACvB,OAAO,OAAO,OAAO,CAAC,MAAM;AAC9B;AAEA,yDAAyD;AACzD,0EAA0E;AAC1E,8EAA8E;AAC9E,yEAAyE;AACzE,4BAA4B;AAC5B,IAAI,iBAAiB;AAMN,SAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,WAAW;IAC1D,IAAI,CAAC,YAAY,aAAa,WAAW;IACzC,WAAW,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU,mJAAA,CAAA,UAAC,CAAC,gBAAgB;IAEpD,kEAAkE;IAClE,IAAI,UAAU,OAAO;QACnB,CAAC,SAAS,MAAM,IAAI,OAAO,EAAE,MAAM;QACnC,CAAC,SAAS,WAAW,IAAI,OAAO,EAAE,MAAM;QACxC,CAAC,SAAS,QAAQ,IAAI,OAAO,EAAE,MAAM;KACtC,CAAC,IAAI,CAAC,OAAO,MAAM;IAEpB,uEAAuE;IACvE,IAAI,QAAQ;IACZ,IAAI,SAAS;IACb,KAAK,OAAO,CAAC,SAAS,SAAS,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM;QACzE,UAAU,KAAK,KAAK,CAAC,OAAO,QAAQ,OAAO,CAAC,cAAc;QAC1D,QAAQ,SAAS,MAAM,MAAM;QAE7B,IAAI,QAAQ;YACV,UAAU,gBAAgB,SAAS;QACrC,OAAO,IAAI,aAAa;YACtB,UAAU,gBAAgB,cAAc;QAC1C,OAAO,IAAI,UAAU;YACnB,UAAU,SAAS,WAAW;QAChC;QAEA,mEAAmE;QACnE,OAAO;IACT;IACA,UAAU;IAEV,IAAI,WAAW,SAAS,QAAQ;IAChC,IAAI,UAAU;QACZ,8DAA8D;QAC9D,IAAI,CAAC,eAAe,IAAI,CAAC,WAAW,MAAM,IAAI,MAC5C,wCAAwC;IAE5C,OAAO;QACL,oEAAoE;QACpE,SAAS,qBAAqB,SAAS;QACvC,WAAW;IACb;IAEA,SAAS,6CACP,sDACA,SAAS;IAEX,IAAI;IACJ,IAAI;QACF,SAAS,IAAI,SAAS,UAAU,KAAK;IACvC,EAAE,OAAO,GAAG;QACV,EAAE,MAAM,GAAG;QACX,MAAM;IACR;IAEA,IAAI,WAAW,SAAS,IAAI;QAC1B,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,mJAAA,CAAA,UAAC;IAClC;IAEA,mEAAmE;IACnE,SAAS,MAAM,GAAG,cAAc,WAAW,SAAS,SAAS;IAE7D,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/result.js"], "sourcesContent": ["import isFunction from './isFunction.js';\nimport toPath from './_toPath.js';\n\n// Traverses the children of `obj` along `path`. If a child is a function, it\n// is invoked with its parent as context. Returns the value of the final\n// child, or `fallback` if any child is undefined.\nexport default function result(obj, path, fallback) {\n  path = toPath(path);\n  var length = path.length;\n  if (!length) {\n    return isFunction(fallback) ? fallback.call(obj) : fallback;\n  }\n  for (var i = 0; i < length; i++) {\n    var prop = obj == null ? void 0 : obj[path[i]];\n    if (prop === void 0) {\n      prop = fallback;\n      i = length; // Ensure we don't continue iterating.\n    }\n    obj = isFunction(prop) ? prop.call(obj) : prop;\n  }\n  return obj;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKe,SAAS,OAAO,GAAG,EAAE,IAAI,EAAE,QAAQ;IAChD,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAM,AAAD,EAAE;IACd,IAAI,SAAS,KAAK,MAAM;IACxB,IAAI,CAAC,QAAQ;QACX,OAAO,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,YAAY,SAAS,IAAI,CAAC,OAAO;IACrD;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,IAAI,OAAO,OAAO,OAAO,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO;YACP,IAAI,QAAQ,sCAAsC;QACpD;QACA,MAAM,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,KAAK,IAAI,CAAC,OAAO;IAC5C;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1755, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/uniqueId.js"], "sourcesContent": ["// Generate a unique integer id (unique within the entire client session).\n// Useful for temporary DOM ids.\nvar idCounter = 0;\nexport default function uniqueId(prefix) {\n  var id = ++idCounter + '';\n  return prefix ? prefix + id : id;\n}\n"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,gCAAgC;;;;AAChC,IAAI,YAAY;AACD,SAAS,SAAS,MAAM;IACrC,IAAI,KAAK,EAAE,YAAY;IACvB,OAAO,SAAS,SAAS,KAAK;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/chain.js"], "sourcesContent": ["import _ from './underscore.js';\n\n// Start chaining a wrapped Underscore object.\nexport default function chain(obj) {\n  var instance = _(obj);\n  instance._chain = true;\n  return instance;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,MAAM,GAAG;IAC/B,IAAI,WAAW,CAAA,GAAA,mJAAA,CAAA,UAAC,AAAD,EAAE;IACjB,SAAS,MAAM,GAAG;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1787, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_executeBound.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n\n// Internal function to execute `sourceFunc` bound to `context` with optional\n// `args`. Determines whether to execute a function as a constructor or as a\n// normal function.\nexport default function executeBound(sourceFunc, boundFunc, context, callingContext, args) {\n  if (!(callingContext instanceof boundFunc)) return sourceFunc.apply(context, args);\n  var self = baseCreate(sourceFunc.prototype);\n  var result = sourceFunc.apply(self, args);\n  if (isObject(result)) return result;\n  return self;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKe,SAAS,aAAa,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,IAAI;IACvF,IAAI,CAAC,CAAC,0BAA0B,SAAS,GAAG,OAAO,WAAW,KAAK,CAAC,SAAS;IAC7E,IAAI,OAAO,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,WAAW,SAAS;IAC1C,IAAI,SAAS,WAAW,KAAK,CAAC,MAAM;IACpC,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,SAAS,OAAO;IAC7B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/partial.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport executeBound from './_executeBound.js';\nimport _ from './underscore.js';\n\n// Partially apply a function by creating a version that has had some of its\n// arguments pre-filled, without changing its dynamic `this` context. `_` acts\n// as a placeholder by default, allowing any combination of arguments to be\n// pre-filled. Set `_.partial.placeholder` for a custom placeholder argument.\nvar partial = restArguments(function(func, boundArgs) {\n  var placeholder = partial.placeholder;\n  var bound = function() {\n    var position = 0, length = boundArgs.length;\n    var args = Array(length);\n    for (var i = 0; i < length; i++) {\n      args[i] = boundArgs[i] === placeholder ? arguments[position++] : boundArgs[i];\n    }\n    while (position < arguments.length) args.push(arguments[position++]);\n    return executeBound(func, bound, this, this, args);\n  };\n  return bound;\n});\n\npartial.placeholder = _;\nexport default partial;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,4EAA4E;AAC5E,8EAA8E;AAC9E,2EAA2E;AAC3E,6EAA6E;AAC7E,IAAI,UAAU,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,IAAI,EAAE,SAAS;IAClD,IAAI,cAAc,QAAQ,WAAW;IACrC,IAAI,QAAQ;QACV,IAAI,WAAW,GAAG,SAAS,UAAU,MAAM;QAC3C,IAAI,OAAO,MAAM;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,KAAK,cAAc,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE;QAC/E;QACA,MAAO,WAAW,UAAU,MAAM,CAAE,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW;QACnE,OAAO,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,MAAM,OAAO,IAAI,EAAE,IAAI,EAAE;IAC/C;IACA,OAAO;AACT;AAEA,QAAQ,WAAW,GAAG,mJAAA,CAAA,UAAC;uCACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1841, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/bind.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport isFunction from './isFunction.js';\nimport executeBound from './_executeBound.js';\n\n// Create a function bound to a given object (assigning `this`, and arguments,\n// optionally).\nexport default restArguments(function(func, context, args) {\n  if (!isFunction(func)) throw new TypeError('Bind must be called on a function');\n  var bound = restArguments(function(callArgs) {\n    return executeBound(func, bound, context, this, args.concat(callArgs));\n  });\n  return bound;\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,IAAI,EAAE,OAAO,EAAE,IAAI;IACvD,IAAI,CAAC,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,OAAO,MAAM,IAAI,UAAU;IAC3C,IAAI,QAAQ,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,QAAQ;QACzC,OAAO,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,MAAM,OAAO,SAAS,IAAI,EAAE,KAAK,MAAM,CAAC;IAC9D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1863, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_isArrayLike.js"], "sourcesContent": ["import createSizePropertyCheck from './_createSizePropertyCheck.js';\nimport getLength from './_getLength.js';\n\n// Internal helper for collection methods to determine whether a collection\n// should be iterated as an array or as an object.\n// Related: https://people.mozilla.org/~jorendorff/es6-draft.html#sec-tolength\n// Avoids a very nasty iOS 8 JIT bug on ARM-64. #2094\nexport default createSizePropertyCheck(getLength);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAMe,CAAA,GAAA,iKAAA,CAAA,UAAuB,AAAD,EAAE,mJAAA,CAAA,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_flatten.js"], "sourcesContent": ["import getLength from './_getLength.js';\nimport isArrayLike from './_isArrayLike.js';\nimport isArray from './isArray.js';\nimport isArguments from './isArguments.js';\n\n// Internal implementation of a recursive `flatten` function.\nexport default function flatten(input, depth, strict, output) {\n  output = output || [];\n  if (!depth && depth !== 0) {\n    depth = Infinity;\n  } else if (depth <= 0) {\n    return output.concat(input);\n  }\n  var idx = output.length;\n  for (var i = 0, length = getLength(input); i < length; i++) {\n    var value = input[i];\n    if (isArrayLike(value) && (isArray(value) || isArguments(value))) {\n      // Flatten current level of array or arguments object.\n      if (depth > 1) {\n        flatten(value, depth - 1, strict, output);\n        idx = output.length;\n      } else {\n        var j = 0, len = value.length;\n        while (j < len) output[idx++] = value[j++];\n      }\n    } else if (!strict) {\n      output[idx++] = value;\n    }\n  }\n  return output;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGe,SAAS,QAAQ,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;IAC1D,SAAS,UAAU,EAAE;IACrB,IAAI,CAAC,SAAS,UAAU,GAAG;QACzB,QAAQ;IACV,OAAO,IAAI,SAAS,GAAG;QACrB,OAAO,OAAO,MAAM,CAAC;IACvB;IACA,IAAI,MAAM,OAAO,MAAM;IACvB,IAAK,IAAI,IAAI,GAAG,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,IAAI,QAAQ,IAAK;QAC1D,IAAI,QAAQ,KAAK,CAAC,EAAE;QACpB,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,CAAA,GAAA,oJAAA,CAAA,UAAW,AAAD,EAAE,MAAM,GAAG;YAChE,sDAAsD;YACtD,IAAI,QAAQ,GAAG;gBACb,QAAQ,OAAO,QAAQ,GAAG,QAAQ;gBAClC,MAAM,OAAO,MAAM;YACrB,OAAO;gBACL,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM;gBAC7B,MAAO,IAAI,IAAK,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI;YAC5C;QACF,OAAO,IAAI,CAAC,QAAQ;YAClB,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/bindAll.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport flatten from './_flatten.js';\nimport bind from './bind.js';\n\n// Bind a number of an object's methods to that object. Remaining arguments\n// are the method names to be bound. Useful for ensuring that all callbacks\n// defined on an object belong to it.\nexport default restArguments(function(obj, keys) {\n  keys = flatten(keys, false, false);\n  var index = keys.length;\n  if (index < 1) throw new Error('bindAll must be passed function names');\n  while (index--) {\n    var key = keys[index];\n    obj[key] = bind(obj[key], obj);\n  }\n  return obj;\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAKe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,GAAG,EAAE,IAAI;IAC7C,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO;IAC5B,IAAI,QAAQ,KAAK,MAAM;IACvB,IAAI,QAAQ,GAAG,MAAM,IAAI,MAAM;IAC/B,MAAO,QAAS;QACd,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,GAAG,CAAC,IAAI,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,GAAG,CAAC,IAAI,EAAE;IAC5B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/memoize.js"], "sourcesContent": ["import has from './_has.js';\n\n// Memoize an expensive function by storing its results.\nexport default function memoize(func, hasher) {\n  var memoize = function(key) {\n    var cache = memoize.cache;\n    var address = '' + (hasher ? hasher.apply(this, arguments) : key);\n    if (!has(cache, address)) cache[address] = func.apply(this, arguments);\n    return cache[address];\n  };\n  memoize.cache = {};\n  return memoize;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,QAAQ,IAAI,EAAE,MAAM;IAC1C,IAAI,UAAU,SAAS,GAAG;QACxB,IAAI,QAAQ,QAAQ,KAAK;QACzB,IAAI,UAAU,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,IAAI,EAAE,aAAa,GAAG;QAChE,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI,EAAE;QAC5D,OAAO,KAAK,CAAC,QAAQ;IACvB;IACA,QAAQ,KAAK,GAAG,CAAC;IACjB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/delay.js"], "sourcesContent": ["import restArguments from './restArguments.js';\n\n// Delays a function for the given number of milliseconds, and then calls\n// it with the arguments supplied.\nexport default restArguments(function(func, wait, args) {\n  return setTimeout(function() {\n    return func.apply(null, args);\n  }, wait);\n});\n"], "names": [], "mappings": ";;;AAAA;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI;IACpD,OAAO,WAAW;QAChB,OAAO,KAAK,KAAK,CAAC,MAAM;IAC1B,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/defer.js"], "sourcesContent": ["import partial from './partial.js';\nimport delay from './delay.js';\nimport _ from './underscore.js';\n\n// Defers a function, scheduling it to run after the current call stack has\n// cleared.\nexport default partial(delay, _, 1);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAIe,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,8IAAA,CAAA,UAAK,EAAE,mJAAA,CAAA,UAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/throttle.js"], "sourcesContent": ["import now from './now.js';\n\n// Returns a function, that, when invoked, will only be triggered at most once\n// during a given window of time. Normally, the throttled function will run\n// as much as it can, without ever going more than once per `wait` duration;\n// but if you'd like to disable the execution on the leading edge, pass\n// `{leading: false}`. To disable execution on the trailing edge, ditto.\nexport default function throttle(func, wait, options) {\n  var timeout, context, args, result;\n  var previous = 0;\n  if (!options) options = {};\n\n  var later = function() {\n    previous = options.leading === false ? 0 : now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n\n  var throttled = function() {\n    var _now = now();\n    if (!previous && options.leading === false) previous = _now;\n    var remaining = wait - (_now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = _now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n\n  throttled.cancel = function() {\n    clearTimeout(timeout);\n    previous = 0;\n    timeout = context = args = null;\n  };\n\n  return throttled;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAOe,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO;IAClD,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,WAAW;IACf,IAAI,CAAC,SAAS,UAAU,CAAC;IAEzB,IAAI,QAAQ;QACV,WAAW,QAAQ,OAAO,KAAK,QAAQ,IAAI,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD;QAC7C,UAAU;QACV,SAAS,KAAK,KAAK,CAAC,SAAS;QAC7B,IAAI,CAAC,SAAS,UAAU,OAAO;IACjC;IAEA,IAAI,YAAY;QACd,IAAI,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD;QACb,IAAI,CAAC,YAAY,QAAQ,OAAO,KAAK,OAAO,WAAW;QACvD,IAAI,YAAY,OAAO,CAAC,OAAO,QAAQ;QACvC,UAAU,IAAI;QACd,OAAO;QACP,IAAI,aAAa,KAAK,YAAY,MAAM;YACtC,IAAI,SAAS;gBACX,aAAa;gBACb,UAAU;YACZ;YACA,WAAW;YACX,SAAS,KAAK,KAAK,CAAC,SAAS;YAC7B,IAAI,CAAC,SAAS,UAAU,OAAO;QACjC,OAAO,IAAI,CAAC,WAAW,QAAQ,QAAQ,KAAK,OAAO;YACjD,UAAU,WAAW,OAAO;QAC9B;QACA,OAAO;IACT;IAEA,UAAU,MAAM,GAAG;QACjB,aAAa;QACb,WAAW;QACX,UAAU,UAAU,OAAO;IAC7B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/debounce.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport now from './now.js';\n\n// When a sequence of calls of the returned function ends, the argument\n// function is triggered. The end of a sequence is defined by the `wait`\n// parameter. If `immediate` is passed, the argument function will be\n// triggered at the beginning of the sequence instead of at the end.\nexport default function debounce(func, wait, immediate) {\n  var timeout, previous, args, result, context;\n\n  var later = function() {\n    var passed = now() - previous;\n    if (wait > passed) {\n      timeout = setTimeout(later, wait - passed);\n    } else {\n      timeout = null;\n      if (!immediate) result = func.apply(context, args);\n      // This check is needed because `func` can recursively invoke `debounced`.\n      if (!timeout) args = context = null;\n    }\n  };\n\n  var debounced = restArguments(function(_args) {\n    context = this;\n    args = _args;\n    previous = now();\n    if (!timeout) {\n      timeout = setTimeout(later, wait);\n      if (immediate) result = func.apply(context, args);\n    }\n    return result;\n  });\n\n  debounced.cancel = function() {\n    clearTimeout(timeout);\n    timeout = args = context = null;\n  };\n\n  return debounced;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMe,SAAS,SAAS,IAAI,EAAE,IAAI,EAAE,SAAS;IACpD,IAAI,SAAS,UAAU,MAAM,QAAQ;IAErC,IAAI,QAAQ;QACV,IAAI,SAAS,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,MAAM;QACrB,IAAI,OAAO,QAAQ;YACjB,UAAU,WAAW,OAAO,OAAO;QACrC,OAAO;YACL,UAAU;YACV,IAAI,CAAC,WAAW,SAAS,KAAK,KAAK,CAAC,SAAS;YAC7C,0EAA0E;YAC1E,IAAI,CAAC,SAAS,OAAO,UAAU;QACjC;IACF;IAEA,IAAI,YAAY,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,KAAK;QAC1C,UAAU,IAAI;QACd,OAAO;QACP,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD;QACb,IAAI,CAAC,SAAS;YACZ,UAAU,WAAW,OAAO;YAC5B,IAAI,WAAW,SAAS,KAAK,KAAK,CAAC,SAAS;QAC9C;QACA,OAAO;IACT;IAEA,UAAU,MAAM,GAAG;QACjB,aAAa;QACb,UAAU,OAAO,UAAU;IAC7B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/wrap.js"], "sourcesContent": ["import partial from './partial.js';\n\n// Returns the first function passed as an argument to the second,\n// allowing you to adjust arguments, run code before and after, and\n// conditionally execute the original function.\nexport default function wrap(func, wrapper) {\n  return partial(wrapper, func);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,KAAK,IAAI,EAAE,OAAO;IACxC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/negate.js"], "sourcesContent": ["// Returns a negated version of the passed-in predicate.\nexport default function negate(predicate) {\n  return function() {\n    return !predicate.apply(this, arguments);\n  };\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACzC,SAAS,OAAO,SAAS;IACtC,OAAO;QACL,OAAO,CAAC,UAAU,KAAK,CAAC,IAAI,EAAE;IAChC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/compose.js"], "sourcesContent": ["// Returns a function that is the composition of a list of functions, each\n// consuming the return value of the function that follows.\nexport default function compose() {\n  var args = arguments;\n  var start = args.length - 1;\n  return function() {\n    var i = start;\n    var result = args[start].apply(this, arguments);\n    while (i--) result = args[i].call(this, result);\n    return result;\n  };\n}\n"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,2DAA2D;;;;AAC5C,SAAS;IACtB,IAAI,OAAO;IACX,IAAI,QAAQ,KAAK,MAAM,GAAG;IAC1B,OAAO;QACL,IAAI,IAAI;QACR,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;QACrC,MAAO,IAAK,SAAS,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;QACxC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/after.js"], "sourcesContent": ["// Returns a function that will only be executed on and after the Nth call.\nexport default function after(times, func) {\n  return function() {\n    if (--times < 1) {\n      return func.apply(this, arguments);\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,2EAA2E;;;;AAC5D,SAAS,MAAM,KAAK,EAAE,IAAI;IACvC,OAAO;QACL,IAAI,EAAE,QAAQ,GAAG;YACf,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;QAC1B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/before.js"], "sourcesContent": ["// Returns a function that will only be executed up to (but not including) the\n// Nth call.\nexport default function before(times, func) {\n  var memo;\n  return function() {\n    if (--times > 0) {\n      memo = func.apply(this, arguments);\n    }\n    if (times <= 1) func = null;\n    return memo;\n  };\n}\n"], "names": [], "mappings": "AAAA,8EAA8E;AAC9E,YAAY;;;;AACG,SAAS,OAAO,KAAK,EAAE,IAAI;IACxC,IAAI;IACJ,OAAO;QACL,IAAI,EAAE,QAAQ,GAAG;YACf,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;QAC1B;QACA,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/once.js"], "sourcesContent": ["import partial from './partial.js';\nimport before from './before.js';\n\n// Returns a function that will be executed at most one time, no matter how\n// often you call it. Useful for lazy initialization.\nexport default partial(before, 2);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,+IAAA,CAAA,UAAM,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/findKey.js"], "sourcesContent": ["import cb from './_cb.js';\nimport keys from './keys.js';\n\n// Returns the first key on an object that passes a truth test.\nexport default function findKey(obj, predicate, context) {\n  predicate = cb(predicate, context);\n  var _keys = keys(obj), key;\n  for (var i = 0, length = _keys.length; i < length; i++) {\n    key = _keys[i];\n    if (predicate(obj[key], key, obj)) return key;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,QAAQ,GAAG,EAAE,SAAS,EAAE,OAAO;IACrD,YAAY,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,WAAW;IAC1B,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MAAM;IACvB,IAAK,IAAI,IAAI,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,QAAQ,IAAK;QACtD,MAAM,KAAK,CAAC,EAAE;QACd,IAAI,UAAU,GAAG,CAAC,IAAI,EAAE,KAAK,MAAM,OAAO;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createPredicateIndexFinder.js"], "sourcesContent": ["import cb from './_cb.js';\nimport getLength from './_getLength.js';\n\n// Internal function to generate `_.findIndex` and `_.findLastIndex`.\nexport default function createPredicateIndexFinder(dir) {\n  return function(array, predicate, context) {\n    predicate = cb(predicate, context);\n    var length = getLength(array);\n    var index = dir > 0 ? 0 : length - 1;\n    for (; index >= 0 && index < length; index += dir) {\n      if (predicate(array[index], index, array)) return index;\n    }\n    return -1;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,2BAA2B,GAAG;IACpD,OAAO,SAAS,KAAK,EAAE,SAAS,EAAE,OAAO;QACvC,YAAY,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,WAAW;QAC1B,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;QACvB,IAAI,QAAQ,MAAM,IAAI,IAAI,SAAS;QACnC,MAAO,SAAS,KAAK,QAAQ,QAAQ,SAAS,IAAK;YACjD,IAAI,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ,OAAO;QACpD;QACA,OAAO,CAAC;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/findIndex.js"], "sourcesContent": ["import createPredicateIndexFinder from './_createPredicateIndexFinder.js';\n\n// Returns the first index on an array-like that passes a truth test.\nexport default createPredicateIndexFinder(1);\n"], "names": [], "mappings": ";;;AAAA;;uCAGe,CAAA,GAAA,oKAAA,CAAA,UAA0B,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/findLastIndex.js"], "sourcesContent": ["import createPredicateIndexFinder from './_createPredicateIndexFinder.js';\n\n// Returns the last index on an array-like that passes a truth test.\nexport default createPredicateIndexFinder(-1);\n"], "names": [], "mappings": ";;;AAAA;;uCAGe,CAAA,GAAA,oKAAA,CAAA,UAA0B,AAAD,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/sortedIndex.js"], "sourcesContent": ["import cb from './_cb.js';\nimport getLength from './_getLength.js';\n\n// Use a comparator function to figure out the smallest index at which\n// an object should be inserted so as to maintain order. Uses binary search.\nexport default function sortedIndex(array, obj, iteratee, context) {\n  iteratee = cb(iteratee, context, 1);\n  var value = iteratee(obj);\n  var low = 0, high = getLength(array);\n  while (low < high) {\n    var mid = Math.floor((low + high) / 2);\n    if (iteratee(array[mid]) < value) low = mid + 1; else high = mid;\n  }\n  return low;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO;IAC/D,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU,SAAS;IACjC,IAAI,QAAQ,SAAS;IACrB,IAAI,MAAM,GAAG,OAAO,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;IAC9B,MAAO,MAAM,KAAM;QACjB,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,MAAM,IAAI,IAAI;QACpC,IAAI,SAAS,KAAK,CAAC,IAAI,IAAI,OAAO,MAAM,MAAM;aAAQ,OAAO;IAC/D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createIndexFinder.js"], "sourcesContent": ["import getLength from './_getLength.js';\nimport { slice } from './_setup.js';\nimport isNaN from './isNaN.js';\n\n// Internal function to generate the `_.indexOf` and `_.lastIndexOf` functions.\nexport default function createIndexFinder(dir, predicateFind, sortedIndex) {\n  return function(array, item, idx) {\n    var i = 0, length = getLength(array);\n    if (typeof idx == 'number') {\n      if (dir > 0) {\n        i = idx >= 0 ? idx : Math.max(idx + length, i);\n      } else {\n        length = idx >= 0 ? Math.min(idx + 1, length) : idx + length + 1;\n      }\n    } else if (sortedIndex && idx && length) {\n      idx = sortedIndex(array, item);\n      return array[idx] === item ? idx : -1;\n    }\n    if (item !== item) {\n      idx = predicateFind(slice.call(array, i, length), isNaN);\n      return idx >= 0 ? idx + i : -1;\n    }\n    for (idx = dir > 0 ? i : length - 1; idx >= 0 && idx < length; idx += dir) {\n      if (array[idx] === item) return idx;\n    }\n    return -1;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,kBAAkB,GAAG,EAAE,aAAa,EAAE,WAAW;IACvE,OAAO,SAAS,KAAK,EAAE,IAAI,EAAE,GAAG;QAC9B,IAAI,IAAI,GAAG,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;QAC9B,IAAI,OAAO,OAAO,UAAU;YAC1B,IAAI,MAAM,GAAG;gBACX,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ;YAC9C,OAAO;gBACL,SAAS,OAAO,IAAI,KAAK,GAAG,CAAC,MAAM,GAAG,UAAU,MAAM,SAAS;YACjE;QACF,OAAO,IAAI,eAAe,OAAO,QAAQ;YACvC,MAAM,YAAY,OAAO;YACzB,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,MAAM,CAAC;QACtC;QACA,IAAI,SAAS,MAAM;YACjB,MAAM,cAAc,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,8IAAA,CAAA,UAAK;YACvD,OAAO,OAAO,IAAI,MAAM,IAAI,CAAC;QAC/B;QACA,IAAK,MAAM,MAAM,IAAI,IAAI,SAAS,GAAG,OAAO,KAAK,MAAM,QAAQ,OAAO,IAAK;YACzE,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,OAAO;QAClC;QACA,OAAO,CAAC;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/indexOf.js"], "sourcesContent": ["import sortedIndex from './sortedIndex.js';\nimport findIndex from './findIndex.js';\nimport createIndexFinder from './_createIndexFinder.js';\n\n// Return the position of the first occurrence of an item in an array,\n// or -1 if the item is not included in the array.\n// If the array is large and already in sort order, pass `true`\n// for **isSorted** to use binary search.\nexport default createIndexFinder(1, findIndex, sortedIndex);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAMe,CAAA,GAAA,2JAAA,CAAA,UAAiB,AAAD,EAAE,GAAG,kJAAA,CAAA,UAAS,EAAE,oJAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/lastIndexOf.js"], "sourcesContent": ["import findLastIndex from './findLastIndex.js';\nimport createIndexFinder from './_createIndexFinder.js';\n\n// Return the position of the last occurrence of an item in an array,\n// or -1 if the item is not included in the array.\nexport default createIndexFinder(-1, findLastIndex);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,CAAA,GAAA,2JAAA,CAAA,UAAiB,AAAD,EAAE,CAAC,GAAG,sJAAA,CAAA,UAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2349, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/find.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport findIndex from './findIndex.js';\nimport findKey from './findKey.js';\n\n// Return the first value which passes a truth test.\nexport default function find(obj, predicate, context) {\n  var keyFinder = isArrayLike(obj) ? findIndex : findKey;\n  var key = keyFinder(obj, predicate, context);\n  if (key !== void 0 && key !== -1) return obj[key];\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,KAAK,GAAG,EAAE,SAAS,EAAE,OAAO;IAClD,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,OAAO,kJAAA,CAAA,UAAS,GAAG,gJAAA,CAAA,UAAO;IACtD,IAAI,MAAM,UAAU,KAAK,WAAW;IACpC,IAAI,QAAQ,KAAK,KAAK,QAAQ,CAAC,GAAG,OAAO,GAAG,CAAC,IAAI;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/findWhere.js"], "sourcesContent": ["import find from './find.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.find`: getting the first\n// object containing specific `key:value` pairs.\nexport default function findWhere(obj, attrs) {\n  return find(obj, matcher(attrs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,UAAU,GAAG,EAAE,KAAK;IAC1C,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/each.js"], "sourcesContent": ["import optimizeCb from './_optimizeCb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// The cornerstone for collection functions, an `each`\n// implementation, aka `forEach`.\n// Handles raw objects in addition to array-likes. Treats all\n// sparse array-likes as if they were dense.\nexport default function each(obj, iteratee, context) {\n  iteratee = optimizeCb(iteratee, context);\n  var i, length;\n  if (isArrayLike(obj)) {\n    for (i = 0, length = obj.length; i < length; i++) {\n      iteratee(obj[i], i, obj);\n    }\n  } else {\n    var _keys = keys(obj);\n    for (i = 0, length = _keys.length; i < length; i++) {\n      iteratee(obj[_keys[i]], _keys[i], obj);\n    }\n  }\n  return obj;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMe,SAAS,KAAK,GAAG,EAAE,QAAQ,EAAE,OAAO;IACjD,WAAW,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,UAAU;IAChC,IAAI,GAAG;IACP,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QACpB,IAAK,IAAI,GAAG,SAAS,IAAI,MAAM,EAAE,IAAI,QAAQ,IAAK;YAChD,SAAS,GAAG,CAAC,EAAE,EAAE,GAAG;QACtB;IACF,OAAO;QACL,IAAI,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;QACjB,IAAK,IAAI,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,QAAQ,IAAK;YAClD,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;QACpC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/map.js"], "sourcesContent": ["import cb from './_cb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Return the results of applying the iteratee to each element.\nexport default function map(obj, iteratee, context) {\n  iteratee = cb(iteratee, context);\n  var _keys = !isArrayLike(obj) && keys(obj),\n      length = (_keys || obj).length,\n      results = Array(length);\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys ? _keys[index] : index;\n    results[index] = iteratee(obj[currentKey], currentKey, obj);\n  }\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,IAAI,GAAG,EAAE,QAAQ,EAAE,OAAO;IAChD,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;IACxB,IAAI,QAAQ,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MAClC,SAAS,CAAC,SAAS,GAAG,EAAE,MAAM,EAC9B,UAAU,MAAM;IACpB,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;QAC3C,IAAI,aAAa,QAAQ,KAAK,CAAC,MAAM,GAAG;QACxC,OAAO,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY;IACzD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_createReduce.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\nimport optimizeCb from './_optimizeCb.js';\n\n// Internal helper to create a reducing function, iterating left or right.\nexport default function createReduce(dir) {\n  // Wrap code that reassigns argument variables in a separate function than\n  // the one that accesses `arguments.length` to avoid a perf hit. (#1991)\n  var reducer = function(obj, iteratee, memo, initial) {\n    var _keys = !isArrayLike(obj) && keys(obj),\n        length = (_keys || obj).length,\n        index = dir > 0 ? 0 : length - 1;\n    if (!initial) {\n      memo = obj[_keys ? _keys[index] : index];\n      index += dir;\n    }\n    for (; index >= 0 && index < length; index += dir) {\n      var currentKey = _keys ? _keys[index] : index;\n      memo = iteratee(memo, obj[currentKey], currentKey, obj);\n    }\n    return memo;\n  };\n\n  return function(obj, iteratee, memo, context) {\n    var initial = arguments.length >= 3;\n    return reducer(obj, optimizeCb(iteratee, context, 4), memo, initial);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,aAAa,GAAG;IACtC,0EAA0E;IAC1E,wEAAwE;IACxE,IAAI,UAAU,SAAS,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;QACjD,IAAI,QAAQ,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MAClC,SAAS,CAAC,SAAS,GAAG,EAAE,MAAM,EAC9B,QAAQ,MAAM,IAAI,IAAI,SAAS;QACnC,IAAI,CAAC,SAAS;YACZ,OAAO,GAAG,CAAC,QAAQ,KAAK,CAAC,MAAM,GAAG,MAAM;YACxC,SAAS;QACX;QACA,MAAO,SAAS,KAAK,QAAQ,QAAQ,SAAS,IAAK;YACjD,IAAI,aAAa,QAAQ,KAAK,CAAC,MAAM,GAAG;YACxC,OAAO,SAAS,MAAM,GAAG,CAAC,WAAW,EAAE,YAAY;QACrD;QACA,OAAO;IACT;IAEA,OAAO,SAAS,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;QAC1C,IAAI,UAAU,UAAU,MAAM,IAAI;QAClC,OAAO,QAAQ,KAAK,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,UAAU,SAAS,IAAI,MAAM;IAC9D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2474, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/reduce.js"], "sourcesContent": ["import createReduce from './_createReduce.js';\n\n// **Reduce** builds up a single result from a list of values, aka `inject`,\n// or `foldl`.\nexport default createReduce(1);\n"], "names": [], "mappings": ";;;AAAA;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/reduceRight.js"], "sourcesContent": ["import createReduce from './_createReduce.js';\n\n// The right-associative version of reduce, also known as `foldr`.\nexport default createReduce(-1);\n"], "names": [], "mappings": ";;;AAAA;;uCAGe,CAAA,GAAA,sJAAA,CAAA,UAAY,AAAD,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/filter.js"], "sourcesContent": ["import cb from './_cb.js';\nimport each from './each.js';\n\n// Return all the elements that pass a truth test.\nexport default function filter(obj, predicate, context) {\n  var results = [];\n  predicate = cb(predicate, context);\n  each(obj, function(value, index, list) {\n    if (predicate(value, index, list)) results.push(value);\n  });\n  return results;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,OAAO,GAAG,EAAE,SAAS,EAAE,OAAO;IACpD,IAAI,UAAU,EAAE;IAChB,YAAY,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,WAAW;IAC1B,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,EAAE,IAAI;QACnC,IAAI,UAAU,OAAO,OAAO,OAAO,QAAQ,IAAI,CAAC;IAClD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/reject.js"], "sourcesContent": ["import filter from './filter.js';\nimport negate from './negate.js';\nimport cb from './_cb.js';\n\n// Return all the elements for which a truth test fails.\nexport default function reject(obj, predicate, context) {\n  return filter(obj, negate(cb(predicate)), context);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,OAAO,GAAG,EAAE,SAAS,EAAE,OAAO;IACpD,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,aAAa;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/every.js"], "sourcesContent": ["import cb from './_cb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Determine whether all of the elements pass a truth test.\nexport default function every(obj, predicate, context) {\n  predicate = cb(predicate, context);\n  var _keys = !isArrayLike(obj) && keys(obj),\n      length = (_keys || obj).length;\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys ? _keys[index] : index;\n    if (!predicate(obj[currentKey], currentKey, obj)) return false;\n  }\n  return true;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,MAAM,GAAG,EAAE,SAAS,EAAE,OAAO;IACnD,YAAY,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,WAAW;IAC1B,IAAI,QAAQ,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MAClC,SAAS,CAAC,SAAS,GAAG,EAAE,MAAM;IAClC,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;QAC3C,IAAI,aAAa,QAAQ,KAAK,CAAC,MAAM,GAAG;QACxC,IAAI,CAAC,UAAU,GAAG,CAAC,WAAW,EAAE,YAAY,MAAM,OAAO;IAC3D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2561, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/some.js"], "sourcesContent": ["import cb from './_cb.js';\nimport isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Determine if at least one element in the object passes a truth test.\nexport default function some(obj, predicate, context) {\n  predicate = cb(predicate, context);\n  var _keys = !isArrayLike(obj) && keys(obj),\n      length = (_keys || obj).length;\n  for (var index = 0; index < length; index++) {\n    var currentKey = _keys ? _keys[index] : index;\n    if (predicate(obj[currentKey], currentKey, obj)) return true;\n  }\n  return false;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,KAAK,GAAG,EAAE,SAAS,EAAE,OAAO;IAClD,YAAY,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,WAAW;IAC1B,IAAI,QAAQ,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,MAClC,SAAS,CAAC,SAAS,GAAG,EAAE,MAAM;IAClC,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;QAC3C,IAAI,aAAa,QAAQ,KAAK,CAAC,MAAM,GAAG;QACxC,IAAI,UAAU,GAAG,CAAC,WAAW,EAAE,YAAY,MAAM,OAAO;IAC1D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/contains.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport indexOf from './indexOf.js';\n\n// Determine if the array or object contains a given item (using `===`).\nexport default function contains(obj, item, fromIndex, guard) {\n  if (!isArrayLike(obj)) obj = values(obj);\n  if (typeof fromIndex != 'number' || guard) fromIndex = 0;\n  return indexOf(obj, item, fromIndex) >= 0;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,SAAS,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK;IAC1D,IAAI,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,MAAM,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;IACpC,IAAI,OAAO,aAAa,YAAY,OAAO,YAAY;IACvD,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,KAAK,MAAM,cAAc;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/invoke.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport isFunction from './isFunction.js';\nimport map from './map.js';\nimport deepGet from './_deepGet.js';\nimport toPath from './_toPath.js';\n\n// Invoke a method (with arguments) on every item in a collection.\nexport default restArguments(function(obj, path, args) {\n  var contextPath, func;\n  if (isFunction(path)) {\n    func = path;\n  } else {\n    path = toPath(path);\n    contextPath = path.slice(0, -1);\n    path = path[path.length - 1];\n  }\n  return map(obj, function(context) {\n    var method = func;\n    if (!method) {\n      if (contextPath && contextPath.length) {\n        context = deepGet(context, contextPath);\n      }\n      if (context == null) return void 0;\n      method = context[path];\n    }\n    return method == null ? method : method.apply(context, args);\n  });\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;uCAGe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,GAAG,EAAE,IAAI,EAAE,IAAI;IACnD,IAAI,aAAa;IACjB,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,OAAO;QACpB,OAAO;IACT,OAAO;QACL,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAM,AAAD,EAAE;QACd,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC;QAC7B,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAC9B;IACA,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,SAAS,OAAO;QAC9B,IAAI,SAAS;QACb,IAAI,CAAC,QAAQ;YACX,IAAI,eAAe,YAAY,MAAM,EAAE;gBACrC,UAAU,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;YAC7B;YACA,IAAI,WAAW,MAAM,OAAO,KAAK;YACjC,SAAS,OAAO,CAAC,KAAK;QACxB;QACA,OAAO,UAAU,OAAO,SAAS,OAAO,KAAK,CAAC,SAAS;IACzD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/pluck.js"], "sourcesContent": ["import map from './map.js';\nimport property from './property.js';\n\n// Convenience version of a common use case of `_.map`: fetching a property.\nexport default function pluck(obj, key) {\n  return map(obj, property(key));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,MAAM,GAAG,EAAE,GAAG;IACpC,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/where.js"], "sourcesContent": ["import filter from './filter.js';\nimport matcher from './matcher.js';\n\n// Convenience version of a common use case of `_.filter`: selecting only\n// objects containing specific `key:value` pairs.\nexport default function where(obj, attrs) {\n  return filter(obj, matcher(attrs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,MAAM,GAAG,EAAE,KAAK;IACtC,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,KAAK,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/max.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport cb from './_cb.js';\nimport each from './each.js';\n\n// Return the maximum element (or element-based computation).\nexport default function max(obj, iteratee, context) {\n  var result = -Infinity, lastComputed = -Infinity,\n      value, computed;\n  if (iteratee == null || (typeof iteratee == 'number' && typeof obj[0] != 'object' && obj != null)) {\n    obj = isArrayLike(obj) ? obj : values(obj);\n    for (var i = 0, length = obj.length; i < length; i++) {\n      value = obj[i];\n      if (value != null && value > result) {\n        result = value;\n      }\n    }\n  } else {\n    iteratee = cb(iteratee, context);\n    each(obj, function(v, index, list) {\n      computed = iteratee(v, index, list);\n      if (computed > lastComputed || (computed === -Infinity && result === -Infinity)) {\n        result = v;\n        lastComputed = computed;\n      }\n    });\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGe,SAAS,IAAI,GAAG,EAAE,QAAQ,EAAE,OAAO;IAChD,IAAI,SAAS,CAAC,UAAU,eAAe,CAAC,UACpC,OAAO;IACX,IAAI,YAAY,QAAS,OAAO,YAAY,YAAY,OAAO,GAAG,CAAC,EAAE,IAAI,YAAY,OAAO,MAAO;QACjG,MAAM,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;QACtC,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,MAAM,EAAE,IAAI,QAAQ,IAAK;YACpD,QAAQ,GAAG,CAAC,EAAE;YACd,IAAI,SAAS,QAAQ,QAAQ,QAAQ;gBACnC,SAAS;YACX;QACF;IACF,OAAO;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;QACxB,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI;YAC/B,WAAW,SAAS,GAAG,OAAO;YAC9B,IAAI,WAAW,gBAAiB,aAAa,CAAC,YAAY,WAAW,CAAC,UAAW;gBAC/E,SAAS;gBACT,eAAe;YACjB;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2716, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/min.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport cb from './_cb.js';\nimport each from './each.js';\n\n// Return the minimum element (or element-based computation).\nexport default function min(obj, iteratee, context) {\n  var result = Infinity, lastComputed = Infinity,\n      value, computed;\n  if (iteratee == null || (typeof iteratee == 'number' && typeof obj[0] != 'object' && obj != null)) {\n    obj = isArrayLike(obj) ? obj : values(obj);\n    for (var i = 0, length = obj.length; i < length; i++) {\n      value = obj[i];\n      if (value != null && value < result) {\n        result = value;\n      }\n    }\n  } else {\n    iteratee = cb(iteratee, context);\n    each(obj, function(v, index, list) {\n      computed = iteratee(v, index, list);\n      if (computed < lastComputed || (computed === Infinity && result === Infinity)) {\n        result = v;\n        lastComputed = computed;\n      }\n    });\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGe,SAAS,IAAI,GAAG,EAAE,QAAQ,EAAE,OAAO;IAChD,IAAI,SAAS,UAAU,eAAe,UAClC,OAAO;IACX,IAAI,YAAY,QAAS,OAAO,YAAY,YAAY,OAAO,GAAG,CAAC,EAAE,IAAI,YAAY,OAAO,MAAO;QACjG,MAAM,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;QACtC,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,MAAM,EAAE,IAAI,QAAQ,IAAK;YACpD,QAAQ,GAAG,CAAC,EAAE;YACd,IAAI,SAAS,QAAQ,QAAQ,QAAQ;gBACnC,SAAS;YACX;QACF;IACF,OAAO;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;QACxB,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI;YAC/B,WAAW,SAAS,GAAG,OAAO;YAC9B,IAAI,WAAW,gBAAiB,aAAa,YAAY,WAAW,UAAW;gBAC7E,SAAS;gBACT,eAAe;YACjB;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2755, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/toArray.js"], "sourcesContent": ["import isArray from './isArray.js';\nimport { slice } from './_setup.js';\nimport isString from './isString.js';\nimport isArrayLike from './_isArrayLike.js';\nimport map from './map.js';\nimport identity from './identity.js';\nimport values from './values.js';\n\n// Safely create a real, live array from anything iterable.\nvar reStrSymbol = /[^\\ud800-\\udfff]|[\\ud800-\\udbff][\\udc00-\\udfff]|[\\ud800-\\udfff]/g;\nexport default function toArray(obj) {\n  if (!obj) return [];\n  if (isArray(obj)) return slice.call(obj);\n  if (isString(obj)) {\n    // Keep surrogate pair characters together.\n    return obj.match(reStrSymbol);\n  }\n  if (isArrayLike(obj)) return map(obj, identity);\n  return values(obj);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,2DAA2D;AAC3D,IAAI,cAAc;AACH,SAAS,QAAQ,GAAG;IACjC,IAAI,CAAC,KAAK,OAAO,EAAE;IACnB,IAAI,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACpC,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QACjB,2CAA2C;QAC3C,OAAO,IAAI,KAAK,CAAC;IACnB;IACA,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,MAAM,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,iJAAA,CAAA,UAAQ;IAC9C,OAAO,CAAA,GAAA,+IAA<PERSON>,CAAA,UAAM,AAAD,EAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/sample.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport values from './values.js';\nimport getLength from './_getLength.js';\nimport random from './random.js';\nimport toArray from './toArray.js';\n\n// Sample **n** random values from a collection using the modern version of the\n// [<PERSON><PERSON> shuffle](https://en.wikipedia.org/wiki/<PERSON>–<PERSON>_shuffle).\n// If **n** is not specified, returns a single random element.\n// The internal `guard` argument allows it to work with `_.map`.\nexport default function sample(obj, n, guard) {\n  if (n == null || guard) {\n    if (!isArrayLike(obj)) obj = values(obj);\n    return obj[random(obj.length - 1)];\n  }\n  var sample = toArray(obj);\n  var length = getLength(sample);\n  n = Math.max(Math.min(n, length), 0);\n  var last = length - 1;\n  for (var index = 0; index < n; index++) {\n    var rand = random(index, last);\n    var temp = sample[index];\n    sample[index] = sample[rand];\n    sample[rand] = temp;\n  }\n  return sample.slice(0, n);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAMe,SAAS,OAAO,GAAG,EAAE,CAAC,EAAE,KAAK;IAC1C,IAAI,KAAK,QAAQ,OAAO;QACtB,IAAI,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,MAAM,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;QACpC,OAAO,GAAG,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,IAAI,MAAM,GAAG,GAAG;IACpC;IACA,IAAI,SAAS,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;IACrB,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE;IACvB,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS;IAClC,IAAI,OAAO,SAAS;IACpB,IAAK,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAS;QACtC,IAAI,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,OAAO;QACzB,IAAI,OAAO,MAAM,CAAC,MAAM;QACxB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK;QAC5B,MAAM,CAAC,KAAK,GAAG;IACjB;IACA,OAAO,OAAO,KAAK,CAAC,GAAG;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2826, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/shuffle.js"], "sourcesContent": ["import sample from './sample.js';\n\n// Shuffle a collection.\nexport default function shuffle(obj) {\n  return sample(obj, Infinity);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,QAAQ,GAAG;IACjC,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,KAAK;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2840, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/sortBy.js"], "sourcesContent": ["import cb from './_cb.js';\nimport pluck from './pluck.js';\nimport map from './map.js';\n\n// Sort the object's values by a criterion produced by an iteratee.\nexport default function sortBy(obj, iteratee, context) {\n  var index = 0;\n  iteratee = cb(iteratee, context);\n  return pluck(map(obj, function(value, key, list) {\n    return {\n      value: value,\n      index: index++,\n      criteria: iteratee(value, key, list)\n    };\n  }).sort(function(left, right) {\n    var a = left.criteria;\n    var b = right.criteria;\n    if (a !== b) {\n      if (a > b || a === void 0) return 1;\n      if (a < b || b === void 0) return -1;\n    }\n    return left.index - right.index;\n  }), 'value');\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGe,SAAS,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO;IACnD,IAAI,QAAQ;IACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;IACxB,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAK,AAAD,EAAE,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,KAAK,SAAS,KAAK,EAAE,GAAG,EAAE,IAAI;QAC7C,OAAO;YACL,OAAO;YACP,OAAO;YACP,UAAU,SAAS,OAAO,KAAK;QACjC;IACF,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,KAAK;QAC1B,IAAI,IAAI,KAAK,QAAQ;QACrB,IAAI,IAAI,MAAM,QAAQ;QACtB,IAAI,MAAM,GAAG;YACX,IAAI,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;YAClC,IAAI,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,CAAC;QACrC;QACA,OAAO,KAAK,KAAK,GAAG,MAAM,KAAK;IACjC,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2874, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_group.js"], "sourcesContent": ["import cb from './_cb.js';\nimport each from './each.js';\n\n// An internal function used for aggregate \"group by\" operations.\nexport default function group(behavior, partition) {\n  return function(obj, iteratee, context) {\n    var result = partition ? [[], []] : {};\n    iteratee = cb(iteratee, context);\n    each(obj, function(value, index) {\n      var key = iteratee(value, index, obj);\n      behavior(result, value, key);\n    });\n    return result;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,MAAM,QAAQ,EAAE,SAAS;IAC/C,OAAO,SAAS,GAAG,EAAE,QAAQ,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY;YAAC,EAAE;YAAE,EAAE;SAAC,GAAG,CAAC;QACrC,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;QACxB,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK;YAC7B,IAAI,MAAM,SAAS,OAAO,OAAO;YACjC,SAAS,QAAQ,OAAO;QAC1B;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/groupBy.js"], "sourcesContent": ["import group from './_group.js';\nimport has from './_has.js';\n\n// Groups the object's values by a criterion. Pass either a string attribute\n// to group by, or a function that returns the criterion.\nexport default group(function(result, value, key) {\n  if (has(result, key)) result[key].push(value); else result[key] = [value];\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,CAAA,GAAA,+IAAA,CAAA,UAAK,AAAD,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE,GAAG;IAC9C,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;SAAa,MAAM,CAAC,IAAI,GAAG;QAAC;KAAM;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2920, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/indexBy.js"], "sourcesContent": ["import group from './_group.js';\n\n// Indexes the object's values by a criterion, similar to `_.groupBy`, but for\n// when you know that your index values will be unique.\nexport default group(function(result, value, key) {\n  result[key] = value;\n});\n"], "names": [], "mappings": ";;;AAAA;;uCAIe,CAAA,GAAA,+IAAA,CAAA,UAAK,AAAD,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE,GAAG;IAC9C,MAAM,CAAC,IAAI,GAAG;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2934, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/countBy.js"], "sourcesContent": ["import group from './_group.js';\nimport has from './_has.js';\n\n// Counts instances of an object that group by a certain criterion. Pass\n// either a string attribute to count by, or a function that returns the\n// criterion.\nexport default group(function(result, value, key) {\n  if (has(result, key)) result[key]++; else result[key] = 1;\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAKe,CAAA,GAAA,+IAAA,CAAA,UAAK,AAAD,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE,GAAG;IAC9C,IAAI,CAAA,GAAA,6IAAA,CAAA,UAAG,AAAD,EAAE,QAAQ,MAAM,MAAM,CAAC,IAAI;SAAS,MAAM,CAAC,IAAI,GAAG;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2951, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/partition.js"], "sourcesContent": ["import group from './_group.js';\n\n// Split a collection into two arrays: one whose elements all pass the given\n// truth test, and one whose elements all do not pass the truth test.\nexport default group(function(result, value, pass) {\n  result[pass ? 0 : 1].push(value);\n}, true);\n"], "names": [], "mappings": ";;;AAAA;;uCAIe,CAAA,GAAA,+IAAA,CAAA,UAAK,AAAD,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE,IAAI;IAC/C,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC;AAC5B,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/size.js"], "sourcesContent": ["import isArrayLike from './_isArrayLike.js';\nimport keys from './keys.js';\n\n// Return the number of elements in a collection.\nexport default function size(obj) {\n  if (obj == null) return 0;\n  return isArrayLike(obj) ? obj.length : keys(obj).length;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGe,SAAS,KAAK,GAAG;IAC9B,IAAI,OAAO,MAAM,OAAO;IACxB,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,OAAO,IAAI,MAAM,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,MAAM;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_keyInObj.js"], "sourcesContent": ["// Internal `_.pick` helper function to determine whether `key` is an enumerable\n// property name of `obj`.\nexport default function keyInObj(value, key, obj) {\n  return key in obj;\n}\n"], "names": [], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;;;;AACX,SAAS,SAAS,KAAK,EAAE,GAAG,EAAE,GAAG;IAC9C,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/pick.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport isFunction from './isFunction.js';\nimport optimizeCb from './_optimizeCb.js';\nimport allKeys from './allKeys.js';\nimport keyInObj from './_keyInObj.js';\nimport flatten from './_flatten.js';\n\n// Return a copy of the object only containing the allowed properties.\nexport default restArguments(function(obj, keys) {\n  var result = {}, iteratee = keys[0];\n  if (obj == null) return result;\n  if (isFunction(iteratee)) {\n    if (keys.length > 1) iteratee = optimizeCb(iteratee, keys[1]);\n    keys = allKeys(obj);\n  } else {\n    iteratee = keyInObj;\n    keys = flatten(keys, false, false);\n    obj = Object(obj);\n  }\n  for (var i = 0, length = keys.length; i < length; i++) {\n    var key = keys[i];\n    var value = obj[key];\n    if (iteratee(value, key, obj)) result[key] = value;\n  }\n  return result;\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;uCAGe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,GAAG,EAAE,IAAI;IAC7C,IAAI,SAAS,CAAC,GAAG,WAAW,IAAI,CAAC,EAAE;IACnC,IAAI,OAAO,MAAM,OAAO;IACxB,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,WAAW;QACxB,IAAI,KAAK,MAAM,GAAG,GAAG,WAAW,CAAA,GAAA,oJAAA,CAAA,UAAU,AAAD,EAAE,UAAU,IAAI,CAAC,EAAE;QAC5D,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE;IACjB,OAAO;QACL,WAAW,kJAAA,CAAA,UAAQ;QACnB,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO;QAC5B,MAAM,OAAO;IACf;IACA,IAAK,IAAI,IAAI,GAAG,SAAS,KAAK,MAAM,EAAE,IAAI,QAAQ,IAAK;QACrD,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,QAAQ,GAAG,CAAC,IAAI;QACpB,IAAI,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC,IAAI,GAAG;IAC/C;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3035, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/omit.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport isFunction from './isFunction.js';\nimport negate from './negate.js';\nimport map from './map.js';\nimport flatten from './_flatten.js';\nimport contains from './contains.js';\nimport pick from './pick.js';\n\n// Return a copy of the object without the disallowed properties.\nexport default restArguments(function(obj, keys) {\n  var iteratee = keys[0], context;\n  if (isFunction(iteratee)) {\n    iteratee = negate(iteratee);\n    if (keys.length > 1) context = keys[1];\n  } else {\n    keys = map(flatten(keys, false, false), String);\n    iteratee = function(value, key) {\n      return !contains(keys, key);\n    };\n  }\n  return pick(obj, iteratee, context);\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;uCAGe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,GAAG,EAAE,IAAI;IAC7C,IAAI,WAAW,IAAI,CAAC,EAAE,EAAE;IACxB,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,WAAW;QACxB,WAAW,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE;QAClB,IAAI,KAAK,MAAM,GAAG,GAAG,UAAU,IAAI,CAAC,EAAE;IACxC,OAAO;QACL,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,QAAQ;QACxC,WAAW,SAAS,KAAK,EAAE,GAAG;YAC5B,OAAO,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;QACzB;IACF;IACA,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,KAAK,UAAU;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3071, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/initial.js"], "sourcesContent": ["import { slice } from './_setup.js';\n\n// Returns everything but the last entry of the array. Especially useful on\n// the arguments object. Passing **n** will return all the values in\n// the array, excluding the last N.\nexport default function initial(array, n, guard) {\n  return slice.call(array, 0, Math.max(0, array.length - (n == null || guard ? 1 : n)));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,QAAQ,KAAK,EAAE,CAAC,EAAE,KAAK;IAC7C,OAAO,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,CAAC,KAAK,QAAQ,QAAQ,IAAI,CAAC;AACpF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/first.js"], "sourcesContent": ["import initial from './initial.js';\n\n// Get the first element of an array. Passing **n** will return the first N\n// values in the array. The **guard** check allows it to work with `_.map`.\nexport default function first(array, n, guard) {\n  if (array == null || array.length < 1) return n == null || guard ? void 0 : [];\n  if (n == null || guard) return array[0];\n  return initial(array, array.length - n);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,MAAM,KAAK,EAAE,CAAC,EAAE,KAAK;IAC3C,IAAI,SAAS,QAAQ,MAAM,MAAM,GAAG,GAAG,OAAO,KAAK,QAAQ,QAAQ,KAAK,IAAI,EAAE;IAC9E,IAAI,KAAK,QAAQ,OAAO,OAAO,KAAK,CAAC,EAAE;IACvC,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM,GAAG;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/rest.js"], "sourcesContent": ["import { slice } from './_setup.js';\n\n// Returns everything but the first entry of the `array`. Especially useful on\n// the `arguments` object. Passing an **n** will return the rest N values in the\n// `array`.\nexport default function rest(array, n, guard) {\n  return slice.call(array, n == null || guard ? 1 : n);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK;IAC1C,OAAO,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,QAAQ,IAAI;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/last.js"], "sourcesContent": ["import rest from './rest.js';\n\n// Get the last element of an array. Passing **n** will return the last N\n// values in the array.\nexport default function last(array, n, guard) {\n  if (array == null || array.length < 1) return n == null || guard ? void 0 : [];\n  if (n == null || guard) return array[array.length - 1];\n  return rest(array, Math.max(0, array.length - n));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK;IAC1C,IAAI,SAAS,QAAQ,MAAM,MAAM,GAAG,GAAG,OAAO,KAAK,QAAQ,QAAQ,KAAK,IAAI,EAAE;IAC9E,IAAI,KAAK,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IACtD,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,OAAO,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/compact.js"], "sourcesContent": ["import filter from './filter.js';\n\n// Trim out all falsy values from an array.\nexport default function compact(array) {\n  return filter(array, Boolean);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,QAAQ,KAAK;IACnC,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,OAAO;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/flatten.js"], "sourcesContent": ["import _flatten from './_flatten.js';\n\n// Flatten out an array, either recursively (by default), or up to `depth`.\n// Passing `true` or `false` as `depth` means `1` or `Infinity`, respectively.\nexport default function flatten(array, depth) {\n  return _flatten(array, depth, false);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,QAAQ,KAAK,EAAE,KAAK;IAC1C,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,OAAO,OAAO;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/difference.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport flatten from './_flatten.js';\nimport filter from './filter.js';\nimport contains from './contains.js';\n\n// Take the difference between one array and a number of other arrays.\n// Only the elements present in just the first array will remain.\nexport default restArguments(function(array, rest) {\n  rest = flatten(rest, true, true);\n  return filter(array, function(value){\n    return !contains(rest, value);\n  });\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,KAAK,EAAE,IAAI;IAC/C,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,MAAM;IAC3B,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAM,AAAD,EAAE,OAAO,SAAS,KAAK;QACjC,OAAO,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3182, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/without.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport difference from './difference.js';\n\n// Return a version of the array that does not contain the specified value(s).\nexport default restArguments(function(array, otherArrays) {\n  return difference(array, otherArrays);\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,KAAK,EAAE,WAAW;IACtD,OAAO,CAAA,GAAA,mJAAA,CAAA,UAAU,AAAD,EAAE,OAAO;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/uniq.js"], "sourcesContent": ["import isBoolean from './isBoolean.js';\nimport cb from './_cb.js';\nimport getLength from './_getLength.js';\nimport contains from './contains.js';\n\n// Produce a duplicate-free version of the array. If the array has already\n// been sorted, you have the option of using a faster algorithm.\n// The faster algorithm will not work with an iteratee if the iteratee\n// is not a one-to-one function, so providing an iteratee will disable\n// the faster algorithm.\nexport default function uniq(array, isSorted, iteratee, context) {\n  if (!isBoolean(isSorted)) {\n    context = iteratee;\n    iteratee = isSorted;\n    isSorted = false;\n  }\n  if (iteratee != null) iteratee = cb(iteratee, context);\n  var result = [];\n  var seen = [];\n  for (var i = 0, length = getLength(array); i < length; i++) {\n    var value = array[i],\n        computed = iteratee ? iteratee(value, i, array) : value;\n    if (isSorted && !iteratee) {\n      if (!i || seen !== computed) result.push(value);\n      seen = computed;\n    } else if (iteratee) {\n      if (!contains(seen, computed)) {\n        seen.push(computed);\n        result.push(value);\n      }\n    } else if (!contains(result, value)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAOe,SAAS,KAAK,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;IAC7D,IAAI,CAAC,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QACxB,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA,IAAI,YAAY,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,UAAE,AAAD,EAAE,UAAU;IAC9C,IAAI,SAAS,EAAE;IACf,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,IAAI,GAAG,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,IAAI,QAAQ,IAAK;QAC1D,IAAI,QAAQ,KAAK,CAAC,EAAE,EAChB,WAAW,WAAW,SAAS,OAAO,GAAG,SAAS;QACtD,IAAI,YAAY,CAAC,UAAU;YACzB,IAAI,CAAC,KAAK,SAAS,UAAU,OAAO,IAAI,CAAC;YACzC,OAAO;QACT,OAAO,IAAI,UAAU;YACnB,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,WAAW;gBAC7B,KAAK,IAAI,CAAC;gBACV,OAAO,IAAI,CAAC;YACd;QACF,OAAO,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,QAAQ;YACnC,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/union.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport uniq from './uniq.js';\nimport flatten from './_flatten.js';\n\n// Produce an array that contains the union: each distinct element from all of\n// the passed-in arrays.\nexport default restArguments(function(arrays) {\n  return uniq(flatten(arrays, true, true));\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,SAAS,MAAM;IAC1C,OAAO,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,MAAM;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/intersection.js"], "sourcesContent": ["import getLength from './_getLength.js';\nimport contains from './contains.js';\n\n// Produce an array that contains every item shared between all the\n// passed-in arrays.\nexport default function intersection(array) {\n  var result = [];\n  var argsLength = arguments.length;\n  for (var i = 0, length = getLength(array); i < length; i++) {\n    var item = array[i];\n    if (contains(result, item)) continue;\n    var j;\n    for (j = 1; j < argsLength; j++) {\n      if (!contains(arguments[j], item)) break;\n    }\n    if (j === argsLength) result.push(item);\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIe,SAAS,aAAa,KAAK;IACxC,IAAI,SAAS,EAAE;IACf,IAAI,aAAa,UAAU,MAAM;IACjC,IAAK,IAAI,IAAI,GAAG,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,IAAI,QAAQ,IAAK;QAC1D,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,OAAO;QAC5B,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,YAAY,IAAK;YAC/B,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAQ,AAAD,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO;QACrC;QACA,IAAI,MAAM,YAAY,OAAO,IAAI,CAAC;IACpC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/unzip.js"], "sourcesContent": ["import max from './max.js';\nimport getLength from './_getLength.js';\nimport pluck from './pluck.js';\n\n// Complement of zip. Unzip accepts an array of arrays and groups\n// each array's elements on shared indices.\nexport default function unzip(array) {\n  var length = (array && max(array, getLength).length) || 0;\n  var result = Array(length);\n\n  for (var index = 0; index < length; index++) {\n    result[index] = pluck(array, index);\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIe,SAAS,MAAM,KAAK;IACjC,IAAI,SAAS,AAAC,SAAS,CAAA,GAAA,4IAAA,CAAA,UAAG,AAAD,EAAE,OAAO,mJAAA,CAAA,UAAS,EAAE,MAAM,IAAK;IACxD,IAAI,SAAS,MAAM;IAEnB,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,QAAS;QAC3C,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAK,AAAD,EAAE,OAAO;IAC/B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/zip.js"], "sourcesContent": ["import restArguments from './restArguments.js';\nimport unzip from './unzip.js';\n\n// Zip together multiple lists into a single array -- elements that share\n// an index go together.\nexport default restArguments(unzip);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIe,CAAA,GAAA,sJAAA,CAAA,UAAa,AAAD,EAAE,8IAAA,CAAA,UAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/object.js"], "sourcesContent": ["import getLength  from './_getLength.js';\n\n// Converts lists into objects. Pass either a single array of `[key, value]`\n// pairs, or two parallel arrays of the same length -- one of keys, and one of\n// the corresponding values. Passing by pairs is the reverse of `_.pairs`.\nexport default function object(list, values) {\n  var result = {};\n  for (var i = 0, length = getLength(list); i < length; i++) {\n    if (values) {\n      result[list[i]] = values[i];\n    } else {\n      result[list[i][0]] = list[i][1];\n    }\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAKe,SAAS,OAAO,IAAI,EAAE,MAAM;IACzC,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,IAAI,GAAG,SAAS,CAAA,GAAA,mJAAA,CAAA,UAAS,AAAD,EAAE,OAAO,IAAI,QAAQ,IAAK;QACzD,IAAI,QAAQ;YACV,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE;QAC7B,OAAO;YACL,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE;QACjC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/range.js"], "sourcesContent": ["// Generate an integer Array containing an arithmetic progression. A port of\n// the native Python `range()` function. See\n// [the Python documentation](https://docs.python.org/library/functions.html#range).\nexport default function range(start, stop, step) {\n  if (stop == null) {\n    stop = start || 0;\n    start = 0;\n  }\n  if (!step) {\n    step = stop < start ? -1 : 1;\n  }\n\n  var length = Math.max(Math.ceil((stop - start) / step), 0);\n  var range = Array(length);\n\n  for (var idx = 0; idx < length; idx++, start += step) {\n    range[idx] = start;\n  }\n\n  return range;\n}\n"], "names": [], "mappings": "AAAA,4EAA4E;AAC5E,4CAA4C;AAC5C,oFAAoF;;;;AACrE,SAAS,MAAM,KAAK,EAAE,IAAI,EAAE,IAAI;IAC7C,IAAI,QAAQ,MAAM;QAChB,OAAO,SAAS;QAChB,QAAQ;IACV;IACA,IAAI,CAAC,MAAM;QACT,OAAO,OAAO,QAAQ,CAAC,IAAI;IAC7B;IAEA,IAAI,SAAS,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,IAAI,OAAO;IACxD,IAAI,QAAQ,MAAM;IAElB,IAAK,IAAI,MAAM,GAAG,MAAM,QAAQ,OAAO,SAAS,KAAM;QACpD,KAAK,CAAC,IAAI,GAAG;IACf;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/chunk.js"], "sourcesContent": ["import { slice } from './_setup.js';\n\n// Chunk a single array into multiple arrays, each containing `count` or fewer\n// items.\nexport default function chunk(array, count) {\n  if (count == null || count < 1) return [];\n  var result = [];\n  var i = 0, length = array.length;\n  while (i < length) {\n    result.push(slice.call(array, i, i += count));\n  }\n  return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIe,SAAS,MAAM,KAAK,EAAE,KAAK;IACxC,IAAI,SAAS,QAAQ,QAAQ,GAAG,OAAO,EAAE;IACzC,IAAI,SAAS,EAAE;IACf,IAAI,IAAI,GAAG,SAAS,MAAM,MAAM;IAChC,MAAO,IAAI,OAAQ;QACjB,OAAO,IAAI,CAAC,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;IACxC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/_chainResult.js"], "sourcesContent": ["import _ from './underscore.js';\n\n// Helper function to continue chaining intermediate results.\nexport default function chainResult(instance, obj) {\n  return instance._chain ? _(obj).chain() : obj;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,YAAY,QAAQ,EAAE,GAAG;IAC/C,OAAO,SAAS,MAAM,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,KAAK,KAAK;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/mixin.js"], "sourcesContent": ["import _ from './underscore.js';\nimport each from './each.js';\nimport functions from './functions.js';\nimport { push } from './_setup.js';\nimport chainResult from './_chainResult.js';\n\n// Add your own custom functions to the Underscore object.\nexport default function mixin(obj) {\n  each(functions(obj), function(name) {\n    var func = _[name] = obj[name];\n    _.prototype[name] = function() {\n      var args = [this._wrapped];\n      push.apply(args, arguments);\n      return chainResult(this, func.apply(_, args));\n    };\n  });\n  return _;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGe,SAAS,MAAM,GAAG;IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE,MAAM,SAAS,IAAI;QAChC,IAAI,OAAO,mJAAA,CAAA,UAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;QAC9B,mJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,KAAK,GAAG;YAClB,IAAI,OAAO;gBAAC,IAAI,CAAC,QAAQ;aAAC;YAC1B,+IAAA,CAAA,OAAI,CAAC,KAAK,CAAC,MAAM;YACjB,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,IAAI,EAAE,KAAK,KAAK,CAAC,mJAAA,CAAA,UAAC,EAAE;QACzC;IACF;IACA,OAAO,mJAAA,CAAA,UAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3437, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/underscore-array-methods.js"], "sourcesContent": ["import _ from './underscore.js';\nimport each from './each.js';\nimport { ArrayProto } from './_setup.js';\nimport chainResult from './_chainResult.js';\n\n// Add all mutator `Array` functions to the wrapper.\neach(['pop', 'push', 'reverse', 'shift', 'sort', 'splice', 'unshift'], function(name) {\n  var method = ArrayProto[name];\n  _.prototype[name] = function() {\n    var obj = this._wrapped;\n    if (obj != null) {\n      method.apply(obj, arguments);\n      if ((name === 'shift' || name === 'splice') && obj.length === 0) {\n        delete obj[0];\n      }\n    }\n    return chainResult(this, obj);\n  };\n});\n\n// Add all accessor `Array` functions to the wrapper.\neach(['concat', 'join', 'slice'], function(name) {\n  var method = ArrayProto[name];\n  _.prototype[name] = function() {\n    var obj = this._wrapped;\n    if (obj != null) obj = method.apply(obj, arguments);\n    return chainResult(this, obj);\n  };\n});\n\nexport default _;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,oDAAoD;AACpD,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;IAAC;IAAO;IAAQ;IAAW;IAAS;IAAQ;IAAU;CAAU,EAAE,SAAS,IAAI;IAClF,IAAI,SAAS,+IAAA,CAAA,aAAU,CAAC,KAAK;IAC7B,mJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,KAAK,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,QAAQ;QACvB,IAAI,OAAO,MAAM;YACf,OAAO,KAAK,CAAC,KAAK;YAClB,IAAI,CAAC,SAAS,WAAW,SAAS,QAAQ,KAAK,IAAI,MAAM,KAAK,GAAG;gBAC/D,OAAO,GAAG,CAAC,EAAE;YACf;QACF;QACA,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,IAAI,EAAE;IAC3B;AACF;AAEA,qDAAqD;AACrD,CAAA,GAAA,6IAAA,CAAA,UAAI,AAAD,EAAE;IAAC;IAAU;IAAQ;CAAQ,EAAE,SAAS,IAAI;IAC7C,IAAI,SAAS,+IAAA,CAAA,aAAU,CAAC,KAAK;IAC7B,mJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,KAAK,GAAG;QAClB,IAAI,MAAM,IAAI,CAAC,QAAQ;QACvB,IAAI,OAAO,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK;QACzC,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAW,AAAD,EAAE,IAAI,EAAE;IAC3B;AACF;uCAEe,mJAAA,CAAA,UAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/index.js"], "sourcesContent": ["// Named Exports\n// =============\n\n//     Underscore.js 1.13.7\n//     https://underscorejs.org\n//     (c) 2009-2024 <PERSON>, <PERSON>, and DocumentCloud and Investigative Reporters & Editors\n//     Underscore may be freely distributed under the MIT license.\n\n// Baseline setup.\nexport { VERSION } from './_setup.js';\nexport { default as restArguments } from './restArguments.js';\n\n// Object Functions\n// ----------------\n// Our most fundamental functions operate on any JavaScript object.\n// Most functions in Underscore depend on at least one function in this section.\n\n// A group of functions that check the types of core JavaScript values.\n// These are often informally referred to as the \"isType\" functions.\nexport { default as isObject } from './isObject.js';\nexport { default as isNull } from './isNull.js';\nexport { default as isUndefined } from './isUndefined.js';\nexport { default as isBoolean } from './isBoolean.js';\nexport { default as isElement } from './isElement.js';\nexport { default as isString } from './isString.js';\nexport { default as isNumber } from './isNumber.js';\nexport { default as isDate } from './isDate.js';\nexport { default as isRegExp } from './isRegExp.js';\nexport { default as isError } from './isError.js';\nexport { default as isSymbol } from './isSymbol.js';\nexport { default as isArrayBuffer } from './isArrayBuffer.js';\nexport { default as isDataView } from './isDataView.js';\nexport { default as isArray } from './isArray.js';\nexport { default as isFunction } from './isFunction.js';\nexport { default as isArguments } from './isArguments.js';\nexport { default as isFinite } from './isFinite.js';\nexport { default as isNaN } from './isNaN.js';\nexport { default as isTypedArray } from './isTypedArray.js';\nexport { default as isEmpty } from './isEmpty.js';\nexport { default as isMatch } from './isMatch.js';\nexport { default as isEqual } from './isEqual.js';\nexport { default as isMap } from './isMap.js';\nexport { default as isWeakMap } from './isWeakMap.js';\nexport { default as isSet } from './isSet.js';\nexport { default as isWeakSet } from './isWeakSet.js';\n\n// Functions that treat an object as a dictionary of key-value pairs.\nexport { default as keys } from './keys.js';\nexport { default as allKeys } from './allKeys.js';\nexport { default as values } from './values.js';\nexport { default as pairs } from './pairs.js';\nexport { default as invert } from './invert.js';\nexport { default as functions,\n         default as methods   } from './functions.js';\nexport { default as extend } from './extend.js';\nexport { default as extendOwn,\n         default as assign    } from './extendOwn.js';\nexport { default as defaults } from './defaults.js';\nexport { default as create } from './create.js';\nexport { default as clone } from './clone.js';\nexport { default as tap } from './tap.js';\nexport { default as get } from './get.js';\nexport { default as has } from './has.js';\nexport { default as mapObject } from './mapObject.js';\n\n// Utility Functions\n// -----------------\n// A bit of a grab bag: Predicate-generating functions for use with filters and\n// loops, string escaping and templating, create random numbers and unique ids,\n// and functions that facilitate Underscore's chaining and iteration conventions.\nexport { default as identity } from './identity.js';\nexport { default as constant } from './constant.js';\nexport { default as noop } from './noop.js';\nexport { default as toPath } from './toPath.js';\nexport { default as property } from './property.js';\nexport { default as propertyOf } from './propertyOf.js';\nexport { default as matcher,\n         default as matches } from './matcher.js';\nexport { default as times } from './times.js';\nexport { default as random } from './random.js';\nexport { default as now } from './now.js';\nexport { default as escape } from './escape.js';\nexport { default as unescape } from './unescape.js';\nexport { default as templateSettings } from './templateSettings.js';\nexport { default as template } from './template.js';\nexport { default as result } from './result.js';\nexport { default as uniqueId } from './uniqueId.js';\nexport { default as chain } from './chain.js';\nexport { default as iteratee } from './iteratee.js';\n\n// Function (ahem) Functions\n// -------------------------\n// These functions take a function as an argument and return a new function\n// as the result. Also known as higher-order functions.\nexport { default as partial } from './partial.js';\nexport { default as bind } from './bind.js';\nexport { default as bindAll } from './bindAll.js';\nexport { default as memoize } from './memoize.js';\nexport { default as delay } from './delay.js';\nexport { default as defer } from './defer.js';\nexport { default as throttle } from './throttle.js';\nexport { default as debounce } from './debounce.js';\nexport { default as wrap } from './wrap.js';\nexport { default as negate } from './negate.js';\nexport { default as compose } from './compose.js';\nexport { default as after } from './after.js';\nexport { default as before } from './before.js';\nexport { default as once } from './once.js';\n\n// Finders\n// -------\n// Functions that extract (the position of) a single element from an object\n// or array based on some criterion.\nexport { default as findKey } from './findKey.js';\nexport { default as findIndex } from './findIndex.js';\nexport { default as findLastIndex } from './findLastIndex.js';\nexport { default as sortedIndex } from './sortedIndex.js';\nexport { default as indexOf } from './indexOf.js';\nexport { default as lastIndexOf } from './lastIndexOf.js';\nexport { default as find,\n         default as detect } from './find.js';\nexport { default as findWhere } from './findWhere.js';\n\n// Collection Functions\n// --------------------\n// Functions that work on any collection of elements: either an array, or\n// an object of key-value pairs.\nexport { default as each,\n         default as forEach } from './each.js';\nexport { default as map,\n         default as collect } from './map.js';\nexport { default as reduce,\n         default as foldl,\n         default as inject } from './reduce.js';\nexport { default as reduceRight,\n         default as foldr       } from './reduceRight.js';\nexport { default as filter,\n         default as select } from './filter.js';\nexport { default as reject } from './reject.js';\nexport { default as every,\n         default as all   } from './every.js';\nexport { default as some,\n         default as any  } from './some.js';\nexport { default as contains,\n         default as includes,\n         default as include  } from './contains.js';\nexport { default as invoke } from './invoke.js';\nexport { default as pluck } from './pluck.js';\nexport { default as where } from './where.js';\nexport { default as max } from './max.js';\nexport { default as min } from './min.js';\nexport { default as shuffle } from './shuffle.js';\nexport { default as sample } from './sample.js';\nexport { default as sortBy } from './sortBy.js';\nexport { default as groupBy } from './groupBy.js';\nexport { default as indexBy } from './indexBy.js';\nexport { default as countBy } from './countBy.js';\nexport { default as partition } from './partition.js';\nexport { default as toArray } from './toArray.js';\nexport { default as size } from './size.js';\n\n// `_.pick` and `_.omit` are actually object functions, but we put\n// them here in order to create a more natural reading order in the\n// monolithic build as they depend on `_.contains`.\nexport { default as pick } from './pick.js';\nexport { default as omit } from './omit.js';\n\n// Array Functions\n// ---------------\n// Functions that operate on arrays (and array-likes) only, because they’re\n// expressed in terms of operations on an ordered list of values.\nexport { default as first,\n         default as head,\n         default as take  } from './first.js';\nexport { default as initial } from './initial.js';\nexport { default as last } from './last.js';\nexport { default as rest,\n         default as tail,\n         default as drop } from './rest.js';\nexport { default as compact } from './compact.js';\nexport { default as flatten } from './flatten.js';\nexport { default as without } from './without.js';\nexport { default as uniq,\n         default as unique } from './uniq.js';\nexport { default as union } from './union.js';\nexport { default as intersection } from './intersection.js';\nexport { default as difference } from './difference.js';\nexport { default as unzip,\n         default as transpose } from './unzip.js';\nexport { default as zip } from './zip.js';\nexport { default as object } from './object.js';\nexport { default as range } from './range.js';\nexport { default as chunk } from './chunk.js';\n\n// OOP\n// ---\n// These modules support the \"object-oriented\" calling style. See also\n// `underscore.js` and `index-default.js`.\nexport { default as mixin } from './mixin.js';\nexport { default } from './underscore-array-methods.js';\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,gBAAgB;AAEhB,2BAA2B;AAC3B,+BAA+B;AAC/B,+GAA+G;AAC/G,kEAAkE;AAElE,kBAAkB;;AAClB;AACA;AAEA,mBAAmB;AACnB,mBAAmB;AACnB,mEAAmE;AACnE,gFAAgF;AAEhF,uEAAuE;AACvE,oEAAoE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,qEAAqE;AACrE;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB,oBAAoB;AACpB,+EAA+E;AAC/E,+EAA+E;AAC/E,iFAAiF;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,4BAA4B;AAC5B,4BAA4B;AAC5B,2EAA2E;AAC3E,uDAAuD;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,UAAU;AACV,UAAU;AACV,2EAA2E;AAC3E,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA,uBAAuB;AACvB,uBAAuB;AACvB,yEAAyE;AACzE,gCAAgC;AAChC;AAEA;AAEA;AAGA;AAEA;AAEA;AACA;AAEA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,kEAAkE;AAClE,mEAAmE;AACnE,mDAAmD;AACnD;AACA;AAEA,kBAAkB;AAClB,kBAAkB;AAClB,2EAA2E;AAC3E,iEAAiE;AACjE;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA,MAAM;AACN,MAAM;AACN,sEAAsE;AACtE,0CAA0C;AAC1C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4372, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/index-default.js"], "sourcesContent": ["// Default Export\n// ==============\n// In this module, we mix our bundled exports into the `_` object and export\n// the result. This is analogous to setting `module.exports = _` in CommonJS.\n// Hence, this module is also the entry point of our UMD bundle and the package\n// entry point for CommonJS and AMD users. In other words, this is (the source\n// of) the module you are interfacing with when you do any of the following:\n//\n// ```js\n// // CommonJS\n// var _ = require('underscore');\n//\n// // AMD\n// define(['underscore'], function(_) {...});\n//\n// // UMD in the browser\n// // _ is available as a global variable\n// ```\nimport * as allExports from './index.js';\nimport { mixin } from './index.js';\n\n// Add all of the Underscore functions to the wrapper object.\nvar _ = mixin(allExports);\n// Legacy Node.js API.\n_._ = _;\n// Export the Underscore API.\nexport default _;\n"], "names": [], "mappings": "AAAA,iBAAiB;AACjB,iBAAiB;AACjB,4EAA4E;AAC5E,6EAA6E;AAC7E,+EAA+E;AAC/E,8EAA8E;AAC9E,4EAA4E;AAC5E,EAAE;AACF,QAAQ;AACR,cAAc;AACd,iCAAiC;AACjC,EAAE;AACF,SAAS;AACT,6CAA6C;AAC7C,EAAE;AACF,wBAAwB;AACxB,yCAAyC;AACzC,MAAM;;;;AACN;AAAA;AACA;;;AAEA,6DAA6D;AAC7D,IAAI,IAAI,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAE;AACd,sBAAsB;AACtB,EAAE,CAAC,GAAG;uCAES", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4409, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/node_modules/underscore/modules/index-all.js"], "sourcesContent": ["// ESM Exports\n// ===========\n// This module is the package entry point for ES module users. In other words,\n// it is the module they are interfacing with when they import from the whole\n// package instead of from a submodule, like this:\n//\n// ```js\n// import { map } from 'underscore';\n// ```\n//\n// The difference with `./index-default`, which is the package entry point for\n// CommonJS, AMD and UMD users, is purely technical. In ES modules, named and\n// default exports are considered to be siblings, so when you have a default\n// export, its properties are not automatically available as named exports. For\n// this reason, we re-export the named exports in addition to providing the same\n// default export as in `./index-default`.\nexport { default } from './index-default.js';\nexport * from './index.js';\n"], "names": [], "mappings": "AAAA,cAAc;AACd,cAAc;AACd,8EAA8E;AAC9E,6EAA6E;AAC7E,kDAAkD;AAClD,EAAE;AACF,QAAQ;AACR,oCAAoC;AACpC,MAAM;AACN,EAAE;AACF,8EAA8E;AAC9E,6EAA6E;AAC7E,4EAA4E;AAC5E,+EAA+E;AAC/E,gFAAgF;AAChF,0CAA0C;;AAC1C;AACA", "ignoreList": [0], "debugId": null}}]}