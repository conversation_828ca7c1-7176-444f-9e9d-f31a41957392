// 测试一致性验证API
async function testConsistencyValidation() {
  try {
    console.log('🔍 测试一致性验证API...');
    
    const generationId = 'cmc62e9d70001vm44etf0st72'; // 之前生成的ID
    
    console.log('📤 发送一致性验证请求...');
    
    const response = await fetch('http://localhost:3001/api/ai/validate-consistency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        generationId,
        validationType: 'full',
        strictMode: false
      })
    });

    console.log('📊 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API响应失败:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 一致性验证成功！');
      console.log('\n📋 验证结果：');
      
      console.log('🆔 验证ID:', result.data.validationId);
      console.log('🎯 验证类型:', result.data.validationType);
      console.log('🔒 严格模式:', result.data.strictMode ? '是' : '否');
      
      console.log('\n📊 评分摘要:');
      console.log('  总体评分:', (result.data.summary.overallScore * 100).toFixed(1) + '%');
      console.log('  角色一致性:', (result.data.summary.characterConsistency * 100).toFixed(1) + '%');
      console.log('  场景一致性:', (result.data.summary.sceneConsistency * 100).toFixed(1) + '%');
      console.log('  发现问题:', result.data.summary.issuesFound, '个');
      console.log('  改进建议:', result.data.summary.recommendations, '条');
      
      if (result.data.results.characterValidation) {
        console.log('\n👥 角色验证详情:');
        result.data.results.characterValidation.characterScores.forEach((char, index) => {
          console.log(`  ${index + 1}. ${char.characterName}`);
          console.log(`     评分: ${(char.score * 100).toFixed(1)}%`);
          console.log(`     项目关联: ${char.hasProjectReference ? '是' : '否'}`);
        });
      }
      
      if (result.data.results.sceneValidation) {
        console.log('\n🏞️ 场景验证详情:');
        result.data.results.sceneValidation.sceneScores.forEach((scene, index) => {
          console.log(`  ${index + 1}. ${scene.sceneName}`);
          console.log(`     评分: ${(scene.score * 100).toFixed(1)}%`);
        });
      }
      
      if (result.data.results.issuesFound && result.data.results.issuesFound.length > 0) {
        console.log('\n⚠️ 发现的问题:');
        result.data.results.issuesFound.forEach((issue, index) => {
          console.log(`  ${index + 1}. [${issue.severity}] ${issue.description}`);
        });
      }
      
      if (result.data.results.recommendations && result.data.results.recommendations.length > 0) {
        console.log('\n💡 改进建议:');
        result.data.results.recommendations.forEach((rec, index) => {
          console.log(`  ${index + 1}. [${rec.priority}] ${rec.description}`);
        });
      }
      
    } else {
      console.log('❌ 验证失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testConsistencyValidation();
