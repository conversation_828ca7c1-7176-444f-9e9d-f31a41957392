const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanFailedSegments() {
  try {
    console.log('🧹 清理失败的视频片段...');
    
    // 查询失败的视频片段
    const failedSegments = await prisma.videoSegment.findMany({
      where: { status: 'failed' },
      include: {
        storyVideo: true
      }
    });
    
    console.log(`找到 ${failedSegments.length} 个失败的视频片段`);
    
    if (failedSegments.length === 0) {
      console.log('✅ 没有失败的片段需要清理');
      return;
    }
    
    // 删除失败的片段
    const deleteResult = await prisma.videoSegment.deleteMany({
      where: { status: 'failed' }
    });
    
    console.log(`🗑️ 已删除 ${deleteResult.count} 个失败的视频片段`);
    
    // 获取相关的故事视频ID
    const storyVideoIds = [...new Set(failedSegments.map(s => s.storyVideoId))];
    
    // 检查每个故事视频是否还有剩余片段
    for (const storyVideoId of storyVideoIds) {
      const remainingSegments = await prisma.videoSegment.findMany({
        where: { storyVideoId }
      });
      
      if (remainingSegments.length === 0) {
        // 如果没有剩余片段，删除故事视频记录
        await prisma.storyVideo.delete({
          where: { id: storyVideoId }
        });
        console.log(`🗑️ 已删除空的故事视频记录: ${storyVideoId}`);
      } else {
        console.log(`📊 故事视频 ${storyVideoId} 还有 ${remainingSegments.length} 个片段`);
      }
    }
    
    console.log('✅ 清理完成');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanFailedSegments();
