// 直接测试豆包API调用
async function testDoubaoDirectAPI() {
  try {
    console.log('🧪 直接测试豆包API...');
    
    const apiKey = 'e7fc00da-28b5-4628-9c59-588d559cdf1c';
    
    // 测试所有三个模型
    const models = [
      { name: 'T2V', id: 'ep-20250624192345-5ccwj' },
      { name: 'I2V', id: 'ep-20250624195026-qjsmk' },
      { name: 'Pro', id: 'ep-20250624192235-zttm6' }
    ];
    
    for (const model of models) {
      console.log(`\n🎯 测试模型: ${model.name} (${model.id})`);
      
      try {
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: model.id,
            content: [
              {
                type: "text",
                text: "一只可爱的小猫在花园里玩耍 --resolution 720p --duration 5"
              }
            ]
          })
        });
        
        console.log(`   状态码: ${response.status}`);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.log(`   ❌ 错误: ${errorText}`);
        } else {
          const result = await response.json();
          console.log(`   ✅ 成功: ${JSON.stringify(result, null, 2)}`);
        }
        
      } catch (error) {
        console.log(`   ❌ 网络错误: ${error.message}`);
      }
      
      // 等待一秒避免频率限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testDoubaoDirectAPI();
