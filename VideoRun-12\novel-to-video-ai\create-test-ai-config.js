// 创建测试AI配置
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestAIConfig() {
  try {
    console.log('创建测试AI配置...');
    
    // 创建一个测试AI配置
    const aiConfig = await prisma.aIConfig.create({
      data: {
        name: '测试模型',
        provider: 'test',
        model: 'test-model',
        apiKey: 'test-key',
        enabled: true,
        status: 'connected'
      }
    });
    
    console.log('✅ 测试AI配置创建成功:', aiConfig);
    
    // 查询所有AI配置
    const allConfigs = await prisma.aIConfig.findMany();
    console.log('📋 所有AI配置:', allConfigs);
    
  } catch (error) {
    console.error('❌ 创建测试AI配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestAIConfig();
