const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoI2VFinalFix() {
  try {
    console.log('🔧 测试豆包图生视频API最终修复（基于官方文档）...\n');
    
    // 1. 检查I2V模型配置
    const i2vModel = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        model: 'ep-20250624013749-dbrbn'
      }
    });
    
    if (!i2vModel || !i2vModel.enabled || !i2vModel.apiKey) {
      console.log('❌ I2V模型未正确配置');
      return;
    }
    
    console.log('✅ I2V模型配置正确');
    
    // 2. 获取角色参考图像
    const charactersWithImages = await prisma.character.findMany({
      where: {
        generatedImages: { not: null }
      },
      take: 1
    });
    
    if (charactersWithImages.length === 0) {
      console.log('❌ 没有找到有参考图像的角色');
      return;
    }
    
    const character = charactersWithImages[0];
    let referenceImageUrl = null;
    
    try {
      const images = JSON.parse(character.generatedImages);
      referenceImageUrl = images.front || images.side || images.back;
    } catch (e) {
      console.log('❌ 解析角色图像失败');
      return;
    }
    
    if (!referenceImageUrl) {
      console.log('❌ 角色没有可用的参考图像');
      return;
    }
    
    console.log(`✅ 找到角色: ${character.name}`);
    
    // 3. 测试修复后的API格式
    console.log('\n🧪 测试修复后的API格式...');
    
    const testPrompt = '角色在雪夜中缓缓转身，表情严肃警惕，眼神坚定';
    const duration = 5;
    
    // 修复后的正确格式（基于官方文档）
    const content = [
      {
        type: "text",
        text: `${testPrompt} --ratio adaptive --dur ${duration}`
      },
      {
        type: "image_url",
        image_url: {
          url: referenceImageUrl
        }
      }
    ];
    
    console.log('📋 修复后的API请求格式:');
    console.log('   端点:', i2vModel.model);
    console.log('   内容格式:', JSON.stringify(content, null, 2));
    
    // 4. 执行API调用测试
    console.log('\n📡 执行API调用测试...');
    
    try {
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${i2vModel.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: i2vModel.model,
          content: content
        })
      });
      
      console.log(`📊 API响应状态: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ API调用成功!');
        console.log('📋 响应数据:', JSON.stringify(result, null, 2));
        
        const taskId = result.data?.task_id || result.task_id || result.id || result.data?.id;
        if (taskId) {
          console.log(`🎯 任务ID: ${taskId}`);
          console.log('🎉 豆包图生视频API修复成功！');
        }
        
      } else {
        const errorText = await response.text();
        console.log('❌ API调用失败:');
        console.log(`   状态码: ${response.status}`);
        console.log(`   错误信息: ${errorText}`);
      }
      
    } catch (error) {
      console.error('❌ API调用异常:', error.message);
    }
    
    // 5. 格式修复总结
    console.log('\n📊 格式修复总结:');
    
    console.log('🔧 关键修复点:');
    console.log('   1. 添加 --ratio adaptive 参数（图生视频必需）');
    console.log('   2. 添加 --dur 参数指定时长');
    console.log('   3. text 在前，image_url 在后的顺序');
    console.log('   4. 使用正确的端点ID: ep-20250624013749-dbrbn');
    
    console.log('\n📋 最终正确格式:');
    console.log(`   [
     {
       "type": "text",
       "text": "提示词 --ratio adaptive --dur 5"
     },
     {
       "type": "image_url",
       "image_url": { "url": "图像URL" }
     }
   ]`);
   
    console.log('\n💡 重要参数说明:');
    console.log('   --ratio adaptive: 图生视频必需参数');
    console.log('   --dur 5/10: 视频时长（5秒或10秒）');
    console.log('   --resolution 720p: 分辨率（可选）');
    console.log('   --fps 24: 帧率（默认24）');
    console.log('   --wm false: 是否包含水印');
    
    // 6. 使用建议
    console.log('\n🎯 使用建议:');
    console.log('   - 图生视频适合角色特写和情感表达');
    console.log('   - 确保角色参考图像质量高且清晰');
    console.log('   - 提示词要简洁明确，描述具体动作');
    console.log('   - 可以与文生视频模型混合使用');
    
    console.log('\n🎬 豆包图生视频API最终修复测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoI2VFinalFix();
