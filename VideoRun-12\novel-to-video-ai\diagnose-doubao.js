// 详细诊断豆包API问题
async function diagnoseProblem() {
  const apiKey = 'e7fc00da-28b5-4628-9c59-588d559cdf1c';
  
  console.log('🔍 开始详细诊断豆包API问题...\n');
  
  // 1. 测试API密钥基本有效性
  console.log('1️⃣ 测试API密钥基本有效性...');
  try {
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'doubao-lite-4k',
        messages: [{ role: 'user', content: '测试' }],
        max_tokens: 10
      })
    });
    
    console.log(`   状态码: ${response.status}`);
    if (response.ok) {
      console.log('   ✅ API密钥基本有效');
    } else {
      const error = await response.text();
      console.log(`   ❌ API密钥问题: ${error}`);
    }
  } catch (error) {
    console.log(`   ❌ 网络错误: ${error.message}`);
  }
  
  console.log('\n2️⃣ 测试视频生成API权限...');
  
  // 2. 测试每个endpoint的权限
  const endpoints = [
    { name: 'T2V', id: 'ep-20250624192345-5ccwj' },
    { name: 'I2V', id: 'ep-20250624195026-qjsmk' },
    { name: 'Pro', id: 'ep-20250624192235-zttm6' }
  ];
  
  for (const endpoint of endpoints) {
    console.log(`\n   🎯 测试 ${endpoint.name} (${endpoint.id}):`);
    
    try {
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: endpoint.id,
          content: [
            {
              type: "text",
              text: "测试"
            }
          ]
        })
      });
      
      console.log(`      状态码: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log(`      ✅ 成功: ${JSON.stringify(result)}`);
      } else {
        const error = await response.text();
        console.log(`      ❌ 失败: ${error}`);
        
        // 分析具体错误
        if (error.includes('access to the requested resource')) {
          console.log(`      🔍 分析: 该endpoint在您的账户中可能没有权限`);
        } else if (error.includes('does not exist')) {
          console.log(`      🔍 分析: 该endpoint不存在或已失效`);
        } else if (error.includes('API key')) {
          console.log(`      🔍 分析: API密钥问题`);
        }
      }
    } catch (error) {
      console.log(`      ❌ 网络错误: ${error.message}`);
    }
    
    // 避免频率限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n3️⃣ 建议解决方案:');
  console.log('   如果所有endpoint都显示权限错误:');
  console.log('   - 检查火山方舟控制台中这些endpoint是否真的"已开通"');
  console.log('   - 确认API密钥是否有访问这些endpoint的权限');
  console.log('   - 尝试在控制台重新生成API密钥');
  console.log('   - 检查账户是否有足够的配额');
}

diagnoseProblem();
