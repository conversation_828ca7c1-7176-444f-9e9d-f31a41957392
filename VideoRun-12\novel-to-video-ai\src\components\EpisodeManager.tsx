'use client'

import { useState } from 'react'
import { Film, Play, Eye, EyeOff, Clock, CheckCircle, Zap, Search } from 'lucide-react'
import { Episode } from '@/types'
import PlotAnalysisModal from './PlotAnalysisModal'

interface EpisodeManagerProps {
  episodes: Episode[]
  onAnalyzePlot?: (episodeId: string) => void
  onGenerateVideo?: (episodeId: string) => void
  onExtractDetailedPlot?: (episodeId: string) => void
}

export default function EpisodeManager({
  episodes,
  onAnalyzePlot,
  onGenerateVideo,
  onExtractDetailedPlot
}: EpisodeManagerProps) {
  const [expandedEpisode, setExpandedEpisode] = useState<string | null>(null)
  const [analyzingEpisode, setAnalyzingEpisode] = useState<string | null>(null)
  const [plotModalOpen, setPlotModalOpen] = useState(false)
  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null)

  // 切换剧集详情展开/收起
  const toggleEpisodeExpansion = (episodeId: string) => {
    setExpandedEpisode(expandedEpisode === episodeId ? null : episodeId)
  }

  // 分析剧情
  const handleAnalyzePlot = async (episodeId: string) => {
    setAnalyzingEpisode(episodeId)
    try {
      await onAnalyzePlot?.(episodeId)
    } finally {
      setAnalyzingEpisode(null)
    }
  }

  // 打开剧情信息模态框
  const handleViewPlotInfo = (episode: Episode) => {
    setSelectedEpisode(episode)
    setPlotModalOpen(true)
  }

  // 关闭剧情信息模态框
  const handleClosePlotModal = () => {
    setPlotModalOpen(false)
    setSelectedEpisode(null)
  }

  // 获取剧集状态显示
  const getEpisodeStatusDisplay = (episode: Episode) => {
    if (episode.plotInfo) {
      return {
        text: '已分析',
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle
      }
    }
    return {
      text: '未分析',
      color: 'bg-gray-100 text-gray-800',
      icon: Clock
    }
  }

  if (episodes.length === 0) {
    return (
      <div className="text-center py-12">
        <Film className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">暂无剧集信息</h3>
        <p className="mt-1 text-sm text-gray-500">
          上传小说文件后，AI将自动按章节拆分剧集
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 剧集统计 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Film className="text-purple-600 mr-2" size={20} />
            <h3 className="text-lg font-medium text-gray-900">剧集管理</h3>
          </div>
          <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
            共 {episodes.length} 集
          </span>
        </div>
        <p className="mt-1 text-sm text-gray-600">
          AI按章节自动拆分的剧集，可以提取剧情信息用于视频生成
        </p>
      </div>

      {/* 剧集列表 */}
      <div className="space-y-4">
        {episodes.map((episode, index) => {
          const isExpanded = expandedEpisode === episode.id
          const isAnalyzing = analyzingEpisode === episode.id
          const statusDisplay = getEpisodeStatusDisplay(episode)

          return (
            <div key={episode.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              {/* 剧集头部 */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded mr-3">
                      第{index + 1}集
                    </span>
                    <h4 className="text-lg font-medium text-gray-900">{episode.title}</h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                      <statusDisplay.icon className="mr-1" size={12} />
                      {statusDisplay.text}
                    </span>
                    <button
                      onClick={() => toggleEpisodeExpansion(episode.id)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title={isExpanded ? "收起详情" : "展开详情"}
                    >
                      {isExpanded ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                {/* 剧集简介 */}
                <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                  {episode.content.substring(0, 200)}...
                </p>

                {/* 操作按钮 */}
                <div className="mt-3 flex items-center space-x-3">
                  {!episode.plotInfo ? (
                    <button
                      onClick={() => handleAnalyzePlot(episode.id)}
                      disabled={isAnalyzing}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isAnalyzing ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600 mr-1"></div>
                          分析中...
                        </>
                      ) : (
                        <>
                          <Zap className="mr-1" size={12} />
                          提取剧情信息
                        </>
                      )}
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={() => handleViewPlotInfo(episode)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                      >
                        <Eye className="mr-1" size={12} />
                        查看剧情信息
                      </button>
                      <button
                        onClick={() => onGenerateVideo?.(episode.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                      >
                        <Play className="mr-1" size={12} />
                        生成视频脚本
                      </button>
                      <button
                        onClick={() => onExtractDetailedPlot?.(episode.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200"
                      >
                        <Search className="mr-1" size={12} />
                        提取具体剧情信息
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* 剧集详情 */}
              {isExpanded && (
                <div className="p-4 bg-gray-50">
                  {/* 剧集内容 */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">详细剧情</h5>
                    <div className="text-sm text-gray-600 max-h-64 overflow-y-auto bg-white p-3 rounded border">
                      {episode.content}
                    </div>
                  </div>

                  {/* 剧情信息简要显示 */}
                  {episode.plotInfo && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CheckCircle className="text-green-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-green-800">剧情信息已提取</span>
                        </div>
                        <button
                          onClick={() => handleViewPlotInfo(episode)}
                          className="text-xs text-green-700 hover:text-green-900 underline"
                        >
                          查看详情
                        </button>
                      </div>
                      <p className="text-xs text-green-700 mt-1">
                        包含人物、场景、事件三要素等详细分析结果
                      </p>
                    </div>
                  )}

                  {/* 创建时间 */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      创建时间：{new Date(episode.createdAt).toLocaleString('zh-CN')}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* 剧集分析说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">
          剧情分析说明
        </h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p><strong>本集人物：</strong>该集中出现的主要角色</p>
          <p><strong>场景信息：</strong>故事发生的地点和环境描述</p>
          <p><strong>事件三要素：</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li><strong>正常状态：</strong>故事的初始状态或平衡点</li>
            <li><strong>矛盾冲突：</strong>打破平衡的问题或冲突</li>
            <li><strong>升级事件：</strong>推动故事发展的关键事件</li>
          </ul>
        </div>
      </div>

      {/* 剧情信息模态框 */}
      {selectedEpisode && (
        <PlotAnalysisModal
          episodeId={selectedEpisode.id}
          episodeTitle={selectedEpisode.title}
          isOpen={plotModalOpen}
          onClose={handleClosePlotModal}
          plotInfo={selectedEpisode.plotInfo}
        />
      )}
    </div>
  )
}
