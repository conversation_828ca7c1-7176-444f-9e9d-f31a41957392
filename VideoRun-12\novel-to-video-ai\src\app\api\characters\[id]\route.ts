import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 更新角色信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    const {
      name,
      identity,
      personality,
      physique,
      facial,
      hairstyle,
      clothing,
      generatedImages
    } = body

    // 检查角色是否存在
    const existingCharacter = await prisma.character.findUnique({
      where: { id }
    })

    if (!existingCharacter) {
      return NextResponse.json(
        { success: false, error: '角色不存在' },
        { status: 404 }
      )
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date()
    }

    // 只更新提供的字段
    if (name !== undefined) updateData.name = name
    if (identity !== undefined) updateData.identity = identity
    if (personality !== undefined) updateData.personality = personality
    if (physique !== undefined) updateData.physique = physique
    if (facial !== undefined) updateData.facial = facial
    if (hairstyle !== undefined) updateData.hairstyle = hairstyle
    if (clothing !== undefined) updateData.clothing = clothing
    
    // 处理生成的图像数据
    if (generatedImages !== undefined) {
      updateData.generatedImages = generatedImages ? JSON.stringify(generatedImages) : null
    }

    // 更新角色
    const updatedCharacter = await prisma.character.update({
      where: { id },
      data: updateData
    })

    // 解析生成的图像数据
    const responseCharacter = {
      ...updatedCharacter,
      generatedImages: updatedCharacter.generatedImages 
        ? JSON.parse(updatedCharacter.generatedImages) 
        : null
    }

    return NextResponse.json({
      success: true,
      data: responseCharacter,
      message: '角色信息更新成功'
    })
  } catch (error) {
    console.error('更新角色信息失败:', error)
    return NextResponse.json(
      { success: false, error: '更新角色信息失败' },
      { status: 500 }
    )
  }
}

// 获取角色详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const character = await prisma.character.findUnique({
      where: { id },
      include: {
        project: true
      }
    })

    if (!character) {
      return NextResponse.json(
        { success: false, error: '角色不存在' },
        { status: 404 }
      )
    }

    // 解析生成的图像数据
    const responseCharacter = {
      ...character,
      generatedImages: character.generatedImages 
        ? JSON.parse(character.generatedImages) 
        : null
    }

    return NextResponse.json({
      success: true,
      data: responseCharacter
    })
  } catch (error) {
    console.error('获取角色信息失败:', error)
    return NextResponse.json(
      { success: false, error: '获取角色信息失败' },
      { status: 500 }
    )
  }
}
