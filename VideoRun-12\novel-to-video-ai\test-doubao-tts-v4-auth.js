const { PrismaClient } = require('@prisma/client');
const VolcengineAuth = require('./volcengine-auth');

const prisma = new PrismaClient();

// 火山引擎认证信息
const ACCESS_KEY_ID = 'AKLTOTgwMzIxY2VlNDIxNDNiMWFlZjAzOWY1OTU3ZDIwOWE';
const SECRET_ACCESS_KEY = 'WXpBMU9ETmtNamxoTmpZMk5EQTNZV0psWVdZelpqRXlOREkxT1dJM01ETQ==';

// 生成请求ID
function generateReqId() {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

async function testDoubaoTTSWithV4Auth() {
  try {
    console.log('🎵 测试豆包TTS API（V4签名认证）...');
    
    // 创建认证实例
    const auth = new VolcengineAuth(ACCESS_KEY_ID, SECRET_ACCESS_KEY);
    
    // 1. 检查豆包TTS配置
    const ttsConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true,
        enabled: true
      }
    });
    
    if (!ttsConfig) {
      console.error('❌ 未找到豆包TTS配置');
      console.log('请先运行: node setup-doubao-tts.js');
      return;
    }
    
    console.log('✅ 找到豆包TTS配置:', ttsConfig.name);
    
    // 2. 测试豆包TTS API调用
    const testText = '你好，这是豆包语音合成的V4签名认证测试。';
    
    console.log('📡 调用豆包TTS API...');
    console.log('测试文本:', testText);
    
    // 构建豆包TTS请求参数
    const requestBody = {
      app: {
        appid: "doubao_tts_app",
        token: "access_token", 
        cluster: "volcano_tts"
      },
      user: {
        uid: "test_user_v4_auth"
      },
      audio: {
        voice_type: "zh_male_M392_conversation_wvae_bigtts",
        encoding: "mp3",
        speed_ratio: 1.0,
        rate: 24000,
        bitrate: 160
      },
      request: {
        reqid: generateReqId(),
        text: testText,
        operation: "query"
      }
    };
    
    const bodyString = JSON.stringify(requestBody);
    
    console.log('请求参数:', {
      textLength: testText.length,
      voice_type: requestBody.audio.voice_type,
      encoding: requestBody.audio.encoding,
      reqid: requestBody.request.reqid
    });
    
    // 生成V4签名认证头
    console.log('🔐 生成V4签名认证...');
    const headers = auth.generateTTSHeaders(bodyString);
    
    console.log('认证头:', {
      'Content-Type': headers['Content-Type'],
      'Host': headers['Host'],
      'X-Date': headers['X-Date'],
      'Authorization': headers['Authorization'].substring(0, 50) + '...'
    });
    
    // 调用豆包TTS API
    console.log('🔗 API端点: https://openspeech.bytedance.com/api/v1/tts');
    
    const response = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
      method: 'POST',
      headers: headers,
      body: bodyString
    });
    
    console.log('API响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API调用失败: ${response.status}`);
      console.error('错误详情:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.error('错误JSON:', JSON.stringify(errorJson, null, 2));
        
        // 分析错误
        if (errorJson.code === 3001) {
          console.error('💡 可能的原因：');
          console.error('  1. AppID或Token不正确');
          console.error('  2. 服务未开通或配额不足');
          console.error('  3. 签名算法有误');
        }
      } catch (e) {
        console.error('错误文本:', errorText);
      }
      return;
    }
    
    // 处理成功响应
    const result = await response.json();
    console.log('✅ API调用成功');
    console.log('响应结果:', {
      code: result.code,
      message: result.message,
      sequence: result.sequence,
      hasData: !!result.data,
      dataLength: result.data ? result.data.length : 0
    });
    
    if (result.code === 3000) {
      console.log('🎉 语音合成成功！');
      
      if (result.data) {
        // 保存音频文件
        const fs = require('fs');
        const audioBuffer = Buffer.from(result.data, 'base64');
        const audioPath = `doubao_tts_v4_${Date.now()}.mp3`;
        fs.writeFileSync(audioPath, audioBuffer);
        console.log('✅ 音频已保存到:', audioPath);
        console.log('音频大小:', audioBuffer.length, '字节');
        
        if (result.addition && result.addition.duration) {
          const duration = parseFloat(result.addition.duration) / 1000;
          console.log('音频时长:', duration.toFixed(1), '秒');
        }
      }
    } else {
      console.error('❌ 语音合成失败');
      console.error('错误码:', result.code);
      console.error('错误信息:', result.message);
    }
    
    // 3. 测试简化的请求（如果上面失败）
    if (result.code !== 3000) {
      console.log('\n🔄 尝试简化的请求格式...');
      
      const simpleRequestBody = {
        text: testText,
        voice_type: "zh_male_M392_conversation_wvae_bigtts",
        encoding: "mp3",
        speed_ratio: 1.0
      };
      
      const simpleBodyString = JSON.stringify(simpleRequestBody);
      const simpleHeaders = auth.generateTTSHeaders(simpleBodyString);
      
      const simpleResponse = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
        method: 'POST',
        headers: simpleHeaders,
        body: simpleBodyString
      });
      
      console.log('简化请求响应状态:', simpleResponse.status);
      
      if (simpleResponse.ok) {
        const simpleResult = await simpleResponse.json();
        console.log('简化请求结果:', {
          code: simpleResult.code,
          message: simpleResult.message
        });
      } else {
        const simpleError = await simpleResponse.text();
        console.log('简化请求错误:', simpleError);
      }
    }
    
    console.log('\n🎉 豆包TTS V4签名认证测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ENOTFOUND') {
      console.error('🌐 网络连接失败，请检查网络设置');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔗 连接被拒绝，请检查API端点');
    } else {
      console.error('详细错误:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 显示使用说明
function showUsage() {
  console.log('🔧 豆包TTS V4签名认证测试');
  console.log('');
  console.log('使用的认证信息:');
  console.log(`AccessKeyID: ${ACCESS_KEY_ID}`);
  console.log(`SecretAccessKey: ${SECRET_ACCESS_KEY.substring(0, 20)}...`);
  console.log('');
  console.log('认证方式: 火山引擎V4签名算法');
  console.log('API端点: https://openspeech.bytedance.com/api/v1/tts');
  console.log('');
  console.log('注意事项:');
  console.log('1. 使用标准的火山引擎V4签名认证');
  console.log('2. 确保已在火山引擎控制台开通语音合成服务');
  console.log('3. 确保AccessKeyID和SecretAccessKey有相应权限');
  console.log('');
}

async function main() {
  showUsage();
  await testDoubaoTTSWithV4Auth();
}

main();
