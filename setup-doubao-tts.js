const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setupDoubaoTTS() {
  try {
    console.log('🎵 设置豆包TTS配置...');
    
    // 1. 检查是否已有豆包TTS配置
    const existingTTSConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        supportsTTS: true
      }
    });
    
    if (existingTTSConfig) {
      console.log('⚠️ 已有豆包TTS配置存在');
      console.log(`现有配置: ${existingTTSConfig.name} (${existingTTSConfig.model})`);
      
      // 更新现有配置以确保支持TTS
      await prisma.aIConfig.update({
        where: { id: existingTTSConfig.id },
        data: {
          supportsTTS: true,
          enabled: true,
          status: 'connected'
        }
      });
      console.log('✅ 已更新现有配置支持TTS');
      return;
    }

    // 2. 添加豆包TTS配置
    const doubaoTTSConfig = await prisma.aIConfig.create({
      data: {
        provider: 'doubao',
        apiKey: 'e7fc00da-28b5-4628-9c59-588d559cdf1c', // 使用现有的API密钥
        model: 'doubao-tts-v1',
        name: '豆包语音合成 (TTS)',
        description: '豆包大模型语音合成服务，支持多种中文声音',
        enabled: true,
        supportsVideo: false,
        supportsImage: false,
        supportsImageToVideo: false,
        supportsTTS: true, // 关键：标记为支持TTS
        temperature: 0.7,
        maxTokens: 4000,
        topP: 0.9,
        status: 'connected'
      }
    });

    console.log('✅ 已添加豆包TTS配置:', doubaoTTSConfig.name);

    // 3. 创建一些示例角色声音配置
    console.log('🎭 创建示例角色声音配置...');
    
    // 获取项目中的角色
    const characters = await prisma.character.findMany({
      take: 5 // 只处理前5个角色作为示例
    });

    if (characters.length > 0) {
      // 预定义的声音配置
      const voiceConfigs = [
        { voiceId: 'zh-CN-XiaoxiaoNeural', voiceName: '晓晓（女声）', gender: 'female' },
        { voiceId: 'zh-CN-YunxiNeural', voiceName: '云希（男声）', gender: 'male' },
        { voiceId: 'zh-CN-YunyangNeural', voiceName: '云扬（男声）', gender: 'male' },
        { voiceId: 'zh-CN-XiaoyiNeural', voiceName: '晓伊（女声）', gender: 'female' },
        { voiceId: 'zh-CN-YunjianNeural', voiceName: '云健（男声）', gender: 'male' }
      ];

      for (let i = 0; i < Math.min(characters.length, voiceConfigs.length); i++) {
        const character = characters[i];
        const voiceConfig = voiceConfigs[i];

        // 检查是否已有声音配置
        const existingVoice = await prisma.characterVoice.findFirst({
          where: {
            characterId: character.id,
            ttsConfigId: doubaoTTSConfig.id
          }
        });

        if (!existingVoice) {
          await prisma.characterVoice.create({
            data: {
              characterId: character.id,
              ttsConfigId: doubaoTTSConfig.id,
              voiceId: voiceConfig.voiceId,
              voiceName: voiceConfig.voiceName,
              basePitch: 0,
              baseSpeed: 1.0,
              baseVolume: 80,
              emotionMappings: JSON.stringify({
                neutral: { pitchAdjust: 0, speedAdjust: 0, volumeAdjust: 0 },
                happy: { pitchAdjust: 2, speedAdjust: 0.1, volumeAdjust: 5 },
                sad: { pitchAdjust: -2, speedAdjust: -0.1, volumeAdjust: -5 },
                angry: { pitchAdjust: 3, speedAdjust: 0.2, volumeAdjust: 10 },
                excited: { pitchAdjust: 4, speedAdjust: 0.15, volumeAdjust: 8 },
                worried: { pitchAdjust: -1, speedAdjust: -0.05, volumeAdjust: -3 },
                surprised: { pitchAdjust: 5, speedAdjust: 0.1, volumeAdjust: 5 },
                gentle: { pitchAdjust: -1, speedAdjust: -0.1, volumeAdjust: -2 }
              }),
              enabled: true
            }
          });

          console.log(`✅ 为角色 "${character.name}" 配置声音: ${voiceConfig.voiceName}`);
        }
      }
    } else {
      console.log('⚠️ 未找到角色，跳过声音配置创建');
    }

    console.log('\n🎉 豆包TTS配置完成！');
    console.log('现在可以使用以下功能：');
    console.log('- 调用 /api/ai/generate-tts 生成语音');
    console.log('- 调用 GET /api/ai/generate-tts 获取可用声音列表');
    console.log('- 为角色配置专属声音');
    
  } catch (error) {
    console.error('❌ 豆包TTS配置失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupDoubaoTTS();
