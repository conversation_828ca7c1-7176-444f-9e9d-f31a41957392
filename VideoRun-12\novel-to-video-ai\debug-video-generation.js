// 调试视频生成问题
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugVideoGeneration() {
  try {
    console.log('🔍 开始调试视频生成问题...\n');

    // 1. 检查AI配置
    console.log('1. 检查AI配置...');
    const aiConfigs = await prisma.aIConfig.findMany({
      where: {
        enabled: true
      }
    });

    console.log(`找到 ${aiConfigs.length} 个启用的AI配置:`);
    aiConfigs.forEach(config => {
      console.log(`- ${config.name} (${config.provider})`);
      console.log(`  状态: ${config.status}`);
      console.log(`  支持视频: ${config.supportsVideo}`);
      console.log(`  API密钥: ${config.apiKey ? '已配置' : '未配置'}`);
    });

    // 2. 检查视频模型
    console.log('\n2. 检查视频模型...');
    const videoModels = aiConfigs.filter(config => config.supportsVideo);
    console.log(`找到 ${videoModels.length} 个视频模型:`);
    videoModels.forEach(model => {
      console.log(`- ${model.name} (${model.provider})`);
    });

    // 3. 测试SkyReels连接
    console.log('\n3. 测试SkyReels连接...');
    const skyreelsModel = aiConfigs.find(config => config.provider === 'skyreels');
    if (skyreelsModel) {
      try {
        const response = await fetch('http://localhost:8000/health');
        const data = await response.json();
        console.log('SkyReels服务器状态:', data);
        
        // 测试生成接口
        console.log('测试SkyReels生成接口...');
        const generateResponse = await fetch('http://localhost:8000/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: '测试视频生成',
            num_frames: 97,
            guidance_scale: 6.0,
            fps: 24,
            resolution: '540P'
          })
        });
        
        if (generateResponse.ok) {
          const generateData = await generateResponse.json();
          console.log('✅ SkyReels生成接口响应:', generateData);
        } else {
          console.log('❌ SkyReels生成接口失败:', generateResponse.status);
        }
      } catch (error) {
        console.log('❌ SkyReels连接失败:', error.message);
      }
    } else {
      console.log('❌ 未找到SkyReels配置');
    }

    // 4. 测试豆包连接
    console.log('\n4. 测试豆包连接...');
    const doubaoModel = aiConfigs.find(config => 
      config.provider === 'doubao' && config.model === 'ep-20250624013223-bwdtj'
    );
    
    if (doubaoModel) {
      try {
        console.log('测试豆包API连接...');
        const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${doubaoModel.apiKey}`
          },
          body: JSON.stringify({
            model: doubaoModel.model,
            content: [
              {
                type: "text",
                text: "测试连接 --ratio 16:9 --fps 24 --dur 5 --resolution 480p"
              }
            ]
          })
        });

        console.log('豆包API响应状态:', response.status);
        if (!response.ok) {
          const errorText = await response.text();
          console.log('豆包API错误:', errorText);
        } else {
          const data = await response.json();
          console.log('✅ 豆包API响应:', data);
        }
      } catch (error) {
        console.log('❌ 豆包连接失败:', error.message);
      }
    } else {
      console.log('❌ 未找到豆包T2V配置');
    }

    // 5. 检查最近的视频片段
    console.log('\n5. 检查最近的视频片段...');
    const recentSegments = await prisma.videoSegment.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`找到 ${recentSegments.length} 个最近的视频片段:`);
    recentSegments.forEach(segment => {
      console.log(`- 片段 ${segment.segmentIndex}: ${segment.status}`);
      console.log(`  视频URL: ${segment.videoUrl || '未生成'}`);
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`  提供商: ${metadata.provider || '未知'}`);
          console.log(`  错误: ${metadata.error || '无'}`);
        } catch (e) {
          console.log(`  元数据解析失败`);
        }
      }
      console.log('');
    });

    // 6. 检查错误日志
    console.log('6. 检查失败的片段...');
    const failedSegments = await prisma.videoSegment.findMany({
      where: {
        status: 'failed'
      },
      take: 3,
      orderBy: {
        updatedAt: 'desc'
      }
    });

    console.log(`找到 ${failedSegments.length} 个失败的片段:`);
    failedSegments.forEach(segment => {
      console.log(`- 片段 ${segment.segmentIndex}:`);
      if (segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`  错误: ${metadata.error || '未知错误'}`);
          console.log(`  提供商: ${metadata.provider || '未知'}`);
        } catch (e) {
          console.log(`  元数据: ${segment.metadata}`);
        }
      }
    });

    console.log('\n✅ 调试完成!');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行调试
debugVideoGeneration();
