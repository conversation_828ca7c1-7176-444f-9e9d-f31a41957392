/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/models/page";
exports.ids = ["app/models/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmodels%2Fpage&page=%2Fmodels%2Fpage&appPaths=%2Fmodels%2Fpage&pagePath=private-next-app-dir%2Fmodels%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmodels%2Fpage&page=%2Fmodels%2Fpage&appPaths=%2Fmodels%2Fpage&pagePath=private-next-app-dir%2Fmodels%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/models/page.tsx */ \"(rsc)/./src/app/models/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'models',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/models/page\",\n        pathname: \"/models\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmodels%2Fpage&page=%2Fmodels%2Fpage&appPaths=%2Fmodels%2Fpage&pagePath=private-next-app-dir%2Fmodels%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/models/page.tsx */ \"(rsc)/./src/app/models/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlNUMlNUNWaWRlb1J1bi0xMiU1QyU1Q1ZpZGVvUnVuLTEyJTVDJTVDbm92ZWwtdG8tdmlkZW8tYWklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtb2RlbHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW9IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFzpobnnm65cXFxcVmlkZW9SdW4tMTJcXFxcVmlkZW9SdW4tMTJcXFxcbm92ZWwtdG8tdmlkZW8tYWlcXFxcc3JjXFxcXGFwcFxcXFxtb2RlbHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"034b89926f62\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwMzRiODk5MjZmNjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n// import { Geist, Geist_Mono } from \"next/font/google\";\n\n// const geistSans = Geist({\n//   variable: \"--font-geist-sans\",\n//   subsets: [\"latin\"],\n// });\n// const geistMono = Geist_Mono({\n//   variable: \"--font-geist-mono\",\n//   subsets: [\"latin\"],\n// });\nconst metadata = {\n    title: \"灵犀Ai——小说转视频神器\",\n    description: \"基于DeepSeek大模型的智能视频内容创作平台，将小说自动转换为结构化的视频脚本，提取角色信息、拆分剧集、生成多维度视频内容。\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ0Esd0RBQXdEO0FBQ2pDO0FBRXZCLDRCQUE0QjtBQUM1QixtQ0FBbUM7QUFDbkMsd0JBQXdCO0FBQ3hCLE1BQU07QUFFTixpQ0FBaUM7QUFDakMsbUNBQW1DO0FBQ25DLHdCQUF3QjtBQUN4QixNQUFNO0FBRUMsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVTtzQkFFVEo7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFzpobnnm65cXFZpZGVvUnVuLTEyXFxWaWRlb1J1bi0xMlxcbm92ZWwtdG8tdmlkZW8tYWlcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuLy8gaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG4vLyBjb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4vLyAgIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4vLyAgIHN1YnNldHM6IFtcImxhdGluXCJdLFxuLy8gfSk7XG5cbi8vIGNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuLy8gICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuLy8gICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbi8vIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLngbXnioBBaeKAlOKAlOWwj+ivtOi9rOinhumikeelnuWZqFwiLFxuICBkZXNjcmlwdGlvbjogXCLln7rkuo5EZWVwU2Vla+Wkp+aooeWei+eahOaZuuiDveinhumikeWGheWuueWIm+S9nOW5s+WPsO+8jOWwhuWwj+ivtOiHquWKqOi9rOaNouS4uue7k+aehOWMlueahOinhumikeiEmuacrO+8jOaPkOWPluinkuiJsuS/oeaBr+OAgeaLhuWIhuWJp+mbhuOAgeeUn+aIkOWkmue7tOW6puinhumikeWGheWuueOAglwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT1cImFudGlhbGlhc2VkXCJcbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/models/page.tsx":
/*!*********************************!*\
  !*** ./src/app/models/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\项目\\VideoRun-12\\VideoRun-12\\novel-to-video-ai\\src\\app\\models\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/models/page.tsx */ \"(ssr)/./src/app/models/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTklQTElQjklRTclOUIlQUUlNUMlNUNWaWRlb1J1bi0xMiU1QyU1Q1ZpZGVvUnVuLTEyJTVDJTVDbm92ZWwtdG8tdmlkZW8tYWklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtb2RlbHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW9IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFzpobnnm65cXFxcVmlkZW9SdW4tMTJcXFxcVmlkZW9SdW4tMTJcXFxcbm92ZWwtdG8tdmlkZW8tYWlcXFxcc3JjXFxcXGFwcFxcXFxtb2RlbHNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E9%A1%B9%E7%9B%AE%5C%5CVideoRun-12%5C%5CVideoRun-12%5C%5Cnovel-to-video-ai%5C%5Csrc%5C%5Capp%5C%5Cmodels%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/models/page.tsx":
/*!*********************************!*\
  !*** ./src/app/models/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"(ssr)/./src/components/Layout.tsx\");\n/* harmony import */ var _components_AIConfigPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AIConfigPanel */ \"(ssr)/./src/components/AIConfigPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModelsPage() {\n    const [savedConfig, setSavedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 处理配置保存\n    const handleConfigSaved = (config)=>{\n        setSavedConfig(config);\n        setShowSuccess(true);\n        // 3秒后隐藏成功提示\n        setTimeout(()=>{\n            setShowSuccess(false);\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"text-purple-600 mr-3\",\n                                size: 32\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"模型配置\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-600\",\n                                        children: \"配置AI大模型，用于小说分析和视频脚本生成\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                showSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-md bg-green-50 p-4 border border-green-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5 text-green-400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-green-800\",\n                                        children: \"配置保存成功！\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm text-green-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"AI模型配置已保存，现在可以开始创建项目并分析小说了。\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"大模型配置\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-gray-600\",\n                                    children: \"选择并配置您要使用的AI模型\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIConfigPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onConfigSaved: handleConfigSaved\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 text-blue-400 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-blue-800 mb-2\",\n                                        children: \"配置说明\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"DeepSeek模型选择：\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside ml-4 space-y-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"DeepSeek Reasoner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" - 专业推理模型，适合小说分析、角色提取和剧情分析\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"豆包模型选择：\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside ml-4 space-y-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"豆包 Seedance 1.0 Pro\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" - 专业视频生成模型，支持高质量文生视频\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"参数调整：\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside ml-4 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"温度(Temperature)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - 控制输出的随机性，0.7为推荐值\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"最大Token数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - 控制单次输出长度，4000为推荐值\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Top P\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                                lineNumber: 94,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" - 控制输出的多样性，0.9为推荐值\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"使用流程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"配置AI模型\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"选择模型提供商，输入API密钥，测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"创建项目\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"在项目页面创建新项目，上传小说文件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"AI分析\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"AI自动分析小说，提取角色和剧集信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full bg-purple-100 text-purple-600 text-sm font-medium\",\n                                                    children: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: \"生成视频脚本\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"基于多维度信息生成专业视频脚本\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                savedConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"当前配置\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"提供商\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: savedConfig.provider\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"模型\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: savedConfig.model\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"温度\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: savedConfig.temperature\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"最大Token数\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: savedConfig.maxTokens\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"状态\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${savedConfig.status === 'connected' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n                                                    children: savedConfig.status === 'connected' ? '已连接' : '未测试'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500\",\n                                                children: \"最后更新\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-sm text-gray-900\",\n                                                children: new Date(savedConfig.updatedAt).toLocaleString('zh-CN')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\app\\\\models\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/models/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AIConfigPanel.tsx":
/*!******************************************!*\
  !*** ./src/components/AIConfigPanel.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIConfigPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Switch!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/switch/switch.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Loader,Settings,Sliders,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sliders-vertical.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AIConfigPanel({ onConfigSaved }) {\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showParameterSettings, setShowParameterSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 预定义的模型配置\n    const predefinedModels = [\n        // DeepSeek 系列\n        {\n            id: 'deepseek-reasoner',\n            provider: 'deepseek',\n            model: 'deepseek-reasoner',\n            name: 'DeepSeek Reasoner',\n            description: '专业推理模型，适合复杂逻辑分析',\n            apiKey: '',\n            enabled: false\n        },\n        // 豆包系列 - 使用正确的端点信息\n        {\n            id: 'doubao-seedream-3-0-t2i-latest',\n            provider: 'doubao',\n            model: 'ep-20250626132353-nlrtf',\n            name: 'Doubao-Seedream-3.0-t2i',\n            description: '豆包文本到图像生成模型 - 最新版本 (2025-06-26)',\n            apiKey: '',\n            enabled: false,\n            supportsVideo: false,\n            supportsImage: true,\n            supportsImageToVideo: false,\n            supportsTTS: false\n        },\n        {\n            id: 'doubao-seedance-1-0-pro',\n            provider: 'doubao',\n            model: 'ep-20250624192235-zttm6',\n            name: 'Doubao-Seedance-1.0-pro',\n            description: '豆包专业版视频生成模型',\n            apiKey: '',\n            enabled: false,\n            supportsVideo: true,\n            supportsImage: false,\n            supportsImageToVideo: false,\n            supportsTTS: false\n        },\n        {\n            id: 'doubao-seedance-1-0-lite-i2v',\n            provider: 'doubao',\n            model: 'ep-20250624195026-qjsmk',\n            name: 'Doubao-Seedance-1.0-lite-i2v',\n            description: '豆包图片到视频转换模型',\n            apiKey: '',\n            enabled: false,\n            supportsVideo: true,\n            supportsImage: false,\n            supportsImageToVideo: true,\n            supportsTTS: false\n        },\n        {\n            id: 'doubao-seedance-1-0-lite-t2v',\n            provider: 'doubao',\n            model: 'ep-20250624192345-5ccwj',\n            name: 'Doubao-Seedance-1.0-lite-t2v',\n            description: '豆包文本到视频生成模型',\n            apiKey: '',\n            enabled: false,\n            supportsVideo: true,\n            supportsImage: false,\n            supportsImageToVideo: false,\n            supportsTTS: false\n        },\n        // SkyReels-V2 本地模型\n        {\n            id: 'skyreels-v2-df-1-3b-540p',\n            provider: 'skyreels',\n            model: 'SkyReels-V2-DF-1.3B-540P',\n            name: 'SkyReels-V2 本地模型',\n            description: '本地部署的SkyReels-V2文生视频模型，支持无限长度视频生成，540P分辨率',\n            apiKey: 'http://localhost:8000',\n            enabled: false,\n            supportsVideo: true,\n            supportsImageToVideo: false\n        }\n    ];\n    // 加载模型配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIConfigPanel.useEffect\": ()=>{\n            loadModels();\n        }\n    }[\"AIConfigPanel.useEffect\"], []);\n    const loadModels = async ()=>{\n        try {\n            const response = await fetch('/api/models');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    // 合并预定义模型和已保存的配置\n                    const savedModels = data.data || [];\n                    const mergedModels = predefinedModels.map((predefined)=>{\n                        // 通过 provider + model 匹配，而不是通过ID\n                        const saved = savedModels.find((s)=>s.provider === predefined.provider && s.model === predefined.model);\n                        return saved ? {\n                            ...predefined,\n                            ...saved,\n                            // 保持预定义的描述和名称，但使用数据库的ID和状态\n                            name: predefined.name,\n                            description: predefined.description\n                        } : predefined;\n                    });\n                    setModels(mergedModels);\n                } else {\n                    setModels(predefinedModels);\n                }\n            } else {\n                setModels(predefinedModels);\n            }\n        } catch (error) {\n            console.error('加载模型配置失败:', error);\n            setModels(predefinedModels);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 更新模型配置\n    const updateModel = (id, updates)=>{\n        setModels((prev)=>prev.map((model)=>{\n                if (model.id === id) {\n                    const updated = {\n                        ...model,\n                        ...updates\n                    };\n                    // 如果更新包含新的ID，需要特殊处理\n                    if (updates.id && updates.id !== id) {\n                        // ID发生了变化，这通常发生在保存新模型时\n                        return updated;\n                    }\n                    return updated;\n                }\n                return model;\n            }));\n    };\n    // 保存模型配置\n    const saveModel = async (model)=>{\n        try {\n            // 检查是否为新模型\n            const isNewModel = !model.id.startsWith('cmc');\n            const response = await fetch('/api/models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...model,\n                    id: isNewModel ? undefined : model.id // 新模型不传ID\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                if (isNewModel) {\n                    // 新模型：更新ID并设置状态\n                    const oldId = model.id;\n                    const dbId = data.data.id;\n                    setModels((prev)=>prev.map((m)=>m.id === oldId ? {\n                                ...m,\n                                id: dbId,\n                                status: 'connected'\n                            } : m));\n                } else {\n                    // 现有模型：只更新状态\n                    updateModel(model.id, {\n                        status: 'connected'\n                    });\n                }\n                onConfigSaved?.();\n            } else {\n                updateModel(model.id, {\n                    status: 'error'\n                });\n                setError(data.error || '保存配置失败');\n            }\n        } catch (error) {\n            updateModel(model.id, {\n                status: 'error'\n            });\n            setError('保存配置失败，请重试');\n        }\n    };\n    // 测试模型连接\n    const testModel = async (model)=>{\n        if (!model.apiKey) {\n            setError('请先输入API密钥');\n            return;\n        }\n        updateModel(model.id, {\n            status: 'testing'\n        });\n        setError(null);\n        try {\n            // 检查模型是否已保存到数据库（通过ID格式判断）\n            const isNewModel = !model.id.startsWith('cmc');\n            if (isNewModel) {\n                // 这是新模型，需要先保存\n                const saveResponse = await fetch('/api/models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        ...model,\n                        id: undefined // 不传ID让数据库生成\n                    })\n                });\n                const saveData = await saveResponse.json();\n                if (!saveData.success) {\n                    updateModel(model.id, {\n                        status: 'error'\n                    });\n                    setError(saveData.error || '保存配置失败');\n                    return;\n                }\n                // 更新本地模型，使用数据库返回的ID\n                const dbId = saveData.data.id;\n                const oldId = model.id;\n                setModels((prev)=>prev.map((m)=>m.id === oldId ? {\n                            ...m,\n                            id: dbId\n                        } : m));\n                model = {\n                    ...model,\n                    id: dbId\n                };\n            }\n            const response = await fetch('/api/models/test', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(model)\n            });\n            const data = await response.json();\n            if (data.success) {\n                updateModel(model.id, {\n                    status: 'connected'\n                });\n            } else {\n                updateModel(model.id, {\n                    status: 'error'\n                });\n                setError(data.error || '连接测试失败');\n            }\n        } catch (error) {\n            updateModel(model.id, {\n                status: 'error'\n            });\n            setError('连接测试失败，请检查网络');\n        }\n    };\n    // 按提供商分组模型\n    const groupedModels = models.reduce((groups, model)=>{\n        const provider = model.provider;\n        if (!groups[provider]) {\n            groups[provider] = [];\n        }\n        groups[provider].push(model);\n        return groups;\n    }, {});\n    // 获取提供商显示名称\n    const getProviderName = (provider)=>{\n        switch(provider){\n            case 'deepseek':\n                return 'DeepSeek';\n            case 'doubao':\n                return '豆包 (火山引擎)';\n            case 'skyreels':\n                return 'SkyReels-V2 (本地模型)';\n            default:\n                return provider;\n        }\n    };\n    // 获取状态图标\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"text-green-500\",\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 32\n                }, this);\n            case 'testing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"text-blue-500 animate-spin\",\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 30\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"text-red-500\",\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 28\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"text-gray-400\",\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"animate-spin mr-2\",\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"加载模型配置中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"mr-2\",\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    \"大模型配置\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: Object.entries(groupedModels).map(([provider, providerModels])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium mb-4 text-gray-900\",\n                                children: getProviderName(provider)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: providerModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border border-gray-100 rounded-lg p-4 bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                getStatusIcon(model.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: model.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowParameterSettings(showParameterSettings === model.id ? null : model.id),\n                                                                    className: \"p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors\",\n                                                                    title: \"API参数设置\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        size: 16\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Switch_headlessui_react__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                            checked: model.enabled,\n                                                            onChange: (enabled)=>{\n                                                                updateModel(model.id, {\n                                                                    enabled\n                                                                });\n                                                                if (enabled && model.apiKey) {\n                                                                    saveModel({\n                                                                        ...model,\n                                                                        enabled\n                                                                    });\n                                                                }\n                                                            },\n                                                            className: `${model.enabled ? 'bg-purple-600' : 'bg-gray-200'} relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `${model.enabled ? 'translate-x-6' : 'translate-x-1'} inline-block h-4 w-4 transform rounded-full bg-white transition-transform`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-3\",\n                                                children: model.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: model.provider === 'skyreels' ? 'text' : 'password',\n                                                        value: model.apiKey,\n                                                        onChange: (e)=>updateModel(model.id, {\n                                                                apiKey: e.target.value\n                                                            }),\n                                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm\",\n                                                        placeholder: model.provider === 'skyreels' ? '输入API服务器地址 (如: http://localhost:8000)' : '输入API密钥...'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>testModel(model),\n                                                        disabled: !model.apiKey || model.status === 'testing',\n                                                        className: \"px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        title: \"测试API连接\",\n                                                        children: model.status === 'testing' ? '测试中' : '测试'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>saveModel(model),\n                                                        disabled: !model.apiKey,\n                                                        className: \"px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        title: \"保存模型配置\",\n                                                        children: \"保存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this),\n                                            showParameterSettings === model.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 bg-white border border-gray-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-md font-medium text-gray-900 mb-4 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Loader_Settings_Sliders_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"mr-2\",\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"API调用参数设置\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Temperature (创造性)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"2\",\n                                                                        step: \"0.1\",\n                                                                        value: model.temperature || 0.7,\n                                                                        onChange: (e)=>updateModel(model.id, {\n                                                                                temperature: parseFloat(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"0 (保守)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: model.temperature || 0.7\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"2 (创新)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Max Tokens (最大输出)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        min: \"100\",\n                                                                        max: \"8000\",\n                                                                        step: \"100\",\n                                                                        value: model.maxTokens || 4000,\n                                                                        onChange: (e)=>updateModel(model.id, {\n                                                                                maxTokens: parseInt(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"当前: \",\n                                                                            model.maxTokens || 4000,\n                                                                            \" tokens\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Top P (核心采样)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0.1\",\n                                                                        max: \"1\",\n                                                                        step: \"0.05\",\n                                                                        value: model.topP || 0.9,\n                                                                        onChange: (e)=>updateModel(model.id, {\n                                                                                topP: parseFloat(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"0.1 (聚焦)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: model.topP || 0.9\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"1.0 (多样)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 465,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                                children: \"参数说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-blue-800 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Temperature:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 474,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" 控制输出的随机性，值越高越有创造性\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Max Tokens:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" 限制模型单次输出的最大长度\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Top P:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \" 控制词汇选择范围，值越小输出越聚焦\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 flex justify-end\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                saveModel(model);\n                                                                setShowParameterSettings(null);\n                                                            },\n                                                            className: \"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm\",\n                                                            title: \"保存API参数设置\",\n                                                            children: \"保存参数设置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, model.id, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, provider, true, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\AIConfigPanel.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIConfigPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FolderOpen,Home,Menu,Settings,Sparkles,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Layout({ children }) {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: '首页',\n            href: '/',\n            icon: _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            current: pathname === '/'\n        },\n        {\n            name: '项目',\n            href: '/projects',\n            icon: _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            current: pathname.startsWith('/projects')\n        },\n        {\n            name: '模型配置',\n            href: '/models',\n            icon: _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            current: pathname === '/models'\n        },\n        {\n            name: '账户',\n            href: '/account',\n            icon: _barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            current: pathname === '/account',\n            disabled: true\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"text-purple-600 mr-2\",\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-gray-900 whitespace-nowrap\",\n                                                children: \"灵犀Ai——小说转视频神器\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex md:items-center md:space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: `\n                    inline-flex items-center text-lg font-semibold\n                    ${item.current ? 'text-purple-600' : 'text-gray-900 hover:text-purple-600'}\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"mr-2\",\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                        className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FolderOpen_Home_Menu_Settings_Sparkles_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `\n                    flex items-center px-3 py-2 text-sm font-medium border-l-4\n                    ${item.current ? 'bg-purple-50 border-purple-500 text-purple-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-800'}\n                    ${item.disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : ''}\n                  `,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-2\",\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\项目\\\\VideoRun-12\\\\VideoRun-12\\\\novel-to-video-ai\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUNKO0FBQ2lCO0FBU3hCO0FBTU4sU0FBU1MsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNYSxXQUFXWCw0REFBV0E7SUFFNUIsTUFBTVksYUFBYTtRQUNqQjtZQUNFQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTWQsdUhBQUlBO1lBQ1ZlLFNBQVNMLGFBQWE7UUFDeEI7UUFDQTtZQUNFRSxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTWIsdUhBQVVBO1lBQ2hCYyxTQUFTTCxTQUFTTSxVQUFVLENBQUM7UUFDL0I7UUFDQTtZQUNFSixNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTVosdUhBQVFBO1lBQ2RhLFNBQVNMLGFBQWE7UUFDeEI7UUFDQTtZQUNFRSxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTVgsdUhBQUlBO1lBQ1ZZLFNBQVNMLGFBQWE7WUFDdEJPLFVBQVU7UUFDWjtLQUNEO0lBRUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDckIsa0RBQUlBO3dDQUFDZSxNQUFLO3dDQUFJTSxXQUFVOzswREFDdkIsOERBQUNkLHVIQUFRQTtnREFBQ2MsV0FBVTtnREFBdUJFLE1BQU07Ozs7OzswREFDakQsOERBQUNDO2dEQUFLSCxXQUFVOzBEQUF3RDs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTzVFLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWlIsV0FBV1ksR0FBRyxDQUFDLENBQUNDLHFCQUNmLDhEQUFDMUIsa0RBQUlBOzRDQUVIZSxNQUFNVyxLQUFLWCxJQUFJOzRDQUNmTSxXQUFXLENBQUM7O29CQUVWLEVBQUVLLEtBQUtULE9BQU8sR0FDVixvQkFDQSxzQ0FDSDtvQkFDRCxFQUFFUyxLQUFLUCxRQUFRLEdBQUcsc0RBQXNELEdBQUc7a0JBQzdFLENBQUM7OzhEQUVELDhEQUFDTyxLQUFLVixJQUFJO29EQUFDSyxXQUFVO29EQUFPRSxNQUFNOzs7Ozs7Z0RBQ2pDRyxLQUFLWixJQUFJOzsyQ0FaTFksS0FBS1osSUFBSTs7Ozs7Ozs7Ozs4Q0FrQnBCLDhEQUFDTTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ007d0NBQ0NDLFNBQVMsSUFBTWpCLGtCQUFrQixDQUFDRDt3Q0FDbENXLFdBQVU7a0RBRVYsNEVBQUNmLHVIQUFJQTs0Q0FBQ2lCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPbkJiLGdDQUNDLDhEQUFDVTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pSLFdBQVdZLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZiw4REFBQzFCLGtEQUFJQTtvQ0FFSGUsTUFBTVcsS0FBS1gsSUFBSTtvQ0FDZk0sV0FBVyxDQUFDOztvQkFFVixFQUFFSyxLQUFLVCxPQUFPLEdBQ1YsbURBQ0Esd0VBQ0g7b0JBQ0QsRUFBRVMsS0FBS1AsUUFBUSxHQUFHLHNEQUFzRCxHQUFHO2tCQUM3RSxDQUFDO29DQUNEUyxTQUFTLElBQU1qQixrQkFBa0I7O3NEQUVqQyw4REFBQ2UsS0FBS1YsSUFBSTs0Q0FBQ0ssV0FBVTs0Q0FBT0UsTUFBTTs7Ozs7O3dDQUNqQ0csS0FBS1osSUFBSTs7bUNBYkxZLEtBQUtaLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFzQjFCLDhEQUFDZTtnQkFBS1IsV0FBVTswQkFDZCw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1paOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJEOlxc6aG555uuXFxWaWRlb1J1bi0xMlxcVmlkZW9SdW4tMTJcXG5vdmVsLXRvLXZpZGVvLWFpXFxzcmNcXGNvbXBvbmVudHNcXExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7XG4gIEhvbWUsXG4gIEZvbGRlck9wZW4sXG4gIFNldHRpbmdzLFxuICBVc2VyLFxuICBNZW51LFxuICBYLFxuICBTcGFya2xlc1xufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfTogTGF5b3V0UHJvcHMpIHtcbiAgY29uc3QgW21vYmlsZU1lbnVPcGVuLCBzZXRNb2JpbGVNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpXG5cbiAgY29uc3QgbmF2aWdhdGlvbiA9IFtcbiAgICB7XG4gICAgICBuYW1lOiAn6aaW6aG1JyxcbiAgICAgIGhyZWY6ICcvJyxcbiAgICAgIGljb246IEhvbWUsXG4gICAgICBjdXJyZW50OiBwYXRobmFtZSA9PT0gJy8nXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn6aG555uuJyxcbiAgICAgIGhyZWY6ICcvcHJvamVjdHMnLFxuICAgICAgaWNvbjogRm9sZGVyT3BlbixcbiAgICAgIGN1cnJlbnQ6IHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9wcm9qZWN0cycpXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn5qih5Z6L6YWN572uJyxcbiAgICAgIGhyZWY6ICcvbW9kZWxzJyxcbiAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgY3VycmVudDogcGF0aG5hbWUgPT09ICcvbW9kZWxzJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+i0puaItycsXG4gICAgICBocmVmOiAnL2FjY291bnQnLFxuICAgICAgaWNvbjogVXNlcixcbiAgICAgIGN1cnJlbnQ6IHBhdGhuYW1lID09PSAnL2FjY291bnQnLFxuICAgICAgZGlzYWJsZWQ6IHRydWVcbiAgICB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIHsvKiDpobbpg6jlr7zoiKrmoI8gKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTZcIj5cbiAgICAgICAgICAgIHsvKiDlt6bkvqcgTG9nbyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInRleHQtcHVycGxlLTYwMCBtci0yXCIgc2l6ZT17MjR9IC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgIOeBteeKgEFp4oCU4oCU5bCP6K+06L2s6KeG6aKR56We5ZmoXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOWPs+S+p+WvvOiIquiPnOWNlSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggbWQ6aXRlbXMtY2VudGVyIG1kOnNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHRleHQtbGcgZm9udC1zZW1pYm9sZFxuICAgICAgICAgICAgICAgICAgICAke2l0ZW0uY3VycmVudFxuICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtcHVycGxlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktOTAwIGhvdmVyOnRleHQtcHVycGxlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAke2l0ZW0uZGlzYWJsZWQgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQgcG9pbnRlci1ldmVudHMtbm9uZScgOiAnJ31cbiAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvbiBjbGFzc05hbWU9XCJtci0yXCIgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOenu+WKqOerr+iPnOWNleaMiemSriAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6aGlkZGVuIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbighbW9iaWxlTWVudU9wZW4pfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTIgcm91bmRlZC1tZCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS01MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPE1lbnUgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDnp7vliqjnq6/oj5zljZUgKi99XG4gICAgICAgIHttb2JpbGVNZW51T3BlbiAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMiBwYi0zIHNwYWNlLXktMSBiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAge25hdmlnYXRpb24ubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgIGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIGJvcmRlci1sLTRcbiAgICAgICAgICAgICAgICAgICAgJHtpdGVtLmN1cnJlbnRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1wdXJwbGUtNTAgYm9yZGVyLXB1cnBsZS01MDAgdGV4dC1wdXJwbGUtNzAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAke2l0ZW0uZGlzYWJsZWQgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQgcG9pbnRlci1ldmVudHMtbm9uZScgOiAnJ31cbiAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvbiBjbGFzc05hbWU9XCJtci0yXCIgc2l6ZT17MTZ9IC8+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvbmF2PlxuXG4gICAgICB7Lyog5Li75YaF5a655Yy65Z+fICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwicHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsInVzZVBhdGhuYW1lIiwiSG9tZSIsIkZvbGRlck9wZW4iLCJTZXR0aW5ncyIsIlVzZXIiLCJNZW51IiwiU3BhcmtsZXMiLCJMYXlvdXQiLCJjaGlsZHJlbiIsIm1vYmlsZU1lbnVPcGVuIiwic2V0TW9iaWxlTWVudU9wZW4iLCJwYXRobmFtZSIsIm5hdmlnYXRpb24iLCJuYW1lIiwiaHJlZiIsImljb24iLCJjdXJyZW50Iiwic3RhcnRzV2l0aCIsImRpc2FibGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwibmF2Iiwic2l6ZSIsInNwYW4iLCJtYXAiLCJpdGVtIiwiYnV0dG9uIiwib25DbGljayIsIm1haW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/@headlessui","vendor-chunks/@react-aria","vendor-chunks/@react-stately"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmodels%2Fpage&page=%2Fmodels%2Fpage&appPaths=%2Fmodels%2Fpage&pagePath=private-next-app-dir%2Fmodels%2Fpage.tsx&appDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E9%A1%B9%E7%9B%AE%5CVideoRun-12%5CVideoRun-12%5Cnovel-to-video-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();