const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDoubaoResponseFormat() {
  try {
    console.log('🔍 测试豆包API响应格式，查找时长字段...\n');
    
    // 获取豆包配置
    const doubaoConfig = await prisma.aIConfig.findFirst({
      where: {
        provider: 'doubao',
        enabled: true,
        supportsVideo: true
      }
    });
    
    if (!doubaoConfig) {
      console.log('❌ 未找到豆包配置');
      return;
    }
    
    // 获取一个最近完成的任务ID
    const recentSegment = await prisma.videoSegment.findFirst({
      where: {
        status: 'completed',
        metadata: { contains: 'doubao' }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    if (!recentSegment || !recentSegment.metadata) {
      console.log('❌ 未找到最近的豆包任务');
      return;
    }
    
    const metadata = JSON.parse(recentSegment.metadata);
    const taskId = metadata.taskId;
    
    if (!taskId) {
      console.log('❌ 未找到任务ID');
      return;
    }
    
    console.log(`📋 使用任务ID: ${taskId}`);
    console.log(`📋 片段预设时长: ${recentSegment.duration}秒`);
    
    // 查询豆包任务状态，获取完整响应
    console.log('📡 查询豆包任务状态...');
    
    const response = await fetch(`https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${doubaoConfig.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      console.error(`❌ API调用失败: ${response.status} ${response.statusText}`);
      return;
    }
    
    const result = await response.json();
    console.log('📋 完整API响应:');
    console.log(JSON.stringify(result, null, 2));
    
    // 分析响应结构，查找时长相关字段
    console.log('\n🔍 分析时长相关字段:');
    
    const possibleDurationFields = [
      'duration',
      'video_duration', 
      'length',
      'video_length',
      'time',
      'video_time'
    ];
    
    function findDurationFields(obj, path = '') {
      const found = [];
      
      if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          // 检查键名是否包含时长相关词汇
          if (possibleDurationFields.some(field => key.toLowerCase().includes(field))) {
            found.push({ path: currentPath, key, value });
          }
          
          // 递归搜索
          if (typeof value === 'object') {
            found.push(...findDurationFields(value, currentPath));
          }
        }
      }
      
      return found;
    }
    
    const durationFields = findDurationFields(result);
    
    if (durationFields.length > 0) {
      console.log('✅ 找到可能的时长字段:');
      durationFields.forEach(field => {
        console.log(`   ${field.path}: ${field.value} (类型: ${typeof field.value})`);
      });
    } else {
      console.log('❌ 未找到明显的时长字段');
    }
    
    // 检查content字段的详细结构
    if (result.content) {
      console.log('\n📋 content字段详细结构:');
      console.log(JSON.stringify(result.content, null, 2));
    }
    
    // 检查是否有视频元数据
    if (result.content && result.content.video_url) {
      console.log(`\n🎬 视频URL: ${result.content.video_url}`);
      
      // 尝试获取视频的实际时长（通过HEAD请求获取Content-Length等信息）
      try {
        console.log('📡 尝试获取视频元数据...');
        const videoResponse = await fetch(result.content.video_url, { method: 'HEAD' });
        
        console.log('📋 视频响应头:');
        for (const [key, value] of videoResponse.headers.entries()) {
          console.log(`   ${key}: ${value}`);
        }
      } catch (error) {
        console.warn('获取视频元数据失败:', error.message);
      }
    }
    
    // 总结分析结果
    console.log('\n📊 分析总结:');
    console.log(`   任务状态: ${result.status}`);
    console.log(`   视频URL: ${result.content?.video_url ? '有' : '无'}`);
    console.log(`   时长字段: ${durationFields.length > 0 ? '找到' + durationFields.length + '个' : '未找到'}`);
    
    if (durationFields.length === 0) {
      console.log('\n💡 建议解决方案:');
      console.log('   1. 豆包API可能不返回视频时长信息');
      console.log('   2. 需要在提示词中明确指定时长要求');
      console.log('   3. 或者通过其他方式获取实际视频时长');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDoubaoResponseFormat();
