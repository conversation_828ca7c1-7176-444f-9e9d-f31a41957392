'use client'

import { useState } from 'react'
import { User, Eye, IdCard, Palette, Heart, FileText, ChevronDown, ChevronRight } from 'lucide-react'
import { Character, CharacterAppearance } from '@/types'

interface CharacterListProps {
  characters: Character[]
  onEdit?: (character: Character) => void
}

interface CharacterCardProps {
  character: Character
  onEdit?: (character: Character) => void
}

function CharacterCard({ character, onEdit }: CharacterCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  // 解析外貌信息
  let appearance: CharacterAppearance
  try {
    appearance = typeof character.appearance === 'string' 
      ? JSON.parse(character.appearance)
      : character.appearance
  } catch {
    appearance = { face: '', body: '', clothing: '' }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      {/* 角色头部信息 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <User className="text-blue-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-lg">{character.name}</h3>
            {character.identity && (
              <p className="text-sm text-gray-600">{character.identity}</p>
            )}
          </div>
        </div>
        
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-1 hover:bg-gray-100 rounded"
        >
          {isExpanded ? (
            <ChevronDown size={20} className="text-gray-500" />
          ) : (
            <ChevronRight size={20} className="text-gray-500" />
          )}
        </button>
      </div>

      {/* 基础信息预览 */}
      {!isExpanded && (
        <div className="space-y-2">
          {character.personality && (
            <div className="flex items-start">
              <Heart className="text-pink-500 mr-2 mt-0.5 flex-shrink-0" size={16} />
              <p className="text-sm text-gray-700 line-clamp-2">{character.personality}</p>
            </div>
          )}
          
          {appearance.face && (
            <div className="flex items-start">
              <Eye className="text-green-500 mr-2 mt-0.5 flex-shrink-0" size={16} />
              <p className="text-sm text-gray-700 line-clamp-1">{appearance.face}</p>
            </div>
          )}
        </div>
      )}

      {/* 详细信息展开 */}
      {isExpanded && (
        <div className="space-y-4 mt-4 border-t pt-4">
          {/* 外貌描述 */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-800 flex items-center">
              <Palette className="mr-2 text-purple-500" size={16} />
              外貌特征
            </h4>
            
            {appearance.face && (
              <div className="ml-6">
                <div className="flex items-start">
                  <Eye className="text-green-500 mr-2 mt-0.5 flex-shrink-0" size={14} />
                  <div>
                    <span className="text-sm font-medium text-gray-600">五官：</span>
                    <p className="text-sm text-gray-700 mt-1">{appearance.face}</p>
                  </div>
                </div>
              </div>
            )}
            
            {appearance.body && (
              <div className="ml-6">
                <div className="flex items-start">
                  <User className="text-blue-500 mr-2 mt-0.5 flex-shrink-0" size={14} />
                  <div>
                    <span className="text-sm font-medium text-gray-600">体型：</span>
                    <p className="text-sm text-gray-700 mt-1">{appearance.body}</p>
                  </div>
                </div>
              </div>
            )}
            
            {appearance.clothing && (
              <div className="ml-6">
                <div className="flex items-start">
                  <Palette className="text-purple-500 mr-2 mt-0.5 flex-shrink-0" size={14} />
                  <div>
                    <span className="text-sm font-medium text-gray-600">服装：</span>
                    <p className="text-sm text-gray-700 mt-1">{appearance.clothing}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 身份信息 */}
          {character.identity && (
            <div>
              <h4 className="font-medium text-gray-800 flex items-center mb-2">
                <IdCard className="mr-2 text-orange-500" size={16} />
                身份背景
              </h4>
              <p className="text-sm text-gray-700 ml-6">{character.identity}</p>
            </div>
          )}

          {/* 性格特点 */}
          {character.personality && (
            <div>
              <h4 className="font-medium text-gray-800 flex items-center mb-2">
                <Heart className="mr-2 text-pink-500" size={16} />
                性格特点
              </h4>
              <p className="text-sm text-gray-700 ml-6">{character.personality}</p>
            </div>
          )}

          {/* 隐线伏笔 */}
          {character.hiddenLines && (
            <div>
              <h4 className="font-medium text-gray-800 flex items-center mb-2">
                <FileText className="mr-2 text-indigo-500" size={16} />
                隐线伏笔
              </h4>
              <p className="text-sm text-gray-700 ml-6">{character.hiddenLines}</p>
            </div>
          )}

          {/* 编辑按钮 */}
          {onEdit && (
            <div className="pt-3 border-t">
              <button
                onClick={() => onEdit(character)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                编辑角色信息
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default function CharacterList({ characters, onEdit }: CharacterListProps) {
  if (characters.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <User className="mr-2" size={20} />
          角色一栏
        </h2>
        
        <div className="text-center py-8">
          <User className="mx-auto text-gray-400 mb-4" size={48} />
          <p className="text-gray-500">暂无角色信息</p>
          <p className="text-sm text-gray-400 mt-1">
            请先上传小说并进行AI分析
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center">
          <User className="mr-2" size={20} />
          角色一栏
        </h2>
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
          共 {characters.length} 个角色
        </span>
      </div>

      <div className="space-y-4">
        {characters.map((character) => (
          <CharacterCard
            key={character.id}
            character={character}
            onEdit={onEdit}
          />
        ))}
      </div>
    </div>
  )
}
