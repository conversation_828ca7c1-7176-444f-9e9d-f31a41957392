// 专门测试豆包内部错误处理
async function testDoubaoInternalError() {
  try {
    console.log('🔍 测试豆包内部错误处理机制...');
    
    const requestId = '02175058307111961b930c95e8da0cc9d527e8fe4180cbf3e18ba';
    console.log('📋 错误请求ID:', requestId);
    
    // 1. 检查当前服务状态
    console.log('\n🌐 1. 检查服务状态...');
    
    const testConfigs = [
      {
        name: '豆包 Seedance Pro',
        model: 'doubao-seedance-1-0-pro-250528',
        apiKey: 'test-key'
      },
      {
        name: '豆包 Pro 4K',
        model: 'doubao-pro-4k',
        apiKey: 'test-key'
      }
    ];
    
    for (const config of testConfigs) {
      console.log(`\n🧪 测试模型: ${config.name}`);
      
      try {
        const response = await fetch('http://localhost:3000/api/models/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            provider: 'doubao',
            model: config.model,
            apiKey: config.apiKey
          })
        });
        
        const result = await response.json();
        
        if (result.error) {
          if (result.error.includes('internal error')) {
            console.log('❌ 仍然存在内部错误');
          } else if (result.error.includes('API key')) {
            console.log('✅ 服务正常（API密钥错误是预期的）');
          } else if (result.error.includes('does not exist')) {
            console.log('⚠️ 模型不存在或无权访问');
          } else {
            console.log('🔍 其他错误:', result.error);
          }
        } else {
          console.log('✅ 连接成功');
        }
      } catch (error) {
        console.log('💥 请求失败:', error.message);
      }
    }
    
    // 2. 分析内部错误
    console.log('\n🔍 2. 内部错误分析...');
    
    console.log('📊 错误特征:');
    console.log('   • 错误类型: "The service encountered an unexpected internal error"');
    console.log('   • 错误级别: 服务端错误 (5xx)');
    console.log('   • 可重试性: 是');
    console.log('   • 持续时间: 通常几分钟到几小时');
    
    console.log('\n🕐 可能原因:');
    console.log('   1. 火山引擎服务端维护');
    console.log('   2. 模型服务临时过载');
    console.log('   3. 网络基础设施问题');
    console.log('   4. 模型版本更新中');
    
    // 3. 解决方案
    console.log('\n💡 3. 解决方案...');
    
    console.log('\n🔄 立即解决方案:');
    console.log('   1. 等待5-10分钟后重试');
    console.log('   2. 使用其他可用的AI模型');
    console.log('   3. 检查火山引擎状态页面');
    console.log('   4. 联系火山引擎技术支持');
    
    console.log('\n⚙️ 技术解决方案:');
    console.log('   1. 实现指数退避重试');
    console.log('   2. 配置多个备用模型');
    console.log('   3. 添加服务降级机制');
    console.log('   4. 设置错误监控告警');
    
    // 4. 备用模型建议
    console.log('\n🔄 4. 备用模型建议...');
    
    const backupModels = [
      {
        provider: 'deepseek',
        model: 'deepseek-chat',
        name: 'DeepSeek Chat',
        status: '✅ 通常稳定'
      },
      {
        provider: 'deepseek',
        model: 'deepseek-reasoner',
        name: 'DeepSeek Reasoner',
        status: '✅ 推理能力强'
      },
      {
        provider: 'tongyi',
        model: 'qwen-turbo',
        name: '通义千问 Turbo',
        status: '✅ 响应快速'
      }
    ];
    
    console.log('\n📋 可用的备用模型:');
    backupModels.forEach((model, index) => {
      console.log(`   ${index + 1}. ${model.name} (${model.provider})`);
      console.log(`      模型: ${model.model}`);
      console.log(`      状态: ${model.status}`);
    });
    
    // 5. 监控建议
    console.log('\n📈 5. 监控和预防建议...');
    
    console.log('\n🔍 监控指标:');
    console.log('   • API响应时间');
    console.log('   • 错误率统计');
    console.log('   • 服务可用性');
    console.log('   • 重试成功率');
    
    console.log('\n⚡ 预防措施:');
    console.log('   1. 配置多个AI服务提供商');
    console.log('   2. 实现智能路由和负载均衡');
    console.log('   3. 设置服务熔断机制');
    console.log('   4. 建立服务状态监控');
    
    // 6. 当前系统状态
    console.log('\n📊 6. 当前系统状态...');
    
    console.log('\n✅ 已实现的功能:');
    console.log('   • 自动重试机制 (3次)');
    console.log('   • 智能错误识别');
    console.log('   • 详细错误日志');
    console.log('   • 超时保护');
    
    console.log('\n🔄 建议改进:');
    console.log('   • 增加指数退避算法');
    console.log('   • 实现服务降级');
    console.log('   • 添加健康检查');
    console.log('   • 配置告警通知');
    
    // 7. 用户操作建议
    console.log('\n👤 7. 用户操作建议...');
    
    console.log('\n⏰ 短期解决方案 (现在):');
    console.log('   1. 等待10-15分钟后重试豆包模型');
    console.log('   2. 临时切换到DeepSeek或通义千问模型');
    console.log('   3. 检查火山引擎官方状态页面');
    
    console.log('\n🔧 中期解决方案 (今天):');
    console.log('   1. 配置多个AI模型作为备用');
    console.log('   2. 联系火山引擎技术支持了解情况');
    console.log('   3. 考虑升级服务套餐获得更好的稳定性');
    
    console.log('\n📞 联系方式:');
    console.log('   • 火山引擎控制台: https://console.volcengine.com/');
    console.log('   • 技术支持: 通过控制台提交工单');
    console.log('   • 状态页面: 查看服务状态和维护公告');
    
    console.log('\n🎯 总结:');
    console.log('   ⚠️ 豆包服务当前存在内部错误');
    console.log('   🔄 系统已配置自动重试机制');
    console.log('   💡 建议临时使用备用模型');
    console.log('   ⏰ 通常此类问题会在几小时内解决');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testDoubaoInternalError();
