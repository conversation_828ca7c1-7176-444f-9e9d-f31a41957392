-- CreateTable
CREATE TABLE "video_generations" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "episodeId" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "videoUrl" TEXT,
    "consistencyMode" TEXT,
    "includeReferenceImages" BOOLEAN NOT NULL DEFAULT false,
    "style" TEXT,
    "quality" TEXT,
    "metadata" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "video_generations_episodeId_fkey" FOREIGN KEY ("episodeId") REFERENCES "episodes" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_ai_configs" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "provider" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "supportsVideo" BOOLEAN NOT NULL DEFAULT false,
    "temperature" REAL NOT NULL DEFAULT 0.7,
    "maxTokens" INTEGER NOT NULL DEFAULT 4000,
    "topP" REAL NOT NULL DEFAULT 0.9,
    "status" TEXT NOT NULL DEFAULT 'disconnected',
    "lastTest" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_ai_configs" ("apiKey", "createdAt", "description", "enabled", "id", "lastTest", "maxTokens", "model", "name", "provider", "status", "temperature", "topP", "updatedAt") SELECT "apiKey", "createdAt", "description", "enabled", "id", "lastTest", "maxTokens", "model", "name", "provider", "status", "temperature", "topP", "updatedAt" FROM "ai_configs";
DROP TABLE "ai_configs";
ALTER TABLE "new_ai_configs" RENAME TO "ai_configs";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
