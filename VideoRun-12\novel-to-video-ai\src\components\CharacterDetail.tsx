'use client'

import { useState, useEffect } from 'react'
import { X, User, <PERSON>lette, Save, Edit, <PERSON>rk<PERSON>, RefreshCw, ChevronDown, Eye } from 'lucide-react'
import { Character } from '@/types'
import ModelSelector from './ModelSelector'
import AppearancePreview from './AppearancePreview'
import PromptPreview from './PromptPreview'

interface CharacterDetailProps {
  character: Character
  isOpen: boolean
  onClose: () => void
  onUpdate?: (character: Character) => void
}

export default function CharacterDetail({ 
  character, 
  isOpen, 
  onClose, 
  onUpdate 
}: CharacterDetailProps) {
  const [activeTab, setActiveTab] = useState<'info' | 'appearance' | 'consistency'>('info')
  const [isEditing, setIsEditing] = useState(false)
  const [editedCharacter, setEditedCharacter] = useState(character)

  // AI形象生成相关状态
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [generatedAppearance, setGeneratedAppearance] = useState<any>(null)

  // 增强提示词相关状态
  const [showPromptDropdown, setShowPromptDropdown] = useState(false)
  const [enhancePrompt, setEnhancePrompt] = useState('')
  const [savedEnhancePrompt, setSavedEnhancePrompt] = useState('')

  // 提示词预览相关状态
  const [showPromptPreview, setShowPromptPreview] = useState(false)

  // 新增：一致性相关状态
  const [consistencyMode, setConsistencyMode] = useState<'plot' | 'consistency' | 'hybrid'>('hybrid')
  const [useConsistencyConstraints, setUseConsistencyConstraints] = useState(true)
  const [generateDetailedDNA, setGenerateDetailedDNA] = useState(true)
  const [consistencyScore, setConsistencyScore] = useState(character.consistencyScore || 0)

  // 默认增强提示词
  const defaultEnhancePrompt = '三视图是该角色三个角度的特写，第一张是该角色的正面，第二张是该角色的侧面，第三张是该角色的背面。要求：1，生成的三视图 人物五官和身形要保持一致。2，三视图一定要符合角色描述。3，角色三视图的背景色一样。4，高质量，精美细节。'



  // 如果弹窗未打开，不渲染
  if (!isOpen) return null

  // 保存编辑
  const handleSave = async () => {
    try {
      // 调用API保存到数据库
      const response = await fetch(`/api/characters/${character.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editedCharacter.name,
          identity: editedCharacter.identity,
          personality: editedCharacter.personality,
          physique: editedCharacter.physique,
          facial: editedCharacter.facial,
          hairstyle: editedCharacter.hairstyle,
          clothing: editedCharacter.clothing
        })
      })

      const result = await response.json()

      if (result.success) {
        // 更新本地状态
        onUpdate?.(editedCharacter)
        setIsEditing(false)
        alert('角色信息保存成功！')
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存角色信息失败:', error)
      alert('保存失败，请重试')
    }
  }

  // 取消编辑
  const handleCancel = () => {
    setEditedCharacter(character)
    setIsEditing(false)
  }

  // 更新角色信息
  const updateCharacterField = (field: keyof Character, value: string) => {
    setEditedCharacter(prev => ({
      ...prev,
      [field]: value
    }))
  }



  // AI生成角色形象
  const handleGenerateAppearance = async (customPrompt?: string) => {
    if (!selectedModel) {
      alert('请先选择AI模型')
      return
    }

    setIsGenerating(true)
    setShowPreview(true)

    // 合并增强提示词
    let finalPrompt = customPrompt || ''
    if (savedEnhancePrompt) {
      finalPrompt = savedEnhancePrompt + (finalPrompt ? ', ' + finalPrompt : '')
    }



    try {
      const response = await fetch('/api/ai/generate-appearance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId: selectedModel,
          character: {
            id: character.id,
            name: character.name,
            facial: character.facial,
            identity: character.identity,
            personality: character.personality,
            physique: character.physique,
            hairstyle: character.hairstyle,
            clothing: character.clothing,
            detailedDNA: character.detailedDNA
          },
          customPrompt: finalPrompt, // 使用合并后的提示词
          // 新增：一致性相关参数
          generateDetailedDNA,
          useConsistencyConstraints,
          consistencyMode
        })
      })

      const data = await response.json()

      if (data.success) {
        setGeneratedAppearance(data.data)
        // 更新一致性评分
        if (data.data.consistencyScore) {
          setConsistencyScore(data.data.consistencyScore)
        }
      } else {
        throw new Error(data.error || 'AI生成失败')
      }
    } catch (error) {
      console.error('AI生成形象失败:', error)
      setGeneratedAppearance(null)
    } finally {
      setIsGenerating(false)
    }
  }

  // 保存生成的形象
  const handleSaveGeneratedAppearance = async (data: any) => {
    try {
      // 准备保存的图像数据
      const generatedImages = {
        front: data.front?.url,
        side: data.side?.url,
        back: data.back?.url
      }

      // 调用API保存到数据库
      const response = await fetch(`/api/characters/${character.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generatedImages
        })
      })

      const result = await response.json()

      if (result.success) {
        // 更新本地状态
        const updatedCharacter = {
          ...character,
          generatedImages
        }
        onUpdate?.(updatedCharacter)
        setShowPreview(false)
        alert('角色形象保存成功！')
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      console.error('保存角色形象失败:', error)
      alert('保存失败，请重试')
    }
  }

  // 重新生成（支持自定义提示词）
  const handleRegenerate = (customPrompt?: string) => {
    handleGenerateAppearance(customPrompt)
  }

  // 保存增强提示词
  const handleSaveEnhancePrompt = () => {
    setSavedEnhancePrompt(enhancePrompt)
    setShowPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_${character.id}`
    localStorage.setItem(storageKey, enhancePrompt)
  }

  // 从localStorage加载增强提示词
  useEffect(() => {
    if (character.id) {
      const storageKey = `enhance_prompt_${character.id}`
      const savedPrompt = localStorage.getItem(storageKey)
      if (savedPrompt) {
        setSavedEnhancePrompt(savedPrompt)
        setEnhancePrompt(savedPrompt)
      } else {
        // 如果没有保存的提示词，使用默认提示词
        setSavedEnhancePrompt(defaultEnhancePrompt)
        setEnhancePrompt(defaultEnhancePrompt)
        // 自动保存默认提示词
        localStorage.setItem(storageKey, defaultEnhancePrompt)
      }
    }
  }, [character.id])

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                <User className="text-purple-600" size={24} />
              </div>
              <div>
                <div className="flex items-center space-x-3">
                  <h2 className="text-xl font-semibold text-gray-900">{character.name}</h2>
                  {consistencyScore > 0 && (
                    <span className={`px-3 py-1 text-sm rounded-full font-medium ${
                      consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :
                      consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      一致性: {(consistencyScore * 100).toFixed(1)}%
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500">人物信息详情</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Edit className="mr-1" size={14} />
                  编辑
                </button>
              ) : (
                <>
                  <button
                    onClick={handleSave}
                    className="inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                  >
                    <Save className="mr-1" size={14} />
                    保存
                  </button>
                  <button
                    onClick={handleCancel}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    取消
                  </button>
                </>
              )}
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* 标签页导航 */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('info')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'info'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <User className="inline mr-2" size={16} />
                角色信息
              </button>
              <button
                onClick={() => setActiveTab('appearance')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'appearance'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Palette className="inline mr-2" size={16} />
                形象设置
              </button>
              <button
                onClick={() => setActiveTab('consistency')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'consistency'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Sparkles className="inline mr-2" size={16} />
                一致性管理
                {consistencyScore > 0 && (
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                    consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :
                    consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {(consistencyScore * 100).toFixed(0)}%
                  </span>
                )}
              </button>
            </nav>
          </div>

          {/* 标签页内容 */}
          <div className="p-6 overflow-y-auto max-h-[75vh]">
            {activeTab === 'info' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  AI提取的角色信息
                </h3>
                
                {/* 身份 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    身份
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.identity || ''}
                      onChange={(e) => updateCharacterField('identity', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={3}
                      placeholder="职业、社会地位、背景等身份信息"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.identity || '暂无信息'}
                    </p>
                  )}
                </div>

                {/* 性格 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    性格
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.personality || ''}
                      onChange={(e) => updateCharacterField('personality', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={4}
                      placeholder="性格特征、行为习惯、心理特点等内在特质"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.personality || '暂无信息'}
                    </p>
                  )}
                </div>

                {/* 身材特征 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    身材特征
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.physique || ''}
                      onChange={(e) => updateCharacterField('physique', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={3}
                      placeholder="身高、体型、体态、肌肉线条等身体特征"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.physique || '暂无信息'}
                    </p>
                  )}
                </div>

                {/* 五官特征 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    五官特征
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.facial || ''}
                      onChange={(e) => updateCharacterField('facial', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={3}
                      placeholder="脸型、眼睛、鼻子、嘴唇、眉毛等面部特征"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.facial || '暂无信息'}
                    </p>
                  )}
                </div>

                {/* 发型样式 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    发型样式
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.hairstyle || ''}
                      onChange={(e) => updateCharacterField('hairstyle', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={3}
                      placeholder="发型样式、发色、发长、发质等头发特征"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.hairstyle || '暂无信息'}
                    </p>
                  )}
                </div>

                {/* 服饰风格 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    服饰风格
                  </label>
                  {isEditing ? (
                    <textarea
                      value={editedCharacter.clothing || ''}
                      onChange={(e) => updateCharacterField('clothing', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      rows={3}
                      placeholder="服装风格、颜色搭配、材质、配饰等穿着特征"
                    />
                  ) : (
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-md">
                      {character.clothing || '暂无信息'}
                    </p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'appearance' && (
              <div className="space-y-6">
                {/* 已保存的三视图展示 */}
                {character.generatedImages && (character.generatedImages.front || character.generatedImages.side || character.generatedImages.back) && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                      <User className="text-green-600 mr-2" size={20} />
                      已保存的角色形象
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* 正面视图 */}
                      {character.generatedImages.front && (
                        <div className="space-y-3">
                          <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                            <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                            正面视图
                          </h5>
                          <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                            <img
                              src={character.generatedImages.front}
                              alt="正面视图"
                              className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                              onClick={() => {
                                window.open(character.generatedImages.front, '_blank')
                              }}
                            />
                          </div>
                        </div>
                      )}

                      {/* 侧面视图 */}
                      {character.generatedImages.side && (
                        <div className="space-y-3">
                          <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            侧面视图
                          </h5>
                          <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                            <img
                              src={character.generatedImages.side}
                              alt="侧面视图"
                              className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                              onClick={() => {
                                window.open(character.generatedImages.side, '_blank')
                              }}
                            />
                          </div>
                        </div>
                      )}

                      {/* 背面视图 */}
                      {character.generatedImages.back && (
                        <div className="space-y-3">
                          <h5 className="text-sm font-medium text-gray-700 text-center flex items-center justify-center">
                            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            背面视图
                          </h5>
                          <div className="bg-gray-100 rounded-lg overflow-hidden aspect-[3/4]">
                            <img
                              src={character.generatedImages.back}
                              alt="背面视图"
                              className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                              onClick={() => {
                                window.open(character.generatedImages.back, '_blank')
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="mt-4 flex justify-center">
                      <button
                        onClick={() => handleGenerateAppearance()}
                        disabled={isGenerating || !selectedModel}
                        className="inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <RefreshCw className="mr-2" size={16} />
                        重新生成形象
                      </button>
                    </div>
                  </div>
                )}

                {/* AI生成功能区域 */}
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 flex items-center">
                        <Sparkles className="text-purple-600 mr-2" size={20} />
                        AI一键生成角色形象
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        基于角色信息自动生成正、侧、背三视图，为连续剧人物一致性准备
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="relative flex">
                      <button
                        onClick={() => handleGenerateAppearance()}
                        disabled={!selectedModel || isGenerating}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-l-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                      >
                        <Sparkles className="mr-2" size={16} />
                        {isGenerating ? '生成中...' : 'AI一键生成角色形象'}
                      </button>

                      <button
                        onClick={() => setShowPromptDropdown(!showPromptDropdown)}
                        disabled={!selectedModel || isGenerating}
                        className="inline-flex items-center px-2 py-2 border border-l-0 border-transparent rounded-r-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                      >
                        <ChevronDown size={16} />
                      </button>

                      {/* 增强提示词下拉框 */}
                      {showPromptDropdown && (
                        <div className="absolute top-full left-0 mt-1 w-96 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                          <div className="p-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              增强提示词设置
                            </label>
                            <textarea
                              value={enhancePrompt}
                              onChange={(e) => setEnhancePrompt(e.target.value)}
                              placeholder="输入增强提示词..."
                              className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none"
                              rows={6}
                            />

                            {/* 保存按钮 */}
                            <div className="mt-3">
                              <button
                                onClick={handleSaveEnhancePrompt}
                                className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                              >
                                保存增强提示词
                              </button>
                            </div>

                            {/* 操作按钮 */}
                            <div className="flex justify-between items-center mt-3">
                              <button
                                onClick={() => {
                                  setEnhancePrompt(defaultEnhancePrompt)
                                }}
                                className="text-sm text-purple-600 hover:text-purple-800"
                              >
                                恢复默认
                              </button>
                              <button
                                onClick={() => setShowPromptDropdown(false)}
                                className="text-sm text-gray-600 hover:text-gray-800"
                              >
                                关闭
                              </button>
                            </div>

                            {/* 当前保存的提示词预览 */}
                            {savedEnhancePrompt && (
                              <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                                <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                                <div className="max-h-16 overflow-y-auto">
                                  {savedEnhancePrompt}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <ModelSelector
                      selectedModel={selectedModel}
                      onModelSelect={setSelectedModel}
                    />

                    {/* 提示词预览按钮 */}
                    <button
                      onClick={() => setShowPromptPreview(true)}
                      disabled={!selectedModel}
                      className="inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Eye className="mr-2" size={16} />
                      预览提示词
                    </button>
                  </div>
                </div>



                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">
                    AI形象生成说明
                  </h4>
                  <p className="text-sm text-blue-700">
                    使用AI生成功能可以基于角色信息自动生成正、侧、背三视图，确保该角色在不同剧集中保持视觉一致性。
                  </p>
                </div>
              </div>
            )}

            {activeTab === 'consistency' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Sparkles className="text-purple-600 mr-2" size={20} />
                  角色一致性管理
                </h3>

                {/* 一致性评分显示 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">当前一致性评分</h4>
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <div className="flex justify-between text-sm text-gray-600 mb-2">
                        <span>一致性评分</span>
                        <span>{(consistencyScore * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-300 ${
                            consistencyScore >= 0.9 ? 'bg-green-500' :
                            consistencyScore >= 0.7 ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${consistencyScore * 100}%` }}
                        />
                      </div>
                    </div>
                    <div className={`px-4 py-2 rounded-lg text-sm font-medium ${
                      consistencyScore >= 0.9 ? 'bg-green-100 text-green-800' :
                      consistencyScore >= 0.7 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {consistencyScore >= 0.9 ? '优秀' :
                       consistencyScore >= 0.7 ? '良好' : '需要改进'}
                    </div>
                  </div>
                </div>

                {/* 一致性模式设置 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">一致性模式设置</h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          value="plot"
                          checked={consistencyMode === 'plot'}
                          onChange={(e) => setConsistencyMode(e.target.value as any)}
                          className="mr-3"
                        />
                        <div>
                          <div className="font-medium text-gray-900">剧情优先</div>
                          <div className="text-sm text-gray-500">重视故事情节的自然发展</div>
                        </div>
                      </label>

                      <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          value="consistency"
                          checked={consistencyMode === 'consistency'}
                          onChange={(e) => setConsistencyMode(e.target.value as any)}
                          className="mr-3"
                        />
                        <div>
                          <div className="font-medium text-gray-900">一致性优先</div>
                          <div className="text-sm text-gray-500">重视角色外貌的一致性</div>
                        </div>
                      </label>

                      <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 border-purple-200 bg-purple-50">
                        <input
                          type="radio"
                          value="hybrid"
                          checked={consistencyMode === 'hybrid'}
                          onChange={(e) => setConsistencyMode(e.target.value as any)}
                          className="mr-3"
                        />
                        <div>
                          <div className="font-medium text-purple-900">混合模式</div>
                          <div className="text-sm text-purple-600">平衡剧情和一致性（推荐）</div>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* 一致性约束设置 */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">约束设置</h4>
                  <div className="space-y-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={useConsistencyConstraints}
                        onChange={(e) => setUseConsistencyConstraints(e.target.checked)}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium text-gray-900">启用一致性约束</div>
                        <div className="text-sm text-gray-500">在生成图像时应用一致性约束条件</div>
                      </div>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={generateDetailedDNA}
                        onChange={(e) => setGenerateDetailedDNA(e.target.checked)}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium text-gray-900">生成详细DNA</div>
                        <div className="text-sm text-gray-500">自动生成详细的角色外貌特征描述</div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* 详细DNA信息显示 */}
                {character.detailedDNA && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-medium text-gray-900 mb-4">详细角色DNA</h4>
                    <div className="space-y-4">
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">面部特征</h5>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>脸型: {character.detailedDNA.facial?.faceShape || '未设置'}</div>
                          <div>眼型: {character.detailedDNA.facial?.eyeShape || '未设置'}</div>
                          <div>眼色: {character.detailedDNA.facial?.eyeColor || '未设置'}</div>
                          <div>鼻型: {character.detailedDNA.facial?.noseShape || '未设置'}</div>
                          <div>嘴型: {character.detailedDNA.facial?.mouthShape || '未设置'}</div>
                          <div>肤色: {character.detailedDNA.facial?.skinTone || '未设置'}</div>
                        </div>
                        {character.detailedDNA.facial?.facialFeatures && (
                          <div className="mt-2 text-sm">
                            <strong>特殊特征:</strong> {character.detailedDNA.facial.facialFeatures}
                          </div>
                        )}
                      </div>

                      {character.detailedDNA.uniqueIdentifiers && character.detailedDNA.uniqueIdentifiers.length > 0 && (
                        <div>
                          <h5 className="font-medium text-gray-700 mb-2">独特标识</h5>
                          <div className="flex flex-wrap gap-2">
                            {character.detailedDNA.uniqueIdentifiers.map((identifier, index) => (
                              <span key={index} className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
                                {identifier}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 一致性说明 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">
                    角色一致性说明
                  </h4>
                  <p className="text-sm text-blue-700">
                    角色一致性功能通过详细的DNA特征描述和参考图像约束，确保同一角色在不同剧集和场景中保持外貌的高度一致性。
                    一致性评分越高，表示角色在视频生成中的外貌越稳定。
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI形象生成预览弹窗 */}
      <AppearancePreview
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        onSave={handleSaveGeneratedAppearance}
        onRegenerate={handleRegenerate}
        data={generatedAppearance}
        isGenerating={isGenerating}
      />

      {/* 提示词预览弹窗 */}
      <PromptPreview
        character={character}
        customPrompt={savedEnhancePrompt}
        isOpen={showPromptPreview}
        onClose={() => setShowPromptPreview(false)}
        onPromptChange={(prompt) => {
          // 可以在这里处理提示词变更
          console.log('新提示词:', prompt)
        }}
      />
    </div>
  )
}
