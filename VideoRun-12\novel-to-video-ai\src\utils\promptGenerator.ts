// 角色形象提示词生成器
// 专门用于从小说角色信息生成高质量的图像生成提示词

interface CharacterInfo {
  name: string
  identity?: string
  personality?: string
  physique?: string
  facial?: string
  hairstyle?: string
  clothing?: string
}

interface PromptOptions {
  artStyle?: 'anime' | 'realistic' | 'semi-realistic' | 'concept-art'
  viewType?: 'front' | 'side' | 'back' | 'three-quarter'
  background?: 'white' | 'transparent' | 'simple' | 'detailed'
  quality?: 'standard' | 'high' | 'masterpiece'
  customEnhancement?: string
}

// 中文特征词汇到英文的映射
const FEATURE_MAPPING = {
  // 年龄相关
  '少女': 'young girl',
  '青年女性': 'young woman', 
  '中年女性': 'middle-aged woman',
  '少年': 'young boy',
  '青年男性': 'young man',
  '中年男性': 'middle-aged man',
  
  // 脸型
  '瓜子脸': 'oval face',
  '圆脸': 'round face',
  '方脸': 'square face',
  '长脸': 'long face',
  '心形脸': 'heart-shaped face',
  
  // 眼睛
  '大眼睛': 'large eyes',
  '小眼睛': 'small eyes',
  '丹凤眼': 'phoenix eyes',
  '杏眼': 'almond eyes',
  '圆眼': 'round eyes',
  '深邃': 'deep-set eyes',
  
  // 发型
  '长发': 'long hair',
  '短发': 'short hair',
  '中长发': 'medium length hair',
  '卷发': 'curly hair',
  '直发': 'straight hair',
  '波浪发': 'wavy hair',
  '马尾': 'ponytail',
  '双马尾': 'twin tails',
  '刘海': 'bangs',
  
  // 身材
  '高挑': 'tall and slender',
  '娇小': 'petite',
  '匀称': 'well-proportioned',
  '丰满': 'full-figured',
  '苗条': 'slim',
  '健美': 'athletic build',
  
  // 气质
  '温柔': 'gentle',
  '冷酷': 'cold',
  '活泼': 'lively',
  '优雅': 'elegant',
  '知性': 'intellectual',
  '神秘': 'mysterious',
  '坚强': 'strong-willed',
  '可爱': 'cute',
  
  // 服装
  '职业装': 'business attire',
  '休闲装': 'casual wear',
  '正装': 'formal wear',
  '校服': 'school uniform',
  '古装': 'traditional costume',
  '现代装': 'modern clothing'
}

// 艺术风格配置
const ART_STYLES = {
  anime: {
    keywords: 'anime style, manga style, cel shading, clean lines',
    description: '日式动漫风格'
  },
  realistic: {
    keywords: 'photorealistic, hyperrealistic, professional photography',
    description: '写实摄影风格'
  },
  'semi-realistic': {
    keywords: 'semi-realistic, digital art, detailed illustration',
    description: '半写实插画风格'
  },
  'concept-art': {
    keywords: 'concept art, character design, professional illustration',
    description: '概念设计风格'
  }
}

// 视角配置
const VIEW_TYPES = {
  front: 'front view, facing camera, looking at viewer',
  side: 'side view, 90-degree profile, side angle',
  back: 'back view, rear angle, showing back',
  'three-quarter': 'three-quarter view, slight angle'
}

// 质量等级配置
const QUALITY_LEVELS = {
  standard: 'good quality, detailed',
  high: 'high quality, high resolution, detailed, sharp focus',
  masterpiece: 'masterpiece, best quality, ultra high resolution, 4K, highly detailed, sharp focus'
}

/**
 * 从角色信息生成图像提示词
 */
export function generateCharacterPrompt(
  character: CharacterInfo, 
  options: PromptOptions = {}
): string {
  const {
    artStyle = 'anime',
    viewType = 'front',
    background = 'white',
    quality = 'high',
    customEnhancement = ''
  } = options

  // 构建提示词各部分
  const parts: string[] = []

  // 1. 质量和风格
  parts.push(QUALITY_LEVELS[quality])
  parts.push(ART_STYLES[artStyle].keywords)

  // 2. 基础信息提取
  const basicInfo = extractBasicInfo(character)
  if (basicInfo) parts.push(basicInfo)

  // 3. 外貌特征
  const appearance = extractAppearanceFeatures(character)
  if (appearance) parts.push(appearance)

  // 4. 服装风格
  const clothing = extractClothingStyle(character)
  if (clothing) parts.push(clothing)

  // 5. 气质表现
  const temperament = extractTemperament(character)
  if (temperament) parts.push(temperament)

  // 6. 视角和姿态
  parts.push(VIEW_TYPES[viewType])
  parts.push('character design sheet, reference pose')

  // 7. 背景设置
  const backgroundDesc = getBackgroundDescription(background)
  parts.push(backgroundDesc)

  // 8. 技术参数
  parts.push('professional character design, clean composition')

  // 9. 自定义增强
  if (customEnhancement.trim()) {
    parts.push(customEnhancement)
  }

  return parts.filter(Boolean).join(', ')
}

/**
 * 提取基础信息（年龄、性别、身份）
 */
function extractBasicInfo(character: CharacterInfo): string {
  const info: string[] = []
  
  if (character.identity) {
    // 从身份信息中提取年龄和职业
    const ageMatch = character.identity.match(/(\d+)岁|十几岁|二十多岁|三十多岁|四十多岁/)
    if (ageMatch) {
      const age = ageMatch[1] || getAgeFromDescription(ageMatch[0])
      info.push(`${age} years old`)
    }
    
    // 提取性别（如果身份中包含）
    if (character.identity.includes('女') || character.identity.includes('姑娘') || character.identity.includes('小姐')) {
      info.push('woman')
    } else if (character.identity.includes('男') || character.identity.includes('先生') || character.identity.includes('小伙')) {
      info.push('man')
    }
    
    // 提取职业
    const profession = extractProfession(character.identity)
    if (profession) info.push(profession)
  }
  
  return info.join(' ')
}

/**
 * 提取外貌特征
 */
function extractAppearanceFeatures(character: CharacterInfo): string {
  const features: string[] = []
  
  // 处理五官特征
  if (character.facial) {
    const facialFeatures = translateFeatures(character.facial)
    features.push(facialFeatures)
  }
  
  // 处理身材特征
  if (character.physique) {
    const bodyFeatures = translateFeatures(character.physique)
    features.push(bodyFeatures)
  }
  
  // 处理发型
  if (character.hairstyle) {
    const hairFeatures = translateFeatures(character.hairstyle)
    features.push(hairFeatures)
  }
  
  return features.filter(Boolean).join(', ')
}

/**
 * 提取服装风格
 */
function extractClothingStyle(character: CharacterInfo): string {
  if (!character.clothing) return ''
  
  return translateFeatures(character.clothing)
}

/**
 * 提取气质特征
 */
function extractTemperament(character: CharacterInfo): string {
  if (!character.personality) return ''
  
  const temperamentWords: string[] = []
  
  // 从性格描述中提取气质关键词
  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {
    if (character.personality!.includes(chinese)) {
      temperamentWords.push(english)
    }
  })
  
  // 添加表情描述
  if (character.personality.includes('温柔') || character.personality.includes('善良')) {
    temperamentWords.push('gentle smile', 'warm expression')
  } else if (character.personality.includes('冷酷') || character.personality.includes('严肃')) {
    temperamentWords.push('serious expression', 'cold gaze')
  } else if (character.personality.includes('活泼') || character.personality.includes('开朗')) {
    temperamentWords.push('cheerful smile', 'bright expression')
  }
  
  return temperamentWords.join(', ')
}

/**
 * 翻译特征描述
 */
function translateFeatures(chineseText: string): string {
  let result = chineseText
  
  // 使用映射表进行翻译
  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {
    const regex = new RegExp(chinese, 'g')
    result = result.replace(regex, english)
  })
  
  // 清理和优化
  result = result
    .replace(/[，。；：]/g, ',') // 替换中文标点
    .replace(/\s+/g, ' ') // 合并空格
    .replace(/,+/g, ',') // 合并逗号
    .trim()
  
  return result
}

/**
 * 从描述中提取年龄
 */
function getAgeFromDescription(ageDesc: string): string {
  const ageMap: Record<string, string> = {
    '十几岁': '16',
    '二十多岁': '25', 
    '三十多岁': '35',
    '四十多岁': '45'
  }
  return ageMap[ageDesc] || '25'
}

/**
 * 提取职业信息
 */
function extractProfession(identity: string): string {
  const professions: Record<string, string> = {
    '学生': 'student',
    '老师': 'teacher',
    '医生': 'doctor',
    '护士': 'nurse',
    '律师': 'lawyer',
    '工程师': 'engineer',
    '设计师': 'designer',
    '程序员': 'programmer',
    '经理': 'manager',
    '秘书': 'secretary',
    '销售': 'salesperson',
    '警察': 'police officer',
    '军人': 'soldier',
    '艺术家': 'artist',
    '作家': 'writer',
    '记者': 'journalist'
  }
  
  for (const [chinese, english] of Object.entries(professions)) {
    if (identity.includes(chinese)) {
      return english
    }
  }
  
  return ''
}

/**
 * 获取背景描述
 */
function getBackgroundDescription(background: string): string {
  const backgrounds: Record<string, string> = {
    white: 'clean white background',
    transparent: 'transparent background',
    simple: 'simple background',
    detailed: 'detailed background scene'
  }
  
  return backgrounds[background] || 'clean white background'
}

/**
 * 生成负面提示词
 */
export function generateNegativePrompt(): string {
  return [
    'blurry', 'low quality', 'pixelated', 'deformed', 'distorted',
    'extra limbs', 'missing limbs', 'extra fingers', 'missing fingers',
    'bad anatomy', 'bad proportions', 'ugly', 'duplicate',
    'watermark', 'signature', 'text', 'logo',
    'oversaturated', 'undersaturated', 'overexposed', 'underexposed'
  ].join(', ')
}

/**
 * 生成角色一致性提示词（用于三视图）
 */
export function generateConsistencyPrompt(character: CharacterInfo, basePrompt: string): {
  front: string
  side: string
  back: string
} {
  // 提取身材和服饰特征（保持一致）
  const consistentFeatures = extractConsistentFeatures(character)

  // 添加强化细节一致性的约束
  const detailConsistency = 'character sheet, model sheet, same outfit details, identical patterns, consistent decorations, same armor design, identical accessories'

  return {
    // 正面：完整脸部可见，站立姿态
    front: `正面视图，角色面向镜头，完整的脸部特征清晰可见，直视前方，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,

    // 侧面：90度侧脸轮廓，站立姿态
    side: `侧面视图，角色90度侧身，完美的侧脸轮廓，侧面剪影，面向左侧或右侧，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`,

    // 背面：看不到脸，站立姿态
    back: `背面视图，角色背对镜头，看不到脸部，只显示后脑勺和背部，背影，站立姿态，挺直身体，双脚着地，标准站姿，${consistentFeatures}，${detailConsistency}，角色设计图，白色背景，全身图，高质量，动漫风格`
  }
}

/**
 * 提取一致性特征（身材、服饰、发型等保持不变的特征）
 */
function extractConsistentFeatures(character: CharacterInfo): string {
  const features: string[] = []

  // 身材特征（保持一致）
  if (character.physique) {
    features.push(character.physique)
  }

  // 发型和发色（保持一致）
  if (character.hairstyle) {
    features.push(character.hairstyle)
  }

  // 服饰风格（保持一致，强调细节一致性）
  if (character.clothing) {
    features.push(character.clothing)
    // 添加细节一致性约束
    features.push('相同的装饰图案', '一致的花纹细节', '相同的服装纹理')
  }

  // 身份特征（保持一致）
  if (character.identity) {
    features.push(character.identity)
  }

  // 添加强化一致性约束
  features.push('相同角色', '完全一致的外观', '同一人物', '相同的装备细节', '一致的配饰')

  return features.join('，')
}

function extractFaceShape(facial: string): string {
  for (const [chinese, english] of Object.entries(FEATURE_MAPPING)) {
    if (facial.includes(chinese) && chinese.includes('脸')) {
      return english
    }
  }
  return ''
}

function extractHairStyle(hairstyle: string): string {
  const hairFeatures: string[] = []
  
  Object.entries(FEATURE_MAPPING).forEach(([chinese, english]) => {
    if (hairstyle.includes(chinese) && (chinese.includes('发') || chinese.includes('头发'))) {
      hairFeatures.push(english)
    }
  })
  
  return hairFeatures.join(' ')
}
