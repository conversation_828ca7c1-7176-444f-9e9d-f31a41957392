import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const videoUrl = searchParams.get('url')

    if (!videoUrl) {
      return NextResponse.json(
        { error: '缺少视频URL参数' },
        { status: 400 }
      )
    }

    console.log('代理视频URL:', videoUrl)

    try {
      // 先尝试获取视频内容
      const response = await fetch(videoUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      if (response.ok) {
        // 如果视频可以访问，直接重定向
        return NextResponse.redirect(videoUrl, 302)
      } else {
        console.error('视频URL访问失败:', response.status, response.statusText)
        return NextResponse.json(
          { error: `视频访问失败: ${response.status} ${response.statusText}` },
          { status: response.status }
        )
      }
    } catch (fetchError) {
      console.error('获取视频失败:', fetchError)
      // 如果HEAD请求失败，仍然尝试重定向，让浏览器处理
      return NextResponse.redirect(videoUrl, 302)
    }

  } catch (error) {
    console.error('视频代理失败:', error)
    return NextResponse.json(
      { error: '视频代理失败' },
      { status: 500 }
    )
  }
}
