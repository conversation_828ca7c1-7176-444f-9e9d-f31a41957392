const CHUNK_PUBLIC_PATH = "server/app/api/ai/generate-story-video/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_skyreels_ts_575ec985._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__9b30f393._.js");
runtime.loadChunk("server/chunks/node_modules_next_6091aa0c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/ai/generate-story-video/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/ai/generate-story-video/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/ai/generate-story-video/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
