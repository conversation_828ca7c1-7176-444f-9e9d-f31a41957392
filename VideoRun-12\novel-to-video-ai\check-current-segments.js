const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentSegments() {
  try {
    console.log('🔍 检查当前视频片段状态...');
    
    // 查询所有视频片段
    const segments = await prisma.videoSegment.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        storyVideo: true
      }
    });
    
    console.log(`找到 ${segments.length} 个视频片段:`);
    
    segments.forEach((segment, index) => {
      console.log(`\n${index + 1}. 片段: ${segment.title.substring(0, 80)}...`);
      console.log(`   状态: ${segment.status}`);
      console.log(`   时长: ${segment.duration}秒`);
      console.log(`   创建时间: ${segment.createdAt}`);
      
      if (segment.status === 'failed' && segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   错误: ${metadata.error || '未知错误'}`);
        } catch (e) {
          console.log(`   元数据解析失败`);
        }
      }
      
      if (segment.status === 'generating' && segment.metadata) {
        try {
          const metadata = JSON.parse(segment.metadata);
          console.log(`   任务ID: ${metadata.taskId || '无'}`);
          console.log(`   提供商: ${metadata.provider || '未知'}`);
        } catch (e) {
          console.log(`   元数据解析失败`);
        }
      }
      
      if (segment.status === 'completed') {
        console.log(`   视频URL: ${segment.videoUrl ? '✅ 有' : '❌ 无'}`);
      }
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentSegments();
