'use client'

import { useState, useEffect } from 'react'
import { ChevronDown, Check, Settings, AlertCircle } from 'lucide-react'
import { AIConfig } from '@/types'

interface ModelOption {
  id: string
  name: string
  model: string
  provider: string
  description: string
}

interface ModelSelectorProps {
  selectedModel?: string
  onModelChange?: (modelId: string) => void
  onModelSelect?: (modelId: string) => void
  className?: string
}

export default function ModelSelector({
  selectedModel,
  onModelChange,
  onModelSelect,
  className = ''
}: ModelSelectorProps) {
  // 统一处理回调函数
  const handleModelSelect = (modelId: string) => {
    onModelChange?.(modelId)
    onModelSelect?.(modelId)
  }
  const [isOpen, setIsOpen] = useState(false)
  const [models, setModels] = useState<ModelOption[]>([])
  const [loading, setLoading] = useState(true)
  const [hasConfig, setHasConfig] = useState(false)

  // 获取已配置的模型列表
  useEffect(() => {
    fetchModels()
  }, [])

  const fetchModels = async () => {
    try {
      const response = await fetch('/api/models')
      const data = await response.json()

      if (data.success && data.data.length > 0) {
        // 只显示已启用且已配置API密钥的模型
        const enabledModels = data.data.filter((model: any) =>
          model.enabled &&
          model.apiKey &&
          model.apiKey.trim() !== ''
        )

        if (enabledModels.length > 0) {
          setHasConfig(true)

          // 转换为ModelOption格式
          const modelOptions: ModelOption[] = enabledModels.map((model: any) => ({
            id: model.id,
            name: model.name,
            model: model.model,
            provider: model.provider,
            description: model.description || '已配置模型'
          }))

          setModels(modelOptions)

          // 如果没有选中模型，默认选择第一个启用的模型
          if (!selectedModel && modelOptions.length > 0) {
            handleModelSelect(modelOptions[0].id)
          }
        } else {
          setHasConfig(false)
          setModels([])
        }
      } else {
        setHasConfig(false)
        setModels([])
      }
    } catch (error) {
      console.error('获取模型列表失败:', error)
      setHasConfig(false)
    } finally {
      setLoading(false)
    }
  }

  // 获取当前选中的模型信息
  const selectedModelInfo = models.find(model => model.id === selectedModel)

  // 获取模型提供商显示名称
  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'deepseek': return 'DeepSeek'
      case 'openai': return 'OpenAI'
      case 'claude': return 'Claude'
      case 'doubao': return '豆包'
      case 'tongyi': return '通义'
      case 'minimax': return 'MiniMax'
      case 'zhipu': return '智谱AI'
      case 'skyreels': return 'SkyReels-V2'
      default: return provider
    }
  }

  // 获取模型状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'disconnected': return 'text-gray-400'
      case 'error': return 'text-red-600'
      default: return 'text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className={`inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-gray-50 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 mr-2"></div>
        <span className="text-sm text-gray-500">加载中...</span>
      </div>
    )
  }

  if (!hasConfig) {
    return (
      <div className={`inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-gray-50 ${className}`}>
        <AlertCircle className="text-orange-500 mr-2" size={16} />
        <span className="text-sm text-gray-500">请先配置AI模型</span>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* 选择按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      >
        <Settings className="text-gray-400 mr-2" size={14} />
        <span className="text-sm text-gray-700">
          {selectedModelInfo ? (
            <>
              {selectedModelInfo.name}
            </>
          ) : (
            '选择模型'
          )}
        </span>
        <ChevronDown className="ml-2 text-gray-400" size={14} />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉选项 */}
          <div className="absolute right-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            <div className="py-1">
              <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
                选择AI模型
              </div>

              {/* 模型列表容器 - 添加最大高度和滚动条 */}
              <div className="max-h-60 overflow-y-auto">
                {models.map((model) => (
                  <button
                    key={model.id}
                    onClick={() => {
                      handleModelSelect(model.id)
                      setIsOpen(false)
                    }}
                    className={`
                      w-full text-left px-3 py-2 text-sm hover:bg-gray-50 flex items-center justify-between
                      ${selectedModel === model.id ? 'bg-purple-50 text-purple-700' : 'text-gray-700'}
                    `}
                  >
                    <div className="flex-1">
                      <div className="font-medium">
                        {model.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {model.description}
                      </div>
                    </div>

                    <div className="flex items-center">
                      {selectedModel === model.id && (
                        <Check className="text-purple-600" size={16} />
                      )}
                    </div>
                  </button>
                ))}

                {models.length === 0 && (
                  <div className="px-3 py-2 text-sm text-gray-500 text-center">
                    暂无可用模型
                  </div>
                )}
              </div>
            </div>
            
            <div className="border-t border-gray-100 px-3 py-2">
              <a
                href="/models"
                className="text-xs text-purple-600 hover:text-purple-700"
                onClick={() => setIsOpen(false)}
              >
                配置更多模型 →
              </a>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
