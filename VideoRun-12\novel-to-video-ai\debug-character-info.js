const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugCharacterInfo() {
  try {
    console.log('🔍 检查角色信息...');
    
    // 查询项目中的角色
    const characters = await prisma.character.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    console.log(`找到 ${characters.length} 个角色:`);
    
    characters.forEach((char, index) => {
      console.log(`\n${index + 1}. 角色: ${char.name}`);
      console.log(`   身份: ${char.identity || '无'}`);
      console.log(`   外貌: ${char.facial || '无'}`);
      console.log(`   体型: ${char.physique || '无'}`);
      console.log(`   发型: ${char.hairstyle || '无'}`);
      console.log(`   服装: ${char.clothing || '无'}`);
      console.log(`   性格: ${char.personality || '无'}`);
      console.log(`   项目ID: ${char.projectId}`);
      console.log(`   创建时间: ${char.createdAt}`);
      
      // 检查生成的图像
      if (char.generatedImages) {
        try {
          const images = JSON.parse(char.generatedImages);
          console.log(`   生成图像: 正面=${!!images.front}, 侧面=${!!images.side}, 背面=${!!images.back}`);
        } catch (e) {
          console.log(`   生成图像: 解析失败`);
        }
      } else {
        console.log(`   生成图像: 无`);
      }
    });
    
    // 检查项目信息
    console.log('\n📁 检查项目信息...');
    const projects = await prisma.project.findMany({
      orderBy: { createdAt: 'desc' },
      take: 3
    });
    
    projects.forEach((project, index) => {
      console.log(`\n项目 ${index + 1}: ${project.title}`);
      console.log(`   ID: ${project.id}`);
      console.log(`   描述: ${project.description || '无'}`);
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugCharacterInfo();
