{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/%E9%A1%B9%E7%9B%AE/VideoRun-12/VideoRun-12/novel-to-video-ai/src/app/api/characters/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\n// 更新角色信息\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const body = await request.json()\n    \n    const {\n      name,\n      identity,\n      personality,\n      physique,\n      facial,\n      hairstyle,\n      clothing,\n      generatedImages\n    } = body\n\n    // 检查角色是否存在\n    const existingCharacter = await prisma.character.findUnique({\n      where: { id }\n    })\n\n    if (!existingCharacter) {\n      return NextResponse.json(\n        { success: false, error: '角色不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 准备更新数据\n    const updateData: any = {\n      updatedAt: new Date()\n    }\n\n    // 只更新提供的字段\n    if (name !== undefined) updateData.name = name\n    if (identity !== undefined) updateData.identity = identity\n    if (personality !== undefined) updateData.personality = personality\n    if (physique !== undefined) updateData.physique = physique\n    if (facial !== undefined) updateData.facial = facial\n    if (hairstyle !== undefined) updateData.hairstyle = hairstyle\n    if (clothing !== undefined) updateData.clothing = clothing\n    \n    // 处理生成的图像数据\n    if (generatedImages !== undefined) {\n      updateData.generatedImages = generatedImages ? JSON.stringify(generatedImages) : null\n    }\n\n    // 更新角色\n    const updatedCharacter = await prisma.character.update({\n      where: { id },\n      data: updateData\n    })\n\n    // 解析生成的图像数据\n    const responseCharacter = {\n      ...updatedCharacter,\n      generatedImages: updatedCharacter.generatedImages \n        ? JSON.parse(updatedCharacter.generatedImages) \n        : null\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: responseCharacter,\n      message: '角色信息更新成功'\n    })\n  } catch (error) {\n    console.error('更新角色信息失败:', error)\n    return NextResponse.json(\n      { success: false, error: '更新角色信息失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 获取角色详情\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n\n    const character = await prisma.character.findUnique({\n      where: { id },\n      include: {\n        project: true\n      }\n    })\n\n    if (!character) {\n      return NextResponse.json(\n        { success: false, error: '角色不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 解析生成的图像数据\n    const responseCharacter = {\n      ...character,\n      generatedImages: character.generatedImages \n        ? JSON.parse(character.generatedImages) \n        : null\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: responseCharacter\n    })\n  } catch (error) {\n    console.error('获取角色信息失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取角色信息失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,eAAe,EAChB,GAAG;QAEJ,WAAW;QACX,MAAM,oBAAoB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1D,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,mBAAmB;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,aAAkB;YACtB,WAAW,IAAI;QACjB;QAEA,WAAW;QACX,IAAI,SAAS,WAAW,WAAW,IAAI,GAAG;QAC1C,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAClD,IAAI,gBAAgB,WAAW,WAAW,WAAW,GAAG;QACxD,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAClD,IAAI,WAAW,WAAW,WAAW,MAAM,GAAG;QAC9C,IAAI,cAAc,WAAW,WAAW,SAAS,GAAG;QACpD,IAAI,aAAa,WAAW,WAAW,QAAQ,GAAG;QAElD,YAAY;QACZ,IAAI,oBAAoB,WAAW;YACjC,WAAW,eAAe,GAAG,kBAAkB,KAAK,SAAS,CAAC,mBAAmB;QACnF;QAEA,OAAO;QACP,MAAM,mBAAmB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACrD,OAAO;gBAAE;YAAG;YACZ,MAAM;QACR;QAEA,YAAY;QACZ,MAAM,oBAAoB;YACxB,GAAG,gBAAgB;YACnB,iBAAiB,iBAAiB,eAAe,GAC7C,KAAK,KAAK,CAAC,iBAAiB,eAAe,IAC3C;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAClD,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,SAAS;YACX;QACF;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,oBAAoB;YACxB,GAAG,SAAS;YACZ,iBAAiB,UAAU,eAAe,GACtC,KAAK,KAAK,CAAC,UAAU,eAAe,IACpC;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}