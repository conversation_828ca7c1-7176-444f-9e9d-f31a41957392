'use client'

import { useState, useEffect } from 'react'
import { 
  Film, 
  ChevronRight, 
  ChevronDown, 
  Play, 
  Search, 
  Video,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { Episode } from '@/types'

interface EpisodeListProps {
  episodes: Episode[]
  onAnalyzePlot?: (episodeId: string) => void
  onGenerateVideo?: (episodeId: string) => void
}

interface EpisodeCardProps {
  episode: Episode
  index: number
  onAnalyzePlot?: (episodeId: string) => void
  onGenerateVideo?: (episodeId: string) => void
}

function EpisodeCard({ episode, index, onAnalyzePlot, onGenerateVideo }: EpisodeCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // 增强提示词相关状态 - 剧情分析
  const [showPlotPromptDropdown, setShowPlotPromptDropdown] = useState(false)
  const [plotEnhancePrompt, setPlotEnhancePrompt] = useState('')
  const [savedPlotEnhancePrompt, setSavedPlotEnhancePrompt] = useState('')

  // 增强提示词相关状态 - 视频生成
  const [showVideoPromptDropdown, setShowVideoPromptDropdown] = useState(false)
  const [videoEnhancePrompt, setVideoEnhancePrompt] = useState('')
  const [savedVideoEnhancePrompt, setSavedVideoEnhancePrompt] = useState('')

  // 状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'created':
        return {
          icon: <Clock className="text-gray-500" size={16} />,
          text: '待分析',
          color: 'text-gray-500',
          bgColor: 'bg-gray-100'
        }
      case 'analyzed':
        return {
          icon: <CheckCircle className="text-green-500" size={16} />,
          text: '已分析',
          color: 'text-green-500',
          bgColor: 'bg-green-100'
        }
      case 'video_generated':
        return {
          icon: <Video className="text-blue-500" size={16} />,
          text: '已生成视频',
          color: 'text-blue-500',
          bgColor: 'bg-blue-100'
        }
      default:
        return {
          icon: <AlertCircle className="text-yellow-500" size={16} />,
          text: '未知状态',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-100'
        }
    }
  }

  const statusInfo = getStatusInfo(episode.status)

  // 保存剧情分析增强提示词
  const handleSavePlotEnhancePrompt = () => {
    setSavedPlotEnhancePrompt(plotEnhancePrompt)
    setShowPlotPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_plot_${episode.id}`
    localStorage.setItem(storageKey, plotEnhancePrompt)
  }

  // 保存视频生成增强提示词
  const handleSaveVideoEnhancePrompt = () => {
    setSavedVideoEnhancePrompt(videoEnhancePrompt)
    setShowVideoPromptDropdown(false)
    // 保存到localStorage
    const storageKey = `enhance_prompt_video_${episode.id}`
    localStorage.setItem(storageKey, videoEnhancePrompt)
  }

  // 从localStorage加载增强提示词
  useEffect(() => {
    if (episode.id) {
      // 加载剧情分析增强提示词
      const plotStorageKey = `enhance_prompt_plot_${episode.id}`
      const savedPlotPrompt = localStorage.getItem(plotStorageKey)
      if (savedPlotPrompt) {
        setSavedPlotEnhancePrompt(savedPlotPrompt)
        setPlotEnhancePrompt(savedPlotPrompt)
      }

      // 加载视频生成增强提示词
      const videoStorageKey = `enhance_prompt_video_${episode.id}`
      const savedVideoPrompt = localStorage.getItem(videoStorageKey)
      if (savedVideoPrompt) {
        setSavedVideoEnhancePrompt(savedVideoPrompt)
        setVideoEnhancePrompt(savedVideoPrompt)
      }
    }
  }, [episode.id])

  // 处理剧情分析（带增强提示词）
  const handleAnalyzePlotWithPrompt = () => {
    if (onAnalyzePlot) {
      // 这里需要修改API调用以支持增强提示词
      onAnalyzePlot(episode.id)
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
      {/* 剧集头部 */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 truncate">{episode.title}</h3>
              <div className="flex items-center mt-1">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${statusInfo.bgColor} ${statusInfo.color}`}>
                  {statusInfo.icon}
                  <span className="ml-1">{statusInfo.text}</span>
                </span>
                <span className="text-xs text-gray-500 ml-2">
                  {episode.content.length.toLocaleString()} 字
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center ml-4">
            {/* 第一集显示生成视频按钮 */}
            {index === 0 && episode.status === 'analyzed' && onGenerateVideo && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onGenerateVideo(episode.id)
                }}
                className="mr-2 px-3 py-1 bg-purple-500 text-white text-sm rounded hover:bg-purple-600 transition-colors"
              >
                <Video size={14} className="inline mr-1" />
                生成视频
              </button>
            )}

            {isExpanded ? (
              <ChevronDown className="text-gray-400" size={20} />
            ) : (
              <ChevronRight className="text-gray-400" size={20} />
            )}
          </div>
        </div>
      </div>

      {/* 剧集详情 */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          {/* 剧情内容 */}
          <div className="mb-4">
            <h4 className="font-medium text-gray-800 mb-2">详细剧情</h4>
            <div className="bg-white rounded p-3 max-h-40 overflow-y-auto">
              <p className="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                {episode.content}
              </p>
            </div>
          </div>

          {/* 剧情信息 */}
          {episode.plotInfo && (
            <div className="mb-4">
              <h4 className="font-medium text-gray-800 mb-2">剧情信息</h4>
              <div className="bg-white rounded p-3 space-y-2">
                {/* 本集人物 */}
                <div>
                  <span className="text-sm font-medium text-gray-600">本集人物：</span>
                  <span className="text-sm text-gray-700 ml-1">
                    {JSON.parse(episode.plotInfo.characters).join('、')}
                  </span>
                </div>
                
                {/* 场景信息 */}
                <div>
                  <span className="text-sm font-medium text-gray-600">主要场景：</span>
                  <span className="text-sm text-gray-700 ml-1">
                    {JSON.parse(episode.plotInfo.scenes).map((scene: any) => scene.location).join('、')}
                  </span>
                </div>
                
                {/* 事件概要 */}
                <div>
                  <span className="text-sm font-medium text-gray-600">核心事件：</span>
                  <span className="text-sm text-gray-700 ml-1">
                    {JSON.parse(episode.plotInfo.events).length} 个事件
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-2">
            {episode.status === 'created' && onAnalyzePlot && (
              <div className="relative flex">
                <button
                  onClick={handleAnalyzePlotWithPrompt}
                  className="px-4 py-2 bg-green-500 text-white text-sm rounded-l hover:bg-green-600 transition-colors"
                >
                  <Search size={14} className="inline mr-1" />
                  提取剧情信息
                </button>

                <button
                  onClick={() => setShowPlotPromptDropdown(!showPlotPromptDropdown)}
                  className="px-2 py-2 bg-green-500 text-white text-sm rounded-r border-l border-green-400 hover:bg-green-600 transition-colors"
                >
                  <ChevronDown size={14} />
                </button>

                {/* 剧情分析增强提示词下拉框 */}
                {showPlotPromptDropdown && (
                  <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="p-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        增强提示词设置
                      </label>
                      <textarea
                        value={plotEnhancePrompt}
                        onChange={(e) => setPlotEnhancePrompt(e.target.value)}
                        placeholder="输入增强提示词，用于优化剧情分析..."
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 resize-none"
                        rows={3}
                      />

                      {/* 保存按钮 */}
                      <div className="mt-3">
                        <button
                          onClick={handleSavePlotEnhancePrompt}
                          className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                        >
                          保存增强提示词
                        </button>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex justify-end mt-3">
                        <button
                          onClick={() => setShowPlotPromptDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800"
                        >
                          关闭
                        </button>
                      </div>

                      {/* 当前保存的提示词预览 */}
                      {savedPlotEnhancePrompt && (
                        <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                          <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                          <div className="max-h-16 overflow-y-auto">
                            {savedPlotEnhancePrompt}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {index === 0 && episode.status === 'analyzed' && onGenerateVideo && (
              <div className="relative flex">
                <button
                  onClick={() => onGenerateVideo(episode.id)}
                  className="px-4 py-2 bg-purple-500 text-white text-sm rounded-l hover:bg-purple-600 transition-colors"
                >
                  <Video size={14} className="inline mr-1" />
                  生成视频
                </button>

                <button
                  onClick={() => setShowVideoPromptDropdown(!showVideoPromptDropdown)}
                  className="px-2 py-2 bg-purple-500 text-white text-sm rounded-r border-l border-purple-400 hover:bg-purple-600 transition-colors"
                >
                  <ChevronDown size={14} />
                </button>

                {/* 视频生成增强提示词下拉框 */}
                {showVideoPromptDropdown && (
                  <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="p-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        增强提示词设置
                      </label>
                      <textarea
                        value={videoEnhancePrompt}
                        onChange={(e) => setVideoEnhancePrompt(e.target.value)}
                        placeholder="输入增强提示词，用于优化视频生成..."
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 resize-none"
                        rows={3}
                      />

                      {/* 保存按钮 */}
                      <div className="mt-3">
                        <button
                          onClick={handleSaveVideoEnhancePrompt}
                          className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                        >
                          保存增强提示词
                        </button>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex justify-end mt-3">
                        <button
                          onClick={() => setShowVideoPromptDropdown(false)}
                          className="text-sm text-gray-600 hover:text-gray-800"
                        >
                          关闭
                        </button>
                      </div>

                      {/* 当前保存的提示词预览 */}
                      {savedVideoEnhancePrompt && (
                        <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                          <div className="font-medium mb-1">当前已保存的增强提示词：</div>
                          <div className="max-h-16 overflow-y-auto">
                            {savedVideoEnhancePrompt}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default function EpisodeList({ episodes, onAnalyzePlot, onGenerateVideo }: EpisodeListProps) {
  if (episodes.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Film className="mr-2" size={20} />
          剧集一栏
        </h2>
        
        <div className="text-center py-8">
          <Film className="mx-auto text-gray-400 mb-4" size={48} />
          <p className="text-gray-500">暂无剧集信息</p>
          <p className="text-sm text-gray-400 mt-1">
            请先上传小说并进行AI分析
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center">
          <Film className="mr-2" size={20} />
          剧集一栏
        </h2>
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
          共 {episodes.length} 集
        </span>
      </div>

      <div className="space-y-3">
        {episodes
          .sort((a, b) => a.orderIndex - b.orderIndex)
          .map((episode, index) => (
            <EpisodeCard
              key={episode.id}
              episode={episode}
              index={index}
              onAnalyzePlot={onAnalyzePlot}
              onGenerateVideo={onGenerateVideo}
            />
          ))}
      </div>

      {/* 说明文字 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          💡 <strong>使用说明：</strong>
        </p>
        <ul className="text-sm text-blue-600 mt-2 space-y-1">
          <li>• 点击剧集可展开查看详细内容</li>
          <li>• 点击"提取剧情信息"分析本集的人物、场景和事件</li>
          <li>• 第一集分析完成后可点击"生成视频"</li>
        </ul>
      </div>
    </div>
  )
}
