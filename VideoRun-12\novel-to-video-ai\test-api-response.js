// 测试API响应
async function testAPIResponse() {
  try {
    console.log('🧪 测试API响应...\n');

    // 测试video-segments API
    const response = await fetch('http://localhost:3002/api/ai/video-segments?episodeId=cmccxomfg000avmi0yknpfrue');
    const data = await response.json();

    // console.log('📋 API响应结构:', JSON.stringify(data, null, 2));

    if (data.success) {
      console.log('✅ API调用成功');

      const segments = data.data.segments || [];
      console.log(`📋 找到 ${segments.length} 个片段\n`);

      // 查找片段4
      const segment4 = segments.find(seg => seg.segmentIndex === 4);
      
      if (segment4) {
        console.log('🎯 片段4信息:');
        console.log(`   ID: ${segment4.id}`);
        console.log(`   状态: ${segment4.status}`);
        console.log(`   标题: ${segment4.title}`);
        console.log(`   视频URL: ${segment4.videoUrl ? '已生成' : '未生成'}`);
        console.log(`   元数据: ${segment4.metadata ? '存在' : '不存在'}`);
        
        if (segment4.metadata) {
          try {
            const metadata = JSON.parse(segment4.metadata);
            console.log('\n🎵 音频信息:');
            
            if (metadata.audio) {
              console.log(`   ✅ 音频已生成`);
              console.log(`   时长: ${metadata.audio.duration}秒`);
              console.log(`   提供商: ${metadata.audio.provider}`);
              console.log(`   音频URL长度: ${metadata.audio.audioUrl ? metadata.audio.audioUrl.length : 0} 字符`);
            } else {
              console.log(`   ❌ 未找到音频信息`);
            }
          } catch (error) {
            console.error('❌ 解析元数据失败:', error);
          }
        }
      } else {
        console.log('❌ 未找到片段4');
      }

      // 显示所有已完成的片段
      const completedSegments = segments.filter(seg => seg.status === 'completed');
      console.log(`\n📊 已完成的片段: ${completedSegments.length} 个`);
      
      completedSegments.forEach(seg => {
        const hasAudio = seg.metadata && JSON.parse(seg.metadata).audio;
        console.log(`   片段 ${seg.segmentIndex}: ${hasAudio ? '🎵 有音频' : '🔇 无音频'}`);
      });

    } else {
      console.log('❌ API调用失败:', data.error);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testAPIResponse();
