const { PrismaClient } = require('@prisma/client');

async function fixVideoConfig() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 修复视频配置...');
    
    // 查找所有豆包配置
    const allDoubao = await prisma.aIConfig.findMany({
      where: { provider: 'doubao' }
    });

    console.log('\n📋 所有豆包配置:');
    allDoubao.forEach((config, index) => {
      console.log(`   ${index + 1}. ${config.name}: ${config.model} (API密钥: ${config.apiKey ? '有' : '无'})`);
    });

    // 查找豆包视频生成模型
    const doubaoVideoConfig = allDoubao.find(config =>
      config.model === 'ep-20250622184757-q77k7' && config.apiKey
    );

    if (doubaoVideoConfig) {
      console.log(`\n✅ 找到豆包视频模型: ${doubaoVideoConfig.name}`);

      // 更新为支持视频
      await prisma.aIConfig.update({
        where: { id: doubaoVideoConfig.id },
        data: {
          supportsVideo: true,
          name: '豆包 Seedance 1.0 Pro (视频生成)'
        }
      });

      console.log('✅ 已将豆包模型标记为支持视频生成');

      // 验证更新
      const updated = await prisma.aIConfig.findUnique({
        where: { id: doubaoVideoConfig.id }
      });

      console.log(`✅ 验证成功: ${updated.name} - 支持视频: ${updated.supportsVideo}`);

    } else {
      console.log('\n❌ 未找到有效的豆包视频配置');
      console.log('需要确保豆包视频模型 ep-20250622184757-q77k7 有正确的API密钥');
    }
    
    // 检查最终结果
    const videoConfigs = await prisma.aIConfig.findMany({
      where: { supportsVideo: true }
    });
    
    console.log(`\n🎬 现在支持视频生成的配置数量: ${videoConfigs.length}`);
    videoConfigs.forEach(config => {
      console.log(`   ✅ ${config.name}: ${config.model}`);
    });
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixVideoConfig();
